Revision: r13780
Date: 2025-02-04 09:07:10 +0300 (tlt 04 Feb 2025) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
Wishlist IDEO3.2: ajout RS custom (partie JS)

## Files changed

## Full metadata
------------------------------------------------------------------------
r13780 | srazanandralisoa | 2025-02-04 09:07:10 +0300 (tlt 04 Feb 2025) | 1 ligne
Chemins modifiés :
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Basefile/Models/SocialsCollection.js
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Templates/socialSvgSelector.html
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Views/SocialSvgSelectorDialog.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Models/Params.js
   A /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Models/SocialList.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/ParamsPanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Templates/SocialNetwork.html
   A /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Templates/listSocial.html
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/ListSocialNetworks.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/SocialNetworks.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/params_panel/module/inline-social.less

Wishlist IDEO3.2: ajout RS custom (partie JS)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Basefile/Models/SocialsCollection.js
===================================================================
--- src/js/JEditor/Commons/Basefile/Models/SocialsCollection.js	(nonexistent)
+++ src/js/JEditor/Commons/Basefile/Models/SocialsCollection.js	(révision 13780)
@@ -0,0 +1,24 @@
+define([
+    "JEditor/Commons/Ancestors/Models/DBCollection",
+    "JEditor/Commons/Basefile/Models/Svg"
+],function(
+	DBCollection,
+	Svg
+){
+var SocialsCollection = DBCollection.extend(
+    /**
+     * @lends SocialsCollection.prototype
+     */
+    {
+        model: Svg,
+        url:  __IDEO_API_PATH__ + "/resources-svgcollection/social",
+        /**
+         * initialize l'objet
+         */
+        initialize: function() {
+            this._super();
+        }
+
+    });
+return SocialsCollection;
+});
\ No newline at end of file
Index: src/js/JEditor/Commons/Files/Templates/socialSvgSelector.html
===================================================================
--- src/js/JEditor/Commons/Files/Templates/socialSvgSelector.html	(nonexistent)
+++ src/js/JEditor/Commons/Files/Templates/socialSvgSelector.html	(révision 13780)
@@ -0,0 +1,14 @@
+<div class="container-icon option-content baseFile">
+    <div class="grid-container-icon content">
+        <%for(i=0;i<content.length;i++){
+            file = content[i]; %>
+            <div  class="oneFile <%=selected === file.name ? 'selected-icon':''%>" data-name="<%= file.name%>" data-id="<%=i%>" >
+                <span class="select"><span class="icon-check"></span></span>
+                <span class="wrapper-icon">
+                    <%= file.content%>
+                </span>
+            </div>
+            <%}%>
+    </div>
+</div>
+
Index: src/js/JEditor/Commons/Files/Views/SocialSvgSelectorDialog.js
===================================================================
--- src/js/JEditor/Commons/Files/Views/SocialSvgSelectorDialog.js	(nonexistent)
+++ src/js/JEditor/Commons/Files/Views/SocialSvgSelectorDialog.js	(révision 13780)
@@ -0,0 +1,79 @@
+define([
+    "jquery",
+    "underscore",
+    "text!../Templates/socialSvgSelector.html",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/DialogView",
+    "JEditor/ParamsPanel/Models/Params",
+    "JEditor/Commons/Basefile/Models/SocialsCollection",
+    "JEditor/Commons/Basefile/Models/Svg",
+    "i18n!../nls/i18n",
+    //not in params
+    "jqueryPlugins/dropdown"
+], function($, _, socialSvgSelector, Events, DialogView,Params,SocialsCollection,Svg, translate) {
+    var SocialSvgSelectorDialog = DialogView.extend({
+        className: 'socialSvgSelector',
+        events: {
+            'click .oneFile': 'onSelect',
+        },
+        currentList: [],
+        currentType : 'social',
+        constructor: function(options) {
+            var opts = _.extend({
+                title: translate('browseIconTitle'),
+                buttons: [
+                    {
+                        text: translate('choose'),
+                        class: 'okay',
+                        click: _.bind(this.onOk, this)
+                    },
+                    {
+                        text: translate('cancel'),
+                        class: 'cancel',
+                        click: _.bind(this.onCancel, this)
+                    }
+                ],
+                width: 720,
+                height: 600,
+                allowMultipleSelect: false
+            }, options);
+            return DialogView.call(this, opts);
+        },
+        initialize: function(options) {
+            this._super();
+            this._template = this.buildTemplate(socialSvgSelector, translate);
+            this.svgCollection = SocialsCollection.getInstance();
+            this.currentList = this.options.svgList;
+            this.render();
+        },
+        render: function() {
+            this.selected = {};
+            var data = (this.svgCollection === undefined ? [] : this.svgCollection);
+            this._super();
+            this.undelegateEvents();
+            this.$el.html(this._template({content:data.toJSON() , type:'social', selected: this.selected}));
+            this.dom[this.cid].selections = this.$('.oneFile');
+            this.dom[this.cid].type = this.$('.onglet.mycollections');
+            this.delegateEvents();
+            return this;
+        },
+        onCancel: function() {
+            $('.box-icon').css("background-color", "transparent");
+            this.selected = {};
+            this.$el.dialog('close');
+        },
+        onOk: function() {
+            this.trigger(Events.ListSocialNetworks.SELECT_ICON, this.selected);
+            this.$el.dialog('close');
+        },
+        onSelect: function(e) {
+            var $target = $(e.currentTarget), name = $target.data('name');
+            this.dom[this.cid].selections.removeClass('selected');
+            this.selected = this.svgCollection.findWhere({name:name});
+            $target.toggleClass('selected');
+            this.trigger(Events.ListViewEvents.SELECT,this.selected, this.selected?1:0,this.svgCollection);
+        },
+    
+    });
+    return SocialSvgSelectorDialog;
+});
Index: src/js/JEditor/Commons/Files/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/Commons/Files/nls/fr-ca/i18n.js	(révision 13779)
+++ src/js/JEditor/Commons/Files/nls/fr-ca/i18n.js	(révision 13780)
@@ -41,5 +41,6 @@
     "emptyNewCollection" :"Votre nouvelle collection d’images est vide.",
     "edit" :"Éditer",
     "delete":"Supprimer",
-    "allF"  :"Toutes"
+    "allF"  :"Toutes",
+    "browseIconTitle":"Parcourir la base d'icônes",
 });
\ No newline at end of file
Index: src/js/JEditor/Commons/Files/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/Commons/Files/nls/fr-fr/i18n.js	(révision 13779)
+++ src/js/JEditor/Commons/Files/nls/fr-fr/i18n.js	(révision 13780)
@@ -43,4 +43,5 @@
     "edit" :"Éditer",
     "delete":"Supprimer",
     "allF"  :"Toutes",
+    "browseIconTitle":"Parcourir la base d'icônes",
 });
\ No newline at end of file
Index: src/js/JEditor/Commons/Files/nls/i18n.js
===================================================================
--- src/js/JEditor/Commons/Files/nls/i18n.js	(révision 13779)
+++ src/js/JEditor/Commons/Files/nls/i18n.js	(révision 13780)
@@ -43,5 +43,6 @@
     "edit" : "Edit",
     "delete": "Delete",
     "allF" : "All",
-    "noneF":"None"
+    "noneF":"None",
+    "browseIconTitle":"Browse the icon database",
 }, "fr-fr":true, "fr-ca":true })
\ No newline at end of file
Index: src/js/JEditor/ParamsPanel/ParamsPanel.js
===================================================================
--- src/js/JEditor/ParamsPanel/ParamsPanel.js	(révision 13779)
+++ src/js/JEditor/ParamsPanel/ParamsPanel.js	(révision 13780)
@@ -17,11 +17,11 @@
     "./Views/CustomShortcode",
     "./Views/ImageCompressionQualityView",
     "./Models/Params",
-
+    "JEditor/Commons/Basefile/Models/SocialsCollection",
     //hidden
     "jqueryui/datepicker"
 ],
-        function ($, _, PanelView, Events, ParamsPanelTemplate, translate, SocialNetworks,SEOView, MobileStoreView, PolitiqueConfidentialiteView,DonneeStructureeView, MarqueClientView, FontsGoogleView, IconsCollectionView,ThemeSiteView,CustomShortcode,ImageCompressionQualityView,Params) {
+        function ($, _, PanelView, Events, ParamsPanelTemplate, translate, SocialNetworks,SEOView, MobileStoreView, PolitiqueConfidentialiteView,DonneeStructureeView, MarqueClientView, FontsGoogleView, IconsCollectionView,ThemeSiteView,CustomShortcode,ImageCompressionQualityView,Params,SocialsCollection) {
             var SocialPanel = PanelView.extend(
                     /**
                      * @lends JEditor.Panels.MessagePanel.prototype
@@ -57,6 +57,7 @@
                                     this._super();
                                     this._template = this.buildTemplate(ParamsPanelTemplate, translate);
                                     this.params = Params.getInstance();
+                                    this.svgCollection = SocialsCollection.getInstance();
                                     this.menuEntries = {
                                         MarqueClient:{
                                             object: new MarqueClientView({model:this.params}),
@@ -155,10 +156,20 @@
                                  * @return undefined;
                                  */
                                 load: function () {
-                                    this.listenToOnce(this.params, Events.BackboneEvents.SYNC, this.loadingEnd);
+                                    var loaded = 0;
+                                    function onLoaded() {
+                                        loaded++;
+                                        if (loaded === 2) {
+                                          this.loadingEnd();
+                                        }
+                                      }
+                        
+                                    this.listenToOnce(this.svgCollection, Events.BackboneEvents.SYNC, onLoaded);
+                                    this.listenToOnce(this.params, Events.BackboneEvents.SYNC, onLoaded);
                                     this.params.fetch();
+                                    this.svgCollection.fetch();
+
                                     this.loadCss(this.cssFile);
-                                    //   this.loadingEnd();
                                 },
                                 /**
                                  * déclenché lors de la fin du chargement du panneau
Index: src/js/JEditor/ParamsPanel/Templates/listSocial.html
===================================================================
--- src/js/JEditor/ParamsPanel/Templates/listSocial.html	(nonexistent)
+++ src/js/JEditor/ParamsPanel/Templates/listSocial.html	(révision 13780)
@@ -0,0 +1,40 @@
+<ul class="sortable">
+    
+    <%
+        for(var i=0;i< liste.length; i++){
+            var social=liste[i];
+            if(social.type === "googleUrl")
+                continue;
+    %>
+        <li  class="inline-params  <%=social.url?" ":"disabled "%>  thin-border  radius  shadow trash" id="I<%=i%>" name="<%=social.title%>">
+                <span class="inline-params__name   <%=social.network%>">
+                    <%=social.icon%>
+                
+                    <% 
+                     var title= (social.title ==="")? social.network : social.title;
+                    %>
+                    <input class="content rs" type="text" name="<%=social.type%>_<%=i %>" value='<%-title%>' data-id="<%=i%>" data-titleId="<%=social.type%>_<%=i %>" data-reseaux="Airbnb" />
+                
+                </span>
+            <label>
+                <span class="custom-input">
+                    <input type="text" data-autosave="true" name="<%=social.type%>_<%=i %>" class="field-input neutral-input  bold" value="<%=social.url%>" />
+                </span>
+            </label>
+            <span class="rightShortcode">
+                <% if (typeof(social.network) !== 'undefined') { %>
+                    <% if (social.network === 'twitter') { %>
+                        <% social.network = 'x'; %>
+                    <% } %>
+                    <span class="shortcode">[[social_link|network=<%= social.network %>]]</span>
+                <% } %>
+
+                <% if( typeof(social.id) !== 'undefined') { %><span class="shortcode">[[social_link|id=<%=social.id %>]]</span><% } %>
+            </span>
+            <span class="rigth-delete icon-bin"> </span>
+        </li>
+    <%
+     }
+    %> 
+
+</ul>
\ No newline at end of file
Index: src/js/JEditor/ParamsPanel/Views/ListSocialNetworks.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/ListSocialNetworks.js	(révision 13779)
+++ src/js/JEditor/ParamsPanel/Views/ListSocialNetworks.js	(révision 13780)
@@ -3,11 +3,14 @@
     "underscore",
     'JEditor/Commons/Ancestors/Views/View',
     'JEditor/Commons/Events', 
-    'text!../Templates/ListSocialNetwork.html',
+   // 'text!../Templates/ListSocialNetwork.html',
+    'text!../Templates/listSocial.html',
     "JEditor/App/Messages/Confirm",
     "JEditor/App/Messages/ClipboardModule", 
+    "../Models/SocialList",
+    "JEditor/Commons/Basefile/Models/SocialsCollection",
     'i18n!../nls/i18n'],
-    function ($, _, View, Events, template, Confirm, ClipboardModule, translate) {
+    function ($, _, View, Events, listSocial, Confirm, ClipboardModule, SocialList,SocialsCollection, translate) {
     var ListSocialNetworks = View.extend({
         events: {
             //"blur .content": "onChangeTitle",
@@ -14,14 +17,15 @@
             "change .content.rs": "onChangeTitle",
             "click .rigth-delete " :"DeleteOneSocialNetwork",
             'click .rightShortcode .shortcode' : 'copyToClipboard',
+            'click  .customrs svg, .customrs-img' : "onCustomRsImageClick"
         },
         initialize: function () {
-            this.model=this.options.model;
-            this.data=this.options.model.toJSON();
-            this.data=this.data.SocialNetworksUrl;
             this._super();
-            this._template = this.buildTemplate(template, translate);
-            
+            this.model = this.options.model;
+            this.svgCollection = SocialsCollection.getInstance();
+           // this._template = this.buildTemplate(template, translate);
+            this._listTemplate = this.buildTemplate(listSocial, translate);
+            this.selectSvgDialog = this.options.selectSvgDialog;
             this.listenTo(this.model, Events.BackboneEvents.CHANGE, this.onChange);
         },
         ___onInputChange: function ($target) {
@@ -142,23 +146,74 @@
                 parent.attr('name', TitleValue);
             }
         },
-        renderAddListe :function(oneliste){
-            this.undelegateEvents();
-            this.$el.html(this._template({ liste: oneliste}));
-            this.delegateEvents();
-            return this;
-        },
+        // renderAddListe :function(oneliste){
+        //     this.undelegateEvents();
+        //     this.$el.html(this._template({ liste: oneliste}));
+        //     this.delegateEvents();
+        //     return this;
+        // },
         copyToClipboard : function (e){
             ClipboardModule.copyToClipboard(e);
         },
         render: function () {
             this.undelegateEvents();
-            
-            this.$el.html(this._template({ liste: (this.data != null? this.data :0)}));
-            
+            var socialList = SocialList;
+            data = _.clone(this.model.toJSON());
+            data = data.SocialNetworksUrl;
+            var liste = data.map(function(social) {
+                var item = Object.assign({}, social);
+                if (social.type === 'customrsUrl') {
+                    var svgData = '';
+                    if (social.svg) {
+                        var svgName = social.svg;
+                        svgData = this.svgCollection.find(function(item) {
+                            return item.attributes.name === svgName;
+                        });
+                    }
+                    if (svgData) {
+                        item.icon = svgData.attributes.content;
+                    }
+                    else {
+                        item.icon = "<span class='customrs-img'></span>";
+                    }
+                }
+                else {
+                    item.icon = (socialList[social.type]) ? socialList[social.type].icon : "<span class='customrs-img'></span>";
+                }
+                
+                return item; // Retourner l'élément modifié
+            }, this);
+
+            this.$el.html(this._listTemplate({liste:liste}));
             this.delegateEvents();
             return this;
+        },
+        onCustomRsImageClick : function(e){
+            this.dataId =  $(e.currentTarget).closest('.inline-params__name').find('input').data('id');
+            this.selectSvgDialog.open();
+            this.stopListening(this.selectSvgDialog, Events.ListSocialNetworks.SELECT_ICON);
+            this.listenTo(this.selectSvgDialog, Events.ListSocialNetworks.SELECT_ICON, this.onSelectedSvgWithStop);
+        },
+        onSelectedSvgWithStop: function(selectedSvg) {
+           
+            this.onSelectedSvg(selectedSvg, this.dataId);
+
+            // Arrêter l'écoute de l'événement après traitement
+            this.stopListening(this.selectSvgDialog, Events.ListSocialNetworks.SELECT_ICON);
+        },
+        onSelectedSvg:function (params,id) {
+            var ns=this.model.get("SocialNetworksUrl");
+            ns[id].svg = params.attributes.name;
+            this.model.set("SocialNetworksUrl",ns);
+            this.model.save();
+            this.trigger("updateOneSocialNetwork");
+            
         }
     });
+    Events.extend({
+        ListSocialNetworks: {
+            SELECT_ICON: 'selectIcon',
+        },
+    })
     return ListSocialNetworks;
 });
\ No newline at end of file
Index: src/less/imports/params_panel/module/inline-social.less
===================================================================
--- src/less/imports/params_panel/module/inline-social.less	(révision 13779)
+++ src/less/imports/params_panel/module/inline-social.less	(révision 13780)
@@ -23,6 +23,9 @@
         float: right;
         margin-right: 20px;
     }
+    .social-network.dropdown-menu li .customrs span.icon {
+        display: inline-block;
+    }
 
 }
 
@@ -68,8 +71,15 @@
             margin-right: 10px;
             padding-right: 10px;
         }
+        &.customrs .customrs-img{
+            cursor: pointer;
+            background: #ccc;
+        }
+        &.customrs svg{
+            cursor: pointer;
+        }
 
-		svg, img {
+		svg, img, .customrs-img {
 			.align-middle();
 			.square(35px);
 			position: relative;
Index: src/js/JEditor/ParamsPanel/Models/Params.js
===================================================================
--- src/js/JEditor/ParamsPanel/Models/Params.js	(révision 13779)
+++ src/js/JEditor/ParamsPanel/Models/Params.js	(révision 13780)
@@ -47,7 +47,8 @@
         quotes:/^(https?:\/\/)?(www\.)?(?!.*['"]).+\/(.*)$/,
         borneoUrl:/^(https?\:\/\/)?(www\.)?borneoapp\.com\/(.*)/,
         boncoinUrl:/^(https?\:\/\/)?(www\.)?leboncoin\.fr\/(.*)/,
-        pagesjauneUrl:/^(https?\:\/\/)?(www\.)?pagesjaunes\.fr\/(.*)/
+        pagesjauneUrl:/^(https?\:\/\/)?(www\.)?pagesjaunes\.fr\/(.*)/,
+        customrsUrl : /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/
     };  
     //verification for google fonts
 
@@ -109,7 +110,7 @@
         },
         initialize: function () {
             this._super();
-            this.on("change:facebookUrl change:twitterUrl change:xUrl change:pinterestUrl change:mybusinessUrl change:youtubeUrl change:linkedinUrl change:viadeoUrl change:instagramUrl change:skypeUrl change:theforkUrl change:tiktokUrl change:tripadvisorUrl change:wazeUrl change:whatsappUrl change:slideshareUrl change:borneoUrl change:boncoinUrl change:pagesjauneUrl", this.onSocialNetworkUrlChange);
+            this.on("change:facebookUrl change:twitterUrl change:xUrl change:pinterestUrl change:mybusinessUrl change:youtubeUrl change:linkedinUrl change:viadeoUrl change:instagramUrl change:skypeUrl change:theforkUrl change:tiktokUrl change:tripadvisorUrl change:wazeUrl change:whatsappUrl change:slideshareUrl change:borneoUrl change:boncoinUrl change:pagesjauneUrl, change:customrsUrl", this.onSocialNetworkUrlChange);
             this.on("change:MarqueClient", this.onMarqueClientChange);
         },
         onSocialNetworkUrlChange: function (model) {
@@ -117,7 +118,8 @@
             var that = this;
             _.each(changed, function (value, key) {
                 if ((key === "facebookUrl" || key === "twitterUrl" || key === "xUrl" || key === "pinterestUrl" || key === "mybusinessUrl" || key==="instagramUrl"||key==="youtubeUrl"||key==="linkedinUrl"||key==="viadeoUrl" || 
-                    key==="skypeUrl" || key==="theforkUrl" || key ==="tiktokUrl" || key ==="tripadvisorUrl" || key==="wazeUrl" || key==="whatsappUrl" || key==="slideshareUrl" || key==="borneoUrl" || key==="boncoinUrl" || key==="pagesjauneUrl")) {
+                    key==="skypeUrl" || key==="theforkUrl" || key ==="tiktokUrl" || key ==="tripadvisorUrl" || key==="wazeUrl" || key==="whatsappUrl" || key==="slideshareUrl" || key==="borneoUrl" || key==="boncoinUrl" || key==="pagesjauneUrl" 
+                    || key === "customrsUrl")) {
                     that.trigger(Events.SettingsEvents.SOCIAL_NETWORK__CHANGE, key, value, model);
                 }
             });
@@ -145,7 +147,7 @@
        
 
             var socialNetworks = ["facebookUrl", "mybusinessUrl", "twitterUrl", "xUrl", "pinterestUrl", "youtubeUrl", "linkedinUrl", "viadeoUrl", "instagramUrl","skypeUrl","theforkUrl","tiktokUrl","tripadvisorUrl","wazeUrl","whatsappUrl","slideshareUrl", 
-                "borneoUrl", "boncoinUrl", "pagesjauneUrl"
+                "borneoUrl", "boncoinUrl", "pagesjauneUrl", "customrsUrl"
             ];
             //en gros c'est ça dans une boucle:
             for (var i = 0; i < socialNetworks.length; i++) {             
Index: src/js/JEditor/ParamsPanel/Models/SocialList.js
===================================================================
--- src/js/JEditor/ParamsPanel/Models/SocialList.js	(nonexistent)
+++ src/js/JEditor/ParamsPanel/Models/SocialList.js	(révision 13780)
@@ -0,0 +1,315 @@
+define({
+    airbnbUrl: {
+        icon: `<svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="none" viewBox="0 0 50 50"><path fill="#FF5A5F" d="M21.767 27.533c.4 1.8 1.566 3.934 3.366 6.2 1.8-2.3 2.967-4.433 3.367-6.2.167-.733.2-1.433.1-2.033-.067-.533-.267-1-.533-1.4-.6-.933-1.7-1.467-2.934-1.467-1.233 0-2.3.567-2.933 1.467-.267.4-.467.867-.533 1.4-.1.6-.067 1.267.1 2.033Z M35.4 39.2a4.953 4.953 0 0 0 3.033-3.9c.134-.867.034-1.7-.233-2.567a6.418 6.418 0 0 0-.283-.717c-.049-.111-.1-.227-.15-.35-.134-.266-.259-.55-.384-.833-.125-.283-.25-.566-.383-.833v-.033a442.323 442.323 0 0 0-7.3-15l-.1-.2c-.133-.25-.267-.509-.4-.767a64.155 64.155 0 0 0-.4-.767c-.267-.533-.567-1.033-.933-1.466-.7-.8-1.634-1.234-2.667-1.234s-1.967.434-2.667 1.234c-.366.433-.666.933-.933 1.466-.133.25-.267.509-.4.767s-.267.517-.4.767l-.1.2a364.37 364.37 0 0 0-7.333 14.966l-.034.067c-.266.567-.533 1.133-.766 1.667-.051.122-.102.237-.151.349a6.418 6.418 0 0 0-.283.717 5.519 5.519 0 0 0-.3 2.6 4.954 4.954 0 0 0 3.034 3.9 5.33 5.33 0 0 0 2.533.334c.833-.1 1.667-.367 2.533-.867 1.2-.667 2.434-1.7 3.8-3.233-2.166-2.7-3.533-5.2-4.033-7.4a6.92 6.92 0 0 1-.167-2.834 5.41 5.41 0 0 1 .9-2.266c1.034-1.467 2.767-2.367 4.7-2.367 1.934 0 3.667.867 4.7 2.367.467.666.767 1.433.9 2.266.1.867.067 1.8-.166 2.834-.5 2.2-1.834 4.666-4.034 7.366 1.4 1.534 2.6 2.567 3.8 3.234.867.5 1.7.766 2.534.866.866.1 1.733 0 2.533-.333Z M8 0a8 8 0 0 0-8 8v34a8 8 0 0 0 8 8h34a8 8 0 0 0 8-8V8a8 8 0 0 0-8-8H8Zm32 31.593c.056.137.111.274.167.407.433 1.233.566 2.4.3 3.567-.367 2.466-2 4.6-4.334 5.533-.833.367-1.766.533-2.7.533a8.807 8.807 0 0 1-4.2-1.2c-1.333-.766-2.666-1.866-4.133-3.466-1.467 1.6-2.767 2.7-4.133 3.466a8.807 8.807 0 0 1-4.2 1.2 6.846 6.846 0 0 1-2.7-.533 7.084 7.084 0 0 1-4.334-5.533c-.166-1.2-.033-2.367.4-3.6.167-.434.334-.834.5-1.2.122-.274.244-.54.362-.8.141-.31.278-.61.405-.9l.033-.067c2.3-5 4.8-10.067 7.367-15.067l.1-.2c.267-.5.533-1.033.8-1.566.333-.6.7-1.234 1.2-1.834 1.1-1.266 2.6-2 4.267-2 1.633 0 3.166.734 4.233 2.067.492.553.814 1.135 1.122 1.693l.078.14c.267.534.533 1.067.8 1.567l.1.2c2.6 5 5.067 10.067 7.367 15.067l.033.033c.127.29.264.59.405.9.118.26.24.526.362.8.11.244.222.519.333.793Z"/></svg>`,
+        name: "airbnb",
+        defaultTitle: "Airbnb"
+    },
+    bookingUrl : {
+        icon: `<svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="none" viewBox="0 0 50 50"><path fill="#0C3B7C" fill-rule="evenodd" d="m22.724 32.907-4.027-.004v-4.815c0-1.029.399-1.564 1.28-1.687h2.747c1.96 0 3.227 1.236 3.227 3.235 0 2.054-1.236 3.27-3.227 3.271Zm-4.027-14.255c0-1.11.47-1.637 1.498-1.705h2.062c1.766 0 2.825 1.057 2.825 2.828 0 1.348-.726 2.922-2.761 2.922h-3.624v-4.045Z M8 0a8 8 0 0 0-8 8v42h42a8 8 0 0 0 8-8V8a8 8 0 0 0-8-8H8Zm19.866 24.725-.728-.41.636-.543c.74-.636 1.98-2.066 1.98-4.532 0-3.779-2.93-6.216-7.465-6.216h-5.175v-.002h-.59a2.526 2.526 0 0 0-2.434 2.495v21.44h8.301c5.04 0 8.293-2.744 8.293-6.994 0-2.288-1.05-4.244-2.818-5.238Zm4.98 9.27a2.955 2.955 0 0 1 2.948-2.963 2.963 2.963 0 0 1 0 5.926 2.956 2.956 0 0 1-2.948-2.963Z"/></svg>`,
+        name: "booking",
+        defaultTitle: "Booking.com"
+    },
+    appleUrl : {
+        icon: `<svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="none" viewBox="0 0 50 50"><path fill="#000" fill-rule="evenodd" d="M8 0a8 8 0 0 0-8 8v34a8 8 0 0 0 8 8h34a8 8 0 0 0 8-8V8a8 8 0 0 0-8-8H8Zm17.075 11.825c.825 0 3.5-.525 5.575-3.55 1.775-2.625 1.575-5.55 1.575-5.55S29.15 2.5 26.45 5.85c-1.95 2.45-1.8 5.95-1.8 5.95s.15.025.425.025ZM40.5 31.05c.15-.425.25-.675.25-.675h-.025s-.025 0-.05-.025c-.15-.05-.5-.2-.95-.475-.9-.525-2.15-1.5-3.05-3.075-.575-1-.975-2.25-1.075-3.8a7.864 7.864 0 0 1 .175-2.2v-.025c.05-.225.1-.425.175-.65.8-2.6 2.675-4.3 3.425-4.875l.3-.225s-2.4-3.725-6.925-3.725c-2.255 0-3.691.585-4.902 1.079-.884.36-1.648.671-2.523.671-1.15 0-1.863-.33-2.646-.694-.952-.441-2.007-.931-4.079-.931-1.95 0-4.675.75-6.7 3.375-1.6 2.05-2.2 4.475-2.45 6.25-.15 1.2-.2 2.5-.05 3.95 0 .047.006.094.011.144.007.056.014.115.014.181.15 1.45.525 3 1.05 4.55a.19.19 0 0 0 .05.1c.05.175.125.375.2.55.013.038.031.081.05.125a1.7 1.7 0 0 1 .05.125l.015.046c.02.066.045.143.085.204.5 1.3 1.075 2.55 1.75 3.725 1.95 3.45 4.475 6.05 6.85 6.05 1.344 0 2.23-.433 3.113-.866.891-.436 1.78-.872 3.137-.859 1.628.012 2.696.51 3.678.966 1.085.505 2.063.96 3.572.659 1.925-.375 4.225-3.1 5.7-5.7.05-.1.1-.175.15-.25.7-1.5 1.3-2.9 1.625-3.7Z"/></svg>`,
+        name: "apple",
+        defaultTitle: "Apple Store"
+    },
+    googleplayUrl : {
+        icon: `<svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="none" viewBox="0 0 50 50"><path fill="#EA4335" d="M24.625 24 5.375 44.125c.625 2.125 2.625 3.75 5 3.75 1 0 1.875-.25 2.625-.75L34.75 34.75 24.625 24Z"/><path fill="#FBBC04" d="m44.125 20.5-9.375-5.375-10.5 9.25L34.875 34.75l9.375-5.25C45.875 28.625 47 26.875 47 25c-.125-1.875-1.25-3.625-2.875-4.5Z"/><path fill="#4285F4" d="M5.375 5.875c-.125.375-.125.875-.125 1.375v35.625c0 .5 0 .875.125 1.375l20-19.625-20-18.75Z"/><path fill="#34A853" d="m24.75 25 10-9.875-21.625-12.25c-.75-.5-1.75-.75-2.75-.75-2.375 0-4.5 1.625-5 3.75L24.75 25Z"/></svg>`,
+        name: "googleplay",
+        defaultTitle: "Google Play"
+    },
+    vimeoUrl : {
+        icon: `<svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="none" viewBox="0 0 50 50"><path fill="#19B1E3" fill-rule="evenodd" d="M8 0a8 8 0 0 0-8 8v34a8 8 0 0 0 8 8h34a8 8 0 0 0 8-8V8a8 8 0 0 0-8-8H8Zm29.406 10.003c3.537.11 5.196 2.432 4.974 6.854-.11 3.317-2.432 7.85-6.964 13.93-4.643 6.08-8.623 9.064-11.829 9.064-1.99 0-3.648-1.879-5.085-5.527a79.365 79.365 0 0 1-.477-1.7 138.43 138.43 0 0 0-.96-3.385 137.905 137.905 0 0 1-.96-3.386c-.184-.67-.34-1.242-.477-1.7-.995-3.648-2.1-5.527-3.317-5.527-.22 0-1.105.553-2.653 1.658L8 18.184c.553-.442 1.437-1.216 2.543-2.211 1.105-.995 1.99-1.769 2.542-2.211 2.211-1.88 3.87-2.985 5.085-3.095 2.764-.332 4.312 1.547 4.975 5.417a186.87 186.87 0 0 0 1.437 7.848c.774 3.427 1.659 5.196 2.543 5.196.663 0 1.769-1.216 3.206-3.427 1.437-2.321 2.21-3.98 2.321-5.196.221-1.99-.553-2.984-2.321-2.984-.774 0-1.659.11-2.543.552 1.658-5.527 4.864-8.18 9.618-8.07Z"/></svg>`,
+        name: "vimeo",
+        defaultTitle: "Vimeo"
+    },
+    doordashUrl : {
+        icon: `<svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="none" viewBox="0 0 50 50"><path fill="#FF3008" fill-rule="evenodd" d="M8 0a8 8 0 0 0-8 8v34a8 8 0 0 0 8 8h34a8 8 0 0 0 8-8V8a8 8 0 0 0-8-8H8Zm25.333 15c3.238-.01 6.215 1.745 7.736 4.566v-.003c3.914 7.337-1.383 14.729-8.389 14.729h-5.384a2.529 2.529 0 0 1-1.781-.73l-5.34-5.3a.823.823 0 0 1-.18-.906.843.843 0 0 1 .776-.514h11.905c1.23-.012 2.218-1.006 2.204-2.22-.013-1.213-1.019-2.188-2.25-2.175H15.363c-.668 0-1.31-.263-1.78-.732L8.244 16.42a.824.824 0 0 1 .128-1.278.844.844 0 0 1 .464-.142h24.496Z"/></svg>`,
+        name: "doordash",
+        defaultTitle: "Doordash"
+    },
+    snapchatUrl : {
+        icon: `<svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="none" viewBox="0 0 50 50"><path fill="#FFFC00" fill-rule="evenodd" d="M8 0a8 8 0 0 0-8 8v34a8 8 0 0 0 8 8h34a8 8 0 0 0 8-8V8a8 8 0 0 0-8-8H8Zm17.356 7.375h-.093l-.645.007c-.832 0-2.497.118-4.286.907a9.55 9.55 0 0 0-2.734 1.813c-.943.896-1.712 2-2.284 3.284-.838 1.878-.639 5.043-.48 7.585l.002.004.004.08.047.754a1.467 1.467 0 0 1-.616.116c-.473 0-1.034-.15-1.67-.448a1.493 1.493 0 0 0-.635-.132c-.379 0-.777.112-1.123.315-.435.256-.717.617-.793 1.017-.05.265-.048.788.534 1.32.32.292.79.561 1.396.8.158.063.346.123.545.186l.005.002c.694.22 1.743.553 2.017 1.198.138.326.079.757-.178 1.277l-.002.005-.017.037c-.064.15-.661 1.503-1.887 2.93a10.61 10.61 0 0 1-2.278 2.016 8.377 8.377 0 0 1-3.198 1.249.878.878 0 0 0-.735.916c.008.129.038.258.09.382l.002.003c.179.418.594.774 1.27 1.088.824.383 2.057.705 3.665.958.08.151.162.527.22.793l.004.015c.061.283.125.573.216.882.098.334.352.733 1.005.733.245 0 .526-.054.85-.117l.012-.002c.483-.095 1.143-.224 1.967-.224.457 0 .93.04 1.407.12.918.152 1.71.712 2.627 1.36 1.34.947 2.859 2.02 5.178 2.02.064 0 .127-.002.19-.006.076.004.171.006.272.006 2.32 0 3.837-1.073 5.176-2.02h.001l.003-.003c.916-.646 1.707-1.205 2.625-1.358.477-.079.95-.12 1.407-.12.787 0 1.41.101 1.967.21.363.071.645.106.862.106h.043c.478 0 .829-.262.963-.721.089-.302.152-.586.215-.873v-.005c.056-.25.142-.645.223-.8 1.609-.252 2.842-.574 3.666-.957.674-.312 1.089-.668 1.269-1.085.054-.125.085-.254.092-.386a.878.878 0 0 0-.734-.916c-5.012-.826-7.27-5.976-7.363-6.194a.587.587 0 0 0-.015-.034l-.005-.01c-.256-.52-.316-.949-.176-1.276.273-.645 1.323-.978 2.018-1.199.2-.063.39-.123.548-.186.684-.27 1.173-.563 1.495-.896.385-.396.46-.776.456-1.025-.012-.602-.472-1.137-1.204-1.398a2.152 2.152 0 0 0-.816-.157 1.82 1.82 0 0 0-.758.155c-.587.275-1.112.425-1.563.446a1.414 1.414 0 0 1-.517-.114l.039-.63.006-.097.006-.108c.16-2.544.36-5.711-.478-7.591-.574-1.288-1.346-2.395-2.293-3.293a9.572 9.572 0 0 0-2.744-1.813 10.914 10.914 0 0 0-4.285-.9Z"/></svg>`,
+        name: "snapchat",
+        defaultTitle: "Snapchat"
+    },
+    yelpUrl : {
+        icon: `<svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="none" viewBox="0 0 50 50"><path fill="#D32323" fill-rule="evenodd" d="M8 0h34a8 8 0 0 1 8 8v34a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8a8 8 0 0 1 8-8Zm15.25 6.292c.5.166.875.5 1 .958.083.542.833 11.793 1 14.334.083 2.164-.832 2.456-1.125 2.539-.292.125-1.208.375-2.333-1.458 0 0-7.625-12.167-7.792-12.542-.125-.413 0-.917.375-1.292 1.125-1.206 7.292-2.956 8.875-2.539Zm-1.208 22.333c-.042.708-.459 1.292-1.084 1.458l-2.455.834c-5.542 1.791-5.707 1.833-6 1.833-.458 0-.833-.292-1.083-.75-.167-.333-.292-.917-.375-1.583-.21-2.042.039-5.125.625-6.084.287-.458.708-.708 1.166-.666.292 0 .543.083 6.5 2.583l1.747.708c.625.25 1 .917.959 1.667Zm.208 4.167c.417-.542 1.167-.71 1.833-.417.667.25 1.084.833 1 1.458v2.663c0 5.875-.041 6.082-.125 6.375a1.275 1.275 0 0 1-1.041.837c-1.5.25-6.125-1.5-7.084-2.666-.202-.2-.32-.467-.333-.75-.013-.185.015-.37.083-.542.125-.292.292-.5 4.459-5.5l1.208-1.458Zm10.376-1.33c5.541 1.835 5.707 1.918 5.957 2.085v-.005c.375.291.5.75.418 1.255-.208 1.54-3.25 5.455-4.667 6.04-.5.21-.958.169-1.333-.124-.208-.208-.458-.583-3.75-6l-.958-1.625c-.375-.583-.293-1.332.166-1.916.459-.584 1.123-.792 1.709-.542.041 0 2.458.833 2.458.833Zm5.666-6.374c-.584.417-7.746 2.125-8.625 2.292v-.005c-.875.164-1.292-.167-1.709-.834-.416-.625-.416-1.334 0-1.834l1.542-2.12.888-1.227c2.52-3.485 2.678-3.704 2.904-3.855.375-.25.875-.25 1.333-.042 1.333.67 4.042 4.75 4.208 6.334v.04c.045.543-.166 1-.541 1.25Z"/></svg>`,
+        name: "yelp",
+        defaultTitle: "Yelp"
+    },
+    houzzUrl : {
+        icon: `<svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="none" viewBox="0 0 50 50"><path fill="#4DBC15" fill-rule="evenodd" d="M25 50c13.807 0 25-11.193 25-25S38.807 0 25 0 0 11.193 0 25s11.193 25 25 25ZM13.867 12.5h5.469v5.86l16.797 4.882V37.5h-8.301v-7.715h-5.566V37.5h-8.399v-25Z"/></svg>`,
+        name: "houzz",
+        defaultTitle: "Houzz"
+    },
+    ubereatsUrl : {
+        icon: `<svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="none" viewBox="0 0 50 50"><path fill="#06C167" fill-rule="evenodd" d="M23.12 22.248a2.936 2.936 0 0 1-2.95-2.947 2.94 2.94 0 0 1 4.076-2.722 2.932 2.932 0 0 1 1.808 2.722 2.944 2.944 0 0 1-2.933 2.947Zm9.639-5.91c-1.257 0-2.313.865-2.615 2.164h5.214c-.285-1.299-1.341-2.164-2.599-2.164ZM23.96 33.602a2.635 2.635 0 0 0-.753-1.866 2.57 2.57 0 0 0-1.842-.764 2.559 2.559 0 0 0-1.838.768 2.622 2.622 0 0 0-.757 1.862 2.649 2.649 0 0 0 .757 1.862 2.584 2.584 0 0 0 1.838.768c1.446 0 2.595-1.156 2.595-2.63Z M8 0a8 8 0 0 0-8 8v34a8 8 0 0 0 8 8h34a8 8 0 0 0 8-8V8a8 8 0 0 0-8-8H8Zm4.56 22.165c1.626 0 2.883-1.249 2.883-3.097v-7.193h1.76V23.53H15.46v-1.082c-.788.815-1.878 1.282-3.101 1.282-2.514 0-4.442-1.815-4.442-4.562v-7.292h1.76v7.192c0 1.881 1.24 3.097 2.883 3.097Zm5.95 1.365h1.677v-1.066a4.28 4.28 0 0 0 3.05 1.266c2.515 0 4.493-1.981 4.493-4.429 0-2.464-1.977-4.445-4.492-4.445a4.242 4.242 0 0 0-3.034 1.265v-4.245H18.51V23.53Zm9.89-4.246c0-2.397 1.895-4.412 4.392-4.412 2.465 0 4.275 1.881 4.275 4.412v.55H30.11c.235 1.381 1.392 2.414 2.834 2.414.988 0 1.826-.4 2.463-1.249l1.224.9c-.855 1.131-2.129 1.814-3.688 1.814-2.564 0-4.543-1.898-4.543-4.429Zm13.914-2.747v-1.565h-.586c-.939 0-1.626.433-2.045 1.116v-1.05h-1.677v8.492H39.7v-4.828c0-1.315.804-2.165 1.91-2.165h.704ZM16.058 26.25H7.917v11.656h8.14v-1.993h-5.941v-2.889H15.9v-1.933h-5.784v-2.849h5.942V26.25ZM38.18 38.125c2.496 0 3.902-1.196 3.902-2.85 0-1.175-.832-2.052-2.575-2.43l-1.842-.379c-1.07-.199-1.407-.398-1.407-.797 0-.518.516-.837 1.467-.837 1.03 0 1.782.28 2 1.235h2.16c-.12-1.792-1.407-2.988-4.022-2.988-2.258 0-3.843.937-3.843 2.75 0 1.255.872 2.072 2.754 2.47l2.06.478c.812.16 1.03.379 1.03.718 0 .538-.614.877-1.605.877-1.248 0-1.96-.279-2.238-1.236h-2.18c.318 1.793 1.645 2.989 4.339 2.989Zm-6.546-2.172h1.624v1.952H30.92c-1.466 0-2.278-.916-2.278-2.072v-4.582H27v-1.953h1.644v-2.45h2.18v2.45h2.436v1.953h-2.437v4.025c0 .458.317.677.813.677ZM23.92 29.3h2.16v8.607h-2.16v-.777a4.197 4.197 0 0 1-2.734.996c-2.555 0-4.555-2.012-4.555-4.523 0-2.51 2-4.523 4.555-4.523a4.197 4.197 0 0 1 2.734.997V29.3Z"/></svg>`,
+        name: "ubereats",
+        defaultTitle: "Uber Eats"
+    },
+    opentableUrl : {
+        icon: `<svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="none" viewBox="0 0 50 50"><path fill="#DD3743" fill-rule="evenodd" d="M28.872 27.86c-1.405 0-2.544-1.15-2.544-2.572 0-1.42 1.139-2.572 2.544-2.572s2.545 1.152 2.545 2.572c0 1.421-1.14 2.573-2.545 2.573Z M25 50c13.807 0 25-11.193 25-25S38.807 0 25 0 0 11.193 0 25s11.193 25 25 25Zm3.872-35c-5.62 0-10.177 4.607-10.177 10.288 0 5.683 4.556 10.29 10.177 10.29 5.622 0 10.178-4.607 10.178-10.29C39.05 19.608 34.494 15 28.872 15ZM11 25.288c0-1.42 1.14-2.572 2.544-2.572 1.405 0 2.545 1.152 2.545 2.572 0 1.421-1.14 2.573-2.545 2.573S11 26.71 11 25.288Z"/></svg>`,
+        name: "opentable",
+        defaultTitle: "OpenTable"
+    },
+    deliverooUrl : {
+        icon: `<svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="none" viewBox="0 0 50 50"><path fill="#35BEB2" fill-rule="evenodd" d="M22.975 28.266c.342-1.415 2.738-.8 2.497.596-.094.543-.502 1.018-1.088 1.04-1.088.042-1.674-.538-1.409-1.636Zm7.917.634c.125.27.137.578.035.857l-.088.239a1.12 1.12 0 0 1-1.434.665l-.253-.094a1.118 1.118 0 0 1-.662-1.438l.088-.24a1.119 1.119 0 0 1 1.434-.664l.252.093c.278.103.504.312.628.582Z M8 0a8 8 0 0 0-8 8v34a8 8 0 0 0 8 8h34a8 8 0 0 0 8-8V8a8 8 0 0 0-8-8H8Zm14.064 25.155a.044.044 0 0 1-.027.018l-10.959 2.288a.098.098 0 0 0-.076.079.1.1 0 0 0 0 .038l1.928 9.012a.08.08 0 0 0 .062.061l19.37 4.056a.123.123 0 0 0 .138-.07c1.406-3.074 2.801-6.157 4.187-9.25a3.97 3.97 0 0 0 .326-1.21c.678-6.427 1.358-12.847 2.036-19.26a.106.106 0 0 0-.093-.116l-7.608-.8a.122.122 0 0 0-.11.046c-.019.021-.031.066-.039.134a9761.66 9761.66 0 0 1-1.393 13.145.348.348 0 0 1-.014.07.015.015 0 0 1-.006.008.014.014 0 0 1-.006.003h-.004a.014.014 0 0 1-.014-.012L27.377 12.33c-.028-.13-.107-.182-.238-.155l-7.413 1.548a.1.1 0 0 0-.077.12l2.422 11.28a.044.044 0 0 1-.006.033Z"/></svg>`,
+        name: "deliveroo",
+        defaultTitle: "Deliveroo"
+    },
+    genericUrl : {
+        icon: `<svg width="16" height="16" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="#000000" class="icon-social"><path class="color1" d="M400.4,341.5c-20.2,0-38.7,7.1-53.3,18.9l-151.6-89.9c0.9-4.9,1.4-9.8,1.4-14.9c0-5-0.5-9.9-1.4-14.7l151.4-89.3 c14.6,11.8,33.2,19,53.5,19c47,0,85.2-38.2,85.2-85.2S447.4,0,400.4,0s-85.2,38.2-85.2,85.2c0,5,0.5,9.9,1.4,14.7l-151.4,89.3 c-14.6-11.8-33.2-19-53.5-19c-47,0-85.2,38.2-85.2,85.2s38.2,85.2,85.2,85.2c20.2,0,38.7-7.1,53.3-18.9l151.6,89.9 c-0.9,4.9-1.4,9.8-1.4,14.9c0,47,38.2,85.2,85.2,85.2s85.2-38.2,85.2-85.2S447.4,341.5,400.4,341.5z M400.4,59.8 c14,0,25.4,11.4,25.4,25.4s-11.4,25.4-25.4,25.4c-14,0-25.4-11.4-25.4-25.4S386.4,59.8,400.4,59.8z M111.6,281 c-14,0-25.4-11.4-25.4-25.4c0-14,11.4-25.4,25.4-25.4c14,0,25.4,11.4,25.4,25.4C137,269.6,125.6,281,111.6,281z M400.4,452.2 c-14,0-25.4-11.4-25.4-25.4c0-14,11.4-25.4,25.4-25.4c14,0,25.4,11.4,25.4,25.4C425.8,440.8,414.4,452.2,400.4,452.2z"/></svg>`,
+        name: "generic",
+        defaultTitle: "Generic"
+    },
+    facebookUrl : {
+        icon: `<svg width="50px" height="50px" viewBox="0 0 50 50" enable-background="new 0 0 50 50" xml:space="preserve">
+            <path fill="#36609F" d="M42.188,0H7.813C3.499,0,0,3.5,0,7.813v34.375C0,46.502,3.499,50,7.813,50h34.375C46.502,50,50,46.502,50,42.188V7.813C50,3.5,46.502,0,42.188,0z M30.355,25.009h-3.503c0,5.601,0,12.491,0,12.491h-5.191c0,0,0-6.824,0-12.491h-2.469v-4.413h2.469v-2.856c0-2.041,0.971-5.239,5.238-5.239l3.846,0.015V16.8c0,0-2.338,0-2.791,0c-0.454,0-1.102,0.229-1.102,1.202v2.594h3.958L30.355,25.009z"
+            />
+        </svg>`,
+        name: "fb",
+        defaultTitle: "Facebook"
+    },
+    twitterUrl : {
+        icon: `<svg width="20" height="20" viewBox="0 0 20 20" fill="#000" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M3.1252 0H16.8752C18.6008 0 20 1.4 20 3.1252V16.8752C20 18.6008 18.6008 20 16.8752 20H3.1252C1.3988 20 0 18.6008 0 16.8752V3.1252C0 1.4 1.3988 0 3.1252 0ZM16.5459 3L11.3335 8.92867L17 17H12.8309L9.01369 11.5636L4.235 17H3L8.46604 10.7834L3 3H7.16908L10.7829 8.14671L15.3109 3H16.5459ZM9.08675 10.0762L9.64142 10.8518L13.4131 16.1334H15.3103L10.687 9.66391L10.1347 8.88837L6.57759 3.91102H4.68037L9.08675 10.0762Z"/></svg>`,
+        name: "twitter",
+        defaultTitle: "twitter"
+    },
+    pinterestUrl : {
+        icon: `<svg width="30px" height="30px" viewBox="0 0 30 30" enable-background="new 0 0 30 30" xml:space="preserve"><path fill="#CB2128" d="M25.313,0H4.688C2.098,0,0,2.1,0,4.688v20.625C0,27.901,2.098,30,4.688,30h20.625C27.901,30,30,27.901,30,25.313V4.688C30,2.1,27.901,0,25.313,0z M16.998,19.449c-1.2,0-2.33-0.671-2.716-1.433c0,0-0.646,2.646-0.782,3.159c-0.482,1.805-1.899,3.611-2.008,3.758c-0.077,0.104-0.247,0.07-0.265-0.064c-0.031-0.231-0.392-2.51,0.033-4.367c0.214-0.933,1.431-6.26,1.431-6.26s-0.356-0.732-0.356-1.816c0-1.704,0.957-2.974,2.146-2.974c1.011,0,1.498,0.784,1.498,1.724c0,1.052-0.646,2.622-0.98,4.077c-0.279,1.218,0.591,2.212,1.755,2.212c2.107,0,3.526-2.796,3.526-6.106c0-2.52-1.642-4.403-4.628-4.403c-3.373,0-5.475,2.6-5.475,5.502c0,1.002,0.286,1.707,0.734,2.254c0.206,0.251,0.234,0.352,0.16,0.64c-0.054,0.212-0.175,0.72-0.227,0.923c-0.073,0.291-0.302,0.393-0.556,0.285c-1.554-0.654-2.277-2.411-2.277-4.387c0-3.265,2.664-7.178,7.949-7.178c4.246,0,7.04,3.174,7.04,6.582C22.999,16.083,20.573,19.449,16.998,19.449z"/></svg>`,
+        name: "pinterest",
+        defaultTitle: "Pinterest"
+    },
+    youtubeUrl : {
+        icon: ` <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="30px" height="30px" viewBox="0 0 30 30" enable-background="new 0 0 30 30" xml:space="preserve">
+            <path fill="#EC2626" d="M12.594,21.504c0,0.273,0,0.488,0.013,0.637c0.013,0.149-0.013,0.244-0.063,0.286
+                c-0.043,0.167-0.177,0.286-0.393,0.363c-0.21,0.077-0.369-0.029-0.483-0.334c-0.025-0.041-0.025-0.143-0.013-0.297
+                c0.006-0.161,0.013-0.369,0.013-0.632l-0.032-3.846H10.12l0.038,3.786c0,0.298-0.014,0.548-0.02,0.751
+                c-0.011,0.202-0.005,0.351,0.02,0.452c0.019,0.167,0.039,0.346,0.063,0.536c0.019,0.19,0.12,0.345,0.312,0.458
+                c0.177,0.102,0.38,0.155,0.616,0.155c0.24,0,0.469-0.042,0.697-0.113c0.222-0.071,0.432-0.173,0.622-0.298s0.33-0.268,0.418-0.411
+                l-0.032,0.757l1.257,0.029v-6.103h-1.517V21.504z M18.312,17.574c-0.152-0.083-0.336-0.125-0.533-0.125
+                c-0.361,0-0.774,0.161-1.25,0.477V15.55h-1.518v8.175l1.258-0.029l0.095-0.513c0.406,0.321,0.774,0.519,1.098,0.59
+                c0.324,0.071,0.597,0.042,0.825-0.083c0.222-0.132,0.399-0.34,0.526-0.638c0.121-0.291,0.185-0.631,0.185-1.012v-3.168
+                C18.997,18.265,18.769,17.83,18.312,17.574z M17.848,22.302c0,0.125-0.069,0.232-0.209,0.327c-0.139,0.09-0.291,0.138-0.463,0.138
+                c-0.178,0-0.336-0.048-0.477-0.138c-0.133-0.095-0.203-0.202-0.203-0.327v-3.584c0-0.125,0.07-0.232,0.203-0.334
+                c0.141-0.095,0.299-0.148,0.477-0.148c0.172,0,0.324,0.054,0.463,0.148c0.14,0.102,0.209,0.209,0.209,0.334V22.302z
+                 M14.745,10.924c0.178,0,0.336-0.06,0.476-0.184c0.133-0.119,0.203-0.275,0.203-0.447V6.941c0-0.19-0.07-0.347-0.203-0.472
+                c-0.14-0.131-0.298-0.196-0.47-0.196c-0.177,0-0.336,0.065-0.469,0.196c-0.141,0.125-0.21,0.281-0.21,0.472v3.352
+                c0,0.166,0.07,0.31,0.203,0.44C14.402,10.858,14.562,10.924,14.745,10.924z M25.313,0H4.688C2.098,0,0,2.1,0,4.688v20.625
+                C0,27.901,2.098,30,4.688,30h20.625C27.901,30,30,27.901,30,25.313V4.688C30,2.1,27.901,0,25.313,0z M17.715,5.488h1.416v4.911
+                c0,0.125,0.05,0.233,0.145,0.328c0.108,0.09,0.235,0.137,0.4,0.137c0.152,0,0.285-0.047,0.394-0.137
+                c0.114-0.095,0.171-0.203,0.171-0.328V5.488h1.314v6.305h-1.676l0.025-0.512c-0.152,0.208-0.298,0.369-0.444,0.482
+                c-0.171,0.101-0.361,0.154-0.577,0.154c-0.248,0-0.457-0.054-0.622-0.154c-0.146-0.096-0.272-0.233-0.38-0.417
+                c-0.039-0.083-0.07-0.173-0.083-0.257c-0.013-0.088-0.025-0.172-0.052-0.256c-0.019-0.101-0.031-0.244-0.031-0.422V5.488z
+                 M13.533,5.548c0.299-0.209,0.672-0.311,1.136-0.311c0.406,0,0.744,0.065,1.023,0.186c0.272,0.101,0.501,0.285,0.672,0.541
+                c0.133,0.185,0.234,0.399,0.299,0.637c0.063,0.257,0.095,0.603,0.095,1.037v1.619c0,0.589-0.032,1.012-0.095,1.268
+                c-0.025,0.237-0.134,0.5-0.33,0.793c-0.197,0.256-0.412,0.422-0.641,0.506c-0.279,0.125-0.591,0.184-0.94,0.184
+                c-0.36,0-0.69-0.047-0.989-0.154c-0.273-0.083-0.482-0.232-0.609-0.447c-0.133-0.166-0.242-0.397-0.33-0.696
+                c-0.063-0.278-0.103-0.696-0.103-1.268V7.774c0-0.567,0.057-1.036,0.166-1.405C13.02,5.988,13.236,5.714,13.533,5.548z
+                 M9.085,2.981l1.003,3.107l1.008-3.107h1.758l-1.897,4.156v4.839H9.345V7.138L7.321,2.981H9.085z M26.016,23.028
+                c0,0.423-0.096,0.81-0.286,1.167s-0.45,0.667-0.78,0.935c-0.33,0.273-0.711,0.482-1.148,0.637
+                c-0.432,0.149-0.901,0.232-1.402,0.232H7.613c-0.488,0-0.952-0.083-1.391-0.232c-0.43-0.154-0.812-0.363-1.141-0.637
+                c-0.33-0.268-0.59-0.577-0.787-0.935c-0.189-0.357-0.285-0.744-0.285-1.167v-6.782c0-0.404,0.096-0.785,0.285-1.154
+                c0.197-0.363,0.457-0.679,0.787-0.941c0.329-0.262,0.711-0.476,1.141-0.637c0.439-0.154,0.902-0.238,1.398-0.238h14.778
+                c0.488,0,0.958,0.084,1.396,0.238c0.438,0.161,0.825,0.375,1.155,0.637c0.33,0.263,0.59,0.578,0.78,0.941
+                c0.196,0.369,0.286,0.75,0.286,1.154V23.028z M22.64,21.641v0.69c0,0.167-0.063,0.298-0.185,0.388
+                c-0.127,0.095-0.279,0.137-0.457,0.137h-0.234c-0.172,0-0.317-0.042-0.432-0.137c-0.114-0.09-0.178-0.221-0.178-0.388v-1.464
+                h2.621v-0.852c0-0.322-0.006-0.625-0.013-0.917c-0.013-0.298-0.038-0.524-0.083-0.69c-0.044-0.28-0.178-0.5-0.406-0.673
+                c-0.222-0.167-0.488-0.286-0.787-0.346c-0.304-0.065-0.608-0.077-0.926-0.029c-0.311,0.041-0.584,0.137-0.818,0.291
+                c-0.292,0.185-0.502,0.435-0.641,0.757c-0.127,0.321-0.197,0.762-0.197,1.333v1.899c0,0.804,0.229,1.376,0.68,1.715
+                c0.418,0.315,0.855,0.477,1.313,0.477h0.141c0.514-0.023,0.971-0.232,1.363-0.613c0.299-0.28,0.444-0.649,0.444-1.096
+                c-0.013-0.148-0.032-0.31-0.069-0.482H22.64z M21.192,19.074c0-0.166,0.063-0.303,0.196-0.41c0.134-0.107,0.286-0.167,0.477-0.167
+                h0.07c0.196,0,0.361,0.054,0.501,0.155c0.133,0.106,0.203,0.244,0.203,0.422l-0.032,0.757H21.16L21.192,19.074z M10.417,15.55
+                H5.526v1.048h1.566v7.187h1.511v-7.156h1.814V15.55z" />
+            </svg>`,
+        name: "youtube",
+        defaultTitle: "Youtube"
+    },
+    linkedinUrl : {
+        icon: `<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="30px" height="30px" viewBox="0 0 30 30" enable-background="new 0 0 30 30" xml:space="preserve">
+            <path fill="#0065A1" d="M25.313,0H4.688C2.098,0,0,2.1,0,4.688v20.625C0,27.901,2.098,30,4.688,30h20.625
+                C27.901,30,30,27.901,30,25.313V4.688C30,2.1,27.901,0,25.313,0z M10.032,23.993H6.301V11.979h3.731V23.993z M8.167,10.338
+                c-1.197,0-2.163-0.971-2.163-2.166S6.97,6.007,8.167,6.007c1.192,0,2.161,0.97,2.161,2.165S9.36,10.338,8.167,10.338z
+                    M23.993,23.993h-3.728V18.15c0-1.393-0.023-3.186-1.937-3.186c-1.939,0-2.235,1.519-2.235,3.086v5.942h-3.723V11.979h3.573v1.643
+                h0.051c0.498-0.944,1.714-1.94,3.527-1.94c3.774,0,4.472,2.487,4.472,5.724V23.993z" />
+            </svg>`,
+        name: "linkedin",
+        defaultTitle: "Linkedin"
+    },
+    viadeoUrl : {
+        icon: `<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="30px" height="30px" viewBox="0 0 30 30" enable-background="new 0 0 30 30" xml:space="preserve">
+            <path fill="#f07355" d="M13.79,23.154c0.008,0,0.015-0.001,0.023-0.001c-0.015,0-0.03,0-0.045,0.001H13.79z M25.313,0H4.688
+                C2.098,0,0,2.1,0,4.688v20.625C0,27.901,2.098,30,4.688,30h20.625C27.901,30,30,27.901,30,25.313V4.688C30,2.1,27.901,0,25.313,0z
+                 M19.4,22.635C17.947,24.212,16.08,25,13.797,25c-2.292,0-4.165-0.788-5.617-2.365C6.727,21.058,6,19.214,6,17.105
+                c0-2.091,0.688-3.903,2.063-5.439c1.513-1.692,3.424-2.538,5.734-2.538c0.972,0,1.872,0.151,2.703,0.451
+                c-0.271,0.525-0.514,1.169-0.58,1.86c-0.66-0.273-1.37-0.411-2.13-0.411c-1.598,0-2.971,0.597-4.119,1.79
+                s-1.722,2.636-1.722,4.329c0,1.091,0.262,2.108,0.785,3.052s1.23,1.672,2.122,2.186c0.591,0.341,1.221,0.565,1.889,0.681
+                c5.712-2.209,5.038-12.988,4.997-13.576C17.516,8.833,16.518,6.073,14.677,3c0,0,2.612,1.724,3.063,6.463c0,0,0,0.011,0.002,0.026
+                c0.033,0.097,0.049,0.149,0.049,0.149c3.353,8.906-3.939,13.5-3.959,13.513c1.048-0.007,2.013-0.26,2.893-0.767
+                c0.891-0.514,1.598-1.242,2.121-2.186s0.785-1.961,0.785-3.052c0-0.886-0.159-1.702-0.475-2.451
+                c0.525-0.109,1.158-0.323,1.826-0.731c0.396,0.966,0.598,2.012,0.598,3.141C21.58,19.214,20.854,21.058,19.4,22.635z
+                 M24.611,9.738c0,0-0.591,1.597-1.821,2.325c-0.928,0.55-2.334,0.723-3.805-0.711c0,0,4.446-2.426,4.612-4.479
+                c0,0-1.365,3.056-5.101,3.902c0,0-1.363-2.1,0.367-3.831c0,0,0.605-0.665,2.336-1.103c0,0,1.73-0.367,2.665-2.344
+                C23.865,3.499,25.803,6.192,24.611,9.738z" />
+            </svg>`,
+        name: "viadeo",
+        defaultTitle: "viadeo"
+    },
+    instagramUrl : {
+        icon: `<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="30px" height="30px" viewBox="0 0 30 30" enable-background="new 0 0 30 30" xml:space="preserve">
+            <path fill="#125688" d="M19.465,14.592c0,2.381-1.931,4.312-4.313,4.312c-2.381,0-4.312-1.931-4.312-4.312
+                c0-0.501,0.09-0.98,0.248-1.429H9.004v5.844c0,1.01,0.826,1.835,1.836,1.835h8.32c1.01,0,1.835-0.825,1.835-1.835v-7.681h-3.032
+                C18.881,12.117,19.465,13.285,19.465,14.592z M19.808,10.434c0.649,0,1.175-0.525,1.175-1.174s-0.525-1.174-1.175-1.174
+                c-0.647,0-1.173,0.525-1.173,1.174S19.16,10.434,19.808,10.434z M15.152,16.748c1.189,0,2.156-0.967,2.156-2.156
+                s-0.967-2.156-2.156-2.156c-1.19,0-2.155,0.967-2.155,2.156S13.962,16.748,15.152,16.748z M25.313,0H4.688C2.098,0,0,2.1,0,4.688
+                v20.625C0,27.901,2.098,30,4.688,30h20.625C27.901,30,30,27.901,30,25.313V4.688C30,2.1,27.901,0,25.313,0z M23,20.551h-0.002
+                c0,1.347-1.102,2.449-2.449,2.449h-11.1C8.102,23,7,21.897,7,20.551V9.449C7,8.102,8.102,7,9.449,7h11.102
+                C21.896,7,23,8.102,23,9.449V20.551z" />
+            </svg>`,
+        name: "instagram",
+        defaultTitle: "instagram"
+    },
+    mybusinessUrl : {
+        icon: `<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="#4e8df7" class="icon-mybusiness1"><path class="color1" d="M154.8 202.6c0.3 24.6 20.4 44.5 45.1 44.5 24.9 0 45.1-20.2 45.1-45.1V65.8h-74.6L154.8 202.6zM87.8 247.1c21.2 0 39-14.6 43.8-34.3l1.4-12 15.4-134.9H99.9c-11.4 0-21.5 8.1-23.9 19.3l-0.3 1.1 -31.8 113c-0.9 3.3-1.2 6.8-0.6 10C46.8 231.2 65.5 247.1 87.8 247.1zM312 247.1c24.6 0 44.7-19.9 45.1-44.4L341.5 65.8h-74.6V202C266.9 226.8 287.1 247.1 312 247.1zM424.1 247.1c22.8 0 42.1-17.1 44.8-39.7 0.3-2.3 0.1-4.7-0.5-6.9L436 85.4l-0.1-0.3c-2.4-11.2-12.5-19.3-23.9-19.3h-48.5l15.4 135C382.3 230.2 398.7 247.1 424.1 247.1zM424.1 269c-24.5 0-44.1-11.3-55.8-30.8C356.4 256.7 335.6 269 312 269c-23.4 0-44.1-12.1-56-30.4 -12 18.3-32.6 30.4-56 30.4 -23.4 0-44.1-12.1-56-30.4 -12 18.3-32.6 30.4-56.1 30.4 -11.8 0-23.4-3.1-33.5-9v170.7c0 8.6 7 15.5 15.5 15.5h361.3c8.6 0 15.5-7 15.5-15.5V265.1C439.6 267.6 432 269 424.1 269zM421.5 380.4c-2.9 10.2-8.6 19.9-16.4 27.1 -7.5 6.8-16.7 11.6-26.6 13.8 -10.9 2.5-22.4 2.5-33.2-0.3 -33.1-8.9-53.6-42-46.9-75.6 1.1-5.7 3-11.2 5.7-16.3 7.4-14.6 20-25.9 35.3-31.7 0.5-0.2 1-0.4 1.5-0.5 14.2-4.9 29.6-4.7 43.8 0.4 7.8 2.9 14.9 7.3 20.9 13 -2 2.2-4.2 4.3-6.3 6.4l-11.9 11.9c-4-3.8-8.8-6.6-14-8.2 -13.7-4.1-28.5-0.3-38.5 10 -4.2 4.3-7.4 9.6-9.4 15.3 -2.8 8.2-2.8 17 0 25.2l-0.1 0v0.1c0.2 0.6 0.4 1.2 0.6 1.7 2.9 7.3 7.8 13.6 14.2 18.2 4.3 3.1 9.3 5.2 14.5 6.3 5.1 1 10.4 1 15.5 0.1 5.1-0.8 10-2.7 14.3-5.6 0 0 0 0.2 0 0.2 6.8-4.6 11.4-11.7 13-19.8H362c0-8.6 0-17.9 0-26.4h61.1c0.5 3.1 0.8 5.5 1 8.2C424.8 362.7 424 371.7 421.5 380.4z"/></svg>
+            <path fill="#125688" d="M19.465,14.592c0,2.381-1.931,4.312-4.313,4.312c-2.381,0-4.312-1.931-4.312-4.312
+                c0-0.501,0.09-0.98,0.248-1.429H9.004v5.844c0,1.01,0.826,1.835,1.836,1.835h8.32c1.01,0,1.835-0.825,1.835-1.835v-7.681h-3.032
+                C18.881,12.117,19.465,13.285,19.465,14.592z M19.808,10.434c0.649,0,1.175-0.525,1.175-1.174s-0.525-1.174-1.175-1.174
+                c-0.647,0-1.173,0.525-1.173,1.174S19.16,10.434,19.808,10.434z M15.152,16.748c1.189,0,2.156-0.967,2.156-2.156
+                s-0.967-2.156-2.156-2.156c-1.19,0-2.155,0.967-2.155,2.156S13.962,16.748,15.152,16.748z M25.313,0H4.688C2.098,0,0,2.1,0,4.688
+                v20.625C0,27.901,2.098,30,4.688,30h20.625C27.901,30,30,27.901,30,25.313V4.688C30,2.1,27.901,0,25.313,0z M23,20.551h-0.002
+                c0,1.347-1.102,2.449-2.449,2.449h-11.1C8.102,23,7,21.897,7,20.551V9.449C7,8.102,8.102,7,9.449,7h11.102
+                C21.896,7,23,8.102,23,9.449V20.551z" />
+            </svg>`,
+        name: "mybusiness",
+        defaultTitle: "Mybusiness"
+    },
+    skypeUrl : {
+        icon: `<svg width="30px" height="30px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+            <g clip-path="url(#clip0_23_95)">
+            <path d="M24.9178 40.1316C16.6118 40.1316 12.8289 35.9375 12.8289 32.8125C12.8289 31.25 14.0625 30.0987 15.7072 30.0987C19.2434 30.0987 18.3388 35.4441 25 35.4441C28.3717 35.4441 30.3454 33.3882 30.3454 31.4967C30.3454 30.3454 29.6875 29.0296 27.4671 28.5362L20.0658 26.6447C14.1447 25.1645 13.0757 21.7928 13.0757 18.75C13.0757 12.4178 18.9145 10.1151 24.4243 10.1151C29.523 10.1151 35.6086 12.9112 35.6086 16.7763C35.6086 18.4211 34.2105 19.3257 32.648 19.3257C29.6053 19.3257 30.0987 15.0493 24.0132 15.0493C20.9704 15.0493 19.4079 16.4474 19.4079 18.4211C19.4079 20.3947 21.7928 21.0526 23.8487 21.5461L29.2763 22.7796C35.2796 24.0954 36.8421 27.6316 36.8421 31.0033C36.8421 36.102 32.8125 40.1316 24.9178 40.1316ZM47.8619 29.1118C48.1086 27.7961 48.1908 26.398 48.1908 25C48.1908 12.0066 37.7467 1.48026 24.8355 1.48026C23.4375 1.48026 22.1217 1.5625 20.8059 1.80921C18.75 0.657895 16.4474 0 13.898 0C6.25 0 0 6.25 0 13.9803C0 16.5296 0.657895 18.8322 1.80921 20.8882C1.5625 22.2039 1.48026 23.602 1.48026 25C1.48026 37.9934 11.9243 48.5197 24.8355 48.5197C26.2336 48.5197 27.5493 48.4375 28.8651 48.1908C30.9211 49.3421 33.2237 50 35.773 50C43.4211 50 49.6711 43.75 49.6711 36.0197C49.6711 33.5526 49.0132 31.1678 47.8619 29.1118Z" fill="#00B0F0"/>
+            </g>
+            <defs>
+            <clipPath id="clip0_23_95">
+            <rect width="50" height="50" fill="white"/>
+            </clipPath>
+            </defs>
+        </svg>`,
+        name: "skype",
+        defaultTitle: "Skype"
+    },
+    theforkUrl : {
+        icon: `<svg width="30px" height="30px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+            <g clip-path="url(#clip0_23_37)">
+                <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.58172 0 0 3.58172 0 8V42C0 46.4183 3.58172 50 8 50H42C46.4183 50 50 46.4183 50 42V8C50 3.58172 46.4183 0 42 0H8ZM25.4035 20.8704H23.9171V5H25.4035V20.8704ZM27.6047 5.21953L29.0912 5.41963V20.3696L27.6047 20.6554V5.21953ZM20.2383 5.39653L21.7247 5.19643V20.6037L20.2383 20.3179V5.39653ZM23.0362 40.2233C23.0362 40.7378 22.979 42.3385 22.9218 43.3105C22.8647 44.2825 22.8075 45.0543 22.7503 44.997C13.889 44.1395 7 37.079 7 28.5321C7 21.6431 11.3735 15.8118 17.7194 13.3249C17.605 14.7255 17.4335 17.4125 17.3764 18.4702C17.3192 19.5278 17.2906 21.4144 17.5765 23.0438C17.8337 24.473 18.4626 25.9308 19.12 27.16C19.3078 27.5042 19.4998 27.8484 19.6879 28.1855C20.1871 29.0804 20.6588 29.9258 20.9495 30.5903C21.0205 30.7525 21.0869 30.9022 21.1499 31.0439C21.4418 31.701 21.6576 32.1868 21.8928 32.9628C22.2072 33.906 22.4073 34.6493 22.4931 35.278C22.5201 35.4271 22.5521 35.5745 22.5865 35.7336C22.6974 36.2461 22.8346 36.8798 22.9218 38.0795C23.0431 39.438 23.0409 39.6641 23.0374 40.0159C23.0368 40.0786 23.0362 40.1454 23.0362 40.2233ZM26.2635 40.2233C26.2635 40.7378 26.3207 42.3385 26.3779 43.3105C26.4351 44.2825 26.4922 45.0543 26.5494 44.997C35.4108 44.1395 42.2998 37.079 42.2998 28.5321C42.2998 21.6431 37.9263 15.8118 31.5803 13.3249C31.6948 14.7255 31.8663 17.4125 31.9233 18.4702C31.9805 19.5278 32.009 21.4144 31.7233 23.0438C31.466 24.473 30.8373 25.9308 30.1798 27.16C29.9922 27.5039 29.8003 27.8478 29.6123 28.1846C29.1129 29.0799 28.641 29.9256 28.3502 30.5903C28.2793 30.7525 28.2128 30.9021 28.1498 31.0438C27.8579 31.701 27.6421 32.1868 27.4069 32.9628C27.0925 33.906 26.8924 34.6493 26.8067 35.278C26.7796 35.4271 26.7476 35.5745 26.7132 35.7336C26.6023 36.246 26.4651 36.8798 26.3779 38.0795C26.2566 39.4379 26.2588 39.6641 26.2623 40.0159C26.2629 40.0786 26.2635 40.1454 26.2635 40.2233Z" fill="#558F40"/>
+                </g>
+                <defs>
+                <clipPath id="clip0_23_37">
+                <rect width="50" height="50" fill="white"/>
+                </clipPath>
+                </defs>
+        </svg>`,
+        name: "thefork",
+        defaultTitle: "Thefork"
+    },
+    tiktokUrl : {
+        icon: `<svg width="30px" height="30px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+            <g clip-path="url(#clip0_23_35)">
+                <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.58172 0 0 3.58172 0 8V42C0 46.4183 3.58172 50 8 50H42C46.4183 50 50 46.4183 50 42V8C50 3.58172 46.4183 0 42 0H8ZM41.0201 21.3758H42.094V14.396C36.7248 14.1275 32.698 10.1007 32.1611 5H26.5235V32.9195C26.5235 36.4094 23.5705 39.094 20.0805 39.094C16.5906 39.094 13.906 36.4094 13.906 32.9195C13.906 29.4295 16.5906 26.745 20.0805 26.745H20.8859V20.8389H20.0805C13.3691 20.8389 8 26.2081 8 32.9195C8 39.6309 13.3691 45 20.0805 45C26.7919 45 32.1611 39.6309 32.1611 32.9195V16.5436C34.0403 19.4966 37.5302 21.3758 41.0201 21.3758Z" fill="#000000"/>
+                </g>
+                <defs>
+                <clipPath id="clip0_23_35">
+                <rect width="50" height="50" fill="white"/>
+                </clipPath>
+                </defs>
+        </svg>`,
+        name: "tiktok",
+        defaultTitle: "Tiktok"
+    },
+    tripadvisorUrl : {
+        icon: `<svg width="30px" height="30px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+            <g clip-path="url(#clip0_23_35)">
+                <g clip-path="url(#clip0_23_43)">
+                    <path d="M13.75 26C13.75 26.75 14.25 27.25 15 27.25C15.75 27.25 16.25 26.75 16.25 26C16.25 25.25 15.75 24.75 15 24.75C14.25 24.75 13.75 25.25 13.75 26Z" fill="#32D99C"/>
+                    <path fill-rule="evenodd" clip-rule="evenodd" d="M8.25 26C8.25 29.75 11.25 32.75 15 32.75C18.75 32.75 21.75 29.75 21.75 26C21.75 22.25 18.75 19.25 15 19.25C11.25 19.25 8.25 22.25 8.25 26ZM10.75 26C10.75 23.75 12.75 21.75 15 21.75C17.25 21.75 19.25 23.75 19.25 26C19.25 28.25 17.25 30.25 15 30.25C12.75 30.25 10.75 28.25 10.75 26Z" fill="#32D99C"/>
+                    <path d="M17 16.25C21.5 17 25 21.25 25 26C25 21.25 28.5 17.25 33 16.25C30.5 15.5 28 15 25 15C22 15 19.5 15.5 17 16.25Z" fill="#32D99C"/>
+                    <path d="M33.75 26C33.75 26.75 34.25 27.25 35 27.25C35.75 27.25 36.25 26.75 36.25 26C36.25 25.25 35.75 24.75 35 24.75C34.25 24.75 33.75 25.25 33.75 26Z" fill="#32D99C"/>
+                    <path fill-rule="evenodd" clip-rule="evenodd" d="M28.25 26C28.25 29.75 31.25 32.75 35 32.75C38.75 32.75 41.75 29.75 41.75 26C41.75 22.25 38.75 19.25 35 19.25C31.25 19.25 28.25 22.25 28.25 26ZM30.75 26C30.75 23.75 32.75 21.75 35 21.75C37.25 21.75 39.25 23.75 39.25 26C39.25 28.25 37.25 30.25 35 30.25C32.75 30.25 30.75 28.25 30.75 26Z" fill="#32D99C"/>
+                    <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.58172 0 0 3.58172 0 8V42C0 46.4183 3.58172 50 8 50H42C46.4183 50 50 46.4183 50 42V8C50 3.58172 46.4183 0 42 0H8ZM39.25 16H45C44.25 16.75 43.25 18.75 43 20C44.25 21.75 45 23.75 45 26C45 31.5 40.5 36 35 36C32.5 36 30 35 28.25 33.25L25 38L21.75 33.25C20 35 17.75 36 15 36C9.5 36 5 31.5 5 26C5 23.75 5.75 21.75 7 20C6.75 18.75 5.75 16.75 5 16H10.75C14.5 13.25 19.5 12 25 12C30.5 12 35.5 13.25 39.25 16Z" fill="#32D99C"/>
+                </g>
+                <defs>
+                    <clipPath id="clip0_23_43">
+                    <rect width="50" height="50" fill="white"/>
+                    </clipPath>
+                </defs>
+        </svg>`,
+        name: "vimeo",
+        defaultTitle: "Vimeo"
+    },
+    wazeUrl : {
+        icon: `<svg width="30px" height="30px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+            <g clip-path="url(#clip0_23_39)">
+            <path d="M15.522 39.3771C15.522 40.2174 16.2026 40.9065 17.0513 40.9065C17.9 40.9065 18.5807 40.2174 18.5807 39.3771C18.5807 38.5368 17.9 37.8477 17.0513 37.8477C16.211 37.8477 15.522 38.5284 15.522 39.3771Z" fill="#32CDFD"/>
+            <path d="M30.2778 39.3771C30.2778 40.2174 30.9584 40.9065 31.8072 40.9065C32.6559 40.9065 33.3449 40.2174 33.3365 39.3771C33.3365 38.5368 32.6475 37.8477 31.8072 37.8477C30.9668 37.8477 30.2778 38.5284 30.2778 39.3771Z" fill="#32CDFD"/>
+            <path fill-rule="evenodd" clip-rule="evenodd" d="M31.8072 35.1335C32.7819 35.1335 33.6894 35.4697 34.4037 36.0243C39.7313 33.6126 43.1177 28.68 43.1177 23.1843C43.1177 15.2854 36.0843 8.86549 27.446 8.86549C18.8076 8.86549 11.7826 15.2854 11.7826 23.1843C11.7826 23.4571 11.7903 23.726 11.7979 23.9911C11.8015 24.118 11.8051 24.244 11.8078 24.3692C11.8582 25.8817 11.8918 27.3019 11.2028 28.5119C10.4969 29.7388 9.16924 30.537 7.02645 31.016C7.95079 33.2092 10.4549 34.8898 14.4632 35.9991C15.1774 35.4529 16.0766 35.1251 17.0429 35.1251C18.5723 35.1251 19.9084 35.9318 20.6563 37.1419C23.32 37.436 25.7737 37.5032 27.446 37.5032C27.6308 37.5032 27.8157 37.5032 28.0006 37.4948C28.698 36.0999 30.1433 35.1335 31.8072 35.1335ZM24.488 19.6046C24.488 20.5885 23.6904 21.3861 22.7066 21.3861C21.7227 21.3861 20.9251 20.5885 20.9251 19.6046C20.9251 18.6208 21.7227 17.8232 22.7066 17.8232C23.6904 17.8232 24.488 18.6208 24.488 19.6046ZM33.3281 24.5625C33.3281 24.0499 33.7483 23.6297 34.2609 23.6297C34.7734 23.6297 35.1936 24.0415 35.1936 24.5625C35.1936 26.1422 34.3869 27.6128 32.9164 28.6968C31.5299 29.7135 29.7148 30.2766 27.7821 30.2766C25.8494 30.2766 24.0343 29.7219 22.6478 28.6968C21.1773 27.6128 20.3706 26.1422 20.3706 24.5625C20.3706 24.0499 20.7907 23.6297 21.3033 23.6297C21.8159 23.6297 22.236 24.0499 22.236 24.5625C22.236 25.5372 22.7822 26.4699 23.757 27.1926C24.8242 27.9825 26.2527 28.4111 27.7821 28.4111C29.3114 28.4111 30.74 27.9741 31.8072 27.1926C32.7903 26.4699 33.3281 25.5372 33.3281 24.5625ZM34.2608 19.6046C34.2608 20.5885 33.4632 21.3861 32.4794 21.3861C31.4955 21.3861 30.6979 20.5885 30.6979 19.6046C30.6979 18.6208 31.4955 17.8232 32.4794 17.8232C33.4632 17.8232 34.2608 18.6208 34.2608 19.6046Z" fill="#32CDFD"/>
+            <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.58172 0 0 3.58172 0 8V42C0 46.4183 3.58172 50 8 50H42C46.4183 50 50 46.4183 50 42V8C50 3.58172 46.4183 0 42 0H8ZM27.4544 7C32.1181 7 36.5129 8.67221 39.8237 11.7141C43.1513 14.7729 44.9832 18.8484 45 23.2011C45 26.4784 43.9496 29.6295 41.9497 32.3269C40.3279 34.5201 38.1599 36.2932 35.639 37.52C35.9163 38.083 36.0675 38.7132 36.0675 39.3771C36.0675 41.7216 34.16 43.6207 31.824 43.6207C29.4795 43.6207 27.5804 41.7132 27.5804 39.3771H27.4628C25.9166 39.3771 23.715 39.3183 21.2949 39.0914C21.2949 39.1376 21.297 39.186 21.2991 39.2342C21.3012 39.2826 21.3033 39.3309 21.3033 39.3771C21.3033 41.7216 19.4042 43.6207 17.0597 43.6207C14.7153 43.6207 12.8162 41.7132 12.8162 39.3771C12.8162 38.7385 12.959 38.1334 13.2111 37.5872C8.69867 36.1839 5.95926 33.9991 5.06013 31.0748C4.94249 30.6967 5.00131 30.2766 5.20298 29.9404C5.41306 29.5959 5.75759 29.3606 6.15253 29.285C10.06 28.5539 10.0264 27.2598 9.94232 24.4196C9.92552 24.0247 9.91711 23.6129 9.91711 23.1843C9.91711 18.8484 11.7574 14.7729 15.085 11.7141C18.3874 8.67221 22.7822 7 27.4544 7Z" fill="#32CDFD"/>
+            </g>
+            <defs>
+            <clipPath id="clip0_23_39">
+            <rect width="50" height="50" fill="white"/>
+            </clipPath>
+            </defs>
+        </svg>`,
+        name: "waze",
+        defaultTitle: "Waze"
+    },
+    whatsappUrl : {
+        icon: ` <svg width="30px" height="30px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+            <g clip-path="url(#clip0_23_39)">
+                <path fill-rule="evenodd" clip-rule="evenodd" d="M16.6585 39.1785C19.1981 40.6866 22.1127 41.4837 25.0899 41.4837C34.2166 41.4837 41.6399 34.0603 41.6478 24.9336C41.6478 20.5108 39.9287 16.346 36.8031 13.2204C33.6696 10.0947 29.5126 8.36784 25.1055 8.36784C15.9709 8.36784 8.53975 15.7912 8.53975 24.9179C8.53975 28.0436 9.41492 31.091 11.0715 33.7243L11.4622 34.3495L9.79 40.46L16.0568 38.8191L16.6585 39.1785ZM30.7706 27.4653C31.2239 27.6294 33.6696 28.8328 34.1697 29.0828C34.2654 29.1307 34.3549 29.1739 34.4379 29.2141C34.7886 29.3839 35.0249 29.4983 35.1387 29.6689C35.2637 29.8798 35.2637 30.8722 34.8496 32.0365C34.4354 33.2008 32.4428 34.2635 31.4895 34.4042C30.63 34.537 29.5517 34.5839 28.3561 34.2088C27.6372 33.9822 26.7074 33.6775 25.5196 33.1617C20.8451 31.1427 17.6909 26.6102 17.1023 25.7643C17.0621 25.7066 17.0339 25.6661 17.018 25.6446L17.01 25.634C16.7326 25.2615 14.9863 22.9166 14.9863 20.4952C14.9863 18.2181 16.1039 17.0179 16.6239 16.4596C16.6624 16.4183 16.6976 16.3804 16.7288 16.346C17.1899 15.8459 17.729 15.7208 18.0572 15.7208C18.3854 15.7208 18.7214 15.7208 19.0105 15.7365C19.0466 15.7383 19.0841 15.7381 19.1228 15.7379C19.4114 15.7361 19.7697 15.7339 20.1279 16.5882C20.2537 16.8919 20.4328 17.329 20.6248 17.7974C21.0648 18.8714 21.5724 20.1101 21.6595 20.2842C21.7845 20.5343 21.8627 20.8234 21.6986 21.1594C21.6627 21.2278 21.6309 21.2912 21.6008 21.3512C21.4866 21.579 21.3964 21.7589 21.1985 21.9877C21.129 22.0702 21.0576 22.157 20.986 22.2442C20.8 22.4705 20.6119 22.6992 20.4483 22.8629C20.1983 23.1129 19.9404 23.3786 20.2295 23.8787C20.5186 24.3788 21.5188 26.0041 22.9957 27.3247C24.5841 28.7431 25.9646 29.3413 26.6663 29.6454C26.8035 29.7048 26.9147 29.753 26.9965 29.7939C27.4966 30.0439 27.7857 29.9971 28.0748 29.6689C28.3639 29.3329 29.3251 28.2155 29.6532 27.7154C29.9814 27.2153 30.3174 27.3012 30.7706 27.4653Z" fill="#25D366"/>
+            <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.58172 0 0 3.58172 0 8V42C0 46.4183 3.58172 50 8 50H42C46.4183 50 50 46.4183 50 42V8C50 3.58172 46.4183 0 42 0H8ZM25.0899 5C30.419 5.00781 35.42 7.07853 39.1785 10.8449C42.9371 14.6112 45.0078 19.6122 45 24.9414C45 35.9201 36.0607 44.8515 25.082 44.8515H25.0742C21.7454 44.8515 18.4636 44.0154 15.5567 42.4292L5 45.1953L7.82086 34.873C6.07833 31.8568 5.16409 28.4264 5.16409 24.9179C5.17191 13.9392 14.1112 5 25.0899 5Z" fill="#25D366"/>
+            </g>
+            <defs>
+            <clipPath id="clip0_23_45">
+            <rect width="50" height="50" fill="white"/>
+            </clipPath>
+            </defs>
+        </svg>`,
+        name: "whatsapp",
+        defaultTitle: "Whatsapp"
+    },
+    slideshareUrl : {
+        icon: `<svg width="30px" height="30px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+            <path d="M10.9746 5.00104H39.2425C40.1547 5.00608 41.0282 5.37071 41.6734 6.01582C42.3185 6.66092 42.683 7.53442 42.6882 8.44672V24.4094C42.9215 24.2546 43.1521 24.095 43.3799 23.9305C44.5949 23.0756 45.4649 24.2755 44.73 25.3554C42.4447 27.9415 39.5385 29.9031 36.2853 31.0551C36.8081 32.7207 37.0809 34.4543 37.0953 36.1998C37.0953 40.8196 34.5905 45.1994 29.8358 45.1994C29.261 45.236 28.6853 45.1481 28.1475 44.9419C27.6098 44.7357 27.1229 44.4161 26.72 44.0047C26.3171 43.5932 26.0077 43.0997 25.813 42.5579C25.6181 42.0159 25.5423 41.4385 25.591 40.8647V34.22C26.1832 34.3426 26.786 34.4079 27.3908 34.415C28.6964 34.4499 29.9798 34.0713 31.0572 33.3331C32.1347 32.595 32.9514 31.5353 33.3906 30.3053C34.1555 28.1753 32.7156 27.6203 31.6506 29.2552C30.0007 31.8351 27.8108 31.8351 25.501 30.3353V30.0652C25.501 27.8103 26.9179 27.8197 28.3332 27.8291C28.4242 27.8297 28.5152 27.8303 28.6058 27.8303C32.979 28.3092 37.3708 27.4196 41.1882 25.3177V8.45307C41.1848 7.93639 40.9781 7.4418 40.6127 7.07642C40.2474 6.71103 39.7528 6.5043 39.2361 6.50095H10.9441C10.6825 6.49372 10.422 6.53855 10.178 6.63337C9.93407 6.72819 9.71148 6.87081 9.5234 7.05284C9.3353 7.23487 9.18547 7.45264 9.0827 7.69337C8.98016 7.93357 8.92655 8.19179 8.92497 8.45293V26.2848H7.42505V8.44699C7.42742 7.98543 7.52201 7.529 7.70321 7.1045C7.88443 6.67999 8.14863 6.29597 8.48032 5.97499C8.812 5.65401 9.20447 5.40253 9.63468 5.23533C10.0615 5.06945 10.5169 4.98984 10.9746 5.00104Z" fill="#026C97"/>
+            <path d="M30.9452 26.0006C31.5678 26.0226 32.1885 25.9216 32.772 25.7037C33.3556 25.4857 33.8903 25.1548 34.3458 24.7298C34.8014 24.3049 35.1688 23.7944 35.4268 23.2275C35.6849 22.6605 35.8286 22.0484 35.85 21.4257C35.7838 20.183 35.2332 19.0158 34.3162 18.1745C33.3992 17.3333 32.189 16.885 30.9452 16.926C29.6891 16.8855 28.468 17.344 27.5487 18.2013C26.6297 19.0585 26.0874 20.2448 26.0405 21.5008C26.1065 22.7435 26.6572 23.9108 27.5741 24.752C28.4911 25.5932 29.7015 26.0416 30.9452 26.0006Z" fill="#026C97"/>
+            <path fill-rule="evenodd" clip-rule="evenodd" d="M24.4651 21.3657C24.4497 21.9954 24.3092 22.6156 24.0521 23.1907C23.7949 23.7656 23.426 24.2837 22.9669 24.7149C22.5078 25.1462 21.9676 25.4818 21.3776 25.7025C20.7877 25.9232 20.1597 26.0245 19.5304 26.0005C18.9079 26.0225 18.2871 25.9215 17.7036 25.7036C17.1201 25.4856 16.5853 25.1547 16.1297 24.7297C15.6742 24.3048 15.3069 23.7943 15.0488 23.2274C14.7907 22.6604 14.6469 22.0483 14.6257 21.4257C14.6917 20.1829 15.2423 19.0157 16.1593 18.1744C17.0763 17.3332 18.2866 16.8849 19.5304 16.9259C20.7694 16.8763 21.9789 17.3128 22.9008 18.1422C23.8226 18.9716 24.384 20.1282 24.4651 21.3657ZM21.0303 27.8303C15.9007 28.2858 10.7761 26.9037 6.5711 23.9305C5.35617 23.0756 4.51621 24.2755 5.31117 25.3405C7.58693 27.9304 10.4894 29.893 13.7407 31.0402C10.9059 40.6996 16.0956 45.1994 20.1903 45.1844C20.7667 45.2278 21.3455 45.1454 21.887 44.943C22.4284 44.7407 22.9193 44.4231 23.3258 44.0122C23.7324 43.6014 24.0449 43.1071 24.2416 42.5637C24.4383 42.0202 24.5146 41.4404 24.4651 40.8647C24.4651 37.9848 24.4651 33.2 24.4651 33.2C25.4173 33.4838 26.3985 33.66 27.39 33.725C28.6617 33.8257 29.9329 33.5235 31.0233 32.8613C32.1135 32.1989 32.9677 31.2102 33.4646 30.0352C34.2896 28.0553 32.8797 27.4854 31.7847 29.0003C29.6698 31.9101 26.955 31.3101 24.1502 28.6253C23.3851 27.9204 22.9652 27.7254 21.0003 27.8303H21.0303Z" fill="#D8711C"/>                        
+        </svg>`,
+        name: "slideshare",
+        defaultTitle: "Slideshare"
+    },
+    borneoUrl : {
+        icon: `<svg width="20px" height="20px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+            <path fill="#FF6B00" fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.58172 0 0 3.58172 0 8V42C0 46.4183 3.58172 50 8 50H42C46.4183 50 50 46.4183 50 42V8C50 3.58172 46.4183 0 42 0H8ZM25.8493 9.35071C26.4571 9.64483 26.451 10.4668 26.0302 10.9984C23.9052 13.6828 22.4695 18.8002 22.4695 24.6717C22.4695 30.5432 23.9052 35.6605 26.0302 38.3449C26.451 38.8765 26.4571 39.6984 25.8493 39.9926C25.3746 40.2223 24.8819 40.3433 24.3765 40.3433C20.5281 40.3433 17.4083 33.3269 17.4083 24.6717C17.4083 16.0164 20.5281 9 24.3765 9C24.8819 9 25.3746 9.12096 25.8493 9.35071ZM40 25.1163C40 28.2878 38.2529 30.8588 36.0976 30.8588C33.9424 30.8588 33.1785 28.2878 33.1785 25.1163C33.1785 21.9448 33.9424 19.3737 36.0976 19.3737C38.2529 19.3737 40 21.9448 40 25.1163ZM33.017 11.6307C33.7259 11.925 33.72 12.8697 33.2569 13.4866C31.4459 15.8991 30.2444 20.1396 30.2444 24.968C30.2444 29.7964 31.4459 34.037 33.2569 36.4495C33.72 37.0663 33.7259 38.011 33.017 38.3053C32.6066 38.4757 32.1823 38.5649 31.7481 38.5649C28.163 38.5649 25.2566 32.4774 25.2566 24.968C25.2566 17.4586 28.163 11.3711 31.7481 11.3711C32.1823 11.3711 32.6066 11.4604 33.017 11.6307ZM15.5925 36.659C16.2867 37.1724 17.0673 36.3645 16.7309 35.5648C15.4878 32.609 14.7677 29.1009 14.7677 25.3385C14.7677 21.3082 15.594 17.5697 17.0043 14.4894C17.3663 13.6987 16.6098 12.8604 15.8989 13.3537C12.3547 15.8134 10 20.1619 10 25.1161C10 29.9261 12.2196 34.1652 15.5925 36.659Z"/>
+        </svg>`,
+        name: "borneo",
+        defaultTitle: "Borneo"
+    },
+    boncoinUrl : {
+        icon: `<svg width="20px" height="20px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+            <path fill="#FDFDFD" d="M8 0a8 8 0 0 0-8 8v34a8 8 0 0 0 8 8h10V24a6 6 0 0 1 6-6h26V8a8 8 0 0 0-8-8H8Z"/>
+            <path fill="#D9D9D9" d="M8 2a6 6 0 0 0-6 6v34a6 6 0 0 0 6 6h10v2H8a8 8 0 0 1-8-8V8a8 8 0 0 1 8-8h34a8 8 0 0 1 8 8v10h-2V8a6 6 0 0 0-6-6H8Z"/>
+            <path fill="#FE6D14" d="M48.586 20H24a4 4 0 0 0-4 4v24.586L48.586 20Z"/>
+            <path fill="#954009" fill-rule="evenodd" d="M21.414 50H46a4 4 0 0 0 4-4V21.414L21.414 50Z"/>
+        </svg>`,
+        name: "leboncoin",
+        defaultTitle: "Boncoin"
+    },
+    pagesjauneUrl : {
+        icon: `<svg width="20px" height="20px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+            <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.58172 0 0 3.58172 0 8V42C0 46.4183 3.58172 50 8 50H42C46.4183 50 50 46.4183 50 42V8C50 3.58172 46.4183 0 42 0H8ZM10.375 19.7917C10.375 20.9167 11.2917 21.8333 12.4584 21.8333H12.5001C13.6251 21.8333 14.5417 20.9167 14.5417 19.75V17.4583C14.5417 16.3333 13.5834 15.375 12.4584 15.375C11.3334 15.375 10.375 16.2917 10.375 17.4583V19.7917ZM19.8334 39.4167C21.625 40.0417 23.5417 40.375 25.4584 40.375C27.125 40.375 28.7501 40.125 30.3334 39.5833C36.6251 37.4167 40.25 31.3333 40.25 22.8333V17.4583C40.25 16.2917 39.3334 15.375 38.1667 15.375C37 15.375 36.0834 16.2917 36.0834 17.4583V22.8333C36.0834 29.5 33.5834 34.0417 29 35.625C24.0834 37.2917 17.7917 35.0833 14.0417 30.3333C14.0001 30.3333 14 30.2917 14 30.2917C13.2917 29.375 11.9584 29.25 11.0834 29.9583C10.1667 30.7083 10.0001 32 10.7084 32.9167C13.0834 35.9167 16.25 38.1667 19.8334 39.4167Z" fill="#FCEB23"/>
+        </svg>`,
+        name: "pagesjaunes",
+        defaultTitle: "Pagesjaune"
+    },
+});
\ No newline at end of file
Index: src/js/JEditor/ParamsPanel/Templates/SocialNetwork.html
===================================================================
--- src/js/JEditor/ParamsPanel/Templates/SocialNetwork.html	(révision 13779)
+++ src/js/JEditor/ParamsPanel/Templates/SocialNetwork.html	(révision 13780)
@@ -364,6 +364,12 @@
                     <%=__("Youtube")%>
                 </a>
             </li>
+            <li>
+                <a  data-id="customrsUrl" class="customrs">
+                    <span class="icon icon-social"></span>
+                    <%=__("Custom")%>
+                </a>
+            </li>
         </ul>
         </div>
     </div>
Index: src/js/JEditor/ParamsPanel/Views/SocialNetworks.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/SocialNetworks.js	(révision 13779)
+++ src/js/JEditor/ParamsPanel/Views/SocialNetworks.js	(révision 13780)
@@ -5,11 +5,12 @@
     'JEditor/Commons/Events', 
     'JEditor/ParamsPanel/Views/ListSocialNetworks',
     "JEditor/App/Messages/ClipboardModule",
-    'text!../Templates/SocialNetwork.html', 
+    'text!../Templates/SocialNetwork.html',
+    "JEditor/Commons/Files/Views/SocialSvgSelectorDialog", 
     'i18n!../nls/i18n',
     //not in params
     'jqueryui/sortable'],
-    function ($, _, View, Events,ListSocialNetworks, ClipboardModule, template, translate) {
+    function ($, _, View, Events,ListSocialNetworks, ClipboardModule, template, SocialSvgSelectorDialog, translate) {
     var SocialNetworks = View.extend({
         events: {
             "click .dropdown-menu":"onClickCascade",
@@ -53,6 +54,7 @@
                 borneoUrl: 'borneo',
                 pagesjauneUrl: 'pagesjaunes',
                 boncoinUrl: 'leboncoin',
+                customrsUrl :'customrs'
             };
             
         },
@@ -125,9 +127,12 @@
         },
         renderListeSocialNetwork :function(data){
             
-            this.ChildViews.ListSocialNetworks=new ListSocialNetworks({model:data});
+            this.selectSvgDialog = new SocialSvgSelectorDialog();
+            this.ChildViews.ListSocialNetworks=new ListSocialNetworks({model:data, selectSvgDialog:this.selectSvgDialog});
             this.listenTo(this.ChildViews.ListSocialNetworks, 'deleteOneSocialNetwork',this.DeleteOne);
+            this.listenTo(this.ChildViews.ListSocialNetworks, 'updateOneSocialNetwork',this.render);
             this.$("#liste").append(this.ChildViews.ListSocialNetworks.render().$el);
+           
         },
         copyToClipboard : function (e){
             ClipboardModule.copyToClipboard(e);
Index: src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 13779)
+++ src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 13780)
@@ -33,6 +33,7 @@
         "Invalid_borneoUrl":"L'url renseignée ne semble pas valide",
         "Invalid_boncoinUrl":"L'url renseignée ne semble pas valide",
         "Invalid_pagesjauneUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_customrsUrl":"L'url renseignée ne semble pas valide",
         "Invalid_GAKey":"Le code renseigné ne semble pas valide",
         "Invalid_GTMKey":"Le code renseigné ne semble pas valide",
         "Invalid_WTKey":"Le code renseigné ne semble pas valide",
@@ -124,4 +125,5 @@
         "qualityCompression" : "Qualité de compression",
         "lowQuality" : "Normal (par défaut)",
         "highQuality" : "Haute",
+        "Custom":"Custom"
     });
Index: src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 13779)
+++ src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 13780)
@@ -33,6 +33,7 @@
         "Invalid_borneoUrl":"L'url renseignée ne semble pas valide",
         "Invalid_boncoinUrl":"L'url renseignée ne semble pas valide",
         "Invalid_pagesjauneUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_customrsUrl":"L'url renseignée ne semble pas valide",
         "Invalid_GAKey":"Le code renseigné ne semble pas valide",
         "Invalid_GTMKey":"Le code renseigné ne semble pas valide",
         "Invalid_WTKey":"Le code renseigné ne semble pas valide",
@@ -129,4 +130,5 @@
         "qualityCompression" : "Qualité de compression",
         "lowQuality" : "Normal (par défaut)",
         "highQuality" : "Haute",
+        "Custom":"Custom"
     });
Index: src/js/JEditor/ParamsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 13779)
+++ src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 13780)
@@ -21,6 +21,7 @@
         "Invalid_borneoUrl":"This is not a valid url",
         "Invalid_boncoinUrl":"This is not a valid url",
         "Invalid_pagesjauneUrl":"This is not a valid url",
+        "Invalid_customrsUrl":"This is not a valid url",
         "Invalid_GAKey":"This is not a valid code",
         "Invalid_GTMKey":"This is not a valid code",
         "Invalid_WTKey":"This is not a valid code",
@@ -126,7 +127,8 @@
         "highQuality" : "High",
         "Borneo": "Borneo",
         "Pagesjaune" : "Pagesjaunes",
-        "Boncoin": "Leboncoin"
+        "Boncoin": "Leboncoin",
+        "Custom": "Custom",
     },
     "fr-fr": true,
     "fr-ca": true
