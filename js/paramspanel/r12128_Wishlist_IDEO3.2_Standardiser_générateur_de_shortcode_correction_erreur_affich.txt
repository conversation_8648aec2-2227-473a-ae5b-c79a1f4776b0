Revision: r12128
Date: 2024-03-14 16:01:24 +0300 (lkm 14 Mar 2024) 
Author: jn.harison 

## Commit message
Wishlist IDEO3.2:Standardiser générateur de shortcode: correction erreur affichage et bug

## Files changed

## Full metadata
------------------------------------------------------------------------
r12128 | jn.harison | 2024-03-14 16:01:24 +0300 (lkm 14 Mar 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/CustomShortcode.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/i18n.js

Wishlist IDEO3.2:Standardiser générateur de shortcode: correction erreur affichage et bug
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 12127)
+++ src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 12128)
@@ -106,5 +106,6 @@
         "bodyEnd": "Fin du body",
         "customPosition": "Position personnalisé",
         "CustomShortcodePosition": "Position du shortcode",
-        "CustomShortcodeName": "Nom du shortcode"
+        "CustomShortcodeName": "Nom du shortcode",
+        "confirmDeleteShortcode": "Vous \u00eates sur le point de supprimer d\u00e9finitivement un shortcode</br><strong><% name %></strong>",
     });
Index: src/js/JEditor/ParamsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 12127)
+++ src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 12128)
@@ -114,7 +114,8 @@
         "bodyEnd": "End of body",
         "customPosition": "Custom position",
         "CustomShortcodePosition": "Shortcode position",
-        "CustomShortcodeName": "Shortcode name"
+        "CustomShortcodeName": "Shortcode name",
+        "confirmDeleteShortcode": "You are about to permanently delete a shortcode</br><strong><% name %></strong>",
     },
     "fr-fr": true,
     "fr-ca": true
Index: src/js/JEditor/ParamsPanel/Views/CustomShortcode.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/CustomShortcode.js	(révision 12127)
+++ src/js/JEditor/ParamsPanel/Views/CustomShortcode.js	(révision 12128)
@@ -26,9 +26,15 @@
                     this._super();
                     this.data = this.options.model.toJSON();
                     this.data = this.data.CustomShortcode;
-                    //console.log(typeof this.data);
-                    //if(!Array.isArray(this.data))this.data=[];
-                    this.currentList = JSON.parse(this.data);
+                    if(!Array.isArray(this.data) || typeof this.data === "string" || this.data.trim() == '')
+                    {
+                        this.currentList = null;
+                    }
+                    else
+                    {
+                        this.currentList = JSON.parse(this.data);
+                    }
+                    
                     this._template = this.buildTemplate(template,translate);
                 },
                 confirm: function(params) {
@@ -55,8 +61,7 @@
                 var $targetName = $target.attr('data-id');
                 var id = $targetName.split('_')[1];
                 this.confirm({
-                    // message: translate('confirmDeleteSocial', {'name': $target.attr("name")}),
-                    message: translate('confirmDeleteSocial', {'name': $target.attr('data-name')}),
+                    message: translate('confirmDeleteShortcode', {'name': $target.attr('data-name')}),
                     title: translate("deleteAction"),
                     type: 'delete',
                     onOk: _.bind(function() {
