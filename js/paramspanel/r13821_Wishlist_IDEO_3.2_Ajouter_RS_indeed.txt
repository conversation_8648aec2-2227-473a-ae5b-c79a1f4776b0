Revision: r13821
Date: 2025-02-12 11:08:22 +0300 (lrb 12 Feb 2025) 
Author: frahajanirina 

## Commit message
Wishlist IDEO 3.2: Ajouter RS indeed

## Files changed

## Full metadata
------------------------------------------------------------------------
r13821 | frahajanirina | 2025-02-12 11:08:22 +0300 (lrb 12 Feb 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Models/Params.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Models/SocialList.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Templates/ListSocialNetwork.html
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Templates/SocialNetwork.html
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/SocialNetworks.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/i18n.js

Wishlist IDEO 3.2: Ajouter RS indeed
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Models/Params.js
===================================================================
--- src/js/JEditor/ParamsPanel/Models/Params.js	(révision 13820)
+++ src/js/JEditor/ParamsPanel/Models/Params.js	(révision 13821)
@@ -48,7 +48,8 @@
         borneoUrl:/^(https?\:\/\/)?(www\.)?borneoapp\.com\/(.*)/,
         boncoinUrl:/^(https?\:\/\/)?(www\.)?leboncoin\.fr\/(.*)/,
         pagesjauneUrl:/^(https?\:\/\/)?(www\.)?pagesjaunes\.fr\/(.*)/,
-        customrsUrl : /^(https?:\/\/)?(www\.)?([\da-z\.-]+)\.([a-z\.]{2,6})((^\/)?.*)/
+        customrsUrl : /^(https?:\/\/)?(www\.)?([\da-z\.-]+)\.([a-z\.]{2,6})((^\/)?.*)/,
+        indeedUrl: /^(https?:\/\/)?((www|emplois\.ca|au|fr)\.)?indeed\.(com|fr)\/(.*)/
     };  
     //verification for google fonts
 
@@ -102,6 +103,7 @@
             IconsCollection: "outline",
             CustomShortcode: null,
             ImageCompressionQuality: "low",
+            indeedUrl: null
         },
         constructor: function () {
             if (arguments.callee.caller !== Params.getInstance)
@@ -110,7 +112,7 @@
         },
         initialize: function () {
             this._super();
-            this.on("change:facebookUrl change:twitterUrl change:xUrl change:pinterestUrl change:mybusinessUrl change:youtubeUrl change:linkedinUrl change:viadeoUrl change:instagramUrl change:skypeUrl change:theforkUrl change:tiktokUrl change:tripadvisorUrl change:wazeUrl change:whatsappUrl change:slideshareUrl change:borneoUrl change:boncoinUrl change:pagesjauneUrl, change:customrsUrl", this.onSocialNetworkUrlChange);
+            this.on("change:facebookUrl change:twitterUrl change:xUrl change:pinterestUrl change:mybusinessUrl change:youtubeUrl change:linkedinUrl change:viadeoUrl change:instagramUrl change:skypeUrl change:theforkUrl change:tiktokUrl change:tripadvisorUrl change:wazeUrl change:whatsappUrl change:slideshareUrl change:borneoUrl change:boncoinUrl change:pagesjauneUrl, change:customrsUrl, change:indeedUrl", this.onSocialNetworkUrlChange);
             this.on("change:MarqueClient", this.onMarqueClientChange);
         },
         onSocialNetworkUrlChange: function (model) {
@@ -119,7 +121,7 @@
             _.each(changed, function (value, key) {
                 if ((key === "facebookUrl" || key === "twitterUrl" || key === "xUrl" || key === "pinterestUrl" || key === "mybusinessUrl" || key==="instagramUrl"||key==="youtubeUrl"||key==="linkedinUrl"||key==="viadeoUrl" || 
                     key==="skypeUrl" || key==="theforkUrl" || key ==="tiktokUrl" || key ==="tripadvisorUrl" || key==="wazeUrl" || key==="whatsappUrl" || key==="slideshareUrl" || key==="borneoUrl" || key==="boncoinUrl" || key==="pagesjauneUrl" 
-                    || key === "customrsUrl")) {
+                    || key === "customrsUrl" || key === "indeedUrl")) {
                     that.trigger(Events.SettingsEvents.SOCIAL_NETWORK__CHANGE, key, value, model);
                 }
             });
@@ -147,7 +149,7 @@
        
 
             var socialNetworks = ["facebookUrl", "mybusinessUrl", "twitterUrl", "xUrl", "pinterestUrl", "youtubeUrl", "linkedinUrl", "viadeoUrl", "instagramUrl","skypeUrl","theforkUrl","tiktokUrl","tripadvisorUrl","wazeUrl","whatsappUrl","slideshareUrl", 
-                "borneoUrl", "boncoinUrl", "pagesjauneUrl", "customrsUrl"
+                "borneoUrl", "boncoinUrl", "pagesjauneUrl", "customrsUrl", "indeedUrl"
             ];
             //en gros c'est ça dans une boucle:
             for (var i = 0; i < socialNetworks.length; i++) {             
Index: src/js/JEditor/ParamsPanel/Models/SocialList.js
===================================================================
--- src/js/JEditor/ParamsPanel/Models/SocialList.js	(révision 13820)
+++ src/js/JEditor/ParamsPanel/Models/SocialList.js	(révision 13821)
@@ -312,4 +312,9 @@
         name: "pagesjaunes",
         defaultTitle: "Pagesjaune"
     },
+    indeedUrl : {
+        icon: `<svg width="20" height="20" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M0 8C0 3.58172 3.58172 0 8 0H42C46.4183 0 50 3.58172 50 8V42C50 46.4183 46.4183 50 42 50H8C3.58172 50 0 46.4183 0 42V8ZM22.2412 26.2232V39.603C22.2412 40.8374 22.5368 41.7639 23.1078 42.3797C23.699 43.016 24.4178 43.3239 25.325 43.3239C26.2465 43.3239 27.0059 43.016 27.5768 42.4003C28.1508 41.7816 28.4464 40.855 28.4464 39.6V24.9125C26.9711 25.7863 25.2322 26.276 23.3861 26.276C22.9976 26.276 22.6267 26.2583 22.2412 26.2232ZM33.782 9.04631C30.4954 6.24897 26.119 6.17567 22.2761 7.53915C16.7 9.82922 12.8221 14.8257 11.0687 20.825C10.7702 21.951 10.5296 23.0771 10.4021 24.2411C10.4021 24.2456 10.4008 24.2649 10.3988 24.2951C10.3834 24.5252 10.326 25.3866 10.4745 25.1119C10.5995 24.9114 10.6716 24.6379 10.7385 24.3842C10.7616 24.2967 10.784 24.2115 10.8078 24.1325C11.657 21.3147 12.6917 18.7519 14.2799 16.3182C18.0823 10.8115 24.1976 7.26645 30.6604 9.22811C31.8604 9.62689 32.93 10.2075 34.0022 10.8995C34.0193 10.9115 34.0455 10.9306 34.0788 10.955C34.4449 11.2225 35.6756 12.1221 35.3701 11.1546C35.0918 10.3189 34.3936 9.60929 33.782 9.04631ZM25.1568 22.9333C27.6754 22.9333 29.7187 20.8661 29.7187 18.318C29.7187 15.7669 27.6754 13.7027 25.1568 13.7027C22.6383 13.7027 20.5979 15.7699 20.5979 18.318C20.5979 20.8661 22.6383 22.9333 25.1568 22.9333Z" fill="#003A9B"/></svg>`,
+        name: "indeed",
+        defaultTitle: "Indeed"
+    }
 });
\ No newline at end of file
Index: src/js/JEditor/ParamsPanel/Templates/ListSocialNetwork.html
===================================================================
--- src/js/JEditor/ParamsPanel/Templates/ListSocialNetwork.html	(révision 13820)
+++ src/js/JEditor/ParamsPanel/Templates/ListSocialNetwork.html	(révision 13821)
@@ -484,6 +484,15 @@
                         <input class="content rs" type="text" name="<%=social.type%>_<%=i %>" value='<%-pagesjauneUrl%>' data-titleId="<%=social.type%>_<%=i %>" data-reseaux="pagesjaunes" />
                     </span>
                 <% } %>
+                <% if( social.type == "indeedUrl") { %>
+                    <span class="inline-params__name  indeed">
+                        <svg width="20" height="20" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M0 8C0 3.58172 3.58172 0 8 0H42C46.4183 0 50 3.58172 50 8V42C50 46.4183 46.4183 50 42 50H8C3.58172 50 0 46.4183 0 42V8ZM22.2412 26.2232V39.603C22.2412 40.8374 22.5368 41.7639 23.1078 42.3797C23.699 43.016 24.4178 43.3239 25.325 43.3239C26.2465 43.3239 27.0059 43.016 27.5768 42.4003C28.1508 41.7816 28.4464 40.855 28.4464 39.6V24.9125C26.9711 25.7863 25.2322 26.276 23.3861 26.276C22.9976 26.276 22.6267 26.2583 22.2412 26.2232ZM33.782 9.04631C30.4954 6.24897 26.119 6.17567 22.2761 7.53915C16.7 9.82922 12.8221 14.8257 11.0687 20.825C10.7702 21.951 10.5296 23.0771 10.4021 24.2411C10.4021 24.2456 10.4008 24.2649 10.3988 24.2951C10.3834 24.5252 10.326 25.3866 10.4745 25.1119C10.5995 24.9114 10.6716 24.6379 10.7385 24.3842C10.7616 24.2967 10.784 24.2115 10.8078 24.1325C11.657 21.3147 12.6917 18.7519 14.2799 16.3182C18.0823 10.8115 24.1976 7.26645 30.6604 9.22811C31.8604 9.62689 32.93 10.2075 34.0022 10.8995C34.0193 10.9115 34.0455 10.9306 34.0788 10.955C34.4449 11.2225 35.6756 12.1221 35.3701 11.1546C35.0918 10.3189 34.3936 9.60929 33.782 9.04631ZM25.1568 22.9333C27.6754 22.9333 29.7187 20.8661 29.7187 18.318C29.7187 15.7669 27.6754 13.7027 25.1568 13.7027C22.6383 13.7027 20.5979 15.7699 20.5979 18.318C20.5979 20.8661 22.6383 22.9333 25.1568 22.9333Z" fill="#003A9B"/></svg>      
+                        <% 
+                            var indeedUrl= (social.title === "") ? __("indeed") : social.title;
+                        %>
+                        <input class="content rs" type="text" name="<%=social.type%>_<%=i %>" value='<%-indeedUrl%>' data-titleId="<%=social.type%>_<%=i %>" data-reseaux="indeed" />
+                    </span>
+                <% } %>
                 <label>
                     <span class="custom-input">
                         <input type="text" data-autosave="true" name="<%=social.type%>_<%=i %>" class="field-input neutral-input  bold" value="<%=social.url%>" />
Index: src/js/JEditor/ParamsPanel/Templates/SocialNetwork.html
===================================================================
--- src/js/JEditor/ParamsPanel/Templates/SocialNetwork.html	(révision 13820)
+++ src/js/JEditor/ParamsPanel/Templates/SocialNetwork.html	(révision 13821)
@@ -106,6 +106,12 @@
                 </a>
             </li>
             <li>
+                <a  data-id="indeedUrl" class="indeed">
+                    <svg width="20" height="20" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M0 8C0 3.58172 3.58172 0 8 0H42C46.4183 0 50 3.58172 50 8V42C50 46.4183 46.4183 50 42 50H8C3.58172 50 0 46.4183 0 42V8ZM22.2412 26.2232V39.603C22.2412 40.8374 22.5368 41.7639 23.1078 42.3797C23.699 43.016 24.4178 43.3239 25.325 43.3239C26.2465 43.3239 27.0059 43.016 27.5768 42.4003C28.1508 41.7816 28.4464 40.855 28.4464 39.6V24.9125C26.9711 25.7863 25.2322 26.276 23.3861 26.276C22.9976 26.276 22.6267 26.2583 22.2412 26.2232ZM33.782 9.04631C30.4954 6.24897 26.119 6.17567 22.2761 7.53915C16.7 9.82922 12.8221 14.8257 11.0687 20.825C10.7702 21.951 10.5296 23.0771 10.4021 24.2411C10.4021 24.2456 10.4008 24.2649 10.3988 24.2951C10.3834 24.5252 10.326 25.3866 10.4745 25.1119C10.5995 24.9114 10.6716 24.6379 10.7385 24.3842C10.7616 24.2967 10.784 24.2115 10.8078 24.1325C11.657 21.3147 12.6917 18.7519 14.2799 16.3182C18.0823 10.8115 24.1976 7.26645 30.6604 9.22811C31.8604 9.62689 32.93 10.2075 34.0022 10.8995C34.0193 10.9115 34.0455 10.9306 34.0788 10.955C34.4449 11.2225 35.6756 12.1221 35.3701 11.1546C35.0918 10.3189 34.3936 9.60929 33.782 9.04631ZM25.1568 22.9333C27.6754 22.9333 29.7187 20.8661 29.7187 18.318C29.7187 15.7669 27.6754 13.7027 25.1568 13.7027C22.6383 13.7027 20.5979 15.7699 20.5979 18.318C20.5979 20.8661 22.6383 22.9333 25.1568 22.9333Z" fill="#003A9B"/></svg>
+                    <%=__("indeed")%>
+                </a>
+            </li>
+            <li>
                 <a  data-id="linkedinUrl" class="linkedin">
                     <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="20px" height="20px" viewBox="0 0 30 30" enable-background="new 0 0 30 30" xml:space="preserve">
                         <path fill="#0065A1" d="M25.313,0H4.688C2.098,0,0,2.1,0,4.688v20.625C0,27.901,2.098,30,4.688,30h20.625
Index: src/js/JEditor/ParamsPanel/Views/SocialNetworks.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/SocialNetworks.js	(révision 13820)
+++ src/js/JEditor/ParamsPanel/Views/SocialNetworks.js	(révision 13821)
@@ -54,7 +54,8 @@
                 borneoUrl: 'borneo',
                 pagesjauneUrl: 'pagesjaunes',
                 boncoinUrl: 'leboncoin',
-                customrsUrl :'customrs'
+                customrsUrl :'customrs',
+                indeedUrl: 'indeed'
             };
             
         },
Index: src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 13820)
+++ src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 13821)
@@ -125,5 +125,7 @@
         "qualityCompression" : "Qualité de compression",
         "lowQuality" : "Normal (par défaut)",
         "highQuality" : "Haute",
-        "Custom":"Custom"
+        "Custom":"Custom",
+        "indeed": "Indeed",
+        "Invalid_indeedUrl": "L'url renseignée ne semble pas valide"
     });
Index: src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 13820)
+++ src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 13821)
@@ -130,5 +130,7 @@
         "qualityCompression" : "Qualité de compression",
         "lowQuality" : "Normal (par défaut)",
         "highQuality" : "Haute",
-        "Custom":"Custom"
+        "Custom":"Custom",
+        "indeed": "Indeed",
+        "Invalid_indeedUrl": "L'url renseignée ne semble pas valide"
     });
Index: src/js/JEditor/ParamsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 13820)
+++ src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 13821)
@@ -129,6 +129,8 @@
         "Pagesjaune" : "Pagesjaunes",
         "Boncoin": "Leboncoin",
         "Custom": "Custom",
+        "indeed": "Indeed",
+        "Invalid_indeedUrl": "This is not a valid url"
     },
     "fr-fr": true,
     "fr-ca": true
