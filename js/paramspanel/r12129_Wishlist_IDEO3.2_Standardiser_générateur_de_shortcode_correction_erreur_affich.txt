Revision: r12129
Date: 2024-03-14 16:19:31 +0300 (lkm 14 Mar 2024) 
Author: jn.harison 

## Commit message
Wishlist IDEO3.2:Standardiser générateur de shortcode: correction erreur affichage suite

## Files changed

## Full metadata
------------------------------------------------------------------------
r12129 | jn.harison | 2024-03-14 16:19:31 +0300 (lkm 14 Mar 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/CustomShortcode.js

Wishlist IDEO3.2:Standardiser générateur de shortcode: correction erreur affichage suite
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Views/CustomShortcode.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/CustomShortcode.js	(révision 12128)
+++ src/js/JEditor/ParamsPanel/Views/CustomShortcode.js	(révision 12129)
@@ -26,7 +26,7 @@
                     this._super();
                     this.data = this.options.model.toJSON();
                     this.data = this.data.CustomShortcode;
-                    if(!Array.isArray(this.data) || typeof this.data === "string" || this.data.trim() == '')
+                    if(this.data === "")
                     {
                         this.currentList = null;
                     }
