Revision: r12132
Date: 2024-03-18 10:50:12 +0300 (lts 18 Mar 2024) 
Author: jn.harison 

## Commit message
Wishlist IDEO3.2:Correction erreur admin standardisation générateur de shortcode

## Files changed

## Full metadata
------------------------------------------------------------------------
r12132 | jn.harison | 2024-03-18 10:50:12 +0300 (lts 18 Mar 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/CustomShortcode.js

Wishlist IDEO3.2:Correction erreur admin standardisation générateur de shortcode
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Views/CustomShortcode.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/CustomShortcode.js	(révision 12131)
+++ src/js/JEditor/ParamsPanel/Views/CustomShortcode.js	(révision 12132)
@@ -26,14 +26,20 @@
                     this._super();
                     this.data = this.options.model.toJSON();
                     this.data = this.data.CustomShortcode;
-                    if(this.data === "")
-                    {
-                        this.currentList = null;
+                    if(typeof this.data !== "object"){
+                        if(this.data === "")
+                        {
+                            this.currentList = null;
+                        }
+                        else
+                        {
+                            this.currentList = JSON.parse(this.data);
+                        }
                     }
-                    else
-                    {
-                        this.currentList = JSON.parse(this.data);
+                    else{
+                        this.currentList = this.data;
                     }
+                  
                     
                     this._template = this.buildTemplate(template,translate);
                 },
