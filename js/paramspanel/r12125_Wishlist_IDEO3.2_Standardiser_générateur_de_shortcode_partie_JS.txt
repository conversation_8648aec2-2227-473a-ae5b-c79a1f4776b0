Revision: r12125
Date: 2024-03-14 14:51:29 +0300 (lkm 14 Mar 2024) 
Author: jn.harison 

## Commit message
Wishlist IDEO3.2:Standardiser générateur de shortcode (partie JS)

## Files changed

## Full metadata
------------------------------------------------------------------------
r12125 | jn.harison | 2024-03-14 14:51:29 +0300 (lkm 14 Mar 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Models/Params.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/ParamsPanel.js
   A /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Templates/CustomShortcode.html
   A /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/CustomShortcode.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/i18n.js

Wishlist IDEO3.2:Standardiser générateur de shortcode (partie JS)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Models/Params.js
===================================================================
--- src/js/JEditor/ParamsPanel/Models/Params.js	(révision 12124)
+++ src/js/JEditor/ParamsPanel/Models/Params.js	(révision 12125)
@@ -71,7 +71,8 @@
             LogoSmall: null,
             Favicon: null,
             Opengraph:null,
-            IconsCollection: "outline"
+            IconsCollection: "outline",
+            CustomShortcode: null
         },
         constructor: function () {
             if (arguments.callee.caller !== Params.getInstance)
Index: src/js/JEditor/ParamsPanel/ParamsPanel.js
===================================================================
--- src/js/JEditor/ParamsPanel/ParamsPanel.js	(révision 12124)
+++ src/js/JEditor/ParamsPanel/ParamsPanel.js	(révision 12125)
@@ -13,11 +13,13 @@
     "./Views/FontsGoogleView",
     "./Views/IconsCollectionView",
     "./Views/ThemeSiteView",
+    "./Views/CustomShortcode",
     "./Models/Params",
+
     //hidden
     "jqueryui/datepicker"
 ],
-        function ($, _, PanelView, Events, ParamsPanelTemplate, translate, SocialNetworks,SEOView, MobileStoreView, PolitiqueConfidentialiteView,DonneeStructureeView, MarqueClientView, FontsGoogleView, IconsCollectionView,ThemeSiteView,Params) {
+        function ($, _, PanelView, Events, ParamsPanelTemplate, translate, SocialNetworks,SEOView, MobileStoreView, PolitiqueConfidentialiteView,DonneeStructureeView, MarqueClientView, FontsGoogleView, IconsCollectionView,ThemeSiteView,CustomShortcode,Params) {
             var SocialPanel = PanelView.extend(
                     /**
                      * @lends JEditor.Panels.MessagePanel.prototype
@@ -94,6 +96,11 @@
                                             icon:"icon-link",
                                             title:translate("PolitiqueConfidentialite")
                                         },
+                                        customShortcode:{
+                                            object: new CustomShortcode({model:this.params}),
+                                            icon:"icon-link",
+                                            title:translate("CustomShortcode")
+                                        },
                                        
                                     };
                                     if(this.app.user.can('view_jsonLd')) {
Index: src/js/JEditor/ParamsPanel/Templates/CustomShortcode.html
===================================================================
--- src/js/JEditor/ParamsPanel/Templates/CustomShortcode.html	(nonexistent)
+++ src/js/JEditor/ParamsPanel/Templates/CustomShortcode.html	(révision 12125)
@@ -0,0 +1,89 @@
+<div class="main-content__wrapper  presentationvideo-settings">
+    <!--
+        ******************************************
+        créer nouvelle childView Custom shortcode,
+        y déplacer ce contenu
+        ******************************************
+        -->
+       
+        <div class="ideo-title">
+            <h1>
+                <%=__('CustomShortcode') %> 
+                <span><%=__('CustomShortcode') %></span>
+            </h1>
+        </div>
+        <div class="block-button medium align-center text-center">
+           <a class="button" id="addShortcode"><%=__('addShortcode')%></a>
+        </div>
+        <br/>
+        <%
+            for(var i=0;i< listeShortcode.length; i++){
+                var shortcode=listeShortcode[i];
+                
+        %>
+
+        <div id="content" class="inline-params thin-border  radius  shadow">
+            <span class="inline-params__name">
+                <svg width="70px" height="70px" viewBox="0 0 70 70" enable-background="new 0 0 70 70" xml:space="preserve">
+                <path fill="#34495E" d="M35,0C15.67,0,0,15.67,0,35s15.67,35,35,35s35-15.67,35-35S54.33,0,35,0z M35,67C17.327,67,3,52.673,3,35S17.327,3,35,3s32,14.327,32,32S52.673,67,35,67z M22.436,35.002l4.769-4.528c0.556-0.527,0.556-1.385,0-1.912c-0.556-0.533-1.464-0.533-2.02,0l-5.782,5.484c-0.556,0.527-0.556,1.385,0,1.912l5.788,5.501c0.561,0.527,1.47,0.527,2.025,0s0.556-1.385,0-1.918L22.436,35.002z M38.728,28.149c-0.7-0.341-1.563-0.082-1.927,0.582l-6.152,11.305c-0.365,0.67-0.087,1.489,0.613,1.829c0.694,0.347,1.563,0.083,1.921-0.582l6.158-11.304C39.699,29.314,39.428,28.49,38.728,28.149z M44.77,28.562c-0.557-0.527-1.465-0.527-2.02,0c-0.563,0.527-0.563,1.385,0,1.912l4.779,4.539l-4.769,4.528c-0.562,0.533-0.562,1.391,0,1.918c0.556,0.527,1.458,0.527,2.021,0l5.775-5.484c0.561-0.527,0.561-1.391,0-1.918L44.77,28.562z"></path>
+                </svg>
+                <%=__('CustomShortcodeName') %> 
+            </span>
+            <span class="custom-input">
+                <input type="text"  id="customShortcodeName_<%=shortcode.id%>" class="neutral-input bold customShortcodeName" value="<%=shortcode.name%>">
+            </span>
+            <%
+            if( shortcode.position == "customPosition"){
+        %> 
+            <span class="rightShortcode">
+                <span class="shortcode">[[custom_<%= shortcode.shortcode %>]]</span>
+            </span><br/>
+        <%
+            }
+        %>
+        <br/>
+            <span class="inline-params__name">
+                <svg width="70px" height="70px" viewBox="0 0 70 70" enable-background="new 0 0 70 70" xml:space="preserve">
+                <path fill="#34495E" d="M35,0C15.67,0,0,15.67,0,35s15.67,35,35,35s35-15.67,35-35S54.33,0,35,0z M35,67C17.327,67,3,52.673,3,35S17.327,3,35,3s32,14.327,32,32S52.673,67,35,67z M22.436,35.002l4.769-4.528c0.556-0.527,0.556-1.385,0-1.912c-0.556-0.533-1.464-0.533-2.02,0l-5.782,5.484c-0.556,0.527-0.556,1.385,0,1.912l5.788,5.501c0.561,0.527,1.47,0.527,2.025,0s0.556-1.385,0-1.918L22.436,35.002z M38.728,28.149c-0.7-0.341-1.563-0.082-1.927,0.582l-6.152,11.305c-0.365,0.67-0.087,1.489,0.613,1.829c0.694,0.347,1.563,0.083,1.921-0.582l6.158-11.304C39.699,29.314,39.428,28.49,38.728,28.149z M44.77,28.562c-0.557-0.527-1.465-0.527-2.02,0c-0.563,0.527-0.563,1.385,0,1.912l4.779,4.539l-4.769,4.528c-0.562,0.533-0.562,1.391,0,1.918c0.556,0.527,1.458,0.527,2.021,0l5.775-5.484c0.561-0.527,0.561-1.391,0-1.918L44.77,28.562z"></path>
+                </svg>
+                <%=__('CustomShortcodePosition') %> 
+            </span>
+            <label> 
+                <span class="custom-input">
+                   <select id="CustomShortcode_<%=shortcode.id%>" name="customShortcodePosition<%=shortcode.id%>" class="neutral-input bold customShortcodePosition">
+                       <option value="headStart"  <%=(shortcode.position==='headStart' ? 'selected' : '') %>  > <%=__("headStart") %></option>
+                       <option value="headEnd" <%=(shortcode.position==='headEnd' ? 'selected' : '') %> ><%=__('headEnd')%></option>
+                       <option value="bodyStart"  <%=(shortcode.position==='bodyStart' ? 'selected' : '') %> ><%=__('bodyStart')%></option>
+                       <option value="bodyEnd"  <%=(shortcode.position==='bodyEnd' ? 'selected' : '') %> ><%=__('bodyEnd')%></option>
+                       <option value="customPosition"  <%=(shortcode.position==='customPosition' ? 'selected' : '') %> ><%=__('customPosition')%></option>
+                   </select>
+                </span>
+                
+            </label>
+           
+            <label>
+                <span class="custom-input" style="margin-bottom: 20px;">
+                    <textarea id="CustomShortcodeContent_<%=shortcode.id%>" name="customShortcodeContent" rows="5" class="neutral-input  bold customShortcodeContent" >
+                        <%=shortcode.content%>
+                        
+                    </textarea>
+                </span>
+               
+            </label>
+
+         
+            <span class="rigth-delete icon-bin" data-name="<%=shortcode.name%>" data-id="CustomShortcode_<%=shortcode.id%>"> </span>
+        </div>
+
+        <%
+            }
+        %> 
+
+        
+        
+        <!-- 
+        ******************
+        end new childView
+        ******************
+        -->
+</div>
\ No newline at end of file

Property changes on: src/js/JEditor/ParamsPanel/Templates/CustomShortcode.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 12124)
+++ src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 12125)
@@ -93,5 +93,14 @@
         "Themedescription" :    "Choisissez s'il faut applique le thème sombre (Dark Mode) par défaut sur le site",
         "ThemeLabel":   "Thème",
         "lightLabel":   "Claire (défault)",
-        "darkLabel" :   "Sombre"
+        "darkLabel" :   "Sombre",
+        "addShortcode": "Ajouter un shortcode",
+        "CustomShortcode": "Shortcode personnalisé",
+        "headStart": "Début du head",
+        "headEnd": 'Fin du head',
+        "bodyStart": "Début du body",
+        "bodyEnd": "Fin du body",
+        "customPosition": "Position personnalisé",
+        "CustomShortcodePosition": "Position du shortcode",
+        "CustomShortcodeName": "Nom du shortcode"
     });
Index: src/js/JEditor/ParamsPanel/Views/CustomShortcode.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/CustomShortcode.js	(nonexistent)
+++ src/js/JEditor/ParamsPanel/Views/CustomShortcode.js	(révision 12125)
@@ -0,0 +1,156 @@
+define(['jquery',
+        "underscore", 
+        'JEditor/Commons/Ancestors/Views/View',
+        'text!../Templates/CustomShortcode.html',
+        "JEditor/App/Messages/ClipboardModule", 
+        "JEditor/App/Messages/Confirm",
+        'i18n!../nls/i18n'], function (
+            $,
+            _,
+            View, 
+            template,
+            ClipboardModule,
+            Confirm,
+            translate) {
+            var CustomShortcode = View.extend({
+                events: {
+                    "change .neutral-input.bold.customShortcodeName ": "onChangeShortcodeName",
+                    "change .neutral-input.bold.customShortcodePosition": "onChangeShortcodePosition",
+                    "change .neutral-input.bold.customShortcodeContent": "onChangeShortcodeContent",
+                    "click #addShortcode": "onClickAddShortcode",
+                    'click .rightShortcode .shortcode' : 'copyToClipboard',
+                    "click .rigth-delete": "deleteOneShortcode"
+                },
+                currentList: null,
+                initialize: function () {
+                    this._super();
+                    this.data = this.options.model.toJSON();
+                    this.data = this.data.CustomShortcode;
+                    //console.log(typeof this.data);
+                    //if(!Array.isArray(this.data))this.data=[];
+                    this.currentList = JSON.parse(this.data);
+                    this._template = this.buildTemplate(template,translate);
+                },
+                confirm: function(params) {
+                    this.app.messageDelegate.set(new Confirm(params));
+                },
+               onClickAddShortcode: function(){
+                var count = ( (this.currentList === undefined || this.currentList === null) ? 0 : this.currentList.length);
+                var shortCodeObject = {
+                    id: count,
+                    name: "",
+                    position: "headStart",
+                    shortcode: "",
+                    content:""
+                };
+                if( this.currentList === undefined || this.currentList === null ) this.currentList = [];
+                this.currentList.push(shortCodeObject);
+                
+                this.model.set("CustomShortcode", this.currentList);
+                this.model.save();
+                this.render();
+               },
+               deleteOneShortcode: function(event){
+                var $target = $(event.currentTarget);
+                var $targetName = $target.attr('data-id');
+                var id = $targetName.split('_')[1];
+                this.confirm({
+                    // message: translate('confirmDeleteSocial', {'name': $target.attr("name")}),
+                    message: translate('confirmDeleteSocial', {'name': $target.attr('data-name')}),
+                    title: translate("deleteAction"),
+                    type: 'delete',
+                    onOk: _.bind(function() {
+                        var index = this.currentList.findIndex( item => item.id == Number(id));
+                        if( index != -1)
+                        {
+                            var newCurrentList = this.currentList.filter( item => item.id !== Number(id));
+                            this.model.set("CustomShortcode", newCurrentList);
+                            this.model.save();
+                    
+                            // re-render the view with the new currentList array
+                            this.currentList = newCurrentList;
+                            this.render();
+        
+                        }
+                      }, this),
+                    //options: {dialogClass: 'delete no-close', dontAskAgain: true}
+                    options: {dialogClass: 'delete no-close'}
+                });
+               },
+            //copy shortcode to clipboard
+            copyToClipboard : function (e){
+                ClipboardModule.copyToClipboard(e);
+            },
+               //modify name of shortcode
+               onChangeShortcodeName :function(event){
+                var $target = $(event.currentTarget);
+                var $targetName = $target.attr("id");
+                var $targetValue = $('#'+$targetName).val();
+                //replace all accent for the shortcode
+                var $targetShortcode = this._removeAccentsShortcode($targetValue);
+                //replace the space by _ for the shortcode
+                $targetShortcode = $targetShortcode.replace(/\s+/g, '_')
+                var id = $targetName.split('_');
+                this.updateProperty(id[1], "name", $targetValue);
+                this.updateProperty(id[1], "shortcode", $targetShortcode);
+                this.model.set("CustomShortcode", this.currentList);
+                this.model.save();
+                this.render();
+               },
+               onChangeShortcodePosition: function(event){
+                var $target = $(event.currentTarget);
+                var $targetName = $target.attr("id");
+                var $targetValue = $('#'+$targetName).val();
+                var id = $targetName.split('_');
+                this.updateProperty(id[1], "position", $targetValue);
+                this.model.set("CustomShortcode", this.currentList);
+                this.model.save();
+                this.render();
+               },
+               onChangeShortcodeContent: function(event){
+                var $target = $(event.currentTarget);
+                var $targetName = $target.attr("id");
+                var $targetValue = $('#'+$targetName).val();
+                var id = $targetName.split('_');
+                this.updateProperty(id[1], "content", $targetValue);
+                this.model.set("CustomShortcode", this.currentList);
+                this.model.save();
+               },
+               updateProperty: function( id, propertyName, newValue)
+               {
+                    id = Number(id);
+                    // Find the object with the given id
+                    var obj = this.currentList.find(item => item.id === id);
+
+                    if (obj) {
+                        obj[propertyName] = newValue;
+                    }  
+               },
+               _removeAccentsShortcode: function (shortcode) {
+                    var accentMap = {
+                        'á': 'a', 'é': 'e', 'í': 'i', 'ó': 'o', 'ú': 'u', 'ý': 'y',
+                        'à': 'a', 'è': 'e', 'ì': 'i', 'ò': 'o', 'ù': 'u', 'ỳ': 'y',
+                        'â': 'a', 'ê': 'e', 'î': 'i', 'ô': 'o', 'û': 'u', 'ŷ': 'y',
+                        'ä': 'a', 'ë': 'e', 'ï': 'i', 'ö': 'o', 'ü': 'u', 'ÿ': 'y',
+                        'ã': 'a', 'õ': 'o', 'ñ': 'n', 'ç': 'c',
+                        'Á': 'A', 'É': 'E', 'Í': 'I', 'Ó': 'O', 'Ú': 'U', 'Ý': 'Y',
+                        'À': 'A', 'È': 'E', 'Ì': 'I', 'Ò': 'O', 'Ù': 'U', 'Ỳ': 'Y',
+                        'Â': 'A', 'Ê': 'E', 'Î': 'I', 'Ô': 'O', 'Û': 'U', 'Ŷ': 'Y',
+                        'Ä': 'A', 'Ë': 'E', 'Ï': 'I', 'Ö': 'O', 'Ü': 'U', 'Ÿ': 'Y',
+                        'Ã': 'A', 'Õ': 'O', 'Ñ': 'N', 'Ç': 'C'
+                    };
+                
+                    return shortcode.replace(/[^\u0000-\u007E]/g, function (ch) {
+                        return accentMap[ch] || ch;
+                    });
+               },
+                render: function () {
+                    this.undelegateEvents();
+                    this.$el.html(this._template({ listeShortcode: (this.currentList != null? this.currentList :[])}));
+                    this.delegateEvents();
+                    return this;
+                }
+            
+            });
+            return CustomShortcode;
+        });
\ No newline at end of file

Property changes on: src/js/JEditor/ParamsPanel/Views/CustomShortcode.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 12124)
+++ src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 12125)
@@ -97,5 +97,14 @@
         "Themedescription" :    "Choisissez s'il faut applique le thème sombre (Dark Mode) par défaut sur le site",
         "ThemeLabel":   "Thème",
         "lightLabel":   "Claire (défault)",
-        "darkLabel" :   "Sombre"
+        "darkLabel" :   "Sombre",
+        "addShortcode": "Ajouter un shortcode",
+        "CustomShortcode": "Shortcode personnalisé",
+        "headStart": "Début du head",
+        "headEnd": 'Fin du head',
+        "bodyStart": "Début du body",
+        "bodyEnd": "Fin du body",
+        "customPosition": "Position personnalisé",
+        "CustomShortcodePosition": "Position du shortcode",
+        "CustomShortcodeName": "Nom du shortcode"
     });
Index: src/js/JEditor/ParamsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 12124)
+++ src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 12125)
@@ -105,7 +105,16 @@
         "Themedescription" :    "Choose whether to apply the default Dark Mode theme to the site",
         "ThemeLabel":   "Theme",
         "lightLabel":   "Light (default)",
-        "darkLabel" :   "Darl"
+        "darkLabel" :   "Darl",
+        "addShortcode": "Add shortcode",
+        "CustomShortcode": "Custom shortcode",
+        "headStart": "Start of head",
+        "headEnd": 'End of head',
+        "bodyStart": "Start of body",
+        "bodyEnd": "End of body",
+        "customPosition": "Custom position",
+        "CustomShortcodePosition": "Shortcode position",
+        "CustomShortcodeName": "Shortcode name"
     },
     "fr-fr": true,
     "fr-ca": true
