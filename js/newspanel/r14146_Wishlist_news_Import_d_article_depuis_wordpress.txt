Revision: r14146
Date: 2025-04-18 14:51:49 +0300 (zom 18 Apr 2025) 
Author: frahajanirina 

## Commit message
Wishlist:news:Import d'article depuis wordpress

## Files changed

## Full metadata
------------------------------------------------------------------------
r14146 | frahajanirina | 2025-04-18 14:51:49 +0300 (zom 18 Apr 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Models/Article.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/NewsPanel.html
   A /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/UploadArticle.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/newsEditor.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/CategorieCollectionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/NewsEditorView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/SaveButton.js
   A /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/UploadArticleView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/news_panel/main.less

Wishlist:news:Import d'article depuis wordpress
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Models/Article.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Article.js	(révision 14145)
+++ src/js/JEditor/NewsPanel/Models/Article.js	(révision 14146)
@@ -64,6 +64,15 @@
                         getPageModel: function(){
                             if (this.pageModel) return this.pageModel ;
                             if(!this.page) return null;
+                            var modelPage = new Page(), self = this;
+                            
+                            // ajouter la page dans la collection (pageCollection)
+                            modelPage.fetchPageById(this.page).success(function(data) {
+                                self.pageModel = new Page(data);
+                                self.pageCollection.add(self.pageModel);
+                            }).error(function(error) {
+                                console.log(error);
+                            });
                                 pageModel = this.pageCollection.findWhere({id: this.page});
                                 if (pageModel) {
                                     this.pageModel = pageModel;
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 14145)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 14146)
@@ -67,6 +67,7 @@
                                     'click #show-zone-version': 'showZoneVersionsPanel',
                                     'click .upload-article' : 'inTheSiteAndComputer',
                                     'click .category-img' : 'uploadImgCategoryinTheSiteAndComputer',
+                                    'click .uploadArticle':'onUploadArticleClick',
                                 },
                                 cssFile: __IDEO_INTEGRATION_PATH__ + "/build/css/news_panel.css",
 
@@ -579,6 +580,11 @@
                                     });
                                 }
                                 },
+                                onUploadArticleClick: function() {
+                                    this.currentCategorie = null;
+                                    this.currentArticle = null;
+                                    this.childViews.newsEditorView.renderUploadArticle();
+                                },
                                 /**
                                  * ajoute les variables à l'objet this.dom
                                 */
Index: src/js/JEditor/NewsPanel/Templates/UploadArticle.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/UploadArticle.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Templates/UploadArticle.html	(révision 14146)
@@ -0,0 +1,16 @@
+<div class="FileTop" style="margin: 0;">
+    <div class="my-files fileList">
+        <div class="content scroll-container">
+            <div class="group-content flex-15">
+                <div class="add-file xml-file" id="dropZone">
+                    <span class="icon">
+                        <span class="icon-hexagon"></span>
+                        <span class="icon-add"></span>
+                    </span>
+                    <%= __("uploadArticle")%>
+                    <input type="file" class="hidden-file" style="display: none;">
+                </div>
+            </div>
+        </div>
+    </div>
+</div>
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Templates/newsEditor.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/newsEditor.html	(révision 14145)
+++ src/js/JEditor/NewsPanel/Templates/newsEditor.html	(révision 14146)
@@ -34,5 +34,6 @@
     <div id="content-editor">
         <div id="category-editor"></div>
         <div id="article-editor"></div>
+        <div id="article-upload"></div>
     </div>
 </div>
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Views/NewsEditorView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 14145)
+++ src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 14146)
@@ -14,6 +14,7 @@
     "JEditor/NewsPanel/Views/ConfigArticleView",
     "JEditor/NewsPanel/Views/PagePreview",
     "JEditor/NewsPanel/Views/ArticleEditorView",
+    "JEditor/NewsPanel/Views/UploadArticleView",
     "i18n!../nls/i18n",
     //not in params
    
@@ -33,6 +34,7 @@
      ConfigArticleView, 
      PagePreview,
      ArticleEditorView,
+     UploadArticleView,
      translate
      ) {
    
@@ -159,6 +161,17 @@
             this.render();
             this.articleView.append(this.articleEditorView.render().el);
          },
+        renderUploadArticle: function (){
+            this.removeChildren();
+            this.options.title = 'Importer';
+            this.articleUploadView = new UploadArticleView({
+                language : this.currentLang,
+                newsPanel : this
+            });
+            this.render();
+            this.uploadView.append(this.articleUploadView.render().el);
+            this.$('.config #params').hide();
+        },
         render: function() {
             this.undelegateEvents();
             this.$el.empty();
@@ -169,6 +182,7 @@
             }));
             this.categoryView = this.$("#category-editor");
             this.articleView = this.$("#article-editor");
+            this.uploadView = this.$("#article-upload");
             this.$(".message").hide();
             this.delegateEvents();
 
Index: src/js/JEditor/NewsPanel/Views/SaveButton.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/SaveButton.js	(révision 14145)
+++ src/js/JEditor/NewsPanel/Views/SaveButton.js	(révision 14146)
@@ -108,7 +108,21 @@
                          * @public
                          */
                         saveArticle: function (e) {
-                            
+                            if (!this.model.ressource) {
+                                this.error({
+                                    title: translate('saveAction'),
+                                    message: translate('RessourceRequired')
+                                });
+
+                                return false;
+                            }
+                            var introduction = $('textarea[name="introduction"]');
+                            this.model.introduction = introduction.val();
+                            if (introduction.val() == '') {
+                                introduction.addClass('error');
+                                
+                                return false;
+                            }
                             this.listenToOnce(this.model, Events.BackboneEvents.SYNC, this.onSave);
                             this.listenToOnce(this.model, Events.BackboneEvents.ERROR, this.onError);
                             this.model.unsetPublishData();
Index: src/js/JEditor/NewsPanel/Templates/NewsPanel.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/NewsPanel.html	(révision 14145)
+++ src/js/JEditor/NewsPanel/Templates/NewsPanel.html	(révision 14146)
@@ -33,6 +33,10 @@
             <a class="dialog-view-trigger addArticle">
                 <span>+</span><%=__("newsAddArticle")%>
             </a>
+            <a class="dialog-view-trigger uploadArticle">
+                <span class="icon-add-image"></span>
+                <%=__("uploadLink")%>
+            </a>
         <% } %>
 	</aside>
     <div id="item-config" class="panel-container"></div>
Index: src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 14145)
+++ src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 14146)
@@ -57,7 +57,11 @@
           });
       };
       }
+      this.listenTo(Backbone, 'upload:success', this._refreshArticleList);
     },
+    _refreshArticleList: function(resp) {
+      this.collection.add(resp.articles);
+    },
     render: function(searchValue) {
       this.undelegateEvents();
       var list;
Index: src/js/JEditor/NewsPanel/Views/CategorieCollectionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/CategorieCollectionView.js	(révision 14145)
+++ src/js/JEditor/NewsPanel/Views/CategorieCollectionView.js	(révision 14146)
@@ -38,8 +38,12 @@
         this.listenTo(this.collection,"change",this.render);
         this.listenTo(this.collection,"add",this.render);
         this.listenTo(this.collection,"remove",this.render);
+        this.listenTo(Backbone, 'upload:success', this._refreshCategoryList);
 
       },
+      _refreshCategoryList: function(resp) {
+        this.collection.add(resp.categories);
+      },
       // override masiso
       render: function() {
         this.undelegateEvents();
Index: src/js/JEditor/NewsPanel/Views/UploadArticleView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/UploadArticleView.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Views/UploadArticleView.js	(révision 14146)
@@ -0,0 +1,104 @@
+define([
+    "jquery",
+    "underscore",
+    "JEditor/Commons/Ancestors/Views/BabblerView",
+    "text!../Templates/UploadArticle.html",
+    "i18n!../nls/i18n",
+    "jqueryPlugins/uploader"
+  ], function ($, _, BabblerView, template, translate) {
+    var UploadArticleView = BabblerView.extend({
+        events: {
+            "click .xml-file": "triggerFileInput",
+            "change .hidden-file": "uploadFile",
+            "dragover #dropZone": "handleDragOver",
+            "drop #dropZone": "handleDrop"
+        },
+        initialize: function() {
+            this.template = this.buildTemplate(template, translate);
+        },
+        render: function () {
+            this.$el.html(this.template());
+            
+            return this;
+        },
+        setLoading:function(loading){
+            this.__loading = loading;
+            if(loading) {
+                $('#news #content .view').addClass('loading');
+            } else {
+                $('#news #content .view').removeClass('loading');
+            }
+
+            return this;
+        },
+        triggerFileInput: function () {
+            var input = this.$(".hidden-file");
+
+            if (!input.length) return;
+            input[0].click();
+        },
+        uploadFile: function (event) {
+            var file = event.target.files[0];
+            if (!file) return;
+
+            if (file.type !== 'text/xml') {
+                this.error({
+                    title: translate('error'),
+                    message: translate('fileUnauthorised')
+                });
+
+                return;
+            }
+
+            this.setLoading(true);
+            
+            var formData = new FormData();
+            formData.append('file', file);
+
+            var self = this;
+
+            $.ajax({
+                url: __IDEO_API_PATH__ + '/news/upload',
+                type: 'POST',
+                data: formData,
+                contentType: false, 
+                processData: false,
+                success: function (response) {
+                    self.setLoading(false);
+                    Backbone.trigger('upload:success', response);
+                },
+                error: function (xhr) {
+                    self.setLoading(false);
+    
+                    var errorResponse;
+                    try {
+                        errorResponse = JSON.parse(xhr.responseText);
+                    } catch (e) {
+                        errorResponse = { message: translate('errorDowbloafFile') };
+                    }
+
+                    self.error({
+                        title: translate('error'),
+                        message: errorResponse.message
+                    });
+                }
+            });
+        },
+        handleDragOver: function (event) {
+            event.preventDefault();
+            this.$("#dropZone").addClass("drag-over");
+            this.$('.drag-over').css('background-color', '#f0f0f0');
+        },
+        handleDrop: function (event) {
+            event.preventDefault();
+
+            var file = event.originalEvent.dataTransfer.files[0];
+            if (!file) return;
+
+            this.uploadFile({ target: { files: [file] } });
+            this.$("#dropZone").removeClass("drag-over");
+        }
+    });
+
+    return UploadArticleView;
+  });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 14145)
+++ src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 14146)
@@ -174,5 +174,9 @@
 	"authorChangedDone": "L'auteur a été changé avec succès.",
 	"imageAlt": "Texte alternatif / description",
 	"addImage"  :   "Ajouter une image",
-	"editImage": "Retoucher l'image"
+	"uploadArticle": "Cliquez ici ou glissez-déposez pour importer les articles depuis WordPress",
+	"errorDowbloafFile": "Erreur lors du téléchargement du fichier",
+	"error": "Erreur",
+	"fileUnauthorised": "Le ficher n\'a pas été importé. Ce type de fichier n\'est pas autorisé.",
+	"uploadLink": "Importer les articles"
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 14145)
+++ src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 14146)
@@ -174,5 +174,9 @@
 	"authorChangedDone": "L'auteur a été changé avec succès.",
 	"imageAlt": "Texte alternatif / description",
 	"addImage"  :   "Ajouter une image",
-	"editImage": "Retoucher l'image"
+	"uploadArticle": "Cliquez ici ou glissez-déposez pour importer les articles depuis WordPress",
+	"errorDowbloafFile": "Erreur lors du téléchargement du fichier",
+	"error": "Erreur",
+	"fileUnauthorised": "Le ficher n\'a pas été importé. Ce type de fichier n\'est pas autorisé.",
+	"uploadLink": "Importer les articles"
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/i18n.js	(révision 14145)
+++ src/js/JEditor/NewsPanel/nls/i18n.js	(révision 14146)
@@ -171,7 +171,11 @@
         "authorChangedDone": "The author has been changed successfully.",
         "imageAlt":"Alternative text / description",
         "addImage" :  "Add an image",
-        "editImage":"Image editing"
+        "uploadArticle": "Click here or drag and drop to import articles from WordPress",
+        "errorDowbloafFile": "Error downloading file",
+        "error": "Error",
+        "fileUnauthorised": "The file was not imported. This file type is not allowed.",
+        "uploadLink": "Import articles"
     },
     "fr-fr": true,
     "fr-ca": true
Index: src/less/imports/news_panel/main.less
===================================================================
--- src/less/imports/news_panel/main.less	(révision 14145)
+++ src/less/imports/news_panel/main.less	(révision 14146)
@@ -314,7 +314,8 @@
 	position: relative;
 }
 .sideBar .addCategory span,
-.sideBar .addArticle span {
+.sideBar .addArticle span, 
+.sideBar .uploadArticle span {
 	display: inline-block;
 	vertical-align: middle;
 	font-size: 30px;
@@ -321,7 +322,7 @@
 	margin-left: 1px;
 	margin-right: 6px;
 } 
-.addArticle , .addCategory {
+.addArticle , .addCategory, .uploadArticle {
 	background: 0 0;
 	border: none;
 	display: block;
