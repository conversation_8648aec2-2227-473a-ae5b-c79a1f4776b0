Revision: r14303
Date: 2025-06-02 12:39:59 +0300 (lts 02 Jon 2025) 
Author: frahajanirina 

## Commit message
News: la description n'était pas généré dans les articles

## Files changed

## Full metadata
------------------------------------------------------------------------
r14303 | frahajanirina | 2025-06-02 12:39:59 +0300 (lts 02 Jon 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Models/Article.js

News: la description n'était pas généré dans les articles
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Models/Article.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Article.js	(révision 14302)
+++ src/js/JEditor/NewsPanel/Models/Article.js	(révision 14303)
@@ -150,7 +150,7 @@
                         }
                     });
                    
-            Article.SetAttributes(['category', 'state', 'numberOfTranslation','title', 'lang','ressource','introduction', 'publicationDate','programmingDate', 'metaTitle', 'metaOpengraph', "page", "content","news", "enseigne", "desc", "langAltRessource"]);
+            Article.SetAttributes(['category', 'state', 'numberOfTranslation','title', 'lang','ressource','introduction', 'publicationDate','programmingDate', 'metaTitle', 'metaDescription', 'metaOpengraph', "page", "content","news", "enseigne", "desc", "langAltRessource"]);
             Events.extend(
                 {
                     ArticleEvents : {
