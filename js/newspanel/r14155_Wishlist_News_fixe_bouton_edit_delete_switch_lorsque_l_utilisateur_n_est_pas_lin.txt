Revision: r14155
Date: 2025-04-25 10:55:56 +0300 (zom 25 Apr 2025) 
Author: frahajanirina 

## Commit message
Wishlist:News:fixe bouton edit, delete, switch lorsque l'utilisateur n'est pas linkeo

## Files changed

## Full metadata
------------------------------------------------------------------------
r14155 | frahajanirina | 2025-04-25 10:55:56 +0300 (zom 25 Apr 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/CategorieAddView.js

Wishlist:News:fixe bouton edit, delete, switch lorsque l'utilisateur n'est pas linkeo
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 14154)
+++ src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 14155)
@@ -86,6 +86,18 @@
           this.$el.html(this._emptyTemplate(params));
         }
       }
+      var self = this;
+      this.$('.switch, .globe-icon, .icon-edit, .icon-bin').each(function() {
+        var articleId = $(this).closest('tr').data('cid');
+        var article = self.collection.get(articleId);
+        var role = self.app.user.role;
+        var client = article.content.client;
+        if (role != 'root' && client == 'linkeo') {
+          $(this).parent().addClass('disabled');
+          $(this).css('cursor', 'not-allowed');
+        }
+      });
+
       this.delegateEvents();
       this.scrollables();
       return this;
@@ -198,6 +210,12 @@
       var $target = $(event.currentTarget);
       var articleId = $target.parent().parent().data('cid');
       var article = this.collection.get(articleId);
+      var role = this.app.user.role;
+      var client = article.content.client;
+      if (role != 'root' && client == 'linkeo') {
+        
+        return false;
+      }
       if (article) 
         this.trigger(Events.ChoiceEvents.SELECT, this, article);
       
Index: src/js/JEditor/NewsPanel/Views/CategorieAddView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(révision 14154)
+++ src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(révision 14155)
@@ -154,6 +154,13 @@
 
                 this.$('.upload-categorie .uploader .preview .imagepreview').after('<span style="font-size:9px;display:block;margin-top:35px;color: #32ace0;text-align:center;">'+this.translations.DefaultMessageUploaderLogo+'</span>');
 
+                var isArticleAuthorNotLinkeo = this.model.lang[this.lang.id].isArticleAuthorNotLinkeo;
+                var nbArticle = this.model.lang[this.lang.id].nbArticle;
+                var role = this.app.user.role;
+                if (role != 'root' && nbArticle > 0 && isArticleAuthorNotLinkeo === false) {
+                    $('button.remove').css('cursor', 'not-allowed');
+                }
+                
                 return this;
             }
         },
