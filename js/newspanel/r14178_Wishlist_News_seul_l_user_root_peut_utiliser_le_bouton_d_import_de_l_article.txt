Revision: r14178
Date: 2025-04-29 14:19:41 +0300 (tlt 29 Apr 2025) 
Author: frahajanirina 

## Commit message
Wishlist:News:seul l'user root peut utiliser le bouton d'import de l'article

## Files changed

## Full metadata
------------------------------------------------------------------------
r14178 | frahajanirina | 2025-04-29 14:19:41 +0300 (tlt 29 Apr 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/categorieList.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/CategorieCollectionView.js

Wishlist:News:seul l'user root peut utiliser le bouton d'import de l'article
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Templates/categorieList.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/categorieList.html	(révision 14177)
+++ src/js/JEditor/NewsPanel/Templates/categorieList.html	(révision 14178)
@@ -25,8 +25,9 @@
 <a class="dialog-view-trigger addCategory">
     <span>+</span><%=__("newsAddACategory")%>
 </a>
-
-<a class="dialog-view-trigger uploadArticle">
-    <span class="icon-add-image"></span>
-    <%=__("uploadLink")%>
-</a>
+<% if(user.role == 'root'){ %>
+    <a class="dialog-view-trigger uploadArticle">
+        <span class="icon-add-image"></span>
+        <%=__("uploadLink")%>
+    </a>
+<% } %>
Index: src/js/JEditor/NewsPanel/Views/CategorieCollectionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/CategorieCollectionView.js	(révision 14177)
+++ src/js/JEditor/NewsPanel/Views/CategorieCollectionView.js	(révision 14178)
@@ -72,7 +72,7 @@
                 this.$el.html(this._template(params));
             }
             else {
-                var params = _.extend({}, {content: list, current : this._current,currentlang : this.lang, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected}, this.addTemplateParams(this.collection, list));
+                var params = _.extend({}, {content: list, current : this._current,currentlang : this.lang, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected, user: this.app.user}, this.addTemplateParams(this.collection, list));
                 this.$el.html(this._emptyTemplate(params));
             }
 
