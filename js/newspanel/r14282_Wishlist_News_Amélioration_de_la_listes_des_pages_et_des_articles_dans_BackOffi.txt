Revision: r14282
Date: 2025-05-21 08:41:49 +0300 (lrb 21 Mey 2025) 
Author: frahajanirina 

## Commit message
Wishlist:News:Amélioration de la listes des pages et des articles dans BackOffice

## Files changed

## Full metadata
------------------------------------------------------------------------
r14282 | frahajanirina | 2025-05-21 08:41:49 +0300 (lrb 21 Mey 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/articlesTable.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/PagePanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Templates/filterPage.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Templates/pagePanel.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Views/PageCollectionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/news_panel/main.less
   A /branches/ideo3_v2/integration/src/less/imports/pagination.less
   M /branches/ideo3_v2/integration/src/less/main.less

Wishlist:News:Amélioration de la listes des pages et des articles dans BackOffice
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Templates/articlesTable.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/articlesTable.html	(révision 14281)
+++ src/js/JEditor/NewsPanel/Templates/articlesTable.html	(révision 14282)
@@ -2,7 +2,6 @@
 
 <div id="news-table">
 	<form id="search-form">
-		<label><%=__('search')%></label>
 		<span class="search-news"> 
 			<i class="icon icon-find"></i>
 			<input type="text"  placeholder="<%=__('searchDesc')%>" value="<%=searchValue%>">
@@ -11,7 +10,7 @@
 	</form>
 	<table id="data-table">
 		<thead>
-			<tr>
+			<tr class="firt-row">
 				<th></th>
 				<th data-sort="title" data-sortorder="<%= (sortAsc)?'asc':'desc' %>">
 					<%=__('title')%>
@@ -60,9 +59,15 @@
 			<%} else {%>
 			<%_.each(content,function(item){ %>
 			<tr class="<%=(!item.active && item.ispublier)?'disabled':''%>" data-cid="<%= item.cid %>">
-				<td><span class="img-news"> <img src="<%=item.image%>" alt="titre"></span></td>
+				<td>
+					<div class="col">
+						<span class="img-news">
+							<img src="<%=item.image%>" alt="titre">
+						</span>
+					</div>
+				</td>
 				<td class="title">
-					<%=item.title%>
+					<a href="//<%=item.urlFront%>" target="_blank"><%=item.title%></a>
 				</td>
 				<td class="author">
 					<%=item.author%>
@@ -104,4 +109,18 @@
 			<%}%>
 		</tbody>
 	</table>
+	<div class="pagination-container hidden">
+		<nav>
+			<ul class="pagination">
+				<li class="page-item prev-page disabled">
+					<a class="page-link">« <%=__('prev')%></a>
+				</li>
+				<li id="pages" class="page-item">
+				</li>
+				<li class="page-item next-page disabled">
+					<a class="page-link"><%=__('next')%> »</a>
+				</li>
+			</ul>
+		</nav>
+	</div>
 </div>
Index: src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 14281)
+++ src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 14282)
@@ -31,6 +31,8 @@
       'click  .switch': 'onSwitchClick',
       'click  .icon-edit': 'onEditClick',
       'click  .icon-bin': 'onDeleteClick',
+      'click #news-table .next-page': 'nextPage',
+      'click  #news-table .prev-page': 'previousPage'
     },
     language: null,
     categoryId: null,
@@ -38,6 +40,9 @@
     fadeInEffect: 'fadeIn',
     fadeOutEffect: 'fadeOut',
     article: null,
+    maxResult: 10,
+    page: 1,
+    pagesPerBlock: 3,
     initialize: function() {
       this.options.i18n = true;
       this._super();
@@ -62,10 +67,8 @@
     _refreshArticleList: function(resp) {
       this.collection.add(resp.articles);
     },
-    render: function(searchValue) {
-      this.undelegateEvents();
+    listArticleByLang: function() {
       var list;
-      var searchValue = (searchValue)?searchValue: "";
       // on modif selon notre besoin dans le tableau
       list = this.listToJson(this.currentList, this.lang);
       //pour le filtre category et recherche
@@ -73,16 +76,95 @@
           list = this.getFilteredList(list);
       // pour le sort    
       list = this.sortList(list);
-      if (list.length > 0 ) {
-          var params = _.extend({}, {content: list, searchValue : searchValue, current : this._current,currentlang : this.lang, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected, user: this.app.user}, this.addTemplateParams(this.collection, list));
+
+      return list;
+    },
+    totalPages: function() {
+      var allArticles = this.listArticleByLang();
+      
+      return Math.ceil(allArticles.length / this.maxResult);
+    },
+    nextPage: function(){    
+      listArticle = this.listArticleByLang();
+      var allPages = this.totalPages();
+      if (allPages === this.page) {
+
+        return false;
+      }
+      this.page ++;
+
+      this.render();
+    },
+    previousPage: function(){
+      if (this.page == 1) {
+
+        return false;
+      }
+      this.page --;
+      
+      this.render();
+    },
+    // limiter le nombre d'article à afficher
+    limitListArticle: function(list) {
+      var offset = (this.page - 1) * this.maxResult;
+      var limit = offset + this.maxResult;
+
+      return list.slice(offset, limit);
+    },
+    // pagination
+    renderPagination: function() {
+      this.$('#news-table .pagination-container').removeClass('hidden');
+      if (this.page > 1) {
+        this.$('.pagination .prev-page').removeClass('disabled');
+      }
+      if (this.totalPages() !== this.page) {
+        this.$('.pagination .next-page').removeClass('disabled');
+      }
+
+      var self = this;
+      var pagesContainer = this.$('#pages');
+      pagesContainer.empty();
+
+      // bloc actuel
+      var currentBlock = Math.floor((this.page - 1) / this.pagesPerBlock); // (ex: page = 4 => currentBlock = 1)
+      var startPage = currentBlock * this.pagesPerBlock + 1; // debut : page 4
+
+      var totaPage = this.totalPages();
+      var endPage = Math.min(startPage + this.pagesPerBlock - 1, totaPage); // fin: page 6
+
+      // boutons de pages du bloc actuel
+      for (var i = startPage; i <= endPage; i++) {
+        (function(pageNumber) {
+          var btn = $('<a class="page-link"></a>').text(pageNumber);
+      
+          if (pageNumber === self.page) {
+            btn.addClass('active');
+          }
+      
+          btn.on('click', function () {
+            self.page = pageNumber;
+            
+            self.render();
+          });
+      
+          pagesContainer.append(btn);
+        })(i);
+      }
+    },
+    render: function(searchValue) {
+      this.undelegateEvents();
+      var searchValue = (searchValue)?searchValue: "";
+      paginationlList = this.limitListArticle(this.listArticleByLang());
+      if (paginationlList.length > 0 ) {
+          var params = _.extend({}, {content: paginationlList, searchValue : searchValue, current : this._current,currentlang : this.lang, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected, user: this.app.user}, this.addTemplateParams(this.collection, paginationlList));
           this.$el.html(this._template(params));
       }
       else {
         if(searchValue){
-          var params = _.extend({}, {content: list, searchValue : searchValue, current : this._current,currentlang : this.lang, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected, user: this.app.user}, this.addTemplateParams(this.collection, list));
+          var params = _.extend({}, {content: paginationlList, searchValue : searchValue, current : this._current,currentlang : this.lang, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected, user: this.app.user}, this.addTemplateParams(this.collection, paginationlList));
           this.$el.html(this._template(params));
         }
-        else{var params = _.extend({}, {content: list, current : this._current,currentlang : this.lang, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected , canAddCat : (this.categoryId)?false : true}, this.addTemplateParams(this.collection, list));
+        else{var params = _.extend({}, {content: paginationlList, current : this._current,currentlang : this.lang, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected , canAddCat : (this.categoryId)?false : true}, this.addTemplateParams(this.collection, paginationlList));
           this.$el.html(this._emptyTemplate(params));
         }
       }
@@ -98,6 +180,9 @@
           $(this).css('cursor', 'not-allowed');
         }
       });
+      if (this.listArticleByLang().length > 10) {
+        this.renderPagination();
+      }
 
       this.delegateEvents();
       this.scrollables();
@@ -135,7 +220,8 @@
           active : (pageModel)?pageModel.active : false,
           content:list.content,
           state : list.state,
-          ispublier: list.ispublier
+          ispublier: list.ispublier,
+          urlFront: (pageModel) ? pageModel.attributes.base_url : ''
         }
         return json;
       });
Index: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 14281)
+++ src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 14282)
@@ -182,5 +182,7 @@
 	"uploadLink": "Importer les articles",
 	"uploadSuccess": "L'article a été importé avec succès",
 	"uploadError": "Erreur lors du téléchargement de l'image de l'article",
-	"editImage": "Retoucher l'image"
+	"editImage": "Retoucher l'image",
+	"prev": "Précédent",
+    "next": "Suivant"
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 14281)
+++ src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 14282)
@@ -182,5 +182,7 @@
 	"uploadLink": "Importer les articles",
 	"uploadSuccess": "L'article a été importé avec succès",
 	"uploadError": "Erreur lors du téléchargement de l'image de l'article",
-	"editImage": "Retoucher l'image"
+	"editImage": "Retoucher l'image",
+	"prev": "Précédent",
+    "next": "Suivant"
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/i18n.js	(révision 14281)
+++ src/js/JEditor/NewsPanel/nls/i18n.js	(révision 14282)
@@ -179,7 +179,9 @@
         "uploadLink": "Import articles",
         "uploadSuccess": "Article has been successfully imported",
         "uploadError": "Failed to upload the article image",
-        "editImage": "Edit picture"
+        "editImage": "Edit picture",
+        "prev": "Previous",
+        "next": "Next"
     },
     "fr-fr": true,
     "fr-ca": true
Index: src/js/JEditor/PagePanel/PagePanel.js
===================================================================
--- src/js/JEditor/PagePanel/PagePanel.js	(révision 14281)
+++ src/js/JEditor/PagePanel/PagePanel.js	(révision 14282)
@@ -70,13 +70,21 @@
 			'click #available-blocks-trigger' : 'showAvailableBlocks',
                         'click #show-zone-version': 'showZoneVersionsPanel',
 			'click .addPage,.empty-content-lang':'onAddPageClick',
-			'input #form-search-page input': 'searchPage',
+			'keydown #form-search-page input': 'searchPage',
 			'click #all-data-table td .icon-bin': '_onRemoveClick',
 			'click #all-data-table td .icon-edit': '_onEditClick',
 			'click #all-data-table td .switch': '_onSwitchClick',
-			'click #all-data-table th[data-column]': 'onSort'
+			'click #all-data-table th[data-column]': 'onSort',
+			'click .main-list-page .next-page': 'nextPage',
+      		'click .main-list-page .prev-page': 'previousPage',
+			'click #form-search-page .clear-btn': 'reset'
 		},
 		cssFile: __IDEO_INTEGRATION_PATH__ + "/build/css/page_panel.css",
+		page: 1,
+		maxResult: 10,
+		pagesPerBlock: 3,
+		isSearchActive: false,
+		lastSearch: null,
 
 		constructor : function() {
 			PanelView.call(this, {
@@ -145,6 +153,7 @@
 			this.listenTo(this.childViews.pageList, 'render', this._scrollbar);
 			this.listenTo(this.childViews.pageLpManager, "lp:toggle", this.render);
 			this.listenTo(this.childViews.pageList, "search:page", this._onSerch);
+			this.listenTo(this.childViews.pageList, "search:state", this._onSearchStateChange);
 			this.listenTo(this.childViews.pageList, "remove:page", this.render);
 			this.listenTo(this.pages, "change", this.render);
 			this.render();
@@ -224,6 +233,10 @@
 				$('#all-data-table tbody').append('<tr class="no-result-row"><td colspan="100%">'+translate('none_result')+'</td></tr>');
 			}
 		},
+		_onSearchStateChange: function(isSearchActive) {
+			this.isSearchActive = isSearchActive;
+			this.render();
+		},		
 		_onRemoveClick: function(event) {
 			this.childViews.pageList._onDeleteClick(event);
 		},
@@ -426,8 +439,11 @@
 			this.dom.window.scroll();
 		},
 		searchPage: function(event) {
-			var inputValue = event.target.value;
-			this.childViews.pageList.pageSearch(inputValue);
+			if (event.keyCode === 13) {
+				var inputValue = event.target.value;
+				this.lastSearch = inputValue;
+				this.childViews.pageList.pageSearch(inputValue);
+			}
 		},
 		/**
 		 * crée le rendu du panneau de droite
@@ -463,6 +479,111 @@
 			this.dom.pagePanel.rightSidebar = this.$('#item-config');
 			this.dom.pagePanel.addPageButtonDown = this.$('.dialog-view-trigger.addPage');
 		},
+
+		getFilteredPages: function() {
+			var bylangSupport = this.pageSupports.groupBy('lang');
+			var currentListSupport = (bylangSupport[this.currentLang.id] !== undefined) ? bylangSupport[this.currentLang.id]: [];
+			var page = this.childViews.pageList.currentList;
+			var isUserCanAccessLpS = this.app.user.can('access_lpsupport_page');
+
+			if(isUserCanAccessLpS && localStorage.getItem('lpaccess') == 'true') {
+				var page = page.concat(currentListSupport);
+			}
+			var filterPage = page.filter(function(p) {
+
+				return p.type !== 'news' && p.type !== 'template';
+			});
+		
+			return filterPage;
+		},
+
+		// limiter le nombre de page à afficher
+		limitListPage: function(list) {
+			var offset = (this.page - 1) * this.maxResult;
+			var limit = offset + this.maxResult;
+		
+			return list.slice(offset, limit);
+		},
+
+		totalPages: function() {
+			var allPages = this.getFilteredPages();
+			
+			return Math.ceil(allPages.length / this.maxResult);
+		},
+
+		nextPage: function(){    
+			listArticle = this.getFilteredPages();
+			var allPages = this.totalPages();
+			if (allPages === this.page) {
+		
+				return false;
+			}
+			this.page ++;
+		
+			this.render();
+		},
+		previousPage: function(){
+			if (this.page == 1) {
+		
+				return false;
+			}
+			this.page --;
+			
+			this.render();
+		},
+
+		// pagination
+		renderPagination: function() {
+			this.$('.main-list-page .pagination-container').removeClass('hidden');
+			if (this.page > 1) {
+			  this.$('.pagination .prev-page').removeClass('disabled');
+			}
+			if (this.totalPages() !== this.page) {
+			  this.$('.pagination .next-page').removeClass('disabled');
+			}
+	  
+			var self = this;
+			var pagesContainer = this.$('#page-number');
+			pagesContainer.empty();
+	  
+			// bloc actuel
+			var currentBlock = Math.floor((this.page - 1) / this.pagesPerBlock); // (ex: page = 4 => currentBlock = 1)
+			var startPage = currentBlock * this.pagesPerBlock + 1; // debut : page 4
+	  
+			var totaPage = this.totalPages();
+			var endPage = Math.min(startPage + this.pagesPerBlock - 1, totaPage); // fin: page 6
+	  
+			// boutons de pages du bloc actuel
+			for (var i = startPage; i <= endPage; i++) {
+			  (function(pageNumber) {
+				var btn = $('<a class="page-link"></a>').text(pageNumber);
+			
+				if (pageNumber === self.page) {
+				  btn.addClass('active');
+				}
+			
+				btn.on('click', function () {
+				  self.page = pageNumber;
+				  
+				  self.render();
+				});
+			
+				pagesContainer.append(btn);
+			  })(i);
+			}
+		},
+		reset: function() {
+			this.searchPage({
+				keyCode: 13,
+				target: { value: '' }
+			});
+            $('.page-search').val('');
+            this.lastSearch = null;
+            this.isSearchActive = false;
+
+			this.render();
+        },
+		
 		/**
 		 * actualise la vue
 		 */
@@ -469,21 +590,17 @@
 		
 		render : function() {
 			this.undelegateEvents();
-			var bylangSupport = this.pageSupports.groupBy('lang');
-			var currentListSupport = (bylangSupport[this.currentLang.id] !== undefined) ? bylangSupport[this.currentLang.id]: [];
-			var page = this.childViews.pageList.currentList;
-			var isUserCanAccessLpS = this.app.user.can('access_lpsupport_page');
 			var isUserCanAccessLayout = this.app.user.can('access_layout');
 			var isUserCanAccessRmPage = this.app.user.can('delete_page');
+			var fullList = this.getFilteredPages();
+			var paginationList = (this.isSearchActive) ? fullList : this.limitListPage(fullList);
 
-			if(isUserCanAccessLpS && localStorage.getItem('lpaccess') == 'true') {
-				var page = page.concat(currentListSupport);
-			}
+
 			this.$el.html(this._template({
 				empty: this._byLang[this.currentLang.id].length === 0,
 				noneSelected: !this.currentPage,
 				canAddPage: this.user.can("create_page"),
-				content: page,
+				content: paginationList,
 				sortAsc: this.childViews.pageList.sortAsc,
 				sortedBy: this.childViews.pageList.sortedBy,
 				canDelete: isUserCanAccessRmPage,
@@ -502,6 +619,13 @@
 
 			// init app intro (tutorial)
 			this.appIntro();
+			if (!this.isSearchActive && this.getFilteredPages().length > 10) {
+				this.renderPagination();
+			}
+			if (this.lastSearch) {
+				$('.page-search').val(this.lastSearch);
+				$('.clear-btn').removeClass('hidden');
+			}
 
 			this.delegateEvents();
 			this.dom.window.scroll();
@@ -589,6 +713,12 @@
 				this.childViews.pageList.lang = lang.id;
 				//this.childViews.addPageView.setLanguage(lang);
 				this.listenTo(this.childViews.pageList, Events.ViewEvents.HIDE, function() {
+					this.isSearchActive = false;
+					this.lastSearch = null;
+					this.searchPage({
+						keyCode: 13,
+						target: { value: '' }
+					});
 					this.render();
 					this.childViews.pageList.render();
 					this.childViews.pageList.show();
Index: src/js/JEditor/PagePanel/Templates/filterPage.html
===================================================================
--- src/js/JEditor/PagePanel/Templates/filterPage.html	(révision 14281)
+++ src/js/JEditor/PagePanel/Templates/filterPage.html	(révision 14282)
@@ -1,6 +1,7 @@
 <form id="form-search-page">
     <span class="search-page-selector">
         <input type="text" class="page-search" placeholder="<%= __('filter')%>" value="<%= value %>" />
+        <button type="button" class="clear-btn hidden">&times;</button>
     </span>
 </form>
 <br>
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Templates/pagePanel.html
===================================================================
--- src/js/JEditor/PagePanel/Templates/pagePanel.html	(révision 14281)
+++ src/js/JEditor/PagePanel/Templates/pagePanel.html	(révision 14282)
@@ -53,7 +53,7 @@
             <div class="page-list">
                 <table id="all-data-table">
                     <thead>
-                        <tr>
+                        <tr class="firt-row">
                             <th class="page-icon"><%=__('icon')%></th>
                             <th data-column="1" data-order="desc" class="title-page">
                                 <%=__('title')%>
@@ -71,56 +71,70 @@
                             </tr>
                         <% } else { %>
                             <% _.each(content, function(page) { %>
-                                <% if (page.type !== 'news' && page.type !== 'template') { %>
-                                    <tr data-cid="<%= page.cid %>" class="data-tr <%= !page.active ? 'disabled' : ''%>">
-                                        <td>
-                                            <% var icons = {
-                                                1: 'icon-home',
-                                                2: 'icon-mail',
-                                                3: 'icon-legal',
-                                                4: 'icon-site-map',
-                                                5: 'icon-star',
-                                                6: 'icon-newsletter-icon',
-                                                7: 'icon-interest-point',
-                                                0: 'icon-page',
-                                                8: 'icon-landing-page'
-                                            };
-                                                if (page.locked) {
-                                                    var iconClass = 'icon-lock';
-                                                } else {
-                                                    var iconClass = icons[page.home] || 'icon-page';    
-                                                }
-                                            %>
-                                            <span class="icon <%= iconClass %>"></span>
-                                            <% if (page.home === 0) { %>
-                                                <span class="type-name"><%= __('contentPage') %></span>
-                                            <% } else if (page.home === 7) { %>
-                                                <span class="type-name"><%= __('pagelp') %></span>
-                                            <% } else if (page.home === 8) { %>
-                                                <span class="type-name"><%= __('landingPage') %></span>
-                                            <% } else { %>
-                                                <span class="type-name"><%= page.name %></span>
-                                            <% } %>                                            
-                                        </td>
-                                        <td class="title"><%= page.name %></td>
-                                        <td>
-                                            <% if (page.type === 'content') { %>
-                                                <span class="switch" data-cid="<%= page.cid %>">
-                                                    <span></span>
-                                                </span>
-                                            <% } %>
-                                            <span class="icon-edit" data-cid="<%= page.cid %>"></span>
-                                            <% if (canDelete && page.type === 'content') { %>
-                                                <span class="icon-bin" data-model="<%= page.cid %>"></span>
-                                            <% } %>
-                                        </td>
-                                    </tr>
-                                <% } %>
+                                <tr data-cid="<%= page.cid %>" class="data-tr <%= !page.active ? 'disabled' : ''%>">
+                                    <td>
+                                        <% var icons = {
+                                            1: 'icon-home',
+                                            2: 'icon-mail',
+                                            3: 'icon-legal',
+                                            4: 'icon-site-map',
+                                            5: 'icon-star',
+                                            6: 'icon-newsletter-icon',
+                                            7: 'icon-interest-point',
+                                            0: 'icon-page',
+                                            8: 'icon-landing-page'
+                                        };
+                                            if (page.locked) {
+                                                var iconClass = 'icon-lock';
+                                            } else {
+                                                var iconClass = icons[page.home] || 'icon-page';    
+                                            }
+                                        %>
+                                        <span class="icon <%= iconClass %>"></span>
+                                        <% if (page.home === 0) { %>
+                                            <span class="type-name"><%= __('contentPage') %></span>
+                                        <% } else if (page.home === 7) { %>
+                                            <span class="type-name"><%= __('pagelp') %></span>
+                                        <% } else if (page.home === 8) { %>
+                                            <span class="type-name"><%= __('landingPage') %></span>
+                                        <% } else { %>
+                                            <span class="type-name"><%= page.name %></span>
+                                        <% } %>                                            
+                                    </td>
+                                    <td class="title">
+                                        <a href="//<%=page.attributes.base_url%>" target="_blank"><%= page.name %></a>
+                                    </td>
+                                    <td>
+                                        <% if (page.type === 'content') { %>
+                                            <span class="switch" data-cid="<%= page.cid %>">
+                                                <span></span>
+                                            </span>
+                                        <% } %>
+                                        <span class="icon-edit" data-cid="<%= page.cid %>"></span>
+                                        <% if (canDelete && page.type === 'content') { %>
+                                            <span class="icon-bin" data-model="<%= page.cid %>"></span>
+                                        <% } %>
+                                    </td>
+                                </tr>
                             <% }); %>
                         <% } %>
                     </tbody>
                 </table>
             </div>
+            <div class="pagination-container hidden">
+                <nav>
+                    <ul class="pagination">
+                        <li class="page-item prev-page disabled">
+                            <a class="page-link">« <%=__('prev')%></a>
+                        </li>
+                        <li id="page-number" class="page-item">
+                        </li>
+                        <li class="page-item next-page disabled">
+                            <a class="page-link"><%=__('next')%> »</a>
+                        </li>
+                    </ul>
+                </nav>
+            </div>
         </div>
     </div>
 
Index: src/js/JEditor/PagePanel/Views/PageCollectionView.js
===================================================================
--- src/js/JEditor/PagePanel/Views/PageCollectionView.js	(révision 14281)
+++ src/js/JEditor/PagePanel/Views/PageCollectionView.js	(révision 14282)
@@ -46,6 +46,7 @@
         this.filter({type:'content'});
       this.setGroupOrder(['content', 'lp','template']);
       this.listenTo(this.collection,"change",this.render);
+      this.listenTo(this, "search:page", this._onSerchInLeftList);
     },
     toggleLock:function (event) {
         //console.log(event);
@@ -78,6 +79,18 @@
         }
         return false;
     },
+    _onSerchInLeftList: function(key) {
+      if(key) {
+        var list = this.$el.find('.wrapper li');
+
+        list.each(function() {
+          var listText = $(this).find('a').text().toLowerCase();
+          var matchs = listText.includes(key.trim().toLowerCase());
+          
+          $(this).toggle(matchs);
+        }); 
+      }
+    },
     _onCheckBoxChange: function(event) {
       var $target = $(event.currentTarget);
       var cid = $target.data('cid');
@@ -259,35 +272,12 @@
             break;
       }
     },
-    render: function(search) {
+    render: function() {
       this._super();
       this.hideNews();
       if(!this.app.user.can('access_layout')) {
         this.$('.wrapper.template').html('');
       }
-      if (search) {
-        // tout les pages typés
-        var listPage = ['home', 'contact', 'legal', 'plan', 'avis', 'newsletter'];
-        
-        // cacher les page typés s'il y a de mot clé  
-        listPage.forEach(function(list){
-          this.$('.' + list).hide();
-        });
-
-        var regex = new RegExp(search, 'g');
-
-        var anotherTypedPage = this.addTemplateParams(this.collection);
-
-        listPage.forEach(function(list) {
-            var page = anotherTypedPage[list];
-            if (page && page.name) {
-                var pageName = page.name.trim().toLowerCase();
-                if (regex.test(pageName)) {
-                  this.$('.' + list).show();
-                }
-            }
-        });
-      }
       this.scrollables();
       return this;
     },
@@ -298,7 +288,7 @@
 
         return regex.test(pageName);
       };
-      this.render(keyword);
+      this.trigger("search:state", keyword.trim().length > 0);
       this.trigger("search:page", keyword);
     },
     
Index: src/js/JEditor/PagePanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/nls/fr-ca/i18n.js	(révision 14281)
+++ src/js/JEditor/PagePanel/nls/fr-ca/i18n.js	(révision 14282)
@@ -138,5 +138,7 @@
     "allPages": "Toutes les pages",
     "icon": "Type",
     "action": "Action",
-    "contentPage": "Page de contenu"
+    "contentPage": "Page de contenu",
+    "prev": "Précédent",
+    "next": "Suivant"
 });
Index: src/js/JEditor/PagePanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/nls/fr-fr/i18n.js	(révision 14281)
+++ src/js/JEditor/PagePanel/nls/fr-fr/i18n.js	(révision 14282)
@@ -144,5 +144,7 @@
     "allPages": "Toutes les pages",
     "icon": "Type",
     "action": "Action",
-    "contentPage": "Page de contenu"
+    "contentPage": "Page de contenu",
+    "prev": "Précédent",
+    "next": "Suivant"
 });
Index: src/js/JEditor/PagePanel/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/nls/i18n.js	(révision 14281)
+++ src/js/JEditor/PagePanel/nls/i18n.js	(révision 14282)
@@ -142,7 +142,9 @@
         "allPages": "All pages",
         "icon": "Type",
         "action": "Action",
-        "contentPage": "Content page"
+        "contentPage": "Content page",
+        "prev": "Previous",
+        "next": "Next"
     },
     "fr-fr": true, "fr-ca":true
 });
Index: src/less/imports/news_panel/main.less
===================================================================
--- src/less/imports/news_panel/main.less	(révision 14281)
+++ src/less/imports/news_panel/main.less	(révision 14282)
@@ -1085,6 +1085,8 @@
 		display: flex;
 		align-items: center;
 		justify-content: space-evenly;
+		width: 41%;
+		float: right;
 	}
 	.search-news{
 		background-color: #fff;
@@ -1104,21 +1106,23 @@
 	#data-table {
 		border-collapse: collapse;
 		width: 100%;
+		.title a {
+			text-decoration: none;
+			color: #000;
+		}
+		.firt-row {
+			background-color: #f2f2f2;
+		}
+		.col {
+			min-height: 60px;
+		}
 	 	th, td {
-		border-top: 1px solid #ddd;
 		padding: 15px 8px;
 		text-align: left;
 		}
-		
-		tr td:first-child, tr th:first-child {
-			border-left: 1px solid #ddd;
-		}
-		tr:last-child td {
+		tr {
 			border-bottom: 1px solid #ddd;
 		  }
-		tr td:last-child , tr th:last-child {
-			border-right: 1px solid #ddd;
-		}
 		th {
 			font-weight: 500;
 			cursor: pointer;
Index: src/less/imports/pagination.less
===================================================================
--- src/less/imports/pagination.less	(nonexistent)
+++ src/less/imports/pagination.less	(révision 14282)
@@ -0,0 +1,40 @@
+.pagination-container {
+  margin: 20px 0;
+  display: flex;
+  justify-content: center;
+}
+
+.pagination {
+  display: flex;
+}
+
+.page-item {
+  margin: 0 3px;
+  cursor: pointer;
+}
+
+.page-link {
+  color: #007bff;
+  border: 1px solid #dee2e6;
+  padding: 8px 12px;
+}
+
+.page-link:hover {
+  background-color: #e9ecef;
+  color: #0056b3;
+}
+
+.page-item.disabled .page-link {
+  color: #6c757d;
+  background-color: #f8f9fa;
+  border-color: #dee2e6;
+  cursor: default;
+}
+
+.page-item .page-link.active {
+  color: #fff;
+  background-color: #007bff;
+  border-color: #007bff;
+  cursor: default;
+}
+  
\ No newline at end of file
Index: src/less/main.less
===================================================================
--- src/less/main.less	(révision 14281)
+++ src/less/main.less	(révision 14282)
@@ -44,6 +44,7 @@
 @import "./imports/common/queries/height-based/main.less";
 @import "./imports/common/queries/width-based/main.less";
 @import "./imports/ckeditor_table_modeles.less";
+@import "./imports/pagination.less";
 
 
 /* ==========================================================================
@@ -2996,6 +2997,14 @@
 .page-search::placeholder {
   color: #fff;
 }
+.clear-btn {
+  cursor: pointer;
+  background: 0 0;
+  border: none;
+  font-size: 16px;
+  margin-left: -35px;
+  color: #fff;
+}
 .data-tr {
   .switch {
     display: inline-block;
@@ -3031,6 +3040,22 @@
   }
 }
 #all-data-table {
+  width: 100%;
+  border-collapse: collapse;
+  .title a {
+    text-decoration: none;
+    color: var(--fg-color)!important;
+  }
+  .firt-row {
+    background-color: #f2f2f2;
+  }
+  .no-result {
+    text-align: center;
+  }
+  th, td {
+    padding: 15px 8px;
+    text-align: center;
+  }
   .sort-icon {
     position: relative;
     margin-left: 5px;
@@ -3053,36 +3078,14 @@
       opacity: 0.8;
     }
   }
-  .title-page, 
-  .action-page, 
-  .page-icon {
+  th {
     font-weight: 500;
-    font-size: 22px;
-    text-align: left;
-    border-top: 1px solid #ddd;
-    padding: 15px 8px;
-  }
-  .title-page {
     cursor: pointer;
+    font-size: 1.2em;
   }
-  .action-page, 
-  .page-icon {
-    text-align: center;
-  }
-  th:first-child, td:first-child {
-    border-left: 1px solid #ddd;
-    text-align: center;
-  }
-  th:last-child, td:last-child {
-    border-right: 1px solid #ddd;
-    text-align: center;
-  }
   tr {
     border-bottom: 1px solid #ddd;
   }
-  td {
-    padding: 25px 8px;
-  }
   .type-name {
     font-size: 12px;
   }
@@ -3100,10 +3103,3 @@
   display: flex;
   justify-content: center;
 }
-#all-data-table {
-  width: 100%;
-  border-collapse: collapse;
-}
-#all-data-table .no-result {
-  text-align: center;
-}
