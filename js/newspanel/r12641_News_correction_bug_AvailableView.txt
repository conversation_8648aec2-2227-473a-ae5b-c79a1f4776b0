Revision: r12641
Date: 2024-07-29 14:25:50 +0300 (lts 29 Jol 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: correction bug AvailableView 

## Files changed

## Full metadata
------------------------------------------------------------------------
r12641 | sraz<PERSON><PERSON><PERSON>oa | 2024-07-29 14:25:50 +0300 (lts 29 Jol 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/AvailableView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/PagePanel.js

News: correction bug AvailableView 
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12640)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12641)
@@ -21,7 +21,6 @@
     "JEditor/NewsPanel/Views/AvailableView",
     "JEditor/NewsPanel/Views/PublishConfigView",
     "JEditor/NewsPanel/Articles/Versions/Views/VersionsCollectionView",
-    "JEditor/NewsPanel/Articles/Blocks",
     "i18n!./nls/i18n",
     "jqueryPlugins/affix",
     "ckeditor"
@@ -49,7 +48,6 @@
             AvailableView,
             PublishConfigView,
             VersionsCollectionView,
-            Blocks,
             translate
             ) {
             var NewsPanel = PanelView.extend(
Index: src/js/JEditor/NewsPanel/Views/AvailableView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/AvailableView.js	(révision 12640)
+++ src/js/JEditor/NewsPanel/Views/AvailableView.js	(révision 12641)
@@ -20,11 +20,6 @@
         Utils,
         translate
         ) {
-            if (!Blocks) {
-                console.error("Le module 'Blocks' est undefined. Vérifie le chemin et la définition du module.");
-            } else {
-                console.log("Module 'Blocks' chargé avec succès.");
-            }
     var /**
      * La vue des blocs disponibles
      * @class AvailableView
@@ -60,6 +55,19 @@
                                     delete this.options.newsPanel;
                                     this.modelLib = {};
                                     this._super();
+                                     // Vérifiez si Blocks est indéfini
+                                     if (typeof Blocks === 'undefined') {
+                                        // Chargez le module Blocks conditionnellement
+                                        var self = this;
+                                        require(["JEditor/PagePanel/Contents/Blocks/Blocks"], function(LoadedBlocks) {
+                                            Blocks = LoadedBlocks;
+                                            self.continueInitialization(); // Continue l'initialisation
+                                        });
+                                    } else {
+                                        this.continueInitialization();
+                                    }
+                                },
+                                continueInitialization: function () {
                                     this.checkMissingBlocks();
                                     for (var category in this.blockOrder) {
                                         if(category==='ignored')
Index: src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js	(révision 12640)
+++ src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js	(révision 12641)
@@ -20,11 +20,7 @@
         Utils,
         translate
         ) {
-            if (!Blocks) {
-                console.error("Le module 'Blocks' est undefined. Vérifie le chemin et la définition du module.");
-            } else {
-                console.log("Module 'Blocks' chargé avec succès.");
-            }
+
     var /**
      * La vue des blocs disponibles
      * @class AvailableView
@@ -60,6 +56,19 @@
                                     delete this.options.pagePanel;
                                     this.modelLib = {};
                                     this._super();
+                                    // Vérifiez si Blocks est indéfini
+                                    if (typeof Blocks === 'undefined') {
+                                        // Chargez le module Blocks conditionnellement
+                                        var self = this;
+                                        require(["JEditor/NewsPanel/Articles/Blocks"], function(LoadedBlocks) {
+                                            Blocks = LoadedBlocks;
+                                            self.continueInitialization(); // Continue l'initialisation
+                                        });
+                                    } else {
+                                        this.continueInitialization();
+                                    }
+                                },
+                                continueInitialization: function () {
                                     this.checkMissingBlocks();
                                     for (var category in this.blockOrder) {
                                         if(category==='ignored')
Index: src/js/JEditor/PagePanel/PagePanel.js
===================================================================
--- src/js/JEditor/PagePanel/PagePanel.js	(révision 12640)
+++ src/js/JEditor/PagePanel/PagePanel.js	(révision 12641)
@@ -17,11 +17,10 @@
 		"collection!JEditor/PagePanel/ContentModel/ContentModelCollection",
         "JEditor/PagePanel/Contents/Zones/Versions/Views/VersionsCollectionView",
 		"JEditor/PagePanel/Views/PageLpManagerView",
-		"JEditor/PagePanel/Contents/Blocks/Blocks",
 		"i18n!./nls/i18n",
 		// not in params
 		"owlCarousel",
-		"jqueryPlugins/affix" ], function($, _, pagePanel, Events, PanelView, RightPanelView,  PageCollectionView, PageCollection, PageSupportCollection, PageListManagerView, AvailableView, AddPageView, LayoutCollection, PageView, LanguagesDropDown, ContentModelCollection, VersionsCollectionView, PageLpManagerView, Blocks, translate) {
+		"jqueryPlugins/affix" ], function($, _, pagePanel, Events, PanelView, RightPanelView,  PageCollectionView, PageCollection, PageSupportCollection, PageListManagerView, AvailableView, AddPageView, LayoutCollection, PageView, LanguagesDropDown, ContentModelCollection, VersionsCollectionView, PageLpManagerView, translate) {
 	 /**
 		 * not main Vue du Panneau de page, gère les vues de page (nom, etc),
 		 * langues, panneaux latéraux
