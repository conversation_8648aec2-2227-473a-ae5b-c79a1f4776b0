Revision: r12579
Date: 2024-07-10 16:25:46 +0300 (lrb 10 Jol 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: ajout preview

## Files changed

## Full metadata
------------------------------------------------------------------------
r12579 | srazana<PERSON>lisoa | 2024-07-10 16:25:46 +0300 (lrb 10 Jol 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Articles/Versions/Views/VersionView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/preview.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/NewsEditorView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/PagePreview.js

News: ajout preview
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Templates/preview.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/preview.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Templates/preview.html	(révision 12579)
@@ -0,0 +1,62 @@
+<div class="full-width sub-menu">
+
+    <!-- RETURN -->
+
+    <div class="block-button left">
+        <a href="structure.php" class="button return">
+            <span class="ico">
+                <?php include('./template/svg/return.svg'); ?>
+            </span>
+            <span class="txt"><%= __("closePreview")%></span>
+        </a>
+        <a href="#" class="button save-version">
+            <span class="ico">
+            </span>
+            <span class="txt"><%= __("saveVersion")%></span>
+        </a>
+    </div>
+
+
+
+    <!-- SCREEN -->
+    <div class="wrap screen-wrap">
+        <div class="section-screen">
+            <div class="block-screen">
+                <span class="screen s-desktop active icon-desktop" data-size="xlarge" data-width="1441" data-height="887"></span>
+            </div>
+            <div class="block-screen">
+                <span class="screen s-laptop icon-laptop" data-size="large" data-width="1035" data-height="774"></span>
+            </div>
+            <div class="block-screen">
+                <span class="screen s-medium icon-tablet" data-size="medium" data-width="850" data-height="1260"></span>
+            </div>   
+            <div class="block-screen">
+                <span class="screen s-mobile icon-mobile" data-size="small" data-width="330" data-height="690"></span>
+            </div>
+
+
+            <div class="block-text left text-screen">
+                <p class="ps-txt show"><%= __("resizeByDevice")%></p>
+                <p class="ps-txt s-mobile" data-size="small"><%= __("deviceMobile")%></p>
+                <p class="ps-txt s-medium" data-size="medium"><%= __("deviceTablet")%></p>
+                <p class="ps-txt s-large" data-size="large"><%= __("deviceDesktop")%></p>
+                <p class="ps-txt s-xlarge"data-size="xlarge"><%= __("deviceLargeDesktop")%></p>
+            </div>
+            <a href="#" class="button fullscreen" title="<%= __("toggleFullscreen")%>">
+                <span class="icon-fullscreen">
+
+                </span>
+            </a>
+        </div>
+
+    </div>
+
+</div>
+<form target="preview" action="<%= apiPath %>/news/preview" method="post">
+    <input type="hidden" name="type"/>
+    <input type="hidden" name="content"/>
+</form>
+<div class="wrapper">
+    <iframe name="preview" id="preview" class="iframe-live xlarge" frameborder="0" src="<%= apiPath %>/news/preview" data-width="1441" data-height="887"></iframe>
+    <!--span class="loader icon-three"></span-->
+</div>

Property changes on: src/js/JEditor/NewsPanel/Templates/preview.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/NewsPanel/Views/PagePreview.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/PagePreview.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Views/PagePreview.js	(révision 12579)
@@ -0,0 +1,119 @@
+define([
+    "jquery",
+    "JEditor/Commons/Ancestors/Views/View",
+    "Modernizr",
+    "JEditor/Commons/Utils",
+    "text!../Templates/preview.html",
+    "i18n!../nls/i18n"
+],
+        function($, View, Modernizr, Utils, preview, translate) {
+
+            var PagePreview = View.extend({
+                attributes: {
+                    id: "page-preview"
+                },
+                events: {
+                    "click .screen": "onScreenClick",
+                    "iframe load": "onIframeLoad",
+                    "click a.fullscreen": "switchScale",
+                    'click .return': 'hide'
+                },
+                initialize: function() {
+                    this._super();
+                    this.rendered = false;
+                    this._template = this.buildTemplate(preview, translate);
+                },
+                scale: function(fitToWindow) {
+                    if (Modernizr.csstransform)
+                        return;
+                    var marginH, marginV;
+                    var iframe = this.dom[this.cid].iframe;
+                    var loaderTransform={};
+                    var scaleProp = Utils.getPrefixedCss('transform');
+                    var iframeH = iframe.data('height');
+                    var iframeW = iframe.data('width');
+                    var parentH = iframe.parent().height();
+                    var parentW = iframe.parent().width();
+                    var scale = 1;
+                    var iframeTransform = {};
+                    if (fitToWindow !== false)
+                        scale = Math.min(parentH / (iframeH * 1.2), parentW / (iframeW * 1.2));
+                    if (scale > 1)
+                        scale = 1;
+                    else
+                        iframe.parent().addClass('no-scroll');
+                    marginH = (parentW - iframeW * scale) / 2;
+                    marginV = (parentH - iframeH * scale) / 2;
+
+                    iframeTransform[scaleProp] = 'scale(' + scale + ')';
+                    iframeTransform[scaleProp + '-origin'] = '0 0';
+                    iframeTransform.margin = fitToWindow !== false ? marginV + 'px ' + marginH + 'px' : '20px ' + marginH + 'px';
+                    iframe.css(iframeTransform);
+                    iframe.data('scale', scale);
+                    
+
+                },
+                switchScale: function() {
+                    var iframe = this.dom[this.cid].iframe;
+                    var scale = parseFloat(iframe.data('scale'), 10);
+                    if (scale < 1) {
+                        this.scale(false);
+                        this.dom[this.cid].iframe.parent().removeClass('no-scroll');
+                    } else
+                        this.scale(true);
+                    return false;
+                },
+                onScreenClick: function(event) {
+                    //on supprime la classe "active" sur tous les boutons
+                    this.dom[this.cid].screenButton.removeClass('active');
+                    //pareil pour les ps-txt
+                    this.dom[this.cid].psTxt.removeClass('show');
+                    //puis on récupère les attributs utiles du bouton cliqué
+                    var clickTarget = $(event.currentTarget);
+                    var size = clickTarget.data('size');
+                    var width = clickTarget.data('width');
+                    var height = clickTarget.data('height');
+                    clickTarget.addClass('active');
+                    this.dom[this.cid].iframe.data({width: width, height: height});
+                    this.scale();
+                    this.dom[this.cid].iframe.attr('class', 'iframe-live ' + size);
+                    this.dom[this.cid].psTxt.filter('[data-size="' + size + '"]').addClass('show');
+                },
+                onIframeLoad: function(event) {
+                    console.log('iframeloaded');
+                },
+                render: function() {
+                    this.undelegateEvents();
+                    this.$el.empty();
+                    this.$el.html(this._template({apiPath:__IDEO_API_PATH__}));
+                    this.dom[this.cid].iframe = this.$('iframe[name="preview"]');
+                    this.dom[this.cid].form = this.$('form');
+                    this.dom[this.cid].pageInput = this.$('input[name="content"]');;
+                    this.dom[this.cid].typeInput = this.$('input[name="type"]');;
+                    this.dom[this.cid].screenButton = this.$('.screen');
+                    this.dom[this.cid].psTxt = this.$('.ps-txt');
+                    //this.dom[this.cid].loader = this.$('.icon-three.loader')
+                    this.rendered = true;
+                    this.$el.addClass('visible');
+                    this.delegateEvents();
+                    return this;
+                },
+                show: function(type, content) {
+                    this.dom.html.addClass('no-scroll');
+                    this.render();
+                    this._super();
+                    this.scale();
+                    this.dom[this.cid].pageInput.val(JSON.stringify(content.toJSON()));
+                    this.dom[this.cid].typeInput.val(type.trim()); 
+                    this.dom[this.cid].form.attr('target', 'preview');
+                    this.dom[this.cid].form.submit();
+                    this.$el.addClass('visible');
+                },
+                hide: function() {
+                    this.dom.html.removeClass('no-scroll');
+                    this._super();
+                    return false;
+                }
+            });
+            return PagePreview;
+        });

Property changes on: src/js/JEditor/NewsPanel/Views/PagePreview.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/NewsPanel/Articles/Versions/Views/VersionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Versions/Views/VersionView.js	(révision 12578)
+++ src/js/JEditor/NewsPanel/Articles/Versions/Views/VersionView.js	(révision 12579)
@@ -70,7 +70,7 @@
             this.model.fetch({
                 success: function(res){
                     var resJson = res.toJSON();
-                    var articleEditorView = that.newsPanel.childViews.newsEditorView.articleEditorView;
+                    var articleEditorView = that.newsPanel.childViews.newsEditorView;
                     articleEditorView.selectedZoneVersion = new ZoneModel(resJson.content);
                     articleEditorView.usePreviousVersion();
                 }
@@ -160,7 +160,7 @@
             this.model.fetch({
                 success: function(res){
                     var resJson = res.toJSON();
-                    that.newsPanel.childViews.newsEditorView.articleEditorView.preview(null, new ZoneModel(resJson.zoneContent));
+                    that.newsPanel.childViews.newsEditorView.preview(null, new ZoneModel(resJson.zoneContent));
                 }
             } );
             
Index: src/js/JEditor/NewsPanel/Views/NewsEditorView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 12578)
+++ src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 12579)
@@ -12,10 +12,9 @@
     "JEditor/NewsPanel/Views/CategorieAddView",
     "JEditor/NewsPanel/Views/ConfigCategorieView",
     "JEditor/NewsPanel/Views/ConfigArticleView",
-    "JEditor/PagePanel/Views/PagePreview",
+    "JEditor/NewsPanel/Views/PagePreview",
     "JEditor/NewsPanel/Views/ArticleEditorView",
     "i18n!../nls/i18n",
-    "moment",
     //not in params
    
     "jqueryPlugins/affix"
@@ -34,8 +33,7 @@
      ConfigArticleView, 
      PagePreview,
      ArticleEditorView,
-     translate,
-     moment
+     translate
      ) {
    
     var NewsEditorView = BabblerView.extend({
@@ -52,6 +50,7 @@
         fadeOutEffect: "fadeOut",
         fadeInEffect: "fadeIn",
         language: null,
+        selectedZoneVersion: null,
         initialize: function() {
             this.options.i18n = true;
             this._template = this.buildTemplate(template, translate);
@@ -74,6 +73,7 @@
         renderAddCategorie :function (){
             this.removeChildren();
             this.addCategorieView = new CategorieAddView({
+                isform : true,
                 languageList : this.options.languages,
                 language : this.options.currentLang,
                 collection: this.options.categorieCollection
@@ -100,8 +100,9 @@
 
          renderCategorieView :function (){
             this.removeChildren();
-            this.options.usepreview = true;
+            this.options.usepreview = (this.model.id != 'uncategorized')?true:false;
             this.addCategorieView = new CategorieAddView({
+                isform : false,
                 languageList : this.options.languages,
                 language : this.options.currentLang,
                 categorie : this.model,
@@ -108,8 +109,13 @@
                 collection: this.categories
             });
             this.render();
-            this.categoryView.prepend(this.addCategorieView.render().el);
-            
+            if (this.model.id != 'uncategorized') {
+                this.categoryView.prepend(this.addCategorieView.render().el);
+                this.listenTo(this.categories, Events.BackboneEvents.REMOVE, _.bind(function(){
+                    self.newsPanel.currentCategorie = null;
+                }));
+            }
+            else this.$('.config #params').hide();
              this.articlesListView = new ArticlesCollectionView({
                 collection : this.articles,
                 language : this.currentLang,
@@ -131,7 +137,6 @@
                 newsPanel : this,
                 model : article,
             });
-           // this.articleEditorView.load();
             this.render();
             this.articleView.append(this.articleEditorView.render().el);
             this.$('.config #params').hide();
@@ -151,25 +156,6 @@
             this.articleEditorView.load();
             this.render();
             this.articleView.append(this.articleEditorView.render().el);
-            var page = this.model.getPageModel() ;
-            var openArticle = (page)?'<a href="//'+ page.attributes.base_url +'" class="btn btn-primary btn-lg btn-block" target="_blank">Ouvrir' : "";
-            this.$('.open-article').html( openArticle );
-            var state = this.model.content.state;
-            switch (state) {
-                case 1:
-                    var date = moment(this.model.programmingDate.date).format("DD/MM/YY");
-                    var stateHtml = '<span class="btn bleu">Programmé '+date+'</span>'
-                    break;
-                case 2:
-                    var stateHtml = '<span class="btn green">Publié</span>'
-                    break;
-                default:
-                    var stateHtml = '<span class="btn orange">Brouillon</span>'
-                    break;
-                
-            }
-            this.$('.article-state').html( stateHtml );
-
          },
         render: function() {
             this.undelegateEvents();
@@ -184,8 +170,21 @@
             this.$(".message").hide();
             this.delegateEvents();
 
+            if (this.pagePreview) {
+                this.pagePreview.remove();
+                this.pagePreview = new PagePreview();
+                this.$el.append(this.pagePreview.el);
+            }
+            this.$('header').affix({
+                offset: {
+                    top: this.checkAffixTop
+                }
+            });
             return this;
         },
+        checkAffixTop:function(){
+			return (document.documentElement.clientHeight < 866 ? 90 : 230);
+		},
         _onParamsClick: function (){
             if (this.model) {
                 this.edit()
@@ -194,6 +193,56 @@
                 this.app.currentPanel.showGlobalConfigView();
             }
         },
+        preview: function (e, zoneVersion) {
+            var page = this.model,
+             type = (page instanceof Article)? 'article' : 'category';
+
+            if (type === 'article' && zoneVersion) {
+                page.content.content = zoneVersion ;
+            }
+            this.pagePreview.show(type, page);
+
+            if (this.app.user.can("restore_zone")) {
+                if (zoneVersion) {
+                    this.selectedZoneVersion = zoneVersion;
+                    this.pagePreview.$('.save-version').show();
+                }else {
+                    this.pagePreview.$('.save-version').hide();
+                }
+            } else {
+                this.pagePreview.$('.save-version').hide();
+            }
+
+            return false;
+        },
+         /**
+         * restauration de page avec la zone selectionnée
+         * 
+         * @param {jQuery.Events} e L'évenement jQuery Déclencheur
+         */
+         usePreviousVersion: function(e){
+
+            if (e) { e.preventDefault(); e.stopPropagation(); }
+
+            if (this.app.user.can("restore_zone")) {
+
+                this.dom.html.removeClass('no-scroll');
+                this.newsPanel.rightPanelView.hidePanel();
+
+                this.setZone(this.selectedZoneVersion);
+
+                var that = this;
+                this.listenToOnce(this.currentZone, Events.BackboneEvents.SYNC, function () {
+                    that.render();
+                    that.notify({
+                        title: translate("saveAction"),
+                        message: translate("previousVersionSuccesful")
+                    });
+                });
+
+                this.model.save();
+            }
+        },
         /**
          * Déclenche l'édition de l'élément (options)
          */
