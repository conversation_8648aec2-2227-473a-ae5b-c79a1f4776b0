Revision: r12510
Date: 2024-06-28 15:53:44 +0300 (zom 28 Jon 2024) 
Author: jpmaret 

## Commit message
Merging dev & integration branches from r12484 tu dev_news & integration_news up to head:r12509

## Files changed

## Full metadata
------------------------------------------------------------------------
r12510 | jpmaret | 2024-06-28 15:53:44 +0300 (zom 28 Jon 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/dev_news
   M /branches/ideo3_v2/dev_news/#librairies/module/GestionContenuUtil/src/GestionContenuUtil/Core/HtmlTagHandlers/CoordsTagHandler.php
   M /branches/ideo3_v2/dev_news/#librairies/vendor/CoreConfigurationService/config/module.config.php
   M /branches/ideo3_v2/integration_news
   M /branches/ideo3_v2/integration_news/src/js/JEditor/ParamsPanel/Models/Params.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/ParamsPanel/Templates/ListSocialNetwork.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/ParamsPanel/Templates/SocialNetwork.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/ParamsPanel/Views/SocialNetworks.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js

Merging dev & integration branches from r12484 tu dev_news & integration_news up to head:r12509
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Models/Params.js
===================================================================
--- src/js/JEditor/ParamsPanel/Models/Params.js	(révision 12509)
+++ src/js/JEditor/ParamsPanel/Models/Params.js	(révision 12510)
@@ -16,6 +16,19 @@
     // var RGPDLinkRegex=/^(http(s)?:\/\/www\.|http(s)?:\/\/){1}[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\#[a-z0-9]+)?(\/.*)?$/;
     var RGPDLinkRegex=/^(http(s)?:\/\/www\.|http(s)?:\/\/)[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\#[a-z0-9]+)?(\/[^"'<>]*)?$/;
     var regExes = {
+        airbnbUrl:      /^(http(s)?:\/\/)?(www\.)?airbnb\.(fr|com|ca|com\.au)\/(.*)/,
+        bookingUrl:     /^(http(s)?:\/\/)?(www\.)?booking\.com\/(.*)/,
+        appleUrl:       /^(http(s)?:\/\/)?(www\.)?(apps.apple\.com|apple\.com)\/(.*)/,
+        googleplayUrl:  /^(http(s)?:\/\/)?(www\.)?play\.google\.com\/(.*)/,
+        vimeoUrl:       /^(http(s)?:\/\/)?(www\.)?vimeo\.com\/(.*)/,
+        doordashUrl:    /^(http(s)?:\/\/)?(www\.)?doordash\.com\/(.*)/,
+        snapchatUrl:    /^(http(s)?:\/\/)?(www\.)?snapchat\.com\/(.*)/,
+        yelpUrl:        /^(http(s)?:\/\/)?(www\.)?yelp\.(fr|com|ca|com\.au)\/(.*)/,
+        houzzUrl:       /^(http(s)?:\/\/)?(www\.)?houzz\.(fr|com|ca|com\.au)\/(.*)/,
+        ubereatsUrl:    /^(http(s)?:\/\/)?(www\.)?ubereats\.com\/(.*)/,
+        opentableUrl:   /^(http(s)?:\/\/)?(www\.)?opentable\.(fr|com|ca|com\.au)\/(.*)/,
+        deliverooUrl:   /^(http(s)?:\/\/)?(www\.)?deliveroo\.(fr|com|ca|com\.au)\/(.*)/,
+        genericUrl:     /^(http(s)?:\/\/)?(www\.)?(.*)/,
         facebookUrl:/^(https?\:\/\/)?(www\.)?facebook\.com\/(.*)/,
         // twitterUrl:/^(https?\:\/\/)?(www\.)?twitter\.com\/(?:#!\/)?([a-zA-Z0-9\._-]+)/,
         twitterUrl:/^(https?\:\/\/)?(www\.)?(twitter|x)\.com\/(?:#!\/)?([a-zA-Z0-9\._-]+)/,
@@ -41,6 +54,19 @@
     var Params = Model.extend({
         url: __IDEO_API_PATH__ + "/settings/",
         defaults: {
+            airbnbUrl: null,
+            bookingUrl:null,
+            appleUrl: null,
+            googleplayUrl: null,
+            vimeoUrl: null, 
+            doordashUrl: null,
+            snapchatUrl: null,
+            yelpUrl: null,
+            houzzUrl: null,
+            ubereatsUrl: null,
+            opentableUrl: null,
+            deliverooUrl: null,
+            genericUrl: null,
             facebookUrl: null,
             twitterUrl: null,
             xUrl: null,
Index: src/js/JEditor/ParamsPanel/Templates/ListSocialNetwork.html
===================================================================
--- src/js/JEditor/ParamsPanel/Templates/ListSocialNetwork.html	(révision 12509)
+++ src/js/JEditor/ParamsPanel/Templates/ListSocialNetwork.html	(révision 12510)
@@ -9,6 +9,148 @@
         
             <li  class="inline-params  <%=social.url?" ":"disabled "%>  thin-border  radius  shadow trash" id="I<%=i%>" name="<%=social.title%>">
                 
+                <% if( social.type =="airbnbUrl") { %>
+                    <span class="inline-params__name  airbnb">
+                        <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="none" viewBox="0 0 50 50"><path fill="#FF5A5F" d="M0 8a8 8 0 0 1 8-8h34a8 8 0 0 1 8 8v34a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z"/><path fill="#fff" d="M40.167 32c-.167-.4-.334-.833-.5-1.2-.267-.6-.534-1.167-.767-1.7l-.033-.033C36.567 24.067 34.1 19 31.5 14l-.1-.2c-.267-.5-.533-1.033-.8-1.567-.333-.6-.667-1.233-1.2-1.833-1.067-1.333-2.6-2.067-4.233-2.067-1.667 0-3.167.734-4.267 2-.5.6-.867 1.234-1.2 1.834-.267.533-.533 1.066-.8 1.566l-.1.2C16.233 18.933 13.733 24 11.433 29l-.033.067c-.233.533-.5 1.1-.767 1.7-.166.366-.333.766-.5 1.2-.433 1.233-.566 2.4-.4 3.6a7.084 7.084 0 0 0 4.334 5.533 6.846 6.846 0 0 0 2.7.533 8.807 8.807 0 0 0 4.2-1.2c1.366-.766 2.666-1.866 4.133-3.466 1.467 1.6 2.8 2.7 4.133 3.466a8.807 8.807 0 0 0 4.2 1.2c.934 0 1.867-.166 2.7-.533 2.334-.933 3.967-3.067 4.334-5.533.266-1.167.133-2.334-.3-3.567Zm-15.034 1.733c-1.8-2.266-2.966-4.4-3.366-6.2-.167-.766-.2-1.433-.1-2.033.066-.533.266-1 .533-1.4.633-.9 1.7-1.467 2.933-1.467 1.234 0 2.334.534 2.934 1.467.266.4.466.867.533 1.4.1.6.067 1.3-.1 2.033-.4 1.767-1.567 3.9-3.367 6.2Zm13.3 1.567a4.953 4.953 0 0 1-3.033 3.9 5.05 5.05 0 0 1-2.533.333c-.834-.1-1.667-.366-2.534-.866-1.2-.667-2.4-1.7-3.8-3.234 2.2-2.7 3.534-5.166 4.034-7.366a8.355 8.355 0 0 0 .166-2.834 5.408 5.408 0 0 0-.9-2.266c-1.033-1.5-2.766-2.367-4.7-2.367-1.933 0-3.666.9-4.7 2.367a5.41 5.41 0 0 0-.9 2.266 6.92 6.92 0 0 0 .167 2.834c.5 2.2 1.867 4.7 4.033 7.4-1.366 1.533-2.6 2.566-3.8 3.233-.866.5-1.7.767-2.533.867a5.33 5.33 0 0 1-2.533-.334 4.954 4.954 0 0 1-3.034-3.9 5.519 5.519 0 0 1 .3-2.6c.1-.333.267-.666.434-1.066.233-.534.5-1.1.766-1.667l.034-.067A364.37 364.37 0 0 1 20.7 14.967l.1-.2c.267-.5.533-1.034.8-1.534.267-.533.567-1.033.933-1.466.7-.8 1.634-1.234 2.667-1.234s1.967.434 2.667 1.234c.366.433.666.933.933 1.466.267.5.533 1.034.8 1.534l.1.2c2.533 4.966 5 10.033 7.3 15V30c.267.533.5 1.133.767 1.667.166.4.333.733.433 1.066.267.867.367 1.7.233 2.567Z"/></svg>
+                        <% 
+                            var Airbnb = "Airbnb" ;
+                        %>
+                        <input class="content rs" type="text" name="<%=social.type%>_<%=i %>" value='<%-Airbnb%>' data-titleId="<%=social.type%>_<%=i %>" data-reseaux="Airbnb" />
+                       
+                    </span>
+                <% } %>
+
+                <% if( social.type =="bookingUrl") { %>
+                    <span class="inline-params__name  booking">
+                        <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="none" viewBox="0 0 50 50"><path fill="#0C3B7C" d="M0 8a8 8 0 0 1 8-8h34a8 8 0 0 1 8 8v34a8 8 0 0 1-8 8H0V8Z"/><path fill="#fff" d="m22.724 32.907-4.027-.004v-4.815c0-1.029.399-1.564 1.28-1.687h2.747c1.96 0 3.227 1.236 3.227 3.235 0 2.054-1.236 3.27-3.227 3.271ZM18.697 19.92v-1.267c0-1.11.47-1.637 1.498-1.705h2.062c1.766 0 2.825 1.057 2.825 2.828 0 1.348-.726 2.922-2.761 2.922h-3.624V19.92Zm9.17 4.806-.729-.41.636-.543c.74-.636 1.98-2.066 1.98-4.532 0-3.779-2.93-6.216-7.465-6.216h-5.175v-.002h-.59a2.526 2.526 0 0 0-2.434 2.495v21.44h8.301c5.04 0 8.293-2.744 8.293-6.994 0-2.288-1.05-4.244-2.818-5.238Z"/><path fill="#00BAFC" d="M32.846 33.995a2.955 2.955 0 0 1 2.948-2.963 2.963 2.963 0 0 1 0 5.926 2.956 2.956 0 0 1-2.948-2.963Z"/></svg>
+                        <% 
+                            var Booking = "Booking.com" ;
+                        %>
+                        <input class="content rs" type="text" name="<%=social.type%>_<%=i %>" value='<%-Booking%>' data-titleId="<%=social.type%>_<%=i %>" data-reseaux="Booking.com" />
+                       
+                    </span>
+                <% } %>
+
+                <% if( social.type =="appleUrl") { %>
+                    <span class="inline-params__name  apple">
+                        <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="none" viewBox="0 0 50 50"><path fill="#000" d="M0 8a8 8 0 0 1 8-8h34a8 8 0 0 1 8 8v34a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z"/><path fill="#fff" d="M30.65 8.275c-2.075 3.025-4.75 3.55-5.575 3.55-.275 0-.425-.025-.425-.025s-.15-3.5 1.8-5.95c2.7-3.35 5.775-3.125 5.775-3.125s.2 2.925-1.575 5.55Z M40.75 30.375s-.1.25-.25.675c-.325.8-.925 2.2-1.625 3.7-.05.075-.1.15-.15.25-1.475 2.6-3.775 5.325-5.7 5.7-1.509.302-2.487-.154-3.572-.659-.982-.457-2.05-.954-3.678-.966-1.356-.013-2.246.423-3.137.86-.884.432-1.77.865-3.113.865-2.375 0-4.9-2.6-6.85-6.05a26.728 26.728 0 0 1-1.75-3.725c-.04-.06-.064-.137-.085-.204l-.015-.046a1.7 1.7 0 0 0-.05-.125 1.805 1.805 0 0 1-.05-.125 4.863 4.863 0 0 1-.2-.55.19.19 0 0 1-.05-.1c-.525-1.55-.9-3.1-1.05-4.55 0-.066-.007-.125-.014-.18-.005-.05-.011-.098-.011-.145-.15-1.45-.1-2.75.05-3.95.25-1.775.85-4.2 2.45-6.25 2.025-2.625 4.75-3.375 6.7-3.375 2.072 0 3.127.49 4.08.931.782.363 1.495.694 2.645.694.875 0 1.64-.311 2.523-.671 1.211-.494 2.647-1.079 4.902-1.079 4.525 0 6.925 3.725 6.925 3.725l-.3.225c-.75.575-2.625 2.275-3.425 4.875a6.261 6.261 0 0 0-.175.65v.025A7.864 7.864 0 0 0 35.6 23c.1 1.55.5 2.8 1.075 3.8a8.459 8.459 0 0 0 3.05 3.075c.45.275.8.425.95.475.025.025.05.025.05.025h.025Z"/></svg>
+                        <% 
+                            var Apple = "Apple Store" ;
+                        %>
+                        <input class="content rs" type="text" name="<%=social.type%>_<%=i %>" value='<%-Apple%>' data-titleId="<%=social.type%>_<%=i %>" data-reseaux="Apple Store" />
+                       
+                    </span>
+                <% } %>
+
+                <% if( social.type =="googleplayUrl") { %>
+                    <span class="inline-params__name  googleplay">
+                        <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="none" viewBox="0 0 50 50"><path fill="#EA4335" d="M24.625 24 5.375 44.125c.625 2.125 2.625 3.75 5 3.75 1 0 1.875-.25 2.625-.75L34.75 34.75 24.625 24Z"/><path fill="#FBBC04" d="m44.125 20.5-9.375-5.375-10.5 9.25L34.875 34.75l9.375-5.25C45.875 28.625 47 26.875 47 25c-.125-1.875-1.25-3.625-2.875-4.5Z"/><path fill="#4285F4" d="M5.375 5.875c-.125.375-.125.875-.125 1.375v35.625c0 .5 0 .875.125 1.375l20-19.625-20-18.75Z"/><path fill="#34A853" d="m24.75 25 10-9.875-21.625-12.25c-.75-.5-1.75-.75-2.75-.75-2.375 0-4.5 1.625-5 3.75L24.75 25Z"/></svg>
+                        <% 
+                            var GooglePlay = "Google Play" ;
+                        %>
+                        <input class="content rs" type="text" name="<%=social.type%>_<%=i %>" value='<%-GooglePlay%>' data-titleId="<%=social.type%>_<%=i %>" data-reseaux="Google Play" />
+                       
+                    </span>
+                <% } %>
+
+                <% if( social.type =="vimeoUrl") { %>
+                    <span class="inline-params__name  vimeo">
+                        <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="none" viewBox="0 0 50 50"><path fill="#19B1E3" d="M0 8a8 8 0 0 1 8-8h34a8 8 0 0 1 8 8v34a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z"/><path fill="#fff" d="M42.38 16.857c.222-4.422-1.437-6.743-4.974-6.854-4.754-.11-7.96 2.543-9.618 8.07.884-.442 1.769-.552 2.543-.552 1.768 0 2.542.994 2.321 2.984-.11 1.216-.884 2.875-2.321 5.196-1.437 2.211-2.543 3.427-3.206 3.427-.884 0-1.769-1.769-2.543-5.196-.22-1.105-.774-3.758-1.437-7.848-.663-3.87-2.21-5.749-4.975-5.417-1.216.11-2.874 1.216-5.085 3.095-.553.442-1.437 1.216-2.542 2.21-1.106.996-1.99 1.77-2.543 2.212l1.658 2.1c1.548-1.105 2.432-1.658 2.653-1.658 1.216 0 2.322 1.88 3.317 5.527.332 1.106.774 2.875 1.437 5.086.663 2.21 1.105 3.98 1.437 5.085 1.437 3.648 3.095 5.527 5.085 5.527 3.206 0 7.186-2.984 11.829-9.065 4.532-6.08 6.854-10.612 6.965-13.929Z"/></svg>
+                        <% 
+                            var Vimeo = "Vimeo" ;
+                        %>
+                        <input class="content rs" type="text" name="<%=social.type%>_<%=i %>" value='<%-Vimeo%>' data-titleId="<%=social.type%>_<%=i %>" data-reseaux="Vimeo" />
+                       
+                    </span>
+                <% } %>
+
+                <% if( social.type =="doordashUrl") { %>
+                    <span class="inline-params__name  doordash">
+                        <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="none" viewBox="0 0 50 50"><path fill="#FF3008" d="M0 8a8 8 0 0 1 8-8h34a8 8 0 0 1 8 8v34a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z"/><path fill="#fff" d="M41.07 19.566c-1.522-2.82-4.5-4.577-7.737-4.566H8.837a.844.844 0 0 0-.774.517.824.824 0 0 0 .182.903l5.336 5.295a2.525 2.525 0 0 0 1.781.732h17.269c1.23-.013 2.236.962 2.25 2.175.013 1.214-.974 2.208-2.205 2.22H20.771a.844.844 0 0 0-.776.514.824.824 0 0 0 .18.906l5.34 5.3a2.529 2.529 0 0 0 1.78.73h5.385c7.006 0 12.303-7.392 8.39-14.73"/></svg>
+                        <% 
+                            var DoorDash = "DoorDash" ;
+                        %>
+                        <input class="content rs" type="text" name="<%=social.type%>_<%=i %>" value='<%-DoorDash%>' data-titleId="<%=social.type%>_<%=i %>" data-reseaux="DoorDash" />
+                       
+                    </span>
+                <% } %>
+
+                <% if( social.type =="snapchatUrl") { %>
+                    <span class="inline-params__name  snapchat">
+                        <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="none" viewBox="0 0 50 50"><path fill="#FFFC00" d="M0 8a8 8 0 0 1 8-8h34a8 8 0 0 1 8 8v34a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z"/><path fill="#fff" d="M25.227 42.186a5.38 5.38 0 0 1-.279-.008c-.06.006-.12.008-.183.008-2.18 0-3.637-1.03-4.923-1.94-.922-.652-1.792-1.267-2.81-1.436a9.08 9.08 0 0 0-1.48-.125c-.865 0-1.55.134-2.05.232-.308.06-.573.111-.778.111-.215 0-.475-.047-.584-.419-.086-.292-.148-.576-.208-.85-.149-.682-.257-1.099-.513-1.138-2.734-.422-4.35-1.044-4.671-1.796a.698.698 0 0 1-.057-.237.439.439 0 0 1 .367-.458c2.171-.358 4.102-1.505 5.739-3.412 1.267-1.477 1.89-2.887 1.957-3.043a.27.27 0 0 1 .01-.02c.315-.64.378-1.194.187-1.644-.353-.831-1.52-1.201-2.291-1.446a8.265 8.265 0 0 1-.518-.176c-.685-.27-1.81-.841-1.66-1.63.109-.574.87-.975 1.484-.975.17 0 .321.03.449.09.694.325 1.319.49 1.856.49.668 0 .99-.254 1.068-.326a110.69 110.69 0 0 0-.065-1.095c-.157-2.494-.353-5.598.441-7.379 2.377-5.328 7.417-5.743 8.905-5.743a379.787 379.787 0 0 0 .736-.007c1.492 0 6.543.415 8.921 5.747.794 1.781.598 4.889.441 7.386l-.007.12c-.022.338-.042.66-.059.97.074.068.369.301.97.325.512-.02 1.1-.184 1.748-.487.2-.095.422-.114.573-.114.228 0 .46.044.652.125l.01.004c.55.195.91.585.919.995.006.382-.277.953-1.674 1.504a8.428 8.428 0 0 1-.518.176c-.773.245-1.938.615-2.29 1.445-.192.45-.13 1.004.186 1.644l.01.021c.098.228 2.45 5.59 7.696 6.454a.44.44 0 0 1 .367.458.705.705 0 0 1-.058.24c-.32.748-1.935 1.369-4.67 1.791-.256.04-.365.454-.512 1.134a12.5 12.5 0 0 1-.209.844c-.08.272-.256.405-.54.405h-.044a4.38 4.38 0 0 1-.778-.099 10.24 10.24 0 0 0-2.05-.217 9.09 9.09 0 0 0-1.48.125c-1.017.17-1.887.783-2.807 1.434-1.289.911-2.747 1.942-4.926 1.942Z"/><path fill="#020202" d="M25.356 8.253c1.405 0 6.24.378 8.52 5.487.75 1.682.558 4.73.404 7.179-.025.388-.048.765-.068 1.124l-.008.155.103.115c.042.046.432.454 1.3.487l.014.001h.014c.574-.022 1.22-.2 1.922-.529a.939.939 0 0 1 .386-.072c.166 0 .342.031.487.093l.022.008c.368.13.628.372.632.592.002.125-.09.573-1.395 1.088a8 8 0 0 1-.49.165c-.848.27-2.13.676-2.562 1.693-.243.573-.176 1.249.198 2.01.154.358 2.576 5.815 8.028 6.714a.24.24 0 0 1-.023.087c-.092.218-.68.97-4.334 1.534-.572.087-.712.728-.874 1.474a12.66 12.66 0 0 1-.2.814c-.026.084-.03.089-.12.089h-.043c-.162 0-.408-.034-.694-.09a10.72 10.72 0 0 0-2.135-.225c-.506 0-1.028.044-1.551.131-1.119.186-2.028.83-2.991 1.51-1.294.914-2.63 1.86-4.67 1.86-.09 0-.177-.003-.264-.007l-.023-.001-.023.002c-.05.004-.1.006-.152.006-2.04 0-3.377-.945-4.67-1.86-.963-.68-1.873-1.324-2.991-1.51a9.48 9.48 0 0 0-1.551-.131c-.907 0-1.617.139-2.135.24-.285.056-.532.103-.694.103-.132 0-.135-.007-.163-.103-.082-.278-.142-.554-.2-.82-.163-.746-.303-1.391-.875-1.479-3.654-.564-4.241-1.317-4.334-1.534a.266.266 0 0 1-.023-.09c5.452-.897 7.874-6.354 8.029-6.715.374-.76.44-1.435.197-2.009-.432-1.016-1.714-1.423-2.562-1.692a8.187 8.187 0 0 1-.49-.166c-1.102-.435-1.44-.874-1.39-1.14.058-.306.59-.617 1.053-.617.104 0 .195.017.263.048.752.353 1.44.532 2.042.532.947 0 1.37-.441 1.415-.491l.102-.115-.008-.154c-.02-.36-.043-.736-.068-1.124-.153-2.448-.346-5.494.404-7.177 2.27-5.09 7.083-5.483 8.504-5.483l.652-.006h.083Zm0-.878h-.093l-.645.007c-.832 0-2.497.118-4.286.907a9.55 9.55 0 0 0-2.734 1.813c-.943.896-1.712 2-2.284 3.284-.838 1.878-.639 5.043-.48 7.585l.002.004.051.834a1.467 1.467 0 0 1-.616.116c-.473 0-1.034-.15-1.67-.448a1.493 1.493 0 0 0-.635-.132c-.379 0-.777.112-1.123.315-.435.256-.717.617-.793 1.017-.05.265-.048.788.534 1.32.32.292.79.561 1.396.8.159.063.348.123.548.187.694.22 1.745.554 2.019 1.199.138.326.079.757-.178 1.277l-.019.042c-.064.15-.661 1.503-1.887 2.93a10.61 10.61 0 0 1-2.278 2.016 8.377 8.377 0 0 1-3.198 1.249.878.878 0 0 0-.735.916c.008.129.038.258.09.382l.002.003c.179.418.594.774 1.27 1.088.824.383 2.057.705 3.665.958.081.154.166.542.224.808.061.283.125.573.216.882.098.334.352.733 1.005.733.248 0 .532-.055.862-.12.483-.094 1.143-.223 1.967-.223.457 0 .93.04 1.407.12.918.152 1.71.712 2.627 1.36 1.34.947 2.859 2.02 5.178 2.02.064 0 .127-.002.19-.006.076.004.171.006.272.006 2.32 0 3.837-1.073 5.177-2.02l.002-.002c.917-.647 1.708-1.206 2.626-1.359.477-.079.95-.12 1.407-.12.787 0 1.41.101 1.967.21.363.071.645.106.862.106h.043c.478 0 .829-.262.963-.721.089-.302.152-.586.215-.873.054-.25.142-.649.223-.805 1.609-.252 2.842-.574 3.666-.957.674-.312 1.089-.668 1.269-1.085.054-.125.085-.254.092-.386a.878.878 0 0 0-.734-.916c-5.012-.826-7.27-5.976-7.363-6.194a.61.61 0 0 0-.02-.043c-.256-.52-.316-.95-.176-1.277.273-.645 1.323-.978 2.018-1.199.2-.063.39-.123.548-.186.684-.27 1.173-.563 1.495-.896.385-.396.46-.776.456-1.025-.012-.602-.472-1.137-1.204-1.398a2.152 2.152 0 0 0-.816-.157 1.82 1.82 0 0 0-.758.155c-.587.275-1.112.425-1.563.446a1.414 1.414 0 0 1-.517-.114l.045-.727.006-.108c.16-2.544.36-5.711-.478-7.591-.574-1.288-1.346-2.395-2.293-3.293a9.572 9.572 0 0 0-2.744-1.813 10.914 10.914 0 0 0-4.285-.9Z"/></svg>
+                        <% 
+                            var Snapchat = "Snapchat" ;
+                        %>
+                        <input class="content rs" type="text" name="<%=social.type%>_<%=i %>" value='<%-Snapchat%>' data-titleId="<%=social.type%>_<%=i %>" data-reseaux="Snapchat" />
+                       
+                    </span>
+                <% } %>
+
+                <% if( social.type =="yelpUrl") { %>
+                    <span class="inline-params__name  yelp">
+                        <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="none" viewBox="0 0 50 50"><path fill="#D32323" d="M42 0H8a8 8 0 0 0-8 8v34a8 8 0 0 0 8 8h34a8 8 0 0 0 8-8V8a8 8 0 0 0-8-8Z"/><path fill="#fff" d="M24.25 7.25c-.125-.458-.5-.792-1-.958-1.583-.417-7.75 1.333-8.875 2.539-.375.375-.5.879-.375 1.292.167.375 7.792 12.542 7.792 12.542 1.125 1.833 2.041 1.583 2.333 1.458.293-.083 1.208-.375 1.125-2.539-.167-2.541-.917-13.792-1-14.334Zm-3.292 22.833c.625-.166 1.042-.75 1.084-1.458.041-.75-.334-1.417-.959-1.667l-1.747-.708c-5.957-2.5-6.208-2.583-6.5-2.583-.458-.042-.879.208-1.166.666-.586.959-.834 4.042-.625 6.084.083.666.208 1.25.375 1.583.25.458.625.75 1.083.75.293 0 .458-.042 6-1.833l2.455-.834Zm3.125 2.292c-.666-.293-1.416-.125-1.833.417l-1.208 1.458c-4.167 5-4.334 5.208-4.459 5.5a1.237 1.237 0 0 0-.083.542c.012.283.131.55.333.75.959 1.166 5.584 2.916 7.084 2.666a1.275 1.275 0 0 0 1.041-.837c.084-.293.125-.5.125-6.375v-2.663c.084-.625-.333-1.208-1-1.458Zm14.5 1.172c-.25-.167-.416-.25-5.957-2.084 0 0-2.417-.833-2.458-.833-.586-.25-1.25-.042-1.709.542-.459.584-.541 1.333-.166 1.916l.958 1.625c3.292 5.417 3.542 5.792 3.75 6 .375.293.833.334 1.333.125 1.417-.586 4.459-4.5 4.667-6.041.082-.505-.043-.964-.418-1.255v.005Zm-8.916-6.167c.879-.167 8.041-1.875 8.625-2.292.375-.25.586-.708.541-1.25v-.041c-.166-1.584-2.875-5.664-4.208-6.334-.458-.208-.958-.208-1.333.042-.25.167-.417.417-3.792 5.083l-1.542 2.119c-.416.5-.416 1.209 0 1.834.417.667.834.998 1.709.834v.005Z"/></svg>
+                        <% 
+                            var Yelp = "Yelp" ;
+                        %>
+                        <input class="content rs" type="text" name="<%=social.type%>_<%=i %>" value='<%-Yelp%>' data-titleId="<%=social.type%>_<%=i %>" data-reseaux="Yelp" />
+                       
+                    </span>
+                <% } %>
+
+                <% if( social.type =="houzzUrl") { %>
+                    <span class="inline-params__name  houzz">
+                        <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="none" viewBox="0 0 50 50"><path fill="#4DBC15" d="M50 25c0 13.807-11.193 25-25 25S0 38.807 0 25 11.193 0 25 0s25 11.193 25 25Z"/><path fill="#fff" d="M19.336 12.5h-5.469v25h8.399v-7.715h5.566V37.5h8.3V23.242L19.337 18.36V12.5Z"/></svg>
+                        <% 
+                            var Houzz = "Houzz" ;
+                        %>
+                        <input class="content rs" type="text" name="<%=social.type%>_<%=i %>" value='<%-Houzz%>' data-titleId="<%=social.type%>_<%=i %>" data-reseaux="Houzz" />
+                       
+                    </span>
+                <% } %>
+
+                <% if( social.type =="ubereatsUrl") { %>
+                    <span class="inline-params__name  ubereats">
+                        <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="none" viewBox="0 0 50 50"><path fill="#06C167" d="M0 8a8 8 0 0 1 8-8h34a8 8 0 0 1 8 8v34a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z"/><path fill="#000" d="M12.56 22.165c1.626 0 2.883-1.249 2.883-3.097v-7.193h1.76V23.53H15.46v-1.082c-.788.815-1.878 1.282-3.101 1.282-2.514 0-4.442-1.815-4.442-4.562v-7.292h1.76v7.192c0 1.881 1.24 3.097 2.883 3.097Zm5.95 1.365h1.677v-1.066a4.28 4.28 0 0 0 3.05 1.266c2.515 0 4.493-1.981 4.493-4.429 0-2.464-1.977-4.445-4.492-4.445a4.242 4.242 0 0 0-3.034 1.265v-4.245H18.51V23.53Zm4.61-1.282a2.936 2.936 0 0 1-2.95-2.947 2.94 2.94 0 0 1 4.076-2.722 2.932 2.932 0 0 1 1.808 2.722 2.944 2.944 0 0 1-2.933 2.947Zm9.672-7.376c-2.497 0-4.392 2.015-4.392 4.412 0 2.53 1.979 4.43 4.543 4.43 1.56 0 2.833-.684 3.688-1.816L35.407 21c-.637.85-1.475 1.249-2.463 1.249-1.443 0-2.599-1.033-2.834-2.414h6.957v-.55c0-2.53-1.81-4.412-4.275-4.412Zm-2.648 3.63c.302-1.299 1.358-2.164 2.615-2.164 1.258 0 2.313.865 2.599 2.164h-5.214Zm12.17-1.965v-1.565h-.586c-.939 0-1.626.433-2.045 1.116v-1.05h-1.677v8.492H39.7v-4.828c0-1.315.804-2.165 1.91-2.165h.704ZM7.917 26.25h8.14v1.992h-5.941v2.85H15.9v1.932h-5.784v2.89h5.942v1.992H7.917V26.25ZM38.18 38.125c2.497 0 3.903-1.196 3.903-2.85 0-1.175-.832-2.052-2.575-2.43l-1.842-.379c-1.07-.199-1.407-.398-1.407-.797 0-.518.516-.837 1.467-.837 1.03 0 1.782.28 2 1.235h2.16c-.12-1.792-1.407-2.988-4.022-2.988-2.258 0-3.843.937-3.843 2.75 0 1.255.872 2.072 2.754 2.47l2.06.478c.812.16 1.03.379 1.03.718 0 .538-.614.877-1.605.877-1.248 0-1.96-.279-2.238-1.236h-2.18c.318 1.793 1.645 2.989 4.339 2.989Zm-6.545-2.172h1.624v1.952H30.92c-1.466 0-2.278-.916-2.278-2.072v-4.582H27v-1.953h1.644v-2.45h2.18v2.45h2.436v1.953h-2.437v4.025c0 .458.317.677.813.677Z M23.92 29.299h2.16v8.607h-2.16v-.777a4.197 4.197 0 0 1-2.734.996c-2.555 0-4.555-2.012-4.555-4.523 0-2.51 2-4.523 4.555-4.523a4.197 4.197 0 0 1 2.734.997v-.777Zm.04 4.303a2.635 2.635 0 0 0-.753-1.866 2.57 2.57 0 0 0-1.842-.764 2.559 2.559 0 0 0-1.838.768 2.622 2.622 0 0 0-.757 1.862 2.649 2.649 0 0 0 .757 1.862 2.584 2.584 0 0 0 1.838.768c1.446 0 2.595-1.156 2.595-2.63Z"/></svg>
+                        <% 
+                            var ubereats = "Uber Eats" ;
+                        %>
+                        <input class="content rs" type="text" name="<%=social.type%>_<%=i %>" value='<%-ubereats%>' data-titleId="<%=social.type%>_<%=i %>" data-reseaux="Uber Eats" />
+                       
+                    </span>
+                <% } %>
+
+                <% if( social.type =="opentableUrl") { %>
+                    <span class="inline-params__name  opentable">
+                        <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="none" viewBox="0 0 50 50"><path fill="#DD3743" d="M50 25c0 13.807-11.193 25-25 25S0 38.807 0 25 11.193 0 25 0s25 11.193 25 25Z"/><path fill="#fff" d="M13.544 22.716c-1.405 0-2.544 1.152-2.544 2.572 0 1.422 1.14 2.573 2.544 2.573 1.405 0 2.545-1.152 2.545-2.573 0-1.42-1.14-2.572-2.545-2.572Z M18.695 25.288C18.695 19.607 23.252 15 28.872 15c5.622 0 10.178 4.607 10.178 10.288 0 5.683-4.556 10.29-10.178 10.29-5.62 0-10.177-4.607-10.177-10.29Zm7.633 0c0 1.421 1.139 2.573 2.544 2.573s2.545-1.152 2.545-2.573c0-1.42-1.14-2.572-2.545-2.572s-2.544 1.152-2.544 2.572Z"/></svg>
+                        <% 
+                            var opentable = "OpenTable" ;
+                        %>
+                        <input class="content rs" type="text" name="<%=social.type%>_<%=i %>" value='<%-opentable%>' data-titleId="<%=social.type%>_<%=i %>" data-reseaux="OpenTable" />
+                       
+                    </span>
+                <% } %>
+
+                <% if( social.type =="deliverooUrl") { %>
+                    <span class="inline-params__name  deliveroo">
+                        <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="none" viewBox="0 0 50 50"><path fill="#35BEB2" d="M0 8a8 8 0 0 1 8-8h34a8 8 0 0 1 8 8v34a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z"/><path fill="#fff" d="M22.037 25.173a.044.044 0 0 0 .027-.018c.006-.01.009-.021.006-.033l-2.422-11.28a.103.103 0 0 1 .014-.076.1.1 0 0 1 .063-.044l7.413-1.548c.13-.027.21.024.238.155l2.386 11.066a.014.014 0 0 0 .014.012c.003 0 .007 0 .01-.003a.015.015 0 0 0 .006-.008.348.348 0 0 0 .014-.07c.467-4.382.932-8.763 1.393-13.145.008-.068.02-.113.038-.134a.122.122 0 0 1 .111-.046l7.608.8a.107.107 0 0 1 .09.075.106.106 0 0 1 .003.041c-.678 6.413-1.358 12.833-2.036 19.26a3.97 3.97 0 0 1-.326 1.21 1295.113 1295.113 0 0 1-4.187 9.25.125.125 0 0 1-.137.07l-19.371-4.056a.08.08 0 0 1-.062-.062l-1.928-9.01a.1.1 0 0 1 .014-.075.099.099 0 0 1 .062-.043l10.959-2.288Zm3.435 3.69c.241-1.398-2.155-2.012-2.497-.597-.265 1.098.32 1.678 1.409 1.637.586-.023.994-.498 1.088-1.04Zm5.455.893a1.123 1.123 0 0 0-.663-1.438l-.252-.093a1.114 1.114 0 0 0-1.434.665l-.088.24a1.123 1.123 0 0 0 .662 1.437l.253.093a1.115 1.115 0 0 0 1.434-.664l.088-.24Z"/></svg>
+                        <% 
+                            var Deliveroo = "Deliveroo" ;
+                        %>
+                        <input class="content rs" type="text" name="<%=social.type%>_<%=i %>" value='<%-Deliveroo%>' data-titleId="<%=social.type%>_<%=i %>" data-reseaux="Deliveroo" />
+                       
+                    </span>
+                <% } %>
+                <% if( social.type =="genericUrl") { %>
+                    <span class="inline-params__name  generic">
+                        <svg width="16" height="16" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="#000000" class="icon-social"><path class="color1" d="M400.4,341.5c-20.2,0-38.7,7.1-53.3,18.9l-151.6-89.9c0.9-4.9,1.4-9.8,1.4-14.9c0-5-0.5-9.9-1.4-14.7l151.4-89.3 c14.6,11.8,33.2,19,53.5,19c47,0,85.2-38.2,85.2-85.2S447.4,0,400.4,0s-85.2,38.2-85.2,85.2c0,5,0.5,9.9,1.4,14.7l-151.4,89.3 c-14.6-11.8-33.2-19-53.5-19c-47,0-85.2,38.2-85.2,85.2s38.2,85.2,85.2,85.2c20.2,0,38.7-7.1,53.3-18.9l151.6,89.9 c-0.9,4.9-1.4,9.8-1.4,14.9c0,47,38.2,85.2,85.2,85.2s85.2-38.2,85.2-85.2S447.4,341.5,400.4,341.5z M400.4,59.8 c14,0,25.4,11.4,25.4,25.4s-11.4,25.4-25.4,25.4c-14,0-25.4-11.4-25.4-25.4S386.4,59.8,400.4,59.8z M111.6,281 c-14,0-25.4-11.4-25.4-25.4c0-14,11.4-25.4,25.4-25.4c14,0,25.4,11.4,25.4,25.4C137,269.6,125.6,281,111.6,281z M400.4,452.2 c-14,0-25.4-11.4-25.4-25.4c0-14,11.4-25.4,25.4-25.4c14,0,25.4,11.4,25.4,25.4C425.8,440.8,414.4,452.2,400.4,452.2z"/></svg>
+                        <% 
+                            var Generic = "Generic" ;
+                        %>
+                        <input class="content rs" type="text" name="<%=social.type%>_<%=i %>" value='<%-Generic%>' data-titleId="<%=social.type%>_<%=i %>" data-reseaux="Generic" />
+                       
+                    </span>
+                <% } %>
+
                 <% if( social.type =="facebookUrl") {%>
                     <span class="inline-params__name  fb">
                         <svg width="50px" height="50px" viewBox="0 0 50 50" enable-background="new 0 0 50 50" xml:space="preserve">
Index: src/js/JEditor/ParamsPanel/Templates/SocialNetwork.html
===================================================================
--- src/js/JEditor/ParamsPanel/Templates/SocialNetwork.html	(révision 12509)
+++ src/js/JEditor/ParamsPanel/Templates/SocialNetwork.html	(révision 12510)
@@ -19,6 +19,85 @@
           </a>
           <ul class="dropdown-menu shadow thin-border radius">
             <li>
+                <a  data-id="airbnbUrl" class="airbnb">
+                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#FF5A5F" d="M0 8a8 8 0 0 1 8-8h34a8 8 0 0 1 8 8v34a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z"/><path fill="#fff" d="M40.167 32c-.167-.4-.334-.833-.5-1.2-.267-.6-.534-1.167-.767-1.7l-.033-.033C36.567 24.067 34.1 19 31.5 14l-.1-.2c-.267-.5-.533-1.033-.8-1.567-.333-.6-.667-1.233-1.2-1.833-1.067-1.333-2.6-2.067-4.233-2.067-1.667 0-3.167.734-4.267 2-.5.6-.867 1.234-1.2 1.834-.267.533-.533 1.066-.8 1.566l-.1.2C16.233 18.933 13.733 24 11.433 29l-.033.067c-.233.533-.5 1.1-.767 1.7-.166.366-.333.766-.5 1.2-.433 1.233-.566 2.4-.4 3.6a7.084 7.084 0 0 0 4.334 5.533 6.846 6.846 0 0 0 2.7.533 8.807 8.807 0 0 0 4.2-1.2c1.366-.766 2.666-1.866 4.133-3.466 1.467 1.6 2.8 2.7 4.133 3.466a8.807 8.807 0 0 0 4.2 1.2c.934 0 1.867-.166 2.7-.533 2.334-.933 3.967-3.067 4.334-5.533.266-1.167.133-2.334-.3-3.567Zm-15.034 1.733c-1.8-2.266-2.966-4.4-3.366-6.2-.167-.766-.2-1.433-.1-2.033.066-.533.266-1 .533-1.4.633-.9 1.7-1.467 2.933-1.467 1.234 0 2.334.534 2.934 1.467.266.4.466.867.533 1.4.1.6.067 1.3-.1 2.033-.4 1.767-1.567 3.9-3.367 6.2Zm13.3 1.567a4.953 4.953 0 0 1-3.033 3.9 5.05 5.05 0 0 1-2.533.333c-.834-.1-1.667-.366-2.534-.866-1.2-.667-2.4-1.7-3.8-3.234 2.2-2.7 3.534-5.166 4.034-7.366a8.355 8.355 0 0 0 .166-2.834 5.408 5.408 0 0 0-.9-2.266c-1.033-1.5-2.766-2.367-4.7-2.367-1.933 0-3.666.9-4.7 2.367a5.41 5.41 0 0 0-.9 2.266 6.92 6.92 0 0 0 .167 2.834c.5 2.2 1.867 4.7 4.033 7.4-1.366 1.533-2.6 2.566-3.8 3.233-.866.5-1.7.767-2.533.867a5.33 5.33 0 0 1-2.533-.334 4.954 4.954 0 0 1-3.034-3.9 5.519 5.519 0 0 1 .3-2.6c.1-.333.267-.666.434-1.066.233-.534.5-1.1.766-1.667l.034-.067A364.37 364.37 0 0 1 20.7 14.967l.1-.2c.267-.5.533-1.034.8-1.534.267-.533.567-1.033.933-1.466.7-.8 1.634-1.234 2.667-1.234s1.967.434 2.667 1.234c.366.433.666.933.933 1.466.267.5.533 1.034.8 1.534l.1.2c2.533 4.966 5 10.033 7.3 15V30c.267.533.5 1.133.767 1.667.166.4.333.733.433 1.066.267.867.367 1.7.233 2.567Z"/></svg>
+                    Airbnb
+                </a>
+            </li>
+            <li>
+                <a  data-id="bookingUrl" class="booking">
+                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#0C3B7C" d="M0 8a8 8 0 0 1 8-8h34a8 8 0 0 1 8 8v34a8 8 0 0 1-8 8H0V8Z"/><path fill="#fff" d="m22.724 32.907-4.027-.004v-4.815c0-1.029.399-1.564 1.28-1.687h2.747c1.96 0 3.227 1.236 3.227 3.235 0 2.054-1.236 3.27-3.227 3.271ZM18.697 19.92v-1.267c0-1.11.47-1.637 1.498-1.705h2.062c1.766 0 2.825 1.057 2.825 2.828 0 1.348-.726 2.922-2.761 2.922h-3.624V19.92Zm9.17 4.806-.729-.41.636-.543c.74-.636 1.98-2.066 1.98-4.532 0-3.779-2.93-6.216-7.465-6.216h-5.175v-.002h-.59a2.526 2.526 0 0 0-2.434 2.495v21.44h8.301c5.04 0 8.293-2.744 8.293-6.994 0-2.288-1.05-4.244-2.818-5.238Z"/><path fill="#00BAFC" d="M32.846 33.995a2.955 2.955 0 0 1 2.948-2.963 2.963 2.963 0 0 1 0 5.926 2.956 2.956 0 0 1-2.948-2.963Z"/></svg>
+                    Booking.com
+                </a>
+            </li>
+            <li>
+                <a  data-id="appleUrl" class="apple">
+                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#000" d="M0 8a8 8 0 0 1 8-8h34a8 8 0 0 1 8 8v34a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z"/><path fill="#fff" d="M30.65 8.275c-2.075 3.025-4.75 3.55-5.575 3.55-.275 0-.425-.025-.425-.025s-.15-3.5 1.8-5.95c2.7-3.35 5.775-3.125 5.775-3.125s.2 2.925-1.575 5.55Z M40.75 30.375s-.1.25-.25.675c-.325.8-.925 2.2-1.625 3.7-.05.075-.1.15-.15.25-1.475 2.6-3.775 5.325-5.7 5.7-1.509.302-2.487-.154-3.572-.659-.982-.457-2.05-.954-3.678-.966-1.356-.013-2.246.423-3.137.86-.884.432-1.77.865-3.113.865-2.375 0-4.9-2.6-6.85-6.05a26.728 26.728 0 0 1-1.75-3.725c-.04-.06-.064-.137-.085-.204l-.015-.046a1.7 1.7 0 0 0-.05-.125 1.805 1.805 0 0 1-.05-.125 4.863 4.863 0 0 1-.2-.55.19.19 0 0 1-.05-.1c-.525-1.55-.9-3.1-1.05-4.55 0-.066-.007-.125-.014-.18-.005-.05-.011-.098-.011-.145-.15-1.45-.1-2.75.05-3.95.25-1.775.85-4.2 2.45-6.25 2.025-2.625 4.75-3.375 6.7-3.375 2.072 0 3.127.49 4.08.931.782.363 1.495.694 2.645.694.875 0 1.64-.311 2.523-.671 1.211-.494 2.647-1.079 4.902-1.079 4.525 0 6.925 3.725 6.925 3.725l-.3.225c-.75.575-2.625 2.275-3.425 4.875a6.261 6.261 0 0 0-.175.65v.025A7.864 7.864 0 0 0 35.6 23c.1 1.55.5 2.8 1.075 3.8a8.459 8.459 0 0 0 3.05 3.075c.45.275.8.425.95.475.025.025.05.025.05.025h.025Z"/></svg>
+                    Apple Store
+                </a>
+            </li>
+            <li>
+                <a  data-id="googleplayUrl" class="googleplay">
+                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#EA4335" d="M24.625 24 5.375 44.125c.625 2.125 2.625 3.75 5 3.75 1 0 1.875-.25 2.625-.75L34.75 34.75 24.625 24Z"/><path fill="#FBBC04" d="m44.125 20.5-9.375-5.375-10.5 9.25L34.875 34.75l9.375-5.25C45.875 28.625 47 26.875 47 25c-.125-1.875-1.25-3.625-2.875-4.5Z"/><path fill="#4285F4" d="M5.375 5.875c-.125.375-.125.875-.125 1.375v35.625c0 .5 0 .875.125 1.375l20-19.625-20-18.75Z"/><path fill="#34A853" d="m24.75 25 10-9.875-21.625-12.25c-.75-.5-1.75-.75-2.75-.75-2.375 0-4.5 1.625-5 3.75L24.75 25Z"/></svg>
+                    Google Play
+                </a>
+            </li>
+            <li>
+                <a  data-id="vimeoUrl" class="vimeo">
+                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#19B1E3" d="M0 8a8 8 0 0 1 8-8h34a8 8 0 0 1 8 8v34a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z"/><path fill="#fff" d="M42.38 16.857c.222-4.422-1.437-6.743-4.974-6.854-4.754-.11-7.96 2.543-9.618 8.07.884-.442 1.769-.552 2.543-.552 1.768 0 2.542.994 2.321 2.984-.11 1.216-.884 2.875-2.321 5.196-1.437 2.211-2.543 3.427-3.206 3.427-.884 0-1.769-1.769-2.543-5.196-.22-1.105-.774-3.758-1.437-7.848-.663-3.87-2.21-5.749-4.975-5.417-1.216.11-2.874 1.216-5.085 3.095-.553.442-1.437 1.216-2.542 2.21-1.106.996-1.99 1.77-2.543 2.212l1.658 2.1c1.548-1.105 2.432-1.658 2.653-1.658 1.216 0 2.322 1.88 3.317 5.527.332 1.106.774 2.875 1.437 5.086.663 2.21 1.105 3.98 1.437 5.085 1.437 3.648 3.095 5.527 5.085 5.527 3.206 0 7.186-2.984 11.829-9.065 4.532-6.08 6.854-10.612 6.965-13.929Z"/></svg>
+                    Vimeo
+                </a>
+            </li>
+            <li>
+                <a  data-id="doordashUrl" class="doordash">
+                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#FF3008" d="M0 8a8 8 0 0 1 8-8h34a8 8 0 0 1 8 8v34a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z"/><path fill="#fff" d="M41.07 19.566c-1.522-2.82-4.5-4.577-7.737-4.566H8.837a.844.844 0 0 0-.774.517.824.824 0 0 0 .182.903l5.336 5.295a2.525 2.525 0 0 0 1.781.732h17.269c1.23-.013 2.236.962 2.25 2.175.013 1.214-.974 2.208-2.205 2.22H20.771a.844.844 0 0 0-.776.514.824.824 0 0 0 .18.906l5.34 5.3a2.529 2.529 0 0 0 1.78.73h5.385c7.006 0 12.303-7.392 8.39-14.73"/></svg>
+                    DoorDash
+                </a>
+            </li>
+            <li>
+                <a  data-id="snapchatUrl" class="snapchat">
+                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#FFFC00" d="M0 8a8 8 0 0 1 8-8h34a8 8 0 0 1 8 8v34a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z"/><path fill="#fff" d="M25.227 42.186a5.38 5.38 0 0 1-.279-.008c-.06.006-.12.008-.183.008-2.18 0-3.637-1.03-4.923-1.94-.922-.652-1.792-1.267-2.81-1.436a9.08 9.08 0 0 0-1.48-.125c-.865 0-1.55.134-2.05.232-.308.06-.573.111-.778.111-.215 0-.475-.047-.584-.419-.086-.292-.148-.576-.208-.85-.149-.682-.257-1.099-.513-1.138-2.734-.422-4.35-1.044-4.671-1.796a.698.698 0 0 1-.057-.237.439.439 0 0 1 .367-.458c2.171-.358 4.102-1.505 5.739-3.412 1.267-1.477 1.89-2.887 1.957-3.043a.27.27 0 0 1 .01-.02c.315-.64.378-1.194.187-1.644-.353-.831-1.52-1.201-2.291-1.446a8.265 8.265 0 0 1-.518-.176c-.685-.27-1.81-.841-1.66-1.63.109-.574.87-.975 1.484-.975.17 0 .321.03.449.09.694.325 1.319.49 1.856.49.668 0 .99-.254 1.068-.326a110.69 110.69 0 0 0-.065-1.095c-.157-2.494-.353-5.598.441-7.379 2.377-5.328 7.417-5.743 8.905-5.743a379.787 379.787 0 0 0 .736-.007c1.492 0 6.543.415 8.921 5.747.794 1.781.598 4.889.441 7.386l-.007.12c-.022.338-.042.66-.059.97.074.068.369.301.97.325.512-.02 1.1-.184 1.748-.487.2-.095.422-.114.573-.114.228 0 .46.044.652.125l.01.004c.55.195.91.585.919.995.006.382-.277.953-1.674 1.504a8.428 8.428 0 0 1-.518.176c-.773.245-1.938.615-2.29 1.445-.192.45-.13 1.004.186 1.644l.01.021c.098.228 2.45 5.59 7.696 6.454a.44.44 0 0 1 .367.458.705.705 0 0 1-.058.24c-.32.748-1.935 1.369-4.67 1.791-.256.04-.365.454-.512 1.134a12.5 12.5 0 0 1-.209.844c-.08.272-.256.405-.54.405h-.044a4.38 4.38 0 0 1-.778-.099 10.24 10.24 0 0 0-2.05-.217 9.09 9.09 0 0 0-1.48.125c-1.017.17-1.887.783-2.807 1.434-1.289.911-2.747 1.942-4.926 1.942Z"/><path fill="#020202" d="M25.356 8.253c1.405 0 6.24.378 8.52 5.487.75 1.682.558 4.73.404 7.179-.025.388-.048.765-.068 1.124l-.008.155.103.115c.042.046.432.454 1.3.487l.014.001h.014c.574-.022 1.22-.2 1.922-.529a.939.939 0 0 1 .386-.072c.166 0 .342.031.487.093l.022.008c.368.13.628.372.632.592.002.125-.09.573-1.395 1.088a8 8 0 0 1-.49.165c-.848.27-2.13.676-2.562 1.693-.243.573-.176 1.249.198 2.01.154.358 2.576 5.815 8.028 6.714a.24.24 0 0 1-.023.087c-.092.218-.68.97-4.334 1.534-.572.087-.712.728-.874 1.474a12.66 12.66 0 0 1-.2.814c-.026.084-.03.089-.12.089h-.043c-.162 0-.408-.034-.694-.09a10.72 10.72 0 0 0-2.135-.225c-.506 0-1.028.044-1.551.131-1.119.186-2.028.83-2.991 1.51-1.294.914-2.63 1.86-4.67 1.86-.09 0-.177-.003-.264-.007l-.023-.001-.023.002c-.05.004-.1.006-.152.006-2.04 0-3.377-.945-4.67-1.86-.963-.68-1.873-1.324-2.991-1.51a9.48 9.48 0 0 0-1.551-.131c-.907 0-1.617.139-2.135.24-.285.056-.532.103-.694.103-.132 0-.135-.007-.163-.103-.082-.278-.142-.554-.2-.82-.163-.746-.303-1.391-.875-1.479-3.654-.564-4.241-1.317-4.334-1.534a.266.266 0 0 1-.023-.09c5.452-.897 7.874-6.354 8.029-6.715.374-.76.44-1.435.197-2.009-.432-1.016-1.714-1.423-2.562-1.692a8.187 8.187 0 0 1-.49-.166c-1.102-.435-1.44-.874-1.39-1.14.058-.306.59-.617 1.053-.617.104 0 .195.017.263.048.752.353 1.44.532 2.042.532.947 0 1.37-.441 1.415-.491l.102-.115-.008-.154c-.02-.36-.043-.736-.068-1.124-.153-2.448-.346-5.494.404-7.177 2.27-5.09 7.083-5.483 8.504-5.483l.652-.006h.083Zm0-.878h-.093l-.645.007c-.832 0-2.497.118-4.286.907a9.55 9.55 0 0 0-2.734 1.813c-.943.896-1.712 2-2.284 3.284-.838 1.878-.639 5.043-.48 7.585l.002.004.051.834a1.467 1.467 0 0 1-.616.116c-.473 0-1.034-.15-1.67-.448a1.493 1.493 0 0 0-.635-.132c-.379 0-.777.112-1.123.315-.435.256-.717.617-.793 1.017-.05.265-.048.788.534 1.32.32.292.79.561 1.396.8.159.063.348.123.548.187.694.22 1.745.554 2.019 1.199.138.326.079.757-.178 1.277l-.019.042c-.064.15-.661 1.503-1.887 2.93a10.61 10.61 0 0 1-2.278 2.016 8.377 8.377 0 0 1-3.198 1.249.878.878 0 0 0-.735.916c.008.129.038.258.09.382l.002.003c.179.418.594.774 1.27 1.088.824.383 2.057.705 3.665.958.081.154.166.542.224.808.061.283.125.573.216.882.098.334.352.733 1.005.733.248 0 .532-.055.862-.12.483-.094 1.143-.223 1.967-.223.457 0 .93.04 1.407.12.918.152 1.71.712 2.627 1.36 1.34.947 2.859 2.02 5.178 2.02.064 0 .127-.002.19-.006.076.004.171.006.272.006 2.32 0 3.837-1.073 5.177-2.02l.002-.002c.917-.647 1.708-1.206 2.626-1.359.477-.079.95-.12 1.407-.12.787 0 1.41.101 1.967.21.363.071.645.106.862.106h.043c.478 0 .829-.262.963-.721.089-.302.152-.586.215-.873.054-.25.142-.649.223-.805 1.609-.252 2.842-.574 3.666-.957.674-.312 1.089-.668 1.269-1.085.054-.125.085-.254.092-.386a.878.878 0 0 0-.734-.916c-5.012-.826-7.27-5.976-7.363-6.194a.61.61 0 0 0-.02-.043c-.256-.52-.316-.95-.176-1.277.273-.645 1.323-.978 2.018-1.199.2-.063.39-.123.548-.186.684-.27 1.173-.563 1.495-.896.385-.396.46-.776.456-1.025-.012-.602-.472-1.137-1.204-1.398a2.152 2.152 0 0 0-.816-.157 1.82 1.82 0 0 0-.758.155c-.587.275-1.112.425-1.563.446a1.414 1.414 0 0 1-.517-.114l.045-.727.006-.108c.16-2.544.36-5.711-.478-7.591-.574-1.288-1.346-2.395-2.293-3.293a9.572 9.572 0 0 0-2.744-1.813 10.914 10.914 0 0 0-4.285-.9Z"/></svg>
+                    Snapchat
+                </a>
+            </li>
+            <li>
+                <a  data-id="yelpUrl" class="yelp">
+                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#D32323" d="M42 0H8a8 8 0 0 0-8 8v34a8 8 0 0 0 8 8h34a8 8 0 0 0 8-8V8a8 8 0 0 0-8-8Z"/><path fill="#fff" d="M24.25 7.25c-.125-.458-.5-.792-1-.958-1.583-.417-7.75 1.333-8.875 2.539-.375.375-.5.879-.375 1.292.167.375 7.792 12.542 7.792 12.542 1.125 1.833 2.041 1.583 2.333 1.458.293-.083 1.208-.375 1.125-2.539-.167-2.541-.917-13.792-1-14.334Zm-3.292 22.833c.625-.166 1.042-.75 1.084-1.458.041-.75-.334-1.417-.959-1.667l-1.747-.708c-5.957-2.5-6.208-2.583-6.5-2.583-.458-.042-.879.208-1.166.666-.586.959-.834 4.042-.625 6.084.083.666.208 1.25.375 1.583.25.458.625.75 1.083.75.293 0 .458-.042 6-1.833l2.455-.834Zm3.125 2.292c-.666-.293-1.416-.125-1.833.417l-1.208 1.458c-4.167 5-4.334 5.208-4.459 5.5a1.237 1.237 0 0 0-.083.542c.012.283.131.55.333.75.959 1.166 5.584 2.916 7.084 2.666a1.275 1.275 0 0 0 1.041-.837c.084-.293.125-.5.125-6.375v-2.663c.084-.625-.333-1.208-1-1.458Zm14.5 1.172c-.25-.167-.416-.25-5.957-2.084 0 0-2.417-.833-2.458-.833-.586-.25-1.25-.042-1.709.542-.459.584-.541 1.333-.166 1.916l.958 1.625c3.292 5.417 3.542 5.792 3.75 6 .375.293.833.334 1.333.125 1.417-.586 4.459-4.5 4.667-6.041.082-.505-.043-.964-.418-1.255v.005Zm-8.916-6.167c.879-.167 8.041-1.875 8.625-2.292.375-.25.586-.708.541-1.25v-.041c-.166-1.584-2.875-5.664-4.208-6.334-.458-.208-.958-.208-1.333.042-.25.167-.417.417-3.792 5.083l-1.542 2.119c-.416.5-.416 1.209 0 1.834.417.667.834.998 1.709.834v.005Z"/></svg>
+                    Yelp
+                </a>
+            </li>
+            <li>
+                <a  data-id="houzzUrl" class="houzz">
+                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#4DBC15" d="M50 25c0 13.807-11.193 25-25 25S0 38.807 0 25 11.193 0 25 0s25 11.193 25 25Z"/><path fill="#fff" d="M19.336 12.5h-5.469v25h8.399v-7.715h5.566V37.5h8.3V23.242L19.337 18.36V12.5Z"/></svg>
+                    Houzz
+                </a>
+            </li>
+            <li>
+                <a  data-id="ubereatsUrl" class="ubereats">
+                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#06C167" d="M0 8a8 8 0 0 1 8-8h34a8 8 0 0 1 8 8v34a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z"/><path fill="#000" d="M12.56 22.165c1.626 0 2.883-1.249 2.883-3.097v-7.193h1.76V23.53H15.46v-1.082c-.788.815-1.878 1.282-3.101 1.282-2.514 0-4.442-1.815-4.442-4.562v-7.292h1.76v7.192c0 1.881 1.24 3.097 2.883 3.097Zm5.95 1.365h1.677v-1.066a4.28 4.28 0 0 0 3.05 1.266c2.515 0 4.493-1.981 4.493-4.429 0-2.464-1.977-4.445-4.492-4.445a4.242 4.242 0 0 0-3.034 1.265v-4.245H18.51V23.53Zm4.61-1.282a2.936 2.936 0 0 1-2.95-2.947 2.94 2.94 0 0 1 4.076-2.722 2.932 2.932 0 0 1 1.808 2.722 2.944 2.944 0 0 1-2.933 2.947Zm9.672-7.376c-2.497 0-4.392 2.015-4.392 4.412 0 2.53 1.979 4.43 4.543 4.43 1.56 0 2.833-.684 3.688-1.816L35.407 21c-.637.85-1.475 1.249-2.463 1.249-1.443 0-2.599-1.033-2.834-2.414h6.957v-.55c0-2.53-1.81-4.412-4.275-4.412Zm-2.648 3.63c.302-1.299 1.358-2.164 2.615-2.164 1.258 0 2.313.865 2.599 2.164h-5.214Zm12.17-1.965v-1.565h-.586c-.939 0-1.626.433-2.045 1.116v-1.05h-1.677v8.492H39.7v-4.828c0-1.315.804-2.165 1.91-2.165h.704ZM7.917 26.25h8.14v1.992h-5.941v2.85H15.9v1.932h-5.784v2.89h5.942v1.992H7.917V26.25ZM38.18 38.125c2.497 0 3.903-1.196 3.903-2.85 0-1.175-.832-2.052-2.575-2.43l-1.842-.379c-1.07-.199-1.407-.398-1.407-.797 0-.518.516-.837 1.467-.837 1.03 0 1.782.28 2 1.235h2.16c-.12-1.792-1.407-2.988-4.022-2.988-2.258 0-3.843.937-3.843 2.75 0 1.255.872 2.072 2.754 2.47l2.06.478c.812.16 1.03.379 1.03.718 0 .538-.614.877-1.605.877-1.248 0-1.96-.279-2.238-1.236h-2.18c.318 1.793 1.645 2.989 4.339 2.989Zm-6.545-2.172h1.624v1.952H30.92c-1.466 0-2.278-.916-2.278-2.072v-4.582H27v-1.953h1.644v-2.45h2.18v2.45h2.436v1.953h-2.437v4.025c0 .458.317.677.813.677Z M23.92 29.299h2.16v8.607h-2.16v-.777a4.197 4.197 0 0 1-2.734.996c-2.555 0-4.555-2.012-4.555-4.523 0-2.51 2-4.523 4.555-4.523a4.197 4.197 0 0 1 2.734.997v-.777Zm.04 4.303a2.635 2.635 0 0 0-.753-1.866 2.57 2.57 0 0 0-1.842-.764 2.559 2.559 0 0 0-1.838.768 2.622 2.622 0 0 0-.757 1.862 2.649 2.649 0 0 0 .757 1.862 2.584 2.584 0 0 0 1.838.768c1.446 0 2.595-1.156 2.595-2.63Z"/></svg>
+                    Uber Eats
+                </a>
+            </li>
+            <li>
+                <a  data-id="opentableUrl" class="opentable">
+                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#DD3743" d="M50 25c0 13.807-11.193 25-25 25S0 38.807 0 25 11.193 0 25 0s25 11.193 25 25Z"/><path fill="#fff" d="M13.544 22.716c-1.405 0-2.544 1.152-2.544 2.572 0 1.422 1.14 2.573 2.544 2.573 1.405 0 2.545-1.152 2.545-2.573 0-1.42-1.14-2.572-2.545-2.572Z M18.695 25.288C18.695 19.607 23.252 15 28.872 15c5.622 0 10.178 4.607 10.178 10.288 0 5.683-4.556 10.29-10.178 10.29-5.62 0-10.177-4.607-10.177-10.29Zm7.633 0c0 1.421 1.139 2.573 2.544 2.573s2.545-1.152 2.545-2.573c0-1.42-1.14-2.572-2.545-2.572s-2.544 1.152-2.544 2.572Z"/></svg>
+                    OpenTable
+                </a>
+            </li>
+            <li>
+                <a  data-id="deliverooUrl" class="deliveroo">
+                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#35BEB2" d="M0 8a8 8 0 0 1 8-8h34a8 8 0 0 1 8 8v34a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z"/><path fill="#fff" d="M22.037 25.173a.044.044 0 0 0 .027-.018c.006-.01.009-.021.006-.033l-2.422-11.28a.103.103 0 0 1 .014-.076.1.1 0 0 1 .063-.044l7.413-1.548c.13-.027.21.024.238.155l2.386 11.066a.014.014 0 0 0 .014.012c.003 0 .007 0 .01-.003a.015.015 0 0 0 .006-.008.348.348 0 0 0 .014-.07c.467-4.382.932-8.763 1.393-13.145.008-.068.02-.113.038-.134a.122.122 0 0 1 .111-.046l7.608.8a.107.107 0 0 1 .09.075.106.106 0 0 1 .003.041c-.678 6.413-1.358 12.833-2.036 19.26a3.97 3.97 0 0 1-.326 1.21 1295.113 1295.113 0 0 1-4.187 9.25.125.125 0 0 1-.137.07l-19.371-4.056a.08.08 0 0 1-.062-.062l-1.928-9.01a.1.1 0 0 1 .014-.075.099.099 0 0 1 .062-.043l10.959-2.288Zm3.435 3.69c.241-1.398-2.155-2.012-2.497-.597-.265 1.098.32 1.678 1.409 1.637.586-.023.994-.498 1.088-1.04Zm5.455.893a1.123 1.123 0 0 0-.663-1.438l-.252-.093a1.114 1.114 0 0 0-1.434.665l-.088.24a1.123 1.123 0 0 0 .662 1.437l.253.093a1.115 1.115 0 0 0 1.434-.664l.088-.24Z"/></svg>
+                    Deliveroo
+                </a>
+            </li>
+            <li>
+                <a  data-id="genericUrl" class="generic">
+                    <svg width="20px" height="20px" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="#000000" class="icon-social"><path class="color1" d="M400.4,341.5c-20.2,0-38.7,7.1-53.3,18.9l-151.6-89.9c0.9-4.9,1.4-9.8,1.4-14.9c0-5-0.5-9.9-1.4-14.7l151.4-89.3 c14.6,11.8,33.2,19,53.5,19c47,0,85.2-38.2,85.2-85.2S447.4,0,400.4,0s-85.2,38.2-85.2,85.2c0,5,0.5,9.9,1.4,14.7l-151.4,89.3 c-14.6-11.8-33.2-19-53.5-19c-47,0-85.2,38.2-85.2,85.2s38.2,85.2,85.2,85.2c20.2,0,38.7-7.1,53.3-18.9l151.6,89.9 c-0.9,4.9-1.4,9.8-1.4,14.9c0,47,38.2,85.2,85.2,85.2s85.2-38.2,85.2-85.2S447.4,341.5,400.4,341.5z M400.4,59.8 c14,0,25.4,11.4,25.4,25.4s-11.4,25.4-25.4,25.4c-14,0-25.4-11.4-25.4-25.4S386.4,59.8,400.4,59.8z M111.6,281 c-14,0-25.4-11.4-25.4-25.4c0-14,11.4-25.4,25.4-25.4c14,0,25.4,11.4,25.4,25.4C137,269.6,125.6,281,111.6,281z M400.4,452.2 c-14,0-25.4-11.4-25.4-25.4c0-14,11.4-25.4,25.4-25.4c14,0,25.4,11.4,25.4,25.4C425.8,440.8,414.4,452.2,400.4,452.2z"/></svg>
+                    Generic
+                </a>
+            </li>
+            
+            <li>
                 <a  data-id="facebookUrl" class="facebook">
                     <svg width="20px" height="20px" viewBox="0 0 50 50" enable-background="new 0 0 50 50" xml:space="preserve">
                         <path fill="#36609F" d="M42.188,0H7.813C3.499,0,0,3.5,0,7.813v34.375C0,46.502,3.499,50,7.813,50h34.375C46.502,50,50,46.502,50,42.188V7.813C50,3.5,46.502,0,42.188,0z M30.355,25.009h-3.503c0,5.601,0,12.491,0,12.491h-5.191c0,0,0-6.824,0-12.491h-2.469v-4.413h2.469v-2.856c0-2.041,0.971-5.239,5.238-5.239l3.846,0.015V16.8c0,0-2.338,0-2.791,0c-0.454,0-1.102,0.229-1.102,1.202v2.594h3.958L30.355,25.009z"
Index: src/js/JEditor/ParamsPanel/Views/SocialNetworks.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/SocialNetworks.js	(révision 12509)
+++ src/js/JEditor/ParamsPanel/Views/SocialNetworks.js	(révision 12510)
@@ -21,6 +21,19 @@
             this._template = this.buildTemplate(template, translate);
             this.ChildViews={};
             this.networks = {
+                airbnbUrl: "airbnb",
+                bookingUrl: "booking",
+                appleUrl: "apple",
+                googleplayUrl: "googleplay",
+                vimeoUrl: "vimeo",
+                doordashUrl: "doordash",
+                snapchatUrl: "snapchat",
+                yelpUrl: "yelp",
+                houzzUrl: "houzz",
+                ubereatsUrl: "ubereats",
+                opentableUrl: "opentable",
+                deliverooUrl: "deliveroo",
+                genericUrl: "generic",
                 facebookUrl: 'facebook',
                 twitterUrl: 'twitter',
                 xUrl: 'x',
Index: src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 12509)
+++ src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 12510)
@@ -1,5 +1,18 @@
 define({
         "socialNetworks": "Réseaux sociaux",
+        "Invalid_airbnbUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_bookingUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_appleUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_googleplayUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_vimeoUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_doordashUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_snapchatUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_yelpUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_houzzUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_ubereatsUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_opentableUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_deliverooUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_genericUrl":"L'url renseignée ne semble pas valide",
         "Invalid_slideshareUrl":"L'url renseignée ne semble pas valide",
         "Invalid_whatsappUrl":"L'url renseignée ne semble pas valide",
         "Invalid_wazeUrl":"L'url renseignée ne semble pas valide",
Index: src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 12509)
+++ src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 12510)
@@ -1,5 +1,18 @@
 define({
         "socialNetworks": "Réseaux sociaux",
+        "Invalid_airbnbUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_bookingUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_appleUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_googleplayUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_vimeoUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_doordashUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_snapchatUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_yelpUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_houzzUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_ubereatsUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_opentableUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_deliverooUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_genericUrl":"L'url renseignée ne semble pas valide",
         "Invalid_slideshareUrl":"L'url renseignée ne semble pas valide",
         "Invalid_whatsappUrl":"L'url renseignée ne semble pas valide",
         "Invalid_wazeUrl":"L'url renseignée ne semble pas valide",
Index: .
===================================================================
--- .	(révision 12509)
+++ .	(révision 12510)

Property changes on: .
___________________________________________________________________
Added: svn:mergeinfo
## -0,0 +0,1 ##
   Fusionné /branches/ideo3_v2/integration:r12486-12509
