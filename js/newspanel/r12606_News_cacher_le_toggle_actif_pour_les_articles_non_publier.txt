Revision: r12606
Date: 2024-07-15 16:42:51 +0300 (lts 15 Jol 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: cacher le toggle actif pour les articles non publier

## Files changed

## Full metadata
------------------------------------------------------------------------
r12606 | sraz<PERSON><PERSON><PERSON>oa | 2024-07-15 16:42:51 +0300 (lts 15 Jol 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Models/Article.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/articlesTable.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js

News: cacher le toggle actif pour les articles non publier
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Templates/articlesTable.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/articlesTable.html	(révision 12605)
+++ src/js/JEditor/NewsPanel/Templates/articlesTable.html	(révision 12606)
@@ -59,7 +59,7 @@
 				</tr>
 			<%} else {%>
 			<%_.each(content,function(item){ %>
-			<tr class="<%=(!item.active)?'disabled':''%>" data-cid="<%= item.cid %>">
+			<tr class="<%=(!item.active && item.ispublier)?'disabled':''%>" data-cid="<%= item.cid %>">
 				<td><span class="img-news"> <img src="<%=item.image%>" alt="titre"></span></td>
 				<td class="title">
 					<%=item.title%>
@@ -88,7 +88,7 @@
 					});%>
 				</td>
 				<td>
-					<span class="switch"><span></span></span>
+					<%if(item.ispublier){%> <span class="switch"><span></span></span><%}%>
 					<span class="globe-icon">
 						<%if(item.numberOfTranslation > 0){%> 
 							<span class="badge"><%=item.numberOfTranslation%></span>
Index: src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 12605)
+++ src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 12606)
@@ -113,7 +113,8 @@
           numberOfTranslation: list.numberOfTranslation,
           active : (pageModel)?pageModel.active : false,
           content:list.content,
-          state : list.state
+          state : list.state,
+          ispublier: list.ispublier
         }
         return json;
       });
@@ -121,8 +122,8 @@
     },
     getHtmlDatePub: function (list){
       var html = '<span> - </span>';
-      if(list.programmingDate) html = '<span class="disabled"><span><i class="icon icon-clic_rdv-icon"></i></span>'+ moment(list.programmingDate.date).format("DD/MM/YY")+'</span>';
-      if(list.publicationDate) html = '<span>'+ moment(list.publicationDate.date).format("DD/MM/YY")+'</span>';
+      if(list.programmingDate) html = '<span class="disabled"><span><i class="icon icon-clic_rdv-icon"></i></span>'+ moment(list.programmingDate).format("DD/MM/YY")+'</span>';
+      if(list.publicationDate) html = '<span>'+ moment(list.publicationDate).format("DD/MM/YY")+'</span>';
       return html
     },
     onSwitchClick: function(event) {
@@ -167,38 +168,6 @@
       }
       return false;
     },
-    onError: function () {
-      this.stopListening(this.article, Events.BackboneEvents.SYNC, this.onSave);
-      $.toast({
-        text: translate("DeleteError"), 
-        type:'error',
-        appendTo:'#news-editor .main',
-        showHideTransition: 'fade', 
-        hideAfter: 5000, 
-        position: 'top-right', 
-        textAlign: 'left', 
-        allowToastClose:false,
-        loader:false
-    });
-    },
-
-    onSave: function () {
-        this.stopListening(this.article, Events.BackboneEvents.ERROR, this.onError);
-        this.trigger(Events.LoadEvents.LOAD_SUCCESS, this);
-        $.toast({
-            text: translate("DeleteSuccesful"), 
-            icon: 'icon-check-circle', 
-            type:'success',
-            appendTo:'#news-editor .main',
-            showHideTransition: 'fade', 
-            hideAfter: 3000, 
-            position: 'top-right', 
-            textAlign: 'left', 
-            allowToastClose:false,
-            loader:false
-        });
-                      
-    },
     deleteModel: function(model) {
       if (!this.app.user.can('delete_page'))
         return;
Index: src/js/JEditor/NewsPanel/Models/Article.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Article.js	(révision 12605)
+++ src/js/JEditor/NewsPanel/Models/Article.js	(révision 12606)
@@ -43,6 +43,8 @@
                             this.pageCollection = PageCollection.getInstance();
                             this.lastState = this.toJSONData();
                             this.allLastState = this.toJSON();
+                            this.ispublier = (this.publicationDate)?true:false;
+                            this.isprogram = (this.programmingDate)?true:false;
                             this.on(Events.BackboneEvents.SYNC, this._onSync);
                         },
                         getCategoryModel: function(){
@@ -67,6 +69,12 @@
                         _onSync: function(model, response, options) {
                             this.lastState = this.toJSONData();
                             this.allLastState = this.toJSON();
+                            this.ispublier = (this.publicationDate)?true:false;
+                            this.isprogram = (this.programmingDate)?true:false;
+                            if (this.programmingDate && this.content.state == 0) {
+                                // il faut afficher un message s'il veux updaté le contenu programmé
+                                this.trigger(Events.ArticleEvents.ARTICLE_UP, this);
+                            }
                         },
                         hasUnsavedChanges: function() {
                             return !(_.isEqual(this.toJSONData(), this.lastState) && _.isEqual(this.content.content.toJSON(), this.content.content.lastState));
@@ -84,6 +92,7 @@
                             this.set('category', this.lastState.category);
                             this.set('title', this.lastState.title);
                             this.set('introduction', this.lastState.introduction);
+                            this.trigger(Events.ArticleEvents.ARTICLE_CANCEL, this);
                         },
                         cancelParams: function() {
                                this.metaTitle =  this.allLastState.metaTitle;
@@ -122,5 +131,13 @@
                     });
                    
             Article.SetAttributes(['category', 'state', 'numberOfTranslation','title', 'lang','ressource','introduction', 'publicationDate','programmingDate', 'metaTitle', 'metaOpengraph', "page", "content","news"]);
+            Events.extend(
+                {
+                    ArticleEvents : {
+                        ARTICLE_CANCEL : 'cancel:currentArticle',
+                        ARTICLE_UP : 'up:currentArticle',
+                       
+                    }
+                });
             return Article;
         });
\ No newline at end of file
