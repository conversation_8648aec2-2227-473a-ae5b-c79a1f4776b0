Revision: r12822
Date: 2024-08-20 09:41:41 +0300 (tlt 20 Aog 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: ajout bouton draggable dans la navigationPanel

## Files changed

## Full metadata
------------------------------------------------------------------------
r12822 | srazanandralisoa | 2024-08-20 09:41:41 +0300 (tlt 20 Aog 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Links/Models/Link.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Menus/Models/MenuItem.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NavigationPanel/NavigationPanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NavigationPanel/Views/DraggableMenuItemView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NavigationPanel/Views/MenuItemView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NavigationPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NavigationPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NavigationPanel/nls/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Models/NewsNav.js
   A /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Models/NewsNavCollection.js

News: ajout bouton draggable dans la navigationPanel
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Menus/Models/MenuItem.js
===================================================================
--- src/js/JEditor/Commons/Menus/Models/MenuItem.js	(révision 12821)
+++ src/js/JEditor/Commons/Menus/Models/MenuItem.js	(révision 12822)
@@ -261,6 +261,8 @@
           if (this.link.isPage() && this.link.getPage())
             icon = this.link.getPage()
             .getIcon();
+          else if (this.link.isNews())
+          icon = 'icon-News';
           else
             icon = 'icon-link';
           break;
Index: src/js/JEditor/NavigationPanel/Views/DraggableMenuItemView.js
===================================================================
--- src/js/JEditor/NavigationPanel/Views/DraggableMenuItemView.js	(révision 12821)
+++ src/js/JEditor/NavigationPanel/Views/DraggableMenuItemView.js	(révision 12822)
@@ -109,6 +109,25 @@
         view.exclude(excludedItems);
         return view;
     };
+    DraggableMenuItemView.fromNews = function(opts) {
+        opts.toMenuItem = function(model) {
+            var link = {type: LinkType.NEWS, href: model.href, name: model.name, lang: this.lang};
+            var item = {type: MenuItem.LINK_TYPE, link: link};
+            return item;
+        };
+        opts.messages = {
+            empty: 'emptyWebsite',
+            addToManage: 'createPageToManageMenus',
+            createNew: 'addPage',
+            elementsName: 'News'
+        };
+        opts.defaultIcon = 'icon-News';
+        opts.icon = function() {
+            return 'icon-News';
+        };
+        var view = new DraggableMenuItemView(opts);
+        return view;
+    };
     DraggableMenuItemView.fromMenus = function(opts) {
         opts.messages = {
             empty: 'noMenus',
Index: src/js/JEditor/NavigationPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NavigationPanel/nls/fr-ca/i18n.js	(révision 12821)
+++ src/js/JEditor/NavigationPanel/nls/fr-ca/i18n.js	(révision 12822)
@@ -58,6 +58,7 @@
 	"placeholderHref":"http://google.com",
 	"myPageslpsupport" :"Mes pages supports",
 	"emptylp":" Page support vide",
+	"News":"Actualités",
 	"copy":"Copié",
 
 });
Index: src/js/JEditor/NavigationPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NavigationPanel/nls/fr-fr/i18n.js	(révision 12821)
+++ src/js/JEditor/NavigationPanel/nls/fr-fr/i18n.js	(révision 12822)
@@ -58,5 +58,6 @@
 	"placeholderHref":"http://google.com",
 	"myPageslpsupport" :"Mes pages supports",
 	"emptylp":" Page support vide",
+	"News":"Actualités",
 	"copy":"Copié",
 });
Index: src/js/JEditor/NavigationPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NavigationPanel/nls/i18n.js	(révision 12821)
+++ src/js/JEditor/NavigationPanel/nls/i18n.js	(révision 12822)
@@ -59,6 +59,7 @@
 		"introNavEnd":"Let's go !",
 		"myPageslpsupport" :"My support pages",
 		"emptylp":"Empty support page",
+		"News":"News",
         "copy":"Copied",
 	},
 	"fr-fr":true, "fr-ca":true
Index: src/js/JEditor/NewsPanel/Models/NewsNav.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/NewsNav.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Models/NewsNav.js	(révision 12822)
@@ -0,0 +1,15 @@
+define(["JEditor/Commons/Ancestors/Models/Model"
+],function(Model){ 
+    var NewsNav = Model.extend(
+    {
+
+
+        defaults: {
+            name: "News",
+            href: "",
+            type: "news"
+        }
+    });
+    NewsNav.SetAttributes(['name','href','type','lang']);
+    return NewsNav;
+});
Index: src/js/JEditor/Commons/Links/Models/Link.js
===================================================================
--- src/js/JEditor/Commons/Links/Models/Link.js	(révision 12821)
+++ src/js/JEditor/Commons/Links/Models/Link.js	(révision 12822)
@@ -4,6 +4,8 @@
   "JEditor/Commons/Links/Models/LinkType",
   "collection!JEditor/Commons/Pages/Models/PageCollection",
   "collection!JEditor/Commons/Pages/Models/PageSupportCollection",
+  "JEditor/NewsPanel/Models/NewsNavCollection",
+  "JEditor/Commons/Languages/Models/ContentLanguageList",
   "collection!JEditor/Commons/Files/Models/FileDBCollection",
   "JEditor/FilePanel/Models/FileCollection",
   "collection!JEditor/Commons/Files/Models/FileGroupCollection",
@@ -14,6 +16,8 @@
   LinkType,
   PageCollection,
   PageSupportCollection,
+  NewsNavCollection,
+  ContentLanguageList,
   FileDBCollection,
   FileCollection,
   FileGroupCollection,
@@ -55,6 +59,8 @@
       initialize: function () {
         this._super();
         this.pageList = PageCollection.getInstance();
+        this.languages = ContentLanguageList.getInstance();
+        this.newsList = NewsNavCollection.getInstance(this.languages);
         this.pageSupportList = PageSupportCollection.getInstance();
         this.file = null;
         this.fileGroupList = FileGroupCollection.getInstance();
@@ -77,6 +83,9 @@
         case types.PAGE:
           datas = this._pageDefaults;
           break;
+        case types.NEWS:
+          datas = this._pageDefaults;
+          break;
         case types.IMAGE:
           datas = this._imageDefaults;
           if (this.context && (this.context instanceof File) && this.context.isImg())
@@ -130,6 +139,13 @@
        *
        * @returns {Boolean}
        */
+      isNews: function () {
+        return this.type === LinkType.NEWS;
+      },
+      /**
+       *
+       * @returns {Boolean}
+       */
       isExternal: function () {
         return this.type === LinkType.EXTERNAL;
       },
@@ -172,6 +188,8 @@
         if (this.type === types.PAGE && this.getPage())
           ret.name = this.getPage()
           .name;
+        else if (this.type === types.NEWS )
+          ret.name = this.name;
         if (this.type === types.IMAGE && this.context)
           ret.name = this.context.title;
         if (this.type === types.FILE && this.getFile())
Index: src/js/JEditor/NavigationPanel/NavigationPanel.js
===================================================================
--- src/js/JEditor/NavigationPanel/NavigationPanel.js	(révision 12821)
+++ src/js/JEditor/NavigationPanel/NavigationPanel.js	(révision 12822)
@@ -5,6 +5,7 @@
     "JEditor/Commons/Ancestors/Views/PanelView",
     "collection!JEditor/Commons/Pages/Models/PageCollection",
     "collection!JEditor/Commons/Pages/Models/PageSupportCollection",
+    "JEditor/NewsPanel/Models/NewsNavCollection",
     "collection!JEditor/Commons/Menus/Models/MenuCollection",
     "JEditor/NavigationPanel/Views/DraggableMenuItemView",
     "JEditor/Commons/Languages/Views/LanguagesDropDown",
@@ -21,6 +22,7 @@
         PanelView,
         PageCollection,
         PageSupportCollection,
+        NewsNavCollection,
         MenuCollection,
         DraggableMenuItemView,
         LanguagesDropDown,
@@ -75,8 +77,10 @@
                         load: function () {
                             var that = this;
                             this.pageList = PageCollection.getInstance();
+                            this.newsList = NewsNavCollection.getInstance(this.languages);
                             this.pageSupportList = PageSupportCollection.getInstance();
                             this.menuList = MenuCollection.getInstance();
+                            this.currentNewsList = this.newsList;
                             this.listenToOnce(this.menuList, Events.BackboneEvents.SYNC, _.bind(function () {
                                 this._pagesByLang = this.pageList.groupBy('lang');
                                 this._pagesSupportByLang = this.pageSupportList.groupBy('lang');
@@ -130,7 +134,8 @@
                             this.childViews.langDropDown = new LanguagesDropDown({collection: this.languages, _default: this.currentLang, defaultLabel: 'language'});
                             this.childViews.menuPanelView = new MenuPanelView({collection: this.menuList, lang: this.currentLang.id});
                             if(this.app.user.can('access_panel_news', __IDEO_NEWS__)){ 
-                                this.childViews.newsMenuPanelView = new NewsMenuPanelView({collection: this.pageList, lang: this.currentLang.id});
+                                this.childViews.newsMenuPanelView = new DraggableMenuItemView.fromNews({collection: this.newsList, lang: this.currentLang.id});
+                                // this.childViews.newsMenuPanelView = new NewsMenuPanelView({collection: this.pageList, lang: this.currentLang.id});
                             }
                             this.childViews.menuListView.hide(false);
                             this.listenTo(this.childViews.menuPanelView, Events.MenuPanelViewEvents.MENU_CHANGED, this._onMenuChanged);
Index: src/js/JEditor/NavigationPanel/Views/MenuItemView.js
===================================================================
--- src/js/JEditor/NavigationPanel/Views/MenuItemView.js	(révision 12821)
+++ src/js/JEditor/NavigationPanel/Views/MenuItemView.js	(révision 12822)
@@ -9,8 +9,9 @@
     "JEditor/Commons/Links/Models/LinkType",
     "collection!JEditor/Commons/Pages/Models/PageCollection",
     "collection!JEditor/Commons/Pages/Models/PageSupportCollection",
+    "JEditor/NewsPanel/Models/NewsNavCollection",
     'i18n!../nls/i18n'
-], function(BabblerView, Events, MenuItem, MenuItemEditView, MenuItemCollection, MenuItemTemplate, Link, LinkType, PageCollection, PageSupportCollection, translate) {
+], function(BabblerView, Events, MenuItem, MenuItemEditView, MenuItemCollection, MenuItemTemplate, Link, LinkType, PageCollection, PageSupportCollection, NewsNavCollection, translate) {
     var MenuItemView = BabblerView.extend({
         tagName: 'li',
         events: {
@@ -232,13 +233,35 @@
                               if(!jqueryElement.hasClass("ui-sortable-helper")) { 
                                   var pageList = PageCollection.getInstance() ; 
                                   var pageSupportList = PageSupportCollection.getInstance();
-                                  var model = (pageList.get(cidDraggable))? pageList.get(cidDraggable) : pageSupportList.get(cidDraggable) ;
-                              
-                                  var link = new Link({
-                                    name: model.name,
-                                    href: model.id,
-                                    type: LinkType.PAGE
-                                  });
+                                  var pageNewsList = NewsNavCollection.getInstance();
+                                  if(pageList.get(cidDraggable)){
+                                    var model = pageList.get(cidDraggable);
+                                    var link = new Link({
+                                        name: model.name,
+                                        href: model.id,
+                                        type: LinkType.PAGE
+                                      });
+                                  }
+                                  else if(pageSupportList.get(cidDraggable)){
+                                    var model = pageSupportList.get(cidDraggable);
+                                    var link = new Link({
+                                        name: model.name,
+                                        href: model.id,
+                                        type: LinkType.PAGE
+                                      });
+                                  }
+                                  else{
+                                    var model = pageNewsList.get(cidDraggable);
+                                    var link = new Link({
+                                        name: model.name,
+                                        href: model.href,
+                                        lang: model.lang,
+                                        type: LinkType.NEWS
+                                      });
+                                  }
+                                   
+
+                                 
                                   var menuItem = new MenuItem({
                                     type: MenuItem.LINK_TYPE,
                                     link: link
Index: src/js/JEditor/NewsPanel/Models/NewsNavCollection.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/NewsNavCollection.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Models/NewsNavCollection.js	(révision 12822)
@@ -0,0 +1,52 @@
+define([
+    "JEditor/Commons/Ancestors/Models/Collection",
+    "./NewsNav"
+], function(
+    Collection,
+    NewsNav      
+) {
+    var NewsNavCollection = Collection.extend({
+        /**
+         * Constructeur de la collection
+         */
+        translations :{
+            "Français": { "news": "Actualités" },
+            "English": { "news": "News" },
+            "default": { "news": "News"}
+        },
+        constructor: function(models, options) {
+            
+            if (arguments.callee.caller !== NewsNavCollection.getInstance) {
+                throw new TypeError("Impossible d'instancier un NewsNavCollection directement. Utilisez NewsNavCollection.getInstance()");
+            } else {
+                // Appelle le constructeur parent
+                Collection.apply(this, arguments);
+                
+            }
+        },
+        addLangs:function(langs){
+            var self = this; 
+            langs.forEach(function(element) {
+                var translation = self.translations[element.name];
+                var lang = element.id.replace('_', '-').toLowerCase();
+                var langpath = (__IDEO_DEFAULT_LANG__ === lang)? '' : '/'+lang;
+                var href = "[[ptr]]"+ langpath +"/news";
+                self.add(new NewsNav({lang: element.id, name : translation.news, href: href , lang : element.id})); 
+            });
+        },
+        model: NewsNav
+    });
+
+    NewsNavCollection.instance = null;
+
+    NewsNavCollection.getInstance = function(langs) {
+        if (this.instance === null) {
+            this.instance = new NewsNavCollection();
+            this.instance.reset();
+            this.instance.addLangs(langs);
+        }
+        return this.instance;
+    };
+
+    return NewsNavCollection;
+});
