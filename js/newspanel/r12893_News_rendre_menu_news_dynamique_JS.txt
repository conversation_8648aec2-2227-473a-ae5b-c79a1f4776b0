Revision: r12893
Date: 2024-08-22 11:33:06 +0300 (lkm 22 Aog 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: rendre menu news dynamique(JS)

## Files changed

## Full metadata
------------------------------------------------------------------------
r12893 | sraz<PERSON><PERSON><PERSON>oa | 2024-08-22 11:33:06 +0300 (lkm 22 Aog 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NavigationPanel/Views/MenuItemView.js

News: rendre menu news dynamique(JS)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NavigationPanel/Views/MenuItemView.js
===================================================================
--- src/js/JEditor/NavigationPanel/Views/MenuItemView.js	(révision 12892)
+++ src/js/JEditor/NavigationPanel/Views/MenuItemView.js	(révision 12893)
@@ -251,7 +251,7 @@
                                       });
                                   }
                                   else{
-                                    if (this.app.user.can('access_panel_news', __IDEO_NEWS__)){
+                                    if (that.app.user.can('access_panel_news', __IDEO_NEWS__)){
                                         var pageNewsList = NewsNavCollection.getInstance();
                                         var model = pageNewsList.get(cidDraggable);
                                         var link = new Link({
