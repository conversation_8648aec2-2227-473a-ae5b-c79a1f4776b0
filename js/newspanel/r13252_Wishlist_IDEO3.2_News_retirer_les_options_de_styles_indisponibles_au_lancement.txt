Revision: r13252
Date: 2024-10-18 10:49:23 +0300 (zom 18 Okt 2024) 
Author: frahajanirina 

## Commit message
Wishlist IDEO3.2: News : retirer les options de styles indisponibles au lancement

## Files changed

## Full metadata
------------------------------------------------------------------------
r13252 | frahajanirina | 2024-10-18 10:49:23 +0300 (zom 18 Okt 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/globalConfig.html

Wishlist IDEO3.2: News : retirer les options de styles indisponibles au lancement
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Templates/globalConfig.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/globalConfig.html	(révision 13251)
+++ src/js/JEditor/NewsPanel/Templates/globalConfig.html	(révision 13252)
@@ -42,11 +42,11 @@
                     <% var _id= _.uniqueId('newsStyle'); %>
                     <div><span class="effect-radio <%=(newsStyle==1)?'active':''%>" id="<%=_id %>" data-value="1" data-helper="grid"><span class="helper"><span class="help"><%=__("gridLegend")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-photogrid-block"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
                     <% var _id= _.uniqueId('newsStyle'); %>
-                    <div><span class="effect-radio <%=(newsStyle==2)?'active':''%>" id="<%=_id %>" data-value="2" data-helper="list"><span class="helper"><span class="help"><%=__("listLegend")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-nav-menu"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
+                    <div class="hidden"><span class="effect-radio <%=(newsStyle==2)?'active':''%>" id="<%=_id %>" data-value="2" data-helper="list"><span class="helper"><span class="help"><%=__("listLegend")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-nav-menu"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
                 </div>
             </div>
         </article>
-        <article class="panel-option" id="formatImage" <%=(newsStyle!=1)?'style="display:none"':''%>>
+        <article class="panel-option hidden" id="formatImage" <%=(newsStyle!=1)?'style="display:none"':''%>>
             <header>
                 <span class="option-name label"><%=__("FormatImage")%></span>
                 <p class="panel-content-legend"><%=__("FormatImageLegend")%></p>
@@ -113,8 +113,8 @@
                     </label>
                     
                     <%  var _id=_.uniqueId('newsStyleAffichage');%>
-                    <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="3" id="<%=_id %>" <%=(newsStyleAff==3)?'checked':''%>>
-                        <label for="<%=_id %>">
+                    <input type="radio" class="select-box hidden"  name="AffichageCarrouselStyle" value="3" id="<%=_id %>" <%=(newsStyleAff==3)?'checked':''%>>
+                        <label for="<%=_id %>" class="hidden">
                         <div class="wrapper">
                             <div class="horizontal-wrap">
                                 <span class="icon-wrapper">
@@ -127,8 +127,8 @@
                     </label>
     
                     <%  var _id=_.uniqueId('newsStyleAffichage');%>
-                    <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="4" id="<%=_id %>" <%=(newsStyleAff==4)?'checked':''%>>
-                        <label for="<%=_id %>">
+                    <input type="radio" class="select-box hidden"  name="AffichageCarrouselStyle" value="4" id="<%=_id %>" <%=(newsStyleAff==4)?'checked':''%>>
+                        <label for="<%=_id %>" class="hidden">
                         <div class="wrapper">
                             <div class="horizontal-wrap">
                                 <span class="icon-wrapper">
@@ -141,8 +141,8 @@
                     </label>
     
                     <%  var _id=_.uniqueId('newsStyleAffichage');%>
-                    <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="5" id="<%=_id %>" <%=(newsStyleAff==5)?'checked':''%>>
-                        <label for="<%=_id %>">
+                    <input type="radio" class="select-box hidden"  name="AffichageCarrouselStyle" value="5" id="<%=_id %>" <%=(newsStyleAff==5)?'checked':''%>>
+                        <label for="<%=_id %>" class="hidden">
                         <div class="wrapper">
                             <div class="horizontal-wrap">
                                 <span class="icon-wrapper">
@@ -155,8 +155,8 @@
                     </label>
     
                     <%  var _id=_.uniqueId('newsStyleAffichage');%>
-                    <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="6" id="<%=_id %>" <%=(newsStyleAff==6)?'checked':''%>>
-                        <label for="<%=_id %>">
+                    <input type="radio" class="select-box hidden"  name="AffichageCarrouselStyle" value="6" id="<%=_id %>" <%=(newsStyleAff==6)?'checked':''%>>
+                        <label for="<%=_id %>" class="hidden">
                         <div class="wrapper">
                             <div class="horizontal-wrap">
                                 <span class="icon-wrapper">
