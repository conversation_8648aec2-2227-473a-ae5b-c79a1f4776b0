Revision: r12451
Date: 2024-06-20 16:15:46 +0300 (lkm 20 Jon 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: rendre dynamic le nombre d'article dans le categorie liste

## Files changed

## Full metadata
------------------------------------------------------------------------
r12451 | srazanandralisoa | 2024-06-20 16:15:46 +0300 (lkm 20 Jon 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/Article.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/Categorie.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/CategorieLang.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/categorieList.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/AddArticleView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/ArticleEditorView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/SaveButton.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/i18n.js

News: rendre dynamic le nombre d'article dans le categorie liste
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Models/Article.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Article.js	(révision 12450)
+++ src/js/JEditor/NewsPanel/Models/Article.js	(révision 12451)
@@ -73,6 +73,8 @@
                         },
                         setCategorY: function (categorie) {
                             this.category[0]=categorie;
+                            this.categoryModel = null;
+                            this.getCategoryModel();
                         },
                         getFile: function () {
                             if (this.file) return this.file ;
@@ -91,13 +93,6 @@
                             return 0;
                         }
                     });
-            // Events.extend(
-            // {
-            //     ArticleAddEvents : {
-            
-            //         ADDED : 'add:newsarticle',
-            //     }
-            //  });
                    
             Article.SetAttributes(['category', 'state', 'numberOfTranslation','title', 'lang','ressource','introduction', 'publicationDate','programmingDate', 'metaTitle', 'metaOpengraph', "page", "content","news"]);
             return Article;
Index: src/js/JEditor/NewsPanel/Models/Categorie.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Categorie.js	(révision 12450)
+++ src/js/JEditor/NewsPanel/Models/Categorie.js	(révision 12451)
@@ -2,10 +2,12 @@
     "underscore",
     "JEditor/Commons/Events",
     "JEditor/Commons/Ancestors/Models/Model",
+    "JEditor/NewsPanel/Models/CategorieLang",
     "JEditor/FilePanel/Models/FileCollection"
 ],function(_,
         Events,
         Model,
+        CategorieLang,
         FileCollection
 ){ var Categorie = Model.extend(
                 {
@@ -49,6 +51,27 @@
                     parse : function(data) {
                         return (data.data)? data.data : data;
                     },
+                    parse: function(data) {
+                        item = (data.data)? data.data : data;
+                        var category = {
+                                id: item.id,
+                                ressource: item.ressource,
+                                numberArticlesInCagetory: item.numberArticlesInCagetory,
+                                lang: {}
+                        };
+                        // Parcourir les langues disponibles et créer les objets correspondants
+                        _.each(item.lang, function(langData, langKey) {
+                            category.lang[langKey] = new CategorieLang({
+                                title: langData.title,
+                                description: langData.description,
+                                metaTitle: langData.metaTitle,
+                                metaDescription: langData.metaDescription,
+                                url: langData.url,
+                                nbArticle: 0
+                            });
+                        });
+                        return category;
+                    }
                 });
             Categorie.SetAttributes(['ressource','lang','numberArticlesInCagetory']);
         return Categorie;
Index: src/js/JEditor/NewsPanel/Models/CategorieLang.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/CategorieLang.js	(révision 12450)
+++ src/js/JEditor/NewsPanel/Models/CategorieLang.js	(révision 12451)
@@ -8,9 +8,10 @@
             description: "",                
             metaTitle:"", 
             metaDescription: "",
-            url:""
+            url:"",
+            nbArticle: 0
         },
     });
-    CategorieLang.SetAttributes(['title','description', 'metaTitle', 'metaDescription', "url"]);
+    CategorieLang.SetAttributes(['title','description', 'metaTitle', 'metaDescription', "url", "nbArticle"]);
     return CategorieLang;
 });
Index: src/js/JEditor/NewsPanel/Templates/categorieList.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/categorieList.html	(révision 12450)
+++ src/js/JEditor/NewsPanel/Templates/categorieList.html	(révision 12451)
@@ -5,7 +5,7 @@
                 <li class="categorie-nav-list <%= categorie==current?'edit':''%> ">
                     <a data-cid="<%= categorie.cid %>" > <%= categorie.lang[currentlang].title %>
                         <span class="action">
-                            <span class="number"><%= categorie.numberArticlesInCagetory %></span>
+                            <span class="number"><%= categorie.lang[currentlang].nbArticle %></span>
                             <span class="icon icon-add" data-model="<%=categorie.cid%>"></span>
                         </span>
                     </a>
Index: src/js/JEditor/NewsPanel/Views/AddArticleView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/AddArticleView.js	(révision 12450)
+++ src/js/JEditor/NewsPanel/Views/AddArticleView.js	(révision 12451)
@@ -43,6 +43,7 @@
             this.fileCollection = new FileCollection();
             this._categories = CategorieCollection.getInstance();
             this.collection = ArticlesCollection.getInstance();
+            this.languages = this.options.languages;
             this.translations = translate.translations;
             this._onLoaded();
         },
@@ -230,7 +231,21 @@
                     title: translate("saveAction"),
                     message: translate("CategoryRequired")
                 });
+            }else {
+                var currentArticles = this.collection.where({ news: this.model.news });
+                selected = this._currentCategory;
+
+                currentArticles.forEach(function(article) {
+                    if (!selected.lang || !selected.lang[article.lang]) {
+                        valid = false;
+                        this.error({
+                            title: translate("saveAction"),
+                            message: translate("errorChangeCategoryTraduction", {'lang': article.lang})
+                        });
+                    }
+                });
             }
+
             return valid;
         },
       
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12450)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12451)
@@ -89,6 +89,14 @@
                                     this.listenToOnce(this.categories, "sync", onLoaded);
                                     this.listenToOnce(this.articles, "sync", onLoaded);
                                     this.listenToOnce(this.newsConfig, "sync", onLoaded);
+                                    var that = this;
+                                    this.articles.on('add', function(article) {
+                                        that.addArticleOnCategory(article);
+                                    });
+                                    
+                                    this.articles.on('remove', function(article) {
+                                        that.removeArticleOnCategory(article);
+                                    });
 
                                     this.categories.fetch();
                                     this.articles.fetch();
@@ -134,6 +142,16 @@
                                     this.listenTo(this.childViews.langDropDown, "selected:choice", this._onLangSelect);
                             
                                 },
+                                addArticleOnCategory:function (article){
+                                    var categorie = article.getCategoryModel();
+                                    categorie.lang[article.lang].nbArticle++;
+                                    this.categories.trigger('change');
+                                },
+                                removeArticleOnCategory:function (article){
+                                    var categorie = article.getCategoryModel();
+                                    categorie.lang[article.lang].nbArticle--;
+                                    this.categories.trigger('change');
+                                },
                                 /**
                                  * met à jour la vue de page (zone du centre de l'écran)
                                  */
Index: src/js/JEditor/NewsPanel/Views/ArticleEditorView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticleEditorView.js	(révision 12450)
+++ src/js/JEditor/NewsPanel/Views/ArticleEditorView.js	(révision 12451)
@@ -68,6 +68,7 @@
                 category: (this.model.category.length > 0)?this.model.category[0]: null,
                 language : this.options.language,
                 collection:  this.options.categorieCollection,
+                languages: ContentLanguageList.getInstance(),
             });
             self = this;
             this.listenTo( this.articleDetail, Events.ArticleAddEvents.ADDED, _.bind(function(article) {
Index: src/js/JEditor/NewsPanel/Views/SaveButton.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/SaveButton.js	(révision 12450)
+++ src/js/JEditor/NewsPanel/Views/SaveButton.js	(révision 12451)
@@ -109,14 +109,22 @@
                          */
                         saveArticle: function (e) {
                             var that = this;
+                            var valid = true;
                             this.listenToOnce(this.model, Events.BackboneEvents.SYNC, this.onSave);
                             this.listenToOnce(this.model, Events.BackboneEvents.ERROR, this.onError);
-                               
-                            this.model.customized = true;
-                            
-                            this.model.save();
+                            var currentArticles = this.model.collection.where({ news: this.model.news });
+                            selected = this.model.getCategoryModel();
+                            currentArticles.forEach(function(article) {
+                                if (!selected.lang || !selected.lang[article.lang]) {
+                                    valid = false;
+                                    that.error({
+                                        title: translate("saveAction"),
+                                        message: translate("errorChangeCategoryTraduction", {'lang': article.lang})
+                                    });
+                                }
+                            });
+                            if(valid) this.model.save();
                            
-
                             return false;
                         }
                     });
Index: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12450)
+++ src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12451)
@@ -90,6 +90,12 @@
 	 "saveAddArticle" : "Creer une article",
 	 "categoryLangNotFound" : "Categorie qui n'existe pas encore au langue selectionner",
 	 "addArticle" : "Ajout Article",
+	 "errorCategoryTraduction" : "Pour traduire cet article, veuillez vous assurer que la catégorie existe bien dans le langue <strong><% lang %></strong>.",
+	 "errorChangeCategoryTraduction" : "Cet article à des traductions associées en <strong><% lang %></strong>. Pour changer la catégorie de l'article, veuillez vous assurer que la nouvelle catégorie existe bien dans les langues <strong><% lang %></strong>.",
+	 "alreadyTraduice" : "Cet article existe déjà dans le langue <strong><% lang %></strong>.",
+	 "translationSuccessfull": "Traduction de l'article avec success",
+	 "CategoryRequired":"Veuillez choisr une catégorie avant de sauvegarder",
+	 "RessourceRequired":"Veuillez choisr un image à la Une avant de sauvegarder", "errorChangeCategoryTraduction" : "Cet article à des traductions associées en <strong><% lang %></strong>. Pour changer la catégorie de l'article, veuillez vous assurer que la nouvelle catégorie existe bien dans les langues <strong><% lang %></strong>.",
 
 	//available
 	"addContent":"Ajouter du contenu",
Index: src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 12450)
+++ src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 12451)
@@ -90,6 +90,12 @@
 	 "saveAddArticle" : "Creer une article",
 	 "categoryLangNotFound" : "Categorie qui n'existe pas encore au langue selectionner",
 	 "addArticle" : "Ajout Article",
+	 "errorCategoryTraduction" : "Pour traduire cet article, veuillez vous assurer que la catégorie existe bien dans le langue <strong><% lang %></strong>.",
+	 "errorChangeCategoryTraduction" : "Cet article à des traductions associées en <strong><% lang %></strong>. Pour changer la catégorie de l'article, veuillez vous assurer que la nouvelle catégorie existe bien dans les langues <strong><% lang %></strong>.",
+	 "alreadyTraduice" : "Cet article existe déjà dans le langue <strong><% lang %></strong>.",
+	 "translationSuccessfull": "Traduction de l'article avec success",
+	 "CategoryRequired":"Veuillez choisr une catégorie avant de sauvegarder",
+	 "RessourceRequired":"Veuillez choisr un image à la Une avant de sauvegarder", "errorChangeCategoryTraduction" : "Cet article à des traductions associées en <strong><% lang %></strong>. Pour changer la catégorie de l'article, veuillez vous assurer que la nouvelle catégorie existe bien dans les langues <strong><% lang %></strong>.",
 
 	 //available
 	 "addContent":"Ajouter du contenu",
Index: src/js/JEditor/NewsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12450)
+++ src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12451)
@@ -91,6 +91,12 @@
         "dateProg" : "",
         "categoryLangNotFound" : "Categorie qui n'existe pas encore au langue selectionner",
         "addArticle" : "Ajout Article",
+        "errorCategoryTraduction" : "Pour traduire cet article, veuillez vous assurer que la catégorie existe bien dans le langue <strong><% lang %></strong>.",
+        "errorChangeCategoryTraduction" : "Cet article à des traductions associées en <strong><% lang %></strong>. Pour changer la catégorie de l'article, veuillez vous assurer que la nouvelle catégorie existe bien dans les langues <strong><% lang %></strong>.",
+        "alreadyTraduice" : "Cet article existe déjà dans le langue <strong><% lang %></strong>.",
+        "translationSuccessfull": "Traduction de l'article avec success",
+        "CategoryRequired":"Veuillez choisr une catégorie avant de sauvegarder",
+        "RessourceRequired":"Veuillez choisr un image à la Une avant de sauvegarder",
 
         //available
         "addContent":"Ajouter du contenu",
