Revision: r12768
Date: 2024-08-16 10:23:07 +0300 (zom 16 Aog 2024) 
Author: jn.harison 

## Commit message
IDEO3.2 NEWS: Ajout sélection pages actualités dans les boutons(cô<PERSON> J<PERSON>)

## Files changed

## Full metadata
------------------------------------------------------------------------
r12768 | jn.harison | 2024-08-16 10:23:07 +0300 (zom 16 Aog 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Pages/Templates/pageSelector.html
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Pages/Templates/pageSelectorWrapper.html
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Pages/Views/PageSelectorWrapper.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Pages/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Pages/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/nls/fr-fr/i18n.js

IDEO3.2 NEWS: Ajout sélection pages actualités dans les boutons(côté JS)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Pages/Templates/pageSelector.html
===================================================================
--- src/js/JEditor/Commons/Pages/Templates/pageSelector.html	(révision 12767)
+++ src/js/JEditor/Commons/Pages/Templates/pageSelector.html	(révision 12768)
@@ -35,6 +35,9 @@
                             <% break; case 8: %>
                             <!-- Landing page -->
                             <div class="radio-label"><span class="icon-landing-page"></span><%= page.name %></div> 
+                            <% break; case 9: %>
+                           <!-- News page -->
+                           <div class="radio-label"><span class="icon-page"></span><%= page.name %></div> 
                           <% break; } %>
                     </div>
                     <% if(!page.active&&!isCurrentPage){ %>
Index: src/js/JEditor/Commons/Pages/Templates/pageSelectorWrapper.html
===================================================================
--- src/js/JEditor/Commons/Pages/Templates/pageSelectorWrapper.html	(révision 12767)
+++ src/js/JEditor/Commons/Pages/Templates/pageSelectorWrapper.html	(révision 12768)
@@ -5,10 +5,14 @@
         </h1>
         <% var _id=_.uniqueId('display_disabled') %>
         <% var _id2=_.uniqueId('display_disabled') %>
+        <% var _id3=_.uniqueId('display_disabled') %>
         <input type="checkbox" id="<%=_id%>" value="showhiddenpage" name="box"/><label for="<%=_id%>" class="label"><span class="checkbox"><span class="icon-checked"></span><span class="icon-unchecked"></span></span><%= __("disabledDisplayed") %></label>
         <% if(user.can("access_lpsupport_page") && storage ) {%>
             <input type="checkbox" id="<%=_id2%>" value="showlp" name="box"/><label for="<%=_id2%>" class="label"><span class="checkbox"><span class="icon-checked"></span><span class="icon-unchecked"></span></span><%= __("OnlyShowLPSupport") %></label>
          <%}%>
+        <% if(user.can('access_panel_news', __IDEO_NEWS__)) {%>
+            <input type="checkbox" id="<%=_id3%>" value="showNews" name="box"/><label for="<%=_id3%>" class="label"><span class="checkbox"><span class="icon-checked"></span><span class="icon-unchecked"></span></span><%= __("OnlyShowNews") %></label>
+        <%}%>
     </header>
     
 </div>
\ No newline at end of file
Index: src/js/JEditor/Commons/Pages/Views/PageSelectorWrapper.js
===================================================================
--- src/js/JEditor/Commons/Pages/Views/PageSelectorWrapper.js	(révision 12767)
+++ src/js/JEditor/Commons/Pages/Views/PageSelectorWrapper.js	(révision 12768)
@@ -73,6 +73,8 @@
                     });
                 }else if(targetValue==="showlp"){
                     this.childViews.pageSelector.filter({type:"LPsupport"});
+                }else if(targetValue==="showNews"){
+                    this.childViews.pageSelector.filter({type:"news", active:true});
                 }
             }else{
                 if(this.app.user.can("access_lpsupport_page")){
Index: src/js/JEditor/Commons/Pages/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/Commons/Pages/nls/fr-fr/i18n.js	(révision 12767)
+++ src/js/JEditor/Commons/Pages/nls/fr-fr/i18n.js	(révision 12768)
@@ -1 +1 @@
-define({"pageName":"Nom de la page","setAsHome":"Définir comme page d'accueil","homelessLang":"Attention, vous n'avez pas encore défini de page d'accueil pour cette langue","batchActions":"Traitement par lots","selectAll":"Tout sélectionner","selectNone":"Tout déselectionner","enable":"Activer","disable":"Désactiver","lock":"Vérouiller","unlock":"Dévérouiller","chooseLayout":"Choisir la disposition","duplicate":"Dupliquer","delete":"Supprimer","prevent404":"Cette page est désactivé, si vous ne l'activez pas, votre lien ne sera pas fontionnel, voulez-vous l'activer?","pageEnabled":"La page a été activée avec succès","pageDisabled":"Lapage n'a pas été activée","pages":"Pages","disabledDisplayed":"Afficher les pages désactivées","OnlyShowLPSupport":"Afficher uniquement les pages supports","preview":"Aperçu","addContent":"Ajouter du contenu","selectPage":"Sélectionnez une page","ok":"Ok","cancel":"Annuler"});
\ No newline at end of file
+define({"pageName":"Nom de la page","setAsHome":"Définir comme page d'accueil","homelessLang":"Attention, vous n'avez pas encore défini de page d'accueil pour cette langue","batchActions":"Traitement par lots","selectAll":"Tout sélectionner","selectNone":"Tout déselectionner","enable":"Activer","disable":"Désactiver","lock":"Vérouiller","unlock":"Dévérouiller","chooseLayout":"Choisir la disposition","duplicate":"Dupliquer","delete":"Supprimer","prevent404":"Cette page est désactivé, si vous ne l'activez pas, votre lien ne sera pas fontionnel, voulez-vous l'activer?","pageEnabled":"La page a été activée avec succès","pageDisabled":"Lapage n'a pas été activée","pages":"Pages","disabledDisplayed":"Afficher les pages désactivées","OnlyShowLPSupport":"Afficher uniquement les pages supports", "OnlyShowNews":"Afficher uniquement les pages actualités","preview":"Aperçu","addContent":"Ajouter du contenu","selectPage":"Sélectionnez une page","ok":"Ok","cancel":"Annuler"});
\ No newline at end of file
Index: src/js/JEditor/Commons/Pages/nls/i18n.js
===================================================================
--- src/js/JEditor/Commons/Pages/nls/i18n.js	(révision 12767)
+++ src/js/JEditor/Commons/Pages/nls/i18n.js	(révision 12768)
@@ -20,6 +20,7 @@
         "pages":"Pages",
         "disabledDisplayed":"Display enabled pages",
         "OnlyShowLPSupport":"Display only support pages",
+        "OnlyShowNews":"Display only news pages",
         "preview":"Preview",
         "addContent":"Add content",
         "selectPage":"Select a page",
Index: src/js/JEditor/PagePanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/nls/fr-fr/i18n.js	(révision 12767)
+++ src/js/JEditor/PagePanel/nls/fr-fr/i18n.js	(révision 12768)
@@ -32,6 +32,7 @@
     "pages": "Pages",
     "disabledDisplayed": "Afficher les pages d\u00e9sactiv\u00e9es",
     "OnlyShowLPSupport": "Afficher uniquement les pages supports",
+    "OnlyShowNews":"Afficher uniquement les pages actualit\u00e9s",
     "preview": "Aper\u00e7u",
     "changeLayout": "Changer le mod\u00e8le de page",
     "okay": "Valider",
