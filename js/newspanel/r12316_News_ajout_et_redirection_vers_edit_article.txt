Revision: r12316
Date: 2024-05-28 16:16:22 +0300 (tlt 28 Mey 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: ajout et redirection vers edit article

## Files changed

## Full metadata
------------------------------------------------------------------------
r12316 | s<PERSON><PERSON><PERSON><PERSON><PERSON> | 2024-05-28 16:16:22 +0300 (tlt 28 Mey 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/Commons/Pages/Models/Page.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Templates/publishConfig.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Views/PublishConfigView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/Article.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/ZoneToolbox.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/addArticle.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/categorieList.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/AddArticleView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/ArticleEditorView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/CategorieCollectionView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/ZoneToolBox.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/i18n.js
   M /branches/ideo3_v2/integration_news/src/less/imports/news_panel/main.less

News: ajout et redirection vers edit article
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Pages/Models/Page.js
===================================================================
--- src/js/JEditor/Commons/Pages/Models/Page.js	(révision 12315)
+++ src/js/JEditor/Commons/Pages/Models/Page.js	(révision 12316)
@@ -40,6 +40,10 @@
                                     if (!attributes.lang || attributes.lang === '')
                                         return translate("noLang");
                                 },
+                                fetchPageById: function(pageId, options) {
+                                    var url = __IDEO_API_PATH__ + '/pages/' + pageId;
+                                    return this.fetch(_.extend({}, options, { url: url }));
+                                },
                                 getIcon: function() {
                                     switch (this.attributes.accueil) {
                                         case 0:
Index: src/js/JEditor/NewsPanel/Templates/addArticle.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/addArticle.html	(révision 12315)
+++ src/js/JEditor/NewsPanel/Templates/addArticle.html	(révision 12316)
@@ -28,7 +28,7 @@
     <% if(create){ %>
         <div class="flex-button">
         <button class="button annuler"> <%=__('cancel')%></button>
-        <button class="button saveNewArticle bg-blue">   <%=__('saveAdd')%> </button>
+        <button class="button saveNewArticle bg-blue">   <%=__('saveAddArticle')%> </button>
     <% } %>  
     </div>
     </div>
Index: src/js/JEditor/NewsPanel/Views/CategorieCollectionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/CategorieCollectionView.js	(révision 12315)
+++ src/js/JEditor/NewsPanel/Views/CategorieCollectionView.js	(révision 12316)
@@ -23,6 +23,7 @@
       },
       events: {
         'click ul li.categorie-nav-list a[data-cid]': '_onCategorieClick',
+        'click ul li.categorie-nav-list a .icon-add' : 'addArticleClick'
       },
       language: null,
       fadeInEffect: 'fadeIn',
@@ -113,7 +114,22 @@
         this.trigger(Events.ChoiceEvents.SELECT, this, this.collection.get(cid));
         return false;
       },
+
+      addArticleClick: function(event){
+        event.stopPropagation();
+        var $target = $(event.currentTarget);
+        var cid = $target.data('model');
+        this.$('.edit').removeClass('edit');
+        $target.parent().parent().addClass('edit');
+        this.trigger(Events.CategorieCollectionViewEvents.ADD_ARTICLE, this.collection.get(cid));
+      }
+
     });
+    Events.extend({
+      CategorieCollectionViewEvents: {
+          ADD_ARTICLE: 'add:article'
+      }
+  });
   
     return CategorieCollectionView;
   });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Articles/Templates/publishConfig.html
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Templates/publishConfig.html	(révision 12315)
+++ src/js/JEditor/NewsPanel/Articles/Templates/publishConfig.html	(révision 12316)
@@ -1,6 +1,6 @@
 <div id="meta-configuration" class="meta-configuration scrollbar-classic">
     <header class="panel-head">
-        <span class="icon icon-params"></span>
+        <span class="icon icon-rotation-right"></span>
         <h1 class="panel-name">Publication</h1>
     </header>
     <div class="panel-content active">
@@ -10,29 +10,41 @@
     </div>
     <div class="option-content ">
        <div for="meta-title">
-        <span class="icon icon-html"></span>
+        <span class="icon icon-calendar-line"></span>
         <span>Programmez l'envoi</span>
        </div>
         <p> Programmez une date de publication de l'article</p>
-      <!-- toogle -->
-      <!-- datepicker -->
+        
       <div class="batch">
-        <p>
-            <a href="#">
-                <span class="switch batchActions">
-                    <span></span>
-                </span>
-                Oui / nom
-            </a>
-        </p>
-        <div class="btn-group-batch">
+         <!-- toogle -->
+            <span class="switch batchActions">
+                <span></span>
+            </span>
+            <span class="labelnon">Non</span>
+            <span class="labeloui">Oui</span>
+        </div> 
+        <div class="btn-group-batch forProgram">
             <div class="block-datepicker">
-                <label for="dateprog"><%= __("dateProg")%></label> <br>
-                <input class="datepicker  custom-input from" name="dateProg" type="text" placeholder="<%= __("datePlaceholder")%>">
+                <div id="datepicker">
+                     <!-- datepicker -->
             </div>
+
+            <div>Votre article sera publié le <span class="dateprog"></span></div>
+            <div>
+                <button>
+                    <span class="icon icon-rotation-right"></span>
+                    <span>Programmer la publication</span>
+                </button>
+            </div>
         </div>
-        <div>Votre article sera publié immédiatement</div>
-
-      </div>
+        <div class="forPub">
+            <div>Votre article sera publié immédiatement</div>
+            <div>
+                <button>
+                    <span class="icon icon-rotation-right"></span>
+                    <span>Publié l'article</span>
+                </button>
+            </div>
+        </div>
     </div>
 </div>
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Articles/Views/PublishConfigView.js
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Views/PublishConfigView.js	(révision 12315)
+++ src/js/JEditor/NewsPanel/Articles/Views/PublishConfigView.js	(révision 12316)
@@ -6,7 +6,8 @@
     "text!JEditor/NewsPanel/Articles/Templates/publishConfig.html",
     "JEditor/App/Views/RightPanelView",
     "i18n!JEditor/NewsPanel/nls/i18n",
-], function ($, _, Events, View, template, RightPanelView, translate) {
+    "JEditor/Commons/Utils",
+], function ($, _, Events, View, template, RightPanelView, translate,  Utils) {
     
     /**
      * 
@@ -21,15 +22,16 @@
         _currentCid: null,
         _programmed:false,
         events: {
-            'click .batch': '_onClick', 
+            'click .switch': '_onClick', 
             'click .button.cancel': '_onCancelClick', 
             'click .panel-menu>a': '_onMenuItemClick'
         },
-        _onClick: function(e) {
-            this.switchprogram.toggleClass('active');
+        _onClick: function(event) {
             this._programmed = !this._programmed;
+            this.datePickerRender();
             return false;
-          },
+        
+         },
         constructor: function(options) {
             if (!options.rightPanelView || !(options.rightPanelView instanceof RightPanelView))
                 throw new TypeError("le constructeur attend un argument option.rightPanelView de type RightPanelView"); 
@@ -41,7 +43,7 @@
             this.listenTo(this.rightPanelView, 'overlayClick', this.cancel);
             this.rightPanelView.addContent(this);
             this.currentLang = this.options.currentLang;
-            this.switchprogram = this.$('.batch');
+            this._programmed = (this.model.programmingDate) ? true:false;
             return this;
         },
          /**
@@ -69,34 +71,41 @@
          */
         render: function () {
             this.undelegateEvents();
-            if (!this.model.lang[this.options.currentLang.id]) {
+            if (!this.model) {
                return false;
             }
-            var params = this.model.lang[this.options.currentLang.id]
+            var params = this.model
             this.$el.html(this.template(params));
-            this.$('.datepicker').datepicker({
-                dateFormat: "dd/mm/yy",
-                onClose: function (dateText) {
-                    if (dateText) {
-                        $(this).next().show(0);
-                    } else {
-                        $(this).next().hide(0);
-                    }
-                }
+            this.$('#datepicker').datepicker({
+                dateFormat: 'dd-mm-yy', // Format de la date
+                onSelect: this.updateModel.bind(this) 
             });
-            this.$el.toggleClass(); 
+            this.switchprogram = this.$(".switch");
             this.datePickerRender();
             this.delegateEvents();
             return this;
         },
+        updateModel: function(selectedDate) {
+            this.model.set('programmingDate', selectedDate);
+        },
         datePickerRender : function (){
             if (this._programmed ) {
-                this.$('.block-datepicker').show();
-                if (this.model.dateprog!='') {
-                    $(".datepicker" ).datepicker('setDate', this.model.dateprog);
+                this.$('.forProgram').show();
+                this.$('.forPub').hide();
+                this.$('.labeloui').show();
+                this.$('.labelnon').hide();
+                this.switchprogram.parent().removeClass('disabled');
+                if (this.model.programmingDate!='') {
+                    $("#datepicker" ).datepicker('setDate', this.model.programmingDate);
                 }
             }
-            else this.$('.block-datepicker').hide();
+            else {
+                this.switchprogram.parent().addClass('disabled');
+                this.$('.forProgram').hide();
+                this.$('.forPub').show();
+                this.$('.labeloui').hide();
+                this.$('.labelnon').show();
+            }
         },
         save: function() {
             this.model.save();
Index: src/js/JEditor/NewsPanel/Models/Article.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Article.js	(révision 12315)
+++ src/js/JEditor/NewsPanel/Models/Article.js	(révision 12316)
@@ -44,8 +44,8 @@
                             if (!category) category = this.get('category');
                             var categories = CategorieCollection.getInstance();
                     
-                            if (Array.isArray(category)) {
-                                return category.map(function(categoryId) {
+                            if (Array.isArray(category) && category.length > 0 ) {
+                                this.category = category.map(function(categoryId) {
                                     if (!(categoryId instanceof Categorie)) {
                                         return categories.findWhere({id: categoryId});
                                     } else {
@@ -53,9 +53,9 @@
                                     }
                                 });
                             } else if (!(category instanceof Categorie)) {
-                                return categories.findWhere({id: category});
+                                this.category = categories.findWhere({id: category});
                             }
-                            return category;
+                            return this.category;
                         },
                     
                         transformPage: function(page) {
@@ -62,11 +62,23 @@
                             if (!page) page = this.get('page');
                             if (page && !(page instanceof Page)) {
                                 var pageCollection = PageCollection.getInstance();
-                                this.page = pageCollection.findWhere({id: this.page});
+                                pageModel = pageCollection.findWhere({id: page});
+                                if (pageModel) {
+                                    this.page = pageModel;
+                                }else {
+                                    var pageModel = new Page();
+                                    pageModel.fetchPageById(page).success(function(data) {
+                                        this.page = new Page(data);
+                                        this.trigger(Events.ArticleAddEvents.ADDED, this);
+                                    }.bind(this)).error(function(error) {
+                                        console.log(error)
+                                    });
+                                    
+                                }
                             }
-                            return page;
+                            return this.page;
                         },                    
-                        _onSync: function() {
+                        _onSync: function(model, response, options) {
                             this.lastState = this.toJSON();
                         },
                         hasUnsavedChanges: function() {
@@ -110,6 +122,14 @@
                             return json;
                         }
                     });
+            Events.extend(
+            {
+                ArticleAddEvents : {
+            
+                    ADDED : 'add:newsarticle',
+                }
+             });
+                   
             Article.SetAttributes(['category', 'state','title', 'lang','ressource','introduction', 'publicationDate','programmingDate', 'metaTitle', 'metaOpengraph', "page", "content"]);
             return Article;
         });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12315)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12316)
@@ -15,8 +15,10 @@
     "./Models/Article",
     "JEditor/App/Views/RightPanelView",
     "JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView",
+    "JEditor/NewsPanel/Articles/Views/PublishConfigView",
+    "i18n!./nls/i18n",
     "JEditor/PagePanel/Contents/Blocks/Blocks",
-    "i18n!./nls/i18n"
+    "jqueryPlugins/affix"
 ],
         function (
             $,
@@ -35,7 +37,7 @@
             Article,
             RightPanelView,
             AvailableView,
-            Blocks,
+            PublishConfigView,
             translate
             ) {
             var NewsPanel = PanelView.extend(
@@ -47,6 +49,7 @@
                                     'click .addCategory':'onAddCategorieClick',
                                     'click .addArticle':'onAddArticleClick',
                                     'click #available-blocks-trigger' : 'showAvailableBlocks',
+                                    'click #publish-config-trigger' : 'showPublishConfig',
                                     'click #show-zone-version': 'showZoneVersionsPanel',
                                 },
                                 cssFile: __IDEO_INTEGRATION_PATH__ + "/build/css/news_panel.css",
@@ -232,6 +235,10 @@
                                     // listeners
                                     this.listenTo(this.childViews.langDropDown, Events.ChoiceEvents.SELECT, this._onLangSelect);
                                     this.listenTo(this.childViews.categorieList, Events.ChoiceEvents.SELECT, this._onCategorieSelect);
+                                    this.listenTo(this.childViews.categorieList, Events.CategorieCollectionViewEvents.ADD_ARTICLE, _.bind(function(categorie) {
+                                         this.childViews.newsEditorView.renderAddArticle(categorie);
+                                    }, this));
+                                    
                                     this.listenTo(this.childViews.categorieList, 'render', this._scrollbar);
                                     this.listenTo(this.childViews.newsEditorView, Events.NewsEditorViewEvents.SHOWRIGHTPANEL, this.showGlobalConfigView);
                                     this.render();
@@ -263,7 +270,7 @@
                                 */
                                 renderRightPanel : function(content, renderChild) {
                                     // setup args
-                                    var content     = content || this.childViews.availableBlocksView;
+                                    var content     = content || this.childViews.availableBlocksView || this.childViews.publishConfigView;
                                     var renderChild = renderChild || true;
                                     
                                     this.rightPanelView.clear();
@@ -391,22 +398,6 @@
                                                 dontAskAgain : true
                                             }
                                         });
-                                    } else if ((this.currentArticle && this.currentArticle.hasUnsavedChanges()) || 
-                                    (this.currentZone && this.currentZone.hasUnsavedChanges())) {
-                                        this.confirmUnsaved({
-                                            message : translate("quitWithoutSaving"),
-                                            title : translate("unsavedChanges"),
-                                            type : 'delete-not-saved',
-                                            onYes : _.bind(function() {
-                                                this.currentArticle.save();
-                                                this.currentZone.save()
-                                                this.currentArticle = article;
-                                            }, this),
-                                            options : {
-                                                dialogClass : 'delete no-close',
-                                                dontAskAgain : true
-                                            }
-                                        }); 
                                     } else
                                      this.currentArticle = article;
                                 },
@@ -454,6 +445,45 @@
                                     this.rightPanelView.hideContent(this.childViews.availableBlocksView);
                                     this.rightPanelView.hidePanel();
                                 },
+                                /**
+                                 * montre le panneau de config de la publication d'une article
+                                 * @returns 
+                                 */
+                                showPublishConfig : function() {
+                                    function onClose() {
+                                        this.rightPanelView.removeContent(this.childViews.publishConfigView);
+                                        this.stopListening(this.childViews.publishConfigView);
+                                        this.rightPanelView.hidePanel();
+                                    }
+                                    try {
+                                        this.childViews.publishConfigView = new PublishConfigView({
+                                            rightPanelView : this.rightPanelView,
+                                            model: this.currentArticle,
+                                            currentLang: this.currentLang
+                                        });
+                                       
+                                        this.rightPanelView.showContent(this.childViews.publishConfigView);
+                                        this.listenToOnce(this.childViews.publishConfigView, Events.PublishConfigViewEvents.SAVE, _.bind(onClose, this))
+                                            .listenToOnce(this.childViews.publishConfigView, Events.PublishConfigViewEvents.CANCEL, _.bind(onClose, this));
+                                      
+                                    } catch (e) {
+                                        console.error(e);
+                                        this.error({
+                                            message: translate("errorPublishingElement"),
+                                            title: translate("error")
+                                        });
+                                    }
+                        
+                                  
+                                    return false;
+                                },
+                                /**
+                                * Cache le panneau de config de la publication d'une article
+                                */
+                                hidePublishConfig : function() {
+                                    this.rightPanelView.hideContent(this.childViews.publishConfigView);
+                                    this.rightPanelView.hidePanel();
+                                },
                             });
                             Object.defineProperties(NewsPanel.prototype,
                                 {
@@ -517,7 +547,11 @@
                                     },
                                     currentZone : {
                                         get : function() {
-                                            return this.childViews.newsEditorView.currentZone;
+                                            if (this.childViews.newsEditorView.articleEditorView) {
+                                                return this.childViews.newsEditorView.articleEditorView.currentZone;
+                                                
+                                            }
+                                            return null;
                                         }
                                     }
                                 });
Index: src/js/JEditor/NewsPanel/Templates/ZoneToolbox.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/ZoneToolbox.html	(révision 12315)
+++ src/js/JEditor/NewsPanel/Templates/ZoneToolbox.html	(révision 12316)
@@ -2,5 +2,5 @@
 <div class="btn-group <%=className%>">
     <button type="button" class="btn btn-default template-link-indicator <%=zone.customized?'clickable':''%>"><span class="icon <%= (zone.customized?'icon-layout-broken':'icon-layout-ok')%>"></span><% if(zone.customized){%><span class="label"><%=__('backToTemplate')%></span><% } %></button>
     <button type="button" class="btn btn-default add-content" id="available-blocks-trigger"><span class="icon icon-add"></span><span class="label"><%=__("addContent")%></span></button>
-    <button type="button" class="btn btn-default publish" id="available-blocks-trigger"><span class="icon icon-add"></span><span class="label"><%=__("publish")%></span></button>
+    <button type="button" class="btn btn-default publish" id="publish-config-trigger"><span class="icon icon-rotation-right"></span><span class="label"><%=__("publish")%></span></button>
 </div>
Index: src/js/JEditor/NewsPanel/Templates/categorieList.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/categorieList.html	(révision 12315)
+++ src/js/JEditor/NewsPanel/Templates/categorieList.html	(révision 12316)
@@ -6,7 +6,7 @@
                     <a data-cid="<%= categorie.cid %>" > <%= categorie.lang[currentlang].title %>
                         <span class="action">
                             <span class="number"><%= categorie.numberArticlesInCagetory %></span>
-                            <span class="icon icon-add " data-model="<%=categorie.cid%>"></span>
+                            <span class="icon icon-add" data-model="<%=categorie.cid%>"></span>
                         </span>
                     </a>
                 </li>
Index: src/js/JEditor/NewsPanel/Views/AddArticleView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/AddArticleView.js	(révision 12315)
+++ src/js/JEditor/NewsPanel/Views/AddArticleView.js	(révision 12316)
@@ -116,11 +116,11 @@
                 this.currentFile = file;
                 this.model.ressource = file.id;
                 this.model.file = file;
-                this.$('upload-categorie .uploader .view').addClass('done');
-                this.$('.upload-categorie .uploader .preview .imagepreview').css({opacity: 1});
-                this.$('.upload-categorie .uploader .preview .imagepreview .progressbar').css({width: 0});
+                this.$('upload-article .uploader .view').addClass('done');
+                this.$('.upload-article .uploader .preview .imagepreview').css({opacity: 1});
+                this.$('.upload-article .uploader .preview .imagepreview .progressbar').css({width: 0});
                 if( this.currentFile === "svg"){
-                    this.$('.upload-categorie .uploader .preview .imagepreview').css({
+                    this.$('.upload-article .uploader .preview .imagepreview').css({
                         backgroundSize: '100%',
                         backgroundRepeat: 'no-repeat',
                         backgroundPosition: 'center'
@@ -188,25 +188,15 @@
             
             if (this._checkInput()) {
                 this.model.save();
-
                 this.collection.add(this.model);
                 this.collection.trigger('change');
-
                 this.onSave();
-                this.trigger(Events.ArticleAddEvents.ADDED, this.model);
+               
             }
             return ;
         },
 
     });
-    Events.extend(
-        {
-            ArticleAddEvents : {
-        
-                ADDED : 'add:newsarticle',
-            }
-        });
    
-   
     return AddArticleView;
   });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Views/ArticleEditorView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticleEditorView.js	(révision 12315)
+++ src/js/JEditor/NewsPanel/Views/ArticleEditorView.js	(révision 12316)
@@ -24,7 +24,7 @@
     "JEditor/NewsPanel/Views/ConfigCategorieView",
     "i18n!../nls/i18n",
     //not in params
-   // "jqueryPlugins/dropdown",
+  
     "jqueryPlugins/affix"
 ], function($,
      _, 
@@ -68,7 +68,6 @@
         selectedZoneVersion: null,
         versionsCollection: new VersionsCollection(),
         page : null,
-        page : null,
         initialize: function () {
             this._super();
             this.currentZoneID = this.options.zoneID || null;
@@ -75,9 +74,11 @@
             this._template = this.buildTemplate(template, translate);
             if (this.model) {
                 this.zoneToolbox = new ZoneToolBox();
+                this.pagePreview = new PagePreview();
             }
-            else this.model = new Article();
-            this.pagePreview = new PagePreview();
+            else{
+                this.model = new Article();
+            } 
             this.articleDetail = new AddArticleView({
                 model: this.model,
                 category: this.model.category[0],
@@ -84,9 +85,8 @@
                 language : this.options.language,
                 collection:  this.options.categorieCollection,
             });
-            this.on(Events.LoadEvents.LOAD_SUCCESS, this.render);
             self = this;
-            this.listenTo(this.articleDetail , Events.ArticleAddEvents.ADDED, _.bind(function(article) {
+            this.listenTo(this.model, Events.ArticleAddEvents.ADDED, _.bind(function(article) {
                 self.newsPanel.currentArticle = article;
             })); 
         },
@@ -142,7 +142,8 @@
             try {
                 
                 if (this.model.page) {
-                   this.page = this.model.page
+                   this.page = this.model.page;
+
                    this.versionsCollection.zoneId = parseInt(this.page.main_zone);
             
                     this.listenToOnce(this.versionsCollection, Events.BackboneEvents.SYNC, this.onAllZoneLoad);
@@ -158,20 +159,45 @@
             return this;
         },
         onAllZoneLoad: function () {
-            var zoneversion = this.versionsCollection.max(function(model) {
-                return model.get('id');
-            });
-            var that = this;
-            this.listenToOnce(this.versionsCollection, Events.BackboneEvents.SYNC, this.onZoneLoad);
-            this.listenToOnce(this.versionsCollection, Events.BackboneEvents.ERROR, function (zoneCollection, xhr, options) {
-                this.trigger(Events.LoadEvents.LOAD_ERROR, this, xhr, options);
-            });
-            zoneversion.fetch({
-                success: function(res){
-                    var resJson = res.toJSON();
-                   that.zone = new ZoneModel(that.cleanData(resJson.zoneContent, ['parentPage', 'parentPageType']));
-                }
-            } );
+            if (this.versionsCollection.length > 0) {
+                var zoneversion = this.versionsCollection.max(function(model) {
+                    return model.get('id');
+                });
+                var that = this;
+                this.listenToOnce(zoneversion, Events.BackboneEvents.ERROR, function (zoneCollection, xhr, options) {
+                    this.trigger(Events.LoadEvents.LOAD_ERROR, this, xhr, options);
+                });
+                zoneversion.fetch({
+                    success: function(res){
+                        var resJson = res.toJSON();
+                        self.zonesFetched = true;
+                        zone = new ZoneModel(that.cleanData(resJson.zoneContent, ['parentPage', 'parentPageType']));
+                        var errors = zone.getInitErrors();
+                        if (errors.length) { 
+                            this.error({
+                                message: translate("corruptedContent"),
+                                title: translate("error")
+                            });
+                            zone.resetInitErrors();
+                        }
+                        self.setZone(zone);
+                    }
+                } );
+            }else{ 
+                // on chargera toutes les zones une par une une fois les id récupérés
+                this.listenToOnce(this.page.zones, Events.BackboneEvents.SYNC, this.onZoneLoad);
+                this.listenToOnce(this.page.zones, Events.BackboneEvents.ERROR, function (zoneCollection, xhr, options) {
+                    this.trigger(Events.LoadEvents.LOAD_ERROR, this, xhr, options);
+                });
+                //on récupère les id
+                this.page.zones.fetch({
+                    data: {
+                        page: this.page.id
+                    }
+                });
+                
+            }
+            
         },
         /**
          * Récupère toutes les versions pour une zone
@@ -186,8 +212,7 @@
             return this;
         },
         onZoneLoad: function () {
-            
-            var zone = this.zone;
+            var zone = this.page.zones.get(this.currentZoneID || this.page.main_zone);
             this.zonesFetched = true;
             var errors = zone.getInitErrors();
             if (errors.length) { 
@@ -212,8 +237,11 @@
                 languages: ContentLanguageList.getInstance(),
                 currentZoneID:this.currentZoneID
             }));
-            
-
+            this.$('header').affix({
+                offset: {
+                    top: this.checkAffixTop
+                }
+            });
             if (this.pagePreview) {
                 this.pagePreview.remove();
                 this.pagePreview = new PagePreview();
@@ -231,11 +259,6 @@
                 this.sectionCollectionView.setElement(this.$('.content-zone'));
                 this.sectionCollectionView.render();
             }
-            this.$('header').affix({
-                offset: {
-                    top: this.checkAffixTop
-                }
-            });
 
             this.dom.uploadZone = this.$('.page');
             var page = this;
@@ -249,6 +272,7 @@
 
             return this;
         },
+
         _onActionClick: function (event) {
             var $target = $(event.currentTarget);
             var action = $target.data('action');
Index: src/js/JEditor/NewsPanel/Views/ZoneToolBox.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ZoneToolBox.js	(révision 12315)
+++ src/js/JEditor/NewsPanel/Views/ZoneToolBox.js	(révision 12316)
@@ -39,6 +39,9 @@
             if (this.app.user.can("restore_zone")) {
                 this.$('.btn-group').prepend(this.childViews.switchVersionButton.el);
             }
+            _.each(this.childViews, function (view) {
+                view.render();
+            });
             this.dom[this.cid].templateIndicator = this.$('.template-link-indicator').children();
             this.delegateEvents();
             return this;
Index: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12315)
+++ src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12316)
@@ -79,5 +79,7 @@
 	 "titleArticle" : "Titre de l'article",
 	 "descriArticle": "Introduction de l'article",
 	 "DefaultMessageUploaderLogo": "Cliquez ici ou glissez-déposez une image",
+	 "saveAddArticle" : "Creer une article"
 
+
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 12315)
+++ src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 12316)
@@ -80,5 +80,7 @@
 	 "titleArticle" : "Titre de l'article",
 	 "descriArticle": "Introduction de l'article",
 	 "DefaultMessageUploaderLogo": "Cliquez ici ou glissez-déposez une image",
+	 "saveAddArticle" : "Creer une article"
 
+
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12315)
+++ src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12316)
@@ -81,6 +81,8 @@
         "titleArticle" : "Titre de l'article",
         "descriArticle": "Introduction de l'article",
         "DefaultMessageUploaderLogo": "Cliquez ici ou glissez-déposez une image",
+        "saveAddArticle" : "Creer une article",
+        "dateProg" : ""
 
     },
     "fr-fr": true,
Index: src/less/imports/news_panel/main.less
===================================================================
--- src/less/imports/news_panel/main.less	(révision 12315)
+++ src/less/imports/news_panel/main.less	(révision 12316)
@@ -218,6 +218,9 @@
 				.message-wrapper{
 					position: absolute;
 					opacity: revert-layer;
+					.message{
+						display: block;
+					}
 				}
 				 .preview {
 					float: left;
@@ -630,6 +633,11 @@
 		  float: left;
 		  position: static;
 		}
+		.zone-toolbox .btn .publish{
+			background: @newsColorLight;
+			color: #fff;
+			border-color: @newsColorLight;
+		}
 		& .zone .label,
 		& .page .label {
 		  display: none;
@@ -805,6 +813,43 @@
 		}
 	}
 }
+.switch {
+	display: inline-block;
+	width: 24px;
+	height: 14px;
+
+	background-color: @pageColor;
+	.border-radius(7px);
+
+	span {
+	  display: block;
+	  float: right;
+	  width: 10px;
+	  height: 12px;
+	  background: #fff;
+	  margin: 1px;
+	  .border-radius(4px);
+	  .box-shadow(-1px 0 1px 0 rgba(0,0,0,0.1));
+
+	}
+	&:hover {
+	  background-color: desaturate(@pageColor,30%);
+	}
+
+}
+.disabled {
+	color: #b3b3b3;
+	.switch {
+		background-color: #b2b2b2;
+		&:hover {
+		background-color: desaturate(@pageColor,50%);
+		}
+		span {
+		float: none;
+		.box-shadow(none);
+		}
+	}
+}
 #news-table {
     margin: auto;
     #search-form {
@@ -835,43 +880,7 @@
 		padding: 15px 8px;
 		text-align: left;
 		}
-		.switch {
-			display: inline-block;
-			width: 24px;
-			height: 14px;
 		
-			background-color: @pageColor;
-			.border-radius(7px);
-		
-			span {
-			  display: block;
-			  float: right;
-			  width: 10px;
-			  height: 12px;
-			  background: #fff;
-			  margin: 1px;
-			  .border-radius(4px);
-			  .box-shadow(-1px 0 1px 0 rgba(0,0,0,0.1));
-		
-			}
-			&:hover {
-			  background-color: desaturate(@pageColor,30%);
-			}
-		
-		}
-		.disabled {
-			color: #b3b3b3;
-			.switch {
-				background-color: #b2b2b2;
-				&:hover {
-				background-color: desaturate(@pageColor,50%);
-				}
-				span {
-				float: none;
-				.box-shadow(none);
-				}
-			}
-		}
 		tr td:first-child, tr th:first-child {
 			border-left: 1px solid #ddd;
 		}
@@ -942,4 +951,36 @@
 		background-color: @programColor;
 	}
 	
-}
\ No newline at end of file
+}
+
+.ui-datepicker {
+    background-color: #1a1a1a; 
+}
+
+.ui-datepicker-header {
+    background-color: #1a1a1a;
+    color: #fff;
+}
+
+.ui-datepicker-calendar {
+    background-color: #1a1a1a; 
+}
+
+.ui-datepicker-current-day {
+    background-color: #ccc; 
+}
+.ui-datepicker-today {
+    background-color: #f0f0f0; 
+}
+
+.ui-datepicker-week-end {
+    background-color: #f9f9f9; 
+}
+.ui-datepicker-title {
+    color: #333;
+}
+
+.ui-datepicker-prev, .ui-datepicker-next {
+    color: #fff; 
+    background-color: #333; 
+}
