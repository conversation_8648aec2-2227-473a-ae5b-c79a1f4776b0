Revision: r12426
Date: 2024-06-18 12:05:52 +0300 (tlt 18 Jon 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: ajout version article

## Files changed

## Full metadata
------------------------------------------------------------------------
r12426 | srazana<PERSON><PERSON>oa | 2024-06-18 12:05:52 +0300 (tlt 18 Jon 2024) | 1 ligne
Chemins modifiés :
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Versions
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Versions/Models
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Versions/Models/ArticleContent.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Versions/Models/ArticleContentCollection.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Versions/Templates
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Versions/Templates/Version.html
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Versions/Templates/VersionsCollection.html
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Versions/Templates/VersionsGroup.html
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Versions/Views
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Versions/Views/VersionView.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Versions/Views/VersionsCollectionView.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Versions/Views/VersionsGroupView.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Versions/nls
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Versions/nls/de-de
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Versions/nls/de-de/i18n.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Versions/nls/en-au
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Versions/nls/en-au/i18n.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Versions/nls/en-ca
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Versions/nls/en-ca/i18n.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Versions/nls/en-us
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Versions/nls/en-us/i18n.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Versions/nls/es-es
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Versions/nls/es-es/i18n.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Versions/nls/fr-ca
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Versions/nls/fr-ca/i18n.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Versions/nls/fr-fr
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Versions/nls/fr-fr/i18n.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Versions/nls/i18n.js

News: ajout version article
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Articles/Versions/Models/ArticleContent.js
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Versions/Models/ArticleContent.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Articles/Versions/Models/ArticleContent.js	(révision 12426)
@@ -0,0 +1,30 @@
+/* global __IDEO_API_PATH__ */
+
+define([
+    "JEditor/Commons/Ancestors/Models/Model",
+    "JEditor/Commons/Events",
+    "i18n!../nls/i18n"
+], function (Model, Events, translate) {
+    var ArticleContent = Model.extend({
+        url: function(){
+            var baseurl = __IDEO_API_PATH__ + "/news/content";
+            var contentId = this.id;
+            
+            return baseurl + '/' + contentId;
+        },
+        
+        defaults: {
+            state: null,
+            client : "",
+            content: null,
+            article_lang: null,
+            date_creation:null,
+        },
+        
+        initialize: function () {
+            this._super();
+        }
+    });
+    ArticleContent.SetAttributes([ 'state','client', 'content', 'article_lang','date_creation']);
+    return ArticleContent;
+});
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Articles/Versions/Models/ArticleContentCollection.js
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Versions/Models/ArticleContentCollection.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Articles/Versions/Models/ArticleContentCollection.js	(révision 12426)
@@ -0,0 +1,29 @@
+/* global __IDEO_API_PATH__ */
+
+define([
+    "underscore", 
+    "JEditor/Commons/Ancestors/Models/Collection", 
+    "JEditor/Commons/Events",
+    "JEditor/NewsPanel/Articles/Versions/Models/ArticleContent"
+], function (_, Collection, Events, ArticleContent) {
+    var ArticleContentsCollection = Collection.extend({
+        
+        articleLangId: null,
+        
+        url: function(){
+            var baseUrl = __IDEO_API_PATH__ + "/news/content/articlelang";
+            var articleLangId = this.articleLangId;
+            
+            return baseUrl + '/' + articleLangId;
+        },
+        
+        model: ArticleContent,
+        
+        initialize: function () {
+            Collection.prototype.initialize.apply(this, arguments);
+        }
+        
+    });
+    
+    return ArticleContentsCollection;
+});
Index: src/js/JEditor/NewsPanel/Articles/Versions/Templates/Version.html
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Versions/Templates/Version.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Articles/Versions/Templates/Version.html	(révision 12426)
@@ -0,0 +1,11 @@
+<span class="full-date">
+    <% if ( moment().format('DD/MM/YY') !== date ) { %>
+    <span class="date"><%= date %></span> - 
+    <% } %>
+    <span class="time"><%= time %></span>
+</span>
+<div class="right">
+    <span class="icon-find preview-version" data-ideotooltip="<%= __("ttPreview") %>"></span>
+    <span class="icon-refresh save-version-icon" data-ideotooltip="<%= __("ttRestore") %>"></span>
+    <span class="icon-bin delete-version" data-ideotooltip="<%= __("ttDelete") %>"></span>
+</div>
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Articles/Versions/Templates/VersionsCollection.html
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Versions/Templates/VersionsCollection.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Articles/Versions/Templates/VersionsCollection.html	(révision 12426)
@@ -0,0 +1,28 @@
+<div id="zone-versions-panel" class="version-collection scroll-container">
+    <header class="panel-head">
+        <span class="icon-refresh"></span>
+        <h1 class="panel-name"><%= __("versionOfZone")%></h1>
+    </header>
+    <div class="panel-content active">
+        <div class="panel-content-intro">
+            <%= __("availableDesc")%>
+        </div>
+    </div>
+    <div id="maxVersionWarning" class="warning">
+        <span class="icon-warning"></span><span><%= __("reachMaxVersionMsg") %></span>
+    </div>
+    <div class="option-content zone-versions availables">
+        <ul class="zone-version-items"></ul>
+        <div class="no-version">
+            <div class="icon">
+                <span class="icon-refresh"></span>
+            </div>
+            <span class="text-intro">
+                <%= __("noVersion") %>
+            </span>
+            <span class="how-to">
+                <%= __("howToZoneVersion") %>
+            </span>
+        </div>
+    </div>
+</div>
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Articles/Versions/Templates/VersionsGroup.html
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Versions/Templates/VersionsGroup.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Articles/Versions/Templates/VersionsGroup.html	(révision 12426)
@@ -0,0 +1,4 @@
+<div class="version-title">
+    <span class="indic"></span>
+    <span class="option-title"><%= versionDate %> <strong>(<%= length %>)</strong></span>
+</div>
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Articles/Versions/Views/VersionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Versions/Views/VersionView.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Articles/Versions/Views/VersionView.js	(révision 12426)
@@ -0,0 +1,173 @@
+define([
+    "underscore",
+    "JEditor/Commons/Ancestors/Views/BabblerView",
+    "JEditor/Commons/Events",
+    "JEditor/PagePanel/Contents/Zones/Models/Zone",
+    "text!JEditor/NewsPanel/Articles/Versions/Templates/Version.html",
+    "moment",
+    "JEditor/App/Config",
+    "i18n!JEditor/NewsPanel/Articles/Versions/nls/i18n"], 
+function (_, BabblerView, Events, ZoneModel,template, moment, Config, translate) {
+    
+    /**
+     * Vue qui gère une version de zone
+     * 
+     * @type VersionView
+     */
+    var VersionView = BabblerView.extend({
+        params: Config.getInstance(),
+        
+        events: {
+            "click span.delete-version": "confirmDelete",
+            "click span.preview-version": "previewVersion",
+            "click span.save-version-icon": "usePreviousVersion"
+        },
+        
+        tagName: 'li',
+        attributes: {
+            class: 'version-line'
+        },
+        
+        /**
+         * Initialisation de la view
+         * 
+         * @returns {VersionViewL#8.VersionViewAnonym$1}
+         */
+        initialize: function () {
+            this._super();
+            this.template = this.buildTemplate(template, translate);
+            return this;
+        },
+        
+        /**
+         * Rendu de la view
+         * 
+         * @returns {VersionViewL#8.VersionViewAnonym$1}
+         */
+        render: function () {
+            var param = this.model.toJSON();
+            param.date = moment(param.date_creation).format("DD/MM/YY");
+            param.time = moment(param.date_creation).format("HH:mm:ss");
+            param.moment = moment;
+            this.$el.html(this.template(param));
+            this.$el.hide();
+            
+            return this;
+        },
+        /**
+         * restauration de page en utilisant une zone antérieure. Restauration directe sans passer par l'aperçu 
+         * 
+         * @param {jQuery.Events} e L'évenement jQuery Déclencheur
+         * @returns {VersionViewL#8.VersionViewAnonym$1}
+         */
+        usePreviousVersion: function(e) {
+            e.stopPropagation();
+            var that = this;
+            this.newsPanel.listenToOnce(this.model, Events.BackboneEvents.SYNC, this.onLoadSuccess);
+            this.newsPanel.listenToOnce(this.model, Events.BackboneEvents.ERROR, this.onLoadError);
+            this.newsPanel.onLoadStart();
+            
+            this.model.fetch({
+                success: function(res){
+                    var resJson = res.toJSON();
+                    var articleEditorView = that.newsPanel.childViews.newsEditorView.articleEditorView;
+                    articleEditorView.selectedZoneVersion = new ZoneModel(resJson.content);
+                    articleEditorView.usePreviousVersion();
+                }
+            } );
+            
+            return this;
+        },
+        
+        /**
+         * Confirmation lors de la suppression de la version
+         * 
+         * @param {jQuery.Events} e L'évenement jQuery Déclencheur
+         * @returns {VersionViewL#8.VersionViewAnonym$1}
+         */
+        confirmDelete: function(e){
+            e.stopPropagation();
+            var that = this;
+            if ( ! this.params.dontAskAgainFor['deleteZoneVersion'] ) {
+                this.confirm({
+                    message: translate('confirmDeleteZoneVersion', {
+                        'name': that.model.name
+                    }),
+                    title: translate("deleteAction"),
+                    type: 'delete',
+                    onOk: _.bind(function () {
+                        that.deleteVersion();
+                    }, this),
+                    options: {
+                        dialogClass: 'delete no-close',
+                        dontAskAgain: true,
+                        subject: 'deleteZoneVersion'
+                    }
+                });
+            } else {
+                that.deleteVersion();
+            }
+            
+            return this;
+        },
+        
+        /**
+         * suppression d'une version 
+         * 
+         * @returns {VersionViewL#10.VersionViewAnonym$1}
+         */
+        deleteVersion: function () {
+            var me = $(this.el);
+            var parent = me.parents('.version-set');
+            this.newsPanel.listenToOnce(this.model, Events.BackboneEvents.SYNC, this.onLoadSuccess);
+            this.newsPanel.listenToOnce(this.model, Events.BackboneEvents.ERROR, this.onLoadError);
+            this.newsPanel.onLoadStart();
+            this.model.destroy();
+            /*
+             on réorganise manuellement la liste car le fait d'écouter le REMOVE 
+             depuis la collection et firé le reloadVersion collapse l'aggregat
+            */
+            var childLength = parent.find('li.view').length - 1;
+            if (childLength === 0) {
+                parent.remove();
+            } else {
+                parent.find('.version-title .option-title strong').text('('+ childLength +')');
+            }
+            me.remove();
+            
+            if ( $ ('#zone-versions-panel .version-set').length === 0 ) {
+                this.options.collectionView.toggleEmptyVersion();
+            }
+            return this;
+        },
+        
+        /**
+         * Mettre en aperçu la version de zone sélectionné
+         * 
+         * @param {jQuery.Events} e L'évenement jQuery Déclencheur
+         * @returns {VersionViewL#8.VersionViewAnonym$1}
+         */
+        previewVersion: function(e) {
+            e.stopPropagation();
+            var that = this;
+            
+            this.newsPanel.listenToOnce(this.model, Events.BackboneEvents.SYNC, this.onLoadSuccess);
+            this.newsPanel.listenToOnce(this.model, Events.BackboneEvents.ERROR, this.onLoadError);
+            this.newsPanel.onLoadStart();
+            
+            // on fetch parce qu'on ne charge pas tous le contenu de la zone durant le fetchall
+            // soucis de perf
+            this.model.fetch({
+                success: function(res){
+                    var resJson = res.toJSON();
+                    that.newsPanel.childViews.newsEditorView.articleEditorView.preview(null, new ZoneModel(resJson.zoneContent));
+                }
+            } );
+            
+            return this;
+        }
+        
+    } );
+    
+    return VersionView;
+});
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Articles/Versions/Views/VersionsCollectionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Versions/Views/VersionsCollectionView.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Articles/Versions/Views/VersionsCollectionView.js	(révision 12426)
@@ -0,0 +1,150 @@
+define([
+    "jquery",
+    "underscore",
+    "moment",
+    "JEditor/Commons/Ancestors/Views/View",
+    "JEditor/Commons/Events",
+    "JEditor/NewsPanel/Articles/Versions/Views/VersionView",
+    "JEditor/NewsPanel/Articles/Versions/Views/VersionsGroupView",
+    "JEditor/NewsPanel/Articles/Versions/Models/ArticleContentCollection",
+    "JEditor/App/Config",
+    "text!JEditor/NewsPanel/Articles/Versions/Templates/VersionsCollection.html",
+    "i18n!JEditor/NewsPanel/Articles/Versions/nls/i18n"
+], function ($, _, moment, View, Events, VersionView, VersionsGroupView, ArticleContentCollection, Config, tpl, translate) {
+    
+    /**
+     * Vue qui gère la liste des versions pour une zone
+     * 
+     * @type VersionsCollectionView
+     */
+    var VersionsCollectionView = View.extend({
+        
+        maxZoneVersion: 20,
+        collection: new ArticleContentCollection(),
+        
+        /**
+         * Initialisation de la view
+         * 
+         * @returns {VersionsCollectionViewL#18.VersionsCollectionViewAnonym$1}
+         */
+        initialize: function () {
+            if (this.options.newsPanel)
+                this.newsPanel = this.options.newsPanel;
+            delete this.options.newsPanel;
+            View.prototype.initialize.apply(this, arguments);
+            this.currentContentModel = this.options.currentContentModel;
+            this.listenTo(this.collection, Events.BackboneEvents.REMOVE, this.checkVersionsLength);
+            this.listenToOnce(this.collection, Events.BackboneEvents.SYNC, this.reloadVersions);
+            this.template = this.buildTemplate(tpl, translate);
+            var config = Config.getInstance();
+            
+            // max zone version
+            if ( ! isNaN(config.get('maxZoneVersion')) && config.get('maxZoneVersion') >= 0 ) 
+            {
+                this.maxZoneVersion = config.get('maxZoneVersion');
+            }
+            
+            return this;
+        },
+        
+        /**
+         * Rendu de la view
+         * 
+         * @returns {VersionsCollectionViewL#18.VersionsCollectionViewAnonym$1}
+         */
+        render: function () {
+            this.undelegateEvents();
+            this.$el.addClass('available-items');
+            this.$el.html(this.template());
+            this.fetchVersions();
+            this.scrollables();
+            $ (window).on('scroll', _.bind(this.updateScrollables, this));
+            this.delegateEvents();
+            
+            return this;
+        },
+        
+        /**
+         * Rafraichir la liste des versions pour la zone courante
+         * 
+         * @returns {VersionsCollectionViewL#18.VersionsCollectionViewAnonym$1}
+         */
+        reloadVersions: function() {
+            var $content = this.$(".zone-versions.availables .zone-version-items");
+            $content.html("");
+            
+            // Grouper les versions par période
+            var versions = this.collection.groupBy(function(model){
+                return moment(model.get('date_creation')).fromNow();
+            }, moment);
+            
+            // pour chaque versions
+            for (var i in versions) {
+                var model = versions[i];
+                var oVersionSep = new VersionsGroupView( { versionDate: i, length: model.length } );
+                var $versionSep = $ (oVersionSep.render().el);
+                for (var j = 0; j < model.length; j++) {
+                    var el = new VersionView( { model: model[j], collectionView: this } ).render().el;
+                    $versionSep.append($(el));
+                }
+                $content.append($versionSep);
+            }
+            
+            this.checkVersionsLength();
+            this.toggleEmptyVersion();
+            
+            return this;
+        },
+        
+        /**
+         * Récupère toutes les versions pour une zone
+         * 
+         * @returns {VersionsCollectionViewL#18.VersionsCollectionViewAnonym$1}
+         */
+        fetchVersions: function(){
+            this.collection.articleLangId = this.currentContentModel.articleLang;
+            this.newsPanel.listenToOnce(this.collection, Events.BackboneEvents.SYNC, this.onLoadSuccess);
+            this.newsPanel.listenToOnce(this.collection, Events.BackboneEvents.ERROR, this.onLoadError);
+            this.newsPanel.onLoadStart();
+            this.collection.fetch();
+            
+            return this;
+        },
+        
+        /**
+         * Check si le nombre max de version pour la zone est atteinte
+         * si oui, affiche le msg sinon le cache
+         * 
+         * @returns {VersionsCollectionViewL#13.VersionsCollectionViewAnonym$1}
+         */
+        checkVersionsLength: function () {
+            var $maxVersionMsg = this.$el.find('#maxVersionWarning');
+            if ( this.collection.length >= this.maxZoneVersion ) {
+                $maxVersionMsg.show();
+            } else {
+                $maxVersionMsg.hide();
+            }
+            
+            return this;
+        },
+        
+        /**
+         * Toggle le message empty quand il y a des versions ou pas
+         * 
+         * @returns {VersionsCollectionViewL#13.VersionsCollectionViewAnonym$1}
+         */
+        toggleEmptyVersion: function () {
+            // if no version
+            if ( this.collection.length === 0 ) {
+                this.$("#zone-versions-panel .no-version").show();
+            } else {
+                this.$("#zone-versions-panel .no-version").hide();
+            }
+            
+            return this;
+        }
+        
+    });
+    
+    return VersionsCollectionView;
+});
Index: src/js/JEditor/NewsPanel/Articles/Versions/Views/VersionsGroupView.js
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Versions/Views/VersionsGroupView.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Articles/Versions/Views/VersionsGroupView.js	(révision 12426)
@@ -0,0 +1,78 @@
+define([
+    "jquery",
+    "underscore",
+    "JEditor/Commons/Ancestors/Views/View",
+    "text!JEditor/PagePanel/Contents/Zones/Versions/Templates/VersionsGroup.html",
+    "i18n!JEditor/PagePanel/Contents/Zones/Versions/nls/i18n"
+], function ($, _, View, tpl, translate) {
+    
+    /**
+     * Vue qui gère une aggrégation de versions
+     * 
+     * @type VersionsGroup
+     */
+    var VersionsGroup = View.extend({
+        
+        events: {
+            'click .version-title': 'toggleViews'
+        },
+        
+        attributes: {
+            class: 'version-set',
+            type: "div"
+        },
+        
+        /**
+         * Initialisation de la view
+         * 
+         * @returns {VersionsGroupL#18.VersionsGroupAnonym$1}
+         */
+        initialize: function () {
+            View.prototype.initialize.apply(this, arguments);
+            this.template = this.buildTemplate(tpl, translate);
+            
+            return this;
+        },
+        
+        /**
+         * Rendu de la view
+         * 
+         * @returns {VersionsGroupL#18.VersionsGroupAnonym$1}
+         */
+        render: function () {
+            this.undelegateEvents();
+            this.$el.empty();
+            this.$el.html(this.template( { versionDate: this.options.versionDate, length: this.options.length } ) );
+            this.delegateEvents();
+            
+            return this;
+        },
+        
+        /**
+         * afficher/cacher l'aggregat de version sur la liste
+         * 
+         * @param {jQuery.Events} e L'évenement jQuery Déclencheur
+         * 
+         * @returns {VersionsGroupL#18.VersionsGroupAnonym$1}
+         */
+        toggleViews: function(e){
+            e.stopPropagation();
+            var me = $(e.target);
+            var versionSet = me.parents('.version-set');
+            var views = versionSet.find('li.view');
+            
+            if ( versionSet.hasClass('active') ) {
+                versionSet.removeClass('active');
+                views.hide();
+            } else {
+                versionSet.addClass('active');
+                views.show();
+            }
+            
+            return this;
+        }
+        
+    });
+    
+    return VersionsGroup;
+});
Index: src/js/JEditor/NewsPanel/Articles/Versions/nls/de-de/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Versions/nls/de-de/i18n.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Articles/Versions/nls/de-de/i18n.js	(révision 12426)
@@ -0,0 +1,2 @@
+define({
+});
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Articles/Versions/nls/en-au/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Versions/nls/en-au/i18n.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Articles/Versions/nls/en-au/i18n.js	(révision 12426)
@@ -0,0 +1,2 @@
+define({
+});
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Articles/Versions/nls/en-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Versions/nls/en-ca/i18n.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Articles/Versions/nls/en-ca/i18n.js	(révision 12426)
@@ -0,0 +1,2 @@
+define({
+});
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Articles/Versions/nls/en-us/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Versions/nls/en-us/i18n.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Articles/Versions/nls/en-us/i18n.js	(révision 12426)
@@ -0,0 +1,2 @@
+define({
+});
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Articles/Versions/nls/es-es/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Versions/nls/es-es/i18n.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Articles/Versions/nls/es-es/i18n.js	(révision 12426)
@@ -0,0 +1,2 @@
+define({
+});
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Articles/Versions/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Versions/nls/fr-ca/i18n.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Articles/Versions/nls/fr-ca/i18n.js	(révision 12426)
@@ -0,0 +1,11 @@
+define({
+    "versionOfZone": "Version de la zone",
+    "availableDesc": "Vous avez la possibilité de restaurer une version antérieure de votre zone. \n\
+                      Utilisez la chronologie et l'aperçu pour retrouver la version à restaurer.",
+    "reachMaxVersionMsg": "La limite de 20 versions par zone est atteinte. \n\
+                           Les anciennes versions seront écrasées à la prochaine sauvegarde de la page.",
+    "deleteAction": "Suppression",
+    "confirmDeleteZoneVersion": "Vous êtes sur le point de supprimer définitivement une version de zone",
+    "noVersion": "Il n'y a pas encore de version pour cette zone.",
+    "howToZoneVersion": "La sauvegarde de la page créera une version pour la zone courante."
+});
Index: src/js/JEditor/NewsPanel/Articles/Versions/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Versions/nls/fr-fr/i18n.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Articles/Versions/nls/fr-fr/i18n.js	(révision 12426)
@@ -0,0 +1,12 @@
+define({
+    "versionOfZone": "Version de la zone",
+    "availableDesc": "Vous avez la possibilité de restaurer une version antérieure de votre zone. \nUtilisez la chronologie et l'aperçu pour retrouver la version à restaurer.",
+    "reachMaxVersionMsg": "La limite de 20 versions par zone est atteinte. \nLes anciennes versions seront écrasées à la prochaine sauvegarde de la page.",
+    "deleteAction": "Suppression",
+    "confirmDeleteZoneVersion": "Vous êtes sur le point de supprimer définitivement une version de zone",
+    "noVersion": "Il n'y a pas encore de version pour cette zone.",
+    "howToZoneVersion": "La sauvegarde de la page créera une version pour la zone courante.",
+    "ttPreview": "Aperçu",
+    "ttRestore": "Restaurer",
+    "ttDelete": "Supprimer"
+});
Index: src/js/JEditor/NewsPanel/Articles/Versions/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Versions/nls/i18n.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Articles/Versions/nls/i18n.js	(révision 12426)
@@ -0,0 +1,15 @@
+define({
+    "root": {
+        "versionOfZone": "Content versions",
+        "availableDesc": "You can restore an old version of your content. \nUse the timeline and the previewer to retrieve the version you would like to restore.",
+        "reachMaxVersionMsg": "The limit of 20 versions per zone is reached. \nThe oldest version will be overwritten the next time the page is saved.",
+        "deleteAction": "Delete",
+        "confirmDeleteZoneVersion": "You are about to permanently delete a content version",
+        "noVersion": "No version recorded yet.",
+        "howToZoneVersion": "Version will be created when page is saved.",
+        "ttPreview": "Preview",
+        "ttRestore": "Restore",
+        "ttDelete": "Delete"
+    },
+    "fr-fr": true, "fr-ca": true
+});
\ No newline at end of file
