Revision: r14136
Date: 2025-04-18 11:39:08 +0300 (zom 18 Apr 2025) 
Author: frahajanirina 

## Commit message
Wishlist:news:Suppression articles/catégories par les clients

## Files changed

## Full metadata
------------------------------------------------------------------------
r14136 | frahajanirina | 2025-04-18 11:39:08 +0300 (zom 18 Apr 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Models/Categorie.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Models/CategorieLang.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/CategorieAddView.js

Wishlist:news:Suppression articles/catégories par les clients
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Models/Categorie.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Categorie.js	(révision 14135)
+++ src/js/JEditor/NewsPanel/Models/Categorie.js	(révision 14136)
@@ -95,7 +95,8 @@
                                 metaTitle: langData.metaTitle,
                                 metaDescription: langData.metaDescription,
                                 url: langData.url,
-                                nbArticle: langData.nbArticle
+                                nbArticle: langData.nbArticle,
+                                isArticleAuthorNotLinkeo: langData.isArticleAuthorNotLinkeo
                             });
                         }, this);
                         return category;
Index: src/js/JEditor/NewsPanel/Models/CategorieLang.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/CategorieLang.js	(révision 14135)
+++ src/js/JEditor/NewsPanel/Models/CategorieLang.js	(révision 14136)
@@ -9,16 +9,18 @@
             metaTitle:"", 
             metaDescription: "",
             url:"",
-            nbArticle: 0
+            nbArticle: 0,
+            isArticleAuthorNotLinkeo: false
         },
         toJSONData: function (){
            return { 
             title: this.title,
-            description: this.description,                
+            description: this.description,
+            isArticleAuthorNotLinkeo: this.isArticleAuthorNotLinkeo                
           
         };
         }
     });
-    CategorieLang.SetAttributes(['title','description', 'metaTitle', 'metaDescription', "url", "nbArticle"]);
+    CategorieLang.SetAttributes(['title','description', 'metaTitle', 'metaDescription', "url", "nbArticle", "isArticleAuthorNotLinkeo"]);
     return CategorieLang;
 });
Index: src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 14135)
+++ src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 14136)
@@ -131,6 +131,12 @@
       var articleId = $target.parent().parent().data('cid');
       var $page = $target.parents('tr');
       var article = this.collection.get(articleId);
+      var role = this.app.user.role;
+      var client = article.content.client;
+      if (role != 'root' && client == 'linkeo') {
+        
+        return false;
+      }
       if (article) {
         if (_.contains(article.state, 2)) {
           var pageModel = article.getPageModel();
@@ -144,11 +150,15 @@
     },
 
     onDeleteClick: function(event) {
-      if (!this.app.user.can('delete_page'))
-        return false;
         var $target = $(event.currentTarget);
         var articleId = $target.parent().parent().data('cid');
         var model = this.collection.get(articleId);
+        var role = this.app.user.role;
+        var client = model.content.client;
+        if (role != 'root' && client == 'linkeo') {
+          
+          return false;
+        }
       if (!this.app.params.dontAskAgainFor['deletePage']) {
         this.confirm({
           message: translate('confirmDeleteArticle', {
@@ -171,8 +181,6 @@
       return false;
     },
     deleteModel: function(model) {
-      if (!this.app.user.can('delete_page'))
-        return;
       this.article = model;
       this.listenToOnce(this.article, Events.BackboneEvents.SYNC, this.onDone);
       this.listenToOnce(this.article, Events.BackboneEvents.ERROR, this.onError);
Index: src/js/JEditor/NewsPanel/Views/CategorieAddView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(révision 14135)
+++ src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(révision 14136)
@@ -226,8 +226,13 @@
             this.render()
         },
         removeClick: function (event){
-            if (!this.app.user.can('delete_page'))
-            return false;
+            var isArticleAuthorNotLinkeo = this.model.lang[this.lang.id].isArticleAuthorNotLinkeo;
+            var nbArticle = this.model.lang[this.lang.id].nbArticle;
+            var role = this.app.user.role;
+            if (role != 'root' && nbArticle > 0 && isArticleAuthorNotLinkeo === false) {
+                
+                return isArticleAuthorNotLinkeo;
+            }
             var $target = $(event.currentTarget);
             var categoryId = $target.parent().data('cid');
             var model = this.collection.get(categoryId);
