Revision: r12583
Date: 2024-07-10 16:39:09 +0300 (lrb 10 Jol 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: correction bug traduction

## Files changed

## Full metadata
------------------------------------------------------------------------
r12583 | srazana<PERSON>lisoa | 2024-07-10 16:39:09 +0300 (lrb 10 Jol 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/TraduiceButton.js

News: correction bug traduction
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Views/TraduiceButton.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/TraduiceButton.js	(révision 12582)
+++ src/js/JEditor/NewsPanel/Views/TraduiceButton.js	(révision 12583)
@@ -101,7 +101,7 @@
                         },
                         traduiceNews: function (lang) {
                             if (this.app.user.can('create_page')) {
-                                if (!this.model.getCategoryModel().lang[lang]) {
+                                if (!this.model.getCategoryModel() || !this.model.getCategoryModel().lang[lang]) {
                                     this.error({
                                         title: translate("saveAction"),
                                         message: translate("errorCategoryTraduction" , {'lang' : lang})
