Revision: r14006
Date: 2025-03-27 12:47:26 +0300 (lkm 27 Mar 2025) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
NEWS : rendre non-oblifatoire le description catégorie (partie JS)

## Files changed

## Full metadata
------------------------------------------------------------------------
r14006 | srazanandralisoa | 2025-03-27 12:47:26 +0300 (lkm 27 Mar 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/CategorieAddView.js

NEWS : rendre non-oblifatoire le description catégorie (partie JS)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Views/CategorieAddView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(révision 14005)
+++ src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(révision 14006)
@@ -257,16 +257,11 @@
          */
         _checkInput: function(){
             var title = this.$('input[name="title"]');
-            var description = this.$('textarea[name="description"]');
             var valid = true
             if (title.val() =='') {
                 title.addClass('error');
                 valid = false;
             }
-            if (description.val() =='') {
-                description.addClass('error');
-                valid = false;
-            }
             return valid;
         },
       
