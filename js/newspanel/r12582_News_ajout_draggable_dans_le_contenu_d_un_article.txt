Revision: r12582
Date: 2024-07-10 16:36:40 +0300 (lrb 10 Jol 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: ajout draggable dans le contenu d'un article

## Files changed

## Full metadata
------------------------------------------------------------------------
r12582 | sraz<PERSON><PERSON><PERSON>oa | 2024-07-10 16:36:40 +0300 (lrb 10 Jol 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/AvailableView.js

News: ajout draggable dans le contenu d'un article
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Views/AvailableView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/AvailableView.js	(révision 12581)
+++ src/js/JEditor/NewsPanel/Views/AvailableView.js	(révision 12582)
@@ -111,7 +111,7 @@
                                             model.dummy = true;
                                             col.addChild(model);
                                             $block.data('model', model);
-                                            $block.draggable({revert: true, appendTo: $("#page-edit"), helper: "clone", cursor: "move"});
+                                            $block.draggable({revert: true, appendTo: $("#news-editor"), helper: "clone", cursor: "move"});
                                             $catDiv.append($block.get(0));
                                         }
                                         $catDiv.addClass('items');
