Revision: r12396
Date: 2024-06-11 16:00:32 +0300 (tlt 11 Jon 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: utiliser la langue dans le sidebar non dans le categorie pour ajouter une article

## Files changed

## Full metadata
------------------------------------------------------------------------
r12396 | srazanandralisoa | 2024-06-11 16:00:32 +0300 (tlt 11 Jon 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/NewsEditorView.js

News: utiliser la langue dans le sidebar non dans le categorie pour ajouter une article
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Views/NewsEditorView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 12395)
+++ src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 12396)
@@ -99,10 +99,6 @@
                 category : this.model
             });
             this.categoryView.append(this.articlesListView.render().el);
-            self = this
-            this.listenTo(this.addCategorieView, Events.CategorieAddEvents.LANG_CHANGED, _.bind(function(lang) {
-                self.currentLang = lang;
-            }));
 
          },
          renderAddArticle: function (cat){
