Revision: r14272
Date: 2025-05-19 15:09:48 +0300 (lts 19 Mey 2025) 
Author: frahajanirina 

## Commit message
News: Permettre de ne pas mettre de catégorie à un article

## Files changed

## Full metadata
------------------------------------------------------------------------
r14272 | frahajanirina | 2025-05-19 15:09:48 +0300 (lts 19 Mey 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/categorieList.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/newsEditor.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/AddArticleView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/i18n.js

News: Permettre de ne pas mettre de catégorie à un article
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 14271)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 14272)
@@ -184,7 +184,7 @@
                                 
                                     this.languages.each(function(lang) {
                                         uncategorizedCategory.get('lang')[lang.id] = new CategorieLang({
-                                            title: translate("unclassified"),
+                                            title: translate("noCategory"),
                                             description: '',
                                             metaTitle: '',
                                             metaDescription: '',
Index: src/js/JEditor/NewsPanel/Templates/categorieList.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/categorieList.html	(révision 14271)
+++ src/js/JEditor/NewsPanel/Templates/categorieList.html	(révision 14272)
@@ -5,7 +5,7 @@
                 <li class="categorie-nav-list <%= categorie==current?'edit':''%> ">
                     <% if(categorie.id == 'uncategorized'){%>
                         <% if(categorie.lang[currentlang].nbArticle > 0){%>
-                        <a data-cid="<%= categorie.cid %>" > <%= categorie.lang[currentlang].title %> </a>
+                        <a data-cid="<%= categorie.cid %>" > <%=__("unclassified")%> </a>
                         <span class="action">
                             <span class="number"><%= categorie.lang[currentlang].nbArticle %></span>
                         </span>
Index: src/js/JEditor/NewsPanel/Templates/newsEditor.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/newsEditor.html	(révision 14271)
+++ src/js/JEditor/NewsPanel/Templates/newsEditor.html	(révision 14272)
@@ -2,7 +2,7 @@
     <div class="page">
         <div class="title">
             <span class="text page-name">
-                <span class="content" ><%= title %></span> 
+                <span class="content"><%= (title === __("noCategory")) ? __("unclassified") : title %></span>
             </span>
         </div>
         <div class="config-preview">
Index: src/js/JEditor/NewsPanel/Views/AddArticleView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/AddArticleView.js	(révision 14271)
+++ src/js/JEditor/NewsPanel/Views/AddArticleView.js	(révision 14272)
@@ -66,10 +66,7 @@
             this._groupList = this.bylang[this.lang.id];
 
             // Filtrer pour exclure la catégorie "Non classé"
-            var filteredGroupList = new Backbone.Collection(this._groupList.filter(function(category) {
-                return category.id !== 'uncategorized';
-            }));
-
+            var filteredGroupList = new Backbone.Collection(this._groupList);
             this._categoryDropdown = new DropDownView({
                 collection: filteredGroupList,
                 _default: (this._currentCategory)?this._categories.get(this._currentCategory):null,
Index: src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 14271)
+++ src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 14272)
@@ -112,8 +112,12 @@
         var categoryModel = list.getCategoryModel();
         var categorie = ''
         if (categoryModel){ 
-          var categories = categoryModel.lang[lang];
+          var categories, categorie;
+          categories = categoryModel.lang[lang];
           categorie = categories.title;
+          if (categorie === translate('noCategory')) {
+            categorie = '';
+          }
         }
         var pageModel = list.getPageModel();
         var dataPub = that.getHtmlDatePub(list)
Index: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 14271)
+++ src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 14272)
@@ -163,6 +163,7 @@
 	"addToMenu":"Ajouter au menu",
 	"allArticles": "Tous les articles",
 	"unclassified": "Non classés",
+	"noCategory": "Sans catégorie",
 	"published": "Publié",
 	"draft": "Brouillon",
 	"program": "Programmé",
Index: src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 14271)
+++ src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 14272)
@@ -163,6 +163,7 @@
 	"addToMenu":"Ajouter au menu",
 	"allArticles": "Tous les articles",
 	"unclassified": "Non classés",
+	"noCategory": "Sans catégorie",
 	"published": "Publié",
 	"draft": "Brouillon",
 	"program": "Programmé",
Index: src/js/JEditor/NewsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/i18n.js	(révision 14271)
+++ src/js/JEditor/NewsPanel/nls/i18n.js	(révision 14272)
@@ -160,6 +160,7 @@
         "addToMenu":"Add to menu",
         "allArticles": "All articles",
         "unclassified": "Unclassified",
+        "noCategory": "No category",
         "published": "Published",
         "draft": "Draft",
         "program": "Program",
