Revision: r12299
Date: 2024-05-16 10:10:29 +0300 (lkm 16 Mey 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
update du css for news

## Files changed

## Full metadata
------------------------------------------------------------------------
r12299 | srazanandralisoa | 2024-05-16 10:10:29 +0300 (lkm 16 Mey 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/less/imports/news_panel/main.less
   M /branches/ideo3_v2/integration_news/src/less/main.less

update du css for news
------------------------------------------------------------------------

## Diff
Index: src/less/imports/news_panel/main.less
===================================================================
--- src/less/imports/news_panel/main.less	(révision 12298)
+++ src/less/imports/news_panel/main.less	(révision 12299)
@@ -210,7 +210,7 @@
 		display: flex;
 		width: 100%;
 		justify-content: space-between;
-		.upload-categorie{
+		.upload-categorie, .upload-article{
 			height: 100%;
 			width: 300px;
 			.uploader{
@@ -229,7 +229,7 @@
 			}
 			}
 		}
-		.categorie-info{
+		.categorie-info, .article-info{
 			height: 100%;
 			width: inherit;
 			padding-left: 25px;
@@ -258,7 +258,7 @@
 				
 			}
 			
-			.btn.dropdown-toggle{
+			.btn.dropdown-toggle , .category-dropdown{
 				background-color: @grey;
 				text-align: left;
 				min-width: 189px;
@@ -781,43 +781,129 @@
 #news-table {
     margin: auto;
     #search-form {
-    margin-bottom: 10px;
+		margin-bottom: 10px;
+		margin-bottom: 10px;
+		display: flex;
+		align-items: center;
+		justify-content: space-evenly;
 	}
 	.search-news{
-		background-color: #ddd;
+		background-color: #fff;
 		border-radius: 5px;
-		border: 1px #aaa;
+		border: 1px solid #ddd;
 		width: 90%;
 		line-height: 24px;
+		padding: 7px;
+		width: 90%;
+		input{
+			width: calc(100% - 31px);
+			border: none;
+		}
 	}
 	#data-table {
 		border-collapse: collapse;
 		width: 100%;
-	 th, td {
-		border: 1px solid #ddd;
-		padding: 8px;
+	 	th, td {
+		border-top: 1px solid #ddd;
+		padding: 15px 8px;
 		text-align: left;
 		}
+		.switch {
+			display: inline-block;
+			width: 24px;
+			height: 14px;
+		
+			background-color: @pageColor;
+			.border-radius(7px);
+		
+			span {
+			  display: block;
+			  float: right;
+			  width: 10px;
+			  height: 12px;
+			  background: #fff;
+			  margin: 1px;
+			  .border-radius(4px);
+			  .box-shadow(-1px 0 1px 0 rgba(0,0,0,0.1));
+		
+			}
+			&:hover {
+			  background-color: desaturate(@pageColor,30%);
+			}
+		
+		}
+		.disabled {
+			color: #b3b3b3;
+			.switch {
+				background-color: #b2b2b2;
+				&:hover {
+				background-color: desaturate(@pageColor,50%);
+				}
+				span {
+				float: none;
+				.box-shadow(none);
+				}
+			}
+		}
+		tr td:first-child, tr th:first-child {
+			border-left: 1px solid #ddd;
+		}
+		tr:last-child td {
+			border-bottom: 1px solid #ddd;
+		  }
+		tr td:last-child , tr th:last-child {
+			border-right: 1px solid #ddd;
+		}
 		th {
-		cursor: pointer;
+			font-weight: 500;
+			cursor: pointer;
+			font-size: 1.2em;
+			text-align: left;
+			/*pour l'icon sort*/
+			.sort-icon {
+				position: relative;
+				margin-left: 5px; 
+				font-size: 12px; 
+				color: #999;
+			}
+
+			.sort-icon .asc::before {
+				content: "\25B2"; 
+				position: absolute;
+				bottom: 8px;
+				opacity: 0.5;
+				.active{
+					opacity: 0.8;
+				}
+			}
+			.sort-icon .desc::before {
+				content: "\25BC";
+				position: absolute;
+				top: 2px;
+				opacity: 0.5;
+				.active{
+					opacity: 0.8;
+				}
+			}
+			&:hover {
+				background-color: #f2f2f2;
+			}
 		}
-		th:hover {
-			background-color: #f2f2f2;
-		}
+		
 	}
 	.img-news img{
-		width: 50px;
-		height: 50px;
-		margin-right: 10px;
+		width: 60px;
+		height: 60px;
 		object-fit: cover;
 		border-radius: 5px;
 	}
 	/* bouton etat*/
 	.etat .btn{
-		padding: 6px;
-		color: #ddd;
+		height: 19px;
+		color: #fff;
 		font-size: small;
-		border-radius: 40%;
+		border-radius: 9px;
+		line-height: inherit;
 	}
 	.green{
 		background-color: @publieColor
@@ -828,18 +914,5 @@
 	.bleu{
 		background-color: @programColor;
 	}
-	/*pour l'icon sort*/
-	.sort-icon {
-		display: inline-block;
-		margin-left: 5px; 
-		font-size: 12px; 
-		color: #999;
-	}
-
-	.sort-icon .asc::before {
-		content: "\25B2"; 
-	}
-	.sort-icon .desc::before {
-		content: "\25BC";
-	}
+	
 }
\ No newline at end of file
Index: src/less/main.less
===================================================================
--- src/less/main.less	(révision 12298)
+++ src/less/main.less	(révision 12299)
@@ -2163,7 +2163,7 @@
 
 }
 //Zone
-#page-edit {
+#page-edit , #news-editor {
   &:focus {
     outline: none;
   }
