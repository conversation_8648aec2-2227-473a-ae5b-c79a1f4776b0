Revision: r12727
Date: 2024-08-09 08:24:06 +0300 (zom 09 Aog 2024) 
Author: jn.harison 

## Commit message
IDEO3.2 NEWS: Correction menu de news lors du changement de langue

## Files changed

## Full metadata
------------------------------------------------------------------------
r12727 | jn.harison | 2024-08-09 08:24:06 +0300 (zom 09 Aog 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NavigationPanel/NavigationPanel.js

IDEO3.2 NEWS: Correction menu de news lors du changement de langue
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NavigationPanel/NavigationPanel.js
===================================================================
--- src/js/JEditor/NavigationPanel/NavigationPanel.js	(révision 12726)
+++ src/js/JEditor/NavigationPanel/NavigationPanel.js	(révision 12727)
@@ -167,6 +167,9 @@
                                 this.childViews.pageSupportListView.lang =lang.id;
                                 this.childViews.menuPanelView.lang = lang;
                                 this.childViews.menuListView.lang = lang.id;
+                                if(this.app.user.can('access_panel_news', __IDEO_NEWS__)){ 
+                                    this.childViews.menuPanelView.lang = lang.id;
+                                }
                                 if (!this.mobile)
                                     this.childViews.menuListView.hide(false);
                                 if(this.currentPageSupportList.length === 0) 
