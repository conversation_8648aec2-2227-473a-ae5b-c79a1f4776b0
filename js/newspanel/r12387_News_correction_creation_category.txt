Revision: r12387
Date: 2024-06-10 17:09:18 +0300 (lts 10 Jon 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: correction creation category 

## Files changed

## Full metadata
------------------------------------------------------------------------
r12387 | srazanandralisoa | 2024-06-10 17:09:18 +0300 (lts 10 Jon 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/Categorie.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/CategorieAddView.js

News: correction creation category 
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Models/Categorie.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Categorie.js	(révision 12386)
+++ src/js/JEditor/NewsPanel/Models/Categorie.js	(révision 12387)
@@ -20,7 +20,6 @@
                     initialize: function() {
                         this._super();
                         this.file = null;
-                        this.lastState = this.toJSON();
                         this.on(Events.BackboneEvents.SYNC, this._onSync);
                     },
                     _onSync: function() {
@@ -27,7 +26,7 @@
                         this.lastState = this;
                     },
                     hasUnsavedChanges: function() {
-                        return !_.isEqual(this.toJSON(), this.lastState);
+                        return false;
                     },
                     cancel: function() {
                         this.set(this.lastState);
Index: src/js/JEditor/NewsPanel/Views/CategorieAddView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(révision 12386)
+++ src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(révision 12387)
@@ -42,7 +42,11 @@
                 this.edit = true;
                 this.currentCategorieLang = this.model.getByLanguage(this.options.language);
             } else{
-                this.model = new Categorie();
+                this.model = new Categorie({
+                    ressource: null,
+                    numberArticlesInCagetory:0,
+                    lang : {}
+                });
                 this.currentCategorieLang = new CategorieLang();
             } 
            // this.model.lang = this.options.language.id;
