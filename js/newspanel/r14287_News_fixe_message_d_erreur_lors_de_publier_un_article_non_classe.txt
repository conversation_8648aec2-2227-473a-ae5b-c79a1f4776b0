Revision: r14287
Date: 2025-05-21 15:41:27 +0300 (lrb 21 Mey 2025) 
Author: frahajanirina 

## Commit message
News:fixe message d'erreur lors de publier un article non classe

## Files changed

## Full metadata
------------------------------------------------------------------------
r14287 | frahajanirina | 2025-05-21 15:41:27 +0300 (lrb 21 Mey 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/NewsPanel.js

News:fixe message d'erreur lors de publier un article non classe
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 14286)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 14287)
@@ -785,7 +785,7 @@
                                  * @returns 
                                  */
                                 showPublishConfig : function() {
-                                    if(this.currentArticle.get('title') =='' || !this.currentArticle.get('ressource') || (this.currentArticle.get('category').length == 0)){
+                                    if(this.currentArticle.get('title') =='' || !this.currentArticle.get('ressource')){
                                         this.error({
                                             message: translate("errorPublishingInvalideElement"),
                                             title: translate("error")
