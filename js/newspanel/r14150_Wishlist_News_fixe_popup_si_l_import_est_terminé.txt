Revision: r14150
Date: 2025-04-24 09:17:58 +0300 (lkm 24 Apr 2025) 
Author: frahajanirina 

## Commit message
Wishlist:News:fixe popup si l'import est terminé 

## Files changed

## Full metadata
------------------------------------------------------------------------
r14150 | frahajanirina | 2025-04-24 09:17:58 +0300 (lkm 24 Apr 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/i18n.js

Wishlist:News:fixe popup si l'import est terminé 
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 14149)
+++ src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 14150)
@@ -178,5 +178,6 @@
 	"errorDowbloafFile": "Erreur lors du téléchargement du fichier",
 	"error": "Erreur",
 	"fileUnauthorised": "Le ficher n\'a pas été importé. Ce type de fichier n\'est pas autorisé.",
-	"uploadLink": "Importer les articles"
+	"uploadLink": "Importer les articles",
+	"uploadSuccess": "L'article a été importé avec succès"
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 14149)
+++ src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 14150)
@@ -178,5 +178,6 @@
 	"errorDowbloafFile": "Erreur lors du téléchargement du fichier",
 	"error": "Erreur",
 	"fileUnauthorised": "Le ficher n\'a pas été importé. Ce type de fichier n\'est pas autorisé.",
-	"uploadLink": "Importer les articles"
+	"uploadLink": "Importer les articles",
+	"uploadSuccess": "L'article a été importé avec succès"
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/i18n.js	(révision 14149)
+++ src/js/JEditor/NewsPanel/nls/i18n.js	(révision 14150)
@@ -175,7 +175,8 @@
         "errorDowbloafFile": "Error downloading file",
         "error": "Error",
         "fileUnauthorised": "The file was not imported. This file type is not allowed.",
-        "uploadLink": "Import articles"
+        "uploadLink": "Import articles",
+        "uploadSuccess": "Article has been successfully imported"
     },
     "fr-fr": true,
     "fr-ca": true
