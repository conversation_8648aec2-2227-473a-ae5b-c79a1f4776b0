Revision: r12436
Date: 2024-06-19 14:27:00 +0300 (lrb 19 Jon 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: update tableau article avec traduction et date de publication

## Files changed

## Full metadata
------------------------------------------------------------------------
r12436 | sraz<PERSON><PERSON><PERSON>oa | 2024-06-19 14:27:00 +0300 (lrb 19 Jon 2024) | 1 ligne
Chemins modifiés :
   A /branches/ideo3_v2/integration_news/assets/img/globe.png
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/Article.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/articlesTable.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/traduiceButton.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/NewsEditorView.js
   M /branches/ideo3_v2/integration_news/src/less/imports/news_panel/main.less

News: update tableau article avec traduction et date de publication
------------------------------------------------------------------------

## Diff
Index: assets/img/globe.png
===================================================================
Impossible d'afficher : fichier considéré comme binaire.
svn:mime-type = application/octet-stream

Property changes on: assets/img/globe.png
___________________________________________________________________
Added: svn:mime-type
## -0,0 +1 ##
+application/octet-stream
\ No newline at end of property
Index: src/js/JEditor/NewsPanel/Templates/articlesTable.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/articlesTable.html	(révision 12435)
+++ src/js/JEditor/NewsPanel/Templates/articlesTable.html	(révision 12436)
@@ -71,8 +71,7 @@
 					<%=item.categorie%>
 				</td>
 				<td class="datepub">
-					<span><i class="icon"></i></span>
-					<span></span>
+					<%=item.publicationDate%>
 				</td>
 				<td class="state">
 					<%_.each(item.state,function(state){ %>
@@ -90,6 +89,12 @@
 				</td>
 				<td>
 					<span class="switch"><span></span></span>
+					<span class="globe-icon">
+						<%if(item.numberOfTranslation > 0){%> 
+							<span class="badge"><%=item.numberOfTranslation%></span>
+						<%}%>
+						
+					</span>
 					<span class="icon-edit"></span>
 					<span class="icon-bin"></span>
 				</td>
Index: src/js/JEditor/NewsPanel/Templates/traduiceButton.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/traduiceButton.html	(révision 12435)
+++ src/js/JEditor/NewsPanel/Templates/traduiceButton.html	(révision 12436)
@@ -1,7 +1,7 @@
 
 
 <div>
-    <button class="btn dropdown-toggle" data-toggle="dropdown"><span class="icon icon-layout-ok"></span></span> <%= __("traduice")%></button>
+    <button class="btn dropdown-toggle" data-toggle="dropdown"><span class="globe-icon"></span></span> <%= __("traduice")%></button>
     <ul class="dropdown-menu">
         <% languages.each(function(lang){ %>
         <li>
Index: src/js/JEditor/NewsPanel/Models/Article.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Article.js	(révision 12435)
+++ src/js/JEditor/NewsPanel/Models/Article.js	(révision 12436)
@@ -28,7 +28,8 @@
                             metaOpengraph: "",
                             page:null,
                             news:null,
-                            content:null
+                            content:null,
+                            numberOfTranslation:0
                         },
                         initialize: function() {
                             this._super();
@@ -98,6 +99,6 @@
             //     }
             //  });
                    
-            Article.SetAttributes(['category', 'state','title', 'lang','ressource','introduction', 'publicationDate','programmingDate', 'metaTitle', 'metaOpengraph', "page", "content","news"]);
+            Article.SetAttributes(['category', 'state', 'numberOfTranslation','title', 'lang','ressource','introduction', 'publicationDate','programmingDate', 'metaTitle', 'metaOpengraph', "page", "content","news"]);
             return Article;
         });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 12435)
+++ src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 12436)
@@ -8,6 +8,7 @@
   "JEditor/Commons/Ancestors/Views/ListView",
   "JEditor/Commons/Utils",
   "i18n!../nls/i18n",
+  "moment",
   //not in params
   "jqueryPlugins/dropdown"
 ], function($,
@@ -17,7 +18,8 @@
   Events,
   ListView,
   Utils,
-  translate) {
+  translate,
+  moment) {
 
   var ArticlesCollectionView = ListView.extend({
     attributes: {
@@ -85,10 +87,12 @@
       if (!list ||list.length < 1 ) {
         return list;
       }
+      var that = this;
        return list.map(function(list) {
         var categoryModel = list.getCategoryModel();
         var categories = categoryModel.lang[lang];
         var pageModel = list.getPageModel();
+        var dataPub = that.getHtmlDatePub(list)
         json = {
           cid : list.id,
           image : (list.file)?list.file.fileUrl:'',
@@ -97,8 +101,8 @@
           categorie : categories.title,
           category : list.category,
           id : list._id,              
-          publicationDate: list.publicationDate, 
-          programmingDate: list.programmingDate,
+          publicationDate: dataPub, 
+          numberOfTranslation: list.numberOfTranslation,
           active : (pageModel)?pageModel.active : false,
           content:list.content,
           state : list.state
@@ -107,7 +111,12 @@
       });
      
     },
-
+    getHtmlDatePub: function (list){
+      var html = '<span> - </span>';
+      if(list.programmingDate) html = '<span class="disabled"><span><i class="icon icon-clic_rdv-icon"></i></span>'+ moment(list.programmingDate.date).format("DD/MM/YY")+'</span>';
+      if(list.publicationDate) html = '<span>'+ moment(list.publicationDate.date).format("DD/MM/YY")+'</span>';
+      return html
+    },
     onSwitchClick: function(event) {
       var $target = $(event.currentTarget);
       var articleId = $target.parent().parent().data('cid');
Index: src/js/JEditor/NewsPanel/Views/NewsEditorView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 12435)
+++ src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 12436)
@@ -14,10 +14,10 @@
     "JEditor/NewsPanel/Views/ConfigArticleView",
     "JEditor/PagePanel/Views/PagePreview",
     "JEditor/NewsPanel/Views/ArticleEditorView",
-    
     "i18n!../nls/i18n",
+    "moment",
     //not in params
-    "moment",
+   
     "jqueryPlugins/affix"
 ], function($,
      _, 
@@ -34,7 +34,8 @@
      ConfigArticleView, 
      PagePreview,
      ArticleEditorView,
-     translate
+     translate,
+     moment
      ) {
    
     var NewsEditorView = BabblerView.extend({
Index: src/less/imports/news_panel/main.less
===================================================================
--- src/less/imports/news_panel/main.less	(révision 12435)
+++ src/less/imports/news_panel/main.less	(révision 12436)
@@ -59,6 +59,28 @@
 
 @import '../common/oocss/oocss';
 
+.globe-icon {
+	position: relative; 
+	padding: 12px;
+	border-radius: 4px;
+	background-image: url('../../assets/img/globe.png');
+	background-size: 18px 18px;
+	background-repeat: no-repeat;
+	background-position: 0px center;
+	opacity: 0.7;
+	.badge {
+		position: absolute;
+		top: 4px;
+  		right: -4px;
+		background-color: @newsColorLight;
+		color: white; 
+		font-size: 9px;
+		padding: 1px 5px;
+		border-radius: 50px;
+	  }
+}
+  
+  
 .header, .sub-header__infos {
 	background-color: @newsColorLight;
 	.news-title{
@@ -938,7 +960,21 @@
 				background-color: #f2f2f2;
 			}
 		}
-		
+		td{
+			max-width: 200px; 
+			overflow: hidden; 
+			text-overflow: ellipsis; 
+			white-space: nowrap; 
+		}
+		.switch,
+		.icon-edit,
+		.icon-bin{
+			cursor: pointer;
+			opacity: 0.7;
+			&:hover{
+				opacity: 1;
+			}
+		} 
 	}
 	.img-news img{
 		width: 60px;
