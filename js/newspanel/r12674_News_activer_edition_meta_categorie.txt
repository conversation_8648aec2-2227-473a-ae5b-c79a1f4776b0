Revision: r12674
Date: 2024-08-01 10:31:25 +0300 (lkm 01 Aog 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: activer edition meta categorie

## Files changed

## Full metadata
------------------------------------------------------------------------
r12674 | sraz<PERSON><PERSON><PERSON>oa | 2024-08-01 10:31:25 +0300 (lkm 01 Aog 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/configArticle.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/configCategorie.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/NewsEditorView.js

News: activer edition meta categorie
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Templates/configArticle.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/configArticle.html	(révision 12673)
+++ src/js/JEditor/NewsPanel/Templates/configArticle.html	(révision 12674)
@@ -4,7 +4,7 @@
         <span class="icon icon-html"></span>
         <span class="label"><%=__('metaTitleLabel')%></span>
     </label>
-    <input type="text" name="metaTitle" value="<%=metaTitle%>"  id="meta-title">
+    <input type="text" name="metaTitle" value="<%=metaTitle%>"  id="meta-title" disabled>
 </div>
 <div class="panel-content-content">
     <label for="meta-description">
Index: src/js/JEditor/NewsPanel/Templates/configCategorie.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/configCategorie.html	(révision 12673)
+++ src/js/JEditor/NewsPanel/Templates/configCategorie.html	(révision 12674)
@@ -4,7 +4,7 @@
     <span class="icon icon-html"></span>
     <span class="label" ><%=__('metaTitleLabel')%></span>
 </label>
-<input type="text" name="metaTitle" value="<%=metaTitle%>"  id="meta-title" disabled>
+<input type="text" name="metaTitle" value="<%=metaTitle%>"  id="meta-title">
 </div>
 <div class="panel-content-content">
     <label for="meta-description">
@@ -11,7 +11,7 @@
         <span class="icon icon-html"></span>
         <span class="label" ><%=__('metaDescLabel')%></span>
     </label>
-    <textarea name="metaDescription" id="meta-description" cols="20" rows="10" disabled ><%=metaDescription%></textarea>
+    <textarea name="metaDescription" id="meta-description" cols="20" rows="10" ><%=metaDescription%></textarea>
     
 </div>
 <div class="panel-content-content">
Index: src/js/JEditor/NewsPanel/Views/NewsEditorView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 12673)
+++ src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 12674)
@@ -257,11 +257,13 @@
                     onYes : _.bind(function() {
                        if(isArticle)this.model.unsetPublishData();
                         this.model.save();
+                        if(!isArticle && this.addCategorieView)this.addCategorieView.changeView();
                         this.showConfig();
                     }, this),
                     onNo : _.bind(function() {
                         if(isArticle)this.model.content.content.cancel();
                         this.model.cancel();
+                        if(!isArticle && this.addCategorieView)this.addCategorieView.changeView();
                         this.showConfig();
                     }, this),
                     options : {
