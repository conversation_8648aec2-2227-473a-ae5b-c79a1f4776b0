Revision: r12452
Date: 2024-06-20 16:18:35 +0300 (lkm 20 Jon 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: ajout message d'erreur traduction et affichage du message upload article

## Files changed

## Full metadata
------------------------------------------------------------------------
r12452 | srazanandralisoa | 2024-06-20 16:18:35 +0300 (lkm 20 Jon 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/TraduiceButton.js
   M /branches/ideo3_v2/integration_news/src/less/imports/news_panel/main.less

News: ajout message d'erreur traduction et affichage du message upload article
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Views/TraduiceButton.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/TraduiceButton.js	(révision 12451)
+++ src/js/JEditor/NewsPanel/Views/TraduiceButton.js	(révision 12452)
@@ -82,9 +82,17 @@
                             var params = $target.attr('data-args');
                             var className = $target.attr('class');
                             if (className == 'disabled') {
-                                this.error({
-                                    title: translate("saveAction"),
-                                    message: translate("article dans la langue existe déjà")
+                                $.toast({
+                                    text: translate("alreadyTraduice", {'lang' : params }), 
+                                    icon: 'icon-check-circle', 
+                                    type:'warning',
+                                    appendTo:'#news-editor .zone .zone-toolbox',
+                                    showHideTransition: 'fade', 
+                                    hideAfter: 5000, 
+                                    position: 'top-right', 
+                                    textAlign: 'left', 
+                                    allowToastClose:false,
+                                    loader:false
                                 });
                             }
                             else if (this[action] && this[action].call) {
@@ -96,7 +104,7 @@
                                 if (!this.model.getCategoryModel().lang[lang]) {
                                     this.error({
                                         title: translate("saveAction"),
-                                        message: translate("category pour la langue non trouvé")
+                                        message: translate("errorCategoryTraduction" , {'lang' : lang})
                                     });
                                     return false;
                                 }
Index: src/less/imports/news_panel/main.less
===================================================================
--- src/less/imports/news_panel/main.less	(révision 12451)
+++ src/less/imports/news_panel/main.less	(révision 12452)
@@ -244,7 +244,7 @@
 					position: absolute;
 					opacity: revert-layer;
 					.message{
-						display: block;
+						display: block !important;
 					}
 				}
 				 .preview {
