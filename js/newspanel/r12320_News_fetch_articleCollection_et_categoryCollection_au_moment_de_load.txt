Revision: r12320
Date: 2024-05-29 15:28:14 +0300 (lrb 29 Mey 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: fetch articleCollection et categoryCollection au moment de load

## Files changed

## Full metadata
------------------------------------------------------------------------
r12320 | srazanandralisoa | 2024-05-29 15:28:14 +0300 (lrb 29 Mey 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Templates/publishConfig.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/Article.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/ArticlesCollection.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/Categorie.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/CategorieCollection.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration_news/src/less/imports/news_panel/main.less

News: fetch articleCollection et categoryCollection au moment de load
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Models/CategorieCollection.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/CategorieCollection.js	(révision 12319)
+++ src/js/JEditor/NewsPanel/Models/CategorieCollection.js	(révision 12320)
@@ -20,10 +20,6 @@
                                 throw new TypeError("Impossible d'instancier un Panneau, veuillez utiliser la méthode statique MaClass.getInstance()");
                             else {
                                 Collection.apply(this, arguments);
-                                if (!this.fetched) {
-                                    this.fetch();
-                                    this.fetched = true;
-                                }
                             }
                         },
                         onSync: function (coll,resp) {
Index: src/js/JEditor/NewsPanel/Articles/Templates/publishConfig.html
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Templates/publishConfig.html	(révision 12319)
+++ src/js/JEditor/NewsPanel/Articles/Templates/publishConfig.html	(révision 12320)
@@ -7,13 +7,13 @@
         <div class="panel-content-intro">
             Publiez maintenant ou programmez ka publication de votre article à la date que vous souhaitez
         </div>
-    </div>
+   
     <div class="option-content ">
-       <div for="meta-title">
+       <h4>
         <span class="icon icon-calendar-line"></span>
         <span>Programmez l'envoi</span>
-       </div>
-        <p> Programmez une date de publication de l'article</p>
+       </h4>
+        <div class="panel-content-intro"> Programmez une date de publication de l'article</div>
         
       <div class="batch">
          <!-- toogle -->
@@ -47,4 +47,5 @@
             </div>
         </div>
     </div>
+</div>
 </div>
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Models/Article.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Article.js	(révision 12319)
+++ src/js/JEditor/NewsPanel/Models/Article.js	(révision 12320)
@@ -81,10 +81,10 @@
                             return this.page;
                         },                    
                         _onSync: function(model, response, options) {
-                            this.lastState = this.toJSON();
+                            this.lastState = this;
                         },
                         hasUnsavedChanges: function() {
-                            return !_.isEqual(this.toJSON(), this.lastState);
+                            return !_.isEqual(this, this.lastState);
                         },
                         cancel: function() {
                             this.set(this.lastState);
Index: src/js/JEditor/NewsPanel/Models/ArticlesCollection.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/ArticlesCollection.js	(révision 12319)
+++ src/js/JEditor/NewsPanel/Models/ArticlesCollection.js	(révision 12320)
@@ -12,10 +12,6 @@
                             throw new TypeError("Impossible d'instancier un Panneau, veuillez utiliser la méthode statique MaClass.getInstance()");
                         else {
                             Collection.apply(this, arguments);
-                            if (!this.fetched) {
-                                this.fetch();
-                                this.fetched = true;
-                            }
                         }
                     },
                     url: function() {
Index: src/js/JEditor/NewsPanel/Models/Categorie.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Categorie.js	(révision 12319)
+++ src/js/JEditor/NewsPanel/Models/Categorie.js	(révision 12320)
@@ -24,10 +24,10 @@
                         this.on(Events.BackboneEvents.SYNC, this._onSync);
                     },
                     _onSync: function() {
-                        this.lastState = this.toJSON();
+                        this.lastState = this;
                     },
                     hasUnsavedChanges: function() {
-                        return !_.isEqual(this.toJSON(), this.lastState);
+                        return !_.isEqual(this, this.lastState);
                     },
                     cancel: function() {
                         this.set(this.lastState);
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12319)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12320)
@@ -66,11 +66,23 @@
                                     this._template = this.buildTemplate(NewsPanelTemplate, translate);
                                     this.rightPanelView = new RightPanelView();
                                     this._byLang = {};
+                                },
+                                load: function () {
+                                    var loaded = 0;
                                     this.categories = CategorieCollection.getInstance();
                                     this.articles =  ArticlesCollection.getInstance();
-                                },
-                                load: function () {
-
+                                    
+                                    function onLoaded() {
+                                        loaded++;
+                                        if (loaded === 2) {
+                                        this.loadingEnd();
+                                        }
+                                    }
+                                    this.listenToOnce(this.categories, "sync", onLoaded);
+                                    this.listenToOnce(this.articles, "sync", onLoaded);
+                                    this.categories.fetch();
+                                    this.articles.fetch();
+                                
                                     this.on(Events.NewsPanelEvents.CATEGORIE_CHANGE, this._onCategorieChange); 
                                     this.on(Events.NewsPanelEvents.ARTICLE_CHANGE, this._onArticleChange); 
 
@@ -109,7 +121,6 @@
                                    
                                     this.listenTo(this.childViews.langDropDown, "selected:choice", this._onLangSelect);
                             
-                                    this.loadingEnd();
                                 },
                                 /**
                                  * met à jour la vue de page (zone du centre de l'écran)
Index: src/less/imports/news_panel/main.less
===================================================================
--- src/less/imports/news_panel/main.less	(révision 12319)
+++ src/less/imports/news_panel/main.less	(révision 12320)
@@ -262,11 +262,12 @@
 			}
 			
 			.btn.dropdown-toggle{
-				background-color: @grey;
+				color: #999;
+				background-color: #f4f4f4;
+				border: 1px solid #e1e1e1;
 				text-align: left;
 				min-width: 189px;
 				line-height: 30px;
-				color: #fff;
 				.caret {
 					margin: 0;
 					display: block;
@@ -273,7 +274,7 @@
 					position: absolute;
 					top: 13px;
 					right: 8px;
-					border-top-color: #fff;
+					border-top-color: #999;
 				}
 			}
 			.category-dropdown {
@@ -286,7 +287,7 @@
 				.dropdown-menu{
 					width: 100%;
 					min-width: 205px;
-					background-color: #999;
+					background-color: #f4f4f4;
 					background-clip: padding-box;
 					& li a {
 						display: block;
@@ -293,12 +294,12 @@
 						line-height: 30px;
 						margin: 0;
 						padding: 0 0 0 10px;
-						color:#f5f5f5;
+						color:#999;
 					}
 				
 					& li a:hover, & li a.active {
-						background-color: #ccc;
-						color: @newsColorLight;
+						background-color:#ddd;
+						color:#000
 					}
 
 				}
@@ -615,6 +616,14 @@
 	.page {
 		.clearfix();
 	}
+	.zone-toolbox .publish{
+		background-color: @newsColorLight;
+		color: #fff !important;
+		border-color: @newsColorLight !important;
+		&:hover{
+			background-color: @newsColorLight;
+		}
+	}
 	&.affix {
 		height: 60px;
   
@@ -633,11 +642,6 @@
 		  float: left;
 		  position: static;
 		}
-		.zone-toolbox .btn .publish{
-			background: @newsColorLight;
-			color: #fff;
-			border-color: @newsColorLight;
-		}
 		& .zone .label,
 		& .page .label {
 		  display: none;
@@ -870,6 +874,8 @@
 		input{
 			width: calc(100% - 31px);
 			border: none;
+			background-color: #fff;
+			color: #1a1a1a;
 		}
 	}
 	#data-table {
@@ -953,34 +959,96 @@
 	
 }
 
-.ui-datepicker {
-    background-color: #1a1a1a; 
-}
+#meta-configuration {
+	span.icon {
+		color: #515151;
+		margin-right: 10px;
+	}
+    &>.scroll-container {
+        height: 100%;
 
-.ui-datepicker-header {
-    background-color: #1a1a1a;
-    color: #fff;
-}
+        & .panel-content {
+            margin: auto;
+        }
+    }
 
-.ui-datepicker-calendar {
-    background-color: #1a1a1a; 
-}
+    header.panel-head {
+        h1.panel-name {
+            left: 0;
+        }
 
-.ui-datepicker-current-day {
-    background-color: #ccc; 
-}
-.ui-datepicker-today {
-    background-color: #f0f0f0; 
-}
+        border-bottom:1px solid #515151;
+        padding-bottom:20px;
+    }
 
-.ui-datepicker-week-end {
-    background-color: #f9f9f9; 
-}
-.ui-datepicker-title {
-    color: #333;
-}
+    h2.option-title {
+        font-size: 1.5em;
+        text-align: center;
+        border: none;
+        font-weight: normal;
+    }
+	#datepicker{
+		margin: 10px auto;
+	.ui-datepicker-inline{
+		margin: auto;
+		width : 100%;
+		border: none;
+		.ui-datepicker-header.ui-widget-header {
+			background-color: #1a1a1a;
+			color: #fff;
+		}
+		.ui-datepicker-prev{
+			color: #fff; 
+			background-color: #333; 
+			.ui-icon.ui-icon-circle-triangle-w::before{
+				border-right: 7px solid #999;
 
-.ui-datepicker-prev, .ui-datepicker-next {
-    color: #fff; 
-    background-color: #333; 
+			}
+			
+		}
+		.ui-datepicker-next {
+			color: #fff; 
+			background-color: #333; 
+			.ui-icon.ui-icon-circle-triangle-e::before{
+				border-left: 7px solid #999;;
+			}
+		}
+				
+		.ui-datepicker-calendar {
+			 background-color: #1a1a1a; 
+		}
+		}	
+	}
+	button {
+		background-color: @newsColorLight;
+		color: #999;
+		border-radius: 3px;
+		width: 100%;
+		padding: 14px;
+		border: 1px solid #newsColorLight;
+		font-size: 0.9em;
+		font-weight: 500;
+		.icon{
+			color: #f4f4f4;
+		}
+	}
+
+    .panel-content {
+        width: 305px;
+        margin: auto;
+
+        .panel-content-intro {
+            color: #727272;
+            font-size: 0.9em;
+        }
+
+        .option-content{
+            & div.items {
+                .clearfix();
+            }
+
+            margin-bottom:100px;
+        }
+   }
+
 }
