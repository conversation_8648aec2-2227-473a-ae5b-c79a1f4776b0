Revision: r12666
Date: 2024-07-31 15:04:27 +0300 (lrb 31 Jol 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: change icon news

## Files changed

## Full metadata
------------------------------------------------------------------------
r12666 | srazana<PERSON>lisoa | 2024-07-31 15:04:27 +0300 (lrb 31 Jol 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/App/Templates/application.html
   M /branches/ideo3_v2/integration/src/js/JEditor/DashBoard/Templates/DashBoardTpl.html

News: change icon news
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/App/Templates/application.html
===================================================================
--- src/js/JEditor/App/Templates/application.html	(révision 12665)
+++ src/js/JEditor/App/Templates/application.html	(révision 12666)
@@ -8,7 +8,7 @@
             <li><a href="#" class="page active panel-link" data-target="pages"><span class="icon icon-file" ></span><span class="text"><%= __("Pages")%></span></a></li>
             <!-- add condition after-->
             <% if(user.can('access_panel_news',__IDEO_NEWS__)){ %>
-            <li><a href="#"  class="news panel-link" data-target="news"><span class="icon icon-file" ></span><span class="text"><%= __("News")%></span></a></li>
+            <li><a href="#"  class="news panel-link" data-target="news"><span class="icon icon-News" ></span><span class="text"><%= __("News")%></span></a></li>
             <% } %>
             <% if(user.can('access_panel_design')){ %>
             	<li><a href="#"  class="design panel-link" data-target="design"><span class="icon icon-design" ></span><span class="text"><%= __("Design")%></span></a></li>
Index: src/js/JEditor/DashBoard/Templates/DashBoardTpl.html
===================================================================
--- src/js/JEditor/DashBoard/Templates/DashBoardTpl.html	(révision 12665)
+++ src/js/JEditor/DashBoard/Templates/DashBoardTpl.html	(révision 12666)
@@ -32,8 +32,8 @@
             <!-- <% if(user.can('access_panel_news',__IDEO_NEWS__)){ %>
             -->
             <li>
-                <a href="#news" class="design panel-link" data-target="news">
-                    <span class="icon icon-file-doc"></span>
+                <a href="#news" class="news panel-link" data-target="news">
+                    <span class="icon icon-News"></span>
                     <span class="title"><%=__('news')%></span>
                     <span class="text">
                         <%=__('news_explained')%>
