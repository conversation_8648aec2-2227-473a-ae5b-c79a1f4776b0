Revision: r12668
Date: 2024-07-31 15:10:19 +0300 (lrb 31 Jol 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: lien vers l'article traduit

## Files changed

## Full metadata
------------------------------------------------------------------------
r12668 | s<PERSON><PERSON><PERSON><PERSON><PERSON> | 2024-07-31 15:10:19 +0300 (lrb 31 Jol 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/TraduiceButton.js

News: lien vers l'article traduit
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Views/TraduiceButton.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/TraduiceButton.js	(révision 12667)
+++ src/js/JEditor/NewsPanel/Views/TraduiceButton.js	(révision 12668)
@@ -82,18 +82,9 @@
                             var params = $target.attr('data-args');
                             var className = $target.attr('class');
                             if (className == 'disabled') {
-                                $.toast({
-                                    text: translate("alreadyTraduice", {'lang' : params }), 
-                                    icon: 'icon-check-circle', 
-                                    type:'warning',
-                                    appendTo:'#news-editor .zone .zone-toolbox',
-                                    showHideTransition: 'fade', 
-                                    hideAfter: 5000, 
-                                    position: 'top-right', 
-                                    textAlign: 'left', 
-                                    allowToastClose:false,
-                                    loader:false
-                                });
+                                var currentArticles = this.articles.findWhere({news: this.model.news , lang : params}); // retourne un model
+                                this.app.currentPanel.currentArticle = currentArticles;
+                               return false;
                             }
                             else if (this[action] && this[action].call) {
                               this[action].call(this, params);
