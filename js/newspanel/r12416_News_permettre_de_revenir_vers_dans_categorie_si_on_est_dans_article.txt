Revision: r12416
Date: 2024-06-13 17:21:57 +0300 (lkm 13 Jon 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: permettre de revenir vers dans categorie si on est dans article

## Files changed

## Full metadata
------------------------------------------------------------------------
r12416 | srazanandralisoa | 2024-06-13 17:21:57 +0300 (lkm 13 Jon 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/NewsPanel.js

News: permettre de revenir vers dans categorie si on est dans article
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12415)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12416)
@@ -374,7 +374,7 @@
                                     this.childViews.newsEditorView.renderAddCategorie()
                                 },
                                 onAddArticleClick : function() {
-                                if(this.currentCategorie.lang[this.currentLang.id])
+                                if(this.currentCategorie && this.currentCategorie.lang[this.currentLang.id])
                                    this.childViews.newsEditorView.renderAddArticle(this.currentCategorie);
                                 else {
                                     this.error({
@@ -566,8 +566,9 @@
                                         set : function(currentCategorie) {
                                             if (currentCategorie !== this._currentCategorie) {
                                                 this._currentCategorie = currentCategorie;
-                                                this.trigger(Events.NewsPanelEvents.CATEGORIE_CHANGE, this, currentCategorie, undefined);
                                             }
+                                            this.trigger(Events.NewsPanelEvents.CATEGORIE_CHANGE, this, currentCategorie, undefined);
+                                            
                                         }
                                     },
                                      /**
