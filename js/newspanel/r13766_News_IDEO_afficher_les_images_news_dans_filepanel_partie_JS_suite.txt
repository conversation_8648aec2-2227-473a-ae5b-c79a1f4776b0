Revision: r13766
Date: 2025-01-31 08:55:21 +0300 (zom 31 Jan 2025) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News IDEO: afficher les images news dans filepanel (partie JS suite)

## Files changed

## Full metadata
------------------------------------------------------------------------
r13766 | srazanandralisoa | 2025-01-31 08:55:21 +0300 (zom 31 Jan 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileDetailManagerView.js

News IDEO: afficher les images news dans filepanel (partie JS suite)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/FilePanel/Views/FileDetailManagerView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileDetailManagerView.js	(révision 13765)
+++ src/js/JEditor/FilePanel/Views/FileDetailManagerView.js	(révision 13766)
@@ -68,7 +68,8 @@
         var params = {};
         if (this.FileDetailView) {
             var isImg = this.FileDetailView.model.isImg();
-            params = _.extend(params, this.FileDetailView.model.toJSON(), {cid: this.FileDetailView.model.cid, isImg: isImg, user: this.app.user});
+            var isRemovable = this.FileDetailView.model.attributes.isRemovable;
+            params = _.extend(params, this.FileDetailView.model.toJSON(), {cid: this.FileDetailView.model.cid, isImg: isImg, user: this.app.user, isRemovable : isRemovable});
         }
         this.$el.html(this._template(params));
         if (this.referrer)
