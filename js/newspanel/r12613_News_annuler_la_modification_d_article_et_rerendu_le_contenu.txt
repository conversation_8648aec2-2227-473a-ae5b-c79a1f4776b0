Revision: r12613
Date: 2024-07-18 11:58:42 +0300 (lkm 18 Jol 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: annuler la modification d'article et rerendu le contenu

## Files changed

## Full metadata
------------------------------------------------------------------------
r12613 | srazanandralisoa | 2024-07-18 11:58:42 +0300 (lkm 18 Jol 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Models/NewsConfig.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/ArticleEditorView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/ArticleTitleField.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/GlobalConfigView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/i18n.js

News: annuler la modification d'article et rerendu le contenu
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Models/NewsConfig.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/NewsConfig.js	(révision 12612)
+++ src/js/JEditor/NewsPanel/Models/NewsConfig.js	(révision 12613)
@@ -31,6 +31,15 @@
                     cancel: function() {
                         this.set(this.lastState);
                     },
+                    parse: function(data){
+                        return{
+                            newsStyle: parseInt(data.newsStyle),                
+                            newsFormat:data.newsFormat, 
+                            newsStyleAff: parseInt(data.newsStyleAff),
+                            newsNbArticle: parseInt(data.newsNbArticle),
+                            layout: data.layout
+                        }
+                    }
     
                 });
             NewsConfig.SetAttributes(['newsStyle', 'newsFormat', 'newsStyleAff', 'newsNbArticle', 'layout']);
Index: src/js/JEditor/NewsPanel/Views/ArticleEditorView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticleEditorView.js	(révision 12612)
+++ src/js/JEditor/NewsPanel/Views/ArticleEditorView.js	(révision 12613)
@@ -85,6 +85,19 @@
                 this.sectionCollectionView.remove();
             BabblerView.prototype.remove.apply(this, arguments);
         },
+        onZoneCancel: function () {
+            this.currentZone = new ZoneModel( this.model.content.content.toJSON());
+            if (this.sectionCollectionView)
+                this.sectionCollectionView.remove();
+                this.sectionCollectionView = new ContentZoneView({
+                    model: this.currentZone
+                });
+            if (!this.model.content.content) {
+                this.model.content.content = this.currentZone;
+                this.model.content.content.lastState = this.currentZone.toJSON();
+            }
+            return this.render();
+        },
         onZoneSave: function () {
             if (this.articleTitleField)
                 this.articleTitleField.remove();
Index: src/js/JEditor/NewsPanel/Views/ArticleTitleField.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticleTitleField.js	(révision 12612)
+++ src/js/JEditor/NewsPanel/Views/ArticleTitleField.js	(révision 12613)
@@ -23,7 +23,7 @@
         var openArticle = (page)?'<a href="//'+ page.attributes.base_url +'" class="btn btn-primary btn-lg btn-block" target="_blank">Ouvrir' : "";
        
         var state = this.model.state;
-        var datep = (state == 1) ? moment(this.model.programmingDate).format("DD/MM/YY"): '';
+        var datep = (_.contains(state, 1)) ? moment(this.model.programmingDate).format("DD/MM/YY"): '';
         this.$el.empty();
         this.$el.html(this._template({
             title: (this.model.id)? this.model.title : 'Article',
Index: src/js/JEditor/NewsPanel/Views/GlobalConfigView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/GlobalConfigView.js	(révision 12612)
+++ src/js/JEditor/NewsPanel/Views/GlobalConfigView.js	(révision 12613)
@@ -73,7 +73,9 @@
 
             this.model = NewsConfig.getInstance();
             this.pageCollection = PageCollection.getInstance();
-            var defaultLayout = this.pageCollection.findWhere({type:"template",lang:this.options.language.id});
+
+            var defaultLayout = this.pageCollection.findWhere({type:"template",lang:this.options.language.id, name:this.model.layout});
+
             this.layoutsDropDown = new TemplateDropDown({collection: this.pageCollection,_default:defaultLayout, language:this.options.language});
             this.layoutsDropDown.filter({type:'template',lang:this.options.language.id});
             this.model.layout = this.layoutsDropDown.current.name;
@@ -81,15 +83,6 @@
            
             this.setTitle(translate('config'));
             this.setDescription(translate('configGlobalDesc'));
-            var templateVars = {
-                newsStyle:this.model.newsStyle,
-                newsFormat:this.model.newsFormat,
-                newsNbArticle:this.model.newsNbArticle,
-                newsStyleAff:this.model.newsStyleAff,
-                canchangeLayout: this.app.user.can("change_news_layout")
-            };
-            var content = this.contenttemplate(templateVars)
-            this.addContent(content);
         }, 
         onLayoutSelect: function(view, templatePage) {
             this.model.layout = templatePage.name;
@@ -118,6 +111,16 @@
                 max: 5,
                 step: 1
             });
+            var templateVars = {
+                newsStyle:this.model.newsStyle,
+                newsFormat:this.model.newsFormat,
+                newsNbArticle:this.model.newsNbArticle,
+                newsStyleAff:this.model.newsStyleAff,
+                canchangeLayout: this.app.user.can("change_news_layout")
+            };
+            var content = this.contenttemplate(templateVars)
+            this.addContent(content);
+            
             this.scrollables();
             this.$('.template-list').append(this.layoutsDropDown.render().el)
             this.$('.layouts.dropup').removeClass('dropup').addClass('dropdown');
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12612)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12613)
@@ -720,7 +720,7 @@
                                             onNo : _.bind(function() {
                                                 this.currentArticle.content.content.cancel();
                                                 this.currentArticle.cancel();
-                                               // this.childViews.newsEditorView.articleEditorView.se();
+                                                this.childViews.newsEditorView.articleEditorView.onZoneCancel();
                                                 this.showPublishView();
                                             }, this),
                                             options : {
@@ -773,8 +773,8 @@
                                     var article = this.currentArticle;
                                     article.set('publicationDate',null);
                                     this.confirm({
-                                        message: translate('majpublication'),
-                                        title: translate("majpublicationDesc"),
+                                        message: translate('majpublicationDesc'),
+                                        title: translate("majpublication"),
                                         type: 'update',
                                         onOk: _.bind(function() {
                                              article.save()
Index: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12612)
+++ src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12613)
@@ -115,7 +115,7 @@
 	"non" : "Non",
 	"willPublish" : "Votre article sera publié le",
 	"programPublish" : "Programmer la publication",
-	"publishImmediate" : "Votre articke sera publié imméfiatement",
+	"publishImmediate" : "Votre articke sera publié immédiatement",
 	"publishArticle" : "Publier l'article",
 	"dateFormat":"Y-m-d",
 	"publishDone":"Article publié",
@@ -122,6 +122,8 @@
 	"programDone":"Article programmé",
 	"publishError": "Erreur de publier",
 	"errorPublishingInvalideElement": "Votre article n'est pas valid",
+	"majpublicationDesc":"Votre article été enregistré, souhaites vous mettre à jour l'aticle programmé",
+	"majpublication":"Mettre à jour la publication",
 
 	//available
 	"addContent":"Ajouter du contenu",
Index: src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 12612)
+++ src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 12613)
@@ -115,7 +115,7 @@
 	 "non" : "Non",
 	 "willPublish" : "Votre article sera publié le",
 	 "programPublish" : "Programmer la publication",
-	 "publishImmediate" : "Votre articke sera publié imméfiatement",
+	 "publishImmediate" : "Votre articke sera publié immédiatement",
 	 "publishArticle" : "Publier l'article",
 	 "dateFormat":"Y-m-d",
 	 "publishDone":"Article publié",
@@ -122,6 +122,8 @@
 	 "programDone":"Article programmé",
 	 "publishError": "Erreur de publier",
 	 "errorPublishingInvalideElement": "Votre article n'est pas valid",
+	 "majpublicationDesc":"Votre article été enregistré, souhaites vous mettre à jour l'aticle programmé",
+     "majpublication":"Mettre à jour la publication",
 	
 	 //available
 	 "addContent":"Ajouter du contenu",
Index: src/js/JEditor/NewsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12612)
+++ src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12613)
@@ -115,7 +115,7 @@
         "non" : "Non",
         "willPublish" : "Votre article sera publié le",
         "programPublish" : "Programmer la publication",
-        "publishImmediate" : "Votre articke sera publié imméfiatement",
+        "publishImmediate" : "Votre articke sera publié immédiatement",
         "publishArticle" : "Publier l'article",
         "dateFormat":"Y-m-d",
         "publishDone":"Article publié",
@@ -122,6 +122,8 @@
         "programDone":"Article programmé",
         "publishError": "Erreur de publier",
         "errorPublishingInvalideElement": "Votre article n'est pas valid",
+        "majpublicationDesc":"Votre article été enregistré, souhaites vous mettre à jour l'aticle programmé",
+        "majpublication":"Mettre à jour la publication",
 
         //available
         "addContent":"Ajouter du contenu",
