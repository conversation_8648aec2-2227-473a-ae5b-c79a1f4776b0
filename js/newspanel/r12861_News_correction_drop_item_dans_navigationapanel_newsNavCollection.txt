Revision: r12861
Date: 2024-08-21 12:29:42 +0300 (lrb 21 Aog 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: correction drop item dans navigationapanel newsNavCollection

## Files changed

## Full metadata
------------------------------------------------------------------------
r12861 | sraz<PERSON><PERSON><PERSON>oa | 2024-08-21 12:29:42 +0300 (lrb 21 Aog 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Links/Models/Link.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NavigationPanel/Views/MenuItemView.js

News: correction drop item dans navigationapanel newsNavCollection
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Links/Models/Link.js
===================================================================
--- src/js/JEditor/Commons/Links/Models/Link.js	(révision 12860)
+++ src/js/JEditor/Commons/Links/Models/Link.js	(révision 12861)
@@ -4,7 +4,6 @@
   "JEditor/Commons/Links/Models/LinkType",
   "collection!JEditor/Commons/Pages/Models/PageCollection",
   "collection!JEditor/Commons/Pages/Models/PageSupportCollection",
-  "JEditor/NewsPanel/Models/NewsNavCollection",
   "JEditor/Commons/Languages/Models/ContentLanguageList",
   "collection!JEditor/Commons/Files/Models/FileDBCollection",
   "JEditor/FilePanel/Models/FileCollection",
@@ -16,7 +15,6 @@
   LinkType,
   PageCollection,
   PageSupportCollection,
-  NewsNavCollection,
   ContentLanguageList,
   FileDBCollection,
   FileCollection,
Index: src/js/JEditor/NavigationPanel/Views/MenuItemView.js
===================================================================
--- src/js/JEditor/NavigationPanel/Views/MenuItemView.js	(révision 12860)
+++ src/js/JEditor/NavigationPanel/Views/MenuItemView.js	(révision 12861)
@@ -233,7 +233,7 @@
                               if(!jqueryElement.hasClass("ui-sortable-helper")) { 
                                   var pageList = PageCollection.getInstance() ; 
                                   var pageSupportList = PageSupportCollection.getInstance();
-                                  var pageNewsList = NewsNavCollection.getInstance();
+    
                                   if(pageList.get(cidDraggable)){
                                     var model = pageList.get(cidDraggable);
                                     var link = new Link({
@@ -251,13 +251,16 @@
                                       });
                                   }
                                   else{
-                                    var model = pageNewsList.get(cidDraggable);
-                                    var link = new Link({
-                                        name: model.name,
-                                        href: model.href,
-                                        lang: model.lang,
-                                        type: LinkType.NEWS
-                                      });
+                                    if (this.app.user.can('access_panel_news', __IDEO_NEWS__)){
+                                        var pageNewsList = NewsNavCollection.getInstance();
+                                        var model = pageNewsList.get(cidDraggable);
+                                        var link = new Link({
+                                            name: model.name,
+                                            href: model.href,
+                                            lang: model.lang,
+                                            type: LinkType.NEWS
+                                          });
+                                    }
                                   }
                                    
 
