Revision: r12343
Date: 2024-06-04 09:01:52 +0300 (tlt 04 Jon 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: Vue  modification article 

## Files changed

## Full metadata
------------------------------------------------------------------------
r12343 | srazana<PERSON>lisoa | 2024-06-04 09:01:52 +0300 (tlt 04 Jon 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/Article.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/Categorie.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/ZoneToolbox.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/articlesTable.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/globalConfig.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/newsEditor.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/AddArticleView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/ArticleEditorView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/GlobalConfigView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/NewsEditorView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/ZoneToolBox.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/i18n.js
   M /branches/ideo3_v2/integration_news/src/less/imports/news_panel/main.less

News: Vue  modification article 
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Templates/newsEditor.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/newsEditor.html	(révision 12342)
+++ src/js/JEditor/NewsPanel/Templates/newsEditor.html	(révision 12343)
@@ -7,14 +7,16 @@
         </div>
         <div class="config-preview">
             <div class="btn-group">
-            <div class="btn-group config">
-                <button type="button"class="btn page-action" id="params-cat" >
-                    <span class="icon icon-params"></span>
-                    <span class="label"><%=__('config')%></span>
-                </button>
-                <ul class="dropdown-menu">
-                </ul>
-            </div>
+                <div class="btn-group open-article" >
+                </div>
+                <div class="btn-group state article-state" >
+                </div>
+                <div class="btn-group config">
+                    <button type="button"class="btn page-action" id="params" >
+                        <span class="icon icon-params"></span>
+                        <span class="label"><%=__('config')%></span>
+                    </button>
+                </div>
                 <%  if( usepreview ){ %> 
                 <button type="button" class="btn btn-default preview" >
                     <i class="icon icon-find"></i>
Index: src/js/JEditor/NewsPanel/Models/Article.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Article.js	(révision 12342)
+++ src/js/JEditor/NewsPanel/Models/Article.js	(révision 12343)
@@ -32,45 +32,36 @@
                         initialize: function() {
                             this._super();
                             this.file = null;
+                            this.categoryModel = null;
+                            this.pageModel = null;
                             this.getFile();
-                            this.transformCategory();
-                            this.transformPage();
+                            this.getPage();
+                            this.getCategory();
                     
                             this.lastState = this.toJSON();
                             this.on(Events.BackboneEvents.SYNC, this._onSync);
                         },
-                    
-                        transformCategory: function(category) {
-                            if (!category) category = this.get('category');
+                        getCategoryModel: function(){
+                            if (this.categoryModel) return this.categoryModel ;
+                            if(!this.category) return null;
                             var categories = CategorieCollection.getInstance();
-                    
-                            if (category.length > 0 ) {
-                                if (Array.isArray(category) ) {
-                                    this.category = category.map(function(categoryId) {
-                                        if (!(categoryId instanceof Categorie)) {
-                                            return categories.findWhere({id: categoryId});
-                                        } else {
-                                            return categoryId;
-                                        }
-                                    });
-                                } else if (!(category instanceof Categorie)) {
-                                    this.category = categories.findWhere({id: category});
-                                }
+                            if (this.category.length && Array.isArray(this.category)) {
+                                categoryId = this.category [0];
+                                this.categoryModel = categories.findWhere({id: categoryId});
                             }
-                            return this.category;
+                            return this.categoryModel;
                         },
-                    
-                        transformPage: function(page) {
-                            if (!page) page = this.get('page');
-                            if (page && !(page instanceof Page)) {
-                                var pageCollection = PageCollection.getInstance();
-                                pageModel = pageCollection.findWhere({id: page});
+                        getPageModel: function(){
+                            if (this.pageModel) return this.pageModel ;
+                            if(!this.page) return null;
+                            var pageCollection = PageCollection.getInstance();
+                                pageModel = pageCollection.findWhere({id: this.page});
                                 if (pageModel) {
-                                    this.page = pageModel;
+                                    this.pageModel = pageModel;
                                 }else {
                                     var pageModel = new Page();
-                                    pageModel.fetchPageById(page).success(function(data) {
-                                        this.page = new Page(data);
+                                    pageModel.fetchPageById(this.page).success(function(data) {
+                                        this.pageModel = new Page(data);
                                         this.trigger(Events.ArticleAddEvents.ADDED, this);
                                     }.bind(this)).error(function(error) {
                                         console.log(error)
@@ -77,20 +68,19 @@
                                     });
                                     
                                 }
-                            }
-                            return this.page;
-                        },                    
+                            return this.pageModel;
+                        },                  
                         _onSync: function(model, response, options) {
-                            this.lastState = this;
+                            this.lastState = this.toJSON();
                         },
                         hasUnsavedChanges: function() {
-                            return !_.isEqual(this, this.lastState);
+                            return !_.isEqual(this.toJSON(), this.lastState);
                         },
                         cancel: function() {
                             this.set(this.lastState);
                         },
                         setCategorY: function (categorie) {
-                            this.category[0]=categorie.attributes;
+                            this.category[0]=categorie;
                         },
                         getFile: function () {
                             if (this.file) return this.file ;
@@ -103,25 +93,10 @@
                         },
                         parse : function(data) {
                             ret = (data.data)? data.data : data;
-                            ret.category = this.transformCategory(ret.category);
-                            ret.page = this.transformPage(ret.page);
                             return ret;
                         },
-                        toJSON: function() {
-                            var json = this._super();
-                            if (json.page && json.page.id) {
-                                json.page = json.page.id;
-                            }
-                            if (json.category )
-                                if(Array.isArray(json.category)) {
-                                json.category = json.category.map(function(cat) {
-                                    return cat.id || cat;
-                                });
-                            } else {
-                                json.category = json.category.id;
-                            }
-                    
-                            return json;
+                        getStateValue: function(){
+                            return 0;
                         }
                     });
             Events.extend(
Index: src/js/JEditor/NewsPanel/Models/Categorie.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Categorie.js	(révision 12342)
+++ src/js/JEditor/NewsPanel/Models/Categorie.js	(révision 12343)
@@ -27,7 +27,7 @@
                         this.lastState = this;
                     },
                     hasUnsavedChanges: function() {
-                        return !_.isEqual(this, this.lastState);
+                        return !_.isEqual(this.toJSON(), this.lastState);
                     },
                     cancel: function() {
                         this.set(this.lastState);
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12342)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12343)
@@ -12,10 +12,11 @@
     "./Views/GlobalConfigView",
     "./Models/CategorieCollection",
     "./Models/ArticlesCollection",
+    "JEditor/Commons/Pages/Models/PageCollection",
     "./Models/Article",
     "JEditor/App/Views/RightPanelView",
-    "JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView",
-    "JEditor/NewsPanel/Articles/Views/PublishConfigView",
+    "JEditor/NewsPanel/Views/AvailableView",
+    "JEditor/NewsPanel/Views/PublishConfigView",
     "i18n!./nls/i18n",
     "JEditor/PagePanel/Contents/Blocks/Blocks",
     "jqueryPlugins/affix"
@@ -34,11 +35,13 @@
             GlobalConfigView,
             CategorieCollection, 
             ArticlesCollection,
+            PageCollection,
             Article,
             RightPanelView,
             AvailableView,
             PublishConfigView,
-            translate
+            translate,
+            Blocks
             ) {
             var NewsPanel = PanelView.extend(
                     /**
@@ -51,6 +54,7 @@
                                     'click #available-blocks-trigger' : 'showAvailableBlocks',
                                     'click #publish-config-trigger' : 'showPublishConfig',
                                     'click #show-zone-version': 'showZoneVersionsPanel',
+                                    // 'click #params' : '_onParams',
                                 },
                                 cssFile: __IDEO_INTEGRATION_PATH__ + "/build/css/news_panel.css",
 
@@ -66,12 +70,14 @@
                                     this._template = this.buildTemplate(NewsPanelTemplate, translate);
                                     this.rightPanelView = new RightPanelView();
                                     this._byLang = {};
+                                    console.log(Blocks);
                                 },
                                 load: function () {
                                     var loaded = 0;
                                     this.categories = CategorieCollection.getInstance();
                                     this.articles =  ArticlesCollection.getInstance();
-                                    
+                                    this.pageCollection = PageCollection.getInstance();
+
                                     function onLoaded() {
                                         loaded++;
                                         if (loaded === 2) {
@@ -103,7 +109,7 @@
                                         newsPanel : this
                                     });
                                     this.childViews.globalConfigView = new GlobalConfigView({
-                                        pagePanel : this
+                                        language : this.currentLang,
                                     });
                         
                                     this.childViews.categorieList = new CategorieCollectionView({
@@ -286,11 +292,9 @@
                                     
                                     this.rightPanelView.clear();
                                     this.rightPanelView.setElement(this.dom.newsPanel.rightSidebar);
-                                    if (this.currentArticle) {
-                                        this.rightPanelView.addContent(content);      
-                                        if (renderChild && renderChild !== 'noRender') {
-                                            content.render();
-                                        }
+                                    this.rightPanelView.addContent(content);      
+                                    if (renderChild && renderChild !== 'noRender') {
+                                        content.render();
                                     }
                                     this.dom.newsPanel.rightSidebar.affix({
                                         offset : {
@@ -299,12 +303,18 @@
                                     });
                                     return this;
                                 },
+
+                                _onParams:function (event){
+                                    if (this.currentArticle) {
+                                        
+                                    }
+                                },
                                 /**
                                  * Affiche le panel de droite en inserant les versions de la zone courante
                                  * 
                                 */
                                 showGlobalConfigView: function(){
-                                    this.renderRightPanel(this.childViews.globalConfigView, 'noRender');
+                                    this.renderRightPanel(this.childViews.globalConfigView);
                                     this.rightPanelView.showContent(this.childViews.globalConfigView);
                                     this.rightPanelView.showPanel();
                                     return false;
Index: src/js/JEditor/NewsPanel/Templates/ZoneToolbox.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/ZoneToolbox.html	(révision 12342)
+++ src/js/JEditor/NewsPanel/Templates/ZoneToolbox.html	(révision 12343)
@@ -1,6 +1,9 @@
 <span class="label"><%=__("zoneToolboxLabel")%></span>
-<div class="btn-group <%=className%>">
-    <button type="button" class="btn btn-default template-link-indicator <%=zone.customized?'clickable':''%>"><span class="icon <%= (zone.customized?'icon-layout-broken':'icon-layout-ok')%>"></span><% if(zone.customized){%><span class="label"><%=__('backToTemplate')%></span><% } %></button>
+<div class="btn-group">
+    <button type="button" class="btn btn-default traduice%>">
+        <span class="icon icon-layout-ok %>"></span>
+        <span class="label"><%=__('traduice')%></span>
+    </button>
     <button type="button" class="btn btn-default add-content" id="available-blocks-trigger"><span class="icon icon-add"></span><span class="label"><%=__("addContent")%></span></button>
     <button type="button" class="btn btn-default publish" id="publish-config-trigger"><span class="icon icon-rotation-right"></span><span class="label"><%=__("publish")%></span></button>
 </div>
Index: src/js/JEditor/NewsPanel/Templates/articlesTable.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/articlesTable.html	(révision 12342)
+++ src/js/JEditor/NewsPanel/Templates/articlesTable.html	(révision 12343)
@@ -69,7 +69,7 @@
 					<span><i class="icon"></i></span>
 					<span></span>
 				</td>
-				<td class="etat">
+				<td class="state">
 					<%_.each(item.state,function(state){ %>
 						<%if(state==2){%> 
 							<span class="btn green">Publié</span>
Index: src/js/JEditor/NewsPanel/Templates/globalConfig.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/globalConfig.html	(révision 12342)
+++ src/js/JEditor/NewsPanel/Templates/globalConfig.html	(révision 12343)
@@ -8,7 +8,19 @@
             <%=__("configDesc")%>
         </div>
     </div>
-
+    <div class="news-option-margin news-template">
+        <article class="panel-option">
+            <header>
+                <h3 class="option-name"><%=__("newsTemplate")%></h3>
+                <p class="panel-content-legend"><%=__("newsTemplateLegend")%></p>
+            </header>
+            <div class="option-content">
+                <div class="template-list">
+                        
+                </div>
+            </div>
+        </article>
+    </div>
     <div class="panel-option-container animated  mr15">
         <article class="panel-option">
             <header>
@@ -151,4 +163,20 @@
             </div>
         </article>
     </div>
-</div>
\ No newline at end of file
+</div>
+<footer class="foot">
+    <div class="button-group save-or-cancel" >
+        <a class="button cancel" data-action="cancel">
+            <span class="wrapper">
+                <span class="icon"></span>
+                <span class="text"><%= __("cancel")%></span>
+            </span>
+        </a>
+        <a class="button save" data-action="save">
+            <span class="wrapper">
+                <span class="icon"></span>
+                <span class="text"><%= __("apply")%></span>
+            </span>
+        </a>
+    </div>
+</footer>
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Views/AddArticleView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/AddArticleView.js	(révision 12342)
+++ src/js/JEditor/NewsPanel/Views/AddArticleView.js	(révision 12343)
@@ -61,7 +61,7 @@
             this.$('.upload-article  .uploader .actions-wrapper .actions .computer input:file.upload').trigger('click');
         },
         _onLoaded: function() {
-            this._currentCategory = (this.model.category)? this.model.category[0]: null;
+            this._currentCategory = (this.model.getCategoryModel())? this.model.getCategoryModel(): null;
             this.lang = this.options.language;
             this.bylang = this._categories.groupBy('lang');
             this._groupList = this.bylang[this.lang.id];
@@ -108,7 +108,7 @@
             this.proccessAttribute(); 
 
             this.$('.upload-article .uploader .preview .imagepreview').after('<span style="font-size:9px;display:block;margin-top:35px;color: #32ace0;text-align:center;">'+this.translations.DefaultMessageUploaderLogo+'</span>');
-
+            if (!this.fileUploader.currentFile)this.$('.upload-article .uploader .message-wrapper .message').show();
             return this;
         },
         _onUpload: function(file){
Index: src/js/JEditor/NewsPanel/Views/ArticleEditorView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticleEditorView.js	(révision 12342)
+++ src/js/JEditor/NewsPanel/Views/ArticleEditorView.js	(révision 12343)
@@ -104,31 +104,6 @@
                 this.sectionCollectionView.remove();
             BabblerView.prototype.remove.apply(this, arguments);
         },
-        onZoneSelect: function (view, selectedZone) {
-
-            if (this.currentZone.hasUnsavedChanges())
-                this.confirmUnsaved({
-                    message: translate("quitWithoutSaving"),
-                    title: translate("unsavedChanges"),
-                    type: 'delete-not-saved',
-                    onYes: _.bind(function () {
-                        this.currentZone.save();
-                        this.setZone(selectedZone);
-                        this.render();
-                    }, this),
-                    onNo: _.bind(function () {
-                        this.setZone(selectedZone);
-                        this.render();
-                    }, this),
-                    options: {
-                        dialogClass: 'delete no-close',
-                        dontAskAgain: true
-                    }
-                });
-            else {
-                this.setZone(selectedZone);
-            }
-        },
         onZoneSave: function () {
             if (this.sectionCollectionView)
                 this.sectionCollectionView.remove();
@@ -140,9 +115,8 @@
         load: function () {
             this.trigger(Events.LoadEvents.LOAD_START, this);
             try {
-                
-                if (this.model.page) {
-                   this.page = this.model.page;
+                this.page = this.model.getPageModel();
+                if (this.page) {
 
                    this.versionsCollection.zoneId = parseInt(this.page.main_zone);
             
@@ -301,6 +275,7 @@
                     this.app.params.lastUrl = url;
                     this.app.params.save();
                     this.app.router.navigate(url);
+                    this.zoneToolbox.setArticle(this.model);
                     this.zoneToolbox.setZone(this.currentZone);
                     if (this.sectionCollectionView)
                         this.sectionCollectionView.remove();
Index: src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 12342)
+++ src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 12343)
@@ -47,7 +47,7 @@
         self = this;
         this.filteredBy = function (item) {
           return item.category.some(function(cat) {
-            return cat.id === categoryId;
+            return cat === categoryId;
           });
       };
       }
@@ -79,24 +79,20 @@
         return list;
       }
        return list.map(function(list) {
-        var categories = '';
-        list.category.forEach(function(element) {
-          if (element && element.lang[lang]) {
-            categories += element.lang[lang].title + ' ';
-          }
-        });
-       
+        var categoryModel = list.getCategoryModel();
+        var categories = categoryModel.lang[lang];
+        var pageModel = list.getPageModel();
         json = {
           cid : list.id,
           image : (list.file)?list.file.fileUrl:'',
           title : list.title,
           author : 'srazanandralisoa',
-          categorie : categories,
+          categorie : categories.title,
           category : list.category,
           id : list._id,              
           publicationDate: list.publicationDate, 
           programmingDate: list.programmingDate,
-          active : (list.page)?list.page.active : false,
+          active : (pageModel)?pageModel.active : false,
           content:list.content,
           state : list.state
         }
@@ -111,7 +107,7 @@
       var $page = $target.parents('tr');
       var article = this.collection.get(articleId);
       if (article) {
-        var pageModel = article.page;
+        var pageModel = article.getPageModel();
           pageModel.active = !pageModel.active;
           pageModel.save();
           $page.toggleClass('disabled');
@@ -186,7 +182,20 @@
       
       return false;
     },
-  
+    sortList: function(list) {
+      if (this.sortedBy !== null){
+        var sortedby = this.sortedBy ;
+        if (sortedby === 'categorie' || sortedby === 'title'){
+          var sortedStrings = _.sortBy(list, function(value) {
+              return value[sortedby].toLowerCase();
+          });
+          return this.sortAsc ? sortedStrings : sortedStrings.reverse();
+        }else 
+        return this.sortAsc ? _.sortBy(list, this.sortedBy) : _.sortBy(list, this.sortedBy).reverse();
+      }
+      else
+          return list;
+  },
     search: function (event) {
         if (event.keyCode === 13) {
           var $target = $(event.currentTarget);
Index: src/js/JEditor/NewsPanel/Views/GlobalConfigView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/GlobalConfigView.js	(révision 12342)
+++ src/js/JEditor/NewsPanel/Views/GlobalConfigView.js	(révision 12343)
@@ -5,10 +5,13 @@
 	"JEditor/Commons/Ancestors/Views/View",
     "JEditor/NewsPanel/Models/NewsConfig",
     "JEditor/App/Config",
+    "JEditor/Commons/Pages/Models/PageCollection",
     "text!JEditor/NewsPanel/Templates/globalConfig.html",
     "JEditor/Commons/Ancestors/Views/SaveCancelPanel",
+    "JEditor/PagePanel/Views/TemplateDropDownView",
+
     "i18n!JEditor/NewsPanel/nls/i18n",
-], function ($, _, Event, View, NewsConfig, Config, template, SaveCancelPanel, translate) {
+], function ($, _, Events, View, NewsConfig, Config, PageCollection, template, SaveCancelPanel,TemplateDropDown, translate) {
     
     /**
      * 
@@ -66,14 +69,18 @@
         initialize: function() {
             this._super();
             this.model = new NewsConfig();
+            this.pageCollection = PageCollection.getInstance();
+            var defaultLayout = this.pageCollection.findWhere({type:"template",lang:this.options.language.id});
+            this.layoutsDropDown = new TemplateDropDown({collection: this.pageCollection,_default:defaultLayout, language:this.options.language});
+            this.layoutsDropDown.filter({type:'template',lang:this.options.language.id});
+       
             this.template = this.buildTemplate(template, translate);
+            this.model.layout = this.layoutsDropDown.current.layout;
+            this.listenTo(this.layoutsDropDown, Events.ChoiceEvents.SELECT, this.onLayoutSelect);
+        }, 
+        onLayoutSelect: function(view, templatePage) {
+            this.model.layout = templatePage.layout;
         },
-        render: function() {
-            this.undelegateEvents();
-            
-            this.delegateEvents();
-            return this;
-        },
         _onSaveClick: function(event) {
             if (this.save)
                 this.save();
@@ -105,38 +112,21 @@
                 step: 1
             });
             this.scrollables();
-            // var saveCancel = new SaveCancelPanel();
-            // saveCancel.on("save",function(){
-            //     optionCollection.each(function(element, index, list) {
-            //         element.applyChanges();
-            //     });
-            // });
-            // saveCancel.on("cancel",function () {
-            //     optionCollection.each(function(element, index, list) {
-            //         element.revertChanges();
-            //     });
-            //     _.each(views, function(view) {
-            //         view.render();
-            //     });
-            // });
-            // saveCancel.setTitle(contentView.translate(titlePrefix + 'Option'));
-            // saveCancel.contentView=contentView;
-            // saveCancel.rightPanelView=rightPanelView;
-            // rightPanelView.addContent(saveCancel);
+            this.$('.template-list').append(this.layoutsDropDown.render().el)
             this.delegateEvents();
             
             return this;
         },
         save: function() {
-            this.model.applyChanges();
-            this.trigger(Events.OptionCollectionViewEvents.SAVE, this);
+            this.model.save();
+            // this.trigger(Events.OptionCollectionViewEvents.SAVE, this);
         },
         /**
          * annule les changements éfectuées depuis l'ouverture
          */
         cancel: function() {
-            this.model.revertChanges();
-            this.trigger(Events.OptionCollectionViewEvents.CANCEL, this);
+            this.model.cancel();
+          //  this.trigger(Events.OptionCollectionViewEvents.CANCEL, this);
         }
         
     });
Index: src/js/JEditor/NewsPanel/Views/NewsEditorView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 12342)
+++ src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 12343)
@@ -5,23 +5,17 @@
     "JEditor/Commons/Events",
     "JEditor/NewsPanel/Views/NewsEditorView",
     "JEditor/Commons/Ancestors/Views/BabblerView",
-    "JEditor/Commons/Ancestors/Views/LoaderView",
-    "collection!JEditor/Commons/Languages/Models/ContentLanguageList",
     "JEditor/NewsPanel/Models/ArticlesCollection",
     "JEditor/NewsPanel/Models/CategorieCollection",
     "JEditor/NewsPanel/Models/Article",
-    "JEditor/PagePanel/Contents/Zones/Models/Zone",
     "JEditor/NewsPanel/Views/ArticlesCollectionView",
     "JEditor/NewsPanel/Views/CategorieAddView",
-    "JEditor/NewsPanel/Views/AddArticleView",
     "JEditor/NewsPanel/Views/ConfigCategorieView",
-    "JEditor/PagePanel/Contents/Zones/Views/ZoneToolBox",
     "JEditor/PagePanel/Views/PagePreview",
     "JEditor/NewsPanel/Views/ArticleEditorView",
     
     "i18n!../nls/i18n",
     //not in params
-   // "jqueryPlugins/dropdown",
     "jqueryPlugins/affix"
 ], function($,
      _, 
@@ -29,18 +23,12 @@
      Events, 
      NewsEditorView, 
      BabblerView, 
-     LoaderView, 
-     ContentLanguageList, 
      ArticlesCollection, 
      CategorieCollection,
      Article,
-     Zone,
      ArticlesCollectionView, 
      CategorieAddView,
-     AddArticleView,
      ConfigCategorieView, 
-
-     ZoneToolBox,
      PagePreview,
      ArticleEditorView,
      translate
@@ -49,7 +37,7 @@
     var NewsEditorView = BabblerView.extend({
         events: {
             'click .btn.preview': 'preview',
-            'click #params-cat' : '_onParamsClick',
+            'click #params' : '_onParamsClick',
             'click a[data-action]': '_onActionClick',
             'click .save-version': 'usePreviousVersion'
         },
@@ -122,7 +110,7 @@
             this.options.title = 'Ajouter une article';
 
             var article =  new Article()
-            if(cat) article.setCategorY(cat);
+            if(cat) article.setCategorY(cat.id);
             this.articleEditorView = new ArticleEditorView({
                 languageList : this.languageList,
                 language : this.currentLang,
@@ -133,7 +121,6 @@
            // this.articleEditorView.load();
             this.render();
             this.articleView.append(this.articleEditorView.render().el);
-            this.$('.zone-selector>.dropdown-toggle, .page-action').dropdown();
         },
          renderArticlePage: function (){
             this.options.usepreview = true;
@@ -148,7 +135,23 @@
             this.articleEditorView.load();
             this.render();
             this.articleView.append(this.articleEditorView.render().el);
-            this.$('.zone-selector>.dropdown-toggle, .page-action').dropdown();
+            var page = this.model.getPageModel() ;
+            var openArticle = (page)?'<a href="'+ page.url +'" class="btn btn-primary btn-lg btn-block">Ouvrir' : "";
+            this.$('.open-article').html( openArticle );
+            var state = this.model.state[0];
+            switch (state) {
+                case 1:
+                    var stateHtml = '<span class="btn bleu">Programmé</span>'
+                    break;
+                case 2:
+                    var stateHtml = '<span class="btn green">Publié</span>'
+                    break;
+                default:
+                    var stateHtml = '<span class="btn orange">Brouillon</span>'
+                    break;
+                
+            }
+            this.$('.article-state').html( stateHtml );
 
          },
         render: function() {
@@ -170,8 +173,9 @@
             if (this.model) {
                 this.edit()
             }
-            else  this.trigger(Events.NewsEditorViewEvents.SHOWRIGHTPANEL, this);
-            return false;
+            else {
+                this.app.currentPanel.showGlobalConfigView();
+            }
         },
         /**
          * Déclenche l'édition de l'élément (options)
Index: src/js/JEditor/NewsPanel/Views/ZoneToolBox.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ZoneToolBox.js	(révision 12342)
+++ src/js/JEditor/NewsPanel/Views/ZoneToolBox.js	(révision 12343)
@@ -1,7 +1,7 @@
 define([
     "JEditor/Commons/Ancestors/Views/BabblerView",
     "JEditor/Commons/Events",
-    "JEditor/PagePanel/Contents/Zones/Views/SaveButton",
+    "JEditor/NewsPanel/Views/SaveButton",
     "JEditor/PagePanel/Contents/Zones/Views/SwitchVersionButton",
     "text!../Templates/ZoneToolbox.html",
     "i18n!../nls/i18n"
@@ -34,7 +34,7 @@
         render: function () {
             this._super();
             this.undelegateEvents();
-            this.$el.html(this._template({zone: this.model, className: this.app.user.can("uncustomize_zone") ? "" : "no-uncustomize"}));
+            this.$el.html(this._template({zone: this.model}));
             this.$('.add-content').before(this.childViews.saveButton.el);
             if (this.app.user.can("restore_zone")) {
                 this.$('.btn-group').prepend(this.childViews.switchVersionButton.el);
@@ -50,10 +50,13 @@
             this.stopListening(this.model);
             this.model = zone;
             this.childViews.switchVersionButton.model = zone;
-            this.childViews.saveButton.model = zone;
+            this.childViews.saveButton.model.content = zone;
             this.listenTo(zone, Events.BackboneEvents.CHANGE, this.breakTemplateLink);
             return this.render();
         },
+        setArticle: function (article) {
+            this.childViews.saveButton.model = article;
+        },
         onZoneSelect: function (view, zone) {
             this.trigger(Events.ChoiceEvents.SELECT, view, zone);
         },
Index: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12342)
+++ src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12343)
@@ -24,10 +24,10 @@
 	"masonryLegend"         :   "Tuiles",
 	"gridLegend"            :   "Grille",
 	"listLegend"            :   "Liste",
-	"newNombreImage"       :   "Nombre de colonne",
-	"newNombreImageLegend" :   "Glissez pour ajuster le nombre de colonne affichées",
-	"newStyleAffichage"    :   "Style des articles",
-	"newStyleAffichageDesc":   "Appliquez un style aux articles",
+	"newsNombreImage"       :   "Nombre de colonne",
+	"newsNombreImageLegend" :   "Glissez pour ajuster le nombre de colonne affichées",
+	"newsStyleAffichage"    :   "Style des articles",
+	"newsStyleAffichageDesc":   "Appliquez un style aux articles",
 	"FormatImage"           :   "Format de l'image",
 	"FormatImageLegend"     :   "Appliquez un format d'image aux articles",
 	'landscape'             :   "Paysage",
@@ -40,6 +40,10 @@
 	"DescStyle4"           :  "Texte sous l'image, bordures",
 	"DescStyle5"           :  "Texte sous l'image, images arrondies (idéal images carrés)",
 	"DescStyle6"           :  "Texte à côté de l'image",
+	"Style"				   :  "Style",
+	"newsTemplate"         :   "Affichage du modèle de page",
+	"newsTemplateLegend"   :   "Appliquez un modèle de page",
+
 	//config category
 	"configDesc" 			: "Ajustez les paramètres des Catégorie/articles",
 	"metaTitleLabel" 		: "Méta Title",
@@ -73,6 +77,7 @@
 	"howToAddContent": "Cliquez sur &laquo; Ajouter du contenu &raquo;<br/> puis glissez un bloc dans cette zone pour créer une section",
 	"versions": "Versions",
 	"pasteTheSection" : "Coller la section",
+	"traduice": "Traduire",
 
 	 //Article 
 	 "catArticle": "Catégorie de l'article",
Index: src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 12342)
+++ src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 12343)
@@ -24,10 +24,10 @@
 	"masonryLegend"         :   "Tuiles",
 	"gridLegend"            :   "Grille",
 	"listLegend"            :   "Liste",
-	"newNombreImage"       :   "Nombre de colonne",
-	"newNombreImageLegend" :   "Glissez pour ajuster le nombre de colonne affichées",
-	"newStyleAffichage"    :   "Style des articles",
-	"newStyleAffichageDesc":   "Appliquez un style aux articles",
+	"newsNombreImage"       :   "Nombre de colonne",
+	"newsNombreImageLegend" :   "Glissez pour ajuster le nombre de colonne affichées",
+	"newsStyleAffichage"    :   "Style des articles",
+	"newsStyleAffichageDesc":   "Appliquez un style aux articles",
 	"FormatImage"           :   "Format de l'image",
 	"FormatImageLegend"     :   "Appliquez un format d'image aux articles",
 	'landscape'             :   "Paysage",
@@ -40,7 +40,10 @@
 	"DescStyle4"           :  "Texte sous l'image, bordures",
 	"DescStyle5"           :  "Texte sous l'image, images arrondies (idéal images carrés)",
 	"DescStyle6"           :  "Texte à côté de l'image",
-
+	"Style"				   :  "Style",
+	"newsTemplate"         :   "Affichage du modèle de page",
+	"newsTemplateLegend"   :   "Appliquez un modèle de page",
+	
 	//config category
 	"configDesc" 			: "Ajustez les paramètres des Catégorie/articles",
 	"metaTitleLabel" 		: "Méta Title",
@@ -74,6 +77,7 @@
 	"howToAddContent": "Cliquez sur &laquo; Ajouter du contenu &raquo;<br/> puis glissez un bloc dans cette zone pour créer une section",
 	"versions": "Versions",
 	"pasteTheSection" : "Coller la section",
+	"traduice": "Traduire",
 
 	 //Article 
 	 "catArticle": "Catégorie de l'article",
Index: src/js/JEditor/NewsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12342)
+++ src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12343)
@@ -25,10 +25,10 @@
         "masonryLegend"         :   "Tuiles",
         "gridLegend"            :   "Grille",
         "listLegend"            :   "Liste",
-        "newNombreImage"       :   "Nombre de colonne",
-        "newNombreImageLegend" :   "Glissez pour ajuster le nombre de colonne affichées",
-        "newStyleAffichage"    :   "Style des articles",
-        "newStyleAffichageDesc":   "Appliquez un style aux articles",
+        "newsNombreImage"       :   "Nombre de colonne",
+        "newsNombreImageLegend" :   "Glissez pour ajuster le nombre de colonne affichées",
+        "newsStyleAffichage"    :   "Style des articles",
+        "newsStyleAffichageDesc":   "Appliquez un style aux articles",
         "FormatImage"           :   "Format de l'image",
         "FormatImageLegend"     :   "Appliquez un format d'image aux articles",
         'landscape'             :   "Paysage",
@@ -41,6 +41,9 @@
         "DescStyle4"           :  "Texte sous l'image, bordures",
         "DescStyle5"           :  "Texte sous l'image, images arrondies (idéal images carrés)",
         "DescStyle6"           :  "Texte à côté de l'image",
+        "Style"                :   "Style",
+        "newsTemplate"         :   "Affichage du modèle de page",
+        "newsTemplateLegend"   :   "Appliquez un modèle de page",
 
         //config category
         "configDesc" 			: "Ajustez les paramètres des Catégorie/articles",
@@ -75,6 +78,7 @@
         "howToAddContent": "Cliquez sur &laquo; Ajouter du contenu &raquo;<br/> puis glissez un bloc dans cette zone pour créer une section",
         "versions": "Versions",
         "pasteTheSection" : "Coller la section",
+        "traduice": "Traduire",
 
         //Article 
         "catArticle": "Catégorie de l'article",
Index: src/less/imports/news_panel/main.less
===================================================================
--- src/less/imports/news_panel/main.less	(révision 12342)
+++ src/less/imports/news_panel/main.less	(révision 12343)
@@ -508,6 +508,9 @@
 		& .btn {
 		  background: #fff;
 		}
+		.config .btn:hover{
+			margin-top: inherit;
+		}
   
 	}
 	.button.preview {
@@ -939,27 +942,27 @@
 		object-fit: cover;
 		border-radius: 5px;
 	}
-	/* bouton etat*/
-	.etat .btn{
-		height: 19px;
-		color: #fff;
-		font-size: small;
-		border-radius: 9px;
-		line-height: inherit;
+	
+}
+/* bouton etat*/
+.state .btn{
+	height: 19px;
+	color: #fff;
+	font-size: small;
+	border-radius: 9px !important;
+	line-height: inherit;
+	&.green{
+		background-color: @publieColor !important;
 	}
-	.green{
-		background-color: @publieColor
+	&.orange{
+		background-color: @brouillonColor !important;
 	}
-	.orange{
-		background-color: @brouillonColor;
+	&.bleu{
+		background-color: @programColor !important;
 	}
-	.bleu{
-		background-color: @programColor;
-	}
-	
 }
 
-#meta-configuration {
+#meta-configuration, .news-configuration {
 	span.icon {
 		color: #515151;
 		margin-right: 10px;
@@ -1034,7 +1037,6 @@
 	}
 
     .panel-content {
-        width: 305px;
         margin: auto;
 
         .panel-content-intro {
