Revision: r12453
Date: 2024-06-20 16:19:55 +0300 (lkm 20 Jon 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: cacher les pages article dans Pagepanel

## Files changed

## Full metadata
------------------------------------------------------------------------
r12453 | sraz<PERSON><PERSON><PERSON>oa | 2024-06-20 16:19:55 +0300 (lkm 20 Jon 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/PagePanel/Views/PageCollectionView.js

News: cacher les pages article dans Pagepanel
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Views/PageCollectionView.js
===================================================================
--- src/js/JEditor/PagePanel/Views/PageCollectionView.js	(révision 12452)
+++ src/js/JEditor/PagePanel/Views/PageCollectionView.js	(révision 12453)
@@ -257,9 +257,14 @@
     },
     render: function() {
       this._super();
+      this.hideNews();
       this.scrollables();
       return this;
     },
+    
+    hideNews: function() {
+      this.$('.wrapper.news').html('');
+    },
     show: function(animate) {
       this._super(animate);
       this.dom.window.scroll();
