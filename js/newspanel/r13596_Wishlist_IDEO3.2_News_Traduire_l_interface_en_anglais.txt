Revision: r13596
Date: 2024-12-12 13:54:10 +0300 (lkm 12 Des 2024) 
Author: frahajanirina 

## Commit message
Wishlist:IDEO3.2:News:Traduire l'interface en anglais

## Files changed

## Full metadata
------------------------------------------------------------------------
r13596 | frahajanirina | 2024-12-12 13:54:10 +0300 (lkm 12 Des 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/addArticle.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/articleTitleField.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/articlesTable.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/ArticleTitleField.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/NewsEditorView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Sections/Templates/section.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Sections/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Sections/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Sections/nls/i18n.js

Wishlist:IDEO3.2:News:Traduire l'interface en anglais
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 13595)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 13596)
@@ -176,7 +176,7 @@
                                 
                                     this.languages.each(function(lang) {
                                         uncategorizedCategory.get('lang')[lang.id] = new CategorieLang({
-                                            title: 'Non classés',
+                                            title: translate("unclassified"),
                                             description: '',
                                             metaTitle: '',
                                             metaDescription: '',
@@ -363,7 +363,7 @@
                                             }
                                             else {
                                                 this.childViews.newsEditorView = new NewsEditorView({
-                                                    title : 'Tous les articles',
+                                                    title : translate("allArticles"),
                                                     languages : this.languages,
                                                     currentLang : this.currentLang,
                                                     categorieCollection :  this.categories
Index: src/js/JEditor/NewsPanel/Templates/addArticle.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/addArticle.html	(révision 13595)
+++ src/js/JEditor/NewsPanel/Templates/addArticle.html	(révision 13596)
@@ -5,7 +5,7 @@
     <div class="article-info">
 
             <div> 
-                <label>Catégorie de l'article</label>
+                <label><%=__('catArticle')%></label>
             </div>
             <!-- Choix de la catégorie -->
             <div class="category-dropdown">
Index: src/js/JEditor/NewsPanel/Templates/articleTitleField.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/articleTitleField.html	(révision 13595)
+++ src/js/JEditor/NewsPanel/Templates/articleTitleField.html	(révision 13596)
@@ -12,13 +12,13 @@
         <div class="btn-group state article-state">
             <%_.each(state,function(state){ %>
                 <%if(state==2){%> 
-                    <span class="btn green">Publié</span>
+                    <span class="btn green"><%=__('published')%></span>
                 <%}%>
                 <%if(state==0){%> 
-                    <span class="btn orange">Brouillon</span>
+                    <span class="btn orange"><%=__('draft')%></span>
                 <%}%>
                 <%if(state==1){%> 
-                    <span class="btn bleu">Programmé <%=datep%></span>
+                    <span class="btn bleu"><%=__('program')%> <%=datep%></span>
                 <%}%>
             <%
             });%>
Index: src/js/JEditor/NewsPanel/Templates/articlesTable.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/articlesTable.html	(révision 13595)
+++ src/js/JEditor/NewsPanel/Templates/articlesTable.html	(révision 13596)
@@ -76,13 +76,13 @@
 				<td class="state">
 					<%_.each(item.state,function(state){ %>
 						<%if(state==2){%> 
-							<span class="btn green">Publié</span>
+							<span class="btn green"><%=__('published')%></span>
 						<%}%>
 						<%if(state==0){%> 
-							<span class="btn orange">Brouillon</span>
+							<span class="btn orange"><%=__('draft')%></span>
 						<%}%>
 						<%if(state==1){%> 
-							<span class="btn bleu">Programmé</span>
+							<span class="btn bleu"><%=__('program')%></span>
 						<%}%>
 					<%
 					});%>
Index: src/js/JEditor/NewsPanel/Views/ArticleTitleField.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticleTitleField.js	(révision 13595)
+++ src/js/JEditor/NewsPanel/Views/ArticleTitleField.js	(révision 13596)
@@ -20,7 +20,7 @@
     render: function() {
         this.undelegateEvents();
         var page = this.model.getPageModel() ;
-        var openArticle = (page)?'<a href="//'+ page.attributes.base_url +'" class="btn btn-primary btn-lg btn-block" target="_blank">Ouvrir' : "";
+        var openArticle = (page)?'<a href="//'+ page.attributes.base_url +'" class="btn btn-primary btn-lg btn-block" target="_blank">'+translate("open") : "";
        
         var state = this.model.state;
         var datep = (_.contains(state, 1)) ? moment(this.model.programmingDate).format("DD/MM/YY"): '';
Index: src/js/JEditor/NewsPanel/Views/NewsEditorView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 13595)
+++ src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 13596)
@@ -82,7 +82,7 @@
             this.listenTo(this.addCategorieView, Events.CategorieAddEvents.CATEGORY_ADD, _.bind(function(categorie) {
                 self.newsPanel.currentCategorie = categorie;
             }));
-            this.options.title = 'Catégorie';
+            this.options.title = translate("category");
             this.options.usepreview = false;
             this.render();
             this.categoryView.html(this.addCategorieView.render().el);
Index: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 13595)
+++ src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 13596)
@@ -126,7 +126,7 @@
 	"programDone":"Article programmé",
 	"publishError": "Erreur de publier",
 	"errorPublishingInvalideElement": "Votre article n'est pas valid",
-	"majpublicationDesc":"Votre article été enregistré, souhaites vous mettre à jour l'aticle programmé",
+	"majpublicationDesc":"Votre article a été enregistré. Souhaitez-vous mettre à jour l'article programmé ?",
 	"majpublication":"Mettre à jour la publication",
 
 	//available
@@ -161,4 +161,10 @@
 
 	//menu
 	"addToMenu":"Ajouter au menu",
+	"allArticles": "Tous les articles",
+	"unclassified": "Non classés",
+	"published": "Publié",
+	"draft": "Brouillon",
+	"program": "Programmé",
+	"open": "Ouvrir"
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 13595)
+++ src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 13596)
@@ -126,7 +126,7 @@
 	 "programDone":"Article programmé",
 	 "publishError": "Erreur de publier",
 	 "errorPublishingInvalideElement": "Votre article n'est pas valid",
-	 "majpublicationDesc":"Votre article été enregistré, souhaites vous mettre à jour l'aticle programmé",
+	 "majpublicationDesc":"Votre article a été enregistré. Souhaitez-vous mettre à jour l'article programmé ?",
      "majpublication":"Mettre à jour la publication",
 	
 	 //available
@@ -161,5 +161,10 @@
 
 	//menu
 	"addToMenu":"Ajouter au menu",
-	 
+	"allArticles": "Tous les articles",
+	"unclassified": "Non classés",
+	"published": "Publié",
+	"draft": "Brouillon",
+	"program": "Programmé",
+	"open": "Ouvrir"
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html	(révision 13595)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html	(révision 13596)
@@ -1,8 +1,8 @@
 <div class="panel-option-container animated  mr15">
     <article class="panel-option">
         <header>
-            <h3 class="option-name"></span> <%=__("styleDegalerie")%></h3>
-            <p class="panel-content-legend"><%= __("styleDegalerieLegend")%></p>
+            <h3 class="option-name"></span> <%=__("galerieStyleContent")%></h3>
+            <p class="panel-content-legend"><%= __("galerieStyleLegend")%></p>
         </header>
         <div>
             <div class="category-content radio-transformed stylleDegalerie">
Index: src/js/JEditor/PagePanel/Contents/Sections/Templates/section.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Sections/Templates/section.html	(révision 13595)
+++ src/js/JEditor/PagePanel/Contents/Sections/Templates/section.html	(révision 13596)
@@ -20,15 +20,15 @@
 			<span class="menu-item menu-group">
 				<span class="menu-item copy">
 					<span class="icon icon-copy"></span>
-					<span class="text">Copier</span>
+					<span class="text"><%= __("copy")%></span>
 				</span>
 				<span class="menu-item paste-before" data-paste="before" style="display:none">
 					<span class="icon icon-paste-before"></span>
-					<span class="text">Coller avant</span>
+					<span class="text"><%= __("pasteBefore")%></span>
 				</span>
 				<span class="menu-item paste-after " data-paste="after" style="display:none">
 					<span class="icon icon-paste-after"></span>
-					<span class="text">Coller après</span>
+					<span class="text"><%= __("pasteAfter")%></span>
 				</span>
 			</span>
 		<% } %>
Index: src/js/JEditor/PagePanel/Contents/Sections/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Sections/nls/fr-ca/i18n.js	(révision 13595)
+++ src/js/JEditor/PagePanel/Contents/Sections/nls/fr-ca/i18n.js	(révision 13596)
@@ -9,5 +9,8 @@
     "sectionOption":"Options de la section",
     "definirPopup": "Définir popup",
     "optionsPopup": "Options popup",
-    "AnnulerPopup": "Annuler popup"
+    "AnnulerPopup": "Annuler popup",
+    "copy": "Copier",
+    "pasteBefore": "Coller avant",
+    "pasteAfter": "Coller après"
 });
Index: src/js/JEditor/PagePanel/Contents/Sections/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Sections/nls/fr-fr/i18n.js	(révision 13595)
+++ src/js/JEditor/PagePanel/Contents/Sections/nls/fr-fr/i18n.js	(révision 13596)
@@ -9,5 +9,8 @@
     "sectionOption":"Options de la section",
     "definirPopup": "Définir popup",
     "optionsPopup": "Options popup",
-    "AnnulerPopup": "Annuler popup"
+    "AnnulerPopup": "Annuler popup",
+    "copy": "Copier",
+    "pasteBefore": "Coller avant",
+    "pasteAfter": "Coller après"
 });
Index: src/js/JEditor/PagePanel/Contents/Sections/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Sections/nls/i18n.js	(révision 13595)
+++ src/js/JEditor/PagePanel/Contents/Sections/nls/i18n.js	(révision 13596)
@@ -10,7 +10,10 @@
         "sectionOption":"Section's Options",
         "definirPopup": "Set popup",
         "optionsPopup": "Popup options",
-        "AnnulerPopup": "Cancel popup"
+        "AnnulerPopup": "Cancel popup",
+        "copy": "Copy",
+        "pasteBefore": "Paste before",
+        "pasteAfter": "Paste after" 
     },
     "fr-fr": true,
     "fr-ca": true
Index: src/js/JEditor/NewsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/i18n.js	(révision 13595)
+++ src/js/JEditor/NewsPanel/nls/i18n.js	(révision 13596)
@@ -1,45 +1,45 @@
 define({
     "root": {
 	    "newsTitle" : "Articles",
-	    "newsDesc": "Parcourez vos pages depuis le menu de gauche. Ajoutez du contenu et prévisualisez votre site à tout moment",
-        "newsAddACategory" : "Ajouter une catégorie",
-        "publishedArticles": "Article\(s\) publié\(s\)",
-        "editedVersion" : "Editez une version",
-        "allArticles": "Tous les articles",	
-        "config" : "Réglages",
-        "newsAddArticle" : "Ajouter un article",
-        "emptyNewsLang": "Vous n'avez encore aucune article",
-        "howToAdd": "Commencez par créer une Catégorie ou un article",
-        "createCategorie" : "Créer une Catégorie",
-        "titleCategorie":"Titre de la catégorie",
-        "descriCategorie":"Description de la catégorie",
-        "cancel":"Annuler",
-        "saveEdit" :"Enregistrer",
-        "saveAdd" : "Créer une catégorie",
-        "preview": "Aper\u00e7u",
-        "quitWithoutSavingArt": "Votre article n'a pas \u00e9t\u00e9 enregistrer, voulez-vous les enregistrer ?",
-        "quitWithoutSavingCat": "Votre cat\u00e9gorie n'a pas \u00e9t\u00e9 enregistrer, voulez-vous les enregistrer ?",
-        "unsavedChanges": "sauvegarder les changements",
-        "confirmDeleteCategory":"Vous \u00eates sur le point de supprimer d\u00e9finitivement une catégorie",
-        "confirmDeleteArticle": "Vous \u00eates sur le point de supprimer d\u00e9finitivement un article",
+	    "newsDesc": "Browse your pages from the left. Add content and preview your site at any time.",
+        "newsAddACategory" : "Add a category",
+        "publishedArticles": "Article\(s\) published",
+        "editedVersion" : "Edit a version",
+        "allArticles": "All articles",	
+        "config" : "Settings",
+        "newsAddArticle" : "Add an article",
+        "emptyNewsLang": "You don't have any articles yet",
+        "howToAdd": "Start by creating a Category or article",
+        "createCategorie" : "Create a Category",
+        "titleCategorie":"Category title",
+        "descriCategorie":"Category description",
+        "cancel":"Cancel",
+        "saveEdit" :"Save",
+        "saveAdd" : "Create a category",
+        "preview": "Preview",
+        "quitWithoutSavingArt": "Your article has not been saved, do you want to save them ?",
+        "quitWithoutSavingCat": "Your category has not been saved, do you want to save them ?",
+        "unsavedChanges": "save changes",
+        "confirmDeleteCategory":"You are about to permanently delete a category",
+        "confirmDeleteArticle": "You are about to permanently delete an article",
         "confirmDeleteImageCategory":"Vous \u00eates sur le point de supprimer d\u00e9finitivement l'image de la catégorie",
         // config global
-        "configGlobalDesc" : "Ajustez les paramétres des catégories/articles","styleDeNews" :   "Affichage de la liste des articles",
-        "styleDeNewsLegend" :   "Appliquez un style à la liste des articles",
-        "masonryLegend"         :   "Tuiles",
-        "gridLegend"            :   "Grille",
-        "listLegend"            :   "Liste",
-        "newsNombreImage"       :   "Nombre de colonne",
-        "newsNombreImageLegend" :   "Glissez pour ajuster le nombre de colonnes affichées",
-        "newsStyleAffichage"    :   "Style des articles",
-        "newsStyleAffichageDesc":   "Appliquez un style aux articles",
-        "FormatImage"           :   "Format de l'image",
-        "FormatImageLegend"     :   "Appliquez un format d'image aux articles",
-        'landscape'             :   "Paysage",
+        "configGlobalDesc" : "Adjust category/article settings","styleDeNews" :   "Displaying the list of articles",
+        "styleDeNewsLegend" :   "Apply a style to the list of articles",
+        "masonryLegend"         :   "Masonry",
+        "gridLegend"            :   "Grid",
+        "listLegend"            :   "List",
+        "newsNombreImage"       :   "Number of columns",
+        "newsNombreImageLegend" :   "Slide to adjust the number of columns displayed",
+        "newsStyleAffichage"    :   "Style of articles",
+        "newsStyleAffichageDesc":   "Apply a style to articles",
+        "FormatImage"           :   "Image format",
+        "FormatImageLegend"     :   "Apply an image format to articles",
+        'landscape'             :   "Landscape",
         'portrait'              :   "Portrait",
-        'square'                :   "Carré",
-        "DescStyle1"           :  "Texte sous l'image", 
-        "DescStyle2"           :  "Texte encadré sur l'image",
+        'square'                :   "Square",
+        "DescStyle1"           :  "Text below the image", 
+        "DescStyle2"           :  "Text framed on image",
         "DescStyle3"           :  "Texte sur l'image, animé au survol",
         "addGalerieField": "ajouter une collection", 
         "DescStyle4"           :  "Texte sous l'image, bordures",
@@ -46,121 +46,124 @@
         "DescStyle5"           :  "Texte sous l'image, images arrondies (idéal images carrés)",
         "DescStyle6"           :  "Texte à côté de l'image",
         "Style"                :   "Style",
-        "newsTemplate"         :   "Affichage du modèle de page",
-        "newsTemplateLegend"   :   "Appliquez un modèle de page",
-        "resume"               :   "Résumé",
-        "resumeLegend"         :   "Afficher le texte d'introduction de l'article dans la liste des articles",
-        "resumecheckLegend"    :   "Afficher le résumé",
+        "newsTemplate"         :   "Displaying the page template",
+        "newsTemplateLegend"   :   "Apply a page template",
+        "resume"               :   "Summary",
+        "resumeLegend"         :   "Show article intro text in article list",
+        "resumecheckLegend"    :   "Show summary",
 
         //config category
-        "configDesc" 			: "Ajustez les paramètres des catégories",
+        "configDesc" 			: "Adjust category settings",
         "metaTitleLabel" 		: "Méta Title",
         "metaDescLabel" 		: "Méta Description",
-        "urlLabel" 				: "URL de la catégorie",
-        "cancel" 				: "Annuler",
-        "apply" 				: "Appliquer",
+        "urlLabel" 				: "Category URL",
+        "cancel" 				: "Cancel",
+        "apply" 				: "Apply",
 
-        "saveSuccesful": "Le contenu a été sauvegardé avec succès",
+        "saveSuccesful": "The content has been saved successfully",
 
         //datatable
-        "search" : "Rechercher",
-        "searchDesc" : "Rechercher un article, une catégorie...",
-        "title" : "Titre",
-        "author" : "Auteur",
-        "category" : "Catégorie",
-        "datepub" : "Date de Publication",
-        "state" : "Etat",
-        "deleteAction":"Suppression",
-        "confirmDeleteArticle": "Vous \u00eates sur le point de supprimer d\u00e9finitivement un article",
-        "none_result": "Auccune article correspond à la recherche",
-        "DeleteSuccesful" : "Supression avec succés",
-        "DeleteError" : " Erreur de la supression",
+        "search" : "Search",
+        "searchDesc" : "Search for an article, a category...",
+        "title" : "Title",
+        "author" : "Author",
+        "category" : "Category",
+        "datepub" : "Publication Date",
+        "state" : "State",
+        "deleteAction":"Deleting",
+        "none_result": "No articles match the search",
+        "DeleteSuccesful" : "Deleted successfully",
+        "DeleteError" : " Deletion error",
 
         // zonetoolbox
-        "publish"				: "Publier",
-        "addContent"			: "Ajouter du contenu",
-        "traduire"				: "Traduire",
-        "save": "Sauvegarder",
+        "publish"				: "Publish",
+        "addContent"			: "Add content",
+        "traduire"				: "Translate",
+        "save": "Save",
         "saveAction": "Sauvegarde",
-        "saveSuccesful": "Le contenu a été sauvegardé avec succès",
-        "saveError": "Une erreur s'est produite lors de la sauvegarde de l'article",
+        "saveError": "An error occurred while saving the article",
         "RessourceRequired" : "Ajoutez d'abord une image",
-        "emptyZone": "La zone sélectionnée est vide",
+        "emptyZone": "The selected area is empty",
         "howToAddContent": "Cliquez sur &laquo; Ajouter du contenu &raquo;<br/> puis glissez un bloc dans cette zone pour créer une section",
         "versions": "Versions",
-        "pasteTheSection" : "Coller la section",
-        "traduice": "Traduire",
-        "errorEditingElement":"Une erreur est survenue lors de l'édition de l'élément",
+        "pasteTheSection" : "Paste the section",
+        "traduice": "Translate",
+        "errorEditingElement":"An error occurred while editing the item",
 
         //Article 
-        "catArticle": "Catégorie de l'article",
-        "titleArticle" : "Titre de l'article",
-        "descriArticle": "Introduction de l'article",
-        "DefaultMessageUploaderLogo": "Cliquez ici ou glissez-déposez une image",
-        "saveAddArticle" : "Créer un article",
+        "catArticle": "Article Category",
+        "titleArticle" : "Article Title",
+        "descriArticle": "Introduction to the article",
+        "DefaultMessageUploaderLogo": "Click here or drag and drop an image",
+        "saveAddArticle" : "Create an article",
         "dateProg" : "",
-        "categoryLangNotFound" : "Categorie qui n'existe pas encore au langue selectionner",
-        "addArticle" : "Ajout Article",
-        "errorCategoryTraduction" : "Pour traduire cet article, veuillez vous assurer que la catégorie existe bien dans le langue <strong><% lang %></strong>.",
-        "errorChangeCategoryTraduction" : "Cet article à des traductions associées en <strong><% lang %></strong>. Pour changer la catégorie de l'article, veuillez vous assurer que la nouvelle catégorie existe bien dans les langues <strong><% lang %></strong>.",
-        "alreadyTraduice" : "Cet article existe déjà dans le langue <strong><% lang %></strong>.",
-        "translationSuccessfull": "Traduction de l'article avec succès",
-        "CategoryRequired":"Veuillez choisr une catégorie avant de sauvegarder",
-        "RessourceRequired":"Veuillez choisr un image à la une avant de sauvegarder",
-        "configDescArt" : "Ajustez les paramètres des articles",
-        "urlArtLabel" : "URL de l'article",
+        "categoryLangNotFound" : "Category that does not yet exist in the selected language",
+        "addArticle" : "Add Article",
+        "errorCategoryTraduction" : "To translate this article, please make sure the category exists in the language <strong><% lang %></strong>.",
+        "errorChangeCategoryTraduction" : "This article has associated translations in <strong><% lang %></strong>. To change the category of the article, please make sure that the new category exists in the languages <strong><% lang %></strong>.",
+        "alreadyTraduice" : "This article already exists in the language <strong><% lang %></strong>.",
+        "translationSuccessfull": "Article successfully translated",
+        "CategoryRequired":"Please choose a category before saving",
+        "RessourceRequired":"Please choose a featured image before saving",
+        "configDescArt" : "Adjust articles settings",
+        "urlArtLabel" : "Article URL",
 
         //publié
         "publishtitre" : "Publication",
-        "publishDesc" : "Publiez maintenant ou programmez la publication de votre article à la date que vous souhaitez",
-        "programOption" : "Programmez l'envoi",
-        "programOptionDesc" : "Programmez une date de publication de l'article",
-        "oui" : "Oui",
-        "non" : "Non",
-        "willPublish" : "Votre article sera publié le",
-        "programPublish" : "Programmer la publication",
-        "publishImmediate" : "Votre article sera publié immédiatement",
-        "publishArticle" : "Publier l'article",
+        "publishDesc" : "Publish now or schedule your article to be published on a date of your choice",
+        "programOption" : "Schedule the sending",
+        "programOptionDesc" : "Schedule a publication date for the article",
+        "oui" : "Yes",
+        "non" : "No",
+        "willPublish" : "Your article will be published on",
+        "programPublish" : "Schedule publication",
+        "publishImmediate" : "Your article will be published immediately",
+        "publishArticle" : "Publish the article",
         "dateFormat":"Y-m-d",
-        "publishDone":"Article publié",
-        "programDone":"Article programmé",
-        "publishError": "Erreur de publier",
-        "errorPublishingInvalideElement": "Votre article n'est pas valid",
-        "majpublicationDesc":"Votre article été enregistré, souhaites vous mettre à jour l'aticle programmé",
-        "majpublication":"Mettre à jour la publication",
+        "publishDone":"Article published",
+        "programDone":"Scheduled article",
+        "publishError": "Error posting",
+        "errorPublishingInvalideElement": "Your article is not valid",
+        "majpublicationDesc":"Your article has been saved. Would you like to update the scheduled article ?",
+        "majpublication":"Update the publication",
 
         //available
-        "addContent":"Ajouter du contenu",
-        "availableDesc":"Glissez vos blocs sur l’emplacement souhaité ou cliquez sur un bloc pour l’ajouter en bas de page.",
-        "standardBlocks":"Blocs standards",
-        "block":"Bloc",
-        "move":"Déplacer",
-        "edit":"Éditer",
-        "delete":"Supprimer",
-        "standard":"Blocs standards",
-        "advanced":"Blocs avancés",
-        "others":"Autres blocs",
-        "errorRenderDuring":"Une erreur nous empêche d'afficher ce bloc",
-        "oops":"Oups !",
+        "availableDesc":"Slide your blocks on the desired location or click on a block to add it to footnote.",
+        "standardBlocks":"Standard blocks",
+        "block":"blocks",
+        "move":"Move",
+        "edit":"Edit",
+        "delete":"Delete",
+        "standard":"Standard blocks",
+        "advanced":"Advanced Blocks",
+        "others":"Other blocks",
+        "errorRenderDuring":"An error prevents us from displaying this block",
+        "oops":"Oops !",
 
         //preview
         "resizeByDevice": "< Redimensionner par support",
         "deviceMobile": "Mobile",
-        "deviceTablet": "Tablette",
-        "deviceDesktop": "Ordinateur",
-        "deviceLargeDesktop": "Ordinateur avec écran large ( > 1441px )",
-        "closePreview": "Fermer",
-        "saveVersion": "Restaurer",
-        "toggleFullscreen": "Taille réelle / Ajuster",
-        "cloningError": "Une erreur est survenue lors du clonage de la page, essayez d'actualiser la fenètre",
+        "deviceTablet": "Tablet",
+        "deviceDesktop": "Computer",
+        "deviceLargeDesktop": "Computer with wide screen ( > 1441px )",
+        "closePreview": "Close",
+        "saveVersion": "Restore",
+        "toggleFullscreen": "Actual size / Adjust",
+        "cloningError": "An error occurred while cloning the page, try refreshing the window",
         "batchCloningError": "Une erreur est survenue lors du clonage des page, essayez d'actualiser la fenêtre",
-        "layoutChanged": "Le layout a été changé avec succès.",
+        "layoutChanged": "The layout has been changed successfully.",
         "success": "Effectué !",
-        "introPageTitle":"Mes pages",
-        "previousVersionSuccesful":"La zone a été restaurée avec succès et <br/>la page a été sauvegardée",
+        "introPageTitle":"My pages",
+        "previousVersionSuccesful":"The area has been successfully restored and <br/>the page has been saved",
 
         //menu
-        "addToMenu":"Ajouter au menu",
+        "addToMenu":"Add to menu",
+        "allArticles": "All articles",
+        "unclassified": "Unclassified",
+        "published": "Published",
+        "draft": "Draft",
+        "program": "Program",
+        "open": "Open"
     },
     "fr-fr": true,
     "fr-ca": true
