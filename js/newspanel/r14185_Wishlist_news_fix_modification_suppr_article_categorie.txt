Revision: r14185
Date: 2025-04-30 11:41:10 +0300 (lrb 30 Apr 2025) 
Author: frahajanirina 

## Commit message
Wishlist:news:fix modification/suppr article & categorie

## Files changed

## Full metadata
------------------------------------------------------------------------
r14185 | frahajanirina | 2025-04-30 11:41:10 +0300 (lrb 30 Apr 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/CategorieAddView.js

Wishlist:news:fix modification/suppr article & categorie
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 14184)
+++ src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 14185)
@@ -92,7 +92,7 @@
         var article = self.collection.get(articleId);
         var role = self.app.user.role;
         var client = article.content.client;
-        if (role != 'root' && client == 'linkeo') {
+        if (role === 'admin' && client == 'linkeo') {
           $(this).parent().addClass('disabled');
           $(this).css('cursor', 'not-allowed');
         }
@@ -149,7 +149,7 @@
       var article = this.collection.get(articleId);
       var role = this.app.user.role;
       var client = article.content.client;
-      if (role != 'root' && client == 'linkeo') {
+      if (role === 'admin' && client == 'linkeo') {
         
         return false;
       }
@@ -171,7 +171,7 @@
         var model = this.collection.get(articleId);
         var role = this.app.user.role;
         var client = model.content.client;
-        if (role != 'root' && client == 'linkeo') {
+        if (role === 'admin' && client == 'linkeo') {
           
           return false;
         }
@@ -212,7 +212,7 @@
       var article = this.collection.get(articleId);
       var role = this.app.user.role;
       var client = article.content.client;
-      if (role != 'root' && client == 'linkeo') {
+      if (role === 'admin' && client == 'linkeo') {
         
         return false;
       }
Index: src/js/JEditor/NewsPanel/Views/CategorieAddView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(révision 14184)
+++ src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(révision 14185)
@@ -157,7 +157,7 @@
                 var isArticleAuthorNotLinkeo = this.model.lang[this.lang.id].isArticleAuthorNotLinkeo;
                 var nbArticle = this.model.lang[this.lang.id].nbArticle;
                 var role = this.app.user.role;
-                if (role != 'root' && nbArticle > 0 && isArticleAuthorNotLinkeo === false) {
+                if (role === 'admin' && nbArticle > 0 && isArticleAuthorNotLinkeo === false) {
                     $('button.remove').css('cursor', 'not-allowed');
                 }
                 
@@ -236,7 +236,7 @@
             var isArticleAuthorNotLinkeo = this.model.lang[this.lang.id].isArticleAuthorNotLinkeo;
             var nbArticle = this.model.lang[this.lang.id].nbArticle;
             var role = this.app.user.role;
-            if (role != 'root' && nbArticle > 0 && isArticleAuthorNotLinkeo === false) {
+            if (role === 'admin' && nbArticle > 0 && isArticleAuthorNotLinkeo === false) {
                 
                 return isArticleAuthorNotLinkeo;
             }
