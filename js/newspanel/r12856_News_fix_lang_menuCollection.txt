Revision: r12856
Date: 2024-08-21 11:55:34 +0300 (lrb 21 Aog 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: fix lang menuCollection

## Files changed

## Full metadata
------------------------------------------------------------------------
r12856 | sraz<PERSON><PERSON><PERSON>oa | 2024-08-21 11:55:34 +0300 (lrb 21 Aog 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Links/Models/Link.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NavigationPanel/NavigationPanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Models/NewsNavCollection.js

News: fix lang menuCollection
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NavigationPanel/NavigationPanel.js
===================================================================
--- src/js/JEditor/NavigationPanel/NavigationPanel.js	(révision 12855)
+++ src/js/JEditor/NavigationPanel/NavigationPanel.js	(révision 12856)
@@ -77,10 +77,12 @@
                         load: function () {
                             var that = this;
                             this.pageList = PageCollection.getInstance();
-                            this.newsList = NewsNavCollection.getInstance(this.languages);
+                            if (this.app.user.can('access_panel_news', __IDEO_NEWS__)){
+                                this.newsList = NewsNavCollection.getInstance(this.languages);
+                                this.currentNewsList = this.newsList;
+                            } 
                             this.pageSupportList = PageSupportCollection.getInstance();
                             this.menuList = MenuCollection.getInstance();
-                            this.currentNewsList = this.newsList;
                             this.listenToOnce(this.menuList, Events.BackboneEvents.SYNC, _.bind(function () {
                                 this._pagesByLang = this.pageList.groupBy('lang');
                                 this._pagesSupportByLang = this.pageSupportList.groupBy('lang');
Index: src/js/JEditor/NewsPanel/Models/NewsNavCollection.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/NewsNavCollection.js	(révision 12855)
+++ src/js/JEditor/NewsPanel/Models/NewsNavCollection.js	(révision 12856)
@@ -12,7 +12,7 @@
         translations :{
             "Français": { "news": "Actualités" },
             "English": { "news": "News" },
-            "default": { "news": "News"}
+            "default": { "news": "Actualités"}
         },
         constructor: function(models, options) {
             
@@ -27,10 +27,10 @@
         addLangs:function(langs){
             var self = this; 
             langs.forEach(function(element) {
-                var translation = self.translations[element.name];
+                var translation = (self.translations.hasOwnProperty(element.name))? self.translations[element.name] :self.translations['default'] ;
                 var lang = element.id.replace('_', '-').toLowerCase();
                 var langpath = (__IDEO_DEFAULT_LANG__ === lang)? '' : '/'+lang;
-                var href = "[[ptr]]"+ langpath +"/news";
+                var href = langpath +"/news/";
                 self.add(new NewsNav({lang: element.id, name : translation.news, href: href , lang : element.id})); 
             });
         },
Index: src/js/JEditor/Commons/Links/Models/Link.js
===================================================================
--- src/js/JEditor/Commons/Links/Models/Link.js	(révision 12855)
+++ src/js/JEditor/Commons/Links/Models/Link.js	(révision 12856)
@@ -60,7 +60,6 @@
         this._super();
         this.pageList = PageCollection.getInstance();
         this.languages = ContentLanguageList.getInstance();
-        this.newsList = NewsNavCollection.getInstance(this.languages);
         this.pageSupportList = PageSupportCollection.getInstance();
         this.file = null;
         this.fileGroupList = FileGroupCollection.getInstance();
