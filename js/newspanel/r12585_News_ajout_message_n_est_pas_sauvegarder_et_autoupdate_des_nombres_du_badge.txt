Revision: r12585
Date: 2024-07-10 16:44:17 +0300 (lrb 10 Jol 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: ajout message n'est pas sauvegarder et autoupdate des nombres du badge

## Files changed

## Full metadata
------------------------------------------------------------------------
r12585 | srazanandralisoa | 2024-07-10 16:44:17 +0300 (lrb 10 Jol 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Models/Article.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Models/Categorie.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Models/CategorieCollection.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Models/CategorieLang.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/AddArticleView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/SaveButton.js

News: ajout message n'est pas sauvegarder et autoupdate des nombres du badge
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Models/CategorieCollection.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/CategorieCollection.js	(révision 12584)
+++ src/js/JEditor/NewsPanel/Models/CategorieCollection.js	(révision 12585)
@@ -1,11 +1,11 @@
 define([
     "JEditor/Commons/Ancestors/Models/DBCollection",
     "JEditor/Commons/Ancestors/Models/Collection",
-    "./Categorie"
+    "./Categorie",
 ], function(
         DBCollection,
         Collection,
-        Categorie
+        Categorie      
         ) {
     var CategorieCollection = Collection.extend(
             /**
@@ -31,6 +31,9 @@
                         parse: function (response) {
                             return response.data; 
                         },
+                        comparator: function(category) {
+                            return category.get('isUncategorized') ? 1 : 0;
+                        },
                         groupBy: function(iterator, context) {
                             if (iterator!=='lang') {
                                 this._super();
Index: src/js/JEditor/NewsPanel/Models/CategorieLang.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/CategorieLang.js	(révision 12584)
+++ src/js/JEditor/NewsPanel/Models/CategorieLang.js	(révision 12585)
@@ -11,6 +11,13 @@
             url:"",
             nbArticle: 0
         },
+        toJSONData: function (){
+           return { 
+            title: this.title,
+            description: this.description,                
+          
+        };
+        }
     });
     CategorieLang.SetAttributes(['title','description', 'metaTitle', 'metaDescription', "url", "nbArticle"]);
     return CategorieLang;
Index: src/js/JEditor/NewsPanel/Views/SaveButton.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/SaveButton.js	(révision 12584)
+++ src/js/JEditor/NewsPanel/Views/SaveButton.js	(révision 12585)
@@ -108,25 +108,11 @@
                          * @public
                          */
                         saveArticle: function (e) {
-                            var that = this;
-                            var valid = true;
+                            
                             this.listenToOnce(this.model, Events.BackboneEvents.SYNC, this.onSave);
                             this.listenToOnce(this.model, Events.BackboneEvents.ERROR, this.onError);
-                            var currentArticles = this.model.collection.where({ news: this.model.news });
-                            selected = this.model.getCategoryModel();
-                            this.model.set('publicationDate',null);
-                            this.model.set('programmingDate',null);
-                            currentArticles.forEach(function(article) {
-                                if (!selected.lang || !selected.lang[article.lang]) {
-                                    valid = false;
-                                    that.error({
-                                        title: translate("saveAction"),
-                                        message: translate("errorChangeCategoryTraduction", {'lang': article.lang})
-                                    });
-                                }
-                                
-                            });
-                            if(valid) this.model.save();
+                            this.model.unsetPublishData();
+                            this.model.save();
 
                             return false;
                         }
Index: src/js/JEditor/NewsPanel/Models/Article.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Article.js	(révision 12584)
+++ src/js/JEditor/NewsPanel/Models/Article.js	(révision 12585)
@@ -25,6 +25,7 @@
                             publicationDate:null, 
                             programmingDate: null,
                             metaTitle: "",
+                            metaDescription:"",
                             metaOpengraph: "",
                             page:null,
                             news:null,
@@ -40,7 +41,7 @@
                             this.getPage();
                             this.getCategory();
                             this.pageCollection = PageCollection.getInstance();
-                            this.lastState = this.toJSON();
+                            this.lastState = this.toJSONData();
                             this.on(Events.BackboneEvents.SYNC, this._onSync);
                         },
                         getCategoryModel: function(){
@@ -63,16 +64,27 @@
                             return this.pageModel;
                         },                  
                         _onSync: function(model, response, options) {
-                            this.lastState = this.toJSON();
+                            this.lastState = this.toJSONData();
                         },
                         hasUnsavedChanges: function() {
-                            return !_.isEqual(this.toJSON(), this.lastState);
+                            return !(_.isEqual(this.toJSONData(), this.lastState) && _.isEqual(this.content.content.toJSON(), this.content.content.lastState));
                         },
+                        toJSONData: function() {
+                            var returnValue = {
+                                id: this.id,
+                                category:this.category,
+                                title : this.title,
+                                introduction : this.introduction
+                            };
+                            return returnValue;
+                        },
                         cancel: function() {
-                            this.set(this.lastState);
+                            this.set('category', this.lastState.category);
+                            this.set('title', this.lastState.title);
+                            this.set('introduction', this.lastState.introduction);
                         },
                         setCategorY: function (categorie) {
-                            this.category[0]=categorie;
+                            this.set('category', [categorie]);
                             this.categoryModel = null;
                             this.getCategoryModel();
                         },
@@ -91,6 +103,10 @@
                         },
                         getStateValue: function(){
                             return 0;
+                        },
+                        unsetPublishData: function() {
+                            this.set('publicationDate', null);
+                            this.set('programmingDate', null);
                         }
                     });
                    
Index: src/js/JEditor/NewsPanel/Models/Categorie.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Categorie.js	(révision 12584)
+++ src/js/JEditor/NewsPanel/Models/Categorie.js	(révision 12585)
@@ -22,16 +22,23 @@
                     initialize: function() {
                         this._super();
                         this.file = null;
+                        this.lastState = this.toJSONData();
                         this.on(Events.BackboneEvents.SYNC, this._onSync);
                     },
-                    _onSync: function() {
-                        this.lastState = this;
+                    _onSync: function(model, response, options) {
+                        this.lastState = this.toJSONData();
                     },
                     hasUnsavedChanges: function() {
-                        return false;
+                       if (this.id=='uncategorized') return false;
+                       return !_.isEqual(this.toJSONData(), this.lastState);
                     },
                     cancel: function() {
-                        this.set(this.lastState);
+                        this.set('ressource',this.lastState.ressource)
+                        var self = this
+                        _.each(this.lastState.lang, function(langData, langKey) {
+                           self.lang[langKey].title = langData.title;
+                           self.lang[langKey].description = langData.description;
+                        });
                     },
                     getByLanguage :function (lang) {
                         return this.lang[lang.id];
@@ -48,8 +55,16 @@
                         this.file =  this.fileCollection.get(this.ressource);
                         return this.file;
                     },
-                    parse : function(data) {
-                        return (data.data)? data.data : data;
+                    toJSONData: function() {
+                        var returnValue = {
+                            id: this.id,
+                            ressource:this.ressource,
+                            lang : {}
+                        };
+                        _.each(this.lang, function(langData, langKey) {
+                            returnValue.lang[langKey] = (langData instanceof CategorieLang)? langData.toJSONData() : langData;
+                        });
+                        return returnValue;
                     },
                     parse: function(data) {
                         item = (data.data)? data.data : data;
@@ -61,6 +76,8 @@
                         };
                         // Parcourir les langues disponibles et créer les objets correspondants
                         _.each(item.lang, function(langData, langKey) {
+                             var existingLang = (this.get('lang'))?this.get('lang')[langKey]:null;
+                             var nbArticle = existingLang ? existingLang.nbArticle : 0;
                             category.lang[langKey] = new CategorieLang({
                                 title: langData.title,
                                 description: langData.description,
@@ -67,12 +84,38 @@
                                 metaTitle: langData.metaTitle,
                                 metaDescription: langData.metaDescription,
                                 url: langData.url,
-                                nbArticle: 0
+                                nbArticle: nbArticle
                             });
-                        });
+                        }, this);
                         return category;
+                    },
+                });
+                Categorie.prototype.incrementLangCounter = function(lang, delta) {
+                    delta = (typeof delta !== 'undefined') ? delta : 1;
+                    var langData = this.get('lang')[lang];
+                    if (langData) {
+                        langData.set('nbArticle', langData.get('nbArticle') + delta);
                     }
-                });
+                    this.set('numberArticlesInCategory', this.get('numberArticlesInCategory') + delta, {silent: true});
+                }
+                Categorie.prototype.initializeLangCounters = function() {
+                    var langCounters = this.get('lang');
+                    _.each(langCounters, function(lang) {
+                        if (!lang) {
+                            lang = new CategorieLang({
+                                title: '',
+                                description: '',
+                                metaTitle: '',
+                                metaDescription: '',
+                                url: '',
+                                nbArticle: 0
+                            });
+                        } else {
+                            lang.set('nbArticle', 0);
+                        }
+                    });
+                };
+                
             Categorie.SetAttributes(['ressource','lang','numberArticlesInCagetory']);
         return Categorie;
     });
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12584)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12585)
@@ -15,6 +15,8 @@
     "./Models/NewsConfig",
     "JEditor/Commons/Pages/Models/PageCollection",
     "./Models/Article",
+    "JEditor/NewsPanel/Models/CategorieLang",
+    "JEditor/NewsPanel/Models/Categorie",
     "JEditor/App/Views/RightPanelView",
     "JEditor/NewsPanel/Views/AvailableView",
     "JEditor/NewsPanel/Views/PublishConfigView",
@@ -40,6 +42,8 @@
             NewsConfig,
             PageCollection,
             Article,
+            CategorieLang,
+            Categorie,
             RightPanelView,
             AvailableView,
             PublishConfigView,
@@ -57,7 +61,7 @@
                                     'click #available-blocks-trigger' : 'showAvailableBlocks',
                                     'click #publish-config-trigger' : 'showPublishConfig',
                                     'click #show-zone-version': 'showZoneVersionsPanel',
-                                    // 'click #params' : '_onParams',
+                                    
                                 },
                                 cssFile: __IDEO_INTEGRATION_PATH__ + "/build/css/news_panel.css",
 
@@ -76,7 +80,7 @@
                                 },
                                 updatePublishedCount:function () {
                                     this.$(".sub-header__action-content .icon .count").text(this.articles.getPublished());
-                                  },
+                                },
                                 load: function () {
                                     var loaded = 0;
                                     this.categories = CategorieCollection.getInstance();
@@ -83,28 +87,37 @@
                                     this.articles =  ArticlesCollection.getInstance();
                                     this.pageCollection = PageCollection.getInstance();
                                     this.newsConfig = NewsConfig.getInstance();
-                                    function onLoaded() {
+                                    var onLoaded = function() {
                                         loaded++;
-                                        if (loaded === 3) {
-                                        this.loadingEnd();
+                                        if (loaded === 3) { 
+                                            this.loadingEnd();
+                                            var uncategorizedCategory = this.getUncategorizedCategory();
+                                            this.languages.each(function(lang) {
+                                              var  articles = this.articles.filter(function(item) {
+                                                    return item.get('lang') === lang.id && item.get('category').length === 0;
+                                                });
+                                                uncategorizedCategory.lang[lang.id].nbArticle = articles.length;
+                                            },this);
+                                            this.categories.add(uncategorizedCategory);
+                                            this.categories.trigger('change');
                                         }
-                                    }
+                                    }.bind(this);
+                                    
                                     this.listenToOnce(this.categories, "sync", onLoaded);
                                     this.listenToOnce(this.articles, "sync", onLoaded);
                                     this.listenToOnce(this.newsConfig, "sync", onLoaded);
-                                    var that = this;
-                                    this.articles.on('add', function(article) {
-                                        that.addArticleOnCategory(article);
-                                    });
-                                    
-                                    this.articles.on('remove', function(article) {
-                                        that.removeArticleOnCategory(article);
-                                    });
 
+                                    this.listenTo(this.articles, 'add', this.onArticleAdd);
+                                    this.listenTo(this.articles, 'remove', this.onArticleRemove);
+                                    this.listenTo(this.articles, 'change:category', this.onArticleCategoryChange);
+                                    this.listenTo(this.articles, 'add', this.updateTranslationCount);
+                                    this.listenTo(this.articles, 'remove', this.updateTranslationCount);
+                                    this.categories.on('remove', this.onCategoryRemoved.bind(this));
+
                                     this.categories.fetch();
                                     this.articles.fetch();
-                                    this.newsConfig.fetch()
-                                
+                                    this.newsConfig.fetch();
+                                   
                                     this.on(Events.NewsPanelEvents.CATEGORIE_CHANGE, this._onCategorieChange); 
                                     this.on(Events.NewsPanelEvents.ARTICLE_CHANGE, this._onArticleChange); 
 
@@ -144,19 +157,124 @@
                                    
                                     this.listenTo(this.childViews.langDropDown, "selected:choice", this._onLangSelect);
                                     this.listenTo(this.articles, 'change', this.updatePublishedCount);
-
+                                    
                             
                                 },
-                                addArticleOnCategory:function (article){
-                                    var categorie = article.getCategoryModel();
-                                    categorie.lang[article.lang].nbArticle++;
+                                getUncategorizedCategory: function (){
+                                    var uncategorizedCategory = this.categories.get('uncategorized');
+                                    if (!uncategorizedCategory) this.addUncategorizedCategory();
+                                    return this.categories.get('uncategorized');
+                                },
+                                addUncategorizedCategory: function() {
+                                    var uncategorizedCategory = new Categorie({
+                                        id: 'uncategorized',
+                                        ressource: null,
+                                        numberArticlesInCategory: 0,
+                                        lang: {},
+                                        isUncategorized: true // Ajouter cette propriété
+                                    });
+                                
+                                    this.languages.each(function(lang) {
+                                        uncategorizedCategory.get('lang')[lang.id] = new CategorieLang({
+                                            title: 'Non classés',
+                                            description: '',
+                                            metaTitle: '',
+                                            metaDescription: '',
+                                            url: '',
+                                            nbArticle: 0
+                                        });
+                                    },this);
+                                
+                                    uncategorizedCategory.initializeLangCounters(this.languages);
+                                    this.categories.add(uncategorizedCategory);
+                                    this.categories.sort();
+                                },
+                                onCategoryRemoved: function(category) {
+                                    this.articles.each(function(article) {
+                                        var categoryIds = article.get('category');
+                                        if (_.contains(categoryIds, category.id)) {
+                                            var newCategoryIds = _.without(categoryIds, category.id);
+                                            article.set('category', newCategoryIds);
+                                            article.categoryModel = null;
+                                            article.getCategoryModel();
+                                        }
+                                    });
+                                },
+                                onArticleAdd: function(article) {
+                                    this.updateCategoryCounter(article, 1);
+                                },
+                            
+                                onArticleRemove: function(article) {
+                                    this.updateCategoryCounter(article, -1);
+                                },
+                            
+                                onArticleCategoryChange: function(article) {
+                                    var uncategorizedCategory = this.getUncategorizedCategory();
+                                    var previousCategories = article.previous('category');
+                                    var currentCategories = article.get('category');
+                                    var lang = article.get('lang');
+                            
+                                    // Décrémenter les compteurs des anciennes catégories
+                                    if (previousCategories.length === 0) uncategorizedCategory.incrementLangCounter(lang, -1);
+    
+                                    _.each(previousCategories, function(categoryId) {
+                                        var category = this.categories.get(categoryId);
+                                        if (category) {
+                                            category.incrementLangCounter(lang, -1);
+                                        }
+                                    }, this);
+                            
+                                    // Incrémenter les compteurs des nouvelles catégories
+                                    if (currentCategories.length === 0) uncategorizedCategory.incrementLangCounter(lang, 1);
+                                    _.each(currentCategories, function(categoryId) {
+                                        var category = this.categories.get(categoryId);
+                                        if (category) {
+                                            category.incrementLangCounter(lang, 1);
+                                        }
+                                    }, this);
                                     this.categories.trigger('change');
                                 },
-                                removeArticleOnCategory:function (article){
-                                    var categorie = article.getCategoryModel();
-                                    categorie.lang[article.lang].nbArticle--;
+                            
+                                updateCategoryCounter: function(article, delta) {
+                                    var categoryIds = article.get('category');
+                                    var uncategorizedCategory = this.getUncategorizedCategory();
+                                    var lang = article.get('lang');
+                                    if (categoryIds.length === 0) uncategorizedCategory.incrementLangCounter(lang, delta);
+    
+                                    _.each(categoryIds, function(categoryId) {
+                                        var category = this.categories.get(categoryId);
+                                        if (category) {
+                                            category.incrementLangCounter(lang, delta);
+                                        }
+                                        
+                                    }, this);
                                     this.categories.trigger('change');
                                 },
+                                updateTranslationCount: function(article) {
+                                   if (article.isNew()) {
+                                        // Nouvel article ajouté
+                                        var newsId = article.get('news');
+                                        if (newsId) {
+                                            var newsGroup = this.articles.filter(function(item) {
+                                                return item.get('news') === newsId;
+                                            });
+                                            _.each(newsGroup, function(item) {
+                                                item.set('numberOfTranslation', newsGroup.length - 1);
+                                            });
+                                        }
+                                    } else if (article.collection) {
+                                        // Article supprimé
+                                        var newsId = article.get('news');
+                                        if (newsId) {
+                                            var newsGroup = this.articles.filter(function(item) {
+                                                return item.get('news') === newsId;
+                                            });
+                                            _.each(newsGroup, function(item) {
+                                                item.set('numberOfTranslation', newsGroup.length - 1);
+                                            });
+                                        }
+                                    }
+                                },
                                 /**
                                  * met à jour la vue de page (zone du centre de l'écran)
                                  */
@@ -460,15 +578,17 @@
                                 _onArticleSelect : function(view, article) {
                                     if (this.currentCategorie && this.currentCategorie.hasUnsavedChanges()) {
                                         this.confirmUnsaved({
-                                            message : translate("quitWithoutSaving"),
+                                            message : translate("quitWithoutSavingCat"),
                                             title : translate("unsavedChanges"),
                                             type : 'delete-not-saved',
                                             onYes : _.bind(function() {
                                                 this.currentCategorie.save();
+                                                this.currentCategorie = null;
                                                 this.currentArticle = article;
                                             }, this),
                                             onNo : _.bind(function() {
                                                 this.currentCategorie.cancel();
+                                                this.currentCategorie = null;
                                                 this.currentArticle = article;
                                             }, this),
                                             options : {
@@ -476,22 +596,49 @@
                                                 dontAskAgain : true
                                             }
                                         });
-                                    } else
-                                     this.currentArticle = article;
+                                    } else if (this.currentArticle && this.currentArticle.hasUnsavedChanges()) {
+                                        this.confirmUnsaved({
+                                            message : translate("quitWithoutSavingArt"),
+                                            title : translate("unsavedChanges"),
+                                            type : 'delete-not-saved',
+                                            onYes : _.bind(function() {
+                                                this.childViews.newsEditorView.articleEditorView.stopListening();
+                                                this.childViews.newsEditorView.removeChildren();
+                                                this.currentArticle.unsetPublishData();
+                                                this.currentArticle.save();
+                                                this.currentArticle = article;
+                                            }, this),
+                                            onNo : _.bind(function() {
+                                                this.currentArticle.content.content.cancel();
+                                                this.currentArticle.cancel();
+                                                this.currentArticle = article;
+                                            }, this),
+                                            options : {
+                                                dialogClass : 'delete no-close',
+                                                dontAskAgain : true
+                                            }
+                                        });
+                                    } 
+                                    else{
+                                        this.currentCategorie = null;
+                                        this.currentArticle = article;
+                                    } 
                                 },
                                 _onCategorieSelect : function(view, categorie) {
                                     if (this.currentCategorie && this.currentCategorie.hasUnsavedChanges()) {
                                         this.confirmUnsaved({
-                                            message : translate("quitWithoutSaving"),
+                                            message : translate("quitWithoutSavingCat"),
                                             title : translate("unsavedChanges"),
                                             type : 'delete-not-saved',
                                             onYes : _.bind(function() {
                                                 this.currentCategorie.save();
+                                                this.currentArticle = null;
                                                 this.currentCategorie = categorie;
                                             }, this),
                                             onNo : _.bind(function() {
                                                 
                                                 this.currentCategorie.cancel();
+                                                this.currentArticle = null;
                                                 this.currentCategorie = categorie;
                                             }, this),
                                             options : {
@@ -499,8 +646,31 @@
                                                 dontAskAgain : true
                                             }
                                         });
-                                    } else
-                                        this.currentCategorie = categorie;
+                                    } else if (this.currentArticle && this.currentArticle.hasUnsavedChanges()) {
+                                        this.confirmUnsaved({
+                                            message : translate("quitWithoutSavingArt"),
+                                            title : translate("unsavedChanges"),
+                                            type : 'delete-not-saved',
+                                            onYes : _.bind(function() {
+                                                this.childViews.newsEditorView.articleEditorView.stopListening();
+                                                this.childViews.newsEditorView.removeChildren();
+                                                this.currentArticle.save();
+                                                this.currentArticle = null;
+                                                this.currentCategorie = categorie;
+                                            }, this),
+                                            onNo : _.bind(function() {
+                                                this.currentArticle.content.content.cancel();
+                                                this.currentArticle.cancel();
+                                                this.currentArticle = null;
+                                                this.currentCategorie = categorie;
+                                            }, this),
+                                            options : {
+                                                dialogClass : 'delete no-close',
+                                                dontAskAgain : true
+                                            }
+                                        });
+                                    } 
+                                    else   this.currentCategorie = categorie;
                                 },
                                 /**
                                  * Montre le panneaux de blocs disponibles
@@ -531,11 +701,11 @@
                                     function onClose() {
                                         this.rightPanelView.removeContent(this.childViews.publishConfigView);
                                         this.stopListening(this.childViews.publishConfigView);
+                                        this.stopListening(this.childViews.publishConfigView.model);
                                         this.rightPanelView.hidePanel();
                                     }
                                     try {
-                                        
-                                        if(this.currentArticle.get('title') =='' || !this.currentArticle.get('ressource')){
+                                        if(this.currentArticle.get('title') =='' || !this.currentArticle.get('ressource') || (this.currentArticle.get('category').length == 0)){
                                             this.error({
                                                 message: translate("errorPublishingInvalideElement"),
                                                 title: translate("error")
@@ -542,7 +712,24 @@
                                             });
                                             return false
                                         }
-                                           
+                                        else if (this.currentArticle && this.currentArticle.hasUnsavedChanges()) {
+                                            this.confirmUnsaved({
+                                                message : translate("quitWithoutSavingArt"),
+                                                title : translate("unsavedChanges"),
+                                                type : 'delete-not-saved',
+                                                onYes : _.bind(function() {
+                                                    this.currentArticle.save();
+                                                }, this),
+                                                onNo : _.bind(function() {
+                                                    this.currentArticle.content.content.cancel();
+                                                    this.currentArticle.cancel();
+                                                }, this),
+                                                options : {
+                                                    dialogClass : 'delete no-close',
+                                                    dontAskAgain : true
+                                                }
+                                            });
+                                        }    
                                         this.childViews.publishConfigView = new PublishConfigView({
                                             rightPanelView : this.rightPanelView,
                                             model: this.currentArticle,
@@ -576,8 +763,9 @@
                                 },
                                 publishDone :function (){
                                     this.stopListening(this.childViews.publishConfigView.model, Events.BackboneEvents.ERROR, this.publishError);
+                                    var type = (this.childViews.publishConfigView.model.programmingDate)? 'programDone':'publishDone';
                                     $.toast({
-                                        text: translate("publishDone"), 
+                                        text: translate(type), 
                                         icon: 'icon-check-circle', 
                                         type:'success',
                                         appendTo: '#news-view .main',
Index: src/js/JEditor/NewsPanel/Views/AddArticleView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/AddArticleView.js	(révision 12584)
+++ src/js/JEditor/NewsPanel/Views/AddArticleView.js	(révision 12585)
@@ -71,8 +71,14 @@
             this.lang = this.options.language;
             this.bylang = this._categories.groupBy('lang');
             this._groupList = this.bylang[this.lang.id];
+
+            // Filtrer pour exclure la catégorie "Non classé"
+            var filteredGroupList = new Backbone.Collection(this._groupList.filter(function(category) {
+                return category.id !== 'uncategorized';
+            }));
+
             this._categoryDropdown = new DropDownView({
-                collection: this._groupList,
+                collection: filteredGroupList,
                 _default: (this._currentCategory)?this._categories.get(this._currentCategory):null,
                 labelField: _.bind(function(model) {
                     return model.lang[this.lang.id].title;
@@ -81,8 +87,23 @@
             this.render();
         },
         _onCategorySelected : function(view, selected) {
-            this._currentCategory = selected;
-            this.model.setCategorY(this._currentCategory.id);
+           var valid = true;
+           var that =this;
+            var currentArticles = this.collection.where({ news: this.model.news });
+            currentArticles.forEach(function(article) {
+                if (!selected.lang || !selected.lang[article.lang]) {
+                    valid = false;
+                    that.error({
+                        title: translate("saveAction"),
+                        message: translate("errorChangeCategoryTraduction", {'lang': article.lang})
+                    });
+                }
+                
+            });
+            if (valid) {
+                this._currentCategory = selected;
+                this.model.setCategorY(this._currentCategory.id);
+            }
         },
         setIntroduction: function(event) {
             console.log('setIntroduction appelé');
@@ -195,7 +216,6 @@
                 this.model.lang[this.lang.id] = this.currentArticleLang;
                 this.model.save();
                 this.collection.add(this.model);
-                this.collection.trigger('change');
             }
         },
 
Index: src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 12584)
+++ src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 12585)
@@ -50,6 +50,8 @@
         this.categoryId = categoryId;
         self = this;
         this.filteredBy = function (item) {
+          if (categoryId == 'uncategorized')
+            return item.category.length == 0;
           return item.category.some(function(cat) {
             return cat === categoryId;
           });
@@ -91,7 +93,11 @@
       var that = this;
        return list.map(function(list) {
         var categoryModel = list.getCategoryModel();
-        var categories = categoryModel.lang[lang];
+        var categorie = ''
+        if (categoryModel){ 
+          var categories = categoryModel.lang[lang];
+          categorie = categories.title;
+        }
         var pageModel = list.getPageModel();
         var dataPub = that.getHtmlDatePub(list)
         json = {
@@ -98,8 +104,9 @@
           cid : list.id,
           image : (list.file)?list.file.fileUrl:'',
           title : list.title,
+          introduction : list.introduction,
           author : (list.content)? list.content.client : "",
-          categorie : categories.title,
+          categorie : categorie,
           category : list.category,
           id : list._id,              
           publicationDate: dataPub, 
@@ -124,10 +131,12 @@
       var $page = $target.parents('tr');
       var article = this.collection.get(articleId);
       if (article) {
-        var pageModel = article.getPageModel();
-          pageModel.active = !pageModel.active;
-          pageModel.save();
-          $page.toggleClass('disabled');
+        if (_.contains(article.state, 2)) {
+          var pageModel = article.getPageModel();
+            pageModel.active = !pageModel.active;
+            pageModel.save();
+            $page.toggleClass('disabled');
+        }
       }
     },
 
@@ -247,7 +256,7 @@
       }
       else
           return list;
-  },
+    },
     search: function (event) {
         if (event.keyCode === 13) {
           var $target = $(event.currentTarget);
@@ -255,20 +264,28 @@
           var categoryId = this.categoryId;
           this.filteredBy = function (item) {
             var valeurColonne1 = item.title.trim().toLowerCase(); 
+            var valeurColonne2 = item.introduction.trim().toLowerCase(); 
+            var valeurColonne3 = item.author.trim().toLowerCase(); 
+            var regexColonne1 = new RegExp(searchTerm.trim().toLowerCase(), 'g');
+            var regexColonne2 = new RegExp(searchTerm.trim().toLowerCase(), 'g');
+            var regexColonne3 = new RegExp(searchTerm.trim().toLowerCase(), 'g');
 
+            var match = regexColonne1.test(valeurColonne1) || regexColonne2.test(valeurColonne2) || regexColonne3.test(valeurColonne3);
+
             if (!categoryId) {
-              var valeurColonne2 = item.categorie.trim().toLowerCase();
+              var valeurColonne4 = item.categorie.trim().toLowerCase();
                // Utiliser des expressions régulières pour trouver des correspondances partielles
-              var regexColonne1 = new RegExp(searchTerm.trim().toLowerCase(), 'g');
-              var regexColonne2 = new RegExp(searchTerm.trim().toLowerCase(), 'g');
-              return regexColonne1.test(valeurColonne1) || regexColonne2.test(valeurColonne2);
+              var regexColonne4 = new RegExp(searchTerm.trim().toLowerCase(), 'g');
+            
+              return regexColonne4.test(valeurColonne4) ||  match
             }
             else{
-              var filtreCat = item.category.some(function(cat) {
+              if (categoryId == 'uncategorized')
+              var filtreCat = (item.category.length == 0);
+              else filtreCat = item.category.some(function(cat) {
                 return cat === categoryId;
               });
-              var regexColonne1 = new RegExp(searchTerm.trim().toLowerCase(), 'g');
-              return regexColonne1.test(valeurColonne1) && filtreCat;
+              return match && filtreCat;
             }
         
            
