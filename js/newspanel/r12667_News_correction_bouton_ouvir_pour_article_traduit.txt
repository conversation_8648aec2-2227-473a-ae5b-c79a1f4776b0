Revision: r12667
Date: 2024-07-31 15:07:19 +0300 (lrb 31 Jol 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: correction bouton ouvir pour article traduit

## Files changed

## Full metadata
------------------------------------------------------------------------
r12667 | s<PERSON><PERSON><PERSON><PERSON><PERSON> | 2024-07-31 15:07:19 +0300 (lrb 31 Jol 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Utils/NewsUtils.js

News: correction bouton ouvir pour article traduit
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Utils/NewsUtils.js
===================================================================
--- src/js/JEditor/NewsPanel/Utils/NewsUtils.js	(révision 12666)
+++ src/js/JEditor/NewsPanel/Utils/NewsUtils.js	(révision 12667)
@@ -2,9 +2,10 @@
     'underscore',
     'backbone',
     'JEditor/Commons/Events',
+    "JEditor/Commons/Pages/Models/Page",
     "JEditor/PagePanel/Contents/Zones/Models/ZoneCollection"
   ],
-  function(_, Backbone, Events, ZoneCollection) {
+  function(_, Backbone, Events, Page, ZoneCollection) {
     var NewsUtils = function() {
 
     };
@@ -37,12 +38,21 @@
         var onError = function(model, resp, options) {
           this.trigger(Events.NewsUtilsEvents.CLONING_ERROR, model, resp, options);
         }
+       
         var onDone = function(clone) {
           this.stopListening(clone, Events.BackboneEvents.ERROR);
-          if (ArticleCollection) {
-            ArticleCollection.add(clone);
-            this.trigger(Events.NewsUtilsEvents.CLONING_DONE, clone);
-          }    
+          var pageModel = new Page();
+          pageModel.fetchPageById(clone.page).success(function(data) {
+              clone.pageModel = new Page(data);
+              clone.pageCollection.add(clone.pageModel);
+
+              if (ArticleCollection) {
+                ArticleCollection.add(clone);
+                this.trigger(Events.NewsUtilsEvents.CLONING_DONE, clone);
+              }   
+          }.bind(this)).error(function(error) {
+              console.log(error)
+          }); 
         };
 
         this.listenToOnce(clone, Events.BackboneEvents.SYNC, _.bind(onDone, this))
