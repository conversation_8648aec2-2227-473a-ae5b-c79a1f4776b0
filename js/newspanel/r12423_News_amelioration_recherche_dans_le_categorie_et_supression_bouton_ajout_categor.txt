Revision: r12423
Date: 2024-06-17 15:19:22 +0300 (lts 17 Jon 2024) 
Author: sraz<PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: amelioration recherche dans le categorie et supression bouton ajout categorie

## Files changed

## Full metadata
------------------------------------------------------------------------
r12423 | srazanandralisoa | 2024-06-17 15:19:22 +0300 (lts 17 Jon 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js

News: amelioration recherche dans le categorie et supression bouton ajout categorie
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 12422)
+++ src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 12423)
@@ -31,7 +31,7 @@
       'click  .icon-bin': 'onDeleteClick',
     },
     language: null,
-    category: null,
+    categoryId: null,
     list:null,
     fadeInEffect: 'fadeIn',
     fadeOutEffect: 'fadeOut',
@@ -44,6 +44,7 @@
       this.language = this.options.language.toJSON();
       if (this.options.category) {
         categoryId = this.options.category.id;
+        this.categoryId = categoryId;
         self = this;
         this.filteredBy = function (item) {
           return item.category.some(function(cat) {
@@ -72,7 +73,7 @@
           var params = _.extend({}, {content: list, searchValue : searchValue, current : this._current,currentlang : this.lang, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected, user: this.app.user}, this.addTemplateParams(this.collection, list));
           this.$el.html(this._template(params));
         }
-        else{var params = _.extend({}, {content: list, current : this._current,currentlang : this.lang, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected}, this.addTemplateParams(this.collection, list));
+        else{var params = _.extend({}, {content: list, current : this._current,currentlang : this.lang, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected , canAddCat : (this.categoryId)?false : true}, this.addTemplateParams(this.collection, list));
           this.$el.html(this._emptyTemplate(params));
         }
       }
@@ -92,7 +93,7 @@
           cid : list.id,
           image : (list.file)?list.file.fileUrl:'',
           title : list.title,
-          author : 'srazanandralisoa',
+          author : (list.content)? list.content.client : "",
           categorie : categories.title,
           category : list.category,
           id : list._id,              
@@ -206,14 +207,26 @@
         if (event.keyCode === 13) {
           var $target = $(event.currentTarget);
           var searchTerm = $target.val();
+          var categoryId = this.categoryId;
           this.filteredBy = function (item) {
             var valeurColonne1 = item.title.trim().toLowerCase(); 
-            var valeurColonne2 = item.categorie.trim().toLowerCase();
+
+            if (!categoryId) {
+              var valeurColonne2 = item.categorie.trim().toLowerCase();
+               // Utiliser des expressions régulières pour trouver des correspondances partielles
+              var regexColonne1 = new RegExp(searchTerm.trim().toLowerCase(), 'g');
+              var regexColonne2 = new RegExp(searchTerm.trim().toLowerCase(), 'g');
+              return regexColonne1.test(valeurColonne1) || regexColonne2.test(valeurColonne2);
+            }
+            else{
+              var filtreCat = item.category.some(function(cat) {
+                return cat === categoryId;
+              });
+              var regexColonne1 = new RegExp(searchTerm.trim().toLowerCase(), 'g');
+              return regexColonne1.test(valeurColonne1) && filtreCat;
+            }
         
-            // Utiliser des expressions régulières pour trouver des correspondances partielles
-            var regexColonne1 = new RegExp(searchTerm.trim().toLowerCase(), 'g');
-            var regexColonne2 = new RegExp(searchTerm.trim().toLowerCase(), 'g');
-            return regexColonne1.test(valeurColonne1) || regexColonne2.test(valeurColonne2);
+           
           };
           this.render(searchTerm);
         }
