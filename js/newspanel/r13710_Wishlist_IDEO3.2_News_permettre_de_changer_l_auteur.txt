Revision: r13710
Date: 2025-01-15 12:09:46 +0300 (lrb 15 Jan 2025) 
Author: frahajanirina 

## Commit message
Wishlist:IDEO3.2:News : permettre de changer l'auteur

## Files changed

## Full metadata
------------------------------------------------------------------------
r13710 | frahajanirina | 2025-01-15 12:09:46 +0300 (lrb 15 Jan 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Models/Article.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/configArticle.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/ConfigArticleView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/news_panel/main.less

Wishlist:IDEO3.2:News : permettre de changer l'auteur
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Models/Article.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Article.js	(révision 13709)
+++ src/js/JEditor/NewsPanel/Models/Article.js	(révision 13710)
@@ -30,7 +30,9 @@
                             page:null,
                             news:null,
                             content:null,
-                            numberOfTranslation:0
+                            numberOfTranslation:0,
+                            enseigne: null,
+                            userRole: null
                         },
                         initialize: function() {
                             this._super();
@@ -84,7 +86,8 @@
                                 id: this.id,
                                 category:this.category,
                                 title : this.title,
-                                introduction : this.introduction
+                                introduction : this.introduction,
+                                client: (this.content != null) ? this.content.client : null
                             };
                             return returnValue;
                         },
@@ -94,6 +97,7 @@
                             this.getCategoryModel();
                             this.set('title', this.lastState.title);
                             this.set('introduction', this.lastState.introduction);
+                            this.content.client = this.lastState.client;
                             this.trigger(Events.ArticleEvents.ARTICLE_CANCEL, this);
                         },
                         cancelParams: function() {
@@ -132,7 +136,7 @@
                         }
                     });
                    
-            Article.SetAttributes(['category', 'state', 'numberOfTranslation','title', 'lang','ressource','introduction', 'publicationDate','programmingDate', 'metaTitle', 'metaOpengraph', "page", "content","news"]);
+            Article.SetAttributes(['category', 'state', 'numberOfTranslation','title', 'lang','ressource','introduction', 'publicationDate','programmingDate', 'metaTitle', 'metaOpengraph', "page", "content","news", "enseigne", "userRole"]);
             Events.extend(
                 {
                     ArticleEvents : {
Index: src/js/JEditor/NewsPanel/Templates/configArticle.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/configArticle.html	(révision 13709)
+++ src/js/JEditor/NewsPanel/Templates/configArticle.html	(révision 13710)
@@ -20,5 +20,31 @@
     </label>
     <p><a href="//<%=url%>" target="_blank"><%=urltext%></a></p>
 </div>
+<% if(userRole == 'root'){%>
+    <br>
+    <div class="panel-content-content">
+        <label for="author">
+            <span class="label"><%=__('changeAuthor')%></span>
+        </label>
+    </div>
+    <% var _id=_.uniqueId('client') ;%>
+    <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="client" value="linkeo" <%=(client=='linkeo') ? "checked=checked" : ""%>/>
+    <label  class="block-label" for="<%= _id %>">
+        <div class="radio-wrapper">
+        <span class="icon  icon-radio-active"></span>
+        <span class="icon  icon-radio-inactive"></span>
+        </div>
+        <div class="block-label-radio">Linkeo</div>
+    </label>
+    <% var _id=_.uniqueId('client') ;%>
+    <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="client" value="<%=enseigne%>" <%=(client==enseigne) ? "checked=checked" : ""%>/>
+    <label  class="block-label" for="<%= _id %>">
+        <div class="radio-wrapper">
+        <span class="icon  icon-radio-active"></span>
+        <span class="icon  icon-radio-inactive"></span>
+        </div>
+        <div class="block-label-radio"><%=enseigne%></div>
+    </label>
+<% }%>
 
 
Index: src/js/JEditor/NewsPanel/Views/ConfigArticleView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ConfigArticleView.js	(révision 13709)
+++ src/js/JEditor/NewsPanel/Views/ConfigArticleView.js	(révision 13710)
@@ -23,7 +23,8 @@
         events: {
             'click .button.save': '_onSaveClick', 
             'click .button.cancel': '_onCancelClick', 
-            'click .panel-menu>a': '_onMenuItemClick'
+            'click .panel-menu>a': '_onMenuItemClick',
+            'click input[name=client]': '_onChangeRadio'
         },
         _onSaveClick: function(event) {
             if (this.save)
@@ -52,7 +53,10 @@
                 url: (page)? page.attributes.base_url : "",
                 urltext:(page)? page.attributes.page_php : "",
                 metaTitle: this.model.metaTitle,
-                metaDescription: this.model.metaDescription
+                metaDescription: this.model.metaDescription,
+                client: this.model.content.client,
+                enseigne: this.model.enseigne,
+                userRole: this.model.userRole
             }
             var content = this.contenttemplate(params)
             this.addContent(content);
@@ -109,6 +113,11 @@
          */
         cancel: function() {
            this.model.cancelParams();
+           this.model.cancel();
+        },
+        _onChangeRadio: function(event){
+            var client = event.target.value;
+            this.model.content.client = client;
         }
         
     });
Index: src/less/imports/news_panel/main.less
===================================================================
--- src/less/imports/news_panel/main.less	(révision 13709)
+++ src/less/imports/news_panel/main.less	(révision 13710)
@@ -202,9 +202,14 @@
             border: 3px solid #1a1a1a;
         }
     }
-	label .icon{
-		color: #5b5b5b;
-		font-size: 1em;
+	label {
+		.icon{
+			color: #5b5b5b;
+			font-size: 1em;
+		}
+		.icon-radio-active {
+			color: #fff;
+		}
 	}
 	position: absolute;
 	top: 20px;
Index: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 13709)
+++ src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 13710)
@@ -166,5 +166,6 @@
 	"published": "Publié",
 	"draft": "Brouillon",
 	"program": "Programmé",
-	"open": "Ouvrir"
+	"open": "Ouvrir",
+	"changeAuthor": "Changer l'auteur",
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 13709)
+++ src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 13710)
@@ -166,5 +166,6 @@
 	"published": "Publié",
 	"draft": "Brouillon",
 	"program": "Programmé",
-	"open": "Ouvrir"
+	"open": "Ouvrir",
+	"changeAuthor": "Changer l'auteur",
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/i18n.js	(révision 13709)
+++ src/js/JEditor/NewsPanel/nls/i18n.js	(révision 13710)
@@ -163,7 +163,8 @@
         "published": "Published",
         "draft": "Draft",
         "program": "Program",
-        "open": "Open"
+        "open": "Open",
+        "changeAuthor": "Change author",
     },
     "fr-fr": true,
     "fr-ca": true
