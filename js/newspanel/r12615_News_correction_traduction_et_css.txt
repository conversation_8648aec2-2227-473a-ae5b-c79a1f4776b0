Revision: r12615
Date: 2024-07-19 16:20:47 +0300 (zom 19 Jol 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: correction traduction et css

## Files changed

## Full metadata
------------------------------------------------------------------------
r12615 | srazana<PERSON>lisoa | 2024-07-19 16:20:47 +0300 (zom 19 Jol 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/configArticle.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/publishConfig.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/ConfigArticleView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/NewsEditorView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/PublishConfigView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/edit.less
   M /branches/ideo3_v2/integration/src/less/imports/news_panel/main.less

News: correction traduction et css
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Templates/configArticle.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/configArticle.html	(révision 12614)
+++ src/js/JEditor/NewsPanel/Templates/configArticle.html	(révision 12615)
@@ -18,7 +18,7 @@
         <span class="icon icon-params"></span>
         <span class="label"><%=__('urlArtLabel')%></span>
     </label>
-    <p><a href="/news/<%=url%>">/news/<%=url%>.php</a></p>
+    <p><a href="//<%=url%>" target="_blank"><%=urltext%></a></p>
 </div>
 
 
Index: src/js/JEditor/NewsPanel/Templates/publishConfig.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/publishConfig.html	(révision 12614)
+++ src/js/JEditor/NewsPanel/Templates/publishConfig.html	(révision 12615)
@@ -1,53 +1,58 @@
-<div id="meta-configuration" class="news-rigthpanel scrollbar-classic">
-    <header class="panel-head">
-        <span class="icon icon-rotation-right"></span>
-        <h1 class="panel-name"><%=__('publishtitre')%></h1>
-    </header>
-    <div class="panel-content active">
-        <div class="panel-content-intro">
-            <%=__('publishDesc')%>
-        </div>
-   
-    <div class="option-content ">
-        <article class="panel-option">
-            <header>
-                <h3 class="option-name"><%=__("programOption")%></h3>
-                <p class="panel-content-legend"><%=__("programOptionDesc")%></p>
-            </header>
-            <div class="option-content">
-                <div class="batch">
-                <!-- toogle -->
-                    <span class="switch batchActions">
-                        <span></span>
-                    </span>
-                    <span class="labelnon"><%=__("non")%></span>
-                    <span class="labeloui"><%=__("oui")%></span>
-                </div> 
-                <div class="btn-group-batch forProgram">
-                    <div class="block-datepicker">
-                        <div id="datepicker">
-                            <!-- datepicker -->
-                        </div>
-            
-                        <div><%=__("willPublish")%>&nbsp;<span class="dateprog"></span></div>
-                        <div>
-                            <button>
-                                <span class="icon icon-rotation-right"></span>
-                                <span><%=__("programPublish")%></span>
-                            </button>
-                        </div>
+<header class="panel-head">
+    <span class="icon icon-rotation-right"></span>
+    <span class="panel-name"><%=__('publishtitre')%></span> 
+</header>
+<div class="panel-content-intro">
+    <%=__('publishDesc')%>
+</div>
+<div class="panel-content"></div>
+    <article class="panel-option">
+        <header>
+            <h3 class="option-name">
+                <span class="icon icon-clic_rdv-icon"></span>
+                <span class="label" ><%=__('programOption')%></span>
+            </h3>
+            <p class="panel-content-legend"><%=__("programOptionDesc")%></p>
+        </header>
+        <div class="option-content">
+            <div class="batch">
+            <!-- toogle -->
+                <span class="switch batchActions">
+                    <span></span>
+                </span>
+                <span class="labelnon"><%=__("non")%></span>
+                <span class="labeloui"><%=__("oui")%></span>
+            </div> 
+            <div class="btn-group-batch forProgram">
+                <div class="block-datepicker">
+                    <div id="datepicker">
+                        <!-- datepicker -->
                     </div>
+        
+                    <div class="forDes"><%=__("willPublish")%>&nbsp;<span class="dateprog"></span></div>
                 </div>
-                <div class="forPub">
-                    <div><%=__("publishImmediate")%></div>
-                    <div>
-                        <button>
-                            <span class="icon icon-rotation-right"></span>
-                            <span><%=__("publishArticle")%></span>
-                        </button>
-                    </div>
-                </div>
             </div>
-        </article>
+            <div class="forPub">
+                <div class="forDes"><%=__("publishImmediate")%></div>
+            </div>
+        </div>
+    </article>
+</div>
+<footer class="foot">
+    <div class="button-group save-or-cancel" >
+        <button class="forPub">
+            <span class="icon icon-rotation-right"></span>
+            <span><%=__("publishArticle")%></span>
+        </button>
+        <button class="forProgram">
+            <span class="icon icon-rotation-right"></span>
+            <span><%=__("programPublish")%></span>
+        </button>
+        <a class="button cancel" data-action="cancel">
+            <span class="wrapper">
+                <span class="icon"></span>
+                <span class="text"><%= __("cancel")%></span>
+            </span>
+        </a>
     </div>
-</div>
\ No newline at end of file
+</footer>
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Views/ConfigArticleView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ConfigArticleView.js	(révision 12614)
+++ src/js/JEditor/NewsPanel/Views/ConfigArticleView.js	(révision 12615)
@@ -47,7 +47,13 @@
             this.listenTo(this.rightPanelView, 'overlayClick', this.cancel);
             this.setTitle(translate('config'));
             this.setDescription(translate('configDescArt'));
-            var params = this.model.toJSON();
+            var page = this.model.getPageModel() ;
+            var params = {
+                url: (page)? page.attributes.base_url : "",
+                urltext:(page)? page.attributes.page_php : "",
+                metaTitle: this.model.metaTitle,
+                metaDescription: this.model.metaDescription
+            }
             var content = this.contenttemplate(params)
             this.addContent(content);
             this.rightPanelView.addContent(this);
Index: src/js/JEditor/NewsPanel/Views/PublishConfigView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/PublishConfigView.js	(révision 12614)
+++ src/js/JEditor/NewsPanel/Views/PublishConfigView.js	(révision 12615)
@@ -7,6 +7,7 @@
     "JEditor/App/Views/RightPanelView",
     "i18n!JEditor/NewsPanel/nls/i18n",
     "JEditor/Commons/Utils",
+    "jqueryui/datepicker"
 ], function ($, _, Events, View, template, RightPanelView, translate,  Utils) {
     
     /**
@@ -16,7 +17,7 @@
     var PublishConfigView = View.extend({
 
         tagName: 'div',
-        className: 'right-panel-nav',
+        className: 'news-right-panel publishview',
         _transitionDelay: 250,
         rendered: false,
         _currentCid: null,
@@ -30,6 +31,7 @@
         events: {
             'click .switch': '_onClick', 
             'click button': '_onSaveClick', 
+            'click .cancel': '_onCancelClick',
             'click .panel-menu>a': '_onMenuItemClick'
         },
         _onClick: function(event) {
@@ -50,8 +52,25 @@
             this.rightPanelView.addContent(this);
             this.currentLang = this.options.currentLang;
             this._programmed = (this.model.programmingDate) ? true:false;
+            $(window).on("scroll.NewsTabbedView."+this.cid, _.bind(this.onScroll, this));
             return this;
         },
+        onScroll:function(){
+            var that=this;
+            if(this.scrollTimeout!==null){
+                window.clearTimeout(this.scrollTimeout);
+            }
+            this.scrollTimeout = window.setTimeout(function () {
+                that.updateHeight();
+            },200);
+        },
+        updateHeight: function() {
+            var win = $(window);
+            var height = win.height() - (this.$el.offset().top - win.scrollTop());
+            if (this.currentPane !== null)
+                this.$el.height(height);
+            this.updateScrollables();
+        },
          /**
          * affiche cette reglage
          */
@@ -84,10 +103,12 @@
             this.$el.html(this.template(params));
             this.$('#datepicker').datepicker({
                 dateFormat: 'yy-mm-dd', // Format de la date
-                onSelect: this.updateModel.bind(this) 
+                onSelect: this.updateModel.bind(this),
+                language: 'fr'
             });
             this.switchprogram = this.$(".switch");
             this.datePickerRender();
+            this.updateHeight();
             this.delegateEvents();
             return this;
         },
@@ -133,6 +154,10 @@
                 this.save();
             this.hide();
         },
+        _onCancelClick: function(event) {
+            this.cancel()
+            this.hide();
+        },
         save: function() {
             this.model.save();
         },
Index: src/less/imports/edit.less
===================================================================
--- src/less/imports/edit.less	(révision 12614)
+++ src/less/imports/edit.less	(révision 12615)
@@ -33,7 +33,7 @@
     &.affix-top{top:0; bottom:0;}
 
 }
-#news-editor #content-editor[data-child-count="0"],
+#news-editor #content-editor .content-zone[data-child-count="0"],
 #page-edit #content-editor[data-child-count="0"]{
     &>.sensor{
         height: 100%;
Index: src/less/imports/news_panel/main.less
===================================================================
--- src/less/imports/news_panel/main.less	(révision 12614)
+++ src/less/imports/news_panel/main.less	(révision 12615)
@@ -306,6 +306,7 @@
 }
 .content-zone{
 	margin-top: 9rem;
+	position: relative;
 }
 .sideBar .addCategory span,
 .sideBar .addArticle span {
@@ -1133,47 +1134,40 @@
 	}
 }
 
-#meta-configuration {
+.publishview {
 	span.icon {
 		color: #515151;
 		margin-right: 10px;
 	}
-    &>.scroll-container {
-        height: 100%;
-
-        & .panel-content {
-            margin: auto;
-        }
-    }
-
-    header.panel-head {
-        h1.panel-name {
-            left: 0;
-        }
-
-        border-bottom:1px solid #515151;
-        padding-bottom:20px;
-    }
-
-    h2.option-title {
-        font-size: 1.5em;
-        text-align: center;
-        border: none;
-        font-weight: normal;
-    }
+	.option-name span{
+		display: inline;
+	}
+	p {
+		color: gray;
+		font-size: .8em;
+	}
+	h3.option-name{
+		font-weight: 400 !important;
+	}
+	.forDes{
+		margin-top: 10px;
+		font-weight: 400;
+	}
 	#datepicker{
 		margin: 10px auto;
+		padding: 19px;
+		background-color: #111;
 	.ui-datepicker-inline{
 		margin: auto;
 		width : 100%;
 		border: none;
 		.ui-datepicker-header.ui-widget-header {
-			background-color: #1a1a1a;
+			background-color: #111;
 			color: #fff;
 		}
 		.ui-datepicker-prev{
 			color: #fff; 
-			background-color: #333; 
+			background-color: #111; 
 			.ui-icon.ui-icon-circle-triangle-w::before{
 				border-right: 7px solid #999;
 
@@ -1182,7 +1176,7 @@
 		}
 		.ui-datepicker-next {
 			color: #fff; 
-			background-color: #333; 
+			background-color: #111; 
 			.ui-icon.ui-icon-circle-triangle-e::before{
 				border-left: 7px solid #999;;
 			}
@@ -1189,7 +1183,7 @@
 		}
 				
 		.ui-datepicker-calendar {
-			 background-color: #1a1a1a; 
+			 background-color: #111; 
 		}
 		}	
 	}
@@ -1198,32 +1192,21 @@
 		color: #fff;
 		border-radius: 3px;
 		width: 100%;
-		padding: 14px;
-		border: 1px solid #newsColorLight;
+		padding: 12px;
+		border: 1px solid @newsColorLight;
 		font-size: 0.9em;
 		font-weight: 500;
 		.icon{
 			color: #f4f4f4;
+			display: inline;
 		}
 	}
 
-    .panel-content {
-        margin: auto;
+	.save-or-cancel > * {
+		width: 100%;
+		margin: 2px auto;
+	}
 
-        .panel-content-intro {
-            color: #727272;
-            font-size: 0.9em;
-        }
-
-        .option-content{
-            & div.items {
-                .clearfix();
-            }
-
-            margin-bottom:100px;
-        }
-   }
-
 }
 
 
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12614)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12615)
@@ -131,7 +131,7 @@
                                         pagePanel : this
                                     });
                                     this.childViews.newsEditorView = new NewsEditorView({
-                                        title : 'tout les articles',
+                                        title : 'tous les articles',
                                         usepreview : false,
                                         languages : this.languages,
                                         currentLang : this.currentLang,
@@ -308,7 +308,7 @@
                                             }
                                             else {
                                             this.childViews.newsEditorView = new NewsEditorView({
-                                                title : 'tout les articles',
+                                                title : 'tous les articles',
                                                 languages : this.languages,
                                                 currentLang : this.currentLang,
                                                 categorieCollection :  this.categories
@@ -363,7 +363,7 @@
                                             }
                                             else {
                                                 this.childViews.newsEditorView = new NewsEditorView({
-                                                    title : 'tout les articles',
+                                                    title : 'tous les articles',
                                                     languages : this.languages,
                                                     currentLang : this.currentLang,
                                                     categorieCollection :  this.categories
Index: src/js/JEditor/NewsPanel/Views/NewsEditorView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 12614)
+++ src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 12615)
@@ -82,7 +82,7 @@
             this.listenTo(this.addCategorieView, Events.CategorieAddEvents.CATEGORY_ADD, _.bind(function(categorie) {
                 self.newsPanel.currentCategorie = categorie;
             }));
-            this.options.title = 'Categorie';
+            this.options.title = 'Catégorie';
             this.options.usepreview = false;
             this.render();
             this.categoryView.html(this.addCategorieView.render().el);
Index: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12614)
+++ src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12615)
@@ -14,7 +14,7 @@
 	"descriCategorie":"Description de la catégorie",
 	"cancel":"Annuler",
 	"saveEdit" :"Enregistrer",
-	"saveAdd" : "Créer la catégorie",
+	"saveAdd" : "Créer une catégorie",
 	"preview": "Aper\u00e7u",
 	"quitWithoutSavingArt": "Votre article n'a pas \u00e9t\u00e9 enregistrer, voulez-vous les enregistrer ?",
 	"quitWithoutSavingCat": "Votre cat\u00e9gorie n'a pas \u00e9t\u00e9 enregistrer, voulez-vous les enregistrer ?",
@@ -100,9 +100,9 @@
 	 "errorCategoryTraduction" : "Pour traduire cet article, veuillez vous assurer que la catégorie existe bien dans le langue <strong><% lang %></strong>.",
 	 "errorChangeCategoryTraduction" : "Cet article à des traductions associées en <strong><% lang %></strong>. Pour changer la catégorie de l'article, veuillez vous assurer que la nouvelle catégorie existe bien dans les langues <strong><% lang %></strong>.",
 	 "alreadyTraduice" : "Cet article existe déjà dans le langue <strong><% lang %></strong>.",
-	 "translationSuccessfull": "Traduction de l'article avec success",
+	 "translationSuccessfull": "Traduction de l'article avec succès",
 	 "CategoryRequired":"Veuillez choisr une catégorie avant de sauvegarder",
-	 "RessourceRequired":"Veuillez choisr un image à la Une avant de sauvegarder", "errorChangeCategoryTraduction" : "Cet article à des traductions associées en <strong><% lang %></strong>. Pour changer la catégorie de l'article, veuillez vous assurer que la nouvelle catégorie existe bien dans les langues <strong><% lang %></strong>.",
+	 "RessourceRequired":"Veuillez choisr un image à la une avant de sauvegarder", "errorChangeCategoryTraduction" : "Cet article à des traductions associées en <strong><% lang %></strong>. Pour changer la catégorie de l'article, veuillez vous assurer que la nouvelle catégorie existe bien dans les langues <strong><% lang %></strong>.",
 	 "configDescArt" : "Ajustez les paramètres des articles",
 	 "urlArtLabel" : "URL de l'article",
 
@@ -115,7 +115,7 @@
 	"non" : "Non",
 	"willPublish" : "Votre article sera publié le",
 	"programPublish" : "Programmer la publication",
-	"publishImmediate" : "Votre articke sera publié immédiatement",
+	"publishImmediate" : "Votre article sera publié immédiatement",
 	"publishArticle" : "Publier l'article",
 	"dateFormat":"Y-m-d",
 	"publishDone":"Article publié",
Index: src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 12614)
+++ src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 12615)
@@ -14,7 +14,7 @@
 	"descriCategorie":"Description de la catégorie",
 	"cancel":"Annuler",
 	"saveEdit" :"Enregistrer",
-	"saveAdd" : "Créer la catégorie",
+	"saveAdd" : "Créer une catégorie",
 	"preview": "Aper\u00e7u",
 	"quitWithoutSavingArt": "Votre article n'a pas \u00e9t\u00e9 enregistrer, voulez-vous les enregistrer ?",
 	"quitWithoutSavingCat": "Votre cat\u00e9gorie n'a pas \u00e9t\u00e9 enregistrer, voulez-vous les enregistrer ?",
@@ -100,9 +100,9 @@
 	 "errorCategoryTraduction" : "Pour traduire cet article, veuillez vous assurer que la catégorie existe bien dans le langue <strong><% lang %></strong>.",
 	 "errorChangeCategoryTraduction" : "Cet article à des traductions associées en <strong><% lang %></strong>. Pour changer la catégorie de l'article, veuillez vous assurer que la nouvelle catégorie existe bien dans les langues <strong><% lang %></strong>.",
 	 "alreadyTraduice" : "Cet article existe déjà dans le langue <strong><% lang %></strong>.",
-	 "translationSuccessfull": "Traduction de l'article avec success",
+	 "translationSuccessfull": "Traduction de l'article avec succès",
 	 "CategoryRequired":"Veuillez choisr une catégorie avant de sauvegarder",
-	 "RessourceRequired":"Veuillez choisr un image à la Une avant de sauvegarder", "errorChangeCategoryTraduction" : "Cet article à des traductions associées en <strong><% lang %></strong>. Pour changer la catégorie de l'article, veuillez vous assurer que la nouvelle catégorie existe bien dans les langues <strong><% lang %></strong>.",
+	 "RessourceRequired":"Veuillez choisr un image à la une avant de sauvegarder", "errorChangeCategoryTraduction" : "Cet article à des traductions associées en <strong><% lang %></strong>. Pour changer la catégorie de l'article, veuillez vous assurer que la nouvelle catégorie existe bien dans les langues <strong><% lang %></strong>.",
 	 "configDescArt" : "Ajustez les paramètres des articles",
 	 "urlArtLabel" : "URL de l'article",
 
@@ -115,7 +115,7 @@
 	 "non" : "Non",
 	 "willPublish" : "Votre article sera publié le",
 	 "programPublish" : "Programmer la publication",
-	 "publishImmediate" : "Votre articke sera publié immédiatement",
+	 "publishImmediate" : "Votre article sera publié immédiatement",
 	 "publishArticle" : "Publier l'article",
 	 "dateFormat":"Y-m-d",
 	 "publishDone":"Article publié",
Index: src/js/JEditor/NewsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12614)
+++ src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12615)
@@ -15,7 +15,7 @@
         "descriCategorie":"Description de la catégorie",
         "cancel":"Annuler",
         "saveEdit" :"Enregistrer",
-        "saveAdd" : "Créer la catégorie",
+        "saveAdd" : "Créer une catégorie",
         "preview": "Aper\u00e7u",
         "quitWithoutSavingArt": "Votre article n'a pas \u00e9t\u00e9 enregistrer, voulez-vous les enregistrer ?",
         "quitWithoutSavingCat": "Votre cat\u00e9gorie n'a pas \u00e9t\u00e9 enregistrer, voulez-vous les enregistrer ?",
@@ -100,9 +100,9 @@
         "errorCategoryTraduction" : "Pour traduire cet article, veuillez vous assurer que la catégorie existe bien dans le langue <strong><% lang %></strong>.",
         "errorChangeCategoryTraduction" : "Cet article à des traductions associées en <strong><% lang %></strong>. Pour changer la catégorie de l'article, veuillez vous assurer que la nouvelle catégorie existe bien dans les langues <strong><% lang %></strong>.",
         "alreadyTraduice" : "Cet article existe déjà dans le langue <strong><% lang %></strong>.",
-        "translationSuccessfull": "Traduction de l'article avec success",
+        "translationSuccessfull": "Traduction de l'article avec succès",
         "CategoryRequired":"Veuillez choisr une catégorie avant de sauvegarder",
-        "RessourceRequired":"Veuillez choisr un image à la Une avant de sauvegarder",
+        "RessourceRequired":"Veuillez choisr un image à la une avant de sauvegarder",
         "configDescArt" : "Ajustez les paramètres des articles",
         "urlArtLabel" : "URL de l'article",
 
@@ -115,7 +115,7 @@
         "non" : "Non",
         "willPublish" : "Votre article sera publié le",
         "programPublish" : "Programmer la publication",
-        "publishImmediate" : "Votre articke sera publié immédiatement",
+        "publishImmediate" : "Votre article sera publié immédiatement",
         "publishArticle" : "Publier l'article",
         "dateFormat":"Y-m-d",
         "publishDone":"Article publié",
