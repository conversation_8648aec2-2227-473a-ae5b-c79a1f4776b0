Revision: r14322
Date: 2025-06-05 07:43:26 +0300 (lkm 05 Jon 2025) 
Author: frahajanirina 

## Commit message
News: fixe titre article, categorie & meta description de categorie si l'intro est vide

## Files changed

## Full metadata
------------------------------------------------------------------------
r14322 | frahajanirina | 2025-06-05 07:43:26 +0300 (lkm 05 Jon 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/configArticle.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/ConfigArticleView.js

News: fixe titre article, categorie & meta description de categorie si l'intro est vide
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Templates/configArticle.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/configArticle.html	(révision 14321)
+++ src/js/JEditor/NewsPanel/Templates/configArticle.html	(révision 14322)
@@ -4,7 +4,7 @@
         <span class="icon icon-html"></span>
         <span class="label"><%=__('metaTitleLabel')%></span>
     </label>
-    <input type="text" name="metaTitle" value="<%=metaTitle%>"  id="meta-title" disabled>
+    <input type="text" name="metaTitle" value="<%=title%> - <%=enseigne%>"  id="meta-title" disabled>
 </div>
 <div class="panel-content-content">
     <label for="meta-description">
Index: src/js/JEditor/NewsPanel/Views/ConfigArticleView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ConfigArticleView.js	(révision 14321)
+++ src/js/JEditor/NewsPanel/Views/ConfigArticleView.js	(révision 14322)
@@ -49,14 +49,17 @@
             this.setTitle(translate('config'));
             this.setDescription(translate('configDescArt'));
             var page = this.model.getPageModel() ;
+            var metaDescription = this.model.metaDescription;
+            var enseigne = this.model.enseigne;
             var params = {
                 url: (page)? page.attributes.base_url : "",
                 urltext:(page)? page.attributes.page_php : "",
                 metaTitle: this.model.metaTitle,
-                metaDescription: this.model.metaDescription,
+                metaDescription: metaDescription.length > 100 ? metaDescription.substring(0, 100) + '... - ' + enseigne : metaDescription + ' - ' + enseigne,
                 client: this.model.content.client,
-                enseigne: this.model.enseigne,
-                isNotClient: (__IDEO_USER_NAME__ !== __IDEO_SITE_CODE_BOUTON__) ? true : false 
+                enseigne: enseigne,
+                isNotClient: (__IDEO_USER_NAME__ !== __IDEO_SITE_CODE_BOUTON__) ? true : false,
+                title: this.model.title 
             }
             var content = this.contenttemplate(params)
             this.addContent(content);
