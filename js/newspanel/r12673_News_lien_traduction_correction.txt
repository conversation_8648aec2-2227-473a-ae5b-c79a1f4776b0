Revision: r12673
Date: 2024-08-01 10:26:14 +0300 (lkm 01 Aog 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: lien traduction correction

## Files changed

## Full metadata
------------------------------------------------------------------------
r12673 | sraz<PERSON><PERSON><PERSON>oa | 2024-08-01 10:26:14 +0300 (lkm 01 Aog 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/TraduiceButton.js

News: lien traduction correction
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12672)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12673)
@@ -573,6 +573,14 @@
                                         this.currentCategorie = null;
                                         this.currentArticle = null;
                                         this.childViews.categorieList.lang = lang.id;
+                                        //changement reglage par rapport au lang
+                                        if (this.childViews.globalConfigView ) {
+                                            this.childViews.globalConfigView.remove();
+                                            this.childViews.globalConfigView = new GlobalConfigView({
+                                                language : this.currentLang,
+                                                newsPanel : this
+                                            });
+                                        }
                                         this.listenTo(this.childViews.categorieList, Events.ViewEvents.HIDE, function() {
                                             this._renderCategorieList();
                                             this.childViews.categorieList.render();
@@ -582,6 +590,20 @@
                                         this._onCategorieChange();
                                     }
                                 },
+                                changeArticleLang: function(article,lang, panel) {
+                                    if (this.loaded) {
+                                        this.currentLang = this.languages.get(lang);
+                                        this.childViews.langDropDown.current = this.currentLang;
+                                        this.currentArticle = article;
+                                        this.childViews.categorieList.lang = lang;
+                                        this.listenTo(this.childViews.categorieList, Events.ViewEvents.HIDE, function() {
+                                            this._renderCategorieList();
+                                            this.childViews.categorieList.render();
+                                            this.childViews.categorieList.show();
+                                        });
+                                        this.childViews.categorieList.hide();
+                                    }
+                                },
                                 _onArticleSelect : function(view, article) {
                                     if (this.currentCategorie && this.currentCategorie.hasUnsavedChanges()) {
                                         this.confirmUnsaved({
Index: src/js/JEditor/NewsPanel/Views/TraduiceButton.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/TraduiceButton.js	(révision 12672)
+++ src/js/JEditor/NewsPanel/Views/TraduiceButton.js	(révision 12673)
@@ -83,7 +83,7 @@
                             var className = $target.attr('class');
                             if (className == 'disabled') {
                                 var currentArticles = this.articles.findWhere({news: this.model.news , lang : params}); // retourne un model
-                                this.app.currentPanel.currentArticle = currentArticles;
+                                this.app.currentPanel.changeArticleLang(currentArticles,params);
                                return false;
                             }
                             else if (this[action] && this[action].call) {
