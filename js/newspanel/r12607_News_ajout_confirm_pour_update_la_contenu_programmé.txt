Revision: r12607
Date: 2024-07-15 16:53:09 +0300 (lts 15 Jol 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: ajout confirm pour update la contenu programmé

## Files changed

## Full metadata
------------------------------------------------------------------------
r12607 | sraz<PERSON><PERSON><PERSON>oa | 2024-07-15 16:53:09 +0300 (lts 15 Jol 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/AddArticleView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/ArticleEditorView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/ArticleTitleField.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/PublishConfigView.js

News: ajout confirm pour update la contenu programmé
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Views/AddArticleView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/AddArticleView.js	(révision 12606)
+++ src/js/JEditor/NewsPanel/Views/AddArticleView.js	(révision 12607)
@@ -210,8 +210,6 @@
         updateArticle:function(e) {
             e.preventDefault(); 
             e.stopImmediatePropagation();
-            this.listenToOnce(this.model, Events.BackboneEvents.SYNC, this.onSave);
-            this.listenToOnce(this.model, Events.BackboneEvents.ERROR, this.onError);
             if (this._checkInput()) {
                 this.model.lang[this.lang.id] = this.currentArticleLang;
                 this.model.save();
@@ -272,9 +270,9 @@
         addArticle: function(e) {
             e.preventDefault(); 
             e.stopImmediatePropagation();
-            this.listenToOnce(this.model, Events.BackboneEvents.SYNC, this.addPageAndSave);
-            this.listenToOnce(this.model, Events.BackboneEvents.ERROR, this.onError);
             if (this._checkInput()) {
+                this.listenToOnce(this.model, Events.BackboneEvents.SYNC, this.addPageAndSave);
+                this.listenToOnce(this.model, Events.BackboneEvents.ERROR, this.onError);
                 this.model.save();
                 this.collection.add(this.model);
                 this.collection.trigger('change');
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12606)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12607)
@@ -720,6 +720,7 @@
                                             onNo : _.bind(function() {
                                                 this.currentArticle.content.content.cancel();
                                                 this.currentArticle.cancel();
+                                               // this.childViews.newsEditorView.articleEditorView.se();
                                                 this.showPublishView();
                                             }, this),
                                             options : {
@@ -734,7 +735,6 @@
                                     return false
                                 },
                                 showPublishView : function() {
-
                                     function onClose() {
                                         this.rightPanelView.removeContent(this.childViews.publishConfigView);
                                         this.stopListening(this.childViews.publishConfigView);
@@ -752,9 +752,6 @@
                                         this.rightPanelView.showContent(this.childViews.publishConfigView);
                                         this.listenToOnce(this.childViews.publishConfigView, Events.PublishConfigViewEvents.SAVE, _.bind(onClose, this))
                                             .listenToOnce(this.childViews.publishConfigView, Events.PublishConfigViewEvents.CANCEL, _.bind(onClose, this));
-                                        this.listenToOnce(this.childViews.publishConfigView.model, Events.BackboneEvents.SYNC, this.publishDone)
-                                            .listenToOnce(this.childViews.publishConfigView.model, Events.BackboneEvents.ERROR, this.publishError)
-
                                       
                                     } catch (e) {
                                         console.error(e);
@@ -772,10 +769,23 @@
                                     this.rightPanelView.hideContent(this.childViews.publishConfigView);
                                     this.rightPanelView.hidePanel();
                                 },
-                                publishDone :function (e){
-                                    this.stopListening(this.childViews.publishConfigView.model, Events.BackboneEvents.ERROR, this.publishError);
-                                    if (e.programmingDate || e.publicationDate) {
-                                        var type = (this.childViews.publishConfigView.model.programmingDate)? 'programDone':'publishDone';
+                                showModalProgram :function (){
+                                    var article = this.currentArticle;
+                                    article.set('publicationDate',null);
+                                    this.confirm({
+                                        message: translate('majpublication'),
+                                        title: translate("majpublicationDesc"),
+                                        type: 'update',
+                                        onOk: _.bind(function() {
+                                             article.save()
+                                          }, this),
+                                        options: {dialogClass: 'delete no-close'}
+                                    });
+                                },
+                                articleSyncSuccess :function (e){
+                                    this.stopListening(this.currentArticle, Events.BackboneEvents.ERROR, this.articleSyncError);
+                                    if (e.content.state > 0 ) {
+                                        var type = (e.content.state === 1)? 'programDone':'publishDone';
                                         $.toast({
                                             text: translate(type), 
                                             icon: 'icon-check-circle', 
@@ -789,7 +799,7 @@
                                             loader:false,
                                             stack :1
                                         });
-                                    } else {
+                                    }else {
                                         $.toast({
                                             text: translate("saveSuccesful"), 
                                             icon: 'icon-check-circle', 
@@ -803,13 +813,12 @@
                                             loader:false
                                         });
                                     }
-                                    
-                                    
                                 },
-                                publishError :function (){
-                                    this.stopListening(this.childViews.publishConfigView.model, Events.BackboneEvents.SYNC, this.publishDone);
+                                articleSyncError :function (e){
+                                    this.stopListening(this.currentArticle, Events.BackboneEvents.SYNC, this.articleSyncSuccess);
+                                    var type = (e.programmingDate || e.publicationDate)? 'publishError':'saveError'
                                     $.toast({
-                                        text: translate("publishError"), 
+                                        text: translate(type), 
                                         icon: 'icon-check-circle', 
                                         type:'error',
                                         appendTo:'#news-view .main',
@@ -879,7 +888,15 @@
                                         },
                                         set : function(currentArticle) {
                                             if (currentArticle !== this._currentArticle) {
+                                              if(this._currentArticle)
+                                                this.stopListening( this._currentArticle, Events.BackboneEvents.SYNC, this.articleSyncSuccess)
+                                                    .stopListening( this._currentArticle, Events.BackboneEvents.ERROR, this.articleSyncError)
+                                                    .stopListening(this._currentArticle, Events.ArticleEvents.ARTICLE_UP, this.showModalProgram);
                                                 this._currentArticle = currentArticle;
+                                              if(this._currentArticle)
+                                                this.listenTo( this._currentArticle, Events.BackboneEvents.SYNC, this.articleSyncSuccess)
+                                                    .listenTo( this._currentArticle, Events.BackboneEvents.ERROR, this.articleSyncError)
+                                                    .listenTo(this._currentArticle, Events.ArticleEvents.ARTICLE_UP, this.showModalProgram);
                                             }
                                             this.trigger(Events.NewsPanelEvents.ARTICLE_CHANGE, this, currentArticle, undefined);
                                         }
Index: src/js/JEditor/NewsPanel/Views/ArticleEditorView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticleEditorView.js	(révision 12606)
+++ src/js/JEditor/NewsPanel/Views/ArticleEditorView.js	(révision 12607)
@@ -220,11 +220,7 @@
                     this.render();
                     this.setLoading(false);
                 }, this);
-                if (zone._hasChanged) {
-                    this.listenToOnce(zone, Events.BackboneEvents.SYNC, callback);
-                    zone.fetch();
-                } else
-                    callback();
+                callback();
                 return this;
             }
         }
Index: src/js/JEditor/NewsPanel/Views/ArticleTitleField.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticleTitleField.js	(révision 12606)
+++ src/js/JEditor/NewsPanel/Views/ArticleTitleField.js	(révision 12607)
@@ -23,7 +23,7 @@
         var openArticle = (page)?'<a href="//'+ page.attributes.base_url +'" class="btn btn-primary btn-lg btn-block" target="_blank">Ouvrir' : "";
        
         var state = this.model.state;
-        var datep = (state == 1) ? moment(this.model.programmingDate.date).format("DD/MM/YY"): '';
+        var datep = (state == 1) ? moment(this.model.programmingDate).format("DD/MM/YY"): '';
         this.$el.empty();
         this.$el.html(this._template({
             title: (this.model.id)? this.model.title : 'Article',
Index: src/js/JEditor/NewsPanel/Views/PublishConfigView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/PublishConfigView.js	(révision 12606)
+++ src/js/JEditor/NewsPanel/Views/PublishConfigView.js	(révision 12607)
@@ -83,7 +83,7 @@
             var params = this.model
             this.$el.html(this.template(params));
             this.$('#datepicker').datepicker({
-                dateFormat: 'dd-mm-yy', // Format de la date
+                dateFormat: 'yy-mm-dd', // Format de la date
                 onSelect: this.updateModel.bind(this) 
             });
             this.switchprogram = this.$(".switch");
@@ -98,10 +98,9 @@
         updateAffichage : function (){
             var d = new Date();
             if (this.model.programmingDate && this.model.programmingDate != '' && !(this.model.programmingDate.date)) {
-                var parts = this.model.programmingDate.split('-');
-                d = new Date(parts[2], parts[1] - 1, parts[0]);
+                d = new Date(this.model.programmingDate);
             } else {
-                var datestring =d.getDate() + '-' + (d.getMonth() + 1) + '-' + d.getFullYear();
+                var datestring =d.getFullYear()+ '-' + (d.getMonth() + 1) + '-' + d.getDate();
                 this.model.programmingDate = datestring;
             }
             
