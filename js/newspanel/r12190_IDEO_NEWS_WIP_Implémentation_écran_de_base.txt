Revision: r12190
Date: 2024-03-27 18:16:04 +0300 (lrb 27 Mar 2024) 
Author: jn.harison 

## Commit message
IDEO NEWS: WIP: Implémentation écran de base

## Files changed

## Full metadata
------------------------------------------------------------------------
r12190 | jn.harison | 2024-03-27 18:16:04 +0300 (lrb 27 Mar 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/Gruntfile.js
   M /branches/ideo3_v2/integration_news/assets/ACLs/admin.json
   M /branches/ideo3_v2/integration_news/assets/ACLs/lpadmin.json
   M /branches/ideo3_v2/integration_news/assets/ACLs/root.json
   M /branches/ideo3_v2/integration_news/assets/ACLs/superadmin.json
   M /branches/ideo3_v2/integration_news/src/js/JEditor/App/Router.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/App/Router_save.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/App/Templates/application.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/App/routes/main.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/App/routes/news.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/App/routes_build.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/App/routes_define.json
   M /branches/ideo3_v2/integration_news/src/js/JEditor/DashBoard/Templates/DashBoardTpl.html
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/News.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/NewsPanel.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/NewsList.html
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/NewsPanel.html
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/NewsListView.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/en-au
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/en-au/i18n.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/en-us
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/en-us/i18n.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/fr-ca
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/fr-fr
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/i18n.js
   A /branches/ideo3_v2/integration_news/src/less/imports/news_panel
   A /branches/ideo3_v2/integration_news/src/less/imports/news_panel/import
   A /branches/ideo3_v2/integration_news/src/less/imports/news_panel/import/vars.less
   A /branches/ideo3_v2/integration_news/src/less/imports/news_panel/main.less
   M /branches/ideo3_v2/integration_news/src/less/imports/variables.less
   M /branches/ideo3_v2/integration_news/src/less/main.less

IDEO NEWS: WIP: Implémentation écran de base
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Models/News.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/News.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Models/News.js	(révision 12190)
@@ -0,0 +1,19 @@
+define([
+    "JEditor/Commons/Ancestors/Models/Model",
+    "JEditor/Commons/Events",
+    "i18n!../nls/i18n"
+], function (Model, Events, translate) {
+    var Feedget = Model.extend({
+        defaults: {
+        },
+        initialize: function () {
+            console.log("init");
+            this._super();
+        },
+        validate: function (attributes, options) {
+            
+        }
+    });
+    
+    return Feedget;
+});
\ No newline at end of file

Property changes on: src/js/JEditor/NewsPanel/Models/News.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/NewsPanel/Templates/NewsList.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/NewsList.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Templates/NewsList.html	(révision 12190)
@@ -0,0 +1,23 @@
+
+	<section class="main-content">
+
+		<div class="main-content__wrapper">
+
+			<div class="news-container-header">
+
+				<div class="form-title">
+					<p>
+						<span class="icon-mail"></span>
+						Tous les articles
+						<span class="icon-refresh" data-click-action="reload"></span>
+					</p>
+				</div>
+
+				
+
+			</div>
+
+			
+		</div>
+
+	</section>

Property changes on: src/js/JEditor/NewsPanel/Templates/NewsList.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/NewsPanel/nls/en-au/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/en-au/i18n.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/nls/en-au/i18n.js	(révision 12190)
@@ -0,0 +1,2 @@
+define({
+});

Property changes on: src/js/JEditor/NewsPanel/nls/en-au/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/NewsPanel/nls/en-us/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/en-us/i18n.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/nls/en-us/i18n.js	(révision 12190)
@@ -0,0 +1,2 @@
+define({
+});

Property changes on: src/js/JEditor/NewsPanel/nls/en-us/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/less/imports/news_panel/import/vars.less
===================================================================
--- src/less/imports/news_panel/import/vars.less	(nonexistent)
+++ src/less/imports/news_panel/import/vars.less	(révision 12190)
@@ -0,0 +1,18 @@
+@main			: #2677d9;
+
+@greyXL			: #f4f4f4;
+@greyL 			: #e1e1e1;
+@grey			: #C7C7C7;
+@greyM 			: #999999;
+@greyD			: #666666;
+@greyXD			: #383838;
+@black			: #1a1a1a;
+
+@fb-color		: #36609F;
+@twitter-color	: #2CAAE1;
+@google-color	: #DC5442;
+@pinterest-color: #CB2128;
+
+
+@raleway 		: 'raleway';
+@opensans 		: 'open sans';
\ No newline at end of file

Property changes on: src/less/imports/news_panel/import/vars.less
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: Gruntfile.js
===================================================================
--- Gruntfile.js	(révision 12189)
+++ Gruntfile.js	(révision 12190)
@@ -22,7 +22,8 @@
                     'build/css/logs_panel.css': 'src/less/imports/logs_panel/main.less',
                     'build/css/design_panel.css': 'src/less/imports/design_panel/main.less',
                     'build/css/evaluation_panel.css': 'src/less/imports/evaluation_panel/main.less',
-                    'build/css/feedget_panel.css': 'src/less/imports/feedget_panel/main.less'
+                    'build/css/feedget_panel.css': 'src/less/imports/feedget_panel/main.less',
+                    'build/css/news_panel.css': 'src/less/imports/news_panel/main.less'
                 },
                 options: {
                     strictImports: true,
@@ -52,6 +53,7 @@
                     'build/css/design_panel.css': 'src/less/imports/design_panel/main.less',
                     'build/css/evaluation_panel.css': 'src/less/imports/evaluation_panel/main.less',
                     'build/css/feedget_panel.css': 'src/less/imports/feedget_panel/main.less',
+                    'build/css/news_panel.css': 'src/less/imports/news_panel/main.less'
                 },
                 options: {
                     strictImports: true,
Index: assets/ACLs/admin.json
===================================================================
--- assets/ACLs/admin.json	(révision 12189)
+++ assets/ACLs/admin.json	(révision 12190)
@@ -198,5 +198,9 @@
  "access_panel_feedget": {
     "value": true,
     "comparison": "==="
+ },
+ "acces_panel_news": {
+    "value": true,
+    "comparison": "==="
  }
 }
Index: assets/ACLs/lpadmin.json
===================================================================
--- assets/ACLs/lpadmin.json	(révision 12189)
+++ assets/ACLs/lpadmin.json	(révision 12190)
@@ -198,6 +198,10 @@
      "access_panel_feedget": {
         "value": true,
         "comparison": "==="
+     },
+     "acces_panel_news": {
+        "value": true,
+        "comparison": "==="
      }
    }
    
\ No newline at end of file
Index: assets/ACLs/root.json
===================================================================
--- assets/ACLs/root.json	(révision 12189)
+++ assets/ACLs/root.json	(révision 12190)
@@ -198,5 +198,9 @@
      "access_panel_feedget": {
         "value": true,
         "comparison": "==="
+     },
+     "access_panel_news": {
+        "value": true,
+        "comparison": null
      }
 }
Index: assets/ACLs/superadmin.json
===================================================================
--- assets/ACLs/superadmin.json	(révision 12189)
+++ assets/ACLs/superadmin.json	(révision 12190)
@@ -198,5 +198,9 @@
  "access_panel_feedget": {
     "value": true,
     "comparison": "==="
+ },
+ "acces_panel_news": {
+    "value": true,
+    "comparison": "==="
  }
 }
Index: src/js/JEditor/App/Router.js
===================================================================
--- src/js/JEditor/App/Router.js	(révision 12189)
+++ src/js/JEditor/App/Router.js	(révision 12190)
@@ -27,7 +27,8 @@
         "quote": __IDEO_QUOTE__,
         "feedget": __IDEO_FEEDGET__,
         "icom": __IDEO_ICOM3__,
-        "restaurant": __IDEO_RESTAURANT__
+        "restaurant": __IDEO_RESTAURANT__,
+        "news": __IDEO_ICOM3__,
       };
       for (var route in routes) {
         if (route_params[route] !== undefined) {
Index: src/js/JEditor/App/Router_save.js
===================================================================
--- src/js/JEditor/App/Router_save.js	(révision 12189)
+++ src/js/JEditor/App/Router_save.js	(révision 12190)
@@ -37,6 +37,7 @@
                 "feedget": __IDEO_FEEDGET__,
                 "icom": __IDEO_ICOM3__,
                 "restaurant": __IDEO_RESTAURANT__,
+                "news": __IDEO_ICOM3__,
             };
             if (!this.user.can("access_panel_" + name, route_params[name])) {
                 this.forbidden();
Index: src/js/JEditor/App/Templates/application.html
===================================================================
--- src/js/JEditor/App/Templates/application.html	(révision 12189)
+++ src/js/JEditor/App/Templates/application.html	(révision 12190)
@@ -6,6 +6,8 @@
     <nav>
         <ul>
             <li><a href="#" class="page active panel-link" data-target="pages"><span class="icon icon-file" ></span><span class="text"><%= __("Pages")%></span></a></li>
+            <!-- add condition after-->
+            <li><a href="#"  class="news panel-link" data-target="news"><span class="icon icon-file" ></span><span class="text"><%= __("News")%></span></a></li>
             <% if(user.can('access_panel_design')){ %>
             	<li><a href="#"  class="design panel-link" data-target="design"><span class="icon icon-design" ></span><span class="text"><%= __("Design")%></span></a></li>
             <% } %>
Index: src/js/JEditor/App/routes/main.js
===================================================================
--- src/js/JEditor/App/routes/main.js	(révision 12189)
+++ src/js/JEditor/App/routes/main.js	(révision 12190)
@@ -17,7 +17,8 @@
     "./quote",
     "./icom",
     "./restaurant",
-    "./feedget"
+    "./feedget",
+    "./news",
 ],
         function (
                 social,
@@ -38,7 +39,8 @@
                 quote,
                 icom,
                 restaurant,
-                feedget
+                feedget,
+                news
                 )
         {
             return {
@@ -60,6 +62,7 @@
                 quote:quote,
                 icom:icom,
                 restaurant:restaurant,
-                feedget:feedget
+                feedget:feedget,
+                news:news
             };
         });
Index: src/js/JEditor/App/routes/news.js
===================================================================
--- src/js/JEditor/App/routes/news.js	(nonexistent)
+++ src/js/JEditor/App/routes/news.js	(révision 12190)
@@ -0,0 +1,8 @@
+define([], function () {
+    return function () {
+        require(["JEditor/NewsPanel/NewsPanel"], _.bind(function (NewsPanel) {
+            if (!(this.app.currentPanel instanceof NewsPanel))
+                this.app.currentPanel = NewsPanel.getInstance();
+        }, this));
+    };
+});
\ No newline at end of file

Property changes on: src/js/JEditor/App/routes/news.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/App/routes_build.js
===================================================================
--- src/js/JEditor/App/routes_build.js	(révision 12189)
+++ src/js/JEditor/App/routes_build.js	(révision 12190)
@@ -17,5 +17,6 @@
  "quote":"quote",
  "icom":"icom",
  "restaurant":"restaurant",
- "feedget":"feedget"
+ "feedget":"feedget",
+ "news":"news"
 });
Index: src/js/JEditor/App/routes_define.json
===================================================================
--- src/js/JEditor/App/routes_define.json	(révision 12189)
+++ src/js/JEditor/App/routes_define.json	(révision 12190)
@@ -17,5 +17,6 @@
   "quote":"quote",
   "icom":"icom",
   "restaurant":"restaurant",
-  "feedget":"feedget"
+  "feedget":"feedget",
+  "news": "news"
 }
Index: src/js/JEditor/DashBoard/Templates/DashBoardTpl.html
===================================================================
--- src/js/JEditor/DashBoard/Templates/DashBoardTpl.html	(révision 12189)
+++ src/js/JEditor/DashBoard/Templates/DashBoardTpl.html	(révision 12190)
@@ -27,6 +27,16 @@
                         <%=__('description')%>
                     </span>
                 </a>
+            </li>
+             <!-- add condition after-->
+            <li>
+                <a href="#news" class="design panel-link" data-target="news">
+                    <span class="icon icon-file-doc"></span>
+                    <span class="title"><%=__('news')%></span>
+                    <span class="text">
+                        <%=__('news_explained')%>
+                    </span>
+                </a>
             </li><!--  
             <% } %>
             <% if(user.can('access_panel_design')){ %>
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12190)
@@ -0,0 +1,54 @@
+define([
+    "JEditor/Commons/Ancestors/Views/PanelView",
+    "text!./Templates/NewsPanel.html",
+    "JEditor/Commons/Languages/Views/LanguagesDropDown",
+    "./Views/NewsListView",
+    "i18n!./nls/i18n"
+],
+        function (PanelView, NewsPanelTemplate, LanguagesDropDown, NewsListView, translate) {
+            var NewsPanel = PanelView.extend(
+                    /**
+                     * @lends JEditor.Panels.NewsPanel.prototype
+                     */
+                            {
+                                cssFile: __IDEO_INTEGRATION_PATH__ + "/build/css/news_panel.css",
+
+                                constructor: function (options) {
+                                    //mon code
+                                    PanelView.call(this, {panelName: 'newsPanel'});
+                                },
+                                /**
+                                 * initialise l'objet
+                                 */
+                                initialize: function () {
+                                    this._super();
+                                    this._template = this.buildTemplate(NewsPanelTemplate, translate);
+                                },
+                                load: function () {
+                                    //load des langues
+                                    this.childViews.langDropDown =  new LanguagesDropDown({
+                                        collection : this.languages,
+                                        _default : this.currentLang,
+                                        defaultLabel : 'language'
+                                    });
+
+                                    this.childViews.newsListView = new NewsListView();
+                                    this.listenTo(this.childViews.langDropDown, "selected:choice", this._onLangSelect);
+
+                                    //utiliser si erreur
+                                    // this.loadingError();
+                                    //toujours utiliser si cuccès
+                                    this.loadingEnd();
+                                },
+                                /**
+                                 * @return JEditor.Panels.NewsPanel
+                                 */
+                                render: function () {
+                                    this.$el.html(this._template());
+                                    this.$('.side-bar__lang').append(this.childViews.langDropDown.render().el);
+                                    this.$('.page-container').append(this.childViews.newsListView.render().el);
+                                    return this;
+                                }
+                            });
+                    return NewsPanel;
+                });

Property changes on: src/js/JEditor/NewsPanel/NewsPanel.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/NewsPanel/Templates/NewsPanel.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/NewsPanel.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Templates/NewsPanel.html	(révision 12190)
@@ -0,0 +1,44 @@
+<div class="header">
+    <div id="sub-header">
+        <div class="sub-header__action">
+            <div class="sub-header__action-content">
+                <span class="icon">
+                    <svg width="32px" height="32px" viewBox="0 0 32 32" enable-background="new 0 0 32 32" xml:space="preserve"><path fill="#FFFFFF" d="M27.678,7.111l-9.82-5.669c-1.021-0.589-2.692-0.589-3.716,0L4.321,7.111C3.3,7.701,2.463,9.15,2.463,10.331v11.34c0,1.18,0.836,2.629,1.857,3.219l9.821,5.669c1.023,0.589,2.695,0.589,3.716,0l9.82-5.669c1.023-0.59,1.859-2.039,1.859-3.219v-11.34C29.537,9.15,28.701,7.701,27.678,7.111z"/></svg>
+                    <span class="count">0</span>
+                </span>
+                <span class="txt"><%=__('publishedArticles')%></span>
+            </div>
+        </div>
+        <div class="sub-header__infos">
+            <h1 class="sub-header__info-title news-title"><%=__('newsTitle')%></h1>
+            <p class="sub-header__info-txt"><%=__('newsDesc')%></p>
+            <div class="sub-header__info-icon">
+                <svg width="32px" height="32px" viewBox="0 0 32 32" enable-background="new 0 0 32 32" xml:space="preserve"><path fill="#FFFFFF" d="M27.678,7.111l-9.82-5.669c-1.021-0.589-2.692-0.589-3.716,0L4.321,7.111C3.3,7.701,2.463,9.15,2.463,10.331v11.34c0,1.18,0.836,2.629,1.857,3.219l9.821,5.669c1.023,0.589,2.695,0.589,3.716,0l9.82-5.669c1.023-0.59,1.859-2.039,1.859-3.219v-11.34C29.537,9.15,28.701,7.701,27.678,7.111z"/></svg>
+                <svg width="32px" height="32px" viewBox="0 0 32 32" enable-background="new 0 0 32 32" xml:space="preserve"><path fill="#FFFFFF" d="M15.563,21.303c0-0.693,0.146-1.283,0.437-1.77c0.292-0.485,0.667-0.917,1.124-1.289c0.459-0.376,0.965-0.714,1.519-1.02c0.557-0.308,1.108-0.625,1.666-0.959c0.666-0.416,1.262-0.846,1.789-1.289c0.526-0.443,0.983-0.943,1.373-1.498c0.389-0.555,0.688-1.172,0.896-1.852c0.209-0.68,0.311-1.463,0.311-2.352c0-1.442-0.258-2.685-0.77-3.725c-0.515-1.04-1.181-1.894-1.998-2.559c-0.818-0.666-1.74-1.151-2.769-1.457c-1.024-0.305-2.063-0.457-3.121-0.457c-0.943,0-1.859,0.118-2.746,0.354c-0.888,0.235-1.714,0.582-2.476,1.04C10.035,2.928,9.356,3.49,8.759,4.156C8.162,4.823,7.685,5.585,7.324,6.445l3.62,2.498c0.222-0.361,0.484-0.743,0.791-1.145c0.304-0.403,0.665-0.763,1.082-1.082c0.415-0.318,0.874-0.583,1.373-0.791c0.5-0.208,1.054-0.312,1.665-0.312c0.5,0,0.993,0.076,1.476,0.229c0.485,0.152,0.91,0.381,1.271,0.687c0.359,0.306,0.65,0.68,0.873,1.124c0.224,0.444,0.334,0.957,0.334,1.539c0,0.777-0.203,1.465-0.604,2.06c-0.399,0.598-0.896,1.118-1.478,1.563c-0.582,0.443-1.18,0.824-1.79,1.143c-0.611,0.319-1.124,0.604-1.539,0.853c-1.388,0.808-2.305,1.748-2.747,2.83c-0.445,1.084-0.667,2.305-0.667,3.664h4.579V21.303z M15.687,30.998v-5.744h-4.619v5.744H15.687z"/></svg>
+            </div>
+        </div>
+    </div>
+</div>
+
+<div class="page-container">
+
+	<aside class="side-bar">
+        <div class="side-bar__lang">
+            <p class="lang-label"><%=__('editedVersion')%></p>
+        </div>
+        <span class="side-bar__title"><%=__('sideInfo')%></span>
+        <div class="scroll-container  message-scroll-container">
+            <ul class="form-list">
+        	    <!-- insert form list here-->
+            </ul>
+        </div>
+        <a class="dialog-view-trigger addCategory">
+            <span>+</span><%=__("newsAddACategory")%>
+        </a>
+
+        <a class="dialog-view-trigger addArticle">
+            <span>+</span><%=__("newsAddArticle")%>
+        </a>
+	</aside>
+    <!-- list -->
+</div>
\ No newline at end of file

Property changes on: src/js/JEditor/NewsPanel/Templates/NewsPanel.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/NewsPanel/Views/NewsListView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/NewsListView.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Views/NewsListView.js	(révision 12190)
@@ -0,0 +1,17 @@
+define([
+    "JEditor/Commons/Ancestors/Views/ListView",
+    "text!../Templates/NewsList.html",
+    "i18n!../nls/i18n"
+  ], function (ListView, template, translate) {
+    var NewsListView = ListView.extend({
+        initialize: function() {
+            this.template = this.buildTemplate(template, translate);
+        },
+        render: function () {
+            this.$el.html(this.template());
+            return this;
+        }
+    });
+   
+    return NewsListView;
+  });
\ No newline at end of file

Property changes on: src/js/JEditor/NewsPanel/Views/NewsListView.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12190)
@@ -0,0 +1,11 @@
+define({
+	"newsTitle" : "Actualités",
+	"newsDesc": "Parcourez vos pages depuis le menu de gauche. Ajoutz du contenu et prévusualisez votre site à tout moment",
+	"newsAddACategory" : "Ajouter une catégorie",
+	"publishedArticles": "Article\(s\) publié\(\)",
+	"editedVersion" : "Editez une version",
+	"allArticles": "Tous les articles",
+	"config" : "Réglages",
+	"newsAddArticle" : "Ajouter un article"
+
+});
\ No newline at end of file

Property changes on: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 12190)
@@ -0,0 +1,11 @@
+define({
+	"newsTitle" : "Actualités",
+	"newsDesc": "Parcourez vos pages depuis le menu de gauche. Ajoutz du contenu et prévusualisez votre site à tout moment",
+	"newsAddACategory" : "Ajouter une catégorie",
+	"publishedArticles": "Article\(s\) publié\(\)",
+	"editedVersion" : "Editez une version",
+	"allArticles": "Tous les articles",
+	"config" : "Réglages",
+	"newsAddArticle" : "Ajouter un article"
+
+});
\ No newline at end of file

Property changes on: src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/NewsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/i18n.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12190)
@@ -0,0 +1,15 @@
+define({
+    "root": {
+	    "newsTitle" : "Actualités",
+	    "newsDesc": "Parcourez vos pages depuis le menu de gauche. Ajoutz du contenu et prévusualisez votre site à tout moment",
+        "newsAddACategory" : "Ajouter une catégorie",
+        "publishedArticles": "Article\(s\) publié\(\)",
+        "editedVersion" : "Editez une version",
+        "allArticles": "Tous les articles",	
+        "config" : "Réglages",
+        "newsAddArticle" : "Ajouter un article"
+
+    },
+    "fr-fr": true,
+    "fr-ca": true
+})
\ No newline at end of file

Property changes on: src/js/JEditor/NewsPanel/nls/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/less/imports/news_panel/main.less
===================================================================
--- src/less/imports/news_panel/main.less	(nonexistent)
+++ src/less/imports/news_panel/main.less	(révision 12190)
@@ -0,0 +1,80 @@
+// basic less tools
+@import 'import/vars';
+@import '../common/import/mixins';
+
+
+// base project settings
+@import '../common/base/base';
+@import '../common/layout/layout';
+
+
+// commons modules
+@import '../common/module/sub-header';
+@import '../common/module/side-bar';
+@import '../common/module/error-message';
+
+
+.header, .sub-header__action, .sub-header__infos  {
+	background-color: #19a6be;
+}
+
+.addCategory {
+	background: 0 0;
+	border: none;
+	display: block;
+	cursor: pointer;
+	text-decoration: none;
+	font-family: Raleway,sans-serif;
+	font-size: 14px;
+	font-weight: 600;
+	color: #19a6be;
+	padding-left: 36px;
+	line-height: 40px;
+	margin-top: 20px;
+	-webkit-transition: color 300ms ease;
+	-moz-transition: color 300ms ease;
+	-o-transition: color 300ms ease;
+	-ms-transition: color 300ms ease;
+	transition: color 300ms ease;
+  }
+  
+.addArticle {
+	background: 0 0;
+	border: none;
+	display: block;
+	cursor: pointer;
+	text-decoration: none;
+	font-family: Raleway,sans-serif;
+	font-size: 14px;
+	font-weight: 600;
+	color: #19a6be;
+	padding-left: 36px;
+	line-height: 40px;
+	margin-top: 20px;
+	-webkit-transition: color 300ms ease;
+	-moz-transition: color 300ms ease;
+	-o-transition: color 300ms ease;
+	-ms-transition: color 300ms ease;
+	transition: color 300ms ease;
+  }
+
+  .news-container-header {
+    background: #fff;
+    z-index: 2;
+    margin-bottom: 30px;
+
+    .form-title p {
+        color: @greyD;
+        font-size: 32px;
+        border-bottom: 1px solid rgba(0, 0, 0, 0.15);
+        padding-bottom: 15px;
+        margin-bottom: 1em;
+        margin-top: 0;
+    }
+
+    .form-title p span
+    {
+        color: @grey;
+        font-size: 22px;
+    }
+  }
\ No newline at end of file

Property changes on: src/less/imports/news_panel/main.less
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/less/imports/variables.less
===================================================================
--- src/less/imports/variables.less	(révision 12189)
+++ src/less/imports/variables.less	(révision 12190)
@@ -28,6 +28,8 @@
 @evaluationColor:       #c2922c;
 @feedgetColorLight:     #2677d9;
 @feedgetColorDark:      #004599;
+@newsColorLight:        #1ed5f5;
+@newsColorDark:         #19a6be;
 
 @contrastTextColor: 	rgba(0,0,0,0.85);
 
Index: src/less/main.less
===================================================================
--- src/less/main.less	(révision 12189)
+++ src/less/main.less	(révision 12190)
@@ -1869,6 +1869,14 @@
           background-color: @feedgetColorDark;
         }
       }
+      &.news {
+        &:hover {
+          background-color: @newsColorLight;
+        }
+        &.active {
+          background-color: @newsColorDark;
+        }
+      }
 
     }
     span.icon {
