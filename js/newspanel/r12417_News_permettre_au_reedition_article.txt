Revision: r12417
Date: 2024-06-13 17:29:02 +0300 (lkm 13 Jon 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: permettre au reedition article

## Files changed

## Full metadata
------------------------------------------------------------------------
r12417 | srazana<PERSON><PERSON>oa | 2024-06-13 17:29:02 +0300 (lkm 13 Jon 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/NewsPanel.js

News: permettre au reedition article
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12416)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12417)
@@ -374,8 +374,8 @@
                                     this.childViews.newsEditorView.renderAddCategorie()
                                 },
                                 onAddArticleClick : function() {
-                                if(this.currentCategorie && this.currentCategorie.lang[this.currentLang.id])
-                                   this.childViews.newsEditorView.renderAddArticle(this.currentCategorie);
+                                if(!this.currentCategorie || (this.currentCategorie && this.currentCategorie.lang[this.currentLang.id]))
+                                    this.childViews.newsEditorView.renderAddArticle(this.currentCategorie);  
                                 else {
                                     this.error({
                                         title: translate("addArticle"),
@@ -596,8 +596,8 @@
                                         set : function(currentArticle) {
                                             if (currentArticle !== this._currentArticle) {
                                                 this._currentArticle = currentArticle;
-                                                this.trigger(Events.NewsPanelEvents.ARTICLE_CHANGE, this, currentArticle, undefined);
                                             }
+                                            this.trigger(Events.NewsPanelEvents.ARTICLE_CHANGE, this, currentArticle, undefined);
                                         }
                                     },
                                     currentZone : {
