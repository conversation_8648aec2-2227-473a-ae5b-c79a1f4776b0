Revision: r12235
Date: 2024-04-24 17:03:26 +0300 (lrb 24 Apr 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: ajout de'une route selon la langue et la categorie

## Files changed

## Full metadata
------------------------------------------------------------------------
r12235 | srazanandralisoa | 2024-04-24 17:03:26 +0300 (lrb 24 Apr 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/App/Router_save.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/App/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/App/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/App/nls/i18n.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/App/routes/news.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/App/routes_build.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/App/routes_define.json

News: ajout de'une route selon la langue et la categorie
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/App/Router_save.js
===================================================================
--- src/js/JEditor/App/Router_save.js	(révision 12234)
+++ src/js/JEditor/App/Router_save.js	(révision 12235)
@@ -14,6 +14,7 @@
             "social": "social",
             "storelocator": "storelocator",
              "*notFound": "notFound",
+            "news(/:lang)(/:categorieID)": "news",
         },
         constructor: function (attrs, options) {
             if (arguments.callee.caller !== Router.getInstance)
@@ -73,6 +74,27 @@
             }, this));
 
         },
+        news: function (lang, categorieID) {
+            require(["JEditor/NewsPanel/NewsPanel"], _.bind(function (NewsPanel) {
+                if (!(this.app.currentPanel instanceof NewsPanel))
+                    this.app.currentPanel = NewsPanel.getInstance();
+                if (!lang)
+                    this.navigate('news/' + this.app.params.defaultcontentlang);
+                var callback = function () {
+                    this.currentLang = this.languages.get(lang ? lang : this.app.params.defaultcontentlang);
+                    if (categorieID) {
+                        this.currentCategorie = this.categories.get(categorieID);
+                    } else
+                        this.currentCategorie = null;
+                };
+                var currentPanel = this.app.currentPanel;
+                if (this.app.currentPanel.loaded) {
+                    callback.call(currentPanel);
+                } else {
+                    this.app.currentPanel.once(Events.LoadEvents.LOAD_SUCCESS, callback);
+                }
+            }, this));
+        },
         design: function () {
             require(["JEditor/DesignPanel/DesignPanel"], _.bind(function (DesignPanel) {
                     this.app.currentPanel = DesignPanel.getInstance();
Index: src/js/JEditor/App/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/App/nls/fr-ca/i18n.js	(révision 12234)
+++ src/js/JEditor/App/nls/fr-ca/i18n.js	(révision 12235)
@@ -14,5 +14,6 @@
     "MobileApp" :"Appli Mobile",
     "Quote" :"Quote",
     "disconnect": "vous avez été déconnecté",
-    "Feedget":"Feedget"
+    "Feedget":"Feedget",
+    "News":"Actialités"
 });
\ No newline at end of file
Index: src/js/JEditor/App/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/App/nls/fr-fr/i18n.js	(révision 12234)
+++ src/js/JEditor/App/nls/fr-fr/i18n.js	(révision 12235)
@@ -14,5 +14,6 @@
     "MobileApp" :"Appli Mobile",
     "Quote" :"Quote",
     "disconnect": "vous avez été déconnecté",
-    "Feedget":"Feedget"
+    "Feedget":"Feedget",
+    "News":"Actialités",
 });
\ No newline at end of file
Index: src/js/JEditor/App/nls/i18n.js
===================================================================
--- src/js/JEditor/App/nls/i18n.js	(révision 12234)
+++ src/js/JEditor/App/nls/i18n.js	(révision 12235)
@@ -22,5 +22,6 @@
         "Deliver" :"Deliver",
         "logs":"logs",
         "disconnect":"You have been disconnected",
-        "Feedget":"Feedget"
+        "Feedget":"Feedget",
+        "News":"News"
     }, "fr-fr":true, "fr-ca":true });
Index: src/js/JEditor/App/routes/news.js
===================================================================
--- src/js/JEditor/App/routes/news.js	(révision 12234)
+++ src/js/JEditor/App/routes/news.js	(révision 12235)
@@ -1,8 +1,23 @@
-define([], function () {
-    return function () {
+define(["JEditor/Commons/Events"], function (Events) {
+    return function (lang, categorieID ) {
         require(["JEditor/NewsPanel/NewsPanel"], _.bind(function (NewsPanel) {
             if (!(this.app.currentPanel instanceof NewsPanel))
                 this.app.currentPanel = NewsPanel.getInstance();
+            if (!lang)
+                this.navigate('news/' + this.app.params.defaultcontentlang);
+            var callback = function () {
+                this.currentLang = this.languages.get(lang ? lang : this.app.params.defaultcontentlang);
+                if (categorieID) {
+                    this.currentCategorie = this.categories.get(categorieID);
+                } else
+                    this.currentCategorie = null;
+            };
+            var currentPanel = this.app.currentPanel;
+            if (this.app.currentPanel.loaded) {
+                callback.call(currentPanel);
+            } else {
+                this.app.currentPanel.once(Events.LoadEvents.LOAD_SUCCESS, callback);
+            }
         }, this));
     };
 });
\ No newline at end of file
Index: src/js/JEditor/App/routes_build.js
===================================================================
--- src/js/JEditor/App/routes_build.js	(révision 12234)
+++ src/js/JEditor/App/routes_build.js	(révision 12235)
@@ -18,5 +18,5 @@
  "icom":"icom",
  "restaurant":"restaurant",
  "feedget":"feedget",
- "news":"news"
+ "news":"news(/:lang)(/:categorieID)"
 });
Index: src/js/JEditor/App/routes_define.json
===================================================================
--- src/js/JEditor/App/routes_define.json	(révision 12234)
+++ src/js/JEditor/App/routes_define.json	(révision 12235)
@@ -18,5 +18,5 @@
   "icom":"icom",
   "restaurant":"restaurant",
   "feedget":"feedget",
-  "news": "news"
+  "news": "news(/:lang)(/:categorieID)"
 }
