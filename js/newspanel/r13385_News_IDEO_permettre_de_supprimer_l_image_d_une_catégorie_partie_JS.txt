Revision: r13385
Date: 2024-10-25 10:33:45 +0300 (zom 25 Okt 2024) 
Author: sraz<PERSON><PERSON><PERSON><PERSON> 

## Commit message
News IDEO: permettre de supprimer l'image d'une catégorie (partie JS)

## Files changed

## Full metadata
------------------------------------------------------------------------
r13385 | srazanandralisoa | 2024-10-25 10:33:45 +0300 (zom 25 Okt 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/categorieForm.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/AddArticleView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/CategorieAddView.js
   M /branches/ideo3_v2/integration/src/less/imports/news_panel/main.less

News IDEO: permettre de supprimer l'image d'une catégorie (partie JS)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Templates/categorieForm.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/categorieForm.html	(révision 13384)
+++ src/js/JEditor/NewsPanel/Templates/categorieForm.html	(révision 13385)
@@ -2,6 +2,7 @@
     <div class="dis-table bordered">
         <div class="flex_categorie">
             <div class="upload-categorie">
+                <% if(edit && fileUrl != ''){ %> <span class="icon-bin"> </span><% } %>
                 <!-- uploader -->
             </div>
             <div class="categorie-info">
Index: src/js/JEditor/NewsPanel/Views/AddArticleView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/AddArticleView.js	(révision 13384)
+++ src/js/JEditor/NewsPanel/Views/AddArticleView.js	(révision 13385)
@@ -125,11 +125,11 @@
             this.$('.category-dropdown').append(this._categoryDropdown.render().el);
             
             this.fileUploader = new FileUploaderView({
-                customStockEvent: '_parsestock_image',
-                acceptedTypes: ['image'],
-                acceptedExtensions: ['jpeg', 'jpg', 'png', 'svg', 'webp'],
-                refusedExtensions: ['bmp'],
-                uploadUrl: __IDEO_UPLOADLOGO_URL__ ? __IDEO_UPLOADLOGO_URL__ : '/admin/resource-logo',
+                uploadParams :{
+                    customStockEvent: '_parsestock_image',
+                    acceptedTypes: ['image'],
+                    refusedExtensions: ['bmp'],
+                },
                 currentFile : this.model.getFile(),
                 collection: this.fileCollection
             });
Index: src/js/JEditor/NewsPanel/Views/CategorieAddView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(révision 13384)
+++ src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(révision 13385)
@@ -33,7 +33,8 @@
          'change input[name="title"]': 'setTitle',
          'change textarea[name="description"]': 'setDescription',
          'click [data-language]': '_onLangClick',
-         'click button.edit' : 'changeView'
+         'click button.edit' : 'changeView',
+         'click .upload-categorie .icon-bin': 'imageRemove',
         },
         isform : false,
         currentCategorieLang : null,
@@ -118,7 +119,9 @@
                 return this;
             }
             else{
+                var file = this.model.getFile();
                 params = {
+                    fileUrl :  (file)? file.fileUrl : '',
                     edit :  this.edit,
                     detail: this.model.getByLanguage(this.lang),
                     cid : this.model.id
@@ -128,12 +131,12 @@
                if(this.edit) this.$('.side-bar__lang').append(this.langDropDown.render().el);
                 
                 this.fileUploader = new FileUploaderView({
-                    customStockEvent: '_parsestock_image',
-                    acceptedTypes: ['image'],
-                    acceptedExtensions: ['jpeg', 'jpg', 'png', 'svg', 'webp'],
-                    refusedExtensions: ['bmp'],
-                    uploadUrl: __IDEO_UPLOADLOGO_URL__ ? __IDEO_UPLOADLOGO_URL__ : '/admin/resource-logo',
-                    currentFile : this.model.getFile(),
+                    uploadParams :{
+                        customStockEvent: '_parsestock_image',
+                        acceptedTypes: ['image'],
+                        refusedExtensions: ['bmp'],
+                    },
+                    currentFile : file,
                     collection: this.fileCollection
                 });
             
@@ -210,6 +213,13 @@
                 this.changeView();
             }
         },
+        imageRemove : function(e){
+            e.stopImmediatePropagation();
+            this.model.set('ressource', null);
+            this.model.file = null;
+            this.render();
+            return false;
+        },
         changeView: function (){
             this.isform = !this.isform;
             this.render()
Index: src/less/imports/news_panel/main.less
===================================================================
--- src/less/imports/news_panel/main.less	(révision 13384)
+++ src/less/imports/news_panel/main.less	(révision 13385)
@@ -393,6 +393,7 @@
 		.upload-categorie, .upload-article{
 			height: 100%;
 			width: 300px;
+			position: relative;
 			.uploader{
 				background-color: #666867;
 				.message-wrapper{
@@ -411,6 +412,23 @@
 					overflow: hidden;
 			}
 			}
+			.icon-bin{
+				bottom: -5%;
+				right: -3%;
+				background-color: #e74c3c;
+				padding: 4px 9px;
+				height: 21px;
+				border-radius: 20px;
+				color: #fff;
+				border: 5px solid #fff;
+				position: absolute;
+				margin-right: -10px;
+				line-height: 20px;
+				text-align: center;
+				font-size: 12px;
+				cursor: pointer;
+				z-index: 1;
+			}
 		}
 		.categorie-info, .article-info{
 			height: 100%;
