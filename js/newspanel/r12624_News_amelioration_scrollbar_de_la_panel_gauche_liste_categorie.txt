Revision: r12624
Date: 2024-07-25 07:44:28 +0300 (lkm 25 Jol 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: amelioration scrollbar de la panel gauche liste categorie

## Files changed

## Full metadata
------------------------------------------------------------------------
r12624 | srazanandralisoa | 2024-07-25 07:44:28 +0300 (lkm 25 Jol 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/NewsPanel.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/categorieList.html

News: amelioration scrollbar de la panel gauche liste categorie
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Templates/NewsPanel.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/NewsPanel.html	(révision 12623)
+++ src/js/JEditor/NewsPanel/Templates/NewsPanel.html	(révision 12624)
@@ -30,11 +30,6 @@
         </div>
         <!--liste des pages-->
         <% if(canAddNews){ %>
-            <a class="dialog-view-trigger addCategory">
-                <span>+</span><%=__("newsAddACategory")%>
-            </a>
-        <% } %>
-        <% if(canAddNews){ %>
             <a class="dialog-view-trigger addArticle">
                 <span>+</span><%=__("newsAddArticle")%>
             </a>
Index: src/js/JEditor/NewsPanel/Templates/categorieList.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/categorieList.html	(révision 12623)
+++ src/js/JEditor/NewsPanel/Templates/categorieList.html	(révision 12624)
@@ -23,3 +23,6 @@
         </ul>
     </nav>
 </div>
+<a class="dialog-view-trigger addCategory">
+    <span>+</span><%=__("newsAddACategory")%>
+</a>
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12623)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12624)
@@ -416,11 +416,12 @@
                                  * @private
                                  */
                                 _scrollbar : function() {
-                                    var button = this.dom.newsPanel.addCategorieButtonDown
+                                    var button = this.dom.newsPanel.addCategorieButtonDown;
+                                    var buttonar = this.dom.newsPanel.addArticleButtonDown;
                                     var categorieList = this.childViews.categorieList.$el;
                                     if (categorieList.parent().length > 0 && this && button) {
                                         var windowHeight = this.dom.window.height();
-                                        var categorieListOffset = categorieList.offsetParent().offset().top - this.dom.window.scrollTop() + categorieList.position().top + button.outerHeight(true);
+                                        var categorieListOffset = categorieList.offsetParent().offset().top - this.dom.window.scrollTop() + categorieList.position().top + button.outerHeight(true) + buttonar.outerHeight(true);
                                         var availableSpace = windowHeight - categorieListOffset;
                                         var categorieListHeight = 0;
                                         this.childViews.categorieList.$('.wrapper').each(function() {
@@ -506,6 +507,8 @@
                                             top : this.checkAffixTop
                                         }
                                     });
+                                    this.dom.window.on('scroll.pageDelegate', _.bind(this._scrollbar, this));
+			                        this.dom.window.on('resize.pageDelegate', _.bind(this._scrollbar, this));
                                     this.dom.window.scroll();
                                 },
                                 /**
@@ -537,6 +540,7 @@
                                     this._super();
                                     this.dom.newsPanel.rightSidebar = this.$('#item-config');
                                     this.dom.newsPanel.addCategorieButtonDown = this.$('.dialog-view-trigger.addCategory');
+                                    this.dom.newsPanel.addArticleButtonDown = this.$('.dialog-view-trigger.addArticle');
                                 },
                                 /**
                                  * @return JEditor.Panels.NewsPanel
