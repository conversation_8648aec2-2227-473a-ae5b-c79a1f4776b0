Revision: r12232
Date: 2024-04-12 10:22:41 +0300 (zom 12 Apr 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
pull update from integration to integration_news from 12162 to 12231

## Files changed

## Full metadata
------------------------------------------------------------------------
r12232 | sraz<PERSON><PERSON><PERSON>oa | 2024-04-12 10:22:41 +0300 (zom 12 Apr 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/assets/ACLs/admin.json
   M /branches/ideo3_v2/integration_news/assets/ACLs/lpadmin.json
   M /branches/ideo3_v2/integration_news/assets/ACLs/root.json
   M /branches/ideo3_v2/integration_news/assets/ACLs/superadmin.json
   M /branches/ideo3_v2/integration_news/src/js/JEditor/App/App.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/DesignPanel/Views/ConstantResourceEditorView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/PagePanel/Contents/Ancestors/Content.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/PagePanel/Contents/Columns/Models/Column.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/PagePanel/Contents/Options/Models/FadeOption.js (de /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/Models/FadeOption.js:12231)
   M /branches/ideo3_v2/integration_news/src/js/JEditor/PagePanel/Contents/Options/Models/main.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/PagePanel/Contents/Options/Templates/fadeOptions.html (de /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/Templates/fadeOptions.html:12231)
   A /branches/ideo3_v2/integration_news/src/js/JEditor/PagePanel/Contents/Options/Views/FadeOptionView.js (de /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/Views/FadeOptionView.js:12231)
   M /branches/ideo3_v2/integration_news/src/js/JEditor/PagePanel/Contents/Options/Views/main.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/PagePanel/Contents/Options/main.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/PagePanel/Contents/Options/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/PagePanel/Contents/Options/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/PagePanel/Contents/Options/nls/i18n.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/PagePanel/Contents/Sections/Models/Section.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/ParamsPanel/ParamsPanel.js

pull update from integration to integration_news from 12162 to 12231
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/App/App.js
===================================================================
--- src/js/JEditor/App/App.js	(révision 12231)
+++ src/js/JEditor/App/App.js	(révision 12232)
@@ -97,7 +97,7 @@
           $.ajax(window.location.href.split('/admin')[0]+'/admin/checkToken')
           .done(function(data) {
             if (that.checktimeOut)window.clearTimeout(this.checktimeOut);
-            that.checktimeOut = window.setTimeout(_.bind(that.check_session, that),10000);
+            that.checktimeOut = window.setTimeout(_.bind(that.check_session, that),300000);
            if (typeof(that.user)==undefined || data.role != that.user.role) {
               url = window.location.href.split('/admin')[0]+'/admin/logoutsso';
               that.notify({
Index: src/js/JEditor/DesignPanel/Views/ConstantResourceEditorView.js
===================================================================
--- src/js/JEditor/DesignPanel/Views/ConstantResourceEditorView.js	(révision 12231)
+++ src/js/JEditor/DesignPanel/Views/ConstantResourceEditorView.js	(révision 12232)
@@ -7,7 +7,9 @@
     "JEditor/DesignPanel/Models/ConstantResource",
     "i18n!../nls/i18n",
     "text!../Templates/constantResourceEditor.html",
-    "ace/ace"
+    "ace/ace",
+     // not in params
+     "jqueryPlugins/toaster"
 ], function($, _, BabblerView, Events, Utils, ConstantResource, translate, constantResourceEditor, ace) {
     ace=window.ace;
     var ConstantResourceEditorView = BabblerView.extend({
@@ -116,36 +118,8 @@
 
                 var resourceExpression = this.dom[this.cid].aceEditor.getSession().getValue();
 
-                var onResourceSaved = _.bind(function() {
-                    this.notify({
-                        message: translate("saveSuccess"),
-                        options: {ok: _.bind(function() {
-                                this.setLoading(false);
-                                this.dom[this.cid].wrapperEditor.removeClass('edited');
-                                this.dom[this.cid].aceEditor.focus();
-                                this.editedResourceContent = this.dom[this.cid].aceEditor.getSession().getValue();
-                                this.stopListening(this.ressources[this.editedResource], Events.BackboneEvents.SYNC, this.setEditor);
-                            }, this)}
-                    });
-                }, this);
-
-                var onSaveError = _.bind(function(model, xhr) {
-                    var message = translate("saveError");
-                    if (xhr && xhr.status == 400 && xhr.responseText.match('LESS Error : '))
-                        message += ('<br/>' + xhr.responseText.replace('LESS Error : ', ''));
-                    console.log(arguments);
-                    this.setLoading(false);
-                    this.error({
-                        message: message,
-                        options: {ok: _.bind(function() {
-                                this.stopListening(this.ressources[this.editedResource], Events.BackboneEvents.ERROR, this.setEditor);
-                                this.dom[this.cid].aceEditor.focus();
-                            }, this)}
-                    });
-                }, this);
-
-                this.listenToOnce(resource, Events.BackboneEvents.SYNC, onResourceSaved);
-                this.listenToOnce(resource, Events.BackboneEvents.ERROR, onSaveError);
+                this.listenToOnce(resource, Events.BackboneEvents.SYNC, this.onResourceSaved);
+                this.listenToOnce(resource, Events.BackboneEvents.ERROR, this.onSaveError);
                 this.ressources[this.editedResource].resource_expression = resourceExpression;
                 this.ressources[this.editedResource].doSave();
                 this.setLoading(true);
@@ -152,6 +126,41 @@
             }
             return false;
         },
+        onResourceSaved :function() {
+
+            this.setLoading(false);
+            this.dom[this.cid].wrapperEditor.removeClass('edited');
+            this.dom[this.cid].aceEditor.focus();
+            this.editedResourceContent = this.dom[this.cid].aceEditor.getSession().getValue();
+            this.stopListening(this.ressources[this.editedResource], Events.BackboneEvents.SYNC, this.setEditor);
+            $.toast({
+                text: translate("saveSuccess"), 
+                icon: 'icon-check-circle', 
+                type:'success',
+                appendTo:'#design-edit .content .html-editor',
+                showHideTransition: 'fade', 
+                hideAfter: 5000, 
+                position: 'top-right', 
+                textAlign: 'left', 
+                allowToastClose:false,
+                loader:false,
+                stack :1
+            });
+        },
+        onSaveError: function(model, xhr) {
+            var message = translate("saveError");
+            if (xhr && xhr.status == 400 && xhr.responseText.match('LESS Error : '))
+                message += ('<br/>' + xhr.responseText.replace('LESS Error : ', ''));
+            console.log(arguments);
+            this.setLoading(false);
+            this.error({
+                message: message,
+                options: {ok: _.bind(function() {
+                        this.stopListening(this.ressources[this.editedResource], Events.BackboneEvents.ERROR, this.setEditor);
+                        this.dom[this.cid].aceEditor.focus();
+                    }, this)}
+            });
+        }
     });
     Events.extend({
         ConstantResourceEditorViewEvents: {
Index: src/js/JEditor/PagePanel/Contents/Ancestors/Content.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Ancestors/Content.js	(révision 12231)
+++ src/js/JEditor/PagePanel/Contents/Ancestors/Content.js	(révision 12232)
@@ -8,6 +8,8 @@
     "JEditor/PagePanel/Contents/Options/Views/ColorsOptionView",
     "JEditor/PagePanel/Contents/Options/Models/EffectsOption",
     "JEditor/PagePanel/Contents/Options/Views/EffectsOptionView",
+    "JEditor/PagePanel/Contents/Options/Models/FadeOption",
+    "JEditor/PagePanel/Contents/Options/Views/FadeOptionView",
     "JEditor/PagePanel/Contents/Options/Models/StylesOption",
     "JEditor/PagePanel/Contents/Options/Views/StylesOptionView",
     "JEditor/PagePanel/Contents/Options/Models/AdvancedOption",
@@ -16,7 +18,7 @@
     "JEditor/PagePanel/Contents/Options/Views/PopupOptionView",
     "JEditor/PagePanel/Contents/Options/Models/PopupStyle",
     "JEditor/PagePanel/Contents/Options/Views/PopupStyleView"
-], function(_, Events, NestedModel, OptionsCollection, OptionLib, ColorsOption, ColorsOptionView, EffectsOption, EffectsOptionView,StylesOption,StylesOptionView,AdvancedOption,AdvancedOptionView, PopupOption, PopupOptionView, PopupStyle, PopupStyleView) {
+], function(_, Events, NestedModel, OptionsCollection, OptionLib, ColorsOption, ColorsOptionView, EffectsOption, EffectsOptionView, FadeOption, FadeOptionView,StylesOption,StylesOptionView,AdvancedOption,AdvancedOptionView, PopupOption, PopupOptionView, PopupStyle, PopupStyleView) {
     var /**
      * @class Content
      * Définit les modèles contenant des options (blocks, colonnes, sections)
@@ -270,7 +272,7 @@
             }
         }
     });
-    OptionLib.registerModel(ColorsOption, EffectsOption, StylesOption,PopupOption, PopupStyle, AdvancedOption );
-    OptionLib.registerView(ColorsOptionView, EffectsOptionView,StylesOptionView, PopupOptionView, PopupStyleView, AdvancedOptionView);
+    OptionLib.registerModel(ColorsOption, EffectsOption,FadeOption, StylesOption,PopupOption, PopupStyle, AdvancedOption );
+    OptionLib.registerView(ColorsOptionView, EffectsOptionView, FadeOptionView, StylesOptionView, PopupOptionView, PopupStyleView, AdvancedOptionView);
     return Content;
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Columns/Models/Column.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Columns/Models/Column.js	(révision 12231)
+++ src/js/JEditor/PagePanel/Contents/Columns/Models/Column.js	(révision 12232)
@@ -3,9 +3,10 @@
     "JEditor/PagePanel/Contents/Blocks/Block/Block",
     "JEditor/PagePanel/Contents/Options/Models/ColorsOption",
     "JEditor/PagePanel/Contents/Options/Models/EffectsOption",
+    "JEditor/PagePanel/Contents/Options/Models/FadeOption",
     "JEditor/PagePanel/Contents/Options/Models/StylesOption",
     "i18n!JEditor/PagePanel/Contents/Columns/nls/i18n"
-], function(Content, Block, ColorsOption, EffectsOption,StylesOption, translate) {
+], function(Content, Block, ColorsOption, EffectsOption, FadeOption, StylesOption, translate) {
 
     /**
      * Une colonne
@@ -36,8 +37,10 @@
                                 this.options.remove(this.options.colors);
                             if (this.options.effects)
                                 this.options.remove(this.options.effects);
+                            if(!this.options.fade)
+                                this.options.add(new FadeOption({}, {content: this}));
                             if(!this.options.styles)
-                                this.options.add(new StylesOption({}, {content: this}));  
+                                this.options.add(new StylesOption({}, {content: this}));
                         },
                         translate: translate
                     });
Index: src/js/JEditor/PagePanel/Contents/Options/Models/FadeOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/Models/FadeOption.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Options/Models/FadeOption.js	(révision 12232)
@@ -0,0 +1,31 @@
+define(
+    ["JEditor/PagePanel/Contents/Options/Models/AbstractOption", "i18n!../nls/i18n"],
+    function(AbstractOption, translate) {
+        /**
+         * 
+         * @class FadeOption
+         * @extends AbstractOption
+         * @property {String} backgroundColor La couleur de fond
+         * @property {String} color La couleur du texte
+         */
+        var FadeOption = AbstractOption.extend(
+                /**
+                 * @lends FadeOption.prototype
+                 */
+                        {
+                            defaults: {
+                                optionType: 'fade',
+                                priority: 100,
+                                type : 0,
+                                delay : 2,
+                                duration : 2
+                            },
+                            initialize: function() {
+                                this._super();
+                            },
+                            translate: translate
+                        });
+                FadeOption.SetAttributes(['type', 'delay', 'duration']);
+                FadeOption.tanslate = translate;
+                return FadeOption;
+            });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Options/Models/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/Models/main.js	(révision 12231)
+++ src/js/JEditor/PagePanel/Contents/Options/Models/main.js	(révision 12232)
@@ -1,10 +1,11 @@
 define(
         "JEditor/PagePanel/Contents/Options/Models/main",
-        ["./ColorsOption", "./EffectsOption", "./OptionsCollection"],
-        function(ColorsOption, EffectsOption, OptionsCollection) {
+        ["./ColorsOption", "./EffectsOption",  "./FadeOption", "./OptionsCollection"],
+        function(ColorsOption, EffectsOption, FadeOption, OptionsCollection) {
             var component = {
                 "ColorsOption": ColorsOption,
                 "EffectsOption": EffectsOption,
+                "FadeOption": FadeOption,
                 "OptionsCollection": OptionsCollection
             };
             return component;
Index: src/js/JEditor/PagePanel/Contents/Options/Templates/fadeOptions.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/Templates/fadeOptions.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Options/Templates/fadeOptions.html	(révision 12232)
@@ -0,0 +1,135 @@
+<div class="panel-option-container animated galleryStyle">
+    <article class="panel-option fade-type">
+        <header>
+            <h3 class="option-name"><%=__("fadeStyleAffichage")%></h3>
+            <p class="panel-content-legend"><%=__("fadeStyleAffichageDesc")%></p>
+        </header>
+        <div class="option-content type">
+        <%  var _id=_.uniqueId('fadetype');%>
+            <input type="radio" class="select-box" name="type" value="0" id="<%=_id %>" <%=(type==0)?'checked':''%>>
+                <label for="<%=_id %>">
+                <div class="wrapper">
+                    <div class="horizontal-wrap">
+                        <span class="icon-wrapper">
+                            <span class="icon-delete"></span>
+                        </span>
+                        <span class="name"><%= __("type0")%></span>   
+                    </div>
+                </div>
+            </label>
+            <%  var _id=_.uniqueId('fadetype');%>
+            <input type="radio" class="select-box" name="type" value="1" id="<%=_id %>" <%=(type==1)?'checked':''%>>
+                <label for="<%=_id %>">
+                <div class="wrapper">
+                    <div class="horizontal-wrap">
+                        <span class="icon-wrapper">
+                            <span class="icon-FadeIn"></span>
+                        </span>
+                        <span class="name"><%= __("type1")%> </span>
+                    </div>
+                </div>
+            </label>
+            <%  var _id=_.uniqueId('fadetype');%>
+            <input type="radio" class="select-box" name="type" value="2" id="<%=_id %>" <%=(type==2)?'checked':''%>>
+                <label for="<%=_id %>">
+                <div class="wrapper">
+                    <div class="horizontal-wrap">
+                        <span class="icon-wrapper">
+                            <span class="icon-FadeinUp"></span>
+                        </span>
+                        <span class="name"><%= __("type2")%></span>
+                    </div>
+                </div>
+            </label>
+            
+            <%  var _id=_.uniqueId('fadetype');%>
+            <input type="radio" class="select-box"  name="type" value="3" id="<%=_id %>" <%=(type==3)?'checked':''%>>
+                <label for="<%=_id %>">
+                <div class="wrapper">
+                    <div class="horizontal-wrap">
+                        <span class="icon-wrapper">
+                            <span class="icon-FadeinDown"></span>
+                        </span>
+                        <span class="name"><%= __("type3")%></span>
+                    </div>
+                </div>
+            </label>
+
+            <%  var _id=_.uniqueId('fadetype');%>
+            <input type="radio" class="select-box"  name="type" value="4" id="<%=_id %>" <%=(type==4)?'checked':''%>>
+                <label for="<%=_id %>">
+                <div class="wrapper">
+                    <div class="horizontal-wrap">
+                        <span class="icon-wrapper">
+                            <span class="icon-FadeinRight"></span>
+                        </span>
+                        <span class="name"><%= __("type4")%></span>
+                    </div>
+                </div>
+            </label>
+
+            <%  var _id=_.uniqueId('fadetype');%>
+            <input type="radio" class="select-box"  name="type" value="5" id="<%=_id %>" <%=(type==5)?'checked':''%>>
+                <label for="<%=_id %>">
+                <div class="wrapper">
+                    <div class="horizontal-wrap">
+                        <span class="icon-wrapper">
+                            <span class="icon-FadeinLeft"></span>
+                        </span>
+                        <span class="name"><%= __("type5")%></span>
+                    </div>
+                </div>
+            </label>
+
+            <%  var _id=_.uniqueId('fadetype');%>
+            <input type="radio" class="select-box"  name="type" value="6" id="<%=_id %>" <%=(type==6)?'checked':''%>>
+                <label for="<%=_id %>">
+                <div class="wrapper">
+                    <div class="horizontal-wrap">
+                        <span class="icon-wrapper">
+                            <span class="icon-FadeInScale"></span>
+                        </span>
+                        <span class="name"><%= __("type6")%></span>
+                    </div>
+                </div>
+            </label>
+        </div>
+    </article>
+</div>
+<div class="panel-option-container animated ">
+    <article class="panel-option delay-value">
+        <header>
+            <h3 class="option-name"><%= __("delayLabel")%></h3>
+            <p class="option-description"><%= __("delayDesc")%></p>
+        </header>
+        <div class="option-content">
+            <!--slider-->
+            <div class="slider-container delay-slider">
+                <span class="less icon"></span>
+                <div class="slider delay">
+                </div>
+                <span class="more icon"></span>
+            </div>
+            <!--end slider-->
+        </div>
+    </article>
+</div>
+<div class="panel-option-container animated">
+    <article class="panel-option duration-value">
+        <header>
+            <h3 class="option-name"><%= __("durationLabel")%></h3>
+            <p class="option-description"><%= __("durationDesc")%></p>
+        </header>
+        <div class="option-content">
+            <!--slider-->
+            <div class="slider-container duration-slider">
+                <span class="less icon"></span>
+                <div class="slider duration">
+                </div>
+                <span class="more icon"></span>
+            </div>
+            <!-- end slider-->
+        </div>
+
+    </article>
+</div>
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Options/Views/FadeOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/Views/FadeOptionView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Options/Views/FadeOptionView.js	(révision 12232)
@@ -0,0 +1,82 @@
+define( [
+    "text!../Templates/fadeOptions.html",
+    "JEditor/PagePanel/Contents/Options/Views/AbstractOptionView",
+    "JEditor/PagePanel/Contents/Options/Models/FadeOption",
+    "i18n!../nls/i18n",
+    //not in params
+], function(fadeOptionsTpl, AbstractOptionView, FadeOption, translate) {
+    var /**
+     * @class FadeOptionView
+     * @extends AbstractOptionView
+     */
+            FadeOptionView = AbstractOptionView.extend(
+                    /**
+                     * @lends FadeOptionView.prototype
+                     */
+                            {
+                                optionType: 'fade',
+                                tagName: "div",
+                                className: "panel-content fade-panel scroll-container",
+                                translate: translate,
+                                events: {
+                                    'change input[type="radio"].select-box': '_onTypeChange',
+                                    'slide .slider.delay': '_setDelay',
+                                    'slide .slider.duration': '_setDuration'
+                                },
+                                /**
+                                 * initialise la vue
+                                 */
+                                initialize: function() {
+                                    this._super();
+                                    this._template = this.buildTemplate(fadeOptionsTpl, translate);
+                                },
+                                /**
+                                 * change le type d'animation
+                                 */
+                                _onTypeChange: function(event) {
+                                    var $target = $(event.currentTarget);
+                                    this.model.type = parseInt($target.val());
+                                },
+                                /**
+                                 * change le delay
+                                */
+                                _setDelay: function(e, data) {
+                                    this.model.delay = data.value;
+                                },
+                                /**
+                                 * change le duration
+                                 */
+                                _setDuration: function(e, data) {
+                                    this.model.duration = data.value;
+                                },
+                                /**
+                                 * actualise la vue
+                                 */
+                                render: function() {
+                                    this.$el.html(this._template(this.model.toJSON()));
+                                    var sliderDuration = this.$('.duration-slider .slider');
+                                    sliderDuration.slider({
+                                        min: 1,
+                                        max: 6,
+                                        step: 1,
+                                        value:this.model.duration,
+                                        range:"min"
+                                    });
+                                    var sliderDetails = this.$('.delay-slider .slider');
+                                    sliderDetails.slider({
+                                        min: 1,
+                                        max: 6,
+                                        step: 1,
+                                        value:this.model.delay,
+                                        range:"min"
+                                    });
+                                    this.dom[this.cid].sliderDuration = sliderDuration;
+                                    this.dom[this.cid].sliderDetails = sliderDetails;
+
+                                    this.scrollables();
+                                    return this;
+                                }
+                            });
+                    FadeOptionView.translate = translate;
+                    return FadeOptionView;
+                });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Options/Views/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/Views/main.js	(révision 12231)
+++ src/js/JEditor/PagePanel/Contents/Options/Views/main.js	(révision 12232)
@@ -1,11 +1,12 @@
 define(
         "JEditor/PagePanel/Contents/Options/Views/main",
-        ["./AbstractOptionView", "./ColorsOptionView", "./EffectsOptionView", "./OptionCollectionView"],
-        function(AbstractOptionView, ColorsOptionView, EffectsOptionView, OptionCollectionView) {
+        ["./AbstractOptionView", "./ColorsOptionView", "./EffectsOptionView", "./FadeOptionView", "./OptionCollectionView"],
+        function(AbstractOptionView, ColorsOptionView, EffectsOptionView, FadeOptionView, OptionCollectionView) {
             var component = {
                 "AbstractOptionView": AbstractOptionView,
                 "ColorsOptionView": ColorsOptionView,
                 "EffectsOptionView": EffectsOptionView,
+                "FadeOptionView": FadeOptionView,
                 "OptionCollectionView": OptionCollectionView
             };
             return component;
Index: src/js/JEditor/PagePanel/Contents/Options/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/main.js	(révision 12231)
+++ src/js/JEditor/PagePanel/Contents/Options/main.js	(révision 12232)
@@ -8,6 +8,10 @@
     exports.EffectsOption = {
         Model: Models.EffectsOption,
         View: Views.EffectsOptionView
+    };
+    exports.FadeOption = {
+        Model: Models.FadeOption,
+        View: Views.FadeOptionView
     }
     return module;
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Options/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/nls/fr-ca/i18n.js	(révision 12231)
+++ src/js/JEditor/PagePanel/Contents/Options/nls/fr-ca/i18n.js	(révision 12232)
@@ -83,5 +83,19 @@
     "sectionWidthDesc"  : "Glisser pour ajuster la largeur de la section (cette option n'a pas d'effet dans la zone d'administration).",
     "sectionScreenWidth"  :"Content alignment",
     "sectionScreenWidthDesc"  : "adjust content alignment on the site grid (this option has no effect in the administration area)",
-    "aucune"  : "Aucune"
+    "aucune"  : "Aucune",
+    "fade" : "Animation",
+    "fadeStyleAffichage" : "Animer l'apparition de la section",
+    "fadeStyleAffichageDesc" : "Choisissez comment la section apparaît à l'écran",
+    "type0" : "Pas d'animation ",
+    "type1" : "Fondu",
+    "type2" : "Fondu et entrée vers le haut",
+    "type3" : "Fondu et entrée vers le bas",
+    "type4" : "Fondu et entrée vers la droite",
+    "type5" : "Fondu et entrée vers la gauche",
+    "type6" : "Fondu et agrandissement",
+    "delayLabel" : "Délais",
+    "delayDesc" : "Ajuster le Délais avant que l'animation se déclenche",
+    "durationLabel" : "Durée",
+    "durationDesc":"Ajuster la durée de l'animation"
 });
Index: src/js/JEditor/PagePanel/Contents/Options/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/nls/fr-fr/i18n.js	(révision 12231)
+++ src/js/JEditor/PagePanel/Contents/Options/nls/fr-fr/i18n.js	(révision 12232)
@@ -83,5 +83,19 @@
     "sectionWidthDesc"  : "Glisser pour ajuster la largeur de la section (cette option n'a pas d'effet dans la zone d'administration).",
     "sectionScreenWidth"  :"Alignement du contenu",
     "sectionScreenWidthDesc"  : "Ajuster l'alignement du contenu sur la grille du site (cette option n'a pas d'effet dans la zone d'administration)",
-    "aucune"  : "Aucune"
+    "aucune"  : "Aucune",
+    "fade" : "Animation",
+    "fadeStyleAffichage" : "Animer l'apparition de la section",
+    "fadeStyleAffichageDesc" : "Choisissez comment la section apparaît à l'écran",
+    "type0" : "Pas d'animation ",
+    "type1" : "Fondu",
+    "type2" : "Fondu et entrée vers le haut",
+    "type3" : "Fondu et entrée vers le bas",
+    "type4" : "Fondu et entrée vers la droite",
+    "type5" : "Fondu et entrée vers la gauche",
+    "type6" : "Fondu et agrandissement",
+    "delayLabel" : "Délais",
+    "delayDesc" : "Ajuster le Délais avant que l'animation se déclenche",
+    "durationLabel" : "Durée",
+    "durationDesc":"Ajuster la durée de l'animation"
 });
Index: src/js/JEditor/PagePanel/Contents/Options/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/nls/i18n.js	(révision 12231)
+++ src/js/JEditor/PagePanel/Contents/Options/nls/i18n.js	(révision 12232)
@@ -85,7 +85,22 @@
     "sectionWidthDesc"  : "Slide to adjust the width of the section.<br>(This option has no effect in the adminstration area)",
     "sectionScreenWidth"  :"Content alignment",
     "sectionScreenWidthDesc"  : "adjust content alignment on the site grid (this option has no effect in the administration area)",
-    "aucune"  : "None"
+    "aucune"  : "None",
+    "fade" : "Animation",
+    "fadeStyleAffichage" : "Animate section appearance",
+    "fadeStyleAffichageDesc" : "Choose how the section appears on screen",
+    "type0" : "No animation ",
+    "type1" : "Fade in",
+    "type2" : "Fade in up",
+    "type3" : "Fade in down",
+    "type4" : "Fade in right",
+    "type5" : "Fade in left",
+    "type6" : "Fade in and scale up",
+    "delayLabel" : "Delay",
+    "delayDesc" : "Adjust Delay before animation starts",
+    "durationLabel" : "Duration",
+    "durationDesc":"Adjust animation duration"
+    
   },
   "fr-fr": true,
   "fr-ca": true
Index: src/js/JEditor/PagePanel/Contents/Sections/Models/Section.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Sections/Models/Section.js	(révision 12231)
+++ src/js/JEditor/PagePanel/Contents/Sections/Models/Section.js	(révision 12232)
@@ -5,6 +5,7 @@
     "JEditor/PagePanel/Contents/Columns/Models/Column",
     "JEditor/PagePanel/Contents/Options/Models/ColorsOption",
     "JEditor/PagePanel/Contents/Options/Models/EffectsOption",
+    "JEditor/PagePanel/Contents/Options/Models/FadeOption",
     "JEditor/PagePanel/Contents/Options/Models/StylesOption",
     "JEditor/PagePanel/Contents/Options/Models/PopupOption",
     "JEditor/PagePanel/Contents/Options/Models/PopupStyle",
@@ -15,6 +16,7 @@
         Column,
         ColorsOption,
         EffectsOption,
+        FadeOption,
         StylesOption,
         PopupOption,
         PopupStyle,
@@ -49,6 +51,8 @@
                                             this.options.remove(this.options.colors);
                                         if (this.options.effects)
                                             this.options.remove(this.options.effects);
+                                        if(!this.options.fade)
+                                            this.options.add(new FadeOption({}, {content: this}));
                                         if(!this.options.styles)
                                             this.options.add(new StylesOption({}, {content: this}));     
                                     }                           
Index: src/js/JEditor/ParamsPanel/ParamsPanel.js
===================================================================
--- src/js/JEditor/ParamsPanel/ParamsPanel.js	(révision 12231)
+++ src/js/JEditor/ParamsPanel/ParamsPanel.js	(révision 12232)
@@ -95,14 +95,17 @@
                                             object: new PolitiqueConfidentialiteView({model:this.params}),
                                             icon:"icon-link",
                                             title:translate("PolitiqueConfidentialite")
-                                        },
-                                        customShortcode:{
+                                        }
+                                    };
+                                     
+                                    if(this.app.user.can('createshortcode')) {
+                                        this.menuEntries. customShortcode = 
+                                        {
                                             object: new CustomShortcode({model:this.params}),
                                             icon:"icon-link",
                                             title:translate("CustomShortcode")
-                                        },
-                                       
-                                    };
+                                        }
+                                    } 
                                     if(this.app.user.can('view_jsonLd')) {
                                         this.menuEntries.donneeStructuree =   
                                         {
Index: assets/ACLs/admin.json
===================================================================
--- assets/ACLs/admin.json	(révision 12231)
+++ assets/ACLs/admin.json	(révision 12232)
@@ -199,6 +199,10 @@
     "value": true,
     "comparison": "==="
  },
+ "createshortcode": {
+    "value": false,
+    "comparison": null
+ },
  "acces_panel_news": {
     "value": true,
     "comparison": "==="
Index: assets/ACLs/lpadmin.json
===================================================================
--- assets/ACLs/lpadmin.json	(révision 12231)
+++ assets/ACLs/lpadmin.json	(révision 12232)
@@ -199,6 +199,10 @@
         "value": true,
         "comparison": "==="
      },
+     "createshortcode": {
+        "value": false,
+        "comparison": null
+     },
      "acces_panel_news": {
         "value": true,
         "comparison": "==="
Index: assets/ACLs/root.json
===================================================================
--- assets/ACLs/root.json	(révision 12231)
+++ assets/ACLs/root.json	(révision 12232)
@@ -199,6 +199,10 @@
         "value": true,
         "comparison": "==="
      },
+     "createshortcode": {
+        "value": true,
+        "comparison": null
+     },
      "access_panel_news": {
         "value": true,
         "comparison": null
Index: assets/ACLs/superadmin.json
===================================================================
--- assets/ACLs/superadmin.json	(révision 12231)
+++ assets/ACLs/superadmin.json	(révision 12232)
@@ -199,6 +199,10 @@
     "value": true,
     "comparison": "==="
  },
+ "createshortcode": {
+    "value": false,
+    "comparison": null
+ },
  "acces_panel_news": {
     "value": true,
     "comparison": "==="
