Revision: r12398
Date: 2024-06-11 17:09:37 +0300 (tlt 11 Jon 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: fix message d'erreur ajout article

## Files changed

## Full metadata
------------------------------------------------------------------------
r12398 | sraz<PERSON><PERSON><PERSON>oa | 2024-06-11 17:09:37 +0300 (tlt 11 Jon 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/AddArticleView.js

News: fix message d'erreur ajout article
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Views/AddArticleView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/AddArticleView.js	(révision 12397)
+++ src/js/JEditor/NewsPanel/Views/AddArticleView.js	(révision 12398)
@@ -189,7 +189,7 @@
                 introduction.addClass('error');
                 valid = false;
             }
-            if (this.model.ressource) {
+            if (!this.model.ressource) {
                 this.error({
                     title: translate("saveAction"),
                     message: translate("RessourceRequired")
