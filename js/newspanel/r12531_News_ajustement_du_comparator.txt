Revision: r12531
Date: 2024-07-03 08:43:30 +0300 (lrb 03 Jol 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: ajustement du comparator

## Files changed

## Full metadata
------------------------------------------------------------------------
r12531 | sraz<PERSON><PERSON><PERSON>oa | 2024-07-03 08:43:30 +0300 (lrb 03 Jol 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/assets/ACLs/admin.json
   M /branches/ideo3_v2/integration/assets/ACLs/lpadmin.json
   M /branches/ideo3_v2/integration/assets/ACLs/root.json
   M /branches/ideo3_v2/integration/assets/ACLs/superadmin.json

News: ajustement du comparator
------------------------------------------------------------------------

## Diff
Index: assets/ACLs/admin.json
===================================================================
--- assets/ACLs/admin.json	(révision 12530)
+++ assets/ACLs/admin.json	(révision 12531)
@@ -209,7 +209,7 @@
  },
  "access_panel_news": {
     "value": false,
-    "comparison": null
+    "comparison": "&&"
  },
  "change_news_layout": {
    "value": false,
Index: assets/ACLs/lpadmin.json
===================================================================
--- assets/ACLs/lpadmin.json	(révision 12530)
+++ assets/ACLs/lpadmin.json	(révision 12531)
@@ -209,7 +209,7 @@
      },
      "access_panel_news": {
         "value": false,
-        "comparison": null
+        "comparison": "&&"
      },
      "change_news_layout": {
        "value": false,
Index: assets/ACLs/root.json
===================================================================
--- assets/ACLs/root.json	(révision 12530)
+++ assets/ACLs/root.json	(révision 12531)
@@ -209,7 +209,7 @@
      },
      "access_panel_news": {
         "value": true,
-        "comparison": null
+        "comparison": "&&"
      },
      "change_news_layout": {
        "value": true,
Index: assets/ACLs/superadmin.json
===================================================================
--- assets/ACLs/superadmin.json	(révision 12530)
+++ assets/ACLs/superadmin.json	(révision 12531)
@@ -209,7 +209,7 @@
  },
  "access_panel_news": {
     "value": true,
-    "comparison": null
+    "comparison": "&&"
  },
  "change_news_layout": {
    "value": false,
