Revision: r12375
Date: 2024-06-06 11:59:11 +0300 (lkm 06 Jon 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: amelioration affichage recherche article

## Files changed

## Full metadata
------------------------------------------------------------------------
r12375 | sraz<PERSON><PERSON><PERSON>oa | 2024-06-06 11:59:11 +0300 (lkm 06 Jon 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/articlesTable.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/i18n.js

News: amelioration affichage recherche article
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Templates/articlesTable.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/articlesTable.html	(révision 12374)
+++ src/js/JEditor/NewsPanel/Templates/articlesTable.html	(révision 12375)
@@ -5,7 +5,7 @@
 		<label><%=__('search')%></label>
 		<span class="search-news"> 
 			<i class="icon icon-find"></i>
-			<input type="text"  placeholder="<%=__('searchDesc')%>">
+			<input type="text"  placeholder="<%=__('searchDesc')%>" value="<%=searchValue%>">
 
 		</span>
 	</form>
@@ -53,6 +53,11 @@
 			</tr>
 		</thead>
 		<tbody>
+			<%if(content.length==0){%> 
+				<tr>
+					<td colspan="7" style="text-align: center;" > <%=__('none_result')%></td>
+				</tr>
+			<%} else {%>
 			<%_.each(content,function(item){ %>
 			<tr class="<%=(!item.active)?'disabled':''%>" data-cid="<%= item.cid %>">
 				<td><span class="img-news"> <img src="<%=item.image%>" alt="titre"></span></td>
@@ -91,6 +96,7 @@
 			</tr>
 			<%
 			});%>
+			<%}%>
 		</tbody>
 	</table>
 </div>
Index: src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 12374)
+++ src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 12375)
@@ -52,9 +52,10 @@
       };
       }
     },
-    render: function() {
+    render: function(searchValue) {
       this.undelegateEvents();
       var list;
+      var searchValue = (searchValue)?searchValue: "";
       // on modif selon notre besoin dans le tableau
       list = this.listToJson(this.currentList, this.lang);
       //pour le filtre category et recherche
@@ -63,12 +64,17 @@
       // pour le sort    
       list = this.sortList(list);
       if (list.length > 0 ) {
-          var params = _.extend({}, {content: list, current : this._current,currentlang : this.lang, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected, user: this.app.user}, this.addTemplateParams(this.collection, list));
+          var params = _.extend({}, {content: list, searchValue : searchValue, current : this._current,currentlang : this.lang, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected, user: this.app.user}, this.addTemplateParams(this.collection, list));
           this.$el.html(this._template(params));
       }
       else {
-          var params = _.extend({}, {content: list, current : this._current,currentlang : this.lang, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected}, this.addTemplateParams(this.collection, list));
+        if(searchValue){
+          var params = _.extend({}, {content: list, searchValue : searchValue, current : this._current,currentlang : this.lang, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected, user: this.app.user}, this.addTemplateParams(this.collection, list));
+          this.$el.html(this._template(params));
+        }
+        else{var params = _.extend({}, {content: list, current : this._current,currentlang : this.lang, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected}, this.addTemplateParams(this.collection, list));
           this.$el.html(this._emptyTemplate(params));
+        }
       }
       this.delegateEvents();
       this.scrollables();
@@ -187,7 +193,7 @@
         var sortedby = this.sortedBy ;
         if (sortedby === 'categorie' || sortedby === 'title'){
           var sortedStrings = _.sortBy(list, function(value) {
-              return value[sortedby].toLowerCase();
+              return value[sortedby].trim().toLowerCase();
           });
           return this.sortAsc ? sortedStrings : sortedStrings.reverse();
         }else 
@@ -201,16 +207,15 @@
           var $target = $(event.currentTarget);
           var searchTerm = $target.val();
           this.filteredBy = function (item) {
-            var valeurColonne1 = item.title.toLowerCase(); 
-            var valeurColonne2 = item.categorie.toLowerCase();
+            var valeurColonne1 = item.title.trim().toLowerCase(); 
+            var valeurColonne2 = item.categorie.trim().toLowerCase();
         
             // Utiliser des expressions régulières pour trouver des correspondances partielles
-            var regexColonne1 = new RegExp(searchTerm.toLowerCase(), 'g');
-            var regexColonne2 = new RegExp(searchTerm.toLowerCase(), 'g');
+            var regexColonne1 = new RegExp(searchTerm.trim().toLowerCase(), 'g');
+            var regexColonne2 = new RegExp(searchTerm.trim().toLowerCase(), 'g');
             return regexColonne1.test(valeurColonne1) || regexColonne2.test(valeurColonne2);
           };
-          this.render();
-          $target.val(searchTerm);
+          this.render(searchTerm);
         }
     },
   });
Index: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12374)
+++ src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12375)
@@ -64,7 +64,9 @@
 	"state" : "Etat",
 	"deleteAction":"Suppression",
 	"confirmDelete": "Vous \u00eates sur le point de supprimer d\u00e9finitivement une page",
+	"none_result": "Auccune article correspond à la recherche",
 
+
 	// zonetoolbox
 	"publish"				: "Publier",
 	"addContent"			: "Ajouter du contenu",
Index: src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 12374)
+++ src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 12375)
@@ -64,7 +64,9 @@
 	"state" : "Etat",
 	"deleteAction":"Suppression",
 	"confirmDelete": "Vous \u00eates sur le point de supprimer d\u00e9finitivement une page",
+	"none_result": "Auccune article correspond à la recherche",
 
+
 	// zonetoolbox
 	"publish"				: "Publier",
 	"addContent"			: "Ajouter du contenu",
Index: src/js/JEditor/NewsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12374)
+++ src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12375)
@@ -65,6 +65,7 @@
         "state" : "Etat",
         "deleteAction":"Suppression",
         "confirmDeleteArticle": "Vous \u00eates sur le point de supprimer d\u00e9finitivement un article",
+        "none_result": "Auccune article correspond à la recherche",
 
         // zonetoolbox
         "publish"				: "Publier",
