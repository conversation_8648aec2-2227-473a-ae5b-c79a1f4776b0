Revision: r12490
Date: 2024-06-25 10:02:22 +0300 (tlt 25 Jon 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: publier un article et fonction update nombre article publié

## Files changed

## Full metadata
------------------------------------------------------------------------
r12490 | srazanandralisoa | 2024-06-25 10:02:22 +0300 (tlt 25 Jon 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/ArticlesCollection.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/categorieForm.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/publishConfig.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/ArticleEditorView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/NewsEditorView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/PublishConfigView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/SaveButton.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/i18n.js
   M /branches/ideo3_v2/integration_news/src/less/imports/news_panel/main.less

News: publier un article et fonction update nombre article publié
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Models/ArticlesCollection.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/ArticlesCollection.js	(révision 12489)
+++ src/js/JEditor/NewsPanel/Models/ArticlesCollection.js	(révision 12490)
@@ -20,7 +20,17 @@
                     model: Article,
                     parse : function(data) {
                         return (data.data)? data.data : data;
-                    }
+                    },
+                    getPublished:function (lang) {
+                        return this.reduce(function (memo,current) {
+                           var pub = (current.get("publicationDate"))?1:0;
+                          if(lang){
+                            return current.get("lang")=== lang ? memo+pub : memo;
+                          }else{
+                            return memo+pub;
+                          }
+                        },0,this);
+                      },
                     
                 });
                 ArticlesCollection.instance = null;
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12489)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12490)
@@ -74,6 +74,9 @@
                                     this.rightPanelView = new RightPanelView();
                                     this._byLang = {};
                                 },
+                                updatePublishedCount:function () {
+                                    this.$(".sub-header__action-content .icon .count").text(this.articles.getPublished());
+                                  },
                                 load: function () {
                                     var loaded = 0;
                                     this.categories = CategorieCollection.getInstance();
@@ -140,6 +143,8 @@
                                     });
                                    
                                     this.listenTo(this.childViews.langDropDown, "selected:choice", this._onLangSelect);
+                                    this.listenTo(this.articles, 'change', this.updatePublishedCount);
+
                             
                                 },
                                 addArticleOnCategory:function (article){
@@ -423,7 +428,7 @@
                                    this._renderCategorieList();
                                    this.setDOM();
                                    this.renderRightPanel();
-                                 
+                                   this.updatePublishedCount();
                                     return this;
                                 },
                                 /**
@@ -529,6 +534,15 @@
                                         this.rightPanelView.hidePanel();
                                     }
                                     try {
+                                        
+                                        if(this.currentArticle.get('title') =='' || !this.currentArticle.get('ressource')){
+                                            this.error({
+                                                message: translate("errorPublishingInvalideElement"),
+                                                title: translate("error")
+                                            });
+                                            return false
+                                        }
+                                           
                                         this.childViews.publishConfigView = new PublishConfigView({
                                             rightPanelView : this.rightPanelView,
                                             model: this.currentArticle,
@@ -538,6 +552,9 @@
                                         this.rightPanelView.showContent(this.childViews.publishConfigView);
                                         this.listenToOnce(this.childViews.publishConfigView, Events.PublishConfigViewEvents.SAVE, _.bind(onClose, this))
                                             .listenToOnce(this.childViews.publishConfigView, Events.PublishConfigViewEvents.CANCEL, _.bind(onClose, this));
+                                        this.listenToOnce(this.childViews.publishConfigView.model, Events.BackboneEvents.SYNC, this.publishDone)
+                                            .listenToOnce(this.childViews.publishConfigView.model, Events.BackboneEvents.ERROR, this.publishError)
+
                                       
                                     } catch (e) {
                                         console.error(e);
@@ -557,6 +574,38 @@
                                     this.rightPanelView.hideContent(this.childViews.publishConfigView);
                                     this.rightPanelView.hidePanel();
                                 },
+                                publishDone :function (){
+                                    this.stopListening(this.childViews.publishConfigView.model, Events.BackboneEvents.ERROR, this.publishError);
+                                    $.toast({
+                                        text: translate("publishDone"), 
+                                        icon: 'icon-check-circle', 
+                                        type:'success',
+                                        appendTo: '#news-view .main',
+                                        showHideTransition: 'fade', 
+                                        hideAfter: 5000, 
+                                        position: 'top-right', 
+                                        textAlign: 'left', 
+                                        allowToastClose:false,
+                                        loader:false,
+                                        stack :1
+                                    });
+                                },
+                                publishError :function (){
+                                    this.stopListening(this.childViews.publishConfigView.model, Events.BackboneEvents.SYNC, this.publishDone);
+                                    $.toast({
+                                        text: translate("publishError"), 
+                                        icon: 'icon-check-circle', 
+                                        type:'error',
+                                        appendTo:'#news-view .main',
+                                        showHideTransition: 'fade', 
+                                        hideAfter: 5000, 
+                                        position: 'top-right', 
+                                        textAlign: 'left', 
+                                        allowToastClose:false,
+                                        loader:false,
+                                        stack :1
+                                    });
+                                }
                             });
                             Object.defineProperties(NewsPanel.prototype,
                                 {
Index: src/js/JEditor/NewsPanel/Templates/categorieForm.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/categorieForm.html	(révision 12489)
+++ src/js/JEditor/NewsPanel/Templates/categorieForm.html	(révision 12490)
@@ -38,5 +38,4 @@
             <% } %>  
         </div>
     </div>
-    
 </div>
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Templates/publishConfig.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/publishConfig.html	(révision 12489)
+++ src/js/JEditor/NewsPanel/Templates/publishConfig.html	(révision 12490)
@@ -1,52 +1,52 @@
 <div id="meta-configuration" class="news-rigthpanel scrollbar-classic">
     <header class="panel-head">
         <span class="icon icon-rotation-right"></span>
-        <h1 class="panel-name">Publication</h1>
+        <h1 class="panel-name"><%=__('publishtitre')%></h1>
     </header>
     <div class="panel-content active">
         <div class="panel-content-intro">
-            Publiez maintenant ou programmez ka publication de votre article à la date que vous souhaitez
+            <%=__('publishDesc')%>
         </div>
    
     <div class="option-content ">
         <article class="panel-option">
             <header>
-                <h3 class="option-name"><%=__("newsNombreImage")%></h3>
-                <p class="panel-content-legend"><%=__("newsNombreImageLegend")%></p>
+                <h3 class="option-name"><%=__("programOption")%></h3>
+                <p class="panel-content-legend"><%=__("programOptionDesc")%></p>
             </header>
             <div class="option-content">
                 <div class="batch">
-                    <!-- toogle -->
-                       <span class="switch batchActions">
-                           <span></span>
-                       </span>
-                       <span class="labelnon">Non</span>
-                       <span class="labeloui">Oui</span>
-                   </div> 
-                   <div class="btn-group-batch forProgram">
-                       <div class="block-datepicker">
-                           <div id="datepicker">
-                                <!-- datepicker -->
-                       </div>
-           
-                       <div>Votre article sera publié le <span class="dateprog"></span></div>
-                       <div>
-                           <button>
-                               <span class="icon icon-rotation-right"></span>
-                               <span>Programmer la publication</span>
-                           </button>
-                       </div>
-                   </div>
-                   <div class="forPub">
-                       <div>Votre article sera publié immédiatement</div>
-                       <div>
-                           <button>
-                               <span class="icon icon-rotation-right"></span>
-                               <span>Publié l'article</span>
-                           </button>
-                       </div>
-                   </div>
-               </div>
+                <!-- toogle -->
+                    <span class="switch batchActions">
+                        <span></span>
+                    </span>
+                    <span class="labelnon"><%=__("non")%></span>
+                    <span class="labeloui"><%=__("oui")%></span>
+                </div> 
+                <div class="btn-group-batch forProgram">
+                    <div class="block-datepicker">
+                        <div id="datepicker">
+                            <!-- datepicker -->
+                        </div>
+            
+                        <div><%=__("willPublish")%>&nbsp;<span class="dateprog"></span></div>
+                        <div>
+                            <button>
+                                <span class="icon icon-rotation-right"></span>
+                                <span><%=__("programPublish")%></span>
+                            </button>
+                        </div>
+                    </div>
+                </div>
+                <div class="forPub">
+                    <div><%=__("publishImmediate")%></div>
+                    <div>
+                        <button>
+                            <span class="icon icon-rotation-right"></span>
+                            <span><%=__("publishArticle")%></span>
+                        </button>
+                    </div>
+                </div>
             </div>
         </article>
     </div>
Index: src/js/JEditor/NewsPanel/Views/ArticleEditorView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticleEditorView.js	(révision 12489)
+++ src/js/JEditor/NewsPanel/Views/ArticleEditorView.js	(révision 12490)
@@ -159,7 +159,7 @@
                 this.articleDetail.render();
             }
             if (this.zoneToolbox && this.currentZone) {
-                this.newsPanel.$('.zone').prepend(this.zoneToolbox.el);
+                this.newsPanel.$('.zone').html(this.zoneToolbox.el);
                 this.zoneToolbox.render();
             }
             if (this.sectionCollectionView) {
Index: src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 12489)
+++ src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 12490)
@@ -37,6 +37,7 @@
     list:null,
     fadeInEffect: 'fadeIn',
     fadeOutEffect: 'fadeOut',
+    article: null,
     initialize: function() {
       this.options.i18n = true;
       this._super();
@@ -157,13 +158,48 @@
       }
       return false;
     },
+    onError: function () {
+      this.stopListening(this.article, Events.BackboneEvents.SYNC, this.onSave);
+      $.toast({
+        text: translate("DeleteError"), 
+        type:'error',
+        appendTo:'#news-editor .main',
+        showHideTransition: 'fade', 
+        hideAfter: 5000, 
+        position: 'top-right', 
+        textAlign: 'left', 
+        allowToastClose:false,
+        loader:false
+    });
+    },
 
+    onSave: function () {
+        this.stopListening(this.article, Events.BackboneEvents.ERROR, this.onError);
+        this.trigger(Events.LoadEvents.LOAD_SUCCESS, this);
+        $.toast({
+            text: translate("DeleteSuccesful"), 
+            icon: 'icon-check-circle', 
+            type:'success',
+            appendTo:'#news-editor .main',
+            showHideTransition: 'fade', 
+            hideAfter: 3000, 
+            position: 'top-right', 
+            textAlign: 'left', 
+            allowToastClose:false,
+            loader:false
+        });
+                      
+    },
     deleteModel: function(model) {
       if (!this.app.user.can('delete_page'))
         return;
+      this.article = model;
+      this.listenToOnce(this.article, Events.BackboneEvents.SYNC, this.onDone);
+      this.listenToOnce(this.article, Events.BackboneEvents.ERROR, this.onError);
       if (model === this.app.currentPanel.currentArticle)
           this.app.currentPanel.currentArticle = null;
         model.destroy();
+
     },
     
     onEditClick: function(event) {
Index: src/js/JEditor/NewsPanel/Views/NewsEditorView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 12489)
+++ src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 12490)
@@ -157,7 +157,7 @@
             var state = this.model.content.state;
             switch (state) {
                 case 1:
-                    var date = moment(this.model.programmingDate).format("DD/MM/YY");
+                    var date = moment(this.model.programmingDate.date).format("DD/MM/YY");
                     var stateHtml = '<span class="btn bleu">Programmé '+date+'</span>'
                     break;
                 case 2:
Index: src/js/JEditor/NewsPanel/Views/PublishConfigView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/PublishConfigView.js	(révision 12489)
+++ src/js/JEditor/NewsPanel/Views/PublishConfigView.js	(révision 12490)
@@ -21,9 +21,15 @@
         rendered: false,
         _currentCid: null,
         _programmed:false,
+        options : {
+            weekday: 'long',
+            year: 'numeric',
+            month: 'long',
+            day: 'numeric',
+        },
         events: {
             'click .switch': '_onClick', 
-            'click .button.cancel': '_onCancelClick', 
+            'click button': '_onSaveClick', 
             'click .panel-menu>a': '_onMenuItemClick'
         },
         _onClick: function(event) {
@@ -87,19 +93,35 @@
         },
         updateModel: function(selectedDate) {
             this.model.set('programmingDate', selectedDate);
+            this.updateAffichage();
         },
+        updateAffichage : function (){
+            var d = new Date();
+            if (this.model.programmingDate && this.model.programmingDate != '') {
+                var parts = this.model.programmingDate.split('-');
+                d = new Date(parts[2], parts[1] - 1, parts[0]);
+            } else {
+                var datestring =d.getDate() + '-' + (d.getMonth() + 1) + '-' + d.getFullYear();
+                this.model.programmingDate = datestring;
+            }
+            
+            $("#datepicker" ).datepicker('setDate', this.model.programmingDate);             
+            this.$('.dateprog').html(d.toLocaleDateString(undefined, this.options));
+        },
         datePickerRender : function (){
             if (this._programmed ) {
+                this.model.publicationDate = null;
                 this.$('.forProgram').show();
                 this.$('.forPub').hide();
                 this.$('.labeloui').show();
                 this.$('.labelnon').hide();
                 this.switchprogram.parent().removeClass('disabled');
-                if (this.model.programmingDate!='') {
-                    $("#datepicker" ).datepicker('setDate', this.model.programmingDate);
-                }
+                this.updateAffichage();
             }
             else {
+                this.model.programmingDate = null;
+                var d = new Date();
+                this.model.publicationDate = d.getDate() + '-' + (d.getMonth() + 1) + '-' + d.getFullYear(); ;
                 this.switchprogram.parent().addClass('disabled');
                 this.$('.forProgram').hide();
                 this.$('.forPub').show();
@@ -107,6 +129,11 @@
                 this.$('.labelnon').show();
             }
         },
+        _onSaveClick: function(event) {
+            if (this.save)
+                this.save();
+            this.hide();
+        },
         save: function() {
             this.model.save();
         },
@@ -121,7 +148,7 @@
     Events.extend({
         PublishConfigViewEvents: {
             SAVE: 'save',
-            CANCEL: 'cancel'
+            CANCEL: 'cancel',
         }
     });
     return PublishConfigView;
Index: src/js/JEditor/NewsPanel/Views/SaveButton.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/SaveButton.js	(révision 12489)
+++ src/js/JEditor/NewsPanel/Views/SaveButton.js	(révision 12490)
@@ -114,6 +114,8 @@
                             this.listenToOnce(this.model, Events.BackboneEvents.ERROR, this.onError);
                             var currentArticles = this.model.collection.where({ news: this.model.news });
                             selected = this.model.getCategoryModel();
+                            this.model.set('publicationDate',null);
+                            this.model.set('programmingDate',null);
                             currentArticles.forEach(function(article) {
                                 if (!selected.lang || !selected.lang[article.lang]) {
                                     valid = false;
@@ -122,9 +124,10 @@
                                         message: translate("errorChangeCategoryTraduction", {'lang': article.lang})
                                     });
                                 }
+                                
                             });
                             if(valid) this.model.save();
-                           
+
                             return false;
                         }
                     });
Index: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12489)
+++ src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12490)
@@ -45,10 +45,10 @@
 	"newsTemplateLegend"   :   "Appliquez un modèle de page",
 
 	//config category
-	"configDesc" 			: "Ajustez les paramètres des Catégorie/articles",
+	"configDesc" 			: "Ajustez les paramètres des Catégorie",
 	"metaTitleLabel" 		: "Méta Title",
 	"metaDescLabel" 		: "Méta Description",
-	"urlLabel" 				: "URL de l'article/la catégorie",
+	"urlLabel" 				: "URL de la catégorie",
 	"cancel" 				: "Annuler",
 	"apply" 				: "Appliquer",
 
@@ -65,6 +65,8 @@
 	"deleteAction":"Suppression",
 	"confirmDelete": "Vous \u00eates sur le point de supprimer d\u00e9finitivement une page",
 	"none_result": "Auccune article correspond à la recherche",
+	"DeleteSuccesful" : "Supression avec succés",
+	"DeleteError" : " Erreur de la supression",
 
 
 	// zonetoolbox
@@ -96,7 +98,22 @@
 	 "translationSuccessfull": "Traduction de l'article avec success",
 	 "CategoryRequired":"Veuillez choisr une catégorie avant de sauvegarder",
 	 "RessourceRequired":"Veuillez choisr un image à la Une avant de sauvegarder", "errorChangeCategoryTraduction" : "Cet article à des traductions associées en <strong><% lang %></strong>. Pour changer la catégorie de l'article, veuillez vous assurer que la nouvelle catégorie existe bien dans les langues <strong><% lang %></strong>.",
+	 "configDescArt" : "Ajustez les paramètres des articles",
+	 "urlArtLabel" : "URL de l'article",
 
+	//publié
+	"publishtitre" : "Publication",
+	"publishDesc" : "Publiez maintenant ou programmez la publication de votre article à la date que vous souhaitez",
+	"programOption" : "Programmez l'envoi",
+	"programOptionDesc" : "Programmez une date de publication de l'article",
+	"oui" : "Oui",
+	"non" : "Non",
+	"willPublish" : "Votre article sera publié le",
+	"programPublish" : "Programmer la publication",
+	"publishImmediate" : "Votre articke sera publié imméfiatement",
+	"publishArticle" : "Publier l'article",
+	"dateFormat":"Y-m-d",
+
 	//available
 	"addContent":"Ajouter du contenu",
 	"availableDesc":"Glissez vos blocs sur l’emplacement souhaité ou cliquez sur un bloc pour l’ajouter en bas de page.",
Index: src/js/JEditor/NewsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12489)
+++ src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12490)
@@ -46,10 +46,10 @@
         "newsTemplateLegend"   :   "Appliquez un modèle de page",
 
         //config category
-        "configDesc" 			: "Ajustez les paramètres des Catégorie/articles",
+        "configDesc" 			: "Ajustez les paramètres des catégories",
         "metaTitleLabel" 		: "Méta Title",
         "metaDescLabel" 		: "Méta Description",
-        "urlLabel" 				: "URL de l'article/la catégorie",
+        "urlLabel" 				: "URL de la catégorie",
         "cancel" 				: "Annuler",
         "apply" 				: "Appliquer",
 
@@ -66,6 +66,8 @@
         "deleteAction":"Suppression",
         "confirmDeleteArticle": "Vous \u00eates sur le point de supprimer d\u00e9finitivement un article",
         "none_result": "Auccune article correspond à la recherche",
+        "DeleteSuccesful" : "Supression avec succés",
+        "DeleteError" : " Erreur de la supression",
 
         // zonetoolbox
         "publish"				: "Publier",
@@ -97,7 +99,22 @@
         "translationSuccessfull": "Traduction de l'article avec success",
         "CategoryRequired":"Veuillez choisr une catégorie avant de sauvegarder",
         "RessourceRequired":"Veuillez choisr un image à la Une avant de sauvegarder",
+        "configDescArt" : "Ajustez les paramètres des articles",
+        "urlArtLabel" : "URL de l'article",
 
+        //publié
+        "publishtitre" : "Publication",
+        "publishDesc" : "Publiez maintenant ou programmez la publication de votre article à la date que vous souhaitez",
+        "programOption" : "Programmez l'envoi",
+        "programOptionDesc" : "Programmez une date de publication de l'article",
+        "oui" : "Oui",
+        "non" : "Non",
+        "willPublish" : "Votre article sera publié le",
+        "programPublish" : "Programmer la publication",
+        "publishImmediate" : "Votre articke sera publié imméfiatement",
+        "publishArticle" : "Publier l'article",
+        "dateFormat":"Y-m-d",
+
         //available
         "addContent":"Ajouter du contenu",
         "availableDesc":"Glissez vos blocs sur l’emplacement souhaité ou cliquez sur un bloc pour l’ajouter en bas de page.",
Index: src/less/imports/news_panel/main.less
===================================================================
--- src/less/imports/news_panel/main.less	(révision 12489)
+++ src/less/imports/news_panel/main.less	(révision 12490)
@@ -1064,7 +1064,7 @@
 	}
 	button {
 		background-color: @newsColorLight;
-		color: #999;
+		color: #fff;
 		border-radius: 3px;
 		width: 100%;
 		padding: 14px;
