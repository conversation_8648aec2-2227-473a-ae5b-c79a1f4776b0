Revision: r12389
Date: 2024-06-11 11:05:09 +0300 (tlt 11 Jon 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: correction ajout Article dans un categorie si la langue n'existe pas

## Files changed

## Full metadata
------------------------------------------------------------------------
r12389 | srazanandralisoa | 2024-06-11 11:05:09 +0300 (tlt 11 Jon 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/AddArticleView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/CategorieAddView.js

News: correction ajout Article dans un categorie si la langue n'existe pas
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12388)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12389)
@@ -18,6 +18,8 @@
     "JEditor/App/Views/RightPanelView",
     "JEditor/NewsPanel/Views/AvailableView",
     "JEditor/NewsPanel/Views/PublishConfigView",
+    "JEditor/PagePanel/Contents/Zones/Versions/Views/VersionsCollectionView",
+
     "i18n!./nls/i18n",
     "jqueryPlugins/affix"
 ],
@@ -41,6 +43,7 @@
             RightPanelView,
             AvailableView,
             PublishConfigView,
+            VersionsCollectionView,
             translate
             ) {
             var NewsPanel = PanelView.extend(
@@ -309,6 +312,27 @@
                                 /**
                                  * Affiche le panel de droite en inserant les versions de la zone courante
                                  * 
+                                 */
+                                showZoneVersionsPanel: function(){
+                                    this.initVersionView();
+                                    this.renderRightPanel(this.childViews.versionsCollectionView, 'noRender');
+                                    this.rightPanelView.showContent(this.childViews.versionsCollectionView);
+                                    this.rightPanelView.showPanel();
+                                    return false;
+                                },
+                                /**
+                                 * Initialisation de la vue du panel de versions de zone
+                                 * 
+                                 */
+                                initVersionView: function() {
+                                    this.childViews.versionsCollectionView = new VersionsCollectionView({
+                                        pagePanel: this,
+                                        currentZoneModel: this.currentZone
+                                    } );
+                                },
+                                /**
+                                 * Affiche le panel de droite en inserant les versions de la zone courante
+                                 * 
                                 */
                                 showGlobalConfigView: function(){
                                     this.renderRightPanel(this.childViews.globalConfigView);
@@ -350,7 +374,14 @@
                                     this.childViews.newsEditorView.renderAddCategorie()
                                 },
                                 onAddArticleClick : function() {
+                                if(this.currentCategorie.lang[this.currentLang.id])
                                    this.childViews.newsEditorView.renderAddArticle(this.currentCategorie);
+                                else {
+                                    this.error({
+                                        title: translate("addArticle"),
+                                        message: translate("categoryLangNotFound")
+                                    });
+                                }
                                 },
                                 /**
                                  * ajoute les variables à l'objet this.dom
@@ -399,6 +430,7 @@
                                             this.childViews.categorieList.show();
                                         });
                                         this.childViews.categorieList.hide();
+                                        this._onCategorieChange();
                                     }
                                 },
                                 _onArticleSelect : function(view, article) {
Index: src/js/JEditor/NewsPanel/Views/AddArticleView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/AddArticleView.js	(révision 12388)
+++ src/js/JEditor/NewsPanel/Views/AddArticleView.js	(révision 12389)
@@ -129,6 +129,15 @@
             } 
 
         },
+
+        onError: function () {
+            this.stopListening(this.model, Events.BackboneEvents.SYNC, this.onSave);
+            this.error({
+                title: translate("saveAction"),
+                message: translate("saveError")
+            });
+        },
+
         onSave: function () {
             console.log("test");
             this.stopListening(this.model, Events.BackboneEvents.ERROR, this.onError);
@@ -150,7 +159,8 @@
         updateArticle:function(e) {
             e.preventDefault(); 
             e.stopImmediatePropagation();
-            
+            this.listenToOnce(this.model, Events.BackboneEvents.SYNC, this.onSave);
+            this.listenToOnce(this.model, Events.BackboneEvents.ERROR, this.onError);
             if (this._checkInput()) {
                 this.model.lang[this.lang.id] = this.currentArticleLang;
                 this.model.save();
@@ -179,6 +189,12 @@
                 introduction.addClass('error');
                 valid = false;
             }
+            if (this.model.ressource) {
+                this.error({
+                    title: translate("saveAction"),
+                    message: translate("RessourceRequired")
+                });
+            }
             return valid;
         },
       
@@ -185,12 +201,12 @@
         addArticle: function(e) {
             e.preventDefault(); 
             e.stopImmediatePropagation();
-            
+            this.listenToOnce(this.model, Events.BackboneEvents.SYNC, this.onSave);
+            this.listenToOnce(this.model, Events.BackboneEvents.ERROR, this.onError);
             if (this._checkInput()) {
                 this.model.save();
                 this.collection.add(this.model);
                 this.collection.trigger('change');
-                this.onSave();
                
             }
             return ;
Index: src/js/JEditor/NewsPanel/Views/CategorieAddView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(révision 12388)
+++ src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(révision 12389)
@@ -41,6 +41,9 @@
                 this.model = this.options.categorie
                 this.edit = true;
                 this.currentCategorieLang = this.model.getByLanguage(this.options.language);
+                if (!this.currentCategorieLang) {
+                    this.currentCategorieLang = new CategorieLang();
+                }
             } else{
                 this.model = new Categorie({
                     ressource: null,
@@ -154,11 +157,16 @@
             
 
         },
+        onError: function () {
+            this.stopListening(this.model, Events.BackboneEvents.SYNC, this.onSave);
+            this.error({
+                title: translate("saveAction"),
+                message: translate("saveError")
+            });
+        },
 
         onSave: function () {
-            console.log("test");
             this.stopListening(this.model, Events.BackboneEvents.ERROR, this.onError);
-            this.trigger(Events.LoadEvents.LOAD_SUCCESS, this);
             $.toast({
                 text: translate("saveSuccesful"), 
                 icon: 'icon-check-circle', 
@@ -177,7 +185,8 @@
         updateCategorie:function(e) {
             e.preventDefault(); 
             e.stopImmediatePropagation();
-            
+            this.listenToOnce(this.model, Events.BackboneEvents.SYNC, this.onSave);
+            this.listenToOnce(this.model, Events.BackboneEvents.ERROR, this.onError);
             if (this._checkInput()) {
                 this.model.lang[this.lang.id] = this.currentCategorieLang;
                 this.model.save();
@@ -208,7 +217,8 @@
         addCategorie: function(e) {
             e.preventDefault(); 
             e.stopImmediatePropagation();
-            
+            this.listenToOnce(this.model, Events.BackboneEvents.SYNC, this.onSave);
+            this.listenToOnce(this.model, Events.BackboneEvents.ERROR, this.onError);
             if (this._checkInput()) {
                 this.model.lang[this.lang.id] = this.currentCategorieLang;
                 this.model.save();
@@ -216,7 +226,6 @@
                 this.collection.add(this.model);
                 this.collection.trigger('change');
 
-                this.onSave();
                 this.trigger(Events.CategorieAddEvents.CATEGORY_ADD, this.model);
             }
             return ;
