Revision: r12520
Date: 2024-07-02 11:09:58 +0300 (tlt 02 Jol 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: ajout config IDEO_NEWS (partie JS)

## Files changed

## Full metadata
------------------------------------------------------------------------
r12520 | srazanandralisoa | 2024-07-02 11:09:58 +0300 (tlt 02 Jol 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/assets/ACLs/admin.json
   M /branches/ideo3_v2/integration/assets/ACLs/lpadmin.json
   M /branches/ideo3_v2/integration/assets/ACLs/root.json
   M /branches/ideo3_v2/integration/src/js/JEditor/App/Router.js
   M /branches/ideo3_v2/integration/src/js/JEditor/App/Router_save.js
   M /branches/ideo3_v2/integration/src/js/JEditor/App/Templates/application.html
   M /branches/ideo3_v2/integration/src/js/JEditor/DashBoard/Templates/DashBoardTpl.html

News: ajout config IDEO_NEWS (partie JS)
------------------------------------------------------------------------

## Diff
Index: assets/ACLs/admin.json
===================================================================
--- assets/ACLs/admin.json	(révision 12519)
+++ assets/ACLs/admin.json	(révision 12520)
@@ -208,7 +208,7 @@
     "comparison": null
  },
  "access_panel_news": {
-    "value": true,
+    "value": false,
     "comparison": "==="
  },
  "change_news_layout": {
Index: assets/ACLs/lpadmin.json
===================================================================
--- assets/ACLs/lpadmin.json	(révision 12519)
+++ assets/ACLs/lpadmin.json	(révision 12520)
@@ -208,7 +208,7 @@
         "comparison": null
      },
      "access_panel_news": {
-        "value": true,
+        "value": false,
         "comparison": "==="
      },
      "change_news_layout": {
Index: src/js/JEditor/App/Router.js
===================================================================
--- src/js/JEditor/App/Router.js	(révision 12519)
+++ src/js/JEditor/App/Router.js	(révision 12520)
@@ -29,7 +29,7 @@
         "marketingauto": __IDEO_MKTGAUTO__,
         "icom": __IDEO_ICOM3__,
         "restaurant": __IDEO_RESTAURANT__,
-        "news": __IDEO_ICOM3__,
+        "news": __IDEO_NEWS__,
       };
       for (var route in routes) {
         if (route_params[route] !== undefined) {
Index: src/js/JEditor/App/Router_save.js
===================================================================
--- src/js/JEditor/App/Router_save.js	(révision 12519)
+++ src/js/JEditor/App/Router_save.js	(révision 12520)
@@ -39,7 +39,7 @@
                 "marketingauto": __IDEO_MKTGAUTO__,
                 "icom": __IDEO_ICOM3__,
                 "restaurant": __IDEO_RESTAURANT__,
-                "news": __IDEO_ICOM3__,
+                "news": __IDEO_NEWS__,
             };
             if (!this.user.can("access_panel_" + name, route_params[name])) {
                 this.forbidden();
Index: src/js/JEditor/App/Templates/application.html
===================================================================
--- src/js/JEditor/App/Templates/application.html	(révision 12519)
+++ src/js/JEditor/App/Templates/application.html	(révision 12520)
@@ -7,7 +7,9 @@
         <ul>
             <li><a href="#" class="page active panel-link" data-target="pages"><span class="icon icon-file" ></span><span class="text"><%= __("Pages")%></span></a></li>
             <!-- add condition after-->
+            <% if(user.can('access_panel_news',__IDEO_NEWS__)){ %>
             <li><a href="#"  class="news panel-link" data-target="news"><span class="icon icon-file" ></span><span class="text"><%= __("News")%></span></a></li>
+            <% } %>
             <% if(user.can('access_panel_design')){ %>
             	<li><a href="#"  class="design panel-link" data-target="design"><span class="icon icon-design" ></span><span class="text"><%= __("Design")%></span></a></li>
             <% } %>
Index: src/js/JEditor/DashBoard/Templates/DashBoardTpl.html
===================================================================
--- src/js/JEditor/DashBoard/Templates/DashBoardTpl.html	(révision 12519)
+++ src/js/JEditor/DashBoard/Templates/DashBoardTpl.html	(révision 12520)
@@ -28,7 +28,9 @@
                     </span>
                 </a>
             </li>
-             <!-- add condition after-->
+            <% } %>
+            <!-- <% if(user.can('access_panel_news',__IDEO_NEWS__)){ %>
+            -->
             <li>
                 <a href="#news" class="design panel-link" data-target="news">
                     <span class="icon icon-file-doc"></span>
Index: assets/ACLs/root.json
===================================================================
--- assets/ACLs/root.json	(révision 12519)
+++ assets/ACLs/root.json	(révision 12520)
@@ -208,8 +208,8 @@
         "comparison": null
      },
      "access_panel_news": {
-        "value": true,
-        "comparison": null
+        "value": false,
+        "comparison": "==="
      },
      "change_news_layout": {
        "value": true,
