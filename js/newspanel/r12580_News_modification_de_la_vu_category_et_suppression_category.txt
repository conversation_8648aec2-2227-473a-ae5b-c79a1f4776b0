Revision: r12580
Date: 2024-07-10 16:29:58 +0300 (lrb 10 Jol 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: modification de la vu category et suppression category

## Files changed

## Full metadata
------------------------------------------------------------------------
r12580 | sraz<PERSON><PERSON><PERSON>oa | 2024-07-10 16:29:58 +0300 (lrb 10 Jol 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/categorieForm.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/categorieList.html
   A /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/categorieView.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/CategorieAddView.js

News: modification de la vu category et suppression category
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Templates/categorieForm.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/categorieForm.html	(révision 12579)
+++ src/js/JEditor/NewsPanel/Templates/categorieForm.html	(révision 12580)
@@ -28,9 +28,9 @@
             </div>
             </div>
         </div>
-        <div class="flex-button">
-            <button class="button annuler"> <%=__('cancel')%></button>
+        <div class="flex-button" data-cid="<%=cid%>">
             <% if(edit){ %>
+            <button class="button remove bg-red"> <span class="icon icon-bin"></span></button>
             <button class="button saveCat bg-blue"> <%=__('saveEdit')%></button>
             <% } else {  %> 
             <button class="button saveNewCat bg-blue">   <%=__('saveAdd')%> </button>
Index: src/js/JEditor/NewsPanel/Templates/categorieView.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/categorieView.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Templates/categorieView.html	(révision 12580)
@@ -0,0 +1,21 @@
+<div class="dis-table">
+    <div class="flex_categorie">
+        <% if (fileUrl) {%>
+            <img class="upload-categorie" style="object-fit: cover; display: block; opacity: 1;height: 200px;" src="<%=fileUrl%>">
+        <% } else {%>
+           <div class="none-image"><div></div></div>
+        <% }%>
+        <div class="categorie-info">
+            <div>
+                <div>
+                    <h2> <%=detail.title%></h3>
+                    <p> <%=detail.description%></p>
+                </div>
+                
+            </div>
+        </div>
+    </div>
+    <div class="flex-button">
+        <button class="button edit bg-blue"><span class="icon-edit"></span>&nbsp;<%=__('edit')%></button> 
+    </div>
+</div>
\ No newline at end of file

Property changes on: src/js/JEditor/NewsPanel/Templates/categorieView.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/NewsPanel/Templates/categorieList.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/categorieList.html	(révision 12579)
+++ src/js/JEditor/NewsPanel/Templates/categorieList.html	(révision 12580)
@@ -3,6 +3,14 @@
         <ul>
             <%_.each(content,function(categorie){ %>
                 <li class="categorie-nav-list <%= categorie==current?'edit':''%> ">
+                    <% if(categorie.id == 'uncategorized'){%>
+                        <% if(categorie.lang[currentlang].nbArticle > 0){%>
+                        <a data-cid="<%= categorie.cid %>" > <%= categorie.lang[currentlang].title %>
+                            <span class="action">
+                                <span class="number"><%= categorie.lang[currentlang].nbArticle %></span>
+                            </span>
+                        </a>
+                    <% } } else {%>
                     <a data-cid="<%= categorie.cid %>" > <%= categorie.lang[currentlang].title %>
                         <span class="action">
                             <span class="number"><%= categorie.lang[currentlang].nbArticle %></span>
@@ -10,7 +18,7 @@
                         </span>
                     </a>
                 </li>
-                <%
+                <% }
             });%>
         </ul>
     </nav>
Index: src/js/JEditor/NewsPanel/Views/CategorieAddView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(révision 12579)
+++ src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(révision 12580)
@@ -4,6 +4,7 @@
     "JEditor/Commons/Events",
     "JEditor/Commons/Ancestors/Views/BabblerView",
     "text!../Templates/categorieForm.html",
+    "text!../Templates/categorieView.html",
     "JEditor/FilePanel/Models/FileCollection",
     "JEditor/Commons/Files/Views/FileUploaderView",
     "JEditor/Commons/Languages/Views/LanguagesDropDown",
@@ -15,6 +16,7 @@
     Events,
     BabblerView, 
     template, 
+    categorieVu,
     FileCollection, 
     FileUploaderView, 
     LanguagesDropDown,
@@ -26,23 +28,27 @@
         events: {
          'click button.saveNewCat' : 'addCategorie',
          'click button.saveCat' : 'updateCategorie',
-         'click button.annuler' : 'cancel',
+         'click button.remove' : 'removeClick',
          'click .upload-categorie ' : 'onlyComputer',
          'change input[name="title"]': 'setTitle',
          'change textarea[name="description"]': 'setDescription',
          'click [data-language]': '_onLangClick',
+         'click button.edit' : 'changeView'
         },
+        isform : false,
         currentCategorieLang : null,
         initialize: function() {
             this.template = this.buildTemplate(template, translate);
+            this.vutemplate = this.buildTemplate(categorieVu, translate);
             this.edit = false;
+            this.isform = this.options.isform;
             this.lang = this.options.language;
             if (this.options.categorie) {
                 this.model = this.options.categorie
                 this.edit = true;
-                this.currentCategorieLang = this.model.getByLanguage(this.options.language);
-                if (!this.currentCategorieLang) {
-                    this.currentCategorieLang = new CategorieLang();
+                var currentCategorieLang = this.model.getByLanguage(this.lang);
+                if (!currentCategorieLang) {
+                    this.model.lang[this.lang.id] = new CategorieLang();
                 }
             } else{
                 this.model = new Categorie({
@@ -50,9 +56,8 @@
                     numberArticlesInCagetory:0,
                     lang : {}
                 });
-                this.currentCategorieLang = new CategorieLang();
+                this.model.lang[this.lang.id] = new CategorieLang();
             } 
-           // this.model.lang = this.options.language.id;
             this.langDropDown =  new LanguagesDropDown({
                 collection : this.options.languageList,
                 _default : this.options.language,
@@ -60,7 +65,6 @@
             });
 
             this.listenTo(this.langDropDown, Events.ChoiceEvents.SELECT, this._onLanguageChange);
-
             this.fileCollection = new FileCollection();
             this.translations = translate.translations;
         },
@@ -86,8 +90,7 @@
         _onLanguageChange: function(view, lang) {
             this.lang = lang;
             if (this.model.id) {
-                this.currentCategorieLang = (this.model.getByLanguage(lang))? this.model.getByLanguage(lang) : new CategorieLang();
-                this.trigger(Events.CategorieAddEvents.LANG_CHANGED, this.lang);
+                this.model.lang[this.lang.id] = (this.model.getByLanguage(lang))? this.model.getByLanguage(lang) : new CategorieLang();
                this.render(); 
             }
         },
@@ -96,44 +99,59 @@
         },
         setDescription: function(event) {
             var $target = $(event.currentTarget);
-            this.currentCategorieLang.description = $target.val();
+            this.model.lang[this.lang.id].description = $target.val();
         },
         setTitle: function(event) {
             var $target = $(event.currentTarget);
-            this.currentCategorieLang.title = $target.val();
+            this.model.lang[this.lang.id].title = $target.val();
         },
         render: function () {
-            params = {
-                edit :  this.edit,
-                detail : this.currentCategorieLang 
+           var currentCategorieLang = this.model.getByLanguage(this.options.language);
+            if(!this.isform){
+                var file = this.model.getFile();
+                params = {
+                    fileUrl :  (file)? file.fileUrl : '',
+                    detail : currentCategorieLang,
+                    cid : this.model.id
+                }
+                this.$el.html(this.vutemplate(params));
+                return this;
             }
-            this.$el.html(this.template(params));
-            this.$('.side-bar__lang').append(this.langDropDown.render().el);
+            else{
+                params = {
+                    edit :  this.edit,
+                    detail : currentCategorieLang,
+                    cid : this.model.id
+                }
+                this.$el.html(this.template(params));
             
-            this.fileUploader = new FileUploaderView({
-                customStockEvent: '_parsestock_image',
-                acceptedTypes: ['image'],
-                acceptedExtensions: ['jpeg', 'jpg', 'png', 'svg', 'webp'],
-                refusedExtensions: ['bmp'],
-                uploadUrl: __IDEO_UPLOADLOGO_URL__ ? __IDEO_UPLOADLOGO_URL__ : '/admin/resource-logo',
-                currentFile : this.model.getFile(),
-                collection: this.fileCollection
-            });
-           
-            this.listenTo(this.fileUploader, Events.FileUploaderEvents.UPLOAD, this._onUpload);
-            this.listenTo(this.fileUploader, Events.FileUploaderEvents.TOOBIG, this.onError);
-
-            if (this.model.ressource) {
-                this.fileUploader.currentFile = this.model.getFile();
-            }
+               if(this.edit) this.$('.side-bar__lang').append(this.langDropDown.render().el);
+                
+                this.fileUploader = new FileUploaderView({
+                    customStockEvent: '_parsestock_image',
+                    acceptedTypes: ['image'],
+                    acceptedExtensions: ['jpeg', 'jpg', 'png', 'svg', 'webp'],
+                    refusedExtensions: ['bmp'],
+                    uploadUrl: __IDEO_UPLOADLOGO_URL__ ? __IDEO_UPLOADLOGO_URL__ : '/admin/resource-logo',
+                    currentFile : this.model.getFile(),
+                    collection: this.fileCollection
+                });
             
-            this.$('.upload-categorie').append(this.fileUploader.el);
-			this.fileUploader.render();
-            this.proccessAttribute(); 
+                this.listenTo(this.fileUploader, Events.FileUploaderEvents.UPLOAD, this._onUpload);
+                this.listenTo(this.fileUploader, Events.FileUploaderEvents.TOOBIG, this.onError);
 
-            this.$('.upload-categorie .uploader .preview .imagepreview').after('<span style="font-size:9px;display:block;margin-top:35px;color: #32ace0;text-align:center;">'+this.translations.DefaultMessageUploaderLogo+'</span>');
+                if (this.model.ressource) {
+                    this.fileUploader.currentFile = this.model.getFile();
+                }
+                
+                this.$('.upload-categorie').append(this.fileUploader.el);
+                this.fileUploader.render();
+                this.proccessAttribute(); 
 
-            return this;
+                this.$('.upload-categorie .uploader .preview .imagepreview').after('<span style="font-size:9px;display:block;margin-top:35px;color: #32ace0;text-align:center;">'+this.translations.DefaultMessageUploaderLogo+'</span>');
+
+                return this;
+            }
         },
         
         _onUpload: function(file){
@@ -184,15 +202,45 @@
         updateCategorie:function(e) {
             e.preventDefault(); 
             e.stopImmediatePropagation();
-            this.listenToOnce(this.model, Events.BackboneEvents.SYNC, this.onSave);
-            this.listenToOnce(this.model, Events.BackboneEvents.ERROR, this.onError);
             if (this._checkInput()) {
-                this.model.lang[this.lang.id] = this.currentCategorieLang;
+                this.listenToOnce(this.model, Events.BackboneEvents.SYNC, this.onSave);
+                this.listenToOnce(this.model, Events.BackboneEvents.ERROR, this.onError);
                 this.model.save();
                 this.collection.add(this.model);
-                this.collection.trigger('change');
+                this.changeView();
             }
         },
+        changeView: function (){
+            this.isform = !this.isform;
+            this.render()
+        },
+        removeClick: function (event){
+            if (!this.app.user.can('delete_page'))
+            return false;
+            var $target = $(event.currentTarget);
+            var categoryId = $target.parent().data('cid');
+            var model = this.collection.get(categoryId);
+            if (!this.app.params.dontAskAgainFor['deletePage']) {
+                this.confirm({
+                message: translate('confirmDeleteCategory', {
+                    'name': model.title
+                }),
+                title: translate("deleteAction"),
+                type: 'delete',
+                onOk: _.bind(function() {
+                    model.destroy();
+                }, this),
+                options: {
+                    dialogClass: 'delete no-close',
+                    dontAskAgain: true,
+                    subject: 'deletePage'
+                }
+                });
+            } else {
+                model.destroy();
+            }
+            return false;
+        },
 
         /**
          * verication de notre input
@@ -215,19 +263,16 @@
         addCategorie: function(e) {
             e.preventDefault(); 
             e.stopImmediatePropagation();
-            this.listenToOnce(this.model, Events.BackboneEvents.SYNC, this.onSave);
-            this.listenToOnce(this.model, Events.BackboneEvents.ERROR, this.onError);
             if (this._checkInput()) {
-                this.model.lang[this.lang.id] = this.currentCategorieLang;
+                this.listenToOnce(this.model, Events.BackboneEvents.SYNC, this.onSave);
+                this.listenToOnce(this.model, Events.BackboneEvents.ERROR, this.onError);
                 this.model.save();
-
                 this.collection.add(this.model);
-                this.collection.trigger('change');
-
+                this.collection.sort();
                 this.trigger(Events.CategorieAddEvents.CATEGORY_ADD, this.model);
             }
             return ;
-        },
+        }
         
     });
     Events.extend(
