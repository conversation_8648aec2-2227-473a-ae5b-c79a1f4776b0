Revision: r12676
Date: 2024-08-01 10:55:13 +0300 (lkm 01 Aog 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: ajout meta pour accueil news(partie Js)

## Files changed

## Full metadata
------------------------------------------------------------------------
r12676 | srazanandralisoa | 2024-08-01 10:55:13 +0300 (lkm 01 Aog 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Models/NewsConfig.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/globalConfig.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/GlobalConfigView.js

News: ajout meta pour accueil news(partie Js)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Models/NewsConfig.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/NewsConfig.js	(révision 12675)
+++ src/js/JEditor/NewsPanel/Models/NewsConfig.js	(révision 12676)
@@ -13,7 +13,9 @@
                         newsFormat:"landscape", 
                         newsStyleAff: 1,
                         newsNbArticle: 3,
-                        layout: null
+                        layout: null,
+                        metaTitle:{},
+                        metaDescription:{}
                     },
                     constructor: function () {
                         if (arguments.callee.caller !== NewsConfig.getInstance)
@@ -32,17 +34,23 @@
                         this.set(this.lastState);
                     },
                     parse: function(data){
-                        return{
+                        if (!data.metaTitle || _.isArray(data.metaTitle))
+                            data.metaTitle = {};
+                        if (!data.metaDescription || _.isArray(data.metaDescription))
+                            data.metaDescription = {};
+                        return {
                             newsStyle: parseInt(data.newsStyle),                
                             newsFormat:data.newsFormat, 
                             newsStyleAff: parseInt(data.newsStyleAff),
                             newsNbArticle: parseInt(data.newsNbArticle),
-                            layout: data.layout
+                            layout: data.layout,
+                            metaTitle:data.metaTitle,
+                            metaDescription :  data.metaDescription
                         }
                     }
     
                 });
-            NewsConfig.SetAttributes(['newsStyle', 'newsFormat', 'newsStyleAff', 'newsNbArticle', 'layout']);
+            NewsConfig.SetAttributes(['newsStyle', 'newsFormat', 'newsStyleAff', 'newsNbArticle', 'layout','metaTitle','metaDescription']);
             /**
              * @static
              * @private
Index: src/js/JEditor/NewsPanel/Templates/globalConfig.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/globalConfig.html	(révision 12675)
+++ src/js/JEditor/NewsPanel/Templates/globalConfig.html	(révision 12676)
@@ -1,5 +1,19 @@
 
 <div class="gallery-template-option galleryStyle">
+    <div class="panel-content-content">
+        <label for="meta-title">
+            <span class="icon icon-html"></span>
+            <span class="label"><%=__('metaTitleLabel')%> </span>
+        </label>
+        <input type="text" name="metaTitle" value="<%=metaTitle%>"  id="meta-title">
+    </div>
+    <div class="panel-content-content">
+        <label for="meta-description">
+            <span class="icon icon-html"></span>
+            <span class="label" ><%=__('metaDescLabel')%></span>
+        </label>
+        <textarea name="metaDescription" id="meta-description" cols="20" rows="10"><%=metaDescription%></textarea>
+    </div>
     <% if(canchangeLayout){ %>
     <div class="news-option-margin news-template">
         <article class="panel-option">
Index: src/js/JEditor/NewsPanel/Views/GlobalConfigView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/GlobalConfigView.js	(révision 12675)
+++ src/js/JEditor/NewsPanel/Views/GlobalConfigView.js	(révision 12676)
@@ -29,6 +29,8 @@
             'click #Radioformatimage div .effect-radio'       : '_onChangeFormatImage',
             'slidechange .slider'   :   '_onSliderChange',
             'change input[type="radio"].select-box': '_onStyleAffichageChange',
+            'change input#meta-title': 'setMetaTitle',
+            'change textarea#meta-description': 'setMetaDesc',
       
         },
         _onChangeStylleImage : function(event){
@@ -71,13 +73,14 @@
                 this.newsPanel = this.options.newsPanel;
             delete this.options.newsPanel;
 
+            this.lang = this.options.language.id;
             this.model = NewsConfig.getInstance();
             this.pageCollection = PageCollection.getInstance();
 
-            var defaultLayout = this.pageCollection.findWhere({type:"template",lang:this.options.language.id, name:this.model.layout});
+            var defaultLayout = this.pageCollection.findWhere({type:"template",lang:  this.lang, name:this.model.layout});
 
             this.layoutsDropDown = new TemplateDropDown({collection: this.pageCollection,_default:defaultLayout, language:this.options.language});
-            this.layoutsDropDown.filter({type:'template',lang:this.options.language.id});
+            this.layoutsDropDown.filter({type:'template',lang: this.lang});
             this.model.layout = this.layoutsDropDown.current.name;
             this.listenTo(this.layoutsDropDown, Events.ChoiceEvents.SELECT, this.onLayoutSelect);
            
@@ -116,6 +119,8 @@
                 newsFormat:this.model.newsFormat,
                 newsNbArticle:this.model.newsNbArticle,
                 newsStyleAff:this.model.newsStyleAff,
+                metaTitle:this.model.metaTitle[this.lang],
+                metaDescription:this.model.metaDescription[this.lang],
                 canchangeLayout: this.app.user.can("change_news_layout")
             };
             var content = this.contenttemplate(templateVars)
@@ -128,6 +133,20 @@
             
             return this;
         },
+        setMetaTitle: function(e) {
+            var $target = $(e.currentTarget);
+            var title = $target.val();
+            if (this.lang) {
+                this.model.metaTitle[this.lang] = title;
+            }
+        },
+        setMetaDesc: function(e) {
+            var $target = $(e.currentTarget);
+            var desc = $target.val();
+            if (this.lang) {
+                this.model.metaDescription[this.lang] = desc;
+            }
+        },
         save: function() {
             this.model.save();
             this.newsPanel.hidesGlobalConfigView();
