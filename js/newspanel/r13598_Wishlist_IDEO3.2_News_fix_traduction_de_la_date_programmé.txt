Revision: r13598
Date: 2024-12-12 15:52:08 +0300 (lkm 12 Des 2024) 
Author: frahajanirina 

## Commit message
Wishlist:IDEO3.2:News:fix traduction de la date programmé

## Files changed

## Full metadata
------------------------------------------------------------------------
r13598 | frahajanirina | 2024-12-12 15:52:08 +0300 (lkm 12 Des 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/PublishConfigView.js

Wishlist:IDEO3.2:News:fix traduction de la date programmé
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Views/PublishConfigView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/PublishConfigView.js	(révision 13597)
+++ src/js/JEditor/NewsPanel/Views/PublishConfigView.js	(révision 13598)
@@ -125,8 +125,9 @@
                 this.model.programmingDate = datestring;
             }
             
-            $("#datepicker" ).datepicker('setDate', this.model.programmingDate);             
-            this.$('.dateprog').html(d.toLocaleDateString(undefined, this.options));
+            $("#datepicker" ).datepicker('setDate', this.model.programmingDate);
+            var lang = this.app.params.id;             
+            this.$('.dateprog').html(d.toLocaleDateString(lang.replace('_', '-'), this.options));
         },
         datePickerRender : function (){
             if (this._programmed ) {
