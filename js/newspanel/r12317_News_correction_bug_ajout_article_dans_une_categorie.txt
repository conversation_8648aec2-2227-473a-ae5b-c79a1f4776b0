Revision: r12317
Date: 2024-05-28 17:07:21 +0300 (tlt 28 Mey 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News correction bug ajout article dans une categorie

## Files changed

## Full metadata
------------------------------------------------------------------------
r12317 | sraz<PERSON><PERSON><PERSON>oa | 2024-05-28 17:07:21 +0300 (tlt 28 Mey 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/Article.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/ArticleEditorView.js

News correction bug ajout article dans une categorie
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Models/Article.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Article.js	(révision 12316)
+++ src/js/JEditor/NewsPanel/Models/Article.js	(révision 12317)
@@ -44,16 +44,18 @@
                             if (!category) category = this.get('category');
                             var categories = CategorieCollection.getInstance();
                     
-                            if (Array.isArray(category) && category.length > 0 ) {
-                                this.category = category.map(function(categoryId) {
-                                    if (!(categoryId instanceof Categorie)) {
-                                        return categories.findWhere({id: categoryId});
-                                    } else {
-                                        return categoryId;
-                                    }
-                                });
-                            } else if (!(category instanceof Categorie)) {
-                                this.category = categories.findWhere({id: category});
+                            if (category.length > 0 ) {
+                                if (Array.isArray(category) ) {
+                                    this.category = category.map(function(categoryId) {
+                                        if (!(categoryId instanceof Categorie)) {
+                                            return categories.findWhere({id: categoryId});
+                                        } else {
+                                            return categoryId;
+                                        }
+                                    });
+                                } else if (!(category instanceof Categorie)) {
+                                    this.category = categories.findWhere({id: category});
+                                }
                             }
                             return this.category;
                         },
Index: src/js/JEditor/NewsPanel/Views/ArticleEditorView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticleEditorView.js	(révision 12316)
+++ src/js/JEditor/NewsPanel/Views/ArticleEditorView.js	(révision 12317)
@@ -81,7 +81,7 @@
             } 
             this.articleDetail = new AddArticleView({
                 model: this.model,
-                category: this.model.category[0],
+                category: (this.model.category.length > 0)?this.model.category[0]: null,
                 language : this.options.language,
                 collection:  this.options.categorieCollection,
             });
