Revision: r14187
Date: 2025-04-30 15:36:18 +0300 (lrb 30 Apr 2025) 
Author: frahajanirina 

## Commit message
Wishlist:News:fixe suppression article lorsque user n'est pas linkeo

## Files changed

## Full metadata
------------------------------------------------------------------------
r14187 | frahajanirina | 2025-04-30 15:36:18 +0300 (lrb 30 Apr 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/CategorieAddView.js

Wishlist:News:fixe suppression article lorsque user n'est pas linkeo
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 14186)
+++ src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 14187)
@@ -90,9 +90,10 @@
       this.$('.switch, .globe-icon, .icon-edit, .icon-bin').each(function() {
         var articleId = $(this).closest('tr').data('cid');
         var article = self.collection.get(articleId);
-        var role = self.app.user.role;
+        var username = __IDEO_USER_NAME__,
+            codeBouton = __IDEO_SITE_CODE_BOUTON__;
         var client = article.content.client;
-        if (role === 'admin' && client == 'linkeo') {
+        if (username === codeBouton && client == 'linkeo') {
           $(this).parent().addClass('disabled');
           $(this).css('cursor', 'not-allowed');
         }
@@ -147,9 +148,10 @@
       var articleId = $target.parent().parent().data('cid');
       var $page = $target.parents('tr');
       var article = this.collection.get(articleId);
-      var role = this.app.user.role;
+      var username = __IDEO_USER_NAME__,
+          codeBouton = __IDEO_SITE_CODE_BOUTON__;
       var client = article.content.client;
-      if (role === 'admin' && client == 'linkeo') {
+      if (username === codeBouton && client == 'linkeo') {
         
         return false;
       }
@@ -169,9 +171,10 @@
         var $target = $(event.currentTarget);
         var articleId = $target.parent().parent().data('cid');
         var model = this.collection.get(articleId);
-        var role = this.app.user.role;
+        var username = __IDEO_USER_NAME__,
+            codeBouton = __IDEO_SITE_CODE_BOUTON__;
         var client = model.content.client;
-        if (role === 'admin' && client == 'linkeo') {
+        if (username === codeBouton && client == 'linkeo') {
           
           return false;
         }
@@ -210,9 +213,10 @@
       var $target = $(event.currentTarget);
       var articleId = $target.parent().parent().data('cid');
       var article = this.collection.get(articleId);
-      var role = this.app.user.role;
+      var username = __IDEO_USER_NAME__,
+          codeBouton = __IDEO_SITE_CODE_BOUTON__;
       var client = article.content.client;
-      if (role === 'admin' && client == 'linkeo') {
+      if (username === codeBouton && client == 'linkeo') {
         
         return false;
       }
Index: src/js/JEditor/NewsPanel/Views/CategorieAddView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(révision 14186)
+++ src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(révision 14187)
@@ -156,8 +156,9 @@
 
                 var isArticleAuthorNotLinkeo = this.model.lang[this.lang.id].isArticleAuthorNotLinkeo;
                 var nbArticle = this.model.lang[this.lang.id].nbArticle;
-                var role = this.app.user.role;
-                if (role === 'admin' && nbArticle > 0 && isArticleAuthorNotLinkeo === false) {
+                var username = __IDEO_USER_NAME__,
+                    codeBouton = __IDEO_SITE_CODE_BOUTON__;
+                if (username === codeBouton && nbArticle > 0 && isArticleAuthorNotLinkeo === false) {
                     $('button.remove').css('cursor', 'not-allowed');
                 }
                 
@@ -235,8 +236,9 @@
         removeClick: function (event){
             var isArticleAuthorNotLinkeo = this.model.lang[this.lang.id].isArticleAuthorNotLinkeo;
             var nbArticle = this.model.lang[this.lang.id].nbArticle;
-            var role = this.app.user.role;
-            if (role === 'admin' && nbArticle > 0 && isArticleAuthorNotLinkeo === false) {
+            var username = __IDEO_USER_NAME__,
+                codeBouton = __IDEO_SITE_CODE_BOUTON__;
+            if (username === codeBouton && nbArticle > 0 && isArticleAuthorNotLinkeo === false) {
                 
                 return isArticleAuthorNotLinkeo;
             }
