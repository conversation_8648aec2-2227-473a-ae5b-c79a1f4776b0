Revision: r12307
Date: 2024-05-16 17:14:56 +0300 (lkm 16 Mey 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
 News: modification template add article

## Files changed

## Full metadata
------------------------------------------------------------------------
r12307 | srazanandralisoa | 2024-05-16 17:14:56 +0300 (lkm 16 Mey 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/addArticle.html

 News: modification template add article
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Templates/addArticle.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/addArticle.html	(révision 12306)
+++ src/js/JEditor/NewsPanel/Templates/addArticle.html	(révision 12307)
@@ -1,39 +1,35 @@
-<div class="main">
-    <div class="dis-table bordered">
-        <div class="flex_categorie">
-            <div class="upload-categorie">
-                <!-- uploader -->
+<div class="flex_categorie">
+    <div class="upload-article">
+        <!-- uploader -->
+    </div>
+    <div class="article-info">
+
+            <div> 
+                <label>Catégorie de l'article</label>
             </div>
-            <div class="categorie-info">
-                <div class="flex-betwen">
-                    <div> 
-                        <h3> Créer une catégorie</h3>
-                    </div>
-                    <!-- Choix de la langue -->
-                    <div class="side-bar__lang bg-blue">
+            <!-- Choix de la catégorie -->
+            <div class="category-dropdown">
 
-                    </div>
-                </div> 
-            <div>
-                <div>
-                    <label>Label :</label> <br>
-                    <input class="field-input" name="label" value="" type="text" placeholder="Label">
+            </div>
+        
+    <div>
+        <div>
+            <label> <%=__('titleArticle')%> :</label> <br>
+            <input class="cat-input neutral-input  bold" value="<%=detail.title%>" name="title" type="text" placeholder="Label">
 
-                </div>
-                <div>
-                    <label> Description :</label><br>
-                    <textarea name="description" placeholder="Description" class="field-input field-description"></textarea>
 
-                </div>
-            </div>
-            </div>
         </div>
-        <div class="flex-button">
-            <button type="button"> annuler</button>
-            <button type="button bg-blue"> créer la categorie</button>
-            <!-- <button type="button"> Enregistrer</button> -->
-    
+        <div>
+            <label> <%=__('descriArticle')%> :</label><br>
+            <textarea name="introduction" placeholder="Introduction" class="cat-input neutral-input  bold" ><%=detail.introduction%></textarea>
+
         </div>
     </div>
-    
+    <% if(create){ %>
+        <div class="flex-button">
+        <button class="button annuler"> <%=__('cancel')%></button>
+        <button class="button saveNewArticle bg-blue">   <%=__('saveAdd')%> </button>
+    <% } %>  
+    </div>
+    </div>
 </div>
\ No newline at end of file
