Revision: r12586
Date: 2024-07-11 08:09:08 +0300 (lkm 11 Jol 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: correction du bug availableview

## Files changed

## Full metadata
------------------------------------------------------------------------
r12586 | srazanandralisoa | 2024-07-11 08:09:08 +0300 (lkm 11 Jol 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/PagePanel.js

News: correction du bug availableview
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12585)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12586)
@@ -21,7 +21,7 @@
     "JEditor/NewsPanel/Views/AvailableView",
     "JEditor/NewsPanel/Views/PublishConfigView",
     "JEditor/NewsPanel/Articles/Versions/Views/VersionsCollectionView",
-
+    "JEditor/PagePanel/Contents/Blocks/Blocks",
     "i18n!./nls/i18n",
     "jqueryPlugins/affix"
 ],
@@ -48,6 +48,7 @@
             AvailableView,
             PublishConfigView,
             VersionsCollectionView,
+            Blocks,
             translate
             ) {
             var NewsPanel = PanelView.extend(
Index: src/js/JEditor/PagePanel/PagePanel.js
===================================================================
--- src/js/JEditor/PagePanel/PagePanel.js	(révision 12585)
+++ src/js/JEditor/PagePanel/PagePanel.js	(révision 12586)
@@ -17,10 +17,11 @@
 		"collection!JEditor/PagePanel/ContentModel/ContentModelCollection",
         "JEditor/PagePanel/Contents/Zones/Versions/Views/VersionsCollectionView",
 		"JEditor/PagePanel/Views/PageLpManagerView",
+		"JEditor/PagePanel/Contents/Blocks/Blocks",
 		"i18n!./nls/i18n",
 		// not in params
 		"owlCarousel",
-		"jqueryPlugins/affix" ], function($, _, pagePanel, Events, PanelView, RightPanelView,  PageCollectionView, PageCollection, PageSupportCollection, PageListManagerView, AvailableView, AddPageView, LayoutCollection, PageView, LanguagesDropDown, ContentModelCollection, VersionsCollectionView, PageLpManagerView, translate) {
+		"jqueryPlugins/affix" ], function($, _, pagePanel, Events, PanelView, RightPanelView,  PageCollectionView, PageCollection, PageSupportCollection, PageListManagerView, AvailableView, AddPageView, LayoutCollection, PageView, LanguagesDropDown, ContentModelCollection, VersionsCollectionView, PageLpManagerView, Blocks, translate) {
 	 /**
 		 * not main Vue du Panneau de page, gère les vues de page (nom, etc),
 		 * langues, panneaux latéraux
