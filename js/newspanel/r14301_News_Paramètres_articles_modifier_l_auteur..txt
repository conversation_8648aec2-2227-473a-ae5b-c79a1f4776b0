Revision: r14301
Date: 2025-06-02 11:22:41 +0300 (lts 02 Jon 2025) 
Author: frahajanirina 

## Commit message
News:Paramètres articles, modifier l'auteur.

## Files changed

## Full metadata
------------------------------------------------------------------------
r14301 | frahajanirina | 2025-06-02 11:22:41 +0300 (lts 02 Jon 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Models/Article.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/configArticle.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/ConfigArticleView.js

News:Paramètres articles, modifier l'auteur.
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Templates/configArticle.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/configArticle.html	(révision 14300)
+++ src/js/JEditor/NewsPanel/Templates/configArticle.html	(révision 14301)
@@ -20,7 +20,7 @@
     </label>
     <p><a href="//<%=url%>" target="_blank"><%=urltext%></a></p>
 </div>
-<% if(userRole == 'root'){%>
+<% if(isNotClient){%>
     <br>
     <div class="panel-content-content">
         <label for="author">
Index: src/js/JEditor/NewsPanel/Views/ConfigArticleView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ConfigArticleView.js	(révision 14300)
+++ src/js/JEditor/NewsPanel/Views/ConfigArticleView.js	(révision 14301)
@@ -56,7 +56,7 @@
                 metaDescription: this.model.metaDescription,
                 client: this.model.content.client,
                 enseigne: this.model.enseigne,
-                userRole: this.model.userRole
+                isNotClient: (__IDEO_USER_NAME__ !== __IDEO_SITE_CODE_BOUTON__) ? true : false 
             }
             var content = this.contenttemplate(params)
             this.addContent(content);
Index: src/js/JEditor/NewsPanel/Models/Article.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Article.js	(révision 14300)
+++ src/js/JEditor/NewsPanel/Models/Article.js	(révision 14301)
@@ -32,7 +32,6 @@
                             content:null,
                             numberOfTranslation:0,
                             enseigne: null,
-                            userRole: null,
                             desc: null,
                             langAltRessource: null
                         },
@@ -151,7 +150,7 @@
                         }
                     });
                    
-            Article.SetAttributes(['category', 'state', 'numberOfTranslation','title', 'lang','ressource','introduction', 'publicationDate','programmingDate', 'metaTitle', 'metaOpengraph', "page", "content","news", "enseigne", "userRole", "desc", "langAltRessource"]);
+            Article.SetAttributes(['category', 'state', 'numberOfTranslation','title', 'lang','ressource','introduction', 'publicationDate','programmingDate', 'metaTitle', 'metaOpengraph', "page", "content","news", "enseigne", "desc", "langAltRessource"]);
             Events.extend(
                 {
                     ArticleEvents : {
