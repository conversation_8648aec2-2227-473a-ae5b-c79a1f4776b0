Revision: r13959
Date: 2025-03-12 13:12:21 +0300 (lrb 12 Mar 2025) 
Author: frahajanirina 

## Commit message
Wishlist:NEWS:fix popup en boucle

## Files changed

## Full metadata
------------------------------------------------------------------------
r13959 | frahajanirina | 2025-03-12 13:12:21 +0300 (lrb 12 Mar 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/NewsPanel.js

Wishlist:NEWS:fix popup en boucle
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 13958)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 13959)
@@ -803,6 +803,11 @@
                                     this.rightPanelView.hidePanel();
                                 },
                                 showModalProgram :function (){
+                                    if (this.isUpdating) {
+
+                                        return;
+                                    }
+                                    this.isUpdating = true;
                                     var article = this.currentArticle;
                                     article.set('publicationDate',null);
                                     this.confirm({
@@ -810,7 +815,9 @@
                                         title: translate("majpublication"),
                                         type: 'update',
                                         onOk: _.bind(function() {
-                                             article.save()
+                                            article.save().always(_.bind(function() {
+                                                this.isUpdating = false;
+                                            }, this));
                                           }, this),
                                         options: {dialogClass: 'delete no-close'}
                                     });
