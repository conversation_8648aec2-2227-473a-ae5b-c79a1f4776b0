Revision: r13711
Date: 2025-01-15 12:13:07 +0300 (lrb 15 Jan 2025) 
Author: frahajanirina 

## Commit message
Wishlist:IDEO3.2:News : Modifier la traduction de la date lors de la sélection de la date de publication de l'article (article programmé)

## Files changed

## Full metadata
------------------------------------------------------------------------
r13711 | frahajanirina | 2025-01-15 12:13:07 +0300 (lrb 15 Jan 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/PublishConfigView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/i18n.js

Wishlist:IDEO3.2:News : Modifier la traduction de la date lors de la sélection de la date de publication de l'article (article programmé)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Views/PublishConfigView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/PublishConfigView.js	(révision 13710)
+++ src/js/JEditor/NewsPanel/Views/PublishConfigView.js	(révision 13711)
@@ -102,9 +102,12 @@
             var params = this.model
             this.$el.html(this.template(params));
             this.$('#datepicker').datepicker({
+                monthNames: translate('month'),
+                dayNames: translate('day'),
+                dayNamesMin: translate('dayMin'),
+                firstDay: 1, // Lundi comme premier jour de la semaine
                 dateFormat: 'yy-mm-dd', // Format de la date
                 onSelect: this.updateModel.bind(this),
-                language: 'fr'
             });
             this.switchprogram = this.$(".switch");
             this.datePickerRender();
Index: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 13710)
+++ src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 13711)
@@ -168,4 +168,7 @@
 	"program": "Programmé",
 	"open": "Ouvrir",
 	"changeAuthor": "Changer l'auteur",
+	"month": ["Janvier", "Février", "Mars", "Avril", "Mai", "Juin", "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"],
+	"day": ["Dimanche", "Lundi", "Mardi", "Mercredi", "Jeudi", "Vendredi", "Samedi"],
+	"dayMin": ["Di", "Lu", "Ma", "Me", "Je", "Ve", "Sa"]
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 13710)
+++ src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 13711)
@@ -168,4 +168,7 @@
 	"program": "Programmé",
 	"open": "Ouvrir",
 	"changeAuthor": "Changer l'auteur",
+	"month": ["Janvier", "Février", "Mars", "Avril", "Mai", "Juin", "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"],
+	"day": ["Dimanche", "Lundi", "Mardi", "Mercredi", "Jeudi", "Vendredi", "Samedi"],
+	"dayMin": ["Di", "Lu", "Ma", "Me", "Je", "Ve", "Sa"]
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/i18n.js	(révision 13710)
+++ src/js/JEditor/NewsPanel/nls/i18n.js	(révision 13711)
@@ -165,6 +165,9 @@
         "program": "Program",
         "open": "Open",
         "changeAuthor": "Change author",
+        "month": ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"],
+        "day": ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"],
+        "dayMin": ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"]
     },
     "fr-fr": true,
     "fr-ca": true
