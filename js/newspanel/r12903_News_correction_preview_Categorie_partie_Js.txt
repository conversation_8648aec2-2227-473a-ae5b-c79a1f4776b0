Revision: r12903
Date: 2024-08-22 16:41:03 +0300 (lkm 22 Aog 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: correction preview Categorie partie Js

## Files changed

## Full metadata
------------------------------------------------------------------------
r12903 | sraz<PERSON><PERSON><PERSON>oa | 2024-08-22 16:41:03 +0300 (lkm 22 Aog 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/PagePreview.js

News: correction preview Categorie partie Js
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Views/PagePreview.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/PagePreview.js	(révision 12902)
+++ src/js/JEditor/NewsPanel/Views/PagePreview.js	(révision 12903)
@@ -103,7 +103,9 @@
                     this.render();
                     this._super();
                     this.scale();
-                    this.dom[this.cid].pageInput.val(JSON.stringify(content.toJSON()));
+                    var content = content.toJSON();
+                    content.currentLang = this.app.currentPanel.currentLang.id;
+                    this.dom[this.cid].pageInput.val(JSON.stringify(content));
                     this.dom[this.cid].typeInput.val(type.trim()); 
                     this.dom[this.cid].form.attr('target', 'preview');
                     this.dom[this.cid].form.submit();
