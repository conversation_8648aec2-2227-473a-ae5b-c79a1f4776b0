Revision: r12306
Date: 2024-05-16 16:57:32 +0300 (lkm 16 Mey 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
NEWS : tableau avec filtre pour l'article, creation et edition article (à suivre)

## Files changed

## Full metadata
------------------------------------------------------------------------
r12306 | srazanandralisoa | 2024-05-16 16:57:32 +0300 (lkm 16 Mey 2024) | 1 ligne
Chemins modifiés :
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Models
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Templates
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Templates/ZoneToolbox.html
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Templates/articleTitleField.html
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Templates/publishConfig.html
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Views
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Views/ArticleTitleField.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Views/PublishConfigView.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Views/ZoneToolBox.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/Article.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/ArticlesCollection.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/NewsConfig.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/NewsPanel.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/ZoneToolbox.html
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/articleEditor.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/articlesTable.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/newsEditor.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/AddArticleView.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/ArticleEditorView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/ConfigCategorieView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/NewsEditorView.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/ZoneToolBox.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/i18n.js
   M /branches/ideo3_v2/integration_news/src/js/build.js
   M /branches/ideo3_v2/integration_news/src/js/config.js
   M /branches/ideo3_v2/integration_news/src/less/imports/news_panel/main.less

NEWS : tableau avec filtre pour l'article, creation et edition article (à suivre)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Articles/Templates/ZoneToolbox.html
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Templates/ZoneToolbox.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Articles/Templates/ZoneToolbox.html	(révision 12306)
@@ -0,0 +1,5 @@
+<span class="label"><%=__("zoneToolboxLabel")%></span>
+<div class="btn-group <%=className%>">
+    <button type="button" class="btn btn-default template-link-indicator <%=zone.customized?'clickable':''%>"><span class="icon <%= (zone.customized?'icon-layout-broken':'icon-layout-ok')%>"></span><% if(zone.customized){%><span class="label"><%=__('backToTemplate')%></span><% } %></button>
+    <button type="button" class="btn btn-default add-content" id="available-blocks-trigger"><span class="icon icon-add"></span><span class="label"><%=__("addContent")%></span></button>
+</div>
Index: src/js/JEditor/NewsPanel/Articles/Templates/articleTitleField.html
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Templates/articleTitleField.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Articles/Templates/articleTitleField.html	(révision 12306)
@@ -0,0 +1,7 @@
+<i class="icon-lock" style="display:none;"></i>
+<i class="icon-unlock" style="display:none;"></i>
+<span class="text page-name <%=className%>">
+    <span class="content" contenteditable="<%=canSetPageTitle%>"><%= page.name %></span> <%  if(page.attributes.accueil !== 7){ %> <i class="icon-edit icon"></i><% } %> 
+</span><!--
+--><span class="inherit-from">(<%=pageTemplate.name%>)</span>
+<span class="inherit-from"><a href="//<%= page.attributes.base_url %>"target="_blank"><%= __("urlText")%></a></span>

Property changes on: src/js/JEditor/NewsPanel/Articles/Templates/articleTitleField.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/NewsPanel/Templates/articleEditor.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/articleEditor.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Templates/articleEditor.html	(révision 12306)
@@ -0,0 +1,7 @@
+
+<div class="dis-table bordered">
+    
+</div>
+<div class="content-zone">
+
+</div>
Index: src/js/JEditor/NewsPanel/Views/ConfigCategorieView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ConfigCategorieView.js	(révision 12305)
+++ src/js/JEditor/NewsPanel/Views/ConfigCategorieView.js	(révision 12306)
@@ -106,7 +106,7 @@
         
     });
     Events.extend({
-        ConfigCategorieVIewEvents: {
+        ConfigCategorieViewEvents: {
             SAVE: 'save',
             CANCEL: 'cancel'
         }
Index: src/js/build.js
===================================================================
--- src/js/build.js	(révision 12305)
+++ src/js/build.js	(révision 12306)
@@ -134,6 +134,10 @@
         {
             name: 'JEditor/LogsPanel/LogsPanel',
             exclude: ['main', 'JEditor/Commons']
+        },
+        {
+            name: 'JEditor/NewsPanel/NewsPanel',
+            exclude: ['main', 'JEditor/Commons']
         }
     ],
     removedCombined: true,
Index: src/js/config.js
===================================================================
--- src/js/config.js	(révision 12305)
+++ src/js/config.js	(révision 12306)
@@ -122,6 +122,10 @@
         {
             name: 'JEditor/LogsPanel/LogsPanel',
             exclude: ['main', 'JEditor/Commons']
+        },
+        {
+            name: 'JEditor/NewsPanel/NewsPanel',
+            exclude: ['main', 'JEditor/Commons']
         }
     ],
     removedCombined: true,
Index: src/js/JEditor/NewsPanel/Articles/Templates/publishConfig.html
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Templates/publishConfig.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Articles/Templates/publishConfig.html	(révision 12306)
@@ -0,0 +1,38 @@
+<div id="meta-configuration" class="meta-configuration scrollbar-classic">
+    <header class="panel-head">
+        <span class="icon icon-params"></span>
+        <h1 class="panel-name">Publication</h1>
+    </header>
+    <div class="panel-content active">
+        <div class="panel-content-intro">
+            Publiez maintenant ou programmez ka publication de votre article à la date que vous souhaitez
+        </div>
+    </div>
+    <div class="option-content ">
+       <div for="meta-title">
+        <span class="icon icon-html"></span>
+        <span>Programmez l'envoi</span>
+       </div>
+        <p> Programmez une date de publication de l'article</p>
+      <!-- toogle -->
+      <!-- datepicker -->
+      <div class="batch">
+        <p>
+            <a href="#">
+                <span class="switch batchActions">
+                    <span></span>
+                </span>
+                Oui / nom
+            </a>
+        </p>
+        <div class="btn-group-batch">
+            <div class="block-datepicker">
+                <label for="dateprog"><%= __("dateProg")%></label> <br>
+                <input class="datepicker  custom-input from" name="dateProg" type="text" placeholder="<%= __("datePlaceholder")%>">
+            </div>
+        </div>
+        <div>Votre article sera publié immédiatement</div>
+
+      </div>
+    </div>
+</div>
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Articles/Views/ArticleTitleField.js
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Views/ArticleTitleField.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Articles/Views/ArticleTitleField.js	(révision 12306)
@@ -0,0 +1,74 @@
+define([
+	"jquery",
+	"text!../Templates/articleTitleField.html",
+	"JEditor/Commons/Ancestors/Views/View",
+    "collection!JEditor/Commons/Pages/Models/PageCollection",
+    "i18n!../nls/i18n"
+],function(	$,
+	pageTitleField,
+	View,
+    PageCollection,
+    translate
+){
+var ArticleTitleField = View.extend({
+    tagName: 'div',
+    events: {
+        'blur .content': '_setName',
+        'click .icon-unlock,.icon-lock': '_toggleLock', 
+        'focus .content': '_onFocus', 
+        'keydown .content': '_checkEnter'
+    },
+    attributes: function() {
+        var attrs = {class:'title '};
+        attrs.class += this.model.locked ? 'locked' : 'unlocked';
+        return attrs;
+    },
+    _checkEnter: function(e) {
+        if (e.keyCode === 13) {
+            $(e.currentTarget).blur();
+            return false;
+        }
+    },
+    _toggleLock: function() {
+        this.model.locked = !this.model.locked;
+        if (this.options.autoSave)
+            this.model.save();
+    },
+    _onFocus: function(event) {
+        var $target = $(event.currentTarget);
+        $target.parent().addClass('active');
+        this.nameValue = $target.text();
+    },
+    initialize: function() {
+        this._template = this.buildTemplate(pageTitleField, translate);
+        this.nameValue = this.model.name;
+        this.render();
+    },
+    render: function() {
+        var template = PageCollection.getInstance().findWhere({type:"template",layout:this.model.layout});
+        this.undelegateEvents();
+        this.$el.empty();
+        var idTypePage=this.model.attributes.accueil;
+        var canSetPageTitle=(idTypePage===7)?"false":(this.app.user.can("set_page_name")?"true":"false");
+        this.$el.html(this._template({page: this.model,pageTemplate:template,canSetPageTitle:canSetPageTitle,className:this.app.user.can("set_page_name")?"editable":""}));
+        this.delegateEvents();
+        return this;
+    },
+    _setName: function(event) {
+        var $target = $(event.currentTarget);
+        $target.parent().removeClass('active');
+        //fix ellipsis bug for long text
+        $target.scrollLeft( 0 );
+        if (this.nameValue !== $target.text() && $target.text().trim() !== '') {
+            this.model.name = $target.text();
+            this.nameValue = this.model.name;
+            if (this.options.autoSave)
+                this.model.save();
+        }
+        else
+            $target.text(this.nameValue);
+    }
+
+});
+return ArticleTitleField;
+});

Property changes on: src/js/JEditor/NewsPanel/Articles/Views/ArticleTitleField.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/NewsPanel/Articles/Views/PublishConfigView.js
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Views/PublishConfigView.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Articles/Views/PublishConfigView.js	(révision 12306)
@@ -0,0 +1,119 @@
+define([
+    "jquery",
+	"underscore",
+	"JEditor/Commons/Events",
+	"JEditor/Commons/Ancestors/Views/View",
+    "text!JEditor/NewsPanel/Articles/Templates/publishConfig.html",
+    "JEditor/App/Views/RightPanelView",
+    "i18n!JEditor/NewsPanel/nls/i18n",
+], function ($, _, Events, View, template, RightPanelView, translate) {
+    
+    /**
+     * 
+     * @type PublishConfigView
+     */
+    var PublishConfigView = View.extend({
+
+        tagName: 'div',
+        className: 'right-panel-nav',
+        _transitionDelay: 250,
+        rendered: false,
+        _currentCid: null,
+        _programmed:false,
+        events: {
+            'click .batch': '_onClick', 
+            'click .button.cancel': '_onCancelClick', 
+            'click .panel-menu>a': '_onMenuItemClick'
+        },
+        _onClick: function(e) {
+            this.switchprogram.toggleClass('active');
+            this._programmed = !this._programmed;
+            return false;
+          },
+        constructor: function(options) {
+            if (!options.rightPanelView || !(options.rightPanelView instanceof RightPanelView))
+                throw new TypeError("le constructeur attend un argument option.rightPanelView de type RightPanelView"); 
+            this.rightPanelView = options.rightPanelView;
+            View.apply(this, arguments);
+        },
+        initialize: function () {
+            this.template = this.buildTemplate(template, translate);
+            this.listenTo(this.rightPanelView, 'overlayClick', this.cancel);
+            this.rightPanelView.addContent(this);
+            this.currentLang = this.options.currentLang;
+            this.switchprogram = this.$('.batch');
+            return this;
+        },
+         /**
+         * affiche cette reglage
+         */
+         show: function() {
+            if (this.rendered === false)
+                this.render();
+            this.rightPanelView.showPanel();
+            this._super();
+        },
+        /**
+         * cache cette reglage
+         */
+        hide: function(hidePanel) {
+            hidePanel = hidePanel !== false ? true : false;
+            if (this.rendered === false)
+                this.render();
+            if (hidePanel && this.rightPanelView.isVisible())
+                this.rightPanelView.hidePanel();
+            this._super();
+        },
+        /**
+         * Rendu de la view
+         */
+        render: function () {
+            this.undelegateEvents();
+            if (!this.model.lang[this.options.currentLang.id]) {
+               return false;
+            }
+            var params = this.model.lang[this.options.currentLang.id]
+            this.$el.html(this.template(params));
+            this.$('.datepicker').datepicker({
+                dateFormat: "dd/mm/yy",
+                onClose: function (dateText) {
+                    if (dateText) {
+                        $(this).next().show(0);
+                    } else {
+                        $(this).next().hide(0);
+                    }
+                }
+            });
+            this.$el.toggleClass(); 
+            this.datePickerRender();
+            this.delegateEvents();
+            return this;
+        },
+        datePickerRender : function (){
+            if (this._programmed ) {
+                this.$('.block-datepicker').show();
+                if (this.model.dateprog!='') {
+                    $(".datepicker" ).datepicker('setDate', this.model.dateprog);
+                }
+            }
+            else this.$('.block-datepicker').hide();
+        },
+        save: function() {
+            this.model.save();
+        },
+        /**
+         * annule les changements éfectuées depuis l'ouverture
+         */
+        cancel: function() {
+           this.model.cancel();
+        }
+        
+    });
+    Events.extend({
+        PublishConfigViewEvents: {
+            SAVE: 'save',
+            CANCEL: 'cancel'
+        }
+    });
+    return PublishConfigView;
+});
Index: src/js/JEditor/NewsPanel/Articles/Views/ZoneToolBox.js
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Views/ZoneToolBox.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Articles/Views/ZoneToolBox.js	(révision 12306)
@@ -0,0 +1,86 @@
+define([
+    "JEditor/Commons/Ancestors/Views/BabblerView",
+    "JEditor/Commons/Events",
+    "JEditor/PagePanel/Contents/Zones/Views/SaveButton",
+    "JEditor/PagePanel/Contents/Zones/Views/SwitchVersionButton",
+    "text!../Templates/ZoneToolbox.html",
+    "i18n!../nls/i18n"
+],function(
+    BabblerView,
+    Events,
+    SaveButton,
+    SwitchVersionButton,
+    template,
+    translate
+    ) {
+    var ZoneToolbox = BabblerView.extend({
+        className: "zone-toolbox",
+        events: {'click .template-link-indicator.clickable': 'uncustomize'},
+        initialize: function () {
+            this._super();
+            this.childViews = {
+                saveButton: new SaveButton({model: this.model}),
+                switchVersionButton: new SwitchVersionButton({model: this.model})
+            };
+            this._template = this.buildTemplate(template, translate);
+        },
+        remove: function () {
+            for( var view in this.childViews){
+                if(this.childViews[view].remove)
+                    this.childViews[view].remove();
+            }
+            BabblerView.prototype.remove.apply(this, arguments);
+        },
+        render: function () {
+            this._super();
+            this.undelegateEvents();
+            this.$el.html(this._template({zone: this.model, className: this.app.user.can("uncustomize_zone") ? "" : "no-uncustomize"}));
+            this.$('.add-content').before(this.childViews.saveButton.el);
+            if (this.app.user.can("restore_zone")) {
+                this.$('.btn-group').prepend(this.childViews.switchVersionButton.el);
+            }
+            this.dom[this.cid].templateIndicator = this.$('.template-link-indicator').children();
+            this.delegateEvents();
+            return this;
+        },
+        setZone: function (zone) {
+            this.stopListening(this.model);
+            this.model = zone;
+            this.childViews.switchVersionButton.model = zone;
+            this.childViews.saveButton.model = zone;
+            this.listenTo(zone, Events.BackboneEvents.CHANGE, this.breakTemplateLink);
+            return this.render();
+        },
+        onZoneSelect: function (view, zone) {
+            this.trigger(Events.ChoiceEvents.SELECT, view, zone);
+        },
+        breakTemplateLink: function (model, options) {
+            if (model !== this.model) {
+                this.dom[this.cid].templateIndicator.removeClass('icon-template-ok').addClass('icon-template-broken').parent().addClass('active');
+                this.model.customized = true;
+            }
+        },
+        doUncustomize: function () {
+            this.model.customized = false;
+            this.model.sections = [];
+            this.listenToOnce(this.model, Events.BackboneEvents.SYNC, function () {
+                // this.onZoneSet(this.currentZone, this.currentZone);
+                this.render();
+            });
+            this.model.save();
+        },
+        uncustomize: function () {
+            if (this.app.user.can("uncustomize_zone"))
+                this.confirm({
+                    message: translate('confirmUncustomize', {'name': this.model.name}),
+                    title: translate("uncustomize"),
+                    type: 'delete',
+                    onOk: _.bind(this.doUncustomize, this),
+                    options: {dialogClass: 'delete no-close', dontAskAgain: true, subject: 'uncustomize'}
+                });
+            else
+                return;
+        }
+    });
+    return ZoneToolbox;
+});
Index: src/js/JEditor/NewsPanel/Models/Article.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Article.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Models/Article.js	(révision 12306)
@@ -0,0 +1,102 @@
+define([
+    "underscore",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Models/Model",
+    "./Categorie",
+    "./CategorieCollection",
+    "JEditor/Commons/Pages/Models/Page",
+    "JEditor/Commons/Pages/Models/PageCollection"
+], function(_, Events, Model, Categorie, CategorieCollection, Page, PageCollection) {
+    var Article = Model.extend(
+            /**
+             * @lends Article
+             */
+                    {
+                        urlRoot: __IDEO_API_PATH__ + '/news/article',
+                        defaults: {
+                            category: [],
+                            state: [0],
+                            title: "",
+                            lang:"",
+                            ressource: "",
+                            introduction: "",                
+                            publicationDate:"", 
+                            programmingDate: "",
+                            metaTitle: "",
+                            metaOpengraph: "",
+                            page:null,
+                            content:null
+                        },
+                        initialize: function() {
+                            this._super();
+                            this.file = null;
+                            this.getFile();
+                            if (this.category ){
+                                var categories = CategorieCollection.getInstance();
+                                if (Array.isArray(this.category)) {
+                                    this.category = this.category.map(function(categoryId) {
+                                        if (!(categoryId instanceof Categorie)) {
+                                            return categories.findWhere({id: categoryId});
+                                        } else {
+                                            return categoryId;
+                                        }
+                                    });
+                                }
+                                else if (!(this.category instanceof Categorie)) {
+                                    this.category = categories.findWhere({id: categoryId});
+            
+                                }
+                            }
+                            if (!(this.page instanceof Page)) {
+                                var pageCollection = PageCollection.getInstance();
+                                this.page = pageCollection.findWhere({id: this.page});
+                            }
+                            
+                            this.lastState = this.toJSON();
+                            this.on(Events.BackboneEvents.SYNC, this._onSync);
+                        },
+                        _onSync: function() {
+                            this.lastState = this.toJSON();
+                        },
+                        hasUnsavedChanges: function() {
+                            return !_.isEqual(this.toJSON(), this.lastState);
+                        },
+                        cancel: function() {
+                            this.set(this.lastState);
+                        },
+                        setCategorY: function (categorie) {
+                            this.category[0]=categorie.attributes;
+                        },
+                        getFile: function () {
+                            if (this.file) return this.file ;
+                            if(!this.ressource) return null;
+                            this.fileCollection =  new FileCollection();
+                            this.fileCollection.setId(this.ressource);
+                            this.fileCollection.fetch({async:false});
+                            this.file =  this.fileCollection.get(this.ressource);
+                            return this.file;
+                        },
+                        parse : function(data) {
+                            ret = (data.data)? data.data : data
+                            return ret;
+                        },
+                        toJSON: function() {
+                            var json = this._super();
+                            if (json.page && json.page.id) {
+                                json.page = json.page.id;
+                            }
+                            if (json.category )
+                                if(Array.isArray(json.category)) {
+                                json.category = json.category.map(function(cat) {
+                                    return cat.id || cat;
+                                });
+                            } else {
+                                json.category = json.category.id;
+                            }
+                    
+                            return json;
+                        }
+                    });
+            Article.SetAttributes(['category', 'state','title', 'lang','ressource','introduction', 'publicationDate','programmingDate', 'metaTitle', 'metaOpengraph', "page", "content"]);
+            return Article;
+        });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Models/ArticlesCollection.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/ArticlesCollection.js	(révision 12305)
+++ src/js/JEditor/NewsPanel/Models/ArticlesCollection.js	(révision 12306)
@@ -1,35 +1,38 @@
 define([
     "collection!JEditor/Commons/Ancestors/Models/Collection",
-    "JEditor/PagePanel/Contents/Zones/Models/Zone"
-], function(Collection, Zone) {
+    "./Article"
+], function(Collection, Article) {
     var ArticlesCollection = Collection.extend(
         /**
          * @lends ArticleCollection.prototype
          */
                 {
-                    url: __IDEO_API_PATH__ + '/news/Articles',
-                    //model: Article,
-                    set: function() {
-                        Collection.prototype.set.apply(this, arguments);
-                        this.each(function(model) {
-                            model.resetChildren();
-                        });
+                    constructor: function() {
+                        if (arguments.callee.caller !== ArticlesCollection.getInstance)
+                            throw new TypeError("Impossible d'instancier un Panneau, veuillez utiliser la méthode statique MaClass.getInstance()");
+                        else {
+                            Collection.apply(this, arguments);
+                            if (!this.fetched) {
+                                this.fetch();
+                                this.fetched = true;
+                            }
+                        }
                     },
-                    reset:function(){
-                        Collection.prototype.reset.apply(this, arguments);
-                        this.each(function(model) {
-                            model.resetChildren();
-                        });
+                    url: function() {
+                        return __IDEO_API_PATH__ + '/news/article'+(this.lang ? '?lang=' + this.lang : '');
+                    },
+                    model: Article,
+                    parse : function(data) {
+                        return (data.data)? data.data : data;
                     }
-                }
-        );
+                    
+                });
+                ArticlesCollection.instance = null;
+                ArticlesCollection.getInstance = function() {
+                    if (this.instance === null)
+                        this.instance = new ArticlesCollection();
+                    return this.instance;
 
-        Object.defineProperties(ArticlesCollection.prototype, {
-            main: {
-                get: function() {
-                    return this.at ? this.at(0) : undefined;
-                }
-            }
-        });
+                };
         return ArticlesCollection;
     });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Models/NewsConfig.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/NewsConfig.js	(révision 12305)
+++ src/js/JEditor/NewsPanel/Models/NewsConfig.js	(révision 12306)
@@ -4,7 +4,7 @@
     "JEditor/Commons/Ancestors/Models/Model"
 ],function(_,
         Events,
-        Model,
+        Model
 ){ var NewsConfig = Model.extend(
                 {
                     defaults: {
@@ -11,7 +11,7 @@
                         newsStyle: 0,                
                         newsFormat:"landscape", 
                         newsStyleAff: 1,
-                        newsNbArticle: 3,
+                        newsNbArticle: 3
                     },
     
                 });
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12305)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12306)
@@ -321,7 +321,7 @@
                                     this.childViews.newsEditorView.renderAddCategorie()
                                 },
                                 onAddArticleClick : function() {
-                                   this.childViews.newsEditorView.renderAddArticle()
+                                   this.childViews.newsEditorView.renderAddArticle(this.currentCategorie);
                                 },
                                 /**
                                  * ajoute les variables à l'objet this.dom
Index: src/js/JEditor/NewsPanel/Templates/ZoneToolbox.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/ZoneToolbox.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Templates/ZoneToolbox.html	(révision 12306)
@@ -0,0 +1,6 @@
+<span class="label"><%=__("zoneToolboxLabel")%></span>
+<div class="btn-group <%=className%>">
+    <button type="button" class="btn btn-default template-link-indicator <%=zone.customized?'clickable':''%>"><span class="icon <%= (zone.customized?'icon-layout-broken':'icon-layout-ok')%>"></span><% if(zone.customized){%><span class="label"><%=__('backToTemplate')%></span><% } %></button>
+    <button type="button" class="btn btn-default add-content" id="available-blocks-trigger"><span class="icon icon-add"></span><span class="label"><%=__("addContent")%></span></button>
+    <button type="button" class="btn btn-default publish" id="available-blocks-trigger"><span class="icon icon-add"></span><span class="label"><%=__("publish")%></span></button>
+</div>
Index: src/js/JEditor/NewsPanel/Templates/articlesTable.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/articlesTable.html	(révision 12305)
+++ src/js/JEditor/NewsPanel/Templates/articlesTable.html	(révision 12306)
@@ -2,10 +2,10 @@
 
 <div id="news-table">
 	<form id="search-form">
-		<label>Search :</label>
+		<label><%=__('search')%></label>
 		<span class="search-news"> 
 			<i class="icon icon-find"></i>
-			<input type="text"  placeholder="Search...">
+			<input type="text"  placeholder="<%=__('searchDesc')%>">
 
 		</span>
 	</form>
@@ -12,70 +12,85 @@
 	<table id="data-table">
 		<thead>
 			<tr>
-				<th data-sort="title">
-				   Title
+				<th></th>
+				<th data-sort="title" data-sortorder="<%= (sortAsc)?'asc':'desc' %>">
+					<%=__('title')%>
 				<span class="sort-icon"> 
-					<span class="asc"></span>
-					<span class="desc"></span>
+					<%if(sortedBy=='title'){%> 
+						<span class="<%= (sortAsc)?'desc':'asc'%>"></span>
+					<%}%>
 				</span>
 				</th>
-				<th data-sort="author">Auteur</th>
-				<th data-sort="categorie">Catégorie</th>
-				<th data-sort="datepub">Date de publication</th>
-				<th data-sort="etat">Etat</th>
+				<th data-sort="author" data-sortorder="<%= (sortAsc)?'asc':'desc' %>"><%=__('author')%>
+					<span class="sort-icon"> 
+						<%if(sortedBy=='author'){%> 
+							<span class="<%= (sortAsc)?'desc':'asc' %>"></span>
+						<%}%>
+					</span>
+				</th>
+				<th data-sort="categorie" data-sortorder="<%= (sortAsc)?'asc':'desc' %>"><%=__('category')%>
+					<span class="sort-icon"> 
+						<%if(sortedBy=='categorie'){%> 
+							<span class="<%= (sortAsc)?'desc':'asc' %>"></span>
+						<%}%>
+					</span>
+				</th>
+				<th data-sort="datepub" data-sortorder="<%= (sortAsc)?'asc':'desc' %>"><%=__('datepub')%>
+					<span class="sort-icon"> 
+						<%if(sortedBy=='datepub'){%> 
+							<span class="<%= (sortAsc)?'desc':'asc' %>"></span>
+						<%}%>
+					</span>
+				</th>
+				<th data-sort="etat" data-sortorder="<%= (sortAsc)?'asc':'desc' %>"><%=__('state')%>
+					<span class="sort-icon"> 
+						<%if(sortedBy=='etat'){%> 
+							<span class="<%= (sortAsc)?'desc':'asc' %>"></span>
+						<%}%>
+					</span>
+				</th>
 				<th></th>
 			</tr>
 		</thead>
 		<tbody>
-			<tr>
+			<%_.each(content,function(item){ %>
+			<tr class="<%=(!item.active)?'disabled':''%>" data-cid="<%= item.cid %>">
+				<td><span class="img-news"> <img src="<%=item.image%>" alt="titre"></span></td>
 				<td class="title">
-					<span class="img-news"> <img src="https://preprod-ideo32.linkeo.ovh/logo-sm.png" alt="titre"></span>
-					<span>titre</span>
+					<%=item.title%>
 				</td>
 				<td class="author">
-					Linkeo
+					<%=item.author%>
 				</td>
-				<td class="categorie"> Catégorie 1</td>
-				<td class="datepub">
-					<span><i class="icon"></i></span>
-					<span></span>
+				<td class="categorie">
+					<%=item.categorie%>
 				</td>
-				<td class="etat">
-					<span class="btn green">Publié</span>
-					<span class="btn orange">Brouillon</span>
-					<span class="btn bleu">Programmé</span>
-				</td>
-				<td>
-					<span > active</span>
-					<span>edit</span>
-					<span>tdanslate</span>
-					<span>delete</span>
-				</td>
-			</tr>
-			<tr>
-				<td class="title">
-					<span class="img-news"> <img src="https://preprod-ideo32.linkeo.ovh/logo-sm.png" alt="titre"></span>
-					<span>titre</span>
-				</td>
-				<td class="author">
-					Linkeo
-				</td>
-				<td class="categorie"> Catégorie 1</td>
 				<td class="datepub">
 					<span><i class="icon"></i></span>
 					<span></span>
 				</td>
 				<td class="etat">
-					<span class="btn orange">Brouillon</span>
-					<span class="btn bleu">Programmé</span>
+					<%_.each(item.state,function(state){ %>
+						<%if(state==2){%> 
+							<span class="btn green">Publié</span>
+						<%}%>
+						<%if(state==0){%> 
+							<span class="btn orange">Brouillon</span>
+						<%}%>
+						<%if(state==1){%> 
+							<span class="btn bleu">Programmé</span>
+						<%}%>
+					<%
+					});%>
 				</td>
 				<td>
-					<span> active</span>
-					<span>edit</span>
-					<span>tdanslate</span>
-					<span>delete</span>
+					<span class="switch"><span></span></span>
+					<span class="icon-edit"></span>
+					<span class="icon-bin"></span>
 				</td>
 			</tr>
+			<%
+			});%>
 		</tbody>
 	</table>
 </div>
Index: src/js/JEditor/NewsPanel/Templates/newsEditor.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/newsEditor.html	(révision 12305)
+++ src/js/JEditor/NewsPanel/Templates/newsEditor.html	(révision 12306)
@@ -30,5 +30,7 @@
 </header>
 <div class="main">
     <div id="content-editor">
+        <div id="category-editor"></div>
+        <div id="article-editor"></div>
     </div>
 </div>
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Views/AddArticleView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/AddArticleView.js	(révision 12305)
+++ src/js/JEditor/NewsPanel/Views/AddArticleView.js	(révision 12306)
@@ -1,71 +1,212 @@
 define([
+    "jquery",
+    "underscore",
+    "JEditor/Commons/Events",
     "JEditor/Commons/Ancestors/Views/BabblerView",
-    "text!../Templates/categorieForm.html",
+    "text!../Templates/addArticle.html",
+    "JEditor/Commons/Files/Views/FileUploaderView",
     "JEditor/FilePanel/Models/FileCollection",
-    "JEditor/Commons/Files/Views/FileUploaderView",
-    "JEditor/Commons/Languages/Views/LanguagesDropDown",
+    "JEditor/Commons/Ancestors/Views/DropDownView",
+    "JEditor/NewsPanel/Models/CategorieCollection",
+    "JEditor/NewsPanel/Models/ArticlesCollection",
     "i18n!../nls/i18n"
-  ], function (BabblerView, template, FileCollection, FileUploaderView, LanguagesDropDown, translate) {
+  ], function ($,
+    _,
+    Events,
+    BabblerView,
+    template,
+    FileUploaderView,
+    FileCollection,
+    DropDownView,
+    CategorieCollection,
+    ArticlesCollection,
+    translate) {
     var AddArticleView = BabblerView.extend({
+        _currentCategory: null,
+        _groupList: null,
+        _categoryDropdown: null,
+        _categories: null,
+        collection: null,
+        events: {
+            'click button.saveNewArticle' : 'addArticle',
+            'click button.saveArticle' : 'updateArticle',
+            'click button.annuler' : 'cancel',
+            'click .upload-article ' : 'onlyComputer',
+           },
         initialize: function() {
             this.template = this.buildTemplate(template, translate);
-            this.langDropDown =  new LanguagesDropDown({
-                collection : this.options.languageList,
-                _default : this.options.language,
-                defaultLabel : 'language'
-            });
-            this.listenTo(this.langDropDown, "selected:choice", this._onLangSelect);
+            this.fileCollection = new FileCollection();
+            this._categories = CategorieCollection.getInstance();
+            this.collection = ArticlesCollection.getInstance();
+            this.translations = translate.translations;
+            this._onLoaded();
+        },
+        proccessAttribute : function() {
 
-            const uploadParams = {
+            this.$('.upload-article .uploader').addClass('done');
+            this.$('.upload-article .uploader .preview .imagepreview').css({opacity: 1});
+            this.$('.upload-article .rigth-delete-image').show();
+            if(this.model.getFile() && this.model.getFile().ext === "svg"){    
+                this.$('.upload-article .uploader .preview .imagepreview').css({
+                    backgroundSize: '100%',
+                    backgroundRepeat: 'no-repeat'
+                  });
+            }  
+
+        },
+        onlyComputer : function(e){
+            e.preventDefault();
+            e.stopImmediatePropagation();
+            this.$('.upload-article  .uploader .actions-wrapper').removeClass('visible');
+            this.$('.upload-article  .uploader .actions-wrapper .actions .computer input:file.upload').trigger('click');
+        },
+        _onLoaded: function() {
+            this._currentCategory = (this.model.category)? this.model.category[0]: null;
+            this.lang = this.options.language;
+            this.bylang = this._categories.groupBy('lang');
+            this._groupList = this.bylang[this.lang.id];
+            this._categoryDropdown = new DropDownView({
+                collection: this._groupList,
+                _default: (this._currentCategory)?this._categories.get(this._currentCategory):null,
+                labelField: _.bind(function(model) {
+                    return model.lang[this.lang.id].title;
+                }, this)});
+            this.listenTo(this._categoryDropdown, Events.ChoiceEvents.SELECT, this._onCategorySelected)
+            this.render();
+        },
+        _onCategorySelected : function(view, selected) {
+            this._currentCategory = selected;
+            this.model.setCategorY(this._currentCategory);
+        },
+        render: function () {
+            params = {
+                create :  (this.model.id)? false : true,
+                detail : this.model.toJSON()
+            }
+            this.$el.html(this.template(params));
+            this.$('.category-dropdown').append(this._categoryDropdown.render().el);
+            
+            this.fileUploader = new FileUploaderView({
                 customStockEvent: '_parsestock_image',
                 acceptedTypes: ['image'],
-                acceptedExtensions: ['jpeg', 'jpg', 'png', 'svg'],
+                acceptedExtensions: ['jpeg', 'jpg', 'png', 'svg', 'webp'],
                 refusedExtensions: ['bmp'],
                 uploadUrl: __IDEO_UPLOADLOGO_URL__ ? __IDEO_UPLOADLOGO_URL__ : '/admin/resource-logo',
-            };
-            this.fileUploader = new FileUploaderView({
-                currentFile : null,
-                collection: this.fileCollection,
-                uploadParams,
+                currentFile : this.model.getFile(),
+                collection: this.fileCollection
             });
            
-            //this.listenTo(this.fileUploader, Events.FileUploaderEvents.UPLOAD, this._onUpload);
-            this.fileCollection = new FileCollection();
-            this.translations = translate.translations;
-        },
-        render: function () {
-           
-            this.$el.html(this.template());
-            this.$('.side-bar__lang').append(this.langDropDown.render().el);
-            this.$('.upload-categorie').append(this.fileUploader.render().el);
-            if (true) {
-                this.$('.upload-categorie .uploader .preview ').hide();
+            this.listenTo(this.fileUploader, Events.FileUploaderEvents.UPLOAD, this._onUpload);
+            this.listenTo(this.fileUploader, Events.FileUploaderEvents.TOOBIG, this.onError);
+
+            if (this.model.ressource) {
+                this.fileUploader.currentFile = this.model.getFile();
             }
-            else this.$('.upload-categorie .uploader .preview .imagepreview').after('<span style="font-size:9px;display:block;margin-top:35px;color: #32ace0;text-align:center;">'+this.translations.DefaultMessageUploaderLogo+'</span>');
+            
+            this.$('.upload-article').append(this.fileUploader.el);
+			this.fileUploader.render();
+            this.proccessAttribute(); 
 
-           
+            this.$('.upload-article .uploader .preview .imagepreview').after('<span style="font-size:9px;display:block;margin-top:35px;color: #32ace0;text-align:center;">'+this.translations.DefaultMessageUploaderLogo+'</span>');
+
             return this;
         },
         _onUpload: function(file){
+            if (file) {
+                this.currentFile = file;
+                this.model.ressource = file.id;
+                this.model.file = file;
+                this.$('upload-categorie .uploader .view').addClass('done');
+                this.$('.upload-categorie .uploader .preview .imagepreview').css({opacity: 1});
+                this.$('.upload-categorie .uploader .preview .imagepreview .progressbar').css({width: 0});
+                if( this.currentFile === "svg"){
+                    this.$('.upload-categorie .uploader .preview .imagepreview').css({
+                        backgroundSize: '100%',
+                        backgroundRepeat: 'no-repeat',
+                        backgroundPosition: 'center'
+                    });
+                }   
+            } 
+
+        },
+        onSave: function () {
+            console.log("test");
+            this.stopListening(this.model, Events.BackboneEvents.ERROR, this.onError);
+            this.trigger(Events.LoadEvents.LOAD_SUCCESS, this);
+            $.toast({
+                text: translate("saveSuccesful"), 
+                icon: 'icon-check-circle', 
+                type:'success',
+                appendTo:'#news-editor .main',
+                showHideTransition: 'fade', 
+                hideAfter: 5000, 
+                position: 'top-right', 
+                textAlign: 'left', 
+                allowToastClose:false,
+                loader:false
+            });
+                          
+        },
+        updateArticle:function(e) {
+            e.preventDefault(); 
+            e.stopImmediatePropagation();
             
-            this.currentFile = new File(file.attributes);
-           
-            this.model.set('Logo', this.currentFile);
-            this.model.save();
-            this.$('upload-categorie .uploader .view').addClass('done');
-            this.$('.upload-categorie .uploader .preview .imagepreview').css({opacity: 1});
-            this.$('.upload-categorie .uploader .preview .imagepreview .progressbar').css({width: 0});
-            if( this.currentFile === "svg"){
-                this.$('.upload-categorie .uploader .preview .imagepreview').css({
-                    backgroundSize: '100%',
-                    backgroundRepeat: 'no-repeat',
-                    backgroundPosition: 'center'
-                });
-            }   
+            if (this._checkInput()) {
+                this.model.lang[this.lang.id] = this.currentArticleLang;
+                this.model.save();
+                this.collection.add(this.model);
+                this.collection.trigger('change');
+                this.onSave();
+            }
+        },
 
+        /**
+         * verication de notre input
+         */
+        _checkInput: function(){
+            var title = this.$('input[name="title"]');
+            var introduction = this.$('textarea[name="introduction"]');
+            var valid = true;
+            this.model.lang = this.lang.id;
+            this.model.title = title.val();
+            this.model.introduction = introduction.val();
+
+            if (title.val() =='') {
+                title.addClass('error');
+                valid = false;
+            }
+            if (introduction.val() =='') {
+                introduction.addClass('error');
+                valid = false;
+            }
+            return valid;
         },
+      
+        addArticle: function(e) {
+            e.preventDefault(); 
+            e.stopImmediatePropagation();
+            
+            if (this._checkInput()) {
+                this.model.save();
 
+                this.collection.add(this.model);
+                this.collection.trigger('change');
+
+                this.onSave();
+                this.trigger(Events.ArticleAddEvents.ADDED, this.model);
+            }
+            return ;
+        },
+
     });
+    Events.extend(
+        {
+            ArticleAddEvents : {
+        
+                ADDED : 'add:newsarticle',
+            }
+        });
    
+   
     return AddArticleView;
   });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Views/ArticleEditorView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticleEditorView.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Views/ArticleEditorView.js	(révision 12306)
@@ -0,0 +1,349 @@
+define([
+    "jquery",
+    "underscore",
+    "text!../Templates/articleEditor.html",
+    "JEditor/Commons/Events",
+    "JEditor/NewsPanel/Views/ArticleEditorView",
+    "JEditor/Commons/Ancestors/Views/BabblerView",
+    "JEditor/Commons/Ancestors/Views/LoaderView",
+    "JEditor/PagePanel/Views/PageTitleField",
+    "JEditor/PagePanel/Views/PagePreview",
+    "JEditor/PagePanel/Contents/Zones/Views/ContentZoneView",
+    "JEditor/NewsPanel/Views/ZoneToolBox",
+    "collection!JEditor/Commons/Languages/Models/ContentLanguageList",
+    "collection!JEditor/Commons/Pages/Models/PageCollection",
+    "JEditor/PagePanel/Contents/Zones/Models/Zone",
+    "JEditor/PagePanel/Utils/PageUtils",
+    "JEditor/NewsPanel/Models/ArticlesCollection",
+    "JEditor/NewsPanel/Views/ArticlesCollectionView",
+    "JEditor/NewsPanel/Views/AddArticleView",
+    "JEditor/NewsPanel/Models/Article",
+    "JEditor/PagePanel/Contents/Zones/Models/Zone",
+    "JEditor/PagePanel/Contents/Zones/Versions/Models/Version",
+    "JEditor/PagePanel/Contents/Zones/Versions/Models/VersionsCollection",
+    "JEditor/NewsPanel/Views/ConfigCategorieView",
+    "i18n!../nls/i18n",
+    //not in params
+   // "jqueryPlugins/dropdown",
+    "jqueryPlugins/affix"
+], function($,
+     _, 
+     template, 
+     Events, 
+     ArticleEditorView, 
+     BabblerView, 
+     LoaderView,
+     PageTitleField, 
+     PagePreview,
+     ContentZoneView,
+     ZoneToolBox,
+     ContentLanguageList,
+     PageCollection,
+     Zone,
+     PageUtils,
+     ArticlesCollection, 
+     ArticlesCollectionView, 
+     AddArticleView,
+     Article,
+     ZoneModel,
+     Version,
+     VersionsCollection,
+     ConfigCategorieView, 
+     translate
+     ) {
+   
+    var ArticleEditorView = BabblerView.extend({
+        events: {
+            'click .btn.preview': 'preview',
+            'click a[data-action]': '_onActionClick',
+            'click .save-version': 'usePreviousVersion'
+        },
+        _loaded: 0,
+        attributes: {
+            id: "article-view"
+        },
+        fadeOutEffect: "fadeOut",
+        fadeInEffect: "fadeIn",
+        zonesFetched: false,
+        selectedZoneVersion: null,
+        versionsCollection: new VersionsCollection(),
+        initialize: function () {
+            this._super();
+            this.currentZoneID = this.options.zoneID || null;
+            this._template = this.buildTemplate(template, translate);
+            if (this.model) {
+                this.zoneToolbox = new ZoneToolBox();
+            }
+            else this.model = new Article()
+            this.pagePreview = new PagePreview();
+            this.articleDetail = new AddArticleView({
+                model: this.model,
+                category: this.model.category[0],
+                language : this.options.language,
+                collection:  this.options.categorieCollection,
+            });
+            this.on(Events.LoadEvents.LOAD_SUCCESS, this.render);
+            self = this;
+            this.listenTo(this.articleDetail , Events.ArticleAddEvents.ADDED, _.bind(function(article) {
+                self.newsPanel.currentArticle = article;
+            })); 
+        },
+        checkAffixTop:function(){
+            return (document.documentElement.clientHeight < 866 ? 90 : 230);
+        },
+        remove: function () {
+            if (this.zoneToolbox)
+                this.zoneToolbox.remove();
+            if (this.pagePreview)
+                this.pagePreview.remove();
+            if (this.articleDetail)
+                this.articleDetail.remove();
+            if (this.sectionCollectionView)
+                this.sectionCollectionView.remove();
+            BabblerView.prototype.remove.apply(this, arguments);
+        },
+        onZoneSelect: function (view, selectedZone) {
+
+            if (this.currentZone.hasUnsavedChanges())
+                this.confirmUnsaved({
+                    message: translate("quitWithoutSaving"),
+                    title: translate("unsavedChanges"),
+                    type: 'delete-not-saved',
+                    onYes: _.bind(function () {
+                        this.currentZone.save();
+                        this.setZone(selectedZone);
+                        this.render();
+                    }, this),
+                    onNo: _.bind(function () {
+                        this.setZone(selectedZone);
+                        this.render();
+                    }, this),
+                    options: {
+                        dialogClass: 'delete no-close',
+                        dontAskAgain: true
+                    }
+                });
+            else {
+                this.setZone(selectedZone);
+            }
+        },
+        onZoneSave: function () {
+            if (this.sectionCollectionView)
+                this.sectionCollectionView.remove();
+            this.sectionCollectionView = new ContentZoneView({
+                model: this.currentZone
+            });
+            return this.render();
+        },
+        load: function () {
+            this.trigger(Events.LoadEvents.LOAD_START, this);
+            try {
+                
+                if (this.currentZoneID) {
+                    this.versionsCollection.zoneId = this.currentZoneID;
+                    
+                    this.listenToOnce(this.versionsCollection, Events.BackboneEvents.SYNC, this.onAllZoneLoad);
+                    this.listenToOnce(this.versionsCollection, Events.BackboneEvents.ERROR, function (zoneCollection, xhr, options) {
+                        this.trigger(Events.LoadEvents.LOAD_ERROR, this, xhr, options);
+                    });
+                    this.versionsCollection.fetch();
+                }
+                
+            } catch (e) {
+                this.trigger(Events.LoadEvents.LOAD_ERROR, this, e, null);
+            }
+            return this;
+        },
+        onAllZoneLoad: function () {
+            var zoneversion = this.versionsCollection.max(function(model) {
+                return model.get('id');
+            });
+            var that = this;
+            this.listenToOnce(this.versionsCollection, Events.BackboneEvents.SYNC, this.onZoneLoad);
+            this.listenToOnce(this.versionsCollection, Events.BackboneEvents.ERROR, function (zoneCollection, xhr, options) {
+                this.trigger(Events.LoadEvents.LOAD_ERROR, this, xhr, options);
+            });
+            zoneversion.fetch({
+                success: function(res){
+                    var resJson = res.toJSON();
+                   that.zone = new ZoneModel(that.cleanData(resJson.zoneContent, ['parentPage', 'parentPageType']));
+                }
+            } );
+        },
+        /**
+         * Récupère toutes les versions pour une zone
+         */
+        fetchVersions: function(){
+            this.versionsCollection.zoneId = this.currentZoneID;
+            this.pagePanel.listenToOnce(this.collection, Events.BackboneEvents.SYNC, this.onLoadSuccess);
+            this.pagePanel.listenToOnce(this.collection, Events.BackboneEvents.ERROR, this.onLoadError);
+            this.pagePanel.onLoadStart();
+            this.versionsCollection.fetch();
+            
+            return this;
+        },
+        onZoneLoad: function () {
+            
+            var zone = this.zone;
+            this.zonesFetched = true;
+            var errors = zone.getInitErrors();
+            if (errors.length) { 
+                this.error({
+                    message: translate("corruptedContent"),
+                    title: translate("error")
+                });
+                zone.resetInitErrors();
+            }
+            this.setZone(zone);
+        },
+        render: function () {
+            
+            this.undelegateEvents();
+            this.$el.empty();
+            this.$el.html(this._template({
+                user: this.app.user,
+                zone: this.currentZone,
+                model : this.model,
+                language : this.options.currentLang,
+                collection: this.options.categorieCollection,
+                languages: ContentLanguageList.getInstance(),
+                currentZoneID:this.currentZoneID
+            }));
+            
+
+            if (this.pagePreview) {
+                this.pagePreview.remove();
+                this.pagePreview = new PagePreview();
+                this.newsPanel.$el.append(this.pagePreview.el);
+            }
+            if (this.articleDetail) {
+                this.$('.dis-table.bordered').append(this.articleDetail.el);
+                this.articleDetail.render();
+            }
+            if (this.zoneToolbox && this.currentZone) {
+                this.newsPanel.$('.zone').prepend(this.zoneToolbox.el);
+                this.zoneToolbox.render();
+            }
+            if (this.sectionCollectionView) {
+                this.sectionCollectionView.setElement(this.$('.content-zone'));
+                this.sectionCollectionView.render();
+            }
+            this.$('header').affix({
+                offset: {
+                    top: this.checkAffixTop
+                }
+            });
+
+            this.dom.uploadZone = this.$('.page');
+            var page = this;
+          
+            //we don't show the message on import
+            this.$(".message").hide();
+
+            this.$('.zone-selector>.dropdown-toggle, .page-action').dropdown();
+            this.dom.window.scroll();
+            this.delegateEvents();
+
+            return this;
+        },
+        _onActionClick: function (event) {
+            var $target = $(event.currentTarget);
+            var action = $target.data('action');
+            var params = $target.data('args');
+            if (this[action])
+                this[action].apply(this, params ? params.split(';') : []);
+            return false;
+        },
+
+        showuploader: function() {                       
+            var uploadResult = this.dom.showUploader.trigger('click');
+            console.dir(uploadResult);
+        },
+        setZone: function (zone) {
+            if (!zone)
+                return this;
+            this.setLoading(true);
+            this.currentZoneID = ((zone instanceof Zone)) ? zone.id : zone;
+            if (!this.zonesFetched)
+                return this;
+            else {
+                var oldZone = this.currentZone ? this.currentZone : null;
+                var callback = _.bind(function () {
+                    this.currentZone = zone;
+                    var url = 'news/' + this.model.lang + '/article/' + this.model.id + '/' + this.currentZone.id;
+                    this.app.params.lastUrl = url;
+                    this.app.params.save();
+                    this.app.router.navigate(url);
+                    this.zoneToolbox.setZone(this.currentZone);
+                    if (this.sectionCollectionView)
+                        this.sectionCollectionView.remove();
+                    this.sectionCollectionView = new ContentZoneView({
+                        model: this.currentZone
+                    });
+                    this.listenTo(this.currentZone, Events.BackboneEvents.SYNC, this.onZoneSave);
+                    this.listenTo(this.currentZone, Events.BackboneEvents.CHANGE + ':customized', this.onCustomizedChange);
+                    this.trigger(Events.LoadEvents.LOAD_SUCCESS, this);
+                    if (oldZone !== null)
+                        this.stopListening(oldZone);
+                    this.render();
+                    this.setLoading(false);
+                }, this);
+                if (zone._hasChanged) {
+                    this.listenToOnce(zone, Events.BackboneEvents.SYNC, callback);
+                    zone.fetch();
+                } else
+                    callback();
+                return this;
+            }
+        },
+        /**
+         * Enlève les données inutile dans l'objet
+         * 
+         * @param {Object} data
+         * @returns {Object}
+         */
+        cleanData: function (data, keys) {
+            for ( var i in keys ) {
+                // remove data on the param
+                if ( typeof data[keys[i]] !== 'undefined' ) {
+                    delete data[keys[i]];
+                }
+            }
+            
+            data = this._ToCamelCase(data);
+            
+            if ( typeof data["template"] === 'undefined' ) {
+                data["template"] = "";
+            }
+            
+            return data;
+        },
+        
+        _ToCamelCase: function(data) {
+            for (var i in data) {
+                // _ to camel case
+                if (i.indexOf('_') !== -1) {
+                    var arr = i.split('_');
+                    var key = "";
+                    for (var j in arr) {
+                        if ( parseInt(j) > 0 ) {
+                            arr[j] = arr[j].replace(/^\w/, function (chr) {
+                                return chr.toUpperCase();
+                            });
+                        }
+                        key += arr[j];
+                    }
+                    data[key] = data[i];
+                    delete data[i];
+                }
+            }
+            return data;
+        }
+    });
+    Events.extend({
+        ArticleEditorViewEvents : {
+            SHOWRIGHTPANEL: 'edit:config'
+        }
+    });
+    return ArticleEditorView;
+});
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 12305)
+++ src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 12306)
@@ -1,51 +1,210 @@
 
 define([
-    "jquery",
-    "underscore",
-    "text!../Templates/articlesTable.html",
-    "text!../Templates/emptyArticle.html",
-    "JEditor/Commons/Events",
-    "JEditor/Commons/Ancestors/Views/ListView",
-    "JEditor/Commons/Utils",
-    "i18n!../nls/i18n",
-    //not in params
-    "jqueryPlugins/dropdown"
-  ], function($,
-    _,
-    articlesList,
-    emptyArticle,
-    Events,
-    ListView,
-    Utils,
-    translate) {
+  "jquery",
+  "underscore",
+  "text!../Templates/articlesTable.html",
+  "text!../Templates/emptyArticle.html",
+  "JEditor/Commons/Events",
+  "JEditor/Commons/Ancestors/Views/ListView",
+  "JEditor/Commons/Utils",
+  "i18n!../nls/i18n",
+  //not in params
+  "jqueryPlugins/dropdown"
+], function($,
+  _,
+  articlesList,
+  emptyArticle,
+  Events,
+  ListView,
+  Utils,
+  translate) {
+
+  var ArticlesCollectionView = ListView.extend({
+    attributes: {
+      class: 'articlesList'
+    },
+    events: {
+      'keypress #search-form input': 'search',
+      'click th[data-sort]': 'onSort',
+      'click  .switch': 'onSwitchClick',
+      'click  .icon-edit': 'onEditClick',
+      'click  .icon-bin': 'onDeleteClick',
+    },
+    language: null,
+    category: null,
+    list:null,
+    fadeInEffect: 'fadeIn',
+    fadeOutEffect: 'fadeOut',
+    initialize: function() {
+      this.options.i18n = true;
+      this._super();
+      this._template = this.buildTemplate(articlesList, translate);
+      this._emptyTemplate = this.buildTemplate(emptyArticle, translate);
+      this._current = null;
+      this.language = this.options.language.toJSON();
+      if (this.options.category) {
+        categoryId = this.options.category.id;
+        self = this;
+        this.filteredBy = function (item) {
+          return item.category.some(function(cat) {
+            return cat.id === categoryId;
+          });
+      };
+      }
+    },
+    render: function() {
+      this.undelegateEvents();
+      var list;
+      // on modif selon notre besoin dans le tableau
+      list = this.listToJson(this.currentList, this.lang);
+      //pour le filtre category et recherche
+      if (this.filteredBy !== null)
+          list = this.getFilteredList(list);
+      // pour le sort    
+      list = this.sortList(list);
+      if (list.length > 0 ) {
+          var params = _.extend({}, {content: list, current : this._current,currentlang : this.lang, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected, user: this.app.user}, this.addTemplateParams(this.collection, list));
+          this.$el.html(this._template(params));
+      }
+      else {
+          var params = _.extend({}, {content: list, current : this._current,currentlang : this.lang, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected}, this.addTemplateParams(this.collection, list));
+          this.$el.html(this._emptyTemplate(params));
+      }
+      this.delegateEvents();
+      this.scrollables();
+      return this;
+    },
+    listToJson : function (list, lang){
+      if (!list ||list.length < 1 ) {
+        return list;
+      }
+       return list.map(function(list) {
+        var categories = '';
+        list.category.forEach(function(element) {
+          if (element && element.lang[lang]) {
+            categories += element.lang[lang].title + ' ';
+          }
+        });
+       
+        json = {
+          cid : list.id,
+          image : (list.image)?list.image.url:'',
+          title : list.title,
+          author : 'srazanandralisoa',
+          categorie : categories,
+          category : list.category,
+          id : list._id,              
+          publicationDate: list.publicationDate, 
+          programmingDate: list.programmingDate,
+          active : (list.page)?list.page.active : false,
+          content:list.content,
+          state : list.state
+        }
+        return json;
+      });
+     
+    },
+
+    onSwitchClick: function(event) {
+      var $target = $(event.currentTarget);
+      var articleId = $target.parent().parent().data('cid');
+      var $page = $target.parents('tr');
+      var article = this.collection.get(articleId);
+      if (article) {
+        var pageModel = article.page;
+          pageModel.active = !pageModel.active;
+          pageModel.save();
+          $page.toggleClass('disabled');
+      }
+    },
+
+    onDeleteClick: function(event) {
+      if (!this.app.user.can('delete_page'))
+        return false;
+        var $target = $(event.currentTarget);
+        var articleId = $target.parent().parent().data('cid');
+        var model = this.collection.get(articleId);
+      if (!this.app.params.dontAskAgainFor['deletePage']) {
+        this.confirm({
+          message: translate('confirmDeleteArticle', {
+            'name': model.title
+          }),
+          title: translate("deleteAction"),
+          type: 'delete',
+          onOk: _.bind(function() {
+            this.deleteModel(model);
+          }, this),
+          options: {
+            dialogClass: 'delete no-close',
+            dontAskAgain: true,
+            subject: 'deletePage'
+          }
+        });
+      } else {
+        this.deleteModel(model);
+      }
+      return false;
+    },
+
+    deleteModel: function(model) {
+      if (!this.app.user.can('delete_page'))
+        return;
+      if (model === this.app.currentPanel.currentArticle)
+          this.app.currentPanel.currentArticle = null;
+        model.destroy();
+    },
+    
+    onEditClick: function(event) {
+      var $target = $(event.currentTarget);
+      var articleId = $target.parent().parent().data('cid');
+      var article = this.collection.get(articleId);
+      if (article) 
+        this.trigger(Events.ChoiceEvents.SELECT, this, article);
+      
+      return false;
+    
+    },
+
+    onSort: function(event) {
+      var sortAttr = $(event.currentTarget).data('sort');
+      var order = $(event.currentTarget).data('sortorder');
   
-    var ArticlesCollectionView = ListView.extend({
-      attributes: {
-        class: 'articlesList scroll-container'
-      },
-      events: {
-      },
-      language: null,
-      fadeInEffect: 'fadeIn',
-      fadeOutEffect: 'fadeOut',
-      initialize: function() {
-        this.options.i18n = true;
-        //  this._super();
-        this._template = this.buildTemplate(articlesList, translate);
-        this._emptyTemplate = this.buildTemplate(emptyArticle, translate);
-        this._current = null;
-        //this.listenTo(this.collection,"change",this.render);
-      },
-      render: function() {
-        this._super();
-        //this.scrollables();
-        return this;
-      },
-      show: function(animate) {
-        this._super(animate);
-        this.dom.window.scroll();
-      },
-    });
+      var $ascIcon = $(event.currentTarget).find('.asc');
+      var $descIcon = $(event.currentTarget).find('.desc');
   
-    return ArticlesCollectionView;
-  });
\ No newline at end of file
+      if (order === 'asc') {
+          $descIcon.removeClass('hidden');
+          $ascIcon.addClass('hidden');
+      } else {
+          $ascIcon.removeClass('hidden');
+          $descIcon.addClass('hidden');
+      }
+  
+      if (this.sortBy) {
+          this.sortBy(sortAttr, order !== 'asc');
+      }
+      
+      return false;
+    },
+  
+    search: function (event) {
+        if (event.keyCode === 13) {
+          var $target = $(event.currentTarget);
+          var searchTerm = $target.val();
+          this.filteredBy = function (item) {
+            var valeurColonne1 = item.title.toLowerCase(); 
+            var valeurColonne2 = item.categorie.toLowerCase();
+        
+            // Utiliser des expressions régulières pour trouver des correspondances partielles
+            var regexColonne1 = new RegExp(searchTerm.toLowerCase(), 'g');
+            var regexColonne2 = new RegExp(searchTerm.toLowerCase(), 'g');
+            return regexColonne1.test(valeurColonne1) || regexColonne2.test(valeurColonne2);
+          };
+          this.render();
+          $target.val(searchTerm);
+        }
+    },
+  });
+
+  return ArticlesCollectionView;
+});
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Views/NewsEditorView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 12305)
+++ src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 12306)
@@ -9,6 +9,8 @@
     "collection!JEditor/Commons/Languages/Models/ContentLanguageList",
     "JEditor/NewsPanel/Models/ArticlesCollection",
     "JEditor/NewsPanel/Models/CategorieCollection",
+    "JEditor/NewsPanel/Models/Article",
+    "JEditor/PagePanel/Contents/Zones/Models/Zone",
     "JEditor/NewsPanel/Views/ArticlesCollectionView",
     "JEditor/NewsPanel/Views/CategorieAddView",
     "JEditor/NewsPanel/Views/AddArticleView",
@@ -31,6 +33,8 @@
      ContentLanguageList, 
      ArticlesCollection, 
      CategorieCollection,
+     Article,
+     Zone,
      ArticlesCollectionView, 
      CategorieAddView,
      AddArticleView,
@@ -113,6 +117,24 @@
             }));
 
          },
+         renderAddArticle: function (cat){
+            this.options.usepreview = true;
+            this.options.title = 'Ajouter une article';
+
+            var article =  new Article()
+            if(cat) article.setCategorY(cat);
+            this.articleEditorView = new ArticleEditorView({
+                languageList : this.languageList,
+                language : this.currentLang,
+                categorieCollection :  this.categories,
+                newsPanel : this,
+                model : article,
+            });
+           // this.articleEditorView.load();
+            this.render();
+            this.articleView.append(this.articleEditorView.render().el);
+            this.$('.zone-selector>.dropdown-toggle, .page-action').dropdown();
+        },
          renderArticlePage: function (){
             this.options.usepreview = true;
             this.articleEditorView = new ArticleEditorView({
Index: src/js/JEditor/NewsPanel/Views/ZoneToolBox.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ZoneToolBox.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Views/ZoneToolBox.js	(révision 12306)
@@ -0,0 +1,86 @@
+define([
+    "JEditor/Commons/Ancestors/Views/BabblerView",
+    "JEditor/Commons/Events",
+    "JEditor/PagePanel/Contents/Zones/Views/SaveButton",
+    "JEditor/PagePanel/Contents/Zones/Views/SwitchVersionButton",
+    "text!../Templates/ZoneToolbox.html",
+    "i18n!../nls/i18n"
+],function(
+    BabblerView,
+    Events,
+    SaveButton,
+    SwitchVersionButton,
+    template,
+    translate
+    ) {
+    var ZoneToolbox = BabblerView.extend({
+        className: "zone-toolbox",
+        events: {'click .template-link-indicator.clickable': 'uncustomize'},
+        initialize: function () {
+            this._super();
+            this.childViews = {
+                saveButton: new SaveButton({model: this.model}),
+                switchVersionButton: new SwitchVersionButton({model: this.model})
+            };
+            this._template = this.buildTemplate(template, translate);
+        },
+        remove: function () {
+            for( var view in this.childViews){
+                if(this.childViews[view].remove)
+                    this.childViews[view].remove();
+            }
+            BabblerView.prototype.remove.apply(this, arguments);
+        },
+        render: function () {
+            this._super();
+            this.undelegateEvents();
+            this.$el.html(this._template({zone: this.model, className: this.app.user.can("uncustomize_zone") ? "" : "no-uncustomize"}));
+            this.$('.add-content').before(this.childViews.saveButton.el);
+            if (this.app.user.can("restore_zone")) {
+                this.$('.btn-group').prepend(this.childViews.switchVersionButton.el);
+            }
+            this.dom[this.cid].templateIndicator = this.$('.template-link-indicator').children();
+            this.delegateEvents();
+            return this;
+        },
+        setZone: function (zone) {
+            this.stopListening(this.model);
+            this.model = zone;
+            this.childViews.switchVersionButton.model = zone;
+            this.childViews.saveButton.model = zone;
+            this.listenTo(zone, Events.BackboneEvents.CHANGE, this.breakTemplateLink);
+            return this.render();
+        },
+        onZoneSelect: function (view, zone) {
+            this.trigger(Events.ChoiceEvents.SELECT, view, zone);
+        },
+        breakTemplateLink: function (model, options) {
+            if (model !== this.model) {
+                this.dom[this.cid].templateIndicator.removeClass('icon-template-ok').addClass('icon-template-broken').parent().addClass('active');
+                this.model.customized = true;
+            }
+        },
+        doUncustomize: function () {
+            this.model.customized = false;
+            this.model.sections = [];
+            this.listenToOnce(this.model, Events.BackboneEvents.SYNC, function () {
+                // this.onZoneSet(this.currentZone, this.currentZone);
+                this.render();
+            });
+            this.model.save();
+        },
+        uncustomize: function () {
+            if (this.app.user.can("uncustomize_zone"))
+                this.confirm({
+                    message: translate('confirmUncustomize', {'name': this.model.name}),
+                    title: translate("uncustomize"),
+                    type: 'delete',
+                    onOk: _.bind(this.doUncustomize, this),
+                    options: {dialogClass: 'delete no-close', dontAskAgain: true, subject: 'uncustomize'}
+                });
+            else
+                return;
+        }
+    });
+    return ZoneToolbox;
+});
Index: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12305)
+++ src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12306)
@@ -49,4 +49,35 @@
 	"apply" 				: "Appliquer",
 
 	"saveSuccesful": "Le contenu a été sauvegardé avec succès",
+
+	//datatable
+	"search" : "Rechercher",
+	"searchDesc" : "Rechercher un article, une catégorie...",
+	"title" : "Titre",
+	"author" : "Auteur",
+	"category" : "Catégorie",
+	"datepub" : "Date de Publication",
+	"state" : "Etat",
+	"deleteAction":"Suppression",
+	"confirmDelete": "Vous \u00eates sur le point de supprimer d\u00e9finitivement une page",
+
+	// zonetoolbox
+	"publish"				: "Publier",
+	"addContent"			: "Ajouter du contenu",
+	"traduire"				: "Traduire",
+	"save": "Sauvegarder",
+	"saveAction": "Sauvegarde",
+	"saveSuccesful": "Le contenu a été sauvegardé avec succès",
+	"saveError": "Une erreur s'est produite lors de la sauvegarde de la page",
+	"emptyZone": "La zone sélectionnée est vide",
+	"howToAddContent": "Cliquez sur &laquo; Ajouter du contenu &raquo;<br/> puis glissez un bloc dans cette zone pour créer une section",
+	"versions": "Versions",
+	"pasteTheSection" : "Coller la section",
+
+	 //Article 
+	 "catArticle": "Catégorie de l'article",
+	 "titleArticle" : "Titre de l'article",
+	 "descriArticle": "Introduction de l'article",
+	 "DefaultMessageUploaderLogo": "Cliquez ici ou glissez-déposez une image",
+
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 12305)
+++ src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 12306)
@@ -48,7 +48,37 @@
 	"urlLabel" 				: "URL de l'article/la catégorie",
 	"cancel" 				: "Annuler",
 	"apply" 				: "Appliquer",
+ 
+	"saveSuccesful": "Le contenu a été sauvegardé avec succès",
 
-	
+	//datatable
+	"search" : "Rechercher",
+	"searchDesc" : "Rechercher un article, une catégorie...",
+	"title" : "Titre",
+	"author" : "Auteur",
+	"category" : "Catégorie",
+	"datepub" : "Date de Publication",
+	"state" : "Etat",
+	"deleteAction":"Suppression",
+	"confirmDelete": "Vous \u00eates sur le point de supprimer d\u00e9finitivement une page",
+
+	// zonetoolbox
+	"publish"				: "Publier",
+	"addContent"			: "Ajouter du contenu",
+	"traduire"				: "Traduire",
+	"save": "Sauvegarder",
+	"saveAction": "Sauvegarde",
 	"saveSuccesful": "Le contenu a été sauvegardé avec succès",
+	"saveError": "Une erreur s'est produite lors de la sauvegarde de la page",
+	"emptyZone": "La zone sélectionnée est vide",
+	"howToAddContent": "Cliquez sur &laquo; Ajouter du contenu &raquo;<br/> puis glissez un bloc dans cette zone pour créer une section",
+	"versions": "Versions",
+	"pasteTheSection" : "Coller la section",
+
+	 //Article 
+	 "catArticle": "Catégorie de l'article",
+	 "titleArticle" : "Titre de l'article",
+	 "descriArticle": "Introduction de l'article",
+	 "DefaultMessageUploaderLogo": "Cliquez ici ou glissez-déposez une image",
+
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12305)
+++ src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12306)
@@ -51,6 +51,37 @@
         "apply" 				: "Appliquer",
 
         "saveSuccesful": "Le contenu a été sauvegardé avec succès",
+
+        //datatable
+        "search" : "Rechercher",
+        "searchDesc" : "Rechercher un article, une catégorie...",
+        "title" : "Titre",
+        "author" : "Auteur",
+        "category" : "Catégorie",
+        "datepub" : "Date de Publication",
+        "state" : "Etat",
+        "deleteAction":"Suppression",
+        "confirmDeleteArticle": "Vous \u00eates sur le point de supprimer d\u00e9finitivement un article",
+
+        // zonetoolbox
+        "publish"				: "Publier",
+        "addContent"			: "Ajouter du contenu",
+        "traduire"				: "Traduire",
+        "save": "Sauvegarder",
+        "saveAction": "Sauvegarde",
+        "saveSuccesful": "Le contenu a été sauvegardé avec succès",
+        "saveError": "Une erreur s'est produite lors de la sauvegarde de la page",
+        "emptyZone": "La zone sélectionnée est vide",
+        "howToAddContent": "Cliquez sur &laquo; Ajouter du contenu &raquo;<br/> puis glissez un bloc dans cette zone pour créer une section",
+        "versions": "Versions",
+        "pasteTheSection" : "Coller la section",
+
+        //Article 
+        "catArticle": "Catégorie de l'article",
+        "titleArticle" : "Titre de l'article",
+        "descriArticle": "Introduction de l'article",
+        "DefaultMessageUploaderLogo": "Cliquez ici ou glissez-déposez une image",
+
     },
     "fr-fr": true,
     "fr-ca": true
Index: src/less/imports/news_panel/main.less
===================================================================
--- src/less/imports/news_panel/main.less	(révision 12305)
+++ src/less/imports/news_panel/main.less	(révision 12306)
@@ -258,7 +258,7 @@
 				
 			}
 			
-			.btn.dropdown-toggle , .category-dropdown{
+			.btn.dropdown-toggle{
 				background-color: @grey;
 				text-align: left;
 				min-width: 189px;
@@ -273,6 +273,33 @@
 					border-top-color: #fff;
 				}
 			}
+			.category-dropdown {
+				.btn.dropdown-toggle {
+					min-width: 97%;
+				}
+				.btn-group{
+					width: 100%;
+				}
+				.dropdown-menu{
+					width: 100%;
+					min-width: 205px;
+					background-color: #999;
+					background-clip: padding-box;
+					& li a {
+						display: block;
+						line-height: 30px;
+						margin: 0;
+						padding: 0 0 0 10px;
+						color:#f5f5f5;
+					}
+				
+					& li a:hover, & li a.active {
+						background-color: #ccc;
+						color: @newsColorLight;
+					}
+
+				}
+			}
 			.btn-group.lang .dropdown-menu {
 				min-width: 205px;
 				background-color: #999;
