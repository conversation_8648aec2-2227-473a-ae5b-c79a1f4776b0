Revision: r12428
Date: 2024-06-18 12:11:22 +0300 (tlt 18 Jon 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: correction affichage de contenu article si pas vide

## Files changed

## Full metadata
------------------------------------------------------------------------
r12428 | sraz<PERSON><PERSON>lisoa | 2024-06-18 12:11:22 +0300 (tlt 18 Jon 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/PagePanel/Contents/Zones/Templates/zone.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/PagePanel/Contents/Zones/Views/ContentZoneView.js

News: correction affichage de contenu article si pas vide
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Zones/Templates/zone.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Zones/Templates/zone.html	(révision 12427)
+++ src/js/JEditor/PagePanel/Contents/Zones/Templates/zone.html	(révision 12428)
@@ -1,4 +1,4 @@
-<div class="nothing-to-display <%= empty?'active empty':'' %>" style="z-index: 1;">
+<div class="nothing-to-display emptyzone <%= empty?'active empty':'' %>" style="z-index: 1;">
     <div class="wrapper">
         <div class="icon">
             <span class="icon-file"></span>
Index: src/js/JEditor/PagePanel/Contents/Zones/Views/ContentZoneView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Zones/Views/ContentZoneView.js	(révision 12427)
+++ src/js/JEditor/PagePanel/Contents/Zones/Views/ContentZoneView.js	(révision 12428)
@@ -54,11 +54,11 @@
             if (this.model.children.length > 0){
                 this.$("#content-popup").show();
                 this.$("#titleLinkfirst").show();
-                this.dom[this.cid].emptyZone.removeClass('active');
+                this.dom[this.cid].emptyZone.removeClass('active empty');
 
             }
             else{
-                this.dom[this.cid].emptyZone.addClass('active');
+                this.dom[this.cid].emptyZone.addClass('active empty');
                 this.$("#content-popup").hide();
                 this.$("#titleLinkfirst").hide();
             }
