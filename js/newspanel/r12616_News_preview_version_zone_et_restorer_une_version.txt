Revision: r12616
Date: 2024-07-22 10:27:01 +0300 (lts 22 Jol 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: preview version zone et restorer une version

## Files changed

## Full metadata
------------------------------------------------------------------------
r12616 | sraz<PERSON><PERSON><PERSON>oa | 2024-07-22 10:27:01 +0300 (lts 22 Jol 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Articles/Versions/Views/VersionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/NewsEditorView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/i18n.js

News: preview version zone et restorer une version
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Articles/Versions/Views/VersionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Versions/Views/VersionView.js	(révision 12615)
+++ src/js/JEditor/NewsPanel/Articles/Versions/Views/VersionView.js	(révision 12616)
@@ -160,7 +160,7 @@
             this.model.fetch({
                 success: function(res){
                     var resJson = res.toJSON();
-                    that.newsPanel.childViews.newsEditorView.preview(null, new ZoneModel(resJson.zoneContent));
+                    that.newsPanel.childViews.newsEditorView.preview(null, new ZoneModel(resJson.content));
                 }
             } );
             
Index: src/js/JEditor/NewsPanel/Views/NewsEditorView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 12615)
+++ src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 12616)
@@ -226,14 +226,14 @@
 
             if (this.app.user.can("restore_zone")) {
 
-                this.dom.html.removeClass('no-scroll');
+                this.newsPanel.dom.html.removeClass('no-scroll');
                 this.newsPanel.rightPanelView.hidePanel();
 
-                this.setZone(this.selectedZoneVersion);
+                this.articleEditorView.setZone(this.selectedZoneVersion);
 
                 var that = this;
-                this.listenToOnce(this.currentZone, Events.BackboneEvents.SYNC, function () {
-                    that.render();
+                this.listenToOnce(this.model, Events.BackboneEvents.SYNC, function () {
+                    that.renderArticlePage();
                     that.notify({
                         title: translate("saveAction"),
                         message: translate("previousVersionSuccesful")
@@ -240,6 +240,7 @@
                     });
                 });
 
+                this.model.unsetPublishData();
                 this.model.save();
             }
         },
Index: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12615)
+++ src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12616)
@@ -153,4 +153,5 @@
 	"layoutChanged": "Le layout a été changé avec succès.",
 	"success": "Effectué !",
 	"introPageTitle":"Mes pages",
+	"previousVersionSuccesful":"La zone a été restaurée avec succès et <br/>la page a été sauvegardée",
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 12615)
+++ src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 12616)
@@ -153,4 +153,5 @@
 	 "layoutChanged": "Le layout a été changé avec succès.",
 	 "success": "Effectué !",
 	 "introPageTitle":"Mes pages",
+	 "previousVersionSuccesful":"La zone a été restaurée avec succès et <br/>la page a été sauvegardée",
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12615)
+++ src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12616)
@@ -153,6 +153,7 @@
         "layoutChanged": "Le layout a été changé avec succès.",
         "success": "Effectué !",
         "introPageTitle":"Mes pages",
+        "previousVersionSuccesful":"La zone a été restaurée avec succès et <br/>la page a été sauvegardée",
     },
     "fr-fr": true,
     "fr-ca": true
