Revision: r12577
Date: 2024-07-10 16:18:36 +0300 (lrb 10 Jol 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: correction affichage du bouton savecancel dans les reglages

## Files changed

## Full metadata
------------------------------------------------------------------------
r12577 | srazanandralisoa | 2024-07-10 16:18:36 +0300 (lrb 10 Jol 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/configArticle.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/configCategorie.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/globalConfig.html
   A /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/saveCancel.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/ConfigArticleView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/ConfigCategorieView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/GlobalConfigView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/SaveCancel.js

News: correction affichage du bouton savecancel dans les reglages
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Templates/globalConfig.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/globalConfig.html	(révision 12576)
+++ src/js/JEditor/NewsPanel/Templates/globalConfig.html	(révision 12577)
@@ -1,188 +1,160 @@
-<div id="news-configuration " class="news-rigthpanel scroll-container">
-    <header class="panel-head">
-        <span class="icon icon-params"></span>
-        <h1 class="panel-name"><%=__('config')%></h1>
-    </header>
-    <div class="gallery-template-option galleryStyle panel-content">
 
-        <div class="panel-content active">
-            <div class="panel-content-intro">
-                <%=__("configDesc")%>
+<div class="gallery-template-option galleryStyle">
+    <% if(canchangeLayout){ %>
+    <div class="news-option-margin news-template">
+        <article class="panel-option">
+            <header>
+                <span class="option-name label"><%=__("newsTemplate")%></span>
+                <p class="panel-content-legend"><%=__("newsTemplateLegend")%></p>
+            </header>
+            <div class="option-content">
+                <div class="template-list">
+                        
+                </div>
             </div>
-        </div>
-        <% if(canchangeLayout){ %>
-        <div class="news-option-margin news-template">
-            <article class="panel-option">
-                <header>
-                    <h3 class="option-name"><%=__("newsTemplate")%></h3>
-                    <p class="panel-content-legend"><%=__("newsTemplateLegend")%></p>
-                </header>
-                <div class="option-content">
-                    <div class="template-list">
-                            
-                    </div>
+        </article>
+    </div>
+    <% } %>
+    <div class="panel-option-container animated  mr15">
+        <article class="panel-option">
+            <header>
+                <span  class="option-name label"><%=__("styleDeNews")%></span> 
+                <p class="panel-content-legend"><%=__("styleDeNewsLegend")%></p>
+            </header>
+            <div>
+                <div class="category-content radio-transformed styleDeNews">
+                    <% var _id= _.uniqueId('newsStyle'); %>
+                    <div><span class="effect-radio <%=(newsStyle==0)?'active':''%>" id="<%=_id %>" data-value="0" data-helper="masonry"><span class="helper"><span class="help"><%=__("masonryLegend")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-photocss-column"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
+                    <% var _id= _.uniqueId('newsStyle'); %>
+                    <div><span class="effect-radio <%=(newsStyle==1)?'active':''%>" id="<%=_id %>" data-value="1" data-helper="grid"><span class="helper"><span class="help"><%=__("gridLegend")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-photogrid-block"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
+                    <% var _id= _.uniqueId('newsStyle'); %>
+                    <div><span class="effect-radio <%=(newsStyle==2)?'active':''%>" id="<%=_id %>" data-value="2" data-helper="list"><span class="helper"><span class="help"><%=__("listLegend")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-nav-menu"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
                 </div>
-            </article>
-        </div>
-        <% } %>
-        <div class="panel-option-container animated  mr15">
-            <article class="panel-option">
-                <header>
-                    <h3 class="option-name"></span> <%=__("styleDeNews")%></h3>
-                    <p class="panel-content-legend"><%=__("styleDeNewsLegend")%></p>
-                </header>
-                <div>
-                    <div class="category-content radio-transformed styleDeNews">
-                        <% var _id= _.uniqueId('newsStyle'); %>
-                        <div><span class="effect-radio <%=(newsStyle==0)?'active':''%>" id="<%=_id %>" data-value="0" data-helper="masonry"><span class="helper"><span class="help"><%=__("masonryLegend")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-photocss-column"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
-                        <% var _id= _.uniqueId('newsStyle'); %>
-                        <div><span class="effect-radio <%=(newsStyle==1)?'active':''%>" id="<%=_id %>" data-value="1" data-helper="grid"><span class="helper"><span class="help"><%=__("gridLegend")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-photogrid-block"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
-                        <% var _id= _.uniqueId('newsStyle'); %>
-                        <div><span class="effect-radio <%=(newsStyle==2)?'active':''%>" id="<%=_id %>" data-value="2" data-helper="list"><span class="helper"><span class="help"><%=__("listLegend")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-nav-menu"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
-                    </div>
+            </div>
+        </article>
+        <article class="panel-option" id="formatImage" <%=(newsStyle!=1)?'style="display:none"':''%>>
+            <header>
+                <span class="option-name label"><%=__("FormatImage")%></span>
+                <p class="panel-content-legend"><%=__("FormatImageLegend")%></p>
+            </header>
+            <div>
+                <div class="category-content radio-transformed" id="Radioformatimage">
+                    <% var _id= _.uniqueId('formatImage'); %>
+                    <div><span class="effect-radio <%=(newsFormat=='landscape')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="landscape" data-helper="landscape"><span class="helper"><span class="help"><%=__("landscape")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-transition-alpha"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
+                    <% var _id= _.uniqueId('formatImage'); %>
+                    <div><span class="effect-radio <%=(newsFormat=='portrait')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="portrait" data-helper="portrait"><span class="helper"><span class="help"><%=__("portrait")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-image-portrait"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
+                    <% var _id= _.uniqueId('formatImage'); %>
+                    <div><span class="effect-radio <%=(newsFormat=='square')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="square" data-helper="square"><span class="helper"><span class="help"><%=__("square")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-image-square"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
                 </div>
-            </article>
-            <article class="panel-option" id="formatImage" <%=(newsStyle!=1)?'style="display:none"':''%>>
-                <header>
-                    <h3 class="option-name"></span> <%=__("FormatImage")%></h3>
-                    <p class="panel-content-legend"><%=__("FormatImageLegend")%></p>
-                </header>
-                <div>
-                    <div class="category-content radio-transformed" id="Radioformatimage">
-                        <% var _id= _.uniqueId('formatImage'); %>
-                        <div><span class="effect-radio <%=(newsFormat=='landscape')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="landscape" data-helper="landscape"><span class="helper"><span class="help"><%=__("landscape")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-transition-alpha"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
-                        <% var _id= _.uniqueId('formatImage'); %>
-                        <div><span class="effect-radio <%=(newsFormat=='portrait')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="portrait" data-helper="portrait"><span class="helper"><span class="help"><%=__("portrait")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-image-portrait"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
-                        <% var _id= _.uniqueId('formatImage'); %>
-                        <div><span class="effect-radio <%=(newsFormat=='square')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="square" data-helper="square"><span class="helper"><span class="help"><%=__("square")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-image-square"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
-                    </div>
-                </div>
-            </article>
-        
-        </div>
-       
-        <div class="mr15 news-option-margin news-nbreImage">
-            <article class="panel-option">
-                <header>
-                    <h3 class="option-name"><%=__("newsNombreImage")%></h3>
-                    <p class="panel-content-legend"><%=__("newsNombreImageLegend")%></p>
-                </header>
-                <div class="option-content">
-                    <div class="slider-container"><span class="icon-less"></span><div class="slider"></div><span class="icon-more"></span></div>
-                </div>
-            </article>
-        </div>
-        <div class="panel-option-container animated">
-            <article class="panel-option background-color">
-                <header>
-                    <h3 class="option-name"><%=__("newsStyleAffichage")%></h3>
-                    <p class="panel-content-legend"><%=__("newsStyleAffichageDesc")%></p>
-                </header>
-                <div class="option-content colors">
-                    <%  var _id=_.uniqueId('newsStyleAffichage');
-                    %>
-                        <input type="radio" class="select-box" name="AffichageCarrouselStyle" value="1" id="<%=_id %>" <%=(newsStyleAff==1)?'checked':''%>>
-                           <label for="<%=_id %>">
-                            <div class="wrapper">
-                                <div class="horizontal-wrap">
-                                    <span class="icon-wrapper">
-                                        <span class="icon-collection-style1"></span>
-                                    </span>
-                                    <span class="name"><%=__("Style")%> 1</span>
-                                    <span class="desc"><%=__("DescStyle1")%></span>
-                                </div>
+            </div>
+        </article>
+    
+    </div>
+    
+    <div class="mr15 news-option-margin news-nbreImage">
+        <article class="panel-option">
+            <header>
+                <span class="option-name label"><%=__("newsNombreImage")%></span>
+                <p class="panel-content-legend"><%=__("newsNombreImageLegend")%></p>
+            </header>
+            <div class="option-content">
+                <div class="slider-container"><span class="icon-less"></span><div class="slider"></div><span class="icon-more"></span></div>
+            </div>
+        </article>
+    </div>
+    <div class="panel-option-container animated">
+        <article class="panel-option background-color">
+            <header>
+                <span class="option-name label"><%=__("newsStyleAffichage")%></span>
+                <p class="panel-content-legend"><%=__("newsStyleAffichageDesc")%></p>
+            </header>
+            <div class="option-content colors">
+                <%  var _id=_.uniqueId('newsStyleAffichage');
+                %>
+                    <input type="radio" class="select-box" name="AffichageCarrouselStyle" value="1" id="<%=_id %>" <%=(newsStyleAff==1)?'checked':''%>>
+                        <label for="<%=_id %>">
+                        <div class="wrapper">
+                            <div class="horizontal-wrap">
+                                <span class="icon-wrapper">
+                                    <span class="icon-collection-style1"></span>
+                                </span>
+                                <span class="name"><%=__("Style")%> 1</span>
+                                <span class="desc"><%=__("DescStyle1")%></span>
                             </div>
-                        </label>
-                        
-                        <%  var _id=_.uniqueId('newsStyleAffichage');%>
-                        <input type="radio" class="select-box" name="AffichageCarrouselStyle" value="2" id="<%=_id %>" <%=(newsStyleAff==2)?'checked':''%>>
-                           <label for="<%=_id %>">
-                            <div class="wrapper">
-                                <div class="horizontal-wrap">
-                                    <span class="icon-wrapper">
-                                        <span class="icon-collection-style2"></span>
-                                    </span>
-                                    <span class="name"><%=__("Style")%> 2</span>
-                                    <span class="desc"><%=__("DescStyle2")%></span>
-                                </div>
+                        </div>
+                    </label>
+                    
+                    <%  var _id=_.uniqueId('newsStyleAffichage');%>
+                    <input type="radio" class="select-box" name="AffichageCarrouselStyle" value="2" id="<%=_id %>" <%=(newsStyleAff==2)?'checked':''%>>
+                        <label for="<%=_id %>">
+                        <div class="wrapper">
+                            <div class="horizontal-wrap">
+                                <span class="icon-wrapper">
+                                    <span class="icon-collection-style2"></span>
+                                </span>
+                                <span class="name"><%=__("Style")%> 2</span>
+                                <span class="desc"><%=__("DescStyle2")%></span>
                             </div>
-                        </label>
-                        
-                        <%  var _id=_.uniqueId('newsStyleAffichage');%>
-                        <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="3" id="<%=_id %>" <%=(newsStyleAff==3)?'checked':''%>>
-                           <label for="<%=_id %>">
-                            <div class="wrapper">
-                                <div class="horizontal-wrap">
-                                    <span class="icon-wrapper">
-                                        <span class="icon-collection-style3"></span>
-                                    </span>
-                                    <span class="name"><%=__("Style")%> 3</span>
-                                    <span class="desc"><%=__("DescStyle3")%></span>
-                                </div>
+                        </div>
+                    </label>
+                    
+                    <%  var _id=_.uniqueId('newsStyleAffichage');%>
+                    <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="3" id="<%=_id %>" <%=(newsStyleAff==3)?'checked':''%>>
+                        <label for="<%=_id %>">
+                        <div class="wrapper">
+                            <div class="horizontal-wrap">
+                                <span class="icon-wrapper">
+                                    <span class="icon-collection-style3"></span>
+                                </span>
+                                <span class="name"><%=__("Style")%> 3</span>
+                                <span class="desc"><%=__("DescStyle3")%></span>
                             </div>
-                        </label>
-        
-                        <%  var _id=_.uniqueId('newsStyleAffichage');%>
-                        <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="4" id="<%=_id %>" <%=(newsStyleAff==4)?'checked':''%>>
-                           <label for="<%=_id %>">
-                            <div class="wrapper">
-                                <div class="horizontal-wrap">
-                                    <span class="icon-wrapper">
-                                        <span class="icon-collection-style4"></span>
-                                    </span>
-                                    <span class="name"><%=__("Style")%> 4</span>
-                                    <span class="desc"><%=__("DescStyle4")%></span>
-                                </div>
+                        </div>
+                    </label>
+    
+                    <%  var _id=_.uniqueId('newsStyleAffichage');%>
+                    <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="4" id="<%=_id %>" <%=(newsStyleAff==4)?'checked':''%>>
+                        <label for="<%=_id %>">
+                        <div class="wrapper">
+                            <div class="horizontal-wrap">
+                                <span class="icon-wrapper">
+                                    <span class="icon-collection-style4"></span>
+                                </span>
+                                <span class="name"><%=__("Style")%> 4</span>
+                                <span class="desc"><%=__("DescStyle4")%></span>
                             </div>
-                        </label>
-        
-                        <%  var _id=_.uniqueId('newsStyleAffichage');%>
-                        <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="5" id="<%=_id %>" <%=(newsStyleAff==5)?'checked':''%>>
-                           <label for="<%=_id %>">
-                            <div class="wrapper">
-                                <div class="horizontal-wrap">
-                                    <span class="icon-wrapper">
-                                        <span class="icon-collection-style5"></span>
-                                    </span>
-                                    <span class="name"><%=__("Style")%> 5</span>
-                                    <span class="desc"><%=__("DescStyle5")%></span>
-                                </div>
+                        </div>
+                    </label>
+    
+                    <%  var _id=_.uniqueId('newsStyleAffichage');%>
+                    <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="5" id="<%=_id %>" <%=(newsStyleAff==5)?'checked':''%>>
+                        <label for="<%=_id %>">
+                        <div class="wrapper">
+                            <div class="horizontal-wrap">
+                                <span class="icon-wrapper">
+                                    <span class="icon-collection-style5"></span>
+                                </span>
+                                <span class="name"><%=__("Style")%> 5</span>
+                                <span class="desc"><%=__("DescStyle5")%></span>
                             </div>
-                        </label>
-        
-                        <%  var _id=_.uniqueId('newsStyleAffichage');%>
-                        <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="6" id="<%=_id %>" <%=(newsStyleAff==6)?'checked':''%>>
-                           <label for="<%=_id %>">
-                            <div class="wrapper">
-                                <div class="horizontal-wrap">
-                                    <span class="icon-wrapper">
-                                        <span class="icon-collection-style6"></span>
-                                    </span>
-                                    <span class="name"><%=__("Style")%> 6</span>
-                                    <span class="desc"><%=__("DescStyle6")%></span>
-                                </div>
+                        </div>
+                    </label>
+    
+                    <%  var _id=_.uniqueId('newsStyleAffichage');%>
+                    <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="6" id="<%=_id %>" <%=(newsStyleAff==6)?'checked':''%>>
+                        <label for="<%=_id %>">
+                        <div class="wrapper">
+                            <div class="horizontal-wrap">
+                                <span class="icon-wrapper">
+                                    <span class="icon-collection-style6"></span>
+                                </span>
+                                <span class="name"><%=__("Style")%> 6</span>
+                                <span class="desc"><%=__("DescStyle6")%></span>
                             </div>
-                        </label>
-                        
-                </div>
-            </article>
-        </div>
+                        </div>
+                    </label>
+                    
+            </div>
+        </article>
     </div>
-    
 </div>
-<footer class="foot">
-    <div class="button-group save-or-cancel" >
-        <a class="button cancel" data-action="cancel">
-            <span class="wrapper">
-                <span class="icon"></span>
-                <span class="text"><%= __("cancel")%></span>
-            </span>
-        </a>
-        <a class="button save" data-action="save">
-            <span class="wrapper">
-                <span class="icon"></span>
-                <span class="text"><%= __("apply")%></span>
-            </span>
-        </a>
-    </div>
-</footer>
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Templates/saveCancel.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/saveCancel.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Templates/saveCancel.html	(révision 12577)
@@ -0,0 +1,24 @@
+<header class="panel-head">
+    <span class="icon icon-params"></span>
+    <span class="panel-name"><%=title%></span> 
+</header>
+<div class="panel-content-intro">
+    <%=description%>
+</div>
+<div class="panel-content"></div>
+<footer class="foot">
+    <div class="button-group save-or-cancel" >
+        <a class="button cancel" data-action="cancel">
+            <span class="wrapper">
+                <span class="icon"></span>
+                <span class="text"><%= __("cancel")%></span>
+            </span>
+        </a>
+        <a class="button save" data-action="save">
+            <span class="wrapper">
+                <span class="icon"></span>
+                <span class="text"><%= __("apply")%></span>
+            </span>
+        </a>
+    </div>
+</footer>
Index: src/js/JEditor/NewsPanel/Views/SaveCancel.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/SaveCancel.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Views/SaveCancel.js	(révision 12577)
@@ -0,0 +1,73 @@
+define(["underscore", "jquery", "JEditor/Commons/Ancestors/Views/View", "text!../Templates/saveCancel.html", "i18n!JEditor/NewsPanel/nls/i18n"], function(_, $, View, template, translate) {
+    var SaveCancel = View.extend({
+        events: {
+            "click footer.foot .button-group .button[data-action]": "onButtonClick"
+        },
+        tagName: 'div',
+        className: "news-tabbed-view",
+        initialize: function() {
+            View.prototype.initialize.apply(this, arguments);
+            this.content = null;
+            this.title = null;
+            this.description = null;
+            this.scrollTimeout = null;
+            this.menuTemplate = this.buildTemplate(template, translate);
+            $(window).on("scroll.NewsTabbedView."+this.cid, _.bind(this.onScroll, this));
+        },
+        onScroll:function(){
+            var that=this;
+            if(this.scrollTimeout!==null){
+                window.clearTimeout(this.scrollTimeout);
+            }
+            this.scrollTimeout = window.setTimeout(function () {
+                that.updateHeight();
+            },200);
+        },
+        render: function() {
+            this.$el.empty();
+            this.$el.html(this.menuTemplate({
+                title: this.title,
+                description:this.description
+            }));
+            if(this.content!==null){
+                this.$('.panel-content').html(this.content)
+                this.updateHeight();
+            }
+        },
+        remove:function(){
+            View.prototype.remove.call(this,arguments);
+            $(window).off("scroll.NewsTabbedView."+this.cid);
+            return this;
+        },
+        setTitle: function(title) {
+            this.title = title;
+        },
+        addContent: function(content) {
+            this.content = content;
+        },
+        setDescription: function(description) {
+            this.description = description;
+        },
+        updateHeight: function() {
+            var win = $(window);
+            var height = win.height() - (this.$el.offset().top - win.scrollTop());
+            if (this.currentPane !== null)
+                this.$el.height(height);
+            this.updateScrollables();
+        },
+        onButtonClick: function(event) {
+            var action = event.currentTarget.dataset.action;
+            if (this[action]) {
+                this[action]();
+            }
+        },
+        save: function() {
+            this.trigger("save", this);
+        },
+        cancel: function() {
+            this.trigger("cancel", this);
+        }
+     
+    });
+    return SaveCancel;
+});

Property changes on: src/js/JEditor/NewsPanel/Views/SaveCancel.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/NewsPanel/Templates/configArticle.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/configArticle.html	(révision 12576)
+++ src/js/JEditor/NewsPanel/Templates/configArticle.html	(révision 12577)
@@ -1,40 +1,24 @@
-<div id="meta-configuration" class="meta-configuration scrollbar-classic">
-    <header class="panel-head">
-        <span class="icon icon-params"></span>
-        <h1 class="panel-name"><%=__('config')%></h1>
-    </header>
-    <div class="panel-content active">
-        <div class="panel-content-intro">
-            <%= __("configDescArt")%>
-        </div>
-    </div>
-    <div class="option-content ">
-       <label for="meta-title">
+
+<div class="panel-content-content">
+    <label for="meta-title">
         <span class="icon icon-html"></span>
-        <span><%=__('metaTitleLabel')%></span>
-       </label>
-       <input type="text" name="metaTitle" value="<%=metaTitle%>"  id="meta-title">
-
-       <label for="url">
+        <span class="label"><%=__('metaTitleLabel')%></span>
+    </label>
+    <input type="text" name="metaTitle" value="<%=metaTitle%>"  id="meta-title">
+</div>
+<div class="panel-content-content">
+    <label for="meta-description">
+        <span class="icon icon-html"></span>
+        <span class="label" ><%=__('metaDescLabel')%></span>
+    </label>
+    <textarea name="metaDescription" id="meta-description" cols="20" rows="10" disabled ><%=metaDescription%></textarea>
+</div>
+<div class="panel-content-content">
+    <label for="url">
         <span class="icon icon-params"></span>
-        <span><%=__('urlArtLabel')%></span>
-       </label>
-        <p><a href="/news/<%=url%>">/news/<%=url%>.php</a></p>
-    </div>
+        <span class="label"><%=__('urlArtLabel')%></span>
+    </label>
+    <p><a href="/news/<%=url%>">/news/<%=url%>.php</a></p>
 </div>
-<footer class="foot">
-    <div class="button-group save-or-cancel" >
-        <a class="button cancel" data-action="cancel">
-            <span class="wrapper">
-                <span class="icon"></span>
-                <span class="text"><%= __("cancel")%></span>
-            </span>
-        </a>
-        <a class="button save" data-action="save">
-            <span class="wrapper">
-                <span class="icon"></span>
-                <span class="text"><%= __("apply")%></span>
-            </span>
-        </a>
-    </div>
-</footer>
\ No newline at end of file
+
+
Index: src/js/JEditor/NewsPanel/Templates/configCategorie.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/configCategorie.html	(révision 12576)
+++ src/js/JEditor/NewsPanel/Templates/configCategorie.html	(révision 12577)
@@ -1,45 +1,24 @@
-<div id="meta-configuration" class="meta-configuration scrollbar-classic">
-    <header class="panel-head">
-        <span class="icon icon-params"></span>
-        <h1 class="panel-name"><%=__('config')%></h1>
-    </header>
-    <div class="panel-content active">
-        <div class="panel-content-intro">
-            <%= __("configDesc")%>
-        </div>
-    </div>
-    <div class="option-content ">
-       <label for="meta-title">
-        <span class="icon icon-html"></span>
-        <span><%=__('metaTitleLabel')%></span>
-       </label>
-       <input type="text" name="metaTitle" value="<%=metaTitle%>"  id="meta-title" disabled>
 
-       <label for="meta-description">
+<div class="panel-content-content">
+    <label for="meta-title">
+    <span class="icon icon-html"></span>
+    <span class="label" ><%=__('metaTitleLabel')%></span>
+</label>
+<input type="text" name="metaTitle" value="<%=metaTitle%>"  id="meta-title" disabled>
+</div>
+<div class="panel-content-content">
+    <label for="meta-description">
         <span class="icon icon-html"></span>
-        <span><%=__('metaDescLabel')%></span>
-       </label>
-       <textarea name="metaDescription" id="meta-description" cols="20" rows="10" disabled ><%=metaDescription%></textarea>
-       <label for="url">
+        <span class="label" ><%=__('metaDescLabel')%></span>
+    </label>
+    <textarea name="metaDescription" id="meta-description" cols="20" rows="10" disabled ><%=metaDescription%></textarea>
+    
+</div>
+<div class="panel-content-content">
+    <label for="url">
         <span class="icon icon-params"></span>
-        <span><%=__('urlLabel')%></span>
-       </label>
-        <p><a href="/news/<%=url%>">/news/<%=url%>.php</a></p>
-    </div>
+        <span class="label" ><%=__('urlLabel')%></span>
+    </label>
+    <p><a href="/news/<%=url%>">/news/<%=url%>.php</a></p>
+        
 </div>
-<footer class="foot">
-    <div class="button-group save-or-cancel" >
-        <a class="button cancel" data-action="cancel">
-            <span class="wrapper">
-                <span class="icon"></span>
-                <span class="text"><%= __("cancel")%></span>
-            </span>
-        </a>
-        <a class="button save" data-action="save">
-            <span class="wrapper">
-                <span class="icon"></span>
-                <span class="text"><%= __("apply")%></span>
-            </span>
-        </a>
-    </div>
-</footer>
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Views/ConfigArticleView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ConfigArticleView.js	(révision 12576)
+++ src/js/JEditor/NewsPanel/Views/ConfigArticleView.js	(révision 12577)
@@ -5,17 +5,18 @@
 	"JEditor/Commons/Ancestors/Views/View",
     "text!JEditor/NewsPanel/Templates/configArticle.html",
     "JEditor/App/Views/RightPanelView",
+    "JEditor/NewsPanel/Views/SaveCancel",
     "i18n!JEditor/NewsPanel/nls/i18n",
-], function ($, _, Events, View, template, RightPanelView, translate) {
+], function ($, _, Events, View, template, RightPanelView, SaveCancel, translate) {
     
     /**
      * 
      * @type ConfigArticleView
      */
-    var ConfigArticleView = View.extend({
+    var ConfigArticleView = SaveCancel.extend({
 
         tagName: 'div',
-        className: 'right-panel-nav',
+        className: 'news-right-panel',
         _transitionDelay: 250,
         rendered: false,
         _currentCid: null,
@@ -41,8 +42,14 @@
             View.apply(this, arguments);
         },
         initialize: function () {
-            this.template = this.buildTemplate(template, translate);
+            SaveCancel.prototype.initialize.apply(this, arguments);
+            this.contenttemplate = this.buildTemplate(template, translate);
             this.listenTo(this.rightPanelView, 'overlayClick', this.cancel);
+            this.setTitle(translate('config'));
+            this.setDescription(translate('configDescArt'));
+            var params = this.model.toJSON();
+            var content = this.contenttemplate(params)
+            this.addContent(content);
             this.rightPanelView.addContent(this);
             this.currentLang = this.options.currentLang;
             return this;
@@ -81,8 +88,7 @@
          */
         render: function () {
             this.undelegateEvents();
-            var params = this.model.toJSON();
-            this.$el.html(this.template(params));
+            this._super();
             this.delegateEvents();
             
             return this;
Index: src/js/JEditor/NewsPanel/Views/ConfigCategorieView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ConfigCategorieView.js	(révision 12576)
+++ src/js/JEditor/NewsPanel/Views/ConfigCategorieView.js	(révision 12577)
@@ -5,17 +5,18 @@
 	"JEditor/Commons/Ancestors/Views/View",
     "text!JEditor/NewsPanel/Templates/configCategorie.html",
     "JEditor/App/Views/RightPanelView",
+    "JEditor/NewsPanel/Views/SaveCancel",
     "i18n!JEditor/NewsPanel/nls/i18n",
-], function ($, _, Events, View, template, RightPanelView, translate) {
+], function ($, _, Events, View, template, RightPanelView,  SaveCancel, translate) {
     
     /**
      * 
      * @type ConfigCategorieView
      */
-    var ConfigCategorieView = View.extend({
+    var ConfigCategorieView = SaveCancel.extend({
 
         tagName: 'div',
-        className: 'right-panel-nav',
+        className: 'news-right-panel',
         _transitionDelay: 250,
         rendered: false,
         _currentCid: null,
@@ -41,9 +42,15 @@
             View.apply(this, arguments);
         },
         initialize: function () {
-            this.template = this.buildTemplate(template, translate);
+            SaveCancel.prototype.initialize.apply(this, arguments);
+            this.contenttemplate = this.buildTemplate(template, translate);
             this.listenTo(this.rightPanelView, 'overlayClick', this.cancel);
             this.rightPanelView.addContent(this);
+            this.setTitle(translate('config'));
+            this.setDescription(translate('configDesc'));
+            var params = this.model.lang[this.options.currentLang.id]
+            var content = this.contenttemplate(params)
+            this.addContent(content);
             this.currentLang = this.options.currentLang;
             return this;
         },
@@ -80,12 +87,11 @@
          * Rendu de la view
          */
         render: function () {
-            this.undelegateEvents();
             if (!this.model.lang[this.options.currentLang.id]) {
                return false;
             }
-            var params = this.model.lang[this.options.currentLang.id]
-            this.$el.html(this.template(params));
+            this.undelegateEvents();
+            this._super();
             this.delegateEvents();
             
             return this;
Index: src/js/JEditor/NewsPanel/Views/GlobalConfigView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/GlobalConfigView.js	(révision 12576)
+++ src/js/JEditor/NewsPanel/Views/GlobalConfigView.js	(révision 12577)
@@ -7,18 +7,18 @@
     "JEditor/Commons/Pages/Models/PageCollection",
     "text!JEditor/NewsPanel/Templates/globalConfig.html",
     "JEditor/PagePanel/Views/TemplateDropDownView",
-
+    "JEditor/NewsPanel/Views/SaveCancel",
     "i18n!JEditor/NewsPanel/nls/i18n",
-], function ($, _, Events, View, NewsConfig, PageCollection, template,TemplateDropDown, translate) {
+], function ($, _, Events, View, NewsConfig, PageCollection, template,TemplateDropDown, SaveCancel, translate) {
     
     /**
      * 
      * @type GlobalConfigView
      */
-    var GlobalConfigView = View.extend({
+    var GlobalConfigView = SaveCancel.extend({
 
         tagName: 'div',
-        className: 'right-panel-nav',
+        className: 'news-right-panel',
         _transitionDelay: 250,
         rendered: false,
         _currentCid: null,
@@ -65,19 +65,31 @@
             this.model.newsFormat=value;
         },
         initialize: function() {
+            SaveCancel.prototype.initialize.apply(this, arguments);
+            this.contenttemplate = this.buildTemplate(template, translate);
             if (this.options.newsPanel)
                 this.newsPanel = this.options.newsPanel;
             delete this.options.newsPanel;
-            this._super();
+
             this.model = NewsConfig.getInstance();
             this.pageCollection = PageCollection.getInstance();
             var defaultLayout = this.pageCollection.findWhere({type:"template",lang:this.options.language.id});
             this.layoutsDropDown = new TemplateDropDown({collection: this.pageCollection,_default:defaultLayout, language:this.options.language});
             this.layoutsDropDown.filter({type:'template',lang:this.options.language.id});
-       
-            this.template = this.buildTemplate(template, translate);
             this.model.layout = this.layoutsDropDown.current.name;
             this.listenTo(this.layoutsDropDown, Events.ChoiceEvents.SELECT, this.onLayoutSelect);
+           
+            this.setTitle(translate('config'));
+            this.setDescription(translate('configGlobalDesc'));
+            var templateVars = {
+                newsStyle:this.model.newsStyle,
+                newsFormat:this.model.newsFormat,
+                newsNbArticle:this.model.newsNbArticle,
+                newsStyleAff:this.model.newsStyleAff,
+                canchangeLayout: this.app.user.can("change_news_layout")
+            };
+            var content = this.contenttemplate(templateVars)
+            this.addContent(content);
         }, 
         onLayoutSelect: function(view, templatePage) {
             this.model.layout = templatePage.name;
@@ -98,14 +110,7 @@
          */
         render: function () {
             this.undelegateEvents();
-            var templateVars = {
-                newsStyle:this.model.newsStyle,
-                newsFormat:this.model.newsFormat,
-                newsNbArticle:this.model.newsNbArticle,
-                newsStyleAff:this.model.newsStyleAff,
-                canchangeLayout: this.app.user.can("change_news_layout")
-            };
-            this.$el.html(this.template(templateVars));
+            this._super();
             this.$('.news-nbreImage .slider').slider({
                 range: "min",
                 value: this.model.newsNbArticle,
@@ -115,6 +120,7 @@
             });
             this.scrollables();
             this.$('.template-list').append(this.layoutsDropDown.render().el)
+            this.$('.layouts.dropup').removeClass('dropup').addClass('dropdown');
             this.delegateEvents();
             
             return this;
