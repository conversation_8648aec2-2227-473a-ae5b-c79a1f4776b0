Revision: r12623
Date: 2024-07-24 12:03:37 +0300 (lrb 24 Jol 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: update non category apres annulation de modification

## Files changed

## Full metadata
------------------------------------------------------------------------
r12623 | s<PERSON><PERSON><PERSON><PERSON><PERSON> | 2024-07-24 12:03:37 +0300 (lrb 24 Jol 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Models/Article.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/NewsPanel.js

News: update non category apres annulation de modification
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Models/Article.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Article.js	(révision 12622)
+++ src/js/JEditor/NewsPanel/Models/Article.js	(révision 12623)
@@ -90,6 +90,8 @@
                         },
                         cancel: function() {
                             this.set('category', this.lastState.category);
+                            this.categoryModel = null;
+                            this.getCategoryModel();
                             this.set('title', this.lastState.title);
                             this.set('introduction', this.lastState.introduction);
                             this.trigger(Events.ArticleEvents.ARTICLE_CANCEL, this);
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12622)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12623)
@@ -23,7 +23,8 @@
     "JEditor/NewsPanel/Articles/Versions/Views/VersionsCollectionView",
     "JEditor/PagePanel/Contents/Blocks/Blocks",
     "i18n!./nls/i18n",
-    "jqueryPlugins/affix"
+    "jqueryPlugins/affix",
+    "ckeditor"
 ],
         function (
             $,
@@ -200,6 +201,7 @@
                                             article.getCategoryModel();
                                         }
                                     });
+                                    this.currentArticle = null;
                                 },
                                 onArticleAdd: function(article) {
                                     this.updateCategoryCounter(article, 1);
@@ -514,6 +516,8 @@
                                     return (document.documentElement.clientHeight < 866 ? 90 : 230);
                                 },
                                 onAddCategorieClick : function() {
+                                    this.currentCategorie = null;
+                                    this.currentArticle = null;
                                     this.childViews.newsEditorView.renderAddCategorie()
                                 },
                                 onAddArticleClick : function() {
