Revision: r13924
Date: 2025-02-25 11:26:11 +0300 (tlt 25 Feb 2025) 
Author: frahajanirina 

## Commit message
Wishlist IDEO3.2: Bug nombre article admin

## Files changed

## Full metadata
------------------------------------------------------------------------
r13924 | frahajanirina | 2025-02-25 11:26:11 +0300 (tlt 25 Feb 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Models/Categorie.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/NewsPanel.js

Wishlist IDEO3.2: Bug nombre article admin
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Models/Categorie.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Categorie.js	(révision 13923)
+++ src/js/JEditor/NewsPanel/Models/Categorie.js	(révision 13924)
@@ -85,8 +85,6 @@
                         };
                         // Parcourir les langues disponibles et créer les objets correspondants
                         _.each(item.lang, function(langData, langKey) {
-                             var existingLang = (this.get('lang'))?this.get('lang')[langKey]:null;
-                             var nbArticle = existingLang ? existingLang.nbArticle : 0;
                             category.lang[langKey] = new CategorieLang({
                                 title: langData.title,
                                 description: langData.description,
@@ -93,7 +91,7 @@
                                 metaTitle: langData.metaTitle,
                                 metaDescription: langData.metaDescription,
                                 url: langData.url,
-                                nbArticle: nbArticle
+                                nbArticle: langData.nbArticle
                             });
                         }, this);
                         return category;
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 13923)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 13924)
@@ -107,11 +107,13 @@
                                     this.listenToOnce(this.articles, "sync", onLoaded);
                                     this.listenToOnce(this.newsConfig, "sync", onLoaded);
 
-                                    this.listenTo(this.articles, 'add', this.onArticleAdd);
-                                    this.listenTo(this.articles, 'remove', this.onArticleRemove);
-                                    this.listenTo(this.articles, 'change:category', this.onArticleCategoryChange);
-                                    this.listenTo(this.articles, 'add', this.updateTranslationCount);
-                                    this.listenTo(this.articles, 'remove', this.updateTranslationCount);
+                                    this.listenToOnce(this.articles, 'sync', function () {
+                                        this.listenTo(this.articles, 'add', this.onArticleAdd);
+                                        this.listenTo(this.articles, 'remove', this.onArticleRemove);
+                                        this.listenTo(this.articles, 'change:category', this.onArticleCategoryChange);
+                                        this.listenTo(this.articles, 'add', this.updateTranslationCount);
+                                        this.listenTo(this.articles, 'remove', this.updateTranslationCount);
+                                    }.bind(this));
                                     this.categories.on('remove', this.onCategoryRemoved.bind(this));
 
                                     this.categories.fetch();
