Revision: r12373
Date: 2024-06-06 10:49:34 +0300 (lkm 06 Jon 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: ajout config et choix layout correction affichage

## Files changed

## Full metadata
------------------------------------------------------------------------
r12373 | srazana<PERSON><PERSON>oa | 2024-06-06 10:49:34 +0300 (lkm 06 Jon 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/NewsConfig.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/globalConfig.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/GlobalConfigView.js

News: ajout config et choix layout correction affichage
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Models/NewsConfig.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/NewsConfig.js	(révision 12372)
+++ src/js/JEditor/NewsPanel/Models/NewsConfig.js	(révision 12373)
@@ -20,6 +20,17 @@
                             throw new TypeError("NewsConfig est un singleton et ne peut être instancié");
                         Model.apply(this, arguments);
                     },
+                    initialize: function() {
+                        this._super();    
+                        this.lastState = this.toJSON();
+                        this.on(Events.BackboneEvents.SYNC, this._onSync);
+                    },
+                    _onSync: function(model, response, options) {
+                        this.lastState = this.toJSON();
+                    },
+                    cancel: function() {
+                        this.set(this.lastState);
+                    },
     
                 });
             NewsConfig.SetAttributes(['newsStyle', 'newsFormat', 'newsStyleAff', 'newsNbArticle', 'layout']);
Index: src/js/JEditor/NewsPanel/Templates/globalConfig.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/globalConfig.html	(révision 12372)
+++ src/js/JEditor/NewsPanel/Templates/globalConfig.html	(révision 12373)
@@ -42,7 +42,7 @@
                     </div>
                 </div>
             </article>
-            <article class="panel-option" id="formatImage" <%=(newsStyle==0)?'style="display:none"':''%>>
+            <article class="panel-option" id="formatImage" <%=(newsStyle!=1)?'style="display:none"':''%>>
                 <header>
                     <h3 class="option-name"></span> <%=__("FormatImage")%></h3>
                     <p class="panel-content-legend"><%=__("FormatImageLegend")%></p>
Index: src/js/JEditor/NewsPanel/Views/GlobalConfigView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/GlobalConfigView.js	(révision 12372)
+++ src/js/JEditor/NewsPanel/Views/GlobalConfigView.js	(révision 12373)
@@ -65,6 +65,9 @@
             this.model.newsFormat=value;
         },
         initialize: function() {
+            if (this.options.newsPanel)
+                this.newsPanel = this.options.newsPanel;
+            delete this.options.newsPanel;
             this._super();
             this.model = NewsConfig.getInstance();
             this.pageCollection = PageCollection.getInstance();
@@ -118,7 +121,7 @@
         },
         save: function() {
             this.model.save();
-            this.trigger(Events.GlobalConfigViewEvents.SAVE, this);
+            this.newsPanel.hidesGlobalConfigView();
         },
         /**
          * annule les changements éfectuées depuis l'ouverture
@@ -125,16 +128,10 @@
          */
         cancel: function() {
             this.model.cancel();
-          this.trigger(Events.GlobalConfigViewEvents.CANCEL, this);
+            this.newsPanel.hidesGlobalConfigView();
         }
         
     });
-    Events.extend({
-        GlobalConfigViewEvents: {
-            SAVE: 'save',
-            CANCEL: 'cancel'
-        }
-    });
     
     return GlobalConfigView;
 });
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12372)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12373)
@@ -112,6 +112,7 @@
                                     });
                                     this.childViews.globalConfigView = new GlobalConfigView({
                                         language : this.currentLang,
+                                        newsPanel : this
                                     });
                         
                                     this.childViews.categorieList = new CategorieCollectionView({
@@ -260,9 +261,6 @@
                                     
                                     this.listenTo(this.childViews.categorieList, 'render', this._scrollbar);
                                     this.listenTo(this.childViews.newsEditorView, Events.NewsEditorViewEvents.SHOWRIGHTPANEL, this.showGlobalConfigView);
-                                    this.listenTo(this.childViews.globalConfigView, Events.GlobalConfigViewEvents.CANCEL, this.hidesGlobalConfigView);
-                                    this.listenTo(this.childViews.globalConfigView, Events.GlobalConfigViewEvents.SAVE, this.hidesGlobalConfigView);
-                                    
                                     this.render();
                                 },
                                 /**
@@ -308,12 +306,6 @@
                                     });
                                     return this;
                                 },
-
-                                _onParams:function (event){
-                                    if (this.currentArticle) {
-                                        
-                                    }
-                                },
                                 /**
                                  * Affiche le panel de droite en inserant les versions de la zone courante
                                  * 
