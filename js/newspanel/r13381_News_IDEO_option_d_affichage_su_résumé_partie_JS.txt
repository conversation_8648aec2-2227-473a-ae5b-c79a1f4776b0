Revision: r13381
Date: 2024-10-25 09:48:19 +0300 (zom 25 Okt 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News IDEO: option d'affichage su résumé (partie JS)

## Files changed

## Full metadata
------------------------------------------------------------------------
r13381 | srazanandralisoa | 2024-10-25 09:48:19 +0300 (zom 25 Okt 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Models/NewsConfig.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/globalConfig.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/GlobalConfigView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/i18n.js

News IDEO: option d'affichage su résumé (partie JS)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Models/NewsConfig.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/NewsConfig.js	(révision 13380)
+++ src/js/JEditor/NewsPanel/Models/NewsConfig.js	(révision 13381)
@@ -15,7 +15,8 @@
                         newsNbArticle: 3,
                         layout: null,
                         metaTitle:{},
-                        metaDescription:{}
+                        metaDescription:{},
+                        showResume:false
                     },
                     constructor: function () {
                         if (arguments.callee.caller !== NewsConfig.getInstance)
@@ -45,12 +46,13 @@
                             newsNbArticle: parseInt(data.newsNbArticle),
                             layout: data.layout,
                             metaTitle:data.metaTitle,
-                            metaDescription :  data.metaDescription
+                            metaDescription :  data.metaDescription,
+                            showResume: data.showResume
                         }
                     }
     
                 });
-            NewsConfig.SetAttributes(['newsStyle', 'newsFormat', 'newsStyleAff', 'newsNbArticle', 'layout','metaTitle','metaDescription']);
+            NewsConfig.SetAttributes(['newsStyle', 'newsFormat', 'newsStyleAff', 'newsNbArticle', 'layout','metaTitle','metaDescription','showResume']);
             /**
              * @static
              * @private
Index: src/js/JEditor/NewsPanel/Templates/globalConfig.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/globalConfig.html	(révision 13380)
+++ src/js/JEditor/NewsPanel/Templates/globalConfig.html	(révision 13381)
@@ -76,6 +76,25 @@
             </div>
         </article>
     </div>
+    <div class=" news-option-margin news-resume">
+        <% var _id=_.uniqueId('showResume') %>
+        <article class="panel-option">
+            <header>
+                <span class="option-name label"><%=__("resume")%></span>
+                <p class="panel-content-legend"><%=__("resumeLegend")%></p>
+            </header>
+            <div class="option-content">
+                <input type="checkbox" class="blue-bg show-resume-button" name="show-resume-button" id="<%=_id %>" <%= showResume ? 'checked="checked"' : '' %> >
+           <label for="<%=_id %>">
+               <span class="checkbox-wrapper">
+                   <span class="icon-unchecked"></span>
+                   <span class="icon-checked"></span>
+               </span>
+               <span class="text"><%=__("resumecheckLegend")%></span>
+           </label>
+            </div>
+        </article>
+    </div>
     <div class="panel-option-container animated">
         <article class="panel-option background-color">
             <header>
Index: src/js/JEditor/NewsPanel/Views/GlobalConfigView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/GlobalConfigView.js	(révision 13380)
+++ src/js/JEditor/NewsPanel/Views/GlobalConfigView.js	(révision 13381)
@@ -31,8 +31,13 @@
             'change input[type="radio"].select-box': '_onStyleAffichageChange',
             'change input#meta-title': 'setMetaTitle',
             'change textarea#meta-description': 'setMetaDesc',
-      
+            'change input[type="checkbox"].show-resume-button': '_onChangeShowResume',
+            
         },
+        _onChangeShowResume:function(event){
+            var input = event.target;
+            this.model.showResume = (input.checked);
+        },
         _onChangeStylleImage : function(event){
             this.$(".effect-radio").removeClass("active");
             var $target = $(event.currentTarget);
@@ -119,6 +124,7 @@
                 newsFormat:this.model.newsFormat,
                 newsNbArticle:this.model.newsNbArticle,
                 newsStyleAff:this.model.newsStyleAff,
+                showResume : this.model.showResume,
                 metaTitle:this.model.metaTitle[this.lang],
                 metaDescription:this.model.metaDescription[this.lang],
                 canchangeLayout: this.app.user.can("change_news_layout")
Index: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 13380)
+++ src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 13381)
@@ -21,6 +21,7 @@
 	"unsavedChanges": "sauvegarder les changements",
 	"confirmDeleteCategory":"Vous \u00eates sur le point de supprimer d\u00e9finitivement une catégorie",
 	"confirmDeleteArticle": "Vous \u00eates sur le point de supprimer d\u00e9finitivement un article",
+	"confirmDeleteImageCategory":"Vous \u00eates sur le point de supprimer d\u00e9finitivement l'image de la catégorie",
 	// config global
 	"configGlobalDesc" : "Ajustez les paramétres des catégories/articles",
 	"styleDeNews" :   "Affichage de la liste des articles",
@@ -47,6 +48,9 @@
 	"Style"				   :  "Style",
 	"newsTemplate"         :   "Affichage du modèle de page",
 	"newsTemplateLegend"   :   "Appliquez un modèle de page",
+	"resume"               :   "Résumé",
+	"resumeLegend"         :   "Afficher le texte d'introduction de l'article dans la liste des articles",
+	"resumecheckLegend"    :   "Afficher le résumé",
 
 	//config category
 	"configDesc" 			: "Ajustez les paramètres des Catégorie",
Index: src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 13380)
+++ src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 13381)
@@ -21,6 +21,7 @@
     "unsavedChanges": "sauvegarder les modifications",
 	"confirmDeleteCategory":"Vous \u00eates sur le point de supprimer d\u00e9finitivement une catégorie",
 	"confirmDeleteArticle": "Vous \u00eates sur le point de supprimer d\u00e9finitivement un article",
+	"confirmDeleteImageCategory":"Vous \u00eates sur le point de supprimer d\u00e9finitivement l'image de la catégorie",
 	// config global
 	"configGlobalDesc" : "Ajustez les paramétres des catégories/articles",
 	"styleDeNews" :   "Affichage de la liste des articles",
@@ -47,6 +48,9 @@
 	"Style"				   :  "Style",
 	"newsTemplate"         :   "Affichage du modèle de page",
 	"newsTemplateLegend"   :   "Appliquez un modèle de page",
+	"resume"               :   "Résumé",
+	"resumeLegend"         :   "Afficher le texte d'introduction de l'article dans la liste des articles",
+	"resumecheckLegend"    :   "Afficher le résumé",
 	
 	//config category
 	"configDesc" 			: "Ajustez les paramètres des Catégorie",
Index: src/js/JEditor/NewsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/i18n.js	(révision 13380)
+++ src/js/JEditor/NewsPanel/nls/i18n.js	(révision 13381)
@@ -22,6 +22,7 @@
         "unsavedChanges": "sauvegarder les changements",
         "confirmDeleteCategory":"Vous \u00eates sur le point de supprimer d\u00e9finitivement une catégorie",
         "confirmDeleteArticle": "Vous \u00eates sur le point de supprimer d\u00e9finitivement un article",
+        "confirmDeleteImageCategory":"Vous \u00eates sur le point de supprimer d\u00e9finitivement l'image de la catégorie",
         // config global
         "configGlobalDesc" : "Ajustez les paramétres des catégories/articles","styleDeNews" :   "Affichage de la liste des articles",
         "styleDeNewsLegend" :   "Appliquez un style à la liste des articles",
@@ -47,6 +48,9 @@
         "Style"                :   "Style",
         "newsTemplate"         :   "Affichage du modèle de page",
         "newsTemplateLegend"   :   "Appliquez un modèle de page",
+        "resume"               :   "Résumé",
+        "resumeLegend"         :   "Afficher le texte d'introduction de l'article dans la liste des articles",
+        "resumecheckLegend"    :   "Afficher le résumé",
 
         //config category
         "configDesc" 			: "Ajustez les paramètres des catégories",
