Revision: r12345
Date: 2024-06-04 10:41:12 +0300 (tlt 04 Jon 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: deplacer les fichiers dans la racine  et ajout availableView pour edition d'article

## Files changed

## Full metadata
------------------------------------------------------------------------
r12345 | srazanandralisoa | 2024-06-04 10:41:12 +0300 (tlt 04 Jon 2024) | 1 ligne
Chemins modifiés :
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Blocks.js
   D /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Models
   D /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Templates
   D /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Articles/Views
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/NewsConfig.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/NewsPanel.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/availableBlocks.html
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/publishConfig.html
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/saveButton.html
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/AvailableView.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/PublishConfigView.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/SaveButton.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/i18n.js

News: deplacer les fichiers dans la racine  et ajout availableView pour edition d'article
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Articles/Templates/publishConfig.html
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Templates/publishConfig.html	(révision 12344)
+++ src/js/JEditor/NewsPanel/Articles/Templates/publishConfig.html	(nonexistent)
@@ -1,51 +0,0 @@
-<div id="meta-configuration" class="meta-configuration scrollbar-classic">
-    <header class="panel-head">
-        <span class="icon icon-rotation-right"></span>
-        <h1 class="panel-name">Publication</h1>
-    </header>
-    <div class="panel-content active">
-        <div class="panel-content-intro">
-            Publiez maintenant ou programmez ka publication de votre article à la date que vous souhaitez
-        </div>
-   
-    <div class="option-content ">
-       <h4>
-        <span class="icon icon-calendar-line"></span>
-        <span>Programmez l'envoi</span>
-       </h4>
-        <div class="panel-content-intro"> Programmez une date de publication de l'article</div>
-        
-      <div class="batch">
-         <!-- toogle -->
-            <span class="switch batchActions">
-                <span></span>
-            </span>
-            <span class="labelnon">Non</span>
-            <span class="labeloui">Oui</span>
-        </div> 
-        <div class="btn-group-batch forProgram">
-            <div class="block-datepicker">
-                <div id="datepicker">
-                     <!-- datepicker -->
-            </div>
-
-            <div>Votre article sera publié le <span class="dateprog"></span></div>
-            <div>
-                <button>
-                    <span class="icon icon-rotation-right"></span>
-                    <span>Programmer la publication</span>
-                </button>
-            </div>
-        </div>
-        <div class="forPub">
-            <div>Votre article sera publié immédiatement</div>
-            <div>
-                <button>
-                    <span class="icon icon-rotation-right"></span>
-                    <span>Publié l'article</span>
-                </button>
-            </div>
-        </div>
-    </div>
-</div>
-</div>
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Articles/Templates/ZoneToolbox.html
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Templates/ZoneToolbox.html	(révision 12344)
+++ src/js/JEditor/NewsPanel/Articles/Templates/ZoneToolbox.html	(nonexistent)
@@ -1,5 +0,0 @@
-<span class="label"><%=__("zoneToolboxLabel")%></span>
-<div class="btn-group <%=className%>">
-    <button type="button" class="btn btn-default template-link-indicator <%=zone.customized?'clickable':''%>"><span class="icon <%= (zone.customized?'icon-layout-broken':'icon-layout-ok')%>"></span><% if(zone.customized){%><span class="label"><%=__('backToTemplate')%></span><% } %></button>
-    <button type="button" class="btn btn-default add-content" id="available-blocks-trigger"><span class="icon icon-add"></span><span class="label"><%=__("addContent")%></span></button>
-</div>
Index: src/js/JEditor/NewsPanel/Articles/Templates/articleTitleField.html
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Templates/articleTitleField.html	(révision 12344)
+++ src/js/JEditor/NewsPanel/Articles/Templates/articleTitleField.html	(nonexistent)
@@ -1,7 +0,0 @@
-<i class="icon-lock" style="display:none;"></i>
-<i class="icon-unlock" style="display:none;"></i>
-<span class="text page-name <%=className%>">
-    <span class="content" contenteditable="<%=canSetPageTitle%>"><%= page.name %></span> <%  if(page.attributes.accueil !== 7){ %> <i class="icon-edit icon"></i><% } %> 
-</span><!--
---><span class="inherit-from">(<%=pageTemplate.name%>)</span>
-<span class="inherit-from"><a href="//<%= page.attributes.base_url %>"target="_blank"><%= __("urlText")%></a></span>

Property changes on: src/js/JEditor/NewsPanel/Articles/Templates/articleTitleField.html
___________________________________________________________________
Deleted: svn:executable
## -1 +0,0 ##
-*
\ No newline at end of property
Index: src/js/JEditor/NewsPanel/Articles/Views/ArticleTitleField.js
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Views/ArticleTitleField.js	(révision 12344)
+++ src/js/JEditor/NewsPanel/Articles/Views/ArticleTitleField.js	(nonexistent)
@@ -1,74 +0,0 @@
-define([
-	"jquery",
-	"text!../Templates/articleTitleField.html",
-	"JEditor/Commons/Ancestors/Views/View",
-    "collection!JEditor/Commons/Pages/Models/PageCollection",
-    "i18n!../nls/i18n"
-],function(	$,
-	pageTitleField,
-	View,
-    PageCollection,
-    translate
-){
-var ArticleTitleField = View.extend({
-    tagName: 'div',
-    events: {
-        'blur .content': '_setName',
-        'click .icon-unlock,.icon-lock': '_toggleLock', 
-        'focus .content': '_onFocus', 
-        'keydown .content': '_checkEnter'
-    },
-    attributes: function() {
-        var attrs = {class:'title '};
-        attrs.class += this.model.locked ? 'locked' : 'unlocked';
-        return attrs;
-    },
-    _checkEnter: function(e) {
-        if (e.keyCode === 13) {
-            $(e.currentTarget).blur();
-            return false;
-        }
-    },
-    _toggleLock: function() {
-        this.model.locked = !this.model.locked;
-        if (this.options.autoSave)
-            this.model.save();
-    },
-    _onFocus: function(event) {
-        var $target = $(event.currentTarget);
-        $target.parent().addClass('active');
-        this.nameValue = $target.text();
-    },
-    initialize: function() {
-        this._template = this.buildTemplate(pageTitleField, translate);
-        this.nameValue = this.model.name;
-        this.render();
-    },
-    render: function() {
-        var template = PageCollection.getInstance().findWhere({type:"template",layout:this.model.layout});
-        this.undelegateEvents();
-        this.$el.empty();
-        var idTypePage=this.model.attributes.accueil;
-        var canSetPageTitle=(idTypePage===7)?"false":(this.app.user.can("set_page_name")?"true":"false");
-        this.$el.html(this._template({page: this.model,pageTemplate:template,canSetPageTitle:canSetPageTitle,className:this.app.user.can("set_page_name")?"editable":""}));
-        this.delegateEvents();
-        return this;
-    },
-    _setName: function(event) {
-        var $target = $(event.currentTarget);
-        $target.parent().removeClass('active');
-        //fix ellipsis bug for long text
-        $target.scrollLeft( 0 );
-        if (this.nameValue !== $target.text() && $target.text().trim() !== '') {
-            this.model.name = $target.text();
-            this.nameValue = this.model.name;
-            if (this.options.autoSave)
-                this.model.save();
-        }
-        else
-            $target.text(this.nameValue);
-    }
-
-});
-return ArticleTitleField;
-});

Property changes on: src/js/JEditor/NewsPanel/Articles/Views/ArticleTitleField.js
___________________________________________________________________
Deleted: svn:executable
## -1 +0,0 ##
-*
\ No newline at end of property
Index: src/js/JEditor/NewsPanel/Articles/Views/ZoneToolBox.js
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Views/ZoneToolBox.js	(révision 12344)
+++ src/js/JEditor/NewsPanel/Articles/Views/ZoneToolBox.js	(nonexistent)
@@ -1,86 +0,0 @@
-define([
-    "JEditor/Commons/Ancestors/Views/BabblerView",
-    "JEditor/Commons/Events",
-    "JEditor/PagePanel/Contents/Zones/Views/SaveButton",
-    "JEditor/PagePanel/Contents/Zones/Views/SwitchVersionButton",
-    "text!../Templates/ZoneToolbox.html",
-    "i18n!../nls/i18n"
-],function(
-    BabblerView,
-    Events,
-    SaveButton,
-    SwitchVersionButton,
-    template,
-    translate
-    ) {
-    var ZoneToolbox = BabblerView.extend({
-        className: "zone-toolbox",
-        events: {'click .template-link-indicator.clickable': 'uncustomize'},
-        initialize: function () {
-            this._super();
-            this.childViews = {
-                saveButton: new SaveButton({model: this.model}),
-                switchVersionButton: new SwitchVersionButton({model: this.model})
-            };
-            this._template = this.buildTemplate(template, translate);
-        },
-        remove: function () {
-            for( var view in this.childViews){
-                if(this.childViews[view].remove)
-                    this.childViews[view].remove();
-            }
-            BabblerView.prototype.remove.apply(this, arguments);
-        },
-        render: function () {
-            this._super();
-            this.undelegateEvents();
-            this.$el.html(this._template({zone: this.model, className: this.app.user.can("uncustomize_zone") ? "" : "no-uncustomize"}));
-            this.$('.add-content').before(this.childViews.saveButton.el);
-            if (this.app.user.can("restore_zone")) {
-                this.$('.btn-group').prepend(this.childViews.switchVersionButton.el);
-            }
-            this.dom[this.cid].templateIndicator = this.$('.template-link-indicator').children();
-            this.delegateEvents();
-            return this;
-        },
-        setZone: function (zone) {
-            this.stopListening(this.model);
-            this.model = zone;
-            this.childViews.switchVersionButton.model = zone;
-            this.childViews.saveButton.model = zone;
-            this.listenTo(zone, Events.BackboneEvents.CHANGE, this.breakTemplateLink);
-            return this.render();
-        },
-        onZoneSelect: function (view, zone) {
-            this.trigger(Events.ChoiceEvents.SELECT, view, zone);
-        },
-        breakTemplateLink: function (model, options) {
-            if (model !== this.model) {
-                this.dom[this.cid].templateIndicator.removeClass('icon-template-ok').addClass('icon-template-broken').parent().addClass('active');
-                this.model.customized = true;
-            }
-        },
-        doUncustomize: function () {
-            this.model.customized = false;
-            this.model.sections = [];
-            this.listenToOnce(this.model, Events.BackboneEvents.SYNC, function () {
-                // this.onZoneSet(this.currentZone, this.currentZone);
-                this.render();
-            });
-            this.model.save();
-        },
-        uncustomize: function () {
-            if (this.app.user.can("uncustomize_zone"))
-                this.confirm({
-                    message: translate('confirmUncustomize', {'name': this.model.name}),
-                    title: translate("uncustomize"),
-                    type: 'delete',
-                    onOk: _.bind(this.doUncustomize, this),
-                    options: {dialogClass: 'delete no-close', dontAskAgain: true, subject: 'uncustomize'}
-                });
-            else
-                return;
-        }
-    });
-    return ZoneToolbox;
-});
Index: src/js/JEditor/NewsPanel/Articles/Views/PublishConfigView.js
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Views/PublishConfigView.js	(révision 12344)
+++ src/js/JEditor/NewsPanel/Articles/Views/PublishConfigView.js	(nonexistent)
@@ -1,128 +0,0 @@
-define([
-    "jquery",
-	"underscore",
-	"JEditor/Commons/Events",
-	"JEditor/Commons/Ancestors/Views/View",
-    "text!JEditor/NewsPanel/Articles/Templates/publishConfig.html",
-    "JEditor/App/Views/RightPanelView",
-    "i18n!JEditor/NewsPanel/nls/i18n",
-    "JEditor/Commons/Utils",
-], function ($, _, Events, View, template, RightPanelView, translate,  Utils) {
-    
-    /**
-     * 
-     * @type PublishConfigView
-     */
-    var PublishConfigView = View.extend({
-
-        tagName: 'div',
-        className: 'right-panel-nav',
-        _transitionDelay: 250,
-        rendered: false,
-        _currentCid: null,
-        _programmed:false,
-        events: {
-            'click .switch': '_onClick', 
-            'click .button.cancel': '_onCancelClick', 
-            'click .panel-menu>a': '_onMenuItemClick'
-        },
-        _onClick: function(event) {
-            this._programmed = !this._programmed;
-            this.datePickerRender();
-            return false;
-        
-         },
-        constructor: function(options) {
-            if (!options.rightPanelView || !(options.rightPanelView instanceof RightPanelView))
-                throw new TypeError("le constructeur attend un argument option.rightPanelView de type RightPanelView"); 
-            this.rightPanelView = options.rightPanelView;
-            View.apply(this, arguments);
-        },
-        initialize: function () {
-            this.template = this.buildTemplate(template, translate);
-            this.listenTo(this.rightPanelView, 'overlayClick', this.cancel);
-            this.rightPanelView.addContent(this);
-            this.currentLang = this.options.currentLang;
-            this._programmed = (this.model.programmingDate) ? true:false;
-            return this;
-        },
-         /**
-         * affiche cette reglage
-         */
-         show: function() {
-            if (this.rendered === false)
-                this.render();
-            this.rightPanelView.showPanel();
-            this._super();
-        },
-        /**
-         * cache cette reglage
-         */
-        hide: function(hidePanel) {
-            hidePanel = hidePanel !== false ? true : false;
-            if (this.rendered === false)
-                this.render();
-            if (hidePanel && this.rightPanelView.isVisible())
-                this.rightPanelView.hidePanel();
-            this._super();
-        },
-        /**
-         * Rendu de la view
-         */
-        render: function () {
-            this.undelegateEvents();
-            if (!this.model) {
-               return false;
-            }
-            var params = this.model
-            this.$el.html(this.template(params));
-            this.$('#datepicker').datepicker({
-                dateFormat: 'dd-mm-yy', // Format de la date
-                onSelect: this.updateModel.bind(this) 
-            });
-            this.switchprogram = this.$(".switch");
-            this.datePickerRender();
-            this.delegateEvents();
-            return this;
-        },
-        updateModel: function(selectedDate) {
-            this.model.set('programmingDate', selectedDate);
-        },
-        datePickerRender : function (){
-            if (this._programmed ) {
-                this.$('.forProgram').show();
-                this.$('.forPub').hide();
-                this.$('.labeloui').show();
-                this.$('.labelnon').hide();
-                this.switchprogram.parent().removeClass('disabled');
-                if (this.model.programmingDate!='') {
-                    $("#datepicker" ).datepicker('setDate', this.model.programmingDate);
-                }
-            }
-            else {
-                this.switchprogram.parent().addClass('disabled');
-                this.$('.forProgram').hide();
-                this.$('.forPub').show();
-                this.$('.labeloui').hide();
-                this.$('.labelnon').show();
-            }
-        },
-        save: function() {
-            this.model.save();
-        },
-        /**
-         * annule les changements éfectuées depuis l'ouverture
-         */
-        cancel: function() {
-           this.model.cancel();
-        }
-        
-    });
-    Events.extend({
-        PublishConfigViewEvents: {
-            SAVE: 'save',
-            CANCEL: 'cancel'
-        }
-    });
-    return PublishConfigView;
-});
Index: src/js/JEditor/NewsPanel/Articles/Blocks.js
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Blocks.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Articles/Blocks.js	(révision 12345)
@@ -0,0 +1,74 @@
+define([
+    "JEditor/PagePanel/Contents/Blocks/ImageBlock",
+    "JEditor/PagePanel/Contents/Blocks/HtmlBlock",
+    "JEditor/PagePanel/Contents/Blocks/TwitterTimelineBlock",
+    "JEditor/PagePanel/Contents/Blocks/SiteMapBlock",
+    "JEditor/PagePanel/Contents/Blocks/GridBlock",
+    "JEditor/PagePanel/Contents/Blocks/ButtonBlock",
+    "JEditor/PagePanel/Contents/Blocks/VideoBlock",
+    "JEditor/PagePanel/Contents/Blocks/TableBlock",
+    "JEditor/PagePanel/Contents/Blocks/NewsletterBlock",
+    "JEditor/PagePanel/Contents/Blocks/Block",
+    "JEditor/PagePanel/Contents/Blocks/MapBlock",
+    "JEditor/PagePanel/Contents/Blocks/TextBlock",
+    "JEditor/PagePanel/Contents/Blocks/SeparatorBlock",
+    "JEditor/PagePanel/Contents/Blocks/SocialNetworkBlock",
+    "JEditor/PagePanel/Contents/Blocks/FormBlock",
+    "JEditor/PagePanel/Contents/Blocks/LegalsBlock",
+    "JEditor/PagePanel/Contents/Blocks/EvaluationBlock",
+    "JEditor/PagePanel/Contents/Blocks/CarrouselBlock",
+    "JEditor/PagePanel/Contents/Blocks/GalerieBlock",
+    "JEditor/PagePanel/Contents/Blocks/CompareBlock",
+    "JEditor/PagePanel/Contents/Blocks/LoopBlock",
+    "JEditor/PagePanel/Contents/Blocks/SlideshowBlock"
+  ], function (
+    ImageBlock,
+    HtmlBlock,
+    TwitterTimelineBlock,
+    SiteMapBlock,
+    GridBlock,
+    ButtonBlock,
+    VideoBlock,
+    TableBlock,
+    NewsletterBlock,
+    Block,
+    MapBlock,
+    TextBlock,
+    SeparatorBlock,
+    SocialNetworkBlock,
+    FormBlock,
+    LegalsBlock,
+    EvaluationBlock,
+    CarrouselBlock,
+    GalerieBlock,
+    CompareBlock,
+    LoopBlock,
+    SlideshowBlock
+  ) {
+    var component = {
+      "ImageBlock": ImageBlock,
+      "HtmlBlock": HtmlBlock,
+      "TwitterTimelineBlock": TwitterTimelineBlock,
+      "SiteMapBlock": SiteMapBlock,
+      "GridBlock": GridBlock,
+      "ButtonBlock": ButtonBlock,
+      "VideoBlock": VideoBlock,
+      "TableBlock": TableBlock,
+      "NewsletterBlock": NewsletterBlock,
+      "Block": Block,
+      "MapBlock": MapBlock,
+      "TextBlock": TextBlock,
+      "SeparatorBlock": SeparatorBlock,
+      "SocialNetworkBlock": SocialNetworkBlock,
+      "FormBlock": FormBlock,
+      "LegalsBlock": LegalsBlock,
+      "EvaluationBlock": EvaluationBlock,
+      "CarrouselBlock"  : CarrouselBlock,
+      "GalerieBlock" : GalerieBlock,
+      "CompareBlock": CompareBlock,
+      "LoopBlock": LoopBlock,
+      "SlideshowBlock": SlideshowBlock
+    };
+    return component;
+  });
+  
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Templates/availableBlocks.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/availableBlocks.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Templates/availableBlocks.html	(révision 12345)
@@ -0,0 +1,14 @@
+<div class="scroll-container">
+    <header class="panel-head">
+        <span class="icon-add"></span>
+        <h1 class="panel-name"><%= __("addContent")%></h1>
+    </header>
+    <div class="panel-content image-panel image active">
+        <div class="panel-content-intro">
+            <%= __("availableDesc")%>
+        </div>
+        <div class="option-content availables">
+            <div class="items"></div>
+        </div>
+    </div>
+</div>
\ No newline at end of file

Property changes on: src/js/JEditor/NewsPanel/Templates/availableBlocks.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/NewsPanel/Templates/saveButton.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/saveButton.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Templates/saveButton.html	(révision 12345)
@@ -0,0 +1 @@
+<span class="icon-save icon"></span><span class="label"><%= __("save")%></span>
\ No newline at end of file

Property changes on: src/js/JEditor/NewsPanel/Templates/saveButton.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/NewsPanel/Models/NewsConfig.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/NewsConfig.js	(révision 12344)
+++ src/js/JEditor/NewsPanel/Models/NewsConfig.js	(révision 12345)
@@ -7,14 +7,16 @@
         Model
 ){ var NewsConfig = Model.extend(
                 {
+                    url: __IDEO_API_PATH__ + "/news/config",
                     defaults: {
                         newsStyle: 0,                
                         newsFormat:"landscape", 
                         newsStyleAff: 1,
-                        newsNbArticle: 3
+                        newsNbArticle: 3,
+                        layout: null
                     },
     
                 });
-            NewsConfig.SetAttributes(['newsStyle', 'newsFormat', 'newsStyleAff', 'newsNbArticle']);
+            NewsConfig.SetAttributes(['newsStyle', 'newsFormat', 'newsStyleAff', 'newsNbArticle', 'layout']);
         return NewsConfig;
     });
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12344)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12345)
@@ -18,7 +18,6 @@
     "JEditor/NewsPanel/Views/AvailableView",
     "JEditor/NewsPanel/Views/PublishConfigView",
     "i18n!./nls/i18n",
-    "JEditor/PagePanel/Contents/Blocks/Blocks",
     "jqueryPlugins/affix"
 ],
         function (
@@ -40,8 +39,7 @@
             RightPanelView,
             AvailableView,
             PublishConfigView,
-            translate,
-            Blocks
+            translate
             ) {
             var NewsPanel = PanelView.extend(
                     /**
@@ -70,7 +68,6 @@
                                     this._template = this.buildTemplate(NewsPanelTemplate, translate);
                                     this.rightPanelView = new RightPanelView();
                                     this._byLang = {};
-                                    console.log(Blocks);
                                 },
                                 load: function () {
                                     var loaded = 0;
Index: src/js/JEditor/NewsPanel/Templates/publishConfig.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/publishConfig.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Templates/publishConfig.html	(révision 12345)
@@ -0,0 +1,51 @@
+<div id="meta-configuration" class="meta-configuration scrollbar-classic">
+    <header class="panel-head">
+        <span class="icon icon-rotation-right"></span>
+        <h1 class="panel-name">Publication</h1>
+    </header>
+    <div class="panel-content active">
+        <div class="panel-content-intro">
+            Publiez maintenant ou programmez ka publication de votre article à la date que vous souhaitez
+        </div>
+   
+    <div class="option-content ">
+       <h4>
+        <span class="icon icon-calendar-line"></span>
+        <span>Programmez l'envoi</span>
+       </h4>
+        <div class="panel-content-intro"> Programmez une date de publication de l'article</div>
+        
+      <div class="batch">
+         <!-- toogle -->
+            <span class="switch batchActions">
+                <span></span>
+            </span>
+            <span class="labelnon">Non</span>
+            <span class="labeloui">Oui</span>
+        </div> 
+        <div class="btn-group-batch forProgram">
+            <div class="block-datepicker">
+                <div id="datepicker">
+                     <!-- datepicker -->
+            </div>
+
+            <div>Votre article sera publié le <span class="dateprog"></span></div>
+            <div>
+                <button>
+                    <span class="icon icon-rotation-right"></span>
+                    <span>Programmer la publication</span>
+                </button>
+            </div>
+        </div>
+        <div class="forPub">
+            <div>Votre article sera publié immédiatement</div>
+            <div>
+                <button>
+                    <span class="icon icon-rotation-right"></span>
+                    <span>Publié l'article</span>
+                </button>
+            </div>
+        </div>
+    </div>
+</div>
+</div>
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Views/AvailableView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/AvailableView.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Views/AvailableView.js	(révision 12345)
@@ -0,0 +1,179 @@
+define([
+    "jquery",
+    "underscore",
+    "text!../Templates/availableBlocks.html",
+    "JEditor/Commons/Ancestors/Views/View",
+    "JEditor/NewsPanel/Articles/Blocks",
+    "JEditor/PagePanel/Contents/Zones/Models/Zone",
+    "JEditor/PagePanel/Contents/Sections/Models/Section",
+    "JEditor/PagePanel/Contents/Columns/Models/Column",
+    "JEditor/Commons/Utils",
+    "i18n!../nls/i18n"
+], function ($,
+        _,
+        availableBlocks,
+        View,
+        Blocks,
+        Zone,
+        Section,
+        Column,
+        Utils,
+        translate
+        ) {
+    var /**
+     * La vue des blocs disponibles
+     * @class AvailableView
+     * @extends View
+     * @property {Array} modelLib Un array de tous les types de blocs disponibles
+     {
+     model: JEditor.PagesPanel.Contents.Blocks.TextBlock.TextBlock,
+     class: 'text-block',
+     icon: 'icon-text',
+     type: 'text'
+     }
+     * @todo En faire un singleton???
+     * @todo Si singleton virer la méthode @see JEditor.App.App.ViewsView.showAvailableBlocks
+     */
+            AvailableView = View.extend(
+                    /**
+                     * @lends AvailableView.prototype
+                     */
+                            {
+                                events: {'click .block.available': 'addToCurrentContainer', 'dragstart .bloc.available': '_ondragStart'},
+                                blockOrder: {
+                                    'standard': ["TextBlock", "ImageBlock", "ButtonBlock", "SeparatorBlock", "TableBlock"],
+                                    'advanced': ["CarrouselBlock", "GridBlock","GalerieBlock","CompareBlock","SlideshowBlock", "LoopBlock", "FormBlock", /*"VideoLinkeoBlock",*/ "MapBlock", "VideoBlock", "HtmlBlock"],
+                                    'others': ["LegalsBlock","SiteMapBlock","SocialNetworkBlock"],
+                                    'ignored':['TwitterTimelineBlock']
+                                },
+                                _ondragStart: function () {
+                                    this.dom.body.addClass('dragging');
+                                },
+                                initialize: function () {
+                                    if (this.options.pagePanel)
+                                        this.pagePanel = this.options.pagePanel;
+                                    delete this.options.pagePanel;
+                                    this.modelLib = {};
+                                    this._super();
+                                    this.checkMissingBlocks();
+                                    for (var category in this.blockOrder) {
+                                        if(category==='ignored')
+                                            continue;
+                                        var currentCategory = this.blockOrder[category];
+                                        this.modelLib[category] = [];
+                                        for (var i = 0; i < currentCategory.length; i++) {
+                                            try {
+                                                var type = currentCategory[i].replace('Block', '').toLowerCase();
+                                                var constructor = Blocks[currentCategory[i]];
+                                                var icon = constructor.ICON ? constructor.ICON : '';
+                                                var className = type + '-block';
+                                                if (constructor.ACCESS_CONTROL === true && !this.app.user.can("use" + currentCategory[i], constructor.ACCESS_CONTROL_CRITERIA)) {
+                                                    continue;
+                                                }
+                                                this.modelLib[category].push({
+                                                    model: constructor,
+                                                    class: className,
+                                                    icon: icon,
+                                                    type: type
+                                                });
+                                            } catch (e) {
+                                                console.error(e.stack);
+                                            }
+                                        }
+                                    }
+                                    this._template = this.buildTemplate(availableBlocks, translate);
+                                },
+                                checkMissingBlocks: function () {
+                                    var list = [];
+                                    for (var cat in this.blockOrder) {
+                                        list = list.concat(this.blockOrder[cat]);
+                                    }
+                                    for (var block in Blocks) {
+                                        if (list.indexOf(block) < 0 && block !== 'Block')
+                                            this.blockOrder.others.push(block);
+                                    }
+                                },
+                                render: function () {
+                                    this.undelegateEvents();
+                                    this.$el.empty();
+                                    var $frag = $(document.createDocumentFragment());
+                                    var zone = new Zone();
+                                    var section = new Section({}, {parent: zone});
+                                    var col = new Column({}, {parent: section});
+                                    for (var category in this.modelLib) {
+                                        var currentCategory = this.modelLib[category];
+                                        if (currentCategory.length > 0)
+                                            $frag.append('<h2 class="option-title">' + translate(category) + '</h2>');
+                                        var $catDiv = $(document.createElement('div'));
+                                        for (var i = 0; i < currentCategory.length; i++) {
+                                            var $block = $(document.createElement('div')).addClass('available block ' + currentCategory[i].class).append('<div class="container"><span class="icon ' + currentCategory[i].icon + '"></span><span class="text">' + currentCategory[i].model.BLOCK_NAME + '</span></div>');
+                                            var model = new currentCategory[i].model({}, {parent: col});
+                                            model.dummy = true;
+                                            col.addChild(model);
+                                            $block.data('model', model);
+                                            $block.draggable({revert: true, appendTo: $("#page-edit"), helper: "clone", cursor: "move"});
+                                            $catDiv.append($block.get(0));
+                                        }
+                                        $catDiv.addClass('items');
+                                        $frag.append($catDiv);
+                                    }
+                                    this.$el.addClass('available-items');
+                                    this.$el.html(this._template({}));
+                                    this.$el.find('.items').append($frag.get(0));
+                                    this.scrollables();
+                                    this.delegateEvents();
+                                    return this;
+                                },
+                                /**
+                                 * au clic sur un bloc, il est ajouté à l'élément courrant (renseigné lors de la_vue.show(container), c'est à ça que sert "container"!!!!)
+                                 * @argument {jQuery.Event} event un évenement click jQuery
+                                 */
+                                addToCurrentContainer: function (event) {
+                                    var clone = $(event.currentTarget).data('model').clone();
+                                    var section = new Section({columns: [new Column({width: 12, blocks: [clone]})]});
+                                    // check if zone have popup 
+                                    var isPopup = false;
+                                    var sections = this.pagePanel.currentZone.attributes.sections;
+                                    var index = 0;
+                                    if (sections.length > 0){
+                                        _.each(sections, function(model,i){
+                                          if(model.isPopup()){
+                                              isPopup = true;
+                                              index=i;
+                                          };
+                                        });
+                                    }
+                                    at = (sections.length-1 === index && isPopup)?(sections.length-1) : (sections.length);
+                                    this.pagePanel.currentZone.addChild(section, at);
+                                    this.pagePanel.hideAvailableBlocks();
+                                    //Scroll en bas de la page et le focus
+                                    $('[data-cid="'+clone.cid+'"]').focus();
+                                    // document.querySelector('[data-cid="'+clone.cid+'"]').focus();
+                                    window.scrollTo(0, document.body.scrollHeight);
+                                },
+                                /**
+                                 * affiche les blocs disponibles
+                                 * @argument {Column} container la colonne à laqulle ajouter le bloc au clic
+                                 */
+                                show: function (container) {
+                                    this.dom.window.on('scroll.updateHeight', _.bind(this.updateHeight, this));
+                                    this._super();
+                                },
+                                updateHeight: function () {
+                                    var offset = this.$el.offset().top;
+                                    var scrollTop = this.dom.window.scrollTop();
+                                    var windowHeight = this.dom.window.height();
+                                    var height = windowHeight - (offset - scrollTop);
+                                    this.$el.height(height);
+                                    this.updateScrollables();
+                                },
+                                /**
+                                 * cache les blocs disponibles
+                                 */
+                                hide: function () {
+                                    this._super();
+                                }
+                            });
+
+                    return AvailableView;
+                });

Property changes on: src/js/JEditor/NewsPanel/Views/AvailableView.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/NewsPanel/Views/PublishConfigView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/PublishConfigView.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Views/PublishConfigView.js	(révision 12345)
@@ -0,0 +1,128 @@
+define([
+    "jquery",
+	"underscore",
+	"JEditor/Commons/Events",
+	"JEditor/Commons/Ancestors/Views/View",
+    "text!JEditor/NewsPanel/Templates/publishConfig.html",
+    "JEditor/App/Views/RightPanelView",
+    "i18n!JEditor/NewsPanel/nls/i18n",
+    "JEditor/Commons/Utils",
+], function ($, _, Events, View, template, RightPanelView, translate,  Utils) {
+    
+    /**
+     * 
+     * @type PublishConfigView
+     */
+    var PublishConfigView = View.extend({
+
+        tagName: 'div',
+        className: 'right-panel-nav',
+        _transitionDelay: 250,
+        rendered: false,
+        _currentCid: null,
+        _programmed:false,
+        events: {
+            'click .switch': '_onClick', 
+            'click .button.cancel': '_onCancelClick', 
+            'click .panel-menu>a': '_onMenuItemClick'
+        },
+        _onClick: function(event) {
+            this._programmed = !this._programmed;
+            this.datePickerRender();
+            return false;
+        
+         },
+        constructor: function(options) {
+            if (!options.rightPanelView || !(options.rightPanelView instanceof RightPanelView))
+                throw new TypeError("le constructeur attend un argument option.rightPanelView de type RightPanelView"); 
+            this.rightPanelView = options.rightPanelView;
+            View.apply(this, arguments);
+        },
+        initialize: function () {
+            this.template = this.buildTemplate(template, translate);
+            this.listenTo(this.rightPanelView, 'overlayClick', this.cancel);
+            this.rightPanelView.addContent(this);
+            this.currentLang = this.options.currentLang;
+            this._programmed = (this.model.programmingDate) ? true:false;
+            return this;
+        },
+         /**
+         * affiche cette reglage
+         */
+         show: function() {
+            if (this.rendered === false)
+                this.render();
+            this.rightPanelView.showPanel();
+            this._super();
+        },
+        /**
+         * cache cette reglage
+         */
+        hide: function(hidePanel) {
+            hidePanel = hidePanel !== false ? true : false;
+            if (this.rendered === false)
+                this.render();
+            if (hidePanel && this.rightPanelView.isVisible())
+                this.rightPanelView.hidePanel();
+            this._super();
+        },
+        /**
+         * Rendu de la view
+         */
+        render: function () {
+            this.undelegateEvents();
+            if (!this.model) {
+               return false;
+            }
+            var params = this.model
+            this.$el.html(this.template(params));
+            this.$('#datepicker').datepicker({
+                dateFormat: 'dd-mm-yy', // Format de la date
+                onSelect: this.updateModel.bind(this) 
+            });
+            this.switchprogram = this.$(".switch");
+            this.datePickerRender();
+            this.delegateEvents();
+            return this;
+        },
+        updateModel: function(selectedDate) {
+            this.model.set('programmingDate', selectedDate);
+        },
+        datePickerRender : function (){
+            if (this._programmed ) {
+                this.$('.forProgram').show();
+                this.$('.forPub').hide();
+                this.$('.labeloui').show();
+                this.$('.labelnon').hide();
+                this.switchprogram.parent().removeClass('disabled');
+                if (this.model.programmingDate!='') {
+                    $("#datepicker" ).datepicker('setDate', this.model.programmingDate);
+                }
+            }
+            else {
+                this.switchprogram.parent().addClass('disabled');
+                this.$('.forProgram').hide();
+                this.$('.forPub').show();
+                this.$('.labeloui').hide();
+                this.$('.labelnon').show();
+            }
+        },
+        save: function() {
+            this.model.save();
+        },
+        /**
+         * annule les changements éfectuées depuis l'ouverture
+         */
+        cancel: function() {
+           this.model.cancel();
+        }
+        
+    });
+    Events.extend({
+        PublishConfigViewEvents: {
+            SAVE: 'save',
+            CANCEL: 'cancel'
+        }
+    });
+    return PublishConfigView;
+});
Index: src/js/JEditor/NewsPanel/Views/SaveButton.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/SaveButton.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Views/SaveButton.js	(révision 12345)
@@ -0,0 +1,124 @@
+define([
+    "jquery",
+    "underscore",
+    "text!../Templates/saveButton.html",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/BabblerView",
+    "JEditor/Commons/Utils",
+    "JEditor/Commons/Ancestors/Views/LoaderView"            ,
+    "i18n!../nls/i18n",
+    // not in params
+    "jqueryPlugins/toaster",
+    "jqueryPlugins/affix"
+], function ($,_,
+        saveButton,
+        Events,
+        BabblerView,
+        Utils,
+        LoaderView        ,
+        translate) {
+    /**
+     * Vue contenant le bouton de sauvegarde de la zone courante
+     * @class SaveButton
+     * @extends BabblerView
+     * @extends LoaderView
+     */
+    var SaveButton = BabblerView.extend(
+            /**
+             * @lends SaveButton.prototype
+             */
+                    {
+                        events: {'click': 'saveArticle'},
+                        attributes: {
+                            id: 'save-zone',
+                            class: 'btn btn-default button save',
+                            type: "button"
+                        },
+                        tagName: "button",
+                        /**
+                         * initialize le bouton
+                         */
+                        initialize: function () {
+                            this._super();
+                            this._template = this.buildTemplate(saveButton, translate);
+                            $(document).on("keydown.saveHandler",_.bind(this.ctrlsHandler,this));
+                        },
+                        ctrlsHandler:function(event){
+                            var ret;
+                            if(event.keyCode===83&& event.ctrlKey){
+                                    this.saveArticle();
+                                    ret= false;
+                            }
+                            return ret;
+                        },
+                        remove:function(){
+                                $(document).off("keydown.saveHandler");
+                        },
+                        /*----------------------------
+                         * test la hauteur de fenètre
+                         * return true si mise en page 'petite hauteur'
+                         *----------------------------*/
+                        checkAffixTop:function(){
+                            return (document.documentElement.clientHeight < 866 ? 90 : 230);
+                        },
+                        /**
+                         * crée le rendu de la vue
+                         */
+                        render: function () {
+                            //magic don't touch
+                            this.undelegateEvents();
+                            //end magic don't touch
+                            this.$el.empty();
+                            this.$el.html(this._template({}));
+                            //magic don't touch
+                            this.delegateEvents();
+                            //end magic don't touch
+                            return this;
+                        },
+                        onSave: function () {
+                            console.log("test");
+                            this.stopListening(this.model, Events.BackboneEvents.ERROR, this.onError);
+                            this.trigger(Events.LoadEvents.LOAD_SUCCESS, this);
+                            $.toast({
+                                text: translate("saveSuccesful"), 
+                                icon: 'icon-check-circle', 
+                                type:'success',
+                                appendTo:'#news-editor .zone .zone-toolbox',
+                                showHideTransition: 'fade', 
+                                hideAfter: 5000, 
+                                position: 'top-right', 
+                                textAlign: 'left', 
+                                allowToastClose:false,
+                                loader:false
+                            });
+                                          
+                        },
+                        onError: function () {
+                            this.stopListening(this.model, Events.BackboneEvents.SYNC, this.onSave);
+                            //this.trigger(Events.LoadEvents.LOAD_SUCCESS, this);
+                            this.error({
+                                title: translate("saveAction"),
+                                message: translate("saveError")
+                            });
+                        },
+                        /**
+                         * Sauvegarde la section
+                         * affiche une confirmation si ça a marché, une erreur dans le cas contraire
+                         * @return {Boolean} False, toujours, parce qu'elle est en général appellée par un evenement du DOM
+                         * @public
+                         */
+                        saveArticle: function (e) {
+                            var that = this;
+                            this.listenToOnce(this.model, Events.BackboneEvents.SYNC, this.onSave);
+                            this.listenToOnce(this.model, Events.BackboneEvents.ERROR, this.onError);
+                               
+                            this.model.customized = true;
+                            
+                            this.model.save();
+                           
+
+                            return false;
+                        }
+                    });
+            return SaveButton;
+        });

Property changes on: src/js/JEditor/NewsPanel/Views/SaveButton.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12344)
+++ src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12345)
@@ -84,7 +84,19 @@
 	 "titleArticle" : "Titre de l'article",
 	 "descriArticle": "Introduction de l'article",
 	 "DefaultMessageUploaderLogo": "Cliquez ici ou glissez-déposez une image",
-	 "saveAddArticle" : "Creer une article"
+	 "saveAddArticle" : "Creer une article",
 
-
+	//available
+	"addContent":"Ajouter du contenu",
+	"availableDesc":"Glissez vos blocs sur l’emplacement souhaité ou cliquez sur un bloc pour l’ajouter en bas de page.",
+	"standardBlocks":"Blocs standards",
+	"block":"Bloc",
+	"move":"Déplacer",
+	"edit":"Éditer",
+	"delete":"Supprimer",
+	"standard":"Blocs standards",
+	"advanced":"Blocs avancés",
+	"others":"Autres blocs",
+	"errorRenderDuring":"Une erreur nous empêche d'afficher ce bloc",
+	"oops":"Oups !"
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 12344)
+++ src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 12345)
@@ -84,7 +84,19 @@
 	 "titleArticle" : "Titre de l'article",
 	 "descriArticle": "Introduction de l'article",
 	 "DefaultMessageUploaderLogo": "Cliquez ici ou glissez-déposez une image",
-	 "saveAddArticle" : "Creer une article"
+	 "saveAddArticle" : "Creer une article",
 
-
+	 //available
+	 "addContent":"Ajouter du contenu",
+	 "availableDesc":"Glissez vos blocs sur l’emplacement souhaité ou cliquez sur un bloc pour l’ajouter en bas de page.",
+	 "standardBlocks":"Blocs standards",
+	 "block":"Bloc",
+	 "move":"Déplacer",
+	 "edit":"Éditer",
+	 "delete":"Supprimer",
+	 "standard":"Blocs standards",
+	 "advanced":"Blocs avancés",
+	 "others":"Autres blocs",
+	 "errorRenderDuring":"Une erreur nous empêche d'afficher ce bloc",
+	 "oops":"Oups !"
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12344)
+++ src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12345)
@@ -86,8 +86,21 @@
         "descriArticle": "Introduction de l'article",
         "DefaultMessageUploaderLogo": "Cliquez ici ou glissez-déposez une image",
         "saveAddArticle" : "Creer une article",
-        "dateProg" : ""
+        "dateProg" : "",
 
+        //available
+        "addContent":"Ajouter du contenu",
+        "availableDesc":"Glissez vos blocs sur l’emplacement souhaité ou cliquez sur un bloc pour l’ajouter en bas de page.",
+        "standardBlocks":"Blocs standards",
+        "block":"Bloc",
+        "move":"Déplacer",
+        "edit":"Éditer",
+        "delete":"Supprimer",
+        "standard":"Blocs standards",
+        "advanced":"Blocs avancés",
+        "others":"Autres blocs",
+        "errorRenderDuring":"Une erreur nous empêche d'afficher ce bloc",
+        "oops":"Oups !"
     },
     "fr-fr": true,
     "fr-ca": true
