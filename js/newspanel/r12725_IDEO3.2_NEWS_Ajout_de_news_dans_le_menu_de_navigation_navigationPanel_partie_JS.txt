Revision: r12725
Date: 2024-08-08 16:19:45 +0300 (lkm 08 Aog 2024) 
Author: jn.harison 

## Commit message
IDEO3.2 NEWS:Ajout de news dans le menu de navigation(navigationPanel) partie JS

## Files changed

## Full metadata
------------------------------------------------------------------------
r12725 | jn.harison | 2024-08-08 16:19:45 +0300 (lkm 08 Aog 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Links/Models/LinkType.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NavigationPanel/NavigationPanel.js
   A /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/addToMenu.html
   A /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/NewsMenuPanelView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/i18n.js

IDEO3.2 NEWS:Ajout de news dans le menu de navigation(navigationPanel) partie JS
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Links/Models/LinkType.js
===================================================================
--- src/js/JEditor/Commons/Links/Models/LinkType.js	(révision 12724)
+++ src/js/JEditor/Commons/Links/Models/LinkType.js	(révision 12725)
@@ -3,5 +3,6 @@
     EXTERNAL: 1,
     FILE: 2,
     NONE: 3,
-    IMAGE: 4
+    IMAGE: 4,
+    NEWS: 5
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Templates/addToMenu.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/addToMenu.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Templates/addToMenu.html	(révision 12725)
@@ -0,0 +1,8 @@
+<header class="view">
+    <h3><%= __('newsTitle') %></h3>
+    <div class="options">
+        <div class=" addmenu">
+            <span class="more">+</span><span class="text"><%= __('addToMenu') %></span>
+        </div>
+    </div>
+</header>
\ No newline at end of file

Property changes on: src/js/JEditor/NewsPanel/Templates/addToMenu.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/NewsPanel/Views/NewsMenuPanelView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/NewsMenuPanelView.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Views/NewsMenuPanelView.js	(révision 12725)
@@ -0,0 +1,87 @@
+define([
+    "jquery",
+    "underscore",
+    "text!../Templates/addToMenu.html",
+    "JEditor/Commons/Ancestors/Views/BabblerView",
+    "JEditor/Commons/Links/Models/Link",
+    "JEditor/Commons/Links/Models/LinkType",
+    "JEditor/Commons/Menus/Models/MenuItem",
+    "i18n!../nls/i18n",
+    // not in params
+    "jqueryPlugins/toaster",
+    "jqueryPlugins/affix"
+], function ($,_,
+        addToMenu,
+        BabblerView,
+        Link,
+        LinkType,
+        MenuItem,
+        translate) {
+    /**
+     * Vue contenant le bouton de sauvegarde de la zone courante
+     * @class SaveButton
+     * @extends BabblerView
+     * @extends LoaderView
+     */
+    var NewsMenuPanelView = BabblerView.extend(
+                    {
+                        tagName: 'div',
+                        attributes: {
+                            class: 'list view'
+                        },
+                        currentMenuView: null,
+                        events: {'click': 'addToMenu'},
+                        initialize: function (options) {
+
+                            this.collectionMenu = options.collection;
+                            this.lang = options.lang;
+                            this._super();
+                            this._template = this.buildTemplate(addToMenu, translate);
+                        },
+                        /**
+                         * crée le rendu de la vue
+                         */
+                        render: function () {
+                            //magic don't touch
+                            this.undelegateEvents();
+                            //end magic don't touch
+                            this.$el.empty();
+                            this.$el.html(this._template({}));
+                            //magic don't touch
+                            this.delegateEvents();
+                            //end magic don't touch
+                            return this;
+                        },
+                       addToMenu: function(event) {
+                        //we need first to check that it is not null
+                        if(this.currentMenuView === null){
+                          throw new TypeError('Choisissez en premier');
+                        }
+                        //we need to add the new link to the menue
+                        //the data to be send
+                        var link = new Link({
+                            name: "News",
+                            href: "#",
+                            type: LinkType.NEWS,
+                            lang: this.lang
+                          });
+                          
+
+                          var menuItem = new MenuItem({
+                            name: "News",
+                            type: MenuItem.LINK_TYPE,
+                            link: link,
+                            cssClass: ""
+                          }, {
+                            collection: this.currentMenuView.model.itemCollection,
+                            parentMenu: this.currentMenuView.model
+                          });
+
+                        //and update the menu to have the new value
+                        this.currentMenuView.model.items.add(menuItem);
+                       
+                       }
+                       
+                    });
+            return NewsMenuPanelView;
+        });

Property changes on: src/js/JEditor/NewsPanel/Views/NewsMenuPanelView.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12724)
+++ src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12725)
@@ -154,4 +154,7 @@
 	"success": "Effectué !",
 	"introPageTitle":"Mes pages",
 	"previousVersionSuccesful":"La zone a été restaurée avec succès et <br/>la page a été sauvegardée",
+
+	//menu
+	"addToMenu":"Ajouter au menu",
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 12724)
+++ src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 12725)
@@ -154,4 +154,8 @@
 	 "success": "Effectué !",
 	 "introPageTitle":"Mes pages",
 	 "previousVersionSuccesful":"La zone a été restaurée avec succès et <br/>la page a été sauvegardée",
+
+	//menu
+	"addToMenu":"Ajouter au menu",
+	 
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12724)
+++ src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12725)
@@ -154,6 +154,9 @@
         "success": "Effectué !",
         "introPageTitle":"Mes pages",
         "previousVersionSuccesful":"La zone a été restaurée avec succès et <br/>la page a été sauvegardée",
+
+        //menu
+        "addToMenu":"Ajouter au menu",
     },
     "fr-fr": true,
     "fr-ca": true
Index: src/js/JEditor/NavigationPanel/NavigationPanel.js
===================================================================
--- src/js/JEditor/NavigationPanel/NavigationPanel.js	(révision 12724)
+++ src/js/JEditor/NavigationPanel/NavigationPanel.js	(révision 12725)
@@ -9,6 +9,7 @@
     "JEditor/NavigationPanel/Views/DraggableMenuItemView",
     "JEditor/Commons/Languages/Views/LanguagesDropDown",
     "JEditor/NavigationPanel/Views/MenuPanelView",
+    "JEditor/NewsPanel/Views/NewsMenuPanelView",
     "i18n!./nls/i18n",
     //not in params
     "owlCarousel",
@@ -24,6 +25,7 @@
         DraggableMenuItemView,
         LanguagesDropDown,
         MenuPanelView,
+        NewsMenuPanelView,
         translate
         ) {
     var NavigationPanel = PanelView.extend(
@@ -127,14 +129,23 @@
                             this.childViews.menuListView = new DraggableMenuItemView.fromMenus({collection: this.menuList, lang: this.currentLang.id});
                             this.childViews.langDropDown = new LanguagesDropDown({collection: this.languages, _default: this.currentLang, defaultLabel: 'language'});
                             this.childViews.menuPanelView = new MenuPanelView({collection: this.menuList, lang: this.currentLang.id});
+                            if(this.app.user.can('access_panel_news', __IDEO_NEWS__)){ 
+                                this.childViews.newsMenuPanelView = new NewsMenuPanelView({collection: this.pageList, lang: this.currentLang.id});
+                            }
                             this.childViews.menuListView.hide(false);
                             this.listenTo(this.childViews.menuPanelView, Events.MenuPanelViewEvents.MENU_CHANGED, this._onMenuChanged);
                             this.listenTo(this.childViews.langDropDown, Events.ChoiceEvents.SELECT, this._onLangSelect);
+                            if(this.app.user.can('access_panel_news', __IDEO_NEWS__)){
+                                 this.listenTo(this.childViews.newsMenuPanelView, Events.MenuPanelViewEvents.MENU_CHANGED, this._onMenuChanged);
+                            }
                         },
                         _onMenuChanged: function (menu, menuView, menuPanelView) {
                             this.childViews.menuListView.currentMenuView = menu !== null ? menuView : null;
                             this.childViews.pageListView.currentMenuView = menu !== null ? menuView : null;
                             this.childViews.pageSupportListView.currentMenuView = menu !== null ? menuView : null;
+                            if(this.app.user.can('access_panel_news', __IDEO_NEWS__)){ 
+                                this.childViews.newsMenuPanelView.currentMenuView = menu !== null ? menuView : null;
+                            }
                             this.currentMenu = menu;
                         },
                         onMenuCountChange: function () {
@@ -187,6 +198,9 @@
                             this.setDOM();//création des variables this.dom[]
                             this.renderPages();
                             this.renderSupportPages();
+                            if(this.app.user.can('access_panel_news', __IDEO_NEWS__)){ 
+                                this.renderNewsMenu();
+                            }
                             this.renderLinks();
                             this.dom[this.options.panelName].langLabel.after(this.childViews.langDropDown.el);
                             this.childViews.langDropDown.render();
@@ -210,6 +224,10 @@
                             this.dom[this.options.panelName].pageList.append(this.childViews.pageSupportListView.el);
                             this.childViews.pageSupportListView.render();
                         },
+                        renderNewsMenu: function(){
+                            this.dom[this.options.panelName].pageList.append(this.childViews.newsMenuPanelView.el);
+                            this.childViews.newsMenuPanelView.render();
+                        },
                         renderLinks: function () {
                             this.dom[this.options.panelName].pageList.append(this.childViews.menuListView.el);
                             this.childViews.menuListView.render();
