Revision: r12415
Date: 2024-06-13 17:17:51 +0300 (lkm 13 Jon 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: correction envoye plusieur toast lors du sauvegarde categorie

## Files changed

## Full metadata
------------------------------------------------------------------------
r12415 | srazanandralisoa | 2024-06-13 17:17:51 +0300 (lkm 13 Jon 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/CategorieAddView.js

News: correction envoye plusieur toast lors du sauvegarde categorie
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Views/CategorieAddView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(révision 12414)
+++ src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(révision 12415)
@@ -60,7 +60,6 @@
             });
 
             this.listenTo(this.langDropDown, Events.ChoiceEvents.SELECT, this._onLanguageChange);
-            this.listenToOnce(this.model, Events.BackboneEvents.SYNC, this.onSave);
 
             this.fileCollection = new FileCollection();
             this.translations = translate.translations;
@@ -192,7 +191,6 @@
                 this.model.save();
                 this.collection.add(this.model);
                 this.collection.trigger('change');
-                this.onSave();
             }
         },
 
