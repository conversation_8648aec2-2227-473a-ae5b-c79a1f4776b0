Revision: r12578
Date: 2024-07-10 16:22:45 +0300 (lrb 10 Jol 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: update le titre et le status de l'article

## Files changed

## Full metadata
------------------------------------------------------------------------
r12578 | s<PERSON><PERSON><PERSON><PERSON>oa | 2024-07-10 16:22:45 +0300 (lrb 10 Jol 2024) | 1 ligne
Chemins modifiés :
   A /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/articleTitleField.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/ArticleEditorView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/ArticleTitleField.js

News: update le titre et le status de l'article
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Templates/articleTitleField.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/articleTitleField.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Templates/articleTitleField.html	(révision 12578)
@@ -0,0 +1,41 @@
+<div class="title">
+    <span class="text page-name">
+        <span class="content" ><%=title%></span> 
+    </span>
+</div>
+<div class="config-preview">
+    <div class="btn-group">
+        <div class="btn-group open-article">
+
+        </div>
+        <% if(state ){ %> 
+        <div class="btn-group state article-state">
+            <%_.each(state,function(state){ %>
+                <%if(state==2){%> 
+                    <span class="btn green">Publié</span>
+                <%}%>
+                <%if(state==0){%> 
+                    <span class="btn orange">Brouillon</span>
+                <%}%>
+                <%if(state==1){%> 
+                    <span class="btn bleu">Programmé <%=datep%></span>
+                <%}%>
+            <%
+            });%>
+        </div>
+        <%}%> 
+        <% if( usepreview ){ %> 
+         <div class="btn-group config">
+            <button type="button"class="btn page-action" id="params" >
+                <span class="icon icon-params"></span>
+                <span class="label"><%=__('config')%></span>
+            </button>
+        </div>
+        
+        <button type="button" class="btn btn-default preview" >
+            <i class="icon icon-find"></i>
+            <span class="label"><%= __("preview")%></span>
+        </button>
+        <%}%> 
+    </div>
+</div>
\ No newline at end of file

Property changes on: src/js/JEditor/NewsPanel/Templates/articleTitleField.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/NewsPanel/Views/ArticleEditorView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticleEditorView.js	(révision 12577)
+++ src/js/JEditor/NewsPanel/Views/ArticleEditorView.js	(révision 12578)
@@ -6,7 +6,6 @@
     "JEditor/NewsPanel/Views/ArticleEditorView",
     "JEditor/Commons/Ancestors/Views/BabblerView",
     "JEditor/Commons/Ancestors/Views/LoaderView",
-    "JEditor/PagePanel/Views/PagePreview",
     "JEditor/PagePanel/Contents/Zones/Views/ContentZoneView",
     "JEditor/NewsPanel/Views/ZoneToolBox",
     "collection!JEditor/Commons/Languages/Models/ContentLanguageList",
@@ -15,6 +14,7 @@
     "JEditor/NewsPanel/Models/Article",
     "JEditor/PagePanel/Contents/Zones/Models/Zone",
     "JEditor/NewsPanel/Articles/Versions/Models/ArticleContent",
+    "JEditor/NewsPanel/Views/ArticleTitleField",
     "i18n!../nls/i18n",
     //not in params
   
@@ -26,7 +26,6 @@
      ArticleEditorView, 
      BabblerView, 
      LoaderView,
-     PagePreview,
      ContentZoneView,
      ZoneToolBox,
      ContentLanguageList,
@@ -35,14 +34,13 @@
      Article,
      ZoneModel,
      ArticleContent,
+     ArticleTitleField,
      translate
      ) {
    
     var ArticleEditorView = BabblerView.extend({
         events: {
-            'click .btn.preview': 'preview',
             'click a[data-action]': '_onActionClick',
-            'click .save-version': 'usePreviousVersion'
         },
         _loaded: 0,
         attributes: {
@@ -51,7 +49,7 @@
         fadeOutEffect: "fadeOut",
         fadeInEffect: "fadeIn",
         zonesFetched: false,
-        selectedZoneVersion: null,
+       
         page : null,
         initialize: function () {
             this._super();
@@ -58,7 +56,6 @@
             this._template = this.buildTemplate(template, translate);
             if (this.model) {
                 this.zoneToolbox = new ZoneToolBox({languageList : this.options.languageList});
-                this.pagePreview = new PagePreview();
             }
             else{
                 this.model = new Article();
@@ -70,6 +67,7 @@
                 collection:  this.options.categorieCollection,
                 languages: ContentLanguageList.getInstance(),
             });
+            this.articleTitleField = new ArticleTitleField({model : this.model});
             self = this;
             this.listenTo( this.articleDetail, Events.ArticleAddEvents.ADDED, _.bind(function(article) {
                 self.newsPanel.currentArticle = article;
@@ -81,8 +79,6 @@
         remove: function () {
             if (this.zoneToolbox)
                 this.zoneToolbox.remove();
-            if (this.pagePreview)
-                this.pagePreview.remove();
             if (this.articleDetail)
                 this.articleDetail.remove();
             if (this.sectionCollectionView)
@@ -90,6 +86,9 @@
             BabblerView.prototype.remove.apply(this, arguments);
         },
         onZoneSave: function () {
+            if (this.articleTitleField)
+                this.articleTitleField.remove();
+            this.articleTitleField = new ArticleTitleField({model : this.model});
             if (this.sectionCollectionView)
                 this.sectionCollectionView.remove();
             this.sectionCollectionView = new ContentZoneView({
@@ -97,6 +96,7 @@
             });
             if (!this.model.content.content) {
                 this.model.content.content = this.currentZone;
+                this.model.content.content.lastState = this.currentZone.toJSON();
             }
             return this.render();
         },
@@ -149,10 +149,9 @@
                     top: this.checkAffixTop
                 }
             });
-            if (this.pagePreview) {
-                this.pagePreview.remove();
-                this.pagePreview = new PagePreview();
-                this.newsPanel.$el.append(this.pagePreview.el);
+            if (this.articleTitleField ) {
+                this.newsPanel.$('.page').html(this.articleTitleField.el);
+                this.articleTitleField.render();
             }
             if (this.articleDetail) {
                 this.$('.dis-table.bordered').append(this.articleDetail.el);
@@ -228,61 +227,7 @@
                     callback();
                 return this;
             }
-        },
-        /**
-         * Crée la preview de la page en cours d'édition par une requête Post
-         * @param {jQuery.Events} e L'évenement jQuery Déclencheur
-         * @param {JEditor/PagePanel/Contents/Zones/Models/Zone} zoneVersion une zone antérieure à afficher da le preview
-         */
-         preview: function (e, zoneVersion) {
-            var page = this.model,
-            zone = zoneVersion || this.currentZone;
-            //console.log(zone);
-            this.pagePreview.show(zone, page);
-
-            if (this.app.user.can("restore_zone")) {
-                if (zoneVersion) {
-                    this.selectedZoneVersion = zoneVersion;
-                    this.pagePreview.$('.save-version').show();
-                }else {
-                    this.pagePreview.$('.save-version').hide();
-                }
-            } else {
-                this.pagePreview.$('.save-version').hide();
-            }
-
-            return false;
-        },
-        /**
-         * restauration de page avec la zone selectionnée
-         * 
-         * @param {jQuery.Events} e L'évenement jQuery Déclencheur
-         */
-        usePreviousVersion: function(e){
-
-            if (e) { e.preventDefault(); e.stopPropagation(); }
-
-            if (this.app.user.can("restore_zone")) {
-
-                this.dom.html.removeClass('no-scroll');
-                if (this.pagePreview) { this.pagePreview.remove(); }
-                this.newsPanel.rightPanelView.hidePanel();
-
-                this.setZone(this.selectedZoneVersion);
-
-                var that = this;
-                this.listenToOnce(this.currentZone, Events.BackboneEvents.SYNC, function () {
-                    that.render();
-                    that.notify({
-                        title: translate("saveAction"),
-                        message: translate("previousVersionSuccesful")
-                    });
-                });
-
-                this.model.save();
-            }
-        },
-
+        }
     });
     Events.extend({
         ArticleEditorViewEvents : {
Index: src/js/JEditor/NewsPanel/Views/ArticleTitleField.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticleTitleField.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Views/ArticleTitleField.js	(révision 12578)
@@ -0,0 +1,41 @@
+define([
+	"jquery",
+	"text!../Templates/articleTitleField.html",
+	"JEditor/Commons/Ancestors/Views/View",
+    "moment",
+    "i18n!../nls/i18n"
+],function(	$,
+	articleTitleField,
+	View,
+    moment,
+    translate
+){
+var ArticleTitleField = View.extend({
+    tagName: 'div',
+    initialize: function() {
+        this._template = this.buildTemplate(articleTitleField, translate);
+        this.nameValue = this.model.name;
+        this.render();
+    },
+    render: function() {
+        this.undelegateEvents();
+        var page = this.model.getPageModel() ;
+        var openArticle = (page)?'<a href="//'+ page.attributes.base_url +'" class="btn btn-primary btn-lg btn-block" target="_blank">Ouvrir' : "";
+       
+        var state = this.model.state;
+        var datep = (state == 1) ? moment(this.model.programmingDate.date).format("DD/MM/YY"): '';
+        this.$el.empty();
+        this.$el.html(this._template({
+            title: (this.model.id)? this.model.title : 'Article',
+            datep:datep,
+            state: (this.model.id)?this.model.state : false,
+            usepreview :(this.model.id)?true: false
+        }));
+        this.$('.open-article').html( openArticle );
+        this.delegateEvents();
+        return this;
+    }
+
+});
+return ArticleTitleField;
+});

Property changes on: src/js/JEditor/NewsPanel/Views/ArticleTitleField.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
