Revision: r14293
Date: 2025-05-21 17:05:32 +0300 (lrb 21 Mey 2025) 
Author: frahajanirina 

## Commit message
News: afficher la catégorie Non classés en italic et gris clair

## Files changed

## Full metadata
------------------------------------------------------------------------
r14293 | frahajanirina | 2025-05-21 17:05:32 +0300 (lrb 21 Mey 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/articlesTable.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
   M /branches/ideo3_v2/integration/src/less/imports/news_panel/main.less

News: afficher la catégorie Non classés en italic et gris clair
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Templates/articlesTable.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/articlesTable.html	(révision 14292)
+++ src/js/JEditor/NewsPanel/Templates/articlesTable.html	(révision 14293)
@@ -72,7 +72,7 @@
 				<td class="author">
 					<%=item.author%>
 				</td>
-				<td class="categorie">
+				<td class="categorie <%= (item.categorie == __('unclassified')) ? 'no-category' : '' %>">
 					<%=item.categorie%>
 				</td>
 				<td class="datepub">
Index: src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 14292)
+++ src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 14293)
@@ -195,13 +195,13 @@
       var that = this;
        return list.map(function(list) {
         var categoryModel = list.getCategoryModel();
-        var categorie = ''
+        var categorie = translate('unclassified');
         if (categoryModel){ 
           var categories, categorie;
           categories = categoryModel.lang[lang];
           categorie = categories.title;
           if (categorie === translate('noCategory')) {
-            categorie = '';
+            categorie = translate('unclassified');
           }
         }
         var pageModel = list.getPageModel();
Index: src/less/imports/news_panel/main.less
===================================================================
--- src/less/imports/news_panel/main.less	(révision 14292)
+++ src/less/imports/news_panel/main.less	(révision 14293)
@@ -1174,6 +1174,10 @@
 			&:hover{
 				opacity: 1;
 			}
+		}
+		.no-category {
+			font-style: italic;
+			color: #999;
 		} 
 	}
 	.img-news img{
