Revision: r12685
Date: 2024-08-02 16:00:21 +0300 (zom 02 Aog 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: desactive page news ne change pas l'url

## Files changed

## Full metadata
------------------------------------------------------------------------
r12685 | s<PERSON><PERSON><PERSON><PERSON><PERSON> | 2024-08-02 16:00:21 +0300 (zom 02 Aog 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js

News: desactive page news ne change pas l'url
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 12684)
+++ src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 12685)
@@ -134,9 +134,11 @@
       if (article) {
         if (_.contains(article.state, 2)) {
           var pageModel = article.getPageModel();
-            pageModel.active = !pageModel.active;
-            pageModel.save();
-            $page.toggleClass('disabled');
+          pageModel.active = !pageModel.active;
+          //pour qu'il ne change pas l'url 
+          pageModel.set('last_url',null);
+          pageModel.save();
+          $page.toggleClass('disabled');
         }
       }
     },
