Revision: r14148
Date: 2025-04-22 09:54:27 +0300 (tlt 22 Apr 2025) 
Author: frahajanirina 

## Commit message
Wishlist:News:ajout notif lorsque l'article a été importé

## Files changed

## Full metadata
------------------------------------------------------------------------
r14148 | frahajanirina | 2025-04-22 09:54:27 +0300 (tlt 22 Apr 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/UploadArticleView.js

Wishlist:News:ajout notif lorsque l'article a été importé
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Views/UploadArticleView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/UploadArticleView.js	(révision 14147)
+++ src/js/JEditor/NewsPanel/Views/UploadArticleView.js	(révision 14148)
@@ -65,6 +65,18 @@
                 processData: false,
                 success: function (response) {
                     self.setLoading(false);
+                    $.toast({
+                        text: translate("uploadSuccess"), 
+                        icon: 'icon-check-circle', 
+                        type:'success',
+                        appendTo:'#news-view .zone',
+                        showHideTransition: 'fade', 
+                        hideAfter: 5000, 
+                        position: {top :50,right:50},  
+                        textAlign: 'left', 
+                        allowToastClose:false,
+                        loader:false
+                    });
                     Backbone.trigger('upload:success', response);
                 },
                 error: function (xhr) {
