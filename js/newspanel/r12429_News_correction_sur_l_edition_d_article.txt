Revision: r12429
Date: 2024-06-18 12:15:15 +0300 (tlt 18 Jon 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: correction sur l'edition d'article

## Files changed

## Full metadata
------------------------------------------------------------------------
r12429 | sraz<PERSON><PERSON><PERSON><PERSON> | 2024-06-18 12:15:15 +0300 (tlt 18 Jon 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/Article.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/NewsPanel.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/ZoneToolbox.html
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/configArticle.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/configCategorie.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/emptyArticle.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/AddArticleView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/ArticleEditorView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/AvailableView.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/ConfigArticleView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/NewsEditorView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/ZoneToolBox.js
   M /branches/ideo3_v2/integration_news/src/less/imports/news_panel/main.less

News: correction sur l'edition d'article
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Templates/NewsPanel.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/NewsPanel.html	(révision 12428)
+++ src/js/JEditor/NewsPanel/Templates/NewsPanel.html	(révision 12429)
@@ -34,6 +34,11 @@
                 <span>+</span><%=__("newsAddACategory")%>
             </a>
         <% } %>
+        <% if(canAddNews){ %>
+            <a class="dialog-view-trigger addArticle">
+                <span>+</span><%=__("newsAddArticle")%>
+            </a>
+        <% } %>
 	</aside>
     <div id="item-config" class="panel-container"></div>
   
Index: src/js/JEditor/NewsPanel/Templates/ZoneToolbox.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/ZoneToolbox.html	(révision 12428)
+++ src/js/JEditor/NewsPanel/Templates/ZoneToolbox.html	(révision 12429)
@@ -1,9 +1,5 @@
 <span class="label"><%=__("zoneToolboxLabel")%></span>
 <div class="btn-group">
-    <button type="button" class="btn btn-default traduice%>">
-        <span class="icon icon-layout-ok %>"></span>
-        <span class="label"><%=__('traduice')%></span>
-    </button>
     <button type="button" class="btn btn-default add-content" id="available-blocks-trigger"><span class="icon icon-add"></span><span class="label"><%=__("addContent")%></span></button>
     <button type="button" class="btn btn-default publish" id="publish-config-trigger"><span class="icon icon-rotation-right"></span><span class="label"><%=__("publish")%></span></button>
 </div>
Index: src/js/JEditor/NewsPanel/Templates/configArticle.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/configArticle.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Templates/configArticle.html	(révision 12429)
@@ -0,0 +1,40 @@
+<div id="meta-configuration" class="meta-configuration scrollbar-classic">
+    <header class="panel-head">
+        <span class="icon icon-params"></span>
+        <h1 class="panel-name"><%=__('config')%></h1>
+    </header>
+    <div class="panel-content active">
+        <div class="panel-content-intro">
+            <%= __("configDescArt")%>
+        </div>
+    </div>
+    <div class="option-content ">
+       <label for="meta-title">
+        <span class="icon icon-html"></span>
+        <span><%=__('metaTitleLabel')%></span>
+       </label>
+       <input type="text" name="metaTitle" value="<%=metaTitle%>"  id="meta-title">
+
+       <label for="url">
+        <span class="icon icon-params"></span>
+        <span><%=__('urlArtLabel')%></span>
+       </label>
+        <p><a href="/news/<%=url%>">/news/<%=url%>.php</a></p>
+    </div>
+</div>
+<footer class="foot">
+    <div class="button-group save-or-cancel" >
+        <a class="button cancel" data-action="cancel">
+            <span class="wrapper">
+                <span class="icon"></span>
+                <span class="text"><%= __("cancel")%></span>
+            </span>
+        </a>
+        <a class="button save" data-action="save">
+            <span class="wrapper">
+                <span class="icon"></span>
+                <span class="text"><%= __("apply")%></span>
+            </span>
+        </a>
+    </div>
+</footer>
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Templates/configCategorie.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/configCategorie.html	(révision 12428)
+++ src/js/JEditor/NewsPanel/Templates/configCategorie.html	(révision 12429)
@@ -13,13 +13,13 @@
         <span class="icon icon-html"></span>
         <span><%=__('metaTitleLabel')%></span>
        </label>
-       <input type="text" name="metaTitle" value="<%=metaTitle%>"  id="meta-title">
+       <input type="text" name="metaTitle" value="<%=metaTitle%>"  id="meta-title" disabled>
 
        <label for="meta-description">
         <span class="icon icon-html"></span>
         <span><%=__('metaDescLabel')%></span>
        </label>
-       <textarea name="metaDescription" id="meta-description" cols="20" rows="10" ><%=metaDescription%></textarea>
+       <textarea name="metaDescription" id="meta-description" cols="20" rows="10" disabled ><%=metaDescription%></textarea>
        <label for="url">
         <span class="icon icon-params"></span>
         <span><%=__('urlLabel')%></span>
Index: src/js/JEditor/NewsPanel/Templates/emptyArticle.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/emptyArticle.html	(révision 12428)
+++ src/js/JEditor/NewsPanel/Templates/emptyArticle.html	(révision 12429)
@@ -10,11 +10,13 @@
             <%= __("howToAdd") %>
         </span>
         <div class="button-add">
+            <% if(canAddCat){%>
             <a>
                 <span class="addCategory">
                     <span>+</span><%=__("newsAddACategory")%>
                 </span>
             </a>
+            <%}%>
             <a>
                 <span class="addArticle">
                     <span>+</span><%=__("newsAddArticle")%>
Index: src/js/JEditor/NewsPanel/Views/AvailableView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/AvailableView.js	(révision 12428)
+++ src/js/JEditor/NewsPanel/Views/AvailableView.js	(révision 12429)
@@ -50,9 +50,9 @@
                                     this.dom.body.addClass('dragging');
                                 },
                                 initialize: function () {
-                                    if (this.options.pagePanel)
-                                        this.pagePanel = this.options.pagePanel;
-                                    delete this.options.pagePanel;
+                                    if (this.options.newsPanel)
+                                        this.newsPanel = this.options.newsPanel;
+                                    delete this.options.newsPanel;
                                     this.modelLib = {};
                                     this._super();
                                     this.checkMissingBlocks();
@@ -133,7 +133,7 @@
                                     var section = new Section({columns: [new Column({width: 12, blocks: [clone]})]});
                                     // check if zone have popup 
                                     var isPopup = false;
-                                    var sections = this.pagePanel.currentZone.attributes.sections;
+                                    var sections = this.newsPanel.currentZone.attributes.sections;
                                     var index = 0;
                                     if (sections.length > 0){
                                         _.each(sections, function(model,i){
@@ -144,8 +144,8 @@
                                         });
                                     }
                                     at = (sections.length-1 === index && isPopup)?(sections.length-1) : (sections.length);
-                                    this.pagePanel.currentZone.addChild(section, at);
-                                    this.pagePanel.hideAvailableBlocks();
+                                    this.newsPanel.currentZone.addChild(section, at);
+                                    this.newsPanel.hideAvailableBlocks();
                                     //Scroll en bas de la page et le focus
                                     $('[data-cid="'+clone.cid+'"]').focus();
                                     // document.querySelector('[data-cid="'+clone.cid+'"]').focus();
Index: src/js/JEditor/NewsPanel/Views/ConfigArticleView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ConfigArticleView.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Views/ConfigArticleView.js	(révision 12429)
@@ -0,0 +1,110 @@
+define([
+    "jquery",
+	"underscore",
+	"JEditor/Commons/Events",
+	"JEditor/Commons/Ancestors/Views/View",
+    "text!JEditor/NewsPanel/Templates/configArticle.html",
+    "JEditor/App/Views/RightPanelView",
+    "i18n!JEditor/NewsPanel/nls/i18n",
+], function ($, _, Events, View, template, RightPanelView, translate) {
+    
+    /**
+     * 
+     * @type ConfigArticleView
+     */
+    var ConfigArticleView = View.extend({
+
+        tagName: 'div',
+        className: 'right-panel-nav',
+        _transitionDelay: 250,
+        rendered: false,
+        _currentCid: null,
+        events: {
+            'click .button.save': '_onSaveClick', 
+            'click .button.cancel': '_onCancelClick', 
+            'click .panel-menu>a': '_onMenuItemClick'
+        },
+        _onSaveClick: function(event) {
+            if (this.save)
+                this.save();
+            this.hide();
+        },
+        _onCancelClick: function() {
+            if (this.cancel)
+                this.cancel();
+            this.hide();
+        },
+        constructor: function(options) {
+            if (!options.rightPanelView || !(options.rightPanelView instanceof RightPanelView))
+                throw new TypeError("le constructeur attend un argument option.rightPanelView de type RightPanelView"); 
+            this.rightPanelView = options.rightPanelView;
+            View.apply(this, arguments);
+        },
+        initialize: function () {
+            this.template = this.buildTemplate(template, translate);
+            this.listenTo(this.rightPanelView, 'overlayClick', this.cancel);
+            this.rightPanelView.addContent(this);
+            this.currentLang = this.options.currentLang;
+            return this;
+        },
+         /**
+         * affiche cette reglage
+         */
+         show: function() {
+            if (this.rendered === false)
+                this.render();
+            this.rightPanelView.showPanel();
+            this._super();
+        },
+        updateHeight: function() {
+            var offset = this.$el.offset().top;
+            var scrollTop = this.dom.window.scrollTop();
+            var windowHeight = this.dom.window.height();
+            var height = windowHeight - (offset - scrollTop);
+            this.$el.height(height);
+            if (this._currentCid !== null)
+                this._byCID[this._currentCid].updateScrollables();
+        },
+        /**
+         * cache cette reglage
+         */
+        hide: function(hidePanel) {
+            hidePanel = hidePanel !== false ? true : false;
+            if (this.rendered === false)
+                this.render();
+            if (hidePanel && this.rightPanelView.isVisible())
+                this.rightPanelView.hidePanel();
+            this._super();
+        },
+        /**
+         * Rendu de la view
+         */
+        render: function () {
+            this.undelegateEvents();
+            var params = this.model.toJSON();
+            this.$el.html(this.template(params));
+            this.delegateEvents();
+            
+            return this;
+        },
+        save: function() {
+            var title = this.$('input[name="metaTitle"]');
+            this.model.metaTitle = title.val();
+            this.model.save();
+        },
+        /**
+         * annule les changements éfectuées depuis l'ouverture
+         */
+        cancel: function() {
+           this.model.cancel();
+        }
+        
+    });
+    Events.extend({
+        ConfigArticleViewEvents: {
+            SAVE: 'save',
+            CANCEL: 'cancel'
+        }
+    });
+    return ConfigArticleView;
+});
Index: src/js/JEditor/NewsPanel/Views/ZoneToolBox.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ZoneToolBox.js	(révision 12428)
+++ src/js/JEditor/NewsPanel/Views/ZoneToolBox.js	(révision 12429)
@@ -2,6 +2,7 @@
     "JEditor/Commons/Ancestors/Views/BabblerView",
     "JEditor/Commons/Events",
     "JEditor/NewsPanel/Views/SaveButton",
+    "JEditor/NewsPanel/Views/TraduiceButton",
     "JEditor/PagePanel/Contents/Zones/Views/SwitchVersionButton",
     "text!../Templates/ZoneToolbox.html",
     "i18n!../nls/i18n"
@@ -9,6 +10,7 @@
     BabblerView,
     Events,
     SaveButton,
+    TraduiceButton,
     SwitchVersionButton,
     template,
     translate
@@ -20,7 +22,9 @@
             this._super();
             this.childViews = {
                 saveButton: new SaveButton({model: this.model}),
-                switchVersionButton: new SwitchVersionButton({model: this.model})
+                switchVersionButton: new SwitchVersionButton({model: this.model}),
+                traduiceButton : new TraduiceButton({languageList : this.options.languageList, model: this.model})
+
             };
             this._template = this.buildTemplate(template, translate);
         },
@@ -33,8 +37,14 @@
         },
         render: function () {
             this._super();
-            this.undelegateEvents();
-            this.$el.html(this._template({zone: this.model}));
+            this.$el.html(this._template({
+                zone: this.model,
+                article : this.article,
+                user:this.app.user,
+                languages: this.article.languageList
+            }));
+            this.$('.add-content').before(this.childViews.traduiceButton.el);
+           
             this.$('.add-content').before(this.childViews.saveButton.el);
             if (this.app.user.can("restore_zone")) {
                 this.$('.btn-group').prepend(this.childViews.switchVersionButton.el);
@@ -50,12 +60,19 @@
             this.stopListening(this.model);
             this.model = zone;
             this.childViews.switchVersionButton.model = zone;
-            this.childViews.saveButton.model.content = zone;
+            this.childViews.saveButton.model.content.content = zone;
             this.listenTo(zone, Events.BackboneEvents.CHANGE, this.breakTemplateLink);
+            this.listenTo(this.model, Events.ContentEvents.ADD_CHILD, this.updtateArticle)
+            .listenTo(this.model, Events.ContentEvents.REMOVE_CHILD, this.updtateArticle)
             return this.render();
         },
+        updtateArticle:function (){
+            this.childViews.saveButton.model.content.content = this.model;
+        },
         setArticle: function (article) {
+            this.article = article;
             this.childViews.saveButton.model = article;
+            this.childViews.traduiceButton.model = article;
         },
         onZoneSelect: function (view, zone) {
             this.trigger(Events.ChoiceEvents.SELECT, view, zone);
@@ -65,6 +82,7 @@
                 this.dom[this.cid].templateIndicator.removeClass('icon-template-ok').addClass('icon-template-broken').parent().addClass('active');
                 this.model.customized = true;
             }
+            this.childViews.saveButton.model.content.content = zone;
         },
         doUncustomize: function () {
             this.model.customized = false;
@@ -86,7 +104,40 @@
                 });
             else
                 return;
+    },
+    /**
+     * Clone la page
+     * @param {String} lang
+     * @returns {PageView}
+     */
+    cloneArticle: function (lang) {
+        if (this.app.user.can('create_page')) {
+            this.setLoading(true);
+            console.log("Clone article");
+            this.listenToOnce(NewsUtils, Events.NewsUtilsEvents.CLONING_DONE, this.onCloningComplete)
+                    .listenToOnce(NewsUtils, Events.NewsUtilsEvents.CLONING_ERROR, this.onCloningError);
+            try {
+                NewsUtils.cloneNews(this.article, lang);
+            } catch (e) {
+                this.onCloningError();
+            }
         }
-    });
+        else
+            this.onCloningError();
+        return this;
+    },
+    onCloningComplete: function (clone) {
+        this.setLoading(false);
+        return this;
+    },
+    onCloningError: function (model, resp, options) {
+        this.setLoading(false);
+        this.error({
+            message: translate("cloningError"),
+            title: translate("error")
+        });
+        return this;
+    }
+  });
     return ZoneToolbox;
 });
Index: src/js/JEditor/NewsPanel/Models/Article.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Article.js	(révision 12428)
+++ src/js/JEditor/NewsPanel/Models/Article.js	(révision 12429)
@@ -27,6 +27,7 @@
                             metaTitle: "",
                             metaOpengraph: "",
                             page:null,
+                            news:null,
                             content:null
                         },
                         initialize: function() {
@@ -37,7 +38,7 @@
                             this.getFile();
                             this.getPage();
                             this.getCategory();
-                    
+                            this.pageCollection = PageCollection.getInstance();
                             this.lastState = this.toJSON();
                             this.on(Events.BackboneEvents.SYNC, this._onSync);
                         },
@@ -54,19 +55,9 @@
                         getPageModel: function(){
                             if (this.pageModel) return this.pageModel ;
                             if(!this.page) return null;
-                            var pageCollection = PageCollection.getInstance();
-                                pageModel = pageCollection.findWhere({id: this.page});
+                                pageModel = this.pageCollection.findWhere({id: this.page});
                                 if (pageModel) {
                                     this.pageModel = pageModel;
-                                }else {
-                                    var pageModel = new Page();
-                                    pageModel.fetchPageById(this.page).success(function(data) {
-                                        this.pageModel = new Page(data);
-                                        this.trigger(Events.ArticleAddEvents.ADDED, this);
-                                    }.bind(this)).error(function(error) {
-                                        console.log(error)
-                                    });
-                                    
                                 }
                             return this.pageModel;
                         },                  
@@ -99,14 +90,14 @@
                             return 0;
                         }
                     });
-            Events.extend(
-            {
-                ArticleAddEvents : {
+            // Events.extend(
+            // {
+            //     ArticleAddEvents : {
             
-                    ADDED : 'add:newsarticle',
-                }
-             });
+            //         ADDED : 'add:newsarticle',
+            //     }
+            //  });
                    
-            Article.SetAttributes(['category', 'state','title', 'lang','ressource','introduction', 'publicationDate','programmingDate', 'metaTitle', 'metaOpengraph', "page", "content"]);
+            Article.SetAttributes(['category', 'state','title', 'lang','ressource','introduction', 'publicationDate','programmingDate', 'metaTitle', 'metaOpengraph', "page", "content","news"]);
             return Article;
         });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12428)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12429)
@@ -18,7 +18,7 @@
     "JEditor/App/Views/RightPanelView",
     "JEditor/NewsPanel/Views/AvailableView",
     "JEditor/NewsPanel/Views/PublishConfigView",
-    "JEditor/PagePanel/Contents/Zones/Versions/Views/VersionsCollectionView",
+    "JEditor/NewsPanel/Articles/Versions/Views/VersionsCollectionView",
 
     "i18n!./nls/i18n",
     "jqueryPlugins/affix"
@@ -326,8 +326,9 @@
                                  */
                                 initVersionView: function() {
                                     this.childViews.versionsCollectionView = new VersionsCollectionView({
-                                        pagePanel: this,
-                                        currentZoneModel: this.currentZone
+                                        newsPanel: this,
+                                        currentContentModel: this.currentArticle.get('content')
+                                       // currentContentModel: this.currentContent
                                     } );
                                 },
                                 /**
@@ -608,6 +609,15 @@
                                             }
                                             return null;
                                         }
+                                    },
+                                    currentContent : {
+                                        get : function() {
+                                            if (this.childViews.newsEditorView.articleEditorView) {
+                                                return this.childViews.newsEditorView.articleEditorView.currentContent;
+                                                
+                                            }
+                                            return null;
+                                        }
                                     }
                                 });
                                 Events.extend(
Index: src/js/JEditor/NewsPanel/Views/AddArticleView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/AddArticleView.js	(révision 12428)
+++ src/js/JEditor/NewsPanel/Views/AddArticleView.js	(révision 12429)
@@ -9,6 +9,7 @@
     "JEditor/Commons/Ancestors/Views/DropDownView",
     "JEditor/NewsPanel/Models/CategorieCollection",
     "JEditor/NewsPanel/Models/ArticlesCollection",
+    "JEditor/Commons/Pages/Models/Page",
     "i18n!../nls/i18n"
   ], function ($,
     _,
@@ -20,6 +21,7 @@
     DropDownView,
     CategorieCollection,
     ArticlesCollection,
+    Page,
     translate) {
     var AddArticleView = BabblerView.extend({
         _currentCategory: null,
@@ -32,8 +34,11 @@
             'click button.saveArticle' : 'updateArticle',
             'click button.annuler' : 'cancel',
             'click .upload-article ' : 'onlyComputer',
+            'change .article-info input[name="title"]': 'setTitle',
+            'change .article-info textarea[name="introduction"]': 'setIntroduction',
            },
         initialize: function() {
+            console.log('AddArticleView initialisée');
             this.template = this.buildTemplate(template, translate);
             this.fileCollection = new FileCollection();
             this._categories = CategorieCollection.getInstance();
@@ -78,7 +83,18 @@
             this._currentCategory = selected;
             this.model.setCategorY(this._currentCategory.id);
         },
+        setIntroduction: function(event) {
+            console.log('setIntroduction appelé');
+            var introduction = event.target.value;
+            this.model.set('introduction', introduction);
+        },
+        setTitle: function(event) {
+            console.log('setTitle appelé');
+            var title = event.target.value;
+            this.model.set('title', title);
+        },
         render: function () {
+            this.undelegateEvents();
             params = {
                 create :  (this.model.id)? false : true,
                 detail : this.model.toJSON()
@@ -109,6 +125,8 @@
 
             this.$('.upload-article .uploader .preview .imagepreview').after('<span style="font-size:9px;display:block;margin-top:35px;color: #32ace0;text-align:center;">'+this.translations.DefaultMessageUploaderLogo+'</span>');
             if (!this.fileUploader.currentFile)this.$('.upload-article .uploader .message-wrapper .message').show();
+           
+            this.delegateEvents();
             return this;
         },
         _onUpload: function(file){
@@ -139,7 +157,6 @@
         },
 
         onSave: function () {
-            console.log("test");
             this.stopListening(this.model, Events.BackboneEvents.ERROR, this.onError);
             this.trigger(Events.LoadEvents.LOAD_SUCCESS, this);
             $.toast({
@@ -156,6 +173,18 @@
             });
                           
         },
+        addPageAndSave: function (){
+            var pageModel = new Page();
+            pageModel.fetchPageById(this.model.page).success(function(data) {
+                this.model.pageModel = new Page(data);
+                this.model.pageCollection.add(this.model.pageModel);
+                this.trigger(Events.ArticleAddEvents.ADDED, this.model);
+            }.bind(this)).error(function(error) {
+                console.log(error)
+            });
+            this.onSave();
+
+        },
         updateArticle:function(e) {
             e.preventDefault(); 
             e.stopImmediatePropagation();
@@ -166,7 +195,6 @@
                 this.model.save();
                 this.collection.add(this.model);
                 this.collection.trigger('change');
-                this.onSave();
             }
         },
 
@@ -196,6 +224,13 @@
                     message: translate("RessourceRequired")
                 });
             }
+            if (this.model.category.length === 0) {
+                valid = false;
+                this.error({
+                    title: translate("saveAction"),
+                    message: translate("CategoryRequired")
+                });
+            }
             return valid;
         },
       
@@ -202,18 +237,25 @@
         addArticle: function(e) {
             e.preventDefault(); 
             e.stopImmediatePropagation();
-            this.listenToOnce(this.model, Events.BackboneEvents.SYNC, this.onSave);
+            this.listenToOnce(this.model, Events.BackboneEvents.SYNC, this.addPageAndSave);
             this.listenToOnce(this.model, Events.BackboneEvents.ERROR, this.onError);
             if (this._checkInput()) {
                 this.model.save();
                 this.collection.add(this.model);
                 this.collection.trigger('change');
-               
             }
             return ;
         },
 
     });
+    Events.extend(
+        {
+            ArticleAddEvents : {
+        
+                ADDED : 'add:newsarticle',
+            }
+         });
+             
    
     return AddArticleView;
   });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Views/ArticleEditorView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticleEditorView.js	(révision 12428)
+++ src/js/JEditor/NewsPanel/Views/ArticleEditorView.js	(révision 12429)
@@ -6,22 +6,15 @@
     "JEditor/NewsPanel/Views/ArticleEditorView",
     "JEditor/Commons/Ancestors/Views/BabblerView",
     "JEditor/Commons/Ancestors/Views/LoaderView",
-    "JEditor/PagePanel/Views/PageTitleField",
     "JEditor/PagePanel/Views/PagePreview",
     "JEditor/PagePanel/Contents/Zones/Views/ContentZoneView",
     "JEditor/NewsPanel/Views/ZoneToolBox",
     "collection!JEditor/Commons/Languages/Models/ContentLanguageList",
-    "collection!JEditor/Commons/Pages/Models/PageCollection",
     "JEditor/PagePanel/Contents/Zones/Models/Zone",
-    "JEditor/PagePanel/Utils/PageUtils",
-    "JEditor/NewsPanel/Models/ArticlesCollection",
-    "JEditor/NewsPanel/Views/ArticlesCollectionView",
     "JEditor/NewsPanel/Views/AddArticleView",
     "JEditor/NewsPanel/Models/Article",
     "JEditor/PagePanel/Contents/Zones/Models/Zone",
-    "JEditor/PagePanel/Contents/Zones/Versions/Models/Version",
-    "JEditor/PagePanel/Contents/Zones/Versions/Models/VersionsCollection",
-    "JEditor/NewsPanel/Views/ConfigCategorieView",
+    "JEditor/NewsPanel/Articles/Versions/Models/ArticleContent",
     "i18n!../nls/i18n",
     //not in params
   
@@ -33,22 +26,15 @@
      ArticleEditorView, 
      BabblerView, 
      LoaderView,
-     PageTitleField, 
      PagePreview,
      ContentZoneView,
      ZoneToolBox,
      ContentLanguageList,
-     PageCollection,
      Zone,
-     PageUtils,
-     ArticlesCollection, 
-     ArticlesCollectionView, 
      AddArticleView,
      Article,
      ZoneModel,
-     Version,
-     VersionsCollection,
-     ConfigCategorieView, 
+     ArticleContent,
      translate
      ) {
    
@@ -66,14 +52,12 @@
         fadeInEffect: "fadeIn",
         zonesFetched: false,
         selectedZoneVersion: null,
-        versionsCollection: new VersionsCollection(),
         page : null,
         initialize: function () {
             this._super();
-            this.currentZoneID = this.options.zoneID || null;
             this._template = this.buildTemplate(template, translate);
             if (this.model) {
-                this.zoneToolbox = new ZoneToolBox();
+                this.zoneToolbox = new ZoneToolBox({languageList : this.options.languageList});
                 this.pagePreview = new PagePreview();
             }
             else{
@@ -86,7 +70,7 @@
                 collection:  this.options.categorieCollection,
             });
             self = this;
-            this.listenTo(this.model, Events.ArticleAddEvents.ADDED, _.bind(function(article) {
+            this.listenTo( this.articleDetail, Events.ArticleAddEvents.ADDED, _.bind(function(article) {
                 self.newsPanel.currentArticle = article;
             })); 
         },
@@ -110,21 +94,23 @@
             this.sectionCollectionView = new ContentZoneView({
                 model: this.currentZone
             });
+            if (!this.model.content.content) {
+                this.model.content.content = this.currentZone;
+            }
             return this.render();
         },
         load: function () {
             this.trigger(Events.LoadEvents.LOAD_START, this);
             try {
-                this.page = this.model.getPageModel();
-                if (this.page) {
+                var content = this.model.get('content');
 
-                   this.versionsCollection.zoneId = parseInt(this.page.main_zone);
-            
-                    this.listenToOnce(this.versionsCollection, Events.BackboneEvents.SYNC, this.onAllZoneLoad);
-                    this.listenToOnce(this.versionsCollection, Events.BackboneEvents.ERROR, function (zoneCollection, xhr, options) {
+                if (content) {
+                    this.content = new ArticleContent(content);
+                    this.listenToOnce( this.content, Events.BackboneEvents.SYNC, this.onContentLoad);
+                    this.listenToOnce( this.content, Events.BackboneEvents.ERROR, function (zoneCollection, xhr, options) {
                         this.trigger(Events.LoadEvents.LOAD_ERROR, this, xhr, options);
                     });
-                    this.versionsCollection.fetch();
+                    this.content.fetch();
                 }
                 
             } catch (e) {
@@ -132,61 +118,8 @@
             }
             return this;
         },
-        onAllZoneLoad: function () {
-            if (this.versionsCollection.length > 0) {
-                var zoneversion = this.versionsCollection.max(function(model) {
-                    return model.get('id');
-                });
-                var that = this;
-                this.listenToOnce(zoneversion, Events.BackboneEvents.ERROR, function (zoneCollection, xhr, options) {
-                    this.trigger(Events.LoadEvents.LOAD_ERROR, this, xhr, options);
-                });
-                zoneversion.fetch({
-                    success: function(res){
-                        var resJson = res.toJSON();
-                        self.zonesFetched = true;
-                        zone = new ZoneModel(that.cleanData(resJson.zoneContent, ['parentPage', 'parentPageType']));
-                        var errors = zone.getInitErrors();
-                        if (errors.length) { 
-                            this.error({
-                                message: translate("corruptedContent"),
-                                title: translate("error")
-                            });
-                            zone.resetInitErrors();
-                        }
-                        self.setZone(zone);
-                    }
-                } );
-            }else{ 
-                // on chargera toutes les zones une par une une fois les id récupérés
-                this.listenToOnce(this.page.zones, Events.BackboneEvents.SYNC, this.onZoneLoad);
-                this.listenToOnce(this.page.zones, Events.BackboneEvents.ERROR, function (zoneCollection, xhr, options) {
-                    this.trigger(Events.LoadEvents.LOAD_ERROR, this, xhr, options);
-                });
-                //on récupère les id
-                this.page.zones.fetch({
-                    data: {
-                        page: this.page.id
-                    }
-                });
-                
-            }
-            
-        },
-        /**
-         * Récupère toutes les versions pour une zone
-         */
-        fetchVersions: function(){
-            this.versionsCollection.zoneId = this.currentZoneID;
-            this.pagePanel.listenToOnce(this.collection, Events.BackboneEvents.SYNC, this.onLoadSuccess);
-            this.pagePanel.listenToOnce(this.collection, Events.BackboneEvents.ERROR, this.onLoadError);
-            this.pagePanel.onLoadStart();
-            this.versionsCollection.fetch();
-            
-            return this;
-        },
-        onZoneLoad: function () {
-            var zone = this.page.zones.get(this.currentZoneID || this.page.main_zone);
+        onContentLoad: function () {
+            var zone = new ZoneModel(this.content.content);
             this.zonesFetched = true;
             var errors = zone.getInitErrors();
             if (errors.length) { 
@@ -209,7 +142,6 @@
                 language : this.options.currentLang,
                 collection: this.options.categorieCollection,
                 languages: ContentLanguageList.getInstance(),
-                currentZoneID:this.currentZoneID
             }));
             this.$('header').affix({
                 offset: {
@@ -264,7 +196,6 @@
             if (!zone)
                 return this;
             this.setLoading(true);
-            this.currentZoneID = ((zone instanceof Zone)) ? zone.id : zone;
             if (!this.zonesFetched)
                 return this;
             else {
@@ -271,7 +202,7 @@
                 var oldZone = this.currentZone ? this.currentZone : null;
                 var callback = _.bind(function () {
                     this.currentZone = zone;
-                    var url = 'news/' + this.model.lang + '/article/' + this.model.id + '/' + this.currentZone.id;
+                    var url = 'news/' + this.model.lang + '/article/' + this.model.id + '/' + this.model.content.id;
                     this.app.params.lastUrl = url;
                     this.app.params.save();
                     this.app.router.navigate(url);
@@ -282,8 +213,7 @@
                     this.sectionCollectionView = new ContentZoneView({
                         model: this.currentZone
                     });
-                    this.listenTo(this.currentZone, Events.BackboneEvents.SYNC, this.onZoneSave);
-                    this.listenTo(this.currentZone, Events.BackboneEvents.CHANGE + ':customized', this.onCustomizedChange);
+                    this.listenTo(this.model, Events.BackboneEvents.SYNC, this.onZoneSave);
                     this.trigger(Events.LoadEvents.LOAD_SUCCESS, this);
                     if (oldZone !== null)
                         this.stopListening(oldZone);
@@ -299,48 +229,59 @@
             }
         },
         /**
-         * Enlève les données inutile dans l'objet
-         * 
-         * @param {Object} data
-         * @returns {Object}
+         * Crée la preview de la page en cours d'édition par une requête Post
+         * @param {jQuery.Events} e L'évenement jQuery Déclencheur
+         * @param {JEditor/PagePanel/Contents/Zones/Models/Zone} zoneVersion une zone antérieure à afficher da le preview
          */
-        cleanData: function (data, keys) {
-            for ( var i in keys ) {
-                // remove data on the param
-                if ( typeof data[keys[i]] !== 'undefined' ) {
-                    delete data[keys[i]];
+         preview: function (e, zoneVersion) {
+            var page = this.model,
+            zone = zoneVersion || this.currentZone;
+            //console.log(zone);
+            this.pagePreview.show(zone, page);
+
+            if (this.app.user.can("restore_zone")) {
+                if (zoneVersion) {
+                    this.selectedZoneVersion = zoneVersion;
+                    this.pagePreview.$('.save-version').show();
+                }else {
+                    this.pagePreview.$('.save-version').hide();
                 }
+            } else {
+                this.pagePreview.$('.save-version').hide();
             }
-            
-            data = this._ToCamelCase(data);
-            
-            if ( typeof data["template"] === 'undefined' ) {
-                data["template"] = "";
+
+            return false;
+        },
+        /**
+         * restauration de page avec la zone selectionnée
+         * 
+         * @param {jQuery.Events} e L'évenement jQuery Déclencheur
+         */
+        usePreviousVersion: function(e){
+
+            if (e) { e.preventDefault(); e.stopPropagation(); }
+
+            if (this.app.user.can("restore_zone")) {
+
+                this.dom.html.removeClass('no-scroll');
+                if (this.pagePreview) { this.pagePreview.remove(); }
+                this.newsPanel.rightPanelView.hidePanel();
+
+                this.setZone(this.selectedZoneVersion);
+
+                var that = this;
+                this.listenToOnce(this.currentZone, Events.BackboneEvents.SYNC, function () {
+                    that.render();
+                    that.notify({
+                        title: translate("saveAction"),
+                        message: translate("previousVersionSuccesful")
+                    });
+                });
+
+                this.model.save();
             }
-            
-            return data;
         },
-        
-        _ToCamelCase: function(data) {
-            for (var i in data) {
-                // _ to camel case
-                if (i.indexOf('_') !== -1) {
-                    var arr = i.split('_');
-                    var key = "";
-                    for (var j in arr) {
-                        if ( parseInt(j) > 0 ) {
-                            arr[j] = arr[j].replace(/^\w/, function (chr) {
-                                return chr.toUpperCase();
-                            });
-                        }
-                        key += arr[j];
-                    }
-                    data[key] = data[i];
-                    delete data[i];
-                }
-            }
-            return data;
-        }
+
     });
     Events.extend({
         ArticleEditorViewEvents : {
Index: src/js/JEditor/NewsPanel/Views/NewsEditorView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 12428)
+++ src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 12429)
@@ -11,11 +11,13 @@
     "JEditor/NewsPanel/Views/ArticlesCollectionView",
     "JEditor/NewsPanel/Views/CategorieAddView",
     "JEditor/NewsPanel/Views/ConfigCategorieView",
+    "JEditor/NewsPanel/Views/ConfigArticleView",
     "JEditor/PagePanel/Views/PagePreview",
     "JEditor/NewsPanel/Views/ArticleEditorView",
     
     "i18n!../nls/i18n",
     //not in params
+    "moment",
     "jqueryPlugins/affix"
 ], function($,
      _, 
@@ -28,7 +30,8 @@
      Article,
      ArticlesCollectionView, 
      CategorieAddView,
-     ConfigCategorieView, 
+     ConfigCategorieView,
+     ConfigArticleView, 
      PagePreview,
      ArticleEditorView,
      translate
@@ -58,6 +61,7 @@
             this.categories = CategorieCollection.getInstance();
         },
         renderArticleList :function (){
+            this.removeChildren();
             this.articlesListView = new ArticlesCollectionView({
                 collection : this.articles,
                 language : this.currentLang
@@ -67,6 +71,7 @@
         },
 
         renderAddCategorie :function (){
+            this.removeChildren();
             this.addCategorieView = new CategorieAddView({
                 languageList : this.options.languages,
                 language : this.options.currentLang,
@@ -77,12 +82,23 @@
                 self.newsPanel.currentCategorie = categorie;
             }));
             this.options.title = 'Categorie';
-            this.options.usepreview = true;
+            this.options.usepreview = false;
             this.render();
             this.categoryView.html(this.addCategorieView.render().el);
+            this.$('.config #params').hide();
          },
 
+        removeChildren: function () {
+            if (this.articlesListView)
+                this.articlesListView.remove();
+            if (this.addCategorieView)
+                this.addCategorieView.remove();
+            if (this.articleEditorView)
+                this.articleEditorView.remove();
+        },
+
          renderCategorieView :function (){
+            this.removeChildren();
             this.options.usepreview = true;
             this.addCategorieView = new CategorieAddView({
                 languageList : this.options.languages,
@@ -99,10 +115,10 @@
                 category : this.model
             });
             this.categoryView.append(this.articlesListView.render().el);
-
          },
          renderAddArticle: function (cat){
-            this.options.usepreview = true;
+            this.removeChildren();
+            this.options.usepreview = false;
             this.options.title = 'Ajouter une article';
 
             var article =  new Article()
@@ -117,9 +133,12 @@
            // this.articleEditorView.load();
             this.render();
             this.articleView.append(this.articleEditorView.render().el);
+            this.$('.config #params').hide();
         },
          renderArticlePage: function (){
+            this.removeChildren();
             this.options.usepreview = true;
+            this.model.content
             this.articleEditorView = new ArticleEditorView({
                 languageList : this.languageList,
                 language : this.currentLang,
@@ -132,12 +151,13 @@
             this.render();
             this.articleView.append(this.articleEditorView.render().el);
             var page = this.model.getPageModel() ;
-            var openArticle = (page)?'<a href="'+ page.url +'" class="btn btn-primary btn-lg btn-block">Ouvrir' : "";
+            var openArticle = (page)?'<a href="//'+ page.attributes.base_url +'" class="btn btn-primary btn-lg btn-block" target="_blank">Ouvrir' : "";
             this.$('.open-article').html( openArticle );
-            var state = this.model.state[0];
+            var state = this.model.content.state;
             switch (state) {
                 case 1:
-                    var stateHtml = '<span class="btn bleu">Programmé</span>'
+                    var date = moment(this.model.programmingDate).format("DD/MM/YY");
+                    var stateHtml = '<span class="btn bleu">Programmé '+date+'</span>'
                     break;
                 case 2:
                     var stateHtml = '<span class="btn green">Publié</span>'
@@ -177,26 +197,37 @@
          * Déclenche l'édition de l'élément (options)
          */
         edit: function() {
-            var rightPanelView, configCategorieView;
+            var rightPanelView, configView;
 
             function onClose() {
-                rightPanelView.removeContent(configCategorieView);
-                this.stopListening(configCategorieView);
+                rightPanelView.removeContent(configView);
+                this.stopListening(configView);
                 rightPanelView.hidePanel();
             }
             try {
                 rightPanelView = this.app.currentPanel.rightPanelView;
-                configCategorieView = new ConfigCategorieView(
-                    {
-                        rightPanelView : rightPanelView,
-                        model: this.model,
-                        currentLang: this.currentLang
-                    }
-                );
+                if (this.model instanceof Article) {
+                    configView = new ConfigArticleView(
+                        {
+                            rightPanelView : rightPanelView,
+                            model: this.model,
+                            currentLang: this.currentLang
+                        }
+                    );
+                }
+                else{
+                    configView = new ConfigCategorieView(
+                        {
+                            rightPanelView : rightPanelView,
+                            model: this.model,
+                            currentLang: this.currentLang
+                        }
+                    );
+                }
                
-                rightPanelView.showContent(configCategorieView);
-                this.listenToOnce(configCategorieView, Events.ConfigCategorieViewEvents.SAVE, _.bind(onClose, this))
-                    .listenToOnce(configCategorieView, Events.ConfigCategorieViewEvents.CANCEL, _.bind(onClose, this));
+                rightPanelView.showContent(configView);
+                this.listenToOnce(configView, Events.NewsEditorViewEvents.SAVE, _.bind(onClose, this))
+                    .listenToOnce(configView, Events.NewsEditorViewEvents.CANCEL, _.bind(onClose, this));
               
             } catch (e) {
                 console.error(e);
Index: src/less/imports/news_panel/main.less
===================================================================
--- src/less/imports/news_panel/main.less	(révision 12428)
+++ src/less/imports/news_panel/main.less	(révision 12429)
@@ -148,8 +148,11 @@
 		
 	}
 }
-
-.sideBar .addCategory span {
+.content-zone{
+	margin-top: 9rem;
+}
+.sideBar .addCategory span,
+.sideBar .addArticle span {
 	display: inline-block;
 	vertical-align: middle;
 	font-size: 30px;
@@ -786,7 +789,8 @@
 					justify-content: space-between;
 					a{
 						text-align: center;
-						margin: 10px;
+						margin-right: 23px;
+						margin-left: auto;
 						font-size: 14px;
 						font-weight: 600;
 						.transition(color 0.3s);
