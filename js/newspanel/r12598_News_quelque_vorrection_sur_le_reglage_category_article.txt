Revision: r12598
Date: 2024-07-11 17:03:35 +0300 (lkm 11 Jol 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: quelque vorrection sur le reglage category article

## Files changed

## Full metadata
------------------------------------------------------------------------
r12598 | srazanandralisoa | 2024-07-11 17:03:35 +0300 (lkm 11 Jol 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Models/Article.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Models/Categorie.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/configCategorie.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/ConfigArticleView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/ConfigCategorieView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/NewsEditorView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/PublishConfigView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/i18n.js

News: quelque vorrection sur le reglage category article
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Models/Categorie.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Categorie.js	(révision 12597)
+++ src/js/JEditor/NewsPanel/Models/Categorie.js	(révision 12598)
@@ -23,6 +23,7 @@
                         this._super();
                         this.file = null;
                         this.lastState = this.toJSONData();
+                        this.allLastState = this.toJSON();
                         this.on(Events.BackboneEvents.SYNC, this._onSync);
                     },
                     _onSync: function(model, response, options) {
@@ -40,6 +41,14 @@
                            self.lang[langKey].description = langData.description;
                         });
                     },
+                    cancelParams: function() {
+                        var self = this
+                        _.each(this.allLastState.lang, function(langData, langKey) {
+                           self.lang[langKey].metaTitle = langData.metaTitle;
+                           self.lang[langKey].metaDescription = langData.metaDescription;
+                           self.lang[langKey].url = langData.url;
+                        });
+                    },
                     getByLanguage :function (lang) {
                         return this.lang[lang.id];
                     },
Index: src/js/JEditor/NewsPanel/Templates/configCategorie.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/configCategorie.html	(révision 12597)
+++ src/js/JEditor/NewsPanel/Templates/configCategorie.html	(révision 12598)
@@ -19,6 +19,6 @@
         <span class="icon icon-params"></span>
         <span class="label" ><%=__('urlLabel')%></span>
     </label>
-    <p><a href="/news/<%=url%>">/news/<%=url%>.php</a></p>
+    <p><a href="<%=url%>" target="_blank"><%=url%></a></p>
         
 </div>
Index: src/js/JEditor/NewsPanel/Views/ConfigCategorieView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ConfigCategorieView.js	(révision 12597)
+++ src/js/JEditor/NewsPanel/Views/ConfigCategorieView.js	(révision 12598)
@@ -48,7 +48,7 @@
             this.rightPanelView.addContent(this);
             this.setTitle(translate('config'));
             this.setDescription(translate('configDesc'));
-            var params = this.model.lang[this.options.currentLang.id]
+            var params = this.model.lang[this.options.currentLang.id];
             var content = this.contenttemplate(params)
             this.addContent(content);
             this.currentLang = this.options.currentLang;
@@ -107,7 +107,7 @@
          * annule les changements éfectuées depuis l'ouverture
          */
         cancel: function() {
-           this.model.cancel();
+           this.model.cancelParams();
         }
         
     });
Index: src/js/JEditor/NewsPanel/Models/Article.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Article.js	(révision 12597)
+++ src/js/JEditor/NewsPanel/Models/Article.js	(révision 12598)
@@ -42,6 +42,7 @@
                             this.getCategory();
                             this.pageCollection = PageCollection.getInstance();
                             this.lastState = this.toJSONData();
+                            this.allLastState = this.toJSON();
                             this.on(Events.BackboneEvents.SYNC, this._onSync);
                         },
                         getCategoryModel: function(){
@@ -65,6 +66,7 @@
                         },                  
                         _onSync: function(model, response, options) {
                             this.lastState = this.toJSONData();
+                            this.allLastState = this.toJSON();
                         },
                         hasUnsavedChanges: function() {
                             return !(_.isEqual(this.toJSONData(), this.lastState) && _.isEqual(this.content.content.toJSON(), this.content.content.lastState));
@@ -83,6 +85,15 @@
                             this.set('title', this.lastState.title);
                             this.set('introduction', this.lastState.introduction);
                         },
+                        cancelParams: function() {
+                               this.metaTitle =  this.allLastState.metaTitle;
+                               this.metaDescription =  this.allLastState.metaDescription;
+                               this.metaOpengraph =  this.allLastState.url;
+                        },
+                        cancelPublish : function(){
+                            this.publicationDate =  this.allLastState.publicationDate;
+                            this.programmingDate =  this.allLastState.programmingDate;
+                        },
                         setCategorY: function (categorie) {
                             this.set('category', [categorie]);
                             this.categoryModel = null;
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12597)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12598)
@@ -655,6 +655,7 @@
                                             onYes : _.bind(function() {
                                                 this.childViews.newsEditorView.articleEditorView.stopListening();
                                                 this.childViews.newsEditorView.removeChildren();
+                                                this.currentArticle.unsetPublishData();
                                                 this.currentArticle.save();
                                                 this.currentArticle = null;
                                                 this.currentCategorie = categorie;
@@ -699,6 +700,41 @@
                                  * @returns 
                                  */
                                 showPublishConfig : function() {
+                                    if(this.currentArticle.get('title') =='' || !this.currentArticle.get('ressource') || (this.currentArticle.get('category').length == 0)){
+                                        this.error({
+                                            message: translate("errorPublishingInvalideElement"),
+                                            title: translate("error")
+                                        });
+                                        return false
+                                    }
+                                    else if (this.currentArticle && this.currentArticle.hasUnsavedChanges()) {
+                                        this.confirmUnsaved({
+                                            message : translate("quitWithoutSavingArt"),
+                                            title : translate("unsavedChanges"),
+                                            type : 'delete-not-saved',
+                                            onYes : _.bind(function() {
+                                                this.currentArticle.unsetPublishData();
+                                                this.currentArticle.save();
+                                                this.showPublishView();
+                                            }, this),
+                                            onNo : _.bind(function() {
+                                                this.currentArticle.content.content.cancel();
+                                                this.currentArticle.cancel();
+                                                this.showPublishView();
+                                            }, this),
+                                            options : {
+                                                dialogClass : 'delete no-close',
+                                                dontAskAgain : true
+                                            }
+                                        });
+                                    }
+                                    else{
+                                        this.showPublishView();
+                                    } 
+                                    return false
+                                },
+                                showPublishView : function() {
+
                                     function onClose() {
                                         this.rightPanelView.removeContent(this.childViews.publishConfigView);
                                         this.stopListening(this.childViews.publishConfigView);
@@ -706,31 +742,7 @@
                                         this.rightPanelView.hidePanel();
                                     }
                                     try {
-                                        if(this.currentArticle.get('title') =='' || !this.currentArticle.get('ressource') || (this.currentArticle.get('category').length == 0)){
-                                            this.error({
-                                                message: translate("errorPublishingInvalideElement"),
-                                                title: translate("error")
-                                            });
-                                            return false
-                                        }
-                                        else if (this.currentArticle && this.currentArticle.hasUnsavedChanges()) {
-                                            this.confirmUnsaved({
-                                                message : translate("quitWithoutSavingArt"),
-                                                title : translate("unsavedChanges"),
-                                                type : 'delete-not-saved',
-                                                onYes : _.bind(function() {
-                                                    this.currentArticle.save();
-                                                }, this),
-                                                onNo : _.bind(function() {
-                                                    this.currentArticle.content.content.cancel();
-                                                    this.currentArticle.cancel();
-                                                }, this),
-                                                options : {
-                                                    dialogClass : 'delete no-close',
-                                                    dontAskAgain : true
-                                                }
-                                            });
-                                        }    
+                                         
                                         this.childViews.publishConfigView = new PublishConfigView({
                                             rightPanelView : this.rightPanelView,
                                             model: this.currentArticle,
@@ -751,8 +763,6 @@
                                             title: translate("error")
                                         });
                                     }
-                        
-                                  
                                     return false;
                                 },
                                 /**
@@ -762,22 +772,39 @@
                                     this.rightPanelView.hideContent(this.childViews.publishConfigView);
                                     this.rightPanelView.hidePanel();
                                 },
-                                publishDone :function (){
+                                publishDone :function (e){
                                     this.stopListening(this.childViews.publishConfigView.model, Events.BackboneEvents.ERROR, this.publishError);
-                                    var type = (this.childViews.publishConfigView.model.programmingDate)? 'programDone':'publishDone';
-                                    $.toast({
-                                        text: translate(type), 
-                                        icon: 'icon-check-circle', 
-                                        type:'success',
-                                        appendTo: '#news-view .main',
-                                        showHideTransition: 'fade', 
-                                        hideAfter: 5000, 
-                                        position: 'top-right', 
-                                        textAlign: 'left', 
-                                        allowToastClose:false,
-                                        loader:false,
-                                        stack :1
-                                    });
+                                    if (e.programmingDate || e.publicationDate) {
+                                        var type = (this.childViews.publishConfigView.model.programmingDate)? 'programDone':'publishDone';
+                                        $.toast({
+                                            text: translate(type), 
+                                            icon: 'icon-check-circle', 
+                                            type:'success',
+                                            appendTo: '#news-view .main',
+                                            showHideTransition: 'fade', 
+                                            hideAfter: 5000, 
+                                            position: 'top-right', 
+                                            textAlign: 'left', 
+                                            allowToastClose:false,
+                                            loader:false,
+                                            stack :1
+                                        });
+                                    } else {
+                                        $.toast({
+                                            text: translate("saveSuccesful"), 
+                                            icon: 'icon-check-circle', 
+                                            type:'success',
+                                            appendTo:'#news-editor .zone .zone-toolbox',
+                                            showHideTransition: 'fade', 
+                                            hideAfter: 5000, 
+                                            position: 'top-right', 
+                                            textAlign: 'left', 
+                                            allowToastClose:false,
+                                            loader:false
+                                        });
+                                    }
+                                    
+                                    
                                 },
                                 publishError :function (){
                                     this.stopListening(this.childViews.publishConfigView.model, Events.BackboneEvents.SYNC, this.publishDone);
Index: src/js/JEditor/NewsPanel/Views/ConfigArticleView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ConfigArticleView.js	(révision 12597)
+++ src/js/JEditor/NewsPanel/Views/ConfigArticleView.js	(révision 12598)
@@ -102,7 +102,7 @@
          * annule les changements éfectuées depuis l'ouverture
          */
         cancel: function() {
-           this.model.cancel();
+           this.model.cancelParams();
         }
         
     });
Index: src/js/JEditor/NewsPanel/Views/NewsEditorView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 12597)
+++ src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 12598)
@@ -247,6 +247,34 @@
          * Déclenche l'édition de l'élément (options)
          */
         edit: function() {
+            var isArticle = (this.model instanceof Article);
+            if (this.model && this.model.hasUnsavedChanges()) {
+                this.confirmUnsaved({
+                    message : isArticle ? translate("quitWithoutSavingArt"): translate("quitWithoutSavingCat"),
+                    title : translate("unsavedChanges"),
+                    type : 'delete-not-saved',
+                    onYes : _.bind(function() {
+                       if(isArticle)this.model.unsetPublishData();
+                        this.model.save();
+                        this.showConfig();
+                    }, this),
+                    onNo : _.bind(function() {
+                        if(isArticle)this.model.content.content.cancel();
+                        this.model.cancel();
+                        this.showConfig();
+                    }, this),
+                    options : {
+                        dialogClass : 'delete no-close',
+                        dontAskAgain : true
+                    }
+                });
+            } 
+            else{
+                this.showConfig();
+            }
+           
+        },
+        showConfig:function(){
             var rightPanelView, configView;
 
             function onClose() {
Index: src/js/JEditor/NewsPanel/Views/PublishConfigView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/PublishConfigView.js	(révision 12597)
+++ src/js/JEditor/NewsPanel/Views/PublishConfigView.js	(révision 12598)
@@ -97,7 +97,7 @@
         },
         updateAffichage : function (){
             var d = new Date();
-            if (this.model.programmingDate && this.model.programmingDate != '') {
+            if (this.model.programmingDate && this.model.programmingDate != '' && !(this.model.programmingDate.date)) {
                 var parts = this.model.programmingDate.split('-');
                 d = new Date(parts[2], parts[1] - 1, parts[0]);
             } else {
@@ -141,7 +141,7 @@
          * annule les changements éfectuées depuis l'ouverture
          */
         cancel: function() {
-           this.model.cancel();
+           this.model.cancelPublish();
         }
         
     });
Index: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12597)
+++ src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12598)
@@ -87,6 +87,7 @@
 	"versions": "Versions",
 	"pasteTheSection" : "Coller la section",
 	"traduice": "Traduire",
+	"errorEditingElement":"Une erreur est survenue lors de l'édition de l'élément",
 
 	 //Article 
 	 "catArticle": "Catégorie de l'article",
Index: src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 12597)
+++ src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 12598)
@@ -87,6 +87,7 @@
 	"versions": "Versions",
 	"pasteTheSection" : "Coller la section",
 	"traduice": "Traduire",
+	"errorEditingElement":"Une erreur est survenue lors de l'édition de l'élément",
 
 	 //Article 
 	 "catArticle": "Catégorie de l'article",
Index: src/js/JEditor/NewsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12597)
+++ src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12598)
@@ -86,6 +86,7 @@
         "versions": "Versions",
         "pasteTheSection" : "Coller la section",
         "traduice": "Traduire",
+        "errorEditingElement":"Une erreur est survenue lors de l'édition de l'élément",
 
         //Article 
         "catArticle": "Catégorie de l'article",
