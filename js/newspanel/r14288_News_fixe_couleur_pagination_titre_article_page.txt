Revision: r14288
Date: 2025-05-21 15:48:08 +0300 (lrb 21 Mey 2025) 
Author: frahajanirina 

## Commit message
News:fixe couleur pagination & titre article/page

## Files changed

## Full metadata
------------------------------------------------------------------------
r14288 | frahajanirina | 2025-05-21 15:48:08 +0300 (lrb 21 Mey 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/articlesTable.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/PagePanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Templates/pagePanel.html
   M /branches/ideo3_v2/integration/src/less/imports/news_panel/main.less
   M /branches/ideo3_v2/integration/src/less/imports/pagination.less
   M /branches/ideo3_v2/integration/src/less/main.less

News:fixe couleur pagination & titre article/page
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Templates/articlesTable.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/articlesTable.html	(révision 14287)
+++ src/js/JEditor/NewsPanel/Templates/articlesTable.html	(révision 14288)
@@ -113,12 +113,12 @@
 		<nav>
 			<ul class="pagination">
 				<li class="page-item prev-page disabled">
-					<a class="page-link">« <%=__('prev')%></a>
+					<a class="page-link news">« <%=__('prev')%></a>
 				</li>
 				<li id="pages" class="page-item">
 				</li>
 				<li class="page-item next-page disabled">
-					<a class="page-link"><%=__('next')%> »</a>
+					<a class="page-link news"><%=__('next')%> »</a>
 				</li>
 			</ul>
 		</nav>
Index: src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 14287)
+++ src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 14288)
@@ -135,7 +135,7 @@
       // boutons de pages du bloc actuel
       for (var i = startPage; i <= endPage; i++) {
         (function(pageNumber) {
-          var btn = $('<a class="page-link"></a>').text(pageNumber);
+          var btn = $('<a class="page-link news"></a>').text(pageNumber);
       
           if (pageNumber === self.page) {
             btn.addClass('active');
Index: src/js/JEditor/PagePanel/PagePanel.js
===================================================================
--- src/js/JEditor/PagePanel/PagePanel.js	(révision 14287)
+++ src/js/JEditor/PagePanel/PagePanel.js	(révision 14288)
@@ -556,7 +556,7 @@
 			// boutons de pages du bloc actuel
 			for (var i = startPage; i <= endPage; i++) {
 			  (function(pageNumber) {
-				var btn = $('<a class="page-link"></a>').text(pageNumber);
+				var btn = $('<a class="page-link page"></a>').text(pageNumber);
 			
 				if (pageNumber === self.page) {
 				  btn.addClass('active');
Index: src/js/JEditor/PagePanel/Templates/pagePanel.html
===================================================================
--- src/js/JEditor/PagePanel/Templates/pagePanel.html	(révision 14287)
+++ src/js/JEditor/PagePanel/Templates/pagePanel.html	(révision 14288)
@@ -125,12 +125,12 @@
                 <nav>
                     <ul class="pagination">
                         <li class="page-item prev-page disabled">
-                            <a class="page-link">« <%=__('prev')%></a>
+                            <a class="page-link page">« <%=__('prev')%></a>
                         </li>
                         <li id="page-number" class="page-item">
                         </li>
                         <li class="page-item next-page disabled">
-                            <a class="page-link"><%=__('next')%> »</a>
+                            <a class="page-link page"><%=__('next')%> »</a>
                         </li>
                     </ul>
                 </nav>
Index: src/less/imports/news_panel/main.less
===================================================================
--- src/less/imports/news_panel/main.less	(révision 14287)
+++ src/less/imports/news_panel/main.less	(révision 14288)
@@ -1107,8 +1107,10 @@
 		border-collapse: collapse;
 		width: 100%;
 		.title a {
-			text-decoration: none;
 			color: #000;
+			&:hover {
+				color: #03a9f4;
+			}
 		}
 		.firt-row {
 			background-color: #f2f2f2;
Index: src/less/imports/pagination.less
===================================================================
--- src/less/imports/pagination.less	(révision 14287)
+++ src/less/imports/pagination.less	(révision 14288)
@@ -14,16 +14,19 @@
 }
 
 .page-link {
-  color: #007bff;
   border: 1px solid #dee2e6;
   padding: 8px 12px;
+  &.page {
+    color: #34d399;
+  }
+  &.news {
+    color: #03a9f4;
+  }
+  &:hover {
+    background-color: #e9ecef;
+  }
 }
 
-.page-link:hover {
-  background-color: #e9ecef;
-  color: #0056b3;
-}
-
 .page-item.disabled .page-link {
   color: #6c757d;
   background-color: #f8f9fa;
@@ -31,10 +34,21 @@
   cursor: default;
 }
 
-.page-item .page-link.active {
-  color: #fff;
-  background-color: #007bff;
-  border-color: #007bff;
-  cursor: default;
+.page-item {
+  .page-link {
+    &.page.active,
+    &.news.active {
+      color: #fff;
+      cursor: default;
+    }
+    &.page.active {
+      background-color: #34d399;
+      border-color: #34d399;
+    }
+    &.news.active {
+      background-color: #03a9f4;
+      border-color: #03a9f4;
+    }
+  }
 }
   
\ No newline at end of file
Index: src/less/main.less
===================================================================
--- src/less/main.less	(révision 14287)
+++ src/less/main.less	(révision 14288)
@@ -3043,8 +3043,10 @@
   width: 100%;
   border-collapse: collapse;
   .title a {
-    text-decoration: none;
-    color: var(--fg-color)!important;
+    color: var(--fg-color);
+    &:hover {
+      color: #34d399;
+    }
   }
   .firt-row {
     background-color: #f2f2f2;
