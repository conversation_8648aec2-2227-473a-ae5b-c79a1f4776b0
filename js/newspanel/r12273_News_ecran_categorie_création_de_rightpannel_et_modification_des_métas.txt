Revision: r12273
Date: 2024-04-30 15:22:46 +0300 (tlt 30 Apr 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: ecran categorie, création de rightpannel et modification des métas

## Files changed

## Full metadata
------------------------------------------------------------------------
r12273 | srazanandralisoa | 2024-04-30 15:22:46 +0300 (tlt 30 Apr 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/App/Views/RightPanelView.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/NewsConfig.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/NewsPanel.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/categorieList.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/configCategorie.html
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/globalConfig.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/newsEditor.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/CategorieAddView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/CategorieCollectionView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/ConfigCategorieView.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/GlobalConfigView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/NewsEditorView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/i18n.js
   M /branches/ideo3_v2/integration_news/src/less/imports/news_panel/import/vars.less
   M /branches/ideo3_v2/integration_news/src/less/imports/news_panel/main.less
   M /branches/ideo3_v2/integration_news/src/less/imports/panel_form_content.less
   M /branches/ideo3_v2/integration_news/src/less/main.less

News: ecran categorie, création de rightpannel et modification des métas
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/App/Views/RightPanelView.js
===================================================================
--- src/js/JEditor/App/Views/RightPanelView.js	(révision 12272)
+++ src/js/JEditor/App/Views/RightPanelView.js	(révision 12273)
@@ -67,7 +67,16 @@
         this.dom.body.addClass('pushtoleft');
         this.trigger(Events.ViewEvents.SHOW);
         var that = this;
-        if (!this.dom.pagePanel.overlay || this.dom.pagePanel.overlay.parents('html').length === 0) {
+        if(this.dom.newsPanel){
+            if (!this.dom.newsPanel.overlay || this.dom.newsPanel.overlay.parents('html').length === 0) {
+                /**
+                 * @fires 'overlayClick'
+                 */
+                this.dom.newsPanel.overlay = $(document.createElement('div')).addClass('panel-overlay').on('click.overlayclick', _.bind(this.hidePanel, this));
+                this.dom.currentPanel.content.append(this.dom.newsPanel.overlay);
+            }
+        }
+         else if (!this.dom.pagePanel.overlay || this.dom.pagePanel.overlay.parents('html').length === 0) {
             /**
              * @fires 'overlayClick'
              */
@@ -81,7 +90,15 @@
      */
     hidePanel: function() {
         this.dom.body.removeClass('pushtoleft');
-        if (this.dom.pagePanel.overlay) {
+        if(this.dom.newsPanel){
+            if (this.dom.newsPanel.overlay) {
+                this.trigger('overlayClick');
+                this.dom.newsPanel.overlay.off('click.overlayclick');
+                this.dom.newsPanel.overlay.remove();
+                delete this.dom.newsPanel.overlay;
+            }
+        }
+       else if (this.dom.pagePanel.overlay) {
             this.trigger('overlayClick');
             this.dom.pagePanel.overlay.off('click.overlayclick');
             //that.dom.pagePanel.overlay.off('dblclick.overlayclick');
Index: src/less/imports/news_panel/import/vars.less
===================================================================
--- src/less/imports/news_panel/import/vars.less	(révision 12272)
+++ src/less/imports/news_panel/import/vars.less	(révision 12273)
@@ -12,6 +12,13 @@
 @twitter-color	: #2CAAE1;
 @google-color	: #DC5442;
 @pinterest-color: #CB2128;
+@pageColor		: #34d399;
+@mybusiness-color: #4e8df7;
+@pinterest-color: #CB2128;
+@youtube-color	: #e62117;
+@linkedin-color	: #008cc9;
+@viadeo-color	: #f07355;
+@instagram-color: #125688;
 
 @bluePrimairy   : #059fe2;
 @raleway 		: 'raleway';
Index: src/less/imports/panel_form_content.less
===================================================================
--- src/less/imports/panel_form_content.less	(révision 12272)
+++ src/less/imports/panel_form_content.less	(révision 12273)
@@ -305,7 +305,17 @@
 
 body.pushtoleft {
     &.pushmore{
-        #page-edit .panel-overlay, #content .sideBar,#page-edit .header,#page-edit .main,#item-config{.transform(translateX(-650px));}
+        #page-edit .panel-overlay, 
+        #content .sideBar,
+        #page-edit .header,
+        #page-edit .main,
+        #news-editor .panel-overlay,
+        #news-editor .header,
+        #news-editor .main,
+        #item-config
+        {
+            .transform(translateX(-650px));
+        }
         .form-item-picker{
             display:block;
         }
Index: src/js/JEditor/NewsPanel/Models/NewsConfig.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/NewsConfig.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Models/NewsConfig.js	(révision 12273)
@@ -0,0 +1,20 @@
+define([
+    "underscore",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Models/Model"
+],function(_,
+        Events,
+        Model,
+){ var NewsConfig = Model.extend(
+                {
+                    defaults: {
+                        newsStyle: 0,                
+                        newsFormat:"landscape", 
+                        newsStyleAff: 1,
+                        newsNbArticle: 3,
+                    },
+    
+                });
+            NewsConfig.SetAttributes(['newsStyle', 'newsFormat', 'newsStyleAff', 'newsNbArticle']);
+        return NewsConfig;
+    });
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12272)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12273)
@@ -9,7 +9,7 @@
     "./Views/CategorieCollectionView",
     "./Views/ArticlesCollectionView",
     "./Views/NewsEditorView",
-    "./Views/ConfigCategorieView",
+    "./Views/GlobalConfigView",
     "./Models/CategorieCollection",
    // "./Models/ArticlesCollection",
     "JEditor/App/Views/RightPanelView",
@@ -27,9 +27,8 @@
             CategorieCollectionView, 
             ArticlesCollectionView,
             NewsEditorView,
-            ConfigCategorieView,
+            GlobalConfigView,
             CategorieCollection, 
-     //       ArticlesCollection,
             RightPanelView,
             translate
             ) {
@@ -81,9 +80,9 @@
                                         languages : this.languages,
                                         currentLang : this.currentLang,
                                         categorieCollection :  this.categories,
-
+                                        newsPanel : this
                                     });
-                                    this.childViews.configCategorieView = new ConfigCategorieView({
+                                    this.childViews.globalConfigView = new GlobalConfigView({
                                         pagePanel : this
                                     });
                         
@@ -158,24 +157,11 @@
                                         callback();
                                 },
                             
-                                showConfigCategorie : function(categorie) {
-                                    this.renderRightPanel();
-                                    this.rightPanelView.showContent(this.childViews.configCategorieView);
-                                    this.rightPanelView.showPanel();
-                                    return false;
-                                },
-                                /**
-                                 * Cache le panneau de blocs disponibles
-                                 */
-                                hideConfigCategorie : function() {
-                                    this.rightPanelView.hideContent(this.childViews.configCategorieView);
-                                    this.rightPanelView.hidePanel();
-                                },
                                 unload : function() {
                                     this.listenToOnce(this.rightPanelView, Events.ViewEvents.HIDE, function() {
                                         this.trigger(Events.PanelEvents.UNLOAD, this);
                                     });
-                                  //  this.rightPanelView.hidePanel();
+                                    this.rightPanelView.hidePanel();
                                 },
                                 /**
                                  * déclenché lors de la fin du chargement du panneau
@@ -188,6 +174,7 @@
                                     this.listenTo(this.childViews.langDropDown, Events.ChoiceEvents.SELECT, this._onLangSelect);
                                     this.listenTo(this.childViews.categorieList, Events.ChoiceEvents.SELECT, this._onCategorieSelect);
                                     this.listenTo(this.childViews.categorieList, 'render', this._scrollbar);
+                                    this.listenTo(this.childViews.newsEditorView, Events.NewsEditorViewEvents.SHOWRIGHTPANEL, this.showGlobalConfigView);
                                     this.render();
                                 },
                                 /**
@@ -217,17 +204,17 @@
                                 */
                                 renderRightPanel : function(content, renderChild) {
                                     // setup args
-                                    var content     = content || this.childViews.configCategorieView;
+                                    var content     = content || this.childViews.globalConfigView;
                                     var renderChild = renderChild || true;
                                     
                                     this.rightPanelView.clear();
                                     this.rightPanelView.setElement(this.dom.newsPanel.rightSidebar);
-                                    if (this.currentArticle) {
+                                    if (!this.currentCategorie) {
                                         this.rightPanelView.addContent(content);
                                         if (renderChild && renderChild !== 'noRender') {
                                             content.render();
                                         }
-                                        }
+                                    }
                                     this.dom.newsPanel.rightSidebar.affix({
                                         offset : {
                                             top : this.checkAffixTop
@@ -236,6 +223,16 @@
                                     return this;
                                 },
                                 /**
+                                 * Affiche le panel de droite en inserant les versions de la zone courante
+                                 * 
+                                */
+                                showGlobalConfigView: function(){
+                                    this.renderRightPanel(this.childViews.globalConfigView, 'noRender');
+                                    this.rightPanelView.showContent(this.childViews.globalConfigView);
+                                    this.rightPanelView.showPanel();
+                                    return false;
+                                },
+                                /**
                                 * rendu de la liste des categories (gauche)
                                 */
                                 _renderCategorieList : function() {
@@ -314,9 +311,8 @@
                                 onLangChange : function(lang, panel) {
                                     if (this.loaded) {
                                         this.childViews.langDropDown.current = this.currentLang;
-                                       // this.currentPage = null;
+                                        this.currentCategorie = null;
                                         this.childViews.categorieList.lang = lang.id;
-                                        //this.childViews.addPageView.setLanguage(lang);
                                         this.listenTo(this.childViews.categorieList, Events.ViewEvents.HIDE, function() {
                                             this._renderCategorieList();
                                             this.childViews.categorieList.render();
Index: src/js/JEditor/NewsPanel/Templates/NewsPanel.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/NewsPanel.html	(révision 12272)
+++ src/js/JEditor/NewsPanel/Templates/NewsPanel.html	(révision 12273)
@@ -20,7 +20,7 @@
     </div>
 </div>
 
-<div class="page-container" id="news-editor">
+<div class="app-panel-content" tabindex="0" id="news-editor">
     <aside class="sideBar news">
         <div class="options">
             <div class="lang">
Index: src/js/JEditor/NewsPanel/Templates/categorieList.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/categorieList.html	(révision 12272)
+++ src/js/JEditor/NewsPanel/Templates/categorieList.html	(révision 12273)
@@ -2,12 +2,13 @@
     <nav>
         <ul>
             <%_.each(content,function(categorie){ %>
-                <li class="categorie-nav-list">
-                    <a data-cid="<%= categorie.cid %>" > <%= categorie.lang[currentlang].title %> </a>
-                        <span class="info">
+                <li class="categorie-nav-list <%= categorie==current?'edit':''%> ">
+                    <a data-cid="<%= categorie.cid %>" > <%= categorie.lang[currentlang].title %>
+                        <span class="action">
                             <span class="number"><%= categorie.numberArticlesInCagetory %></span>
-                            <a class="icon icon-add" data-model="<%=categorie.cid%>"></a>
+                            <span class="icon icon-add " data-model="<%=categorie.cid%>"></span>
                         </span>
+                    </a>
                 </li>
                 <%
             });%>
Index: src/js/JEditor/NewsPanel/Templates/configCategorie.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/configCategorie.html	(révision 12272)
+++ src/js/JEditor/NewsPanel/Templates/configCategorie.html	(révision 12273)
@@ -1,28 +1,45 @@
-<div id="zone-versions-panel" class="version-collection scroll-container">
+<div id="meta-configuration" class="meta-configuration scrollbar-classic">
     <header class="panel-head">
-        <span class="icon-refresh"></span>
-        <h1 class="panel-name"><%= __("versionOfZone")%></h1>
+        <span class="icon icon-params"></span>
+        <h1 class="panel-name"><%=__('config')%></h1>
     </header>
     <div class="panel-content active">
         <div class="panel-content-intro">
-            <%= __("availableDesc")%>
+            <%= __("configDesc")%>
         </div>
     </div>
-    <div id="maxVersionWarning" class="warning">
-        <span class="icon-warning"></span><span><%= __("reachMaxVersionMsg") %></span>
+    <div class="option-content ">
+       <label for="meta-title">
+        <span class="icon icon-html"></span>
+        <span><%=__('metaTitleLabel')%></span>
+       </label>
+       <input type="text" name="metaTitle" value="<%=metaTitle%>"  id="meta-title">
+
+       <label for="meta-description">
+        <span class="icon icon-html"></span>
+        <span><%=__('metaDescLabel')%></span>
+       </label>
+       <textarea name="metaDescription" id="meta-description" cols="20" rows="10" ><%=metaDescription%></textarea>
+       <label for="url">
+        <span class="icon icon-params"></span>
+        <span><%=__('urlLabel')%></span>
+       </label>
+        <p><a href="/news/<%=url%>">/news/<%=url%>.php</a></p>
     </div>
-    <div class="option-content zone-versions availables">
-        <ul class="zone-version-items"></ul>
-        <div class="no-version">
-            <div class="icon">
-                <span class="icon-refresh"></span>
-            </div>
-            <span class="text-intro">
-                <%= __("noVersion") %>
+</div>
+<footer class="foot">
+    <div class="button-group save-or-cancel" >
+        <a class="button cancel" data-action="cancel">
+            <span class="wrapper">
+                <span class="icon"></span>
+                <span class="text"><%= __("cancel")%></span>
             </span>
-            <span class="how-to">
-                <%= __("howToZoneVersion") %>
+        </a>
+        <a class="button save" data-action="save">
+            <span class="wrapper">
+                <span class="icon"></span>
+                <span class="text"><%= __("apply")%></span>
             </span>
-        </div>
+        </a>
     </div>
-</div>
\ No newline at end of file
+</footer>
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Templates/globalConfig.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/globalConfig.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Templates/globalConfig.html	(révision 12273)
@@ -0,0 +1,154 @@
+<div id="news-configuration" class="news-configuration scroll-container">
+    <header class="panel-head">
+        <span class="icon icon-params"></span>
+        <h1 class="panel-name"><%=__('config')%></h1>
+    </header>
+    <div class="panel-content active">
+        <div class="panel-content-intro">
+            <%=__("configDesc")%>
+        </div>
+    </div>
+
+    <div class="panel-option-container animated  mr15">
+        <article class="panel-option">
+            <header>
+                <h3 class="option-name"></span> <%=__("styleDeNews")%></h3>
+                <p class="panel-content-legend"><%=__("styleDeNewsLegend")%></p>
+            </header>
+            <div>
+                <div class="category-content radio-transformed styleDeNews">
+                    <% var _id= _.uniqueId('newsStyle'); %>
+                    <div><span class="effect-radio <%=(newsStyle===0)?'active':''%>" id="<%=_id %>" data-value="0" data-helper="masonry"><span class="helper"><span class="help"><%=__("masonryLegend")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-photocss-column"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
+                    <% var _id= _.uniqueId('newsStyle'); %>
+                    <div><span class="effect-radio <%=(newsStyle===1)?'active':''%>" id="<%=_id %>" data-value="1" data-helper="grid"><span class="helper"><span class="help"><%=__("gridLegend")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-photogrid-block"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
+                    <% var _id= _.uniqueId('newsStyle'); %>
+                    <div><span class="effect-radio <%=(newsStyle===2)?'active':''%>" id="<%=_id %>" data-value="2" data-helper="list"><span class="helper"><span class="help"><%=__("listLegend")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-nav-menu"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
+                </div>
+            </div>
+        </article>
+        <article class="panel-option" id="formatImage" <%=(newsStyle===0)?'style="display:none"':''%>>
+            <header>
+                <h3 class="option-name"></span> <%=__("FormatImage")%></h3>
+                <p class="panel-content-legend"><%=__("FormatImageLegend")%></p>
+            </header>
+            <div>
+                <div class="category-content radio-transformed" id="Radioformatimage">
+                    <% var _id= _.uniqueId('formatImage'); %>
+                    <div><span class="effect-radio <%=(newsFormat=='landscape')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="landscape" data-helper="landscape"><span class="helper"><span class="help"><%=__("landscape")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-transition-alpha"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
+                    <% var _id= _.uniqueId('formatImage'); %>
+                    <div><span class="effect-radio <%=(newsFormat=='portrait')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="portrait" data-helper="portrait"><span class="helper"><span class="help"><%=__("portrait")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-image-portrait"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
+                    <% var _id= _.uniqueId('formatImage'); %>
+                    <div><span class="effect-radio <%=(newsFormat=='square')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="square" data-helper="square"><span class="helper"><span class="help"><%=__("square")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-image-square"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
+                </div>
+            </div>
+        </article>
+    
+    </div>
+   
+    <div class="mr15 news-option-margin news-nbreImage">
+        <article class="panel-option">
+            <header>
+                <h3 class="option-name"><%=__("newsNombreImage")%></h3>
+                <p class="panel-content-legend"><%=__("newsNombreImageLegend")%></p>
+            </header>
+            <div class="option-content">
+                <div class="slider-container"><span class="icon-less"></span><div class="slider"></div><span class="icon-more"></span></div>
+            </div>
+        </article>
+    </div>
+    <div class="panel-option-container animated">
+        <article class="panel-option background-color">
+            <header>
+                <h3 class="option-name"><%=__("newsStyleAffichage")%></h3>
+                <p class="panel-content-legend"><%=__("newsStyleAffichageDesc")%></p>
+            </header>
+            <div class="option-content colors">
+                <%  var _id=_.uniqueId('newsStyleAffichage');
+                %>
+                    <input type="radio" class="select-box" name="AffichageCarrouselStyle" value="1" id="<%=_id %>" <%=(newsStyleAff==1)?'checked':''%>>
+                       <label for="<%=_id %>">
+                        <div class="wrapper">
+                            <div class="horizontal-wrap">
+                                <span class="icon-wrapper">
+                                    <span class="icon-collection-style1"></span>
+                                </span>
+                                <span class="name"><%=__("Style")%> 1</span>
+                                <span class="desc"><%=__("DescStyle1")%></span>
+                            </div>
+                        </div>
+                    </label>
+                    
+                    <%  var _id=_.uniqueId('newsStyleAffichage');%>
+                    <input type="radio" class="select-box" name="AffichageCarrouselStyle" value="2" id="<%=_id %>" <%=(newsStyleAff==2)?'checked':''%>>
+                       <label for="<%=_id %>">
+                        <div class="wrapper">
+                            <div class="horizontal-wrap">
+                                <span class="icon-wrapper">
+                                    <span class="icon-collection-style2"></span>
+                                </span>
+                                <span class="name"><%=__("Style")%> 2</span>
+                                <span class="desc"><%=__("DescStyle2")%></span>
+                            </div>
+                        </div>
+                    </label>
+                    
+                    <%  var _id=_.uniqueId('newsStyleAffichage');%>
+                    <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="3" id="<%=_id %>" <%=(newsStyleAff==3)?'checked':''%>>
+                       <label for="<%=_id %>">
+                        <div class="wrapper">
+                            <div class="horizontal-wrap">
+                                <span class="icon-wrapper">
+                                    <span class="icon-collection-style3"></span>
+                                </span>
+                                <span class="name"><%=__("Style")%> 3</span>
+                                <span class="desc"><%=__("DescStyle3")%></span>
+                            </div>
+                        </div>
+                    </label>
+    
+                    <%  var _id=_.uniqueId('newsStyleAffichage');%>
+                    <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="4" id="<%=_id %>" <%=(newsStyleAff==4)?'checked':''%>>
+                       <label for="<%=_id %>">
+                        <div class="wrapper">
+                            <div class="horizontal-wrap">
+                                <span class="icon-wrapper">
+                                    <span class="icon-collection-style4"></span>
+                                </span>
+                                <span class="name"><%=__("Style")%> 4</span>
+                                <span class="desc"><%=__("DescStyle4")%></span>
+                            </div>
+                        </div>
+                    </label>
+    
+                    <%  var _id=_.uniqueId('newsStyleAffichage');%>
+                    <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="5" id="<%=_id %>" <%=(newsStyleAff==5)?'checked':''%>>
+                       <label for="<%=_id %>">
+                        <div class="wrapper">
+                            <div class="horizontal-wrap">
+                                <span class="icon-wrapper">
+                                    <span class="icon-collection-style5"></span>
+                                </span>
+                                <span class="name"><%=__("Style")%> 5</span>
+                                <span class="desc"><%=__("DescStyle5")%></span>
+                            </div>
+                        </div>
+                    </label>
+    
+                    <%  var _id=_.uniqueId('newsStyleAffichage');%>
+                    <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="6" id="<%=_id %>" <%=(newsStyleAff==6)?'checked':''%>>
+                       <label for="<%=_id %>">
+                        <div class="wrapper">
+                            <div class="horizontal-wrap">
+                                <span class="icon-wrapper">
+                                    <span class="icon-collection-style6"></span>
+                                </span>
+                                <span class="name"><%=__("Style")%> 6</span>
+                                <span class="desc"><%=__("DescStyle6")%></span>
+                            </div>
+                        </div>
+                    </label>
+                    
+            </div>
+        </article>
+    </div>
+</div>
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Templates/newsEditor.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/newsEditor.html	(révision 12272)
+++ src/js/JEditor/NewsPanel/Templates/newsEditor.html	(révision 12273)
@@ -8,11 +8,10 @@
         <div class="config-preview">
             <div class="btn-group">
             <div class="btn-group config">
-                <a class="btn dropdown-toggle page-action" href="#">
+                <button type="button"class="btn page-action" id="params-cat" >
                     <span class="icon icon-params"></span>
                     <span class="label"><%=__('config')%></span>
-                    <span class="caret"></span>
-                </a>
+                </button>
                 <ul class="dropdown-menu">
                 </ul>
             </div>
Index: src/js/JEditor/NewsPanel/Views/CategorieAddView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(révision 12272)
+++ src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(révision 12273)
@@ -68,7 +68,6 @@
                     backgroundSize: '100%',
                     backgroundRepeat: 'no-repeat'
                   });
- 
             }  
 
         },
@@ -82,6 +81,7 @@
             this.lang = lang;
             if (this.model.id) {
                 this.currentCategorieLang = (this.model.getByLanguage(lang))? this.model.getByLanguage(lang) : new CategorieLang();
+                this.trigger(Events.CategorieAddEvents.LANG_CHANGED, this.lang);
                this.render(); 
             }
         },
@@ -108,10 +108,11 @@
             const uploadParams = {
                 customStockEvent: '_parsestock_image',
                 acceptedTypes: ['image'],
-                acceptedExtensions: ['jpeg', 'jpg', 'png', 'svg'],
+                acceptedExtensions: ['jpeg', 'jpg', 'png', 'svg', 'webp'],
                 refusedExtensions: ['bmp'],
                 uploadUrl: __IDEO_UPLOADLOGO_URL__ ? __IDEO_UPLOADLOGO_URL__ : '/admin/resource-logo',
             };
+
             this.fileUploader = new FileUploaderView({
                 currentFile : this.model.getFile(),
                 collection: this.fileCollection,
@@ -154,6 +155,7 @@
             
 
         },
+
         onSave: function () {
             console.log("test");
             this.stopListening(this.model, Events.BackboneEvents.ERROR, this.onError);
@@ -172,52 +174,56 @@
             });
                           
         },
+
         updateCategorie:function(e) {
             e.preventDefault(); 
+            e.stopImmediatePropagation();
+            
+            if (this._checkInput()) {
+                this.model.lang[this.lang.id] = this.currentCategorieLang;
+                this.model.save();
+                this.collection.add(this.model);
+                this.collection.trigger('change');
+                this.onSave();
+            }
+        },
+
+        /**
+         * verication de notre input
+         */
+        _checkInput: function(){
             var title = this.$('input[name="title"]');
             var description = this.$('textarea[name="description"]');
-            
+            var valid = true
             if (title.val() =='') {
                 title.addClass('error');
-                return;
-
+                valid = false;
             }
             if (description.val() =='') {
                 description.addClass('error');
-                return;
+                valid = false;
             }
-            this.model.lang[this.lang.id]= this.currentCategorieLang;
-            this.model.save()
-            this.updateColletion(this.model);
+            return valid;
         },
-        updateColletion: function(model){
-            var existingModel = this.collection.findWhere({ id: model.id });
-            if (existingModel){
-                existingModel.set(model.toJSON());
-            }
-            else this.collection.add(model);
-            this.onSave(),
-            this.render();
-        },
       
         addCategorie: function(e) {
             e.preventDefault(); 
-            var title = this.$('input[name="title"]');
-            var description = this.$('textarea[name="description"]');
+            e.stopImmediatePropagation();
             
-            if (title.val() =='') {
-                title.addClass('error');
-                return;
+            if (this._checkInput()) {
+                // // initialisation de la valeur meta du model
+                // this.currentCategorieLang.setMeta();
 
+                this.model.lang[this.lang.id] = this.currentCategorieLang;
+                this.model.save();
+
+                this.collection.add(this.model);
+                this.collection.trigger('change');
+
+                this.onSave();
+                this.trigger(Events.CategorieAddEvents.CATEGORY_ADD, this);
             }
-            if (description.val() =='') {
-                description.addClass('error');
-                return;
-            }
-            this.model.lang[this.lang.id]= this.currentCategorieLang;
-            this.model.save();
-            this.updateColletion(this.model);
-            this.trigger(Events.CategorieAddEvents.CATEGORY_ADD, this);
+            return ;
         },
         
     });
@@ -226,6 +232,7 @@
             CategorieAddEvents : {
         
                 CATEGORY_ADD : 'add:newscategory',
+                LANG_CHANGED :'changlang:category'
             }
         });
    
Index: src/js/JEditor/NewsPanel/Views/CategorieCollectionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/CategorieCollectionView.js	(révision 12272)
+++ src/js/JEditor/NewsPanel/Views/CategorieCollectionView.js	(révision 12273)
@@ -63,11 +63,11 @@
         } else {
             list = this.sortList(list);
             if (list.length > 0) {
-                var params = _.extend({}, {content: list, currentlang : this.lang, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected, user: this.app.user}, this.addTemplateParams(this.collection, list));
+                var params = _.extend({}, {content: list, current : this._current,currentlang : this.lang, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected, user: this.app.user}, this.addTemplateParams(this.collection, list));
                 this.$el.html(this._template(params));
             }
             else {
-                var params = _.extend({}, {content: list, currentlang : this.lang, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected}, this.addTemplateParams(this.collection, list));
+                var params = _.extend({}, {content: list, current : this._current,currentlang : this.lang, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected}, this.addTemplateParams(this.collection, list));
                 this.$el.html(this._emptyTemplate(params));
             }
 
Index: src/js/JEditor/NewsPanel/Views/ConfigCategorieView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ConfigCategorieView.js	(révision 12272)
+++ src/js/JEditor/NewsPanel/Views/ConfigCategorieView.js	(révision 12273)
@@ -3,12 +3,10 @@
 	"underscore",
 	"JEditor/Commons/Events",
 	"JEditor/Commons/Ancestors/Views/View",
-    "JEditor/NewsPanel/Models/Categorie",
-    "JEditor/App/Config",
     "text!JEditor/NewsPanel/Templates/configCategorie.html",
-    "JEditor/Commons/Ancestors/Views/SaveCancelPanel",
+    "JEditor/App/Views/RightPanelView",
     "i18n!JEditor/NewsPanel/nls/i18n",
-], function ($, _, Event, View, Categorie, Config, tpl, SaveCancelPanel, translate) {
+], function ($, _, Events, View, template, RightPanelView, translate) {
     
     /**
      * 
@@ -36,21 +34,25 @@
                 this.cancel();
             this.hide();
         },
+        constructor: function(options) {
+            if (!options.rightPanelView || !(options.rightPanelView instanceof RightPanelView))
+                throw new TypeError("le constructeur attend un argument option.rightPanelView de type RightPanelView"); 
+            this.rightPanelView = options.rightPanelView;
+            View.apply(this, arguments);
+        },
         initialize: function () {
+            this.template = this.buildTemplate(template, translate);
+            this.listenTo(this.rightPanelView, 'overlayClick', this.cancel);
+            this.rightPanelView.addContent(this);
+            this.currentLang = this.options.currentLang;
             return this;
         },
          /**
-         * affiche cette collection d'option
+         * affiche cette reglage
          */
-         show: function(singleView) {
+         show: function() {
             if (this.rendered === false)
                 this.render();
-            if (!singleView)
-                singleView = this._byCID[this._order[0]];
-            this.dom.window.on('scroll.rightPanelNavView', _.bind(this.updateHeight, this));
-            this.updateHeight();
-            this.showChild(singleView.cid);
-            singleView.updateScrollables();
             this.rightPanelView.showPanel();
             this._super();
         },
@@ -64,7 +66,7 @@
                 this._byCID[this._currentCid].updateScrollables();
         },
         /**
-         * cache cette collection d'option
+         * cache cette reglage
          */
         hide: function(hidePanel) {
             hidePanel = hidePanel !== false ? true : false;
@@ -72,7 +74,6 @@
                 this.render();
             if (hidePanel && this.rightPanelView.isVisible())
                 this.rightPanelView.hidePanel();
-            this.dom.window.off('scroll.rightPanelNavView');
             this._super();
         },
         /**
@@ -80,50 +81,35 @@
          */
         render: function () {
             this.undelegateEvents();
-            this.$el.addClass('available-items');
-            this.$el.html(this.template());
-            this.fetchVersions();
-            this.scrollables();
-            $ (window).on('scroll', _.bind(this.updateScrollables, this));
+            if (!this.model.lang[this.options.currentLang.id]) {
+               return false;
+            }
+            var params = this.model.lang[this.options.currentLang.id]
+            this.$el.html(this.template(params));
             this.delegateEvents();
             
             return this;
         },
         save: function() {
-            this.collection.each(function(element, index, list) {
-                element.applyChanges();
-            });
-            this.trigger(Events.OptionCollectionViewEvents.SAVE, this);
+            var title = this.$('input[name="metaTitle"]');
+            var description = this.$('textarea[name="metaDescription"]');
+            this.model.lang[this.currentLang.id].metaDescription = description.val();
+            this.model.lang[this.currentLang.id].metaTitle = title.val();
+            this.model.save();
         },
         /**
          * annule les changements éfectuées depuis l'ouverture
          */
         cancel: function() {
-            this.collection.each(function(element, index, list) {
-                element.revertChanges();
-            });
-            _.each(this.views, function(view) {
-                view.render();
-            });
-            this.trigger(Events.OptionCollectionViewEvents.CANCEL, this);
-        },
-        /**
-         * Toggle le message empty quand il y a des versions ou pas
-         * 
-         * @returns {ConfigCategorieViewL#13.ConfigCategorieViewAnonym$1}
-         */
-        toggleEmptyVersion: function () {
-            // if no version
-            if ( this.collection.length === 0 ) {
-                this.$("#zone-versions-panel .no-version").show();
-            } else {
-                this.$("#zone-versions-panel .no-version").hide();
-            }
-            
-            return this;
+           this.model.cancel();
         }
         
     });
-    
+    Events.extend({
+        ConfigCategorieVIewEvents: {
+            SAVE: 'save',
+            CANCEL: 'cancel'
+        }
+    });
     return ConfigCategorieView;
 });
Index: src/js/JEditor/NewsPanel/Views/GlobalConfigView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/GlobalConfigView.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Views/GlobalConfigView.js	(révision 12273)
@@ -0,0 +1,145 @@
+define([
+    "jquery",
+	"underscore",
+	"JEditor/Commons/Events",
+	"JEditor/Commons/Ancestors/Views/View",
+    "JEditor/NewsPanel/Models/NewsConfig",
+    "JEditor/App/Config",
+    "text!JEditor/NewsPanel/Templates/globalConfig.html",
+    "JEditor/Commons/Ancestors/Views/SaveCancelPanel",
+    "i18n!JEditor/NewsPanel/nls/i18n",
+], function ($, _, Event, View, NewsConfig, Config, template, SaveCancelPanel, translate) {
+    
+    /**
+     * 
+     * @type GlobalConfigView
+     */
+    var GlobalConfigView = View.extend({
+
+        tagName: 'div',
+        className: 'right-panel-nav',
+        _transitionDelay: 250,
+        rendered: false,
+        _currentCid: null,
+        events: {
+            'click .button.save': '_onSaveClick', 
+            'click .button.cancel': '_onCancelClick',
+            'click .styleDeNews div .effect-radio'   : '_onChangeStylleImage',
+            'click #Radioformatimage div .effect-radio'       : '_onChangeFormatImage',
+            'slidechange .slider'   :   '_onSliderChange',
+            'change input[type="radio"].select-box': '_onStyleAffichageChange',
+      
+        },
+        _onChangeStylleImage : function(event){
+            this.$(".effect-radio").removeClass("active");
+            var $target = $(event.currentTarget);
+            $target.addClass("active");
+            var value = parseInt($target.attr("data-value"));
+            if(value===1){
+                $("#formatImage").show();
+                var elnewsFormat=$("#Radioformatimage").children().find("[data-value="+this.model.newsFormat+"]");
+                elnewsFormat.addClass("active");
+            }else{
+                $("#formatImage").hide();
+            }
+            this.model.newsStyle=value;
+        },
+          _onStyleAffichageChange :function(event){
+            var $target = $(event.currentTarget);
+            this.model.newsStyleAff = $target.val();
+        },
+        /**
+        * chagement du slider
+        */
+        _onSliderChange: function(event,ui){
+            var value = ui.value;
+            this.model.newsNbArticle = value;
+            return false;
+         },
+        _onChangeFormatImage : function(event){
+            this.$(".formatImage-radio").removeClass("active");
+            var $target = $(event.currentTarget);
+            $target.addClass("active");
+            var value = $target.attr("data-value");
+            this.model.newsFormat=value;
+        },
+        initialize: function() {
+            this._super();
+            this.model = new NewsConfig();
+            this.template = this.buildTemplate(template, translate);
+        },
+        render: function() {
+            this.undelegateEvents();
+            
+            this.delegateEvents();
+            return this;
+        },
+        _onSaveClick: function(event) {
+            if (this.save)
+                this.save();
+            this.hide();
+        },
+        _onCancelClick: function() {
+            if (this.cancel)
+                this.cancel();
+            this.hide();
+        },
+       
+        /**
+         * Rendu de la view
+         */
+        render: function () {
+            this.undelegateEvents();
+            var templateVars = {
+                newsStyle:this.model.newsStyle,
+                newsFormat:this.model.newsFormat,
+                newsNbArticle:this.model.newsNbArticle,
+                newsStyleAff:this.model.newsStyleAff
+            };
+            this.$el.html(this.template(templateVars));
+            this.$('.news-nbreImage .slider').slider({
+                range: "min",
+                value: this.model.newsNbArticle,
+                min: 1,
+                max: 5,
+                step: 1
+            });
+            this.scrollables();
+            // var saveCancel = new SaveCancelPanel();
+            // saveCancel.on("save",function(){
+            //     optionCollection.each(function(element, index, list) {
+            //         element.applyChanges();
+            //     });
+            // });
+            // saveCancel.on("cancel",function () {
+            //     optionCollection.each(function(element, index, list) {
+            //         element.revertChanges();
+            //     });
+            //     _.each(views, function(view) {
+            //         view.render();
+            //     });
+            // });
+            // saveCancel.setTitle(contentView.translate(titlePrefix + 'Option'));
+            // saveCancel.contentView=contentView;
+            // saveCancel.rightPanelView=rightPanelView;
+            // rightPanelView.addContent(saveCancel);
+            this.delegateEvents();
+            
+            return this;
+        },
+        save: function() {
+            this.model.applyChanges();
+            this.trigger(Events.OptionCollectionViewEvents.SAVE, this);
+        },
+        /**
+         * annule les changements éfectuées depuis l'ouverture
+         */
+        cancel: function() {
+            this.model.revertChanges();
+            this.trigger(Events.OptionCollectionViewEvents.CANCEL, this);
+        }
+        
+    });
+    
+    return GlobalConfigView;
+});
Index: src/js/JEditor/NewsPanel/Views/NewsEditorView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 12272)
+++ src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 12273)
@@ -10,11 +10,12 @@
     "JEditor/NewsPanel/Models/ArticlesCollection",
     "JEditor/NewsPanel/Views/ArticlesCollectionView",
     "JEditor/NewsPanel/Views/CategorieAddView",
+    "JEditor/NewsPanel/Views/ConfigCategorieView",
     "JEditor/PagePanel/Contents/Zones/Views/ZoneToolBox",
     "JEditor/PagePanel/Views/PagePreview",
     "i18n!../nls/i18n",
     //not in params
-    "jqueryPlugins/dropdown",
+   // "jqueryPlugins/dropdown",
     "jqueryPlugins/affix"
 ], function($,
      _, 
@@ -26,7 +27,8 @@
      ContentLanguageList, 
      ArticlesCollection, 
      ArticlesCollectionView, 
-     CategorieAddView, 
+     CategorieAddView,
+     ConfigCategorieView, 
      ZoneToolBox,
      PagePreview,
      translate
@@ -35,6 +37,7 @@
     var NewsEditorView = BabblerView.extend({
         events: {
             'click .btn.preview': 'preview',
+            'click #params-cat' : '_onParamsClick',
             'click a[data-action]': '_onActionClick',
             'click .save-version': 'usePreviousVersion'
         },
@@ -49,6 +52,7 @@
             this.options.i18n = true;
             this._template = this.buildTemplate(template, translate);
             this.pagePreview = new PagePreview();
+            this.currentLang = this.options.currentLang;
 
         },
         renderArticleList :function (){
@@ -74,6 +78,7 @@
          },
 
          renderCategorieView :function (){
+            this.options.usepreview = true;
             this.addCategorieView = new CategorieAddView({
                 languageList : this.options.languages,
                 language : this.options.currentLang,
@@ -88,6 +93,11 @@
                 language : this.currentLang,
             });
             this.$("#content-editor").append(this.articlesListView.render().el);
+            self = this
+            this.listenTo(this.addCategorieView, Events.CategorieAddEvents.LANG_CHANGED, _.bind(function(lang) {
+                self.currentLang = lang;
+            }));
+
          },
          renderArticlePage: function (){
             this.options.usepreview = true;
@@ -120,15 +130,60 @@
                  usepreview : this.options.usepreview || false
             }));
             this.$(".message").hide();
-            this.$('.zone-selector>.dropdown-toggle, .page-action').dropdown();
+         //   this.$('.zone-selector>.dropdown-toggle, .page-action').dropdown();
             this.delegateEvents();
 
             return this;
         },
+        _onParamsClick: function (){
+            if (this.model) {
+                this.edit()
+            }
+            else  this.trigger(Events.NewsEditorViewEvents.SHOWRIGHTPANEL, this);
+            return false;
+        },
+        /**
+         * Déclenche l'édition de l'élément (options)
+         */
+        edit: function() {
+            var rightPanelView, configCategorieView;
+
+            function onClose() {
+                rightPanelView.removeContent(configCategorieView);
+                this.stopListening(configCategorieView);
+                rightPanelView.hidePanel();
+            }
+            try {
+                rightPanelView = this.app.currentPanel.rightPanelView;
+                configCategorieView = new ConfigCategorieView(
+                    {
+                        rightPanelView : rightPanelView,
+                        model: this.model,
+                        currentLang: this.currentLang
+                    }
+                );
+               
+                rightPanelView.showContent(configCategorieView);
+                this.listenToOnce(configCategorieView, Events.ConfigCategorieVIewEvents.SAVE, _.bind(onClose, this))
+                    .listenToOnce(configCategorieView, Events.ConfigCategorieVIewEvents.CANCEL, _.bind(onClose, this));
+              
+            } catch (e) {
+                console.error(e);
+                this.error({
+                    message: translate("errorEditingElement"),
+                    title: translate("error")
+                });
+            }
+        },
         show: function(animate) {
         this._super(animate);
         this.dom.window.scroll();
         },
     });
+    Events.extend({
+        NewsEditorViewEvents : {
+            SHOWRIGHTPANEL: 'edit:config'
+        }
+    });
     return NewsEditorView;
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12272)
+++ src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12273)
@@ -18,4 +18,35 @@
 	"preview": "Aper\u00e7u",
 	"quitWithoutSaving": "Vous n'avez pas enregistr\u00e9 les modifications apport\u00e9es \u00e0 la catégorie, voulez-vous les enregistrer ?",
 	"unsavedChanges": "sauvegarder les changements",
+	// config global
+	"styleDeNews" :   "Affichage de la liste des articles",
+	"styleDeNewsLegend" :   "Appliquez un style à la liste des articles",
+	"masonryLegend"         :   "Tuiles",
+	"gridLegend"            :   "Grille",
+	"listLegend"            :   "Liste",
+	"newNombreImage"       :   "Nombre de colonne",
+	"newNombreImageLegend" :   "Glissez pour ajuster le nombre de colonne affichées",
+	"newStyleAffichage"    :   "Style des articles",
+	"newStyleAffichageDesc":   "Appliquez un style aux articles",
+	"FormatImage"           :   "Format de l'image",
+	"FormatImageLegend"     :   "Appliquez un format d'image aux articles",
+	'landscape'             :   "Paysage",
+	'portrait'              :   "Portrait",
+	'square'                :   "Carré",
+	"DescStyle1"           :  "Texte sous l'image", 
+	"DescStyle2"           :  "Texte encadré sur l'image",
+	"DescStyle3"           :  "Texte sur l'image, animé au survol",
+	"addGalerieField": "ajouter une collection", 
+	"DescStyle4"           :  "Texte sous l'image, bordures",
+	"DescStyle5"           :  "Texte sous l'image, images arrondies (idéal images carrés)",
+	"DescStyle6"           :  "Texte à côté de l'image",
+	//config category
+	"configDesc" 			: "Ajustez les paramètres des Catégorie/articles",
+	"metaTitleLabel" 		: "Méta Title",
+	"metaDescLabel" 		: "Méta Description",
+	"urlLabel" 				: "URL de l'article/la catégorie",
+	"cancel" 				: "Annuler",
+	"apply" 				: "Appliquer",
+
+	"saveSuccesful": "Le contenu a été sauvegardé avec succès",
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 12272)
+++ src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 12273)
@@ -18,4 +18,37 @@
 	"preview": "Aper\u00e7u",
 	"quitWithoutSaving": "Vous n'avez pas enregistr\u00e9 les modifications apport\u00e9es \u00e0 la catégorie, voulez-vous les enregistrer ?",
     "unsavedChanges": "sauvegarder les changements",
+	// config global
+	"styleDeNews" :   "Affichage de la liste des articles",
+	"styleDeNewsLegend" :   "Appliquez un style à la liste des articles",
+	"masonryLegend"         :   "Tuiles",
+	"gridLegend"            :   "Grille",
+	"listLegend"            :   "Liste",
+	"newNombreImage"       :   "Nombre de colonne",
+	"newNombreImageLegend" :   "Glissez pour ajuster le nombre de colonne affichées",
+	"newStyleAffichage"    :   "Style des articles",
+	"newStyleAffichageDesc":   "Appliquez un style aux articles",
+	"FormatImage"           :   "Format de l'image",
+	"FormatImageLegend"     :   "Appliquez un format d'image aux articles",
+	'landscape'             :   "Paysage",
+	'portrait'              :   "Portrait",
+	'square'                :   "Carré",
+	"DescStyle1"           :  "Texte sous l'image", 
+	"DescStyle2"           :  "Texte encadré sur l'image",
+	"DescStyle3"           :  "Texte sur l'image, animé au survol",
+	"addGalerieField": "ajouter une collection", 
+	"DescStyle4"           :  "Texte sous l'image, bordures",
+	"DescStyle5"           :  "Texte sous l'image, images arrondies (idéal images carrés)",
+	"DescStyle6"           :  "Texte à côté de l'image",
+
+	//config category
+	"configDesc" 			: "Ajustez les paramètres des Catégorie/articles",
+	"metaTitleLabel" 		: "Méta Title",
+	"metaDescLabel" 		: "Méta Description",
+	"urlLabel" 				: "URL de l'article/la catégorie",
+	"cancel" 				: "Annuler",
+	"apply" 				: "Appliquer",
+
+	
+	"saveSuccesful": "Le contenu a été sauvegardé avec succès",
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12272)
+++ src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12273)
@@ -19,6 +19,38 @@
         "preview": "Aper\u00e7u",
         "quitWithoutSaving": "Vous n'avez pas enregistr\u00e9 les modifications apport\u00e9es \u00e0 la catégorie, voulez-vous les enregistrer ?",
         "unsavedChanges": "sauvegarder les changements",
+        // config global
+        "styleDeNews" :   "Affichage de la liste des articles",
+        "styleDeNewsLegend" :   "Appliquez un style à la liste des articles",
+        "masonryLegend"         :   "Tuiles",
+        "gridLegend"            :   "Grille",
+        "listLegend"            :   "Liste",
+        "newNombreImage"       :   "Nombre de colonne",
+        "newNombreImageLegend" :   "Glissez pour ajuster le nombre de colonne affichées",
+        "newStyleAffichage"    :   "Style des articles",
+        "newStyleAffichageDesc":   "Appliquez un style aux articles",
+        "FormatImage"           :   "Format de l'image",
+        "FormatImageLegend"     :   "Appliquez un format d'image aux articles",
+        'landscape'             :   "Paysage",
+        'portrait'              :   "Portrait",
+        'square'                :   "Carré",
+        "DescStyle1"           :  "Texte sous l'image", 
+        "DescStyle2"           :  "Texte encadré sur l'image",
+        "DescStyle3"           :  "Texte sur l'image, animé au survol",
+        "addGalerieField": "ajouter une collection", 
+        "DescStyle4"           :  "Texte sous l'image, bordures",
+        "DescStyle5"           :  "Texte sous l'image, images arrondies (idéal images carrés)",
+        "DescStyle6"           :  "Texte à côté de l'image",
+
+        //config category
+        "configDesc" 			: "Ajustez les paramètres des Catégorie/articles",
+        "metaTitleLabel" 		: "Méta Title",
+        "metaDescLabel" 		: "Méta Description",
+        "urlLabel" 				: "URL de l'article/la catégorie",
+        "cancel" 				: "Annuler",
+        "apply" 				: "Appliquer",
+
+        "saveSuccesful": "Le contenu a été sauvegardé avec succès",
     },
     "fr-fr": true,
     "fr-ca": true
Index: src/less/imports/news_panel/main.less
===================================================================
--- src/less/imports/news_panel/main.less	(révision 12272)
+++ src/less/imports/news_panel/main.less	(révision 12273)
@@ -14,7 +14,51 @@
 @import '../common/module/side-bar';
 @import '../common/module/error-message';
 
+@import '../page_panel/module/checkbox';
+@import '../page_panel/module/classic-modifier';
+@import '../page_panel/module/radio';
+@import '../page_panel/module/ideo-btn';
+@import '../page_panel/module/dialog';
+@import '../page_panel/module/panel-overlay';
+@import '../page_panel/module/blockquote';
+@import '../page_panel/module/panel_zoneVersions';
+@import '../page_panel/module/tooltip';
+@import '../lesshat';
+@import '../common/module/app-intro';
 
+@import '../page_panel/module/block-options/inline-label';
+@import '../page_panel/module/block-options/inline-block-label';
+@import '../page_panel/module/block-options/block-label';
+@import '../page_panel/module/block-options/social-option';
+@import '../page_panel/module/block-options/grid-option';
+@import '../page_panel/module/block-options/advanced-option';
+@import '../page_panel/module/block-options/save-or-cancel.less';
+@import '../page_panel/module/block-options/btn-gallery-content';
+@import '../page_panel/module/block-options/field-input';
+@import '../page_panel/module/block-options/btn-group';
+@import '../page_panel/module/block-options/col-setup';
+@import '../page_panel/module/block-options/show-part';
+@import '../page_panel/module/block-options/compare-option';
+
+@import '../page_panel/module/block-render/social-bar';
+@import '../page_panel/module/block-render/grid';
+@import '../page_panel/module/block-render/click-rdv';
+@import '../page_panel/module/block-render/table';
+@import '../page_panel/module/block-render/render-fake';
+@import '../page_panel/module/block-render/compare';
+
+@import '../page_panel/module/checkbox';
+@import '../page_panel/module/classic-modifier';
+@import '../page_panel/module/radio';
+@import '../page_panel/module/ideo-btn';
+@import '../page_panel/module/dialog';
+@import '../page_panel/module/panel-overlay';
+@import '../page_panel/module/blockquote';
+@import '../page_panel/module/panel_zoneVersions';
+@import '../page_panel/module/tooltip';
+
+@import '../common/oocss/oocss';
+
 .header, .sub-header__infos {
 	background-color: @newsColorLight;
 	.news-title{
@@ -26,34 +70,85 @@
 	color: #ddd;
 }
 .categorie-nav-list{
-	display: flex;
-	justify-content: space-between;
-	border-bottom: 1px solid @grey;
-	padding: 10px;
-	margin: 0px 10px;
-	a {
-		cursor: pointer;
-	}
 	&.edit{
-		background: #fff;
-		font-weight: 600;
-		color: #020705;
+		background-color: #fff;
 	}
-	.info .number {
-		background: @greyM;
-		padding: 3px 9px;
-		color:@greyXL ;
-		border-radius: 100%;
-		font-size: small;
+	a {
+	  cursor: pointer;
+	  position: relative;
+	  display: block;
+	  text-decoration: none;
+	  font-family: 'Raleway', sans-serif;
+	  font-size: 14px;
+	  font-weight: 400;
+	  padding: 11px 55px 11px 31px;
+	  text-overflow: ellipsis;
+	  .transition(background 300ms ease);
+	  overflow: hidden;
+	  border-bottom: 1px solid #bcbebd;
+	  }
+	  
+	.action {
+		text-align: right;
+		position: absolute;
+		right: 10px;
+		top: 10px;
+		width: 60px;
+		height: 10px;
+		line-height: 10px;
+		.transition(opacity 300ms ease);
+		.number {
+			background: @greyM;
+			padding: 3px 9px;
+			color:@greyXL ;
+			border-radius: 100%;
+			font-size: small;
+		}
+		.icon{
+			display: inline-block;
+			font-size: 10px;
+			background: @newsColorLight;
+			color:@greyXL ;
+			padding: 5px 5px;
+			border-radius: 100%;
+			font-size: small;
+			&:hover {
+				color: #d42525;
+			  }
+		}
+	  }
+}
+.meta-configuration,
+.news-configuration {
+	position: absolute;
+	top: 20px;
+	left: 20px;
+	right: 5px;
+	bottom: 20px;
+	.icon{
+		display: inline;
 	}
-	.info .icon{
-		background: @newsColorLight;
-		color:@greyXL ;
-		padding: 5px 5px;
-		border-radius: 100%;
-		font-size: small;
+	.option-content{
+		label{
+			font-size: 1.2em;
+			font-weight: 200;
+			margin: 0;
+		}
+		textarea, input {
+		width: 345px;
+		margin-left: 3px;
+		padding: .5em .9em;
+		border: none;
+		outline: none;
+		background-color: #000;
+		color: #999;
+		margin: 1.2em;
+		border-bottom: 1px solid #333333;
+		}
+		
 	}
 }
+
 .sideBar .addCategory span {
 	display: inline-block;
 	vertical-align: middle;
@@ -156,6 +251,11 @@
 				font-family: 'raleway';
 				color: #999;
 				font-weight: normal;
+				&.error{
+					outline: none;
+					border: 2px solid @googleColor;
+				}
+				
 			}
 			
 			.btn.dropdown-toggle{
@@ -356,7 +456,7 @@
   
 		}
 		&.unlocked {
-		  i.icon-lock {
+		  i.number {
 			display: none;
 		  }
 		}
Index: src/less/main.less
===================================================================
--- src/less/main.less	(révision 12272)
+++ src/less/main.less	(révision 12273)
@@ -1128,9 +1128,12 @@
     display: none;
   }
   #page-edit .panel-overlay,
+  #news-editor .panel-overlay,
   #content .sideBar,
   #page-edit .header,
+  #news-editor .header,
   #page-edit .main,
+  #news-editor .main,
   #item-config {
     .transform(translateX(-420px));
   }
