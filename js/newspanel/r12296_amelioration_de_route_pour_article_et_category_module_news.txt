Revision: r12296
Date: 2024-05-16 09:55:59 +0300 (lkm 16 Mey 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
amelioration de route pour article et category module news

## Files changed

## Full metadata
------------------------------------------------------------------------
r12296 | srazana<PERSON>lisoa | 2024-05-16 09:55:59 +0300 (lkm 16 Mey 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/App/Router_save.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/App/routes/news.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/App/routes_build.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/App/routes_define.json

amelioration de route pour article et category module news
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/App/Router_save.js
===================================================================
--- src/js/JEditor/App/Router_save.js	(révision 12295)
+++ src/js/JEditor/App/Router_save.js	(révision 12296)
@@ -14,7 +14,7 @@
             "social": "social",
             "storelocator": "storelocator",
              "*notFound": "notFound",
-            "news(/:lang)(/:categorieID)": "news",
+            "news(/:lang)(/:type)(/:objectID)(/:zoneID)": "news",
         },
         constructor: function (attrs, options) {
             if (arguments.callee.caller !== Router.getInstance)
@@ -74,18 +74,34 @@
             }, this));
 
         },
-        news: function (lang, categorieID) {
+        news: function (lang, type, objectID, zoneID) {
+           
             require(["JEditor/NewsPanel/NewsPanel"], _.bind(function (NewsPanel) {
                 if (!(this.app.currentPanel instanceof NewsPanel))
                     this.app.currentPanel = NewsPanel.getInstance();
+                if (!type)
+                    type = '';
+            
                 if (!lang)
                     this.navigate('news/' + this.app.params.defaultcontentlang);
+               
                 var callback = function () {
                     this.currentLang = this.languages.get(lang ? lang : this.app.params.defaultcontentlang);
-                    if (categorieID) {
-                        this.currentCategorie = this.categories.get(categorieID);
-                    } else
-                        this.currentCategorie = null;
+                    switch (type) {
+                        case 'article':
+                            if (objectID) {
+                                this.currentArticle = this.articles.get(objectID);
+                            } else
+                                this.currentArticle = null;
+                            break;
+                        case 'category':
+                        default:
+                            if (objectID) {
+                                this.currentCategorie = this.categories.get(objectID);
+                            } else
+                                this.currentCategorie = null;
+                            break;
+                    } 
                 };
                 var currentPanel = this.app.currentPanel;
                 if (this.app.currentPanel.loaded) {
Index: src/js/JEditor/App/routes/news.js
===================================================================
--- src/js/JEditor/App/routes/news.js	(révision 12295)
+++ src/js/JEditor/App/routes/news.js	(révision 12296)
@@ -1,16 +1,30 @@
 define(["JEditor/Commons/Events"], function (Events) {
-    return function (lang, categorieID ) {
+    return function (lang, type, objectID, zoneID) {
         require(["JEditor/NewsPanel/NewsPanel"], _.bind(function (NewsPanel) {
             if (!(this.app.currentPanel instanceof NewsPanel))
                 this.app.currentPanel = NewsPanel.getInstance();
+            if (!type)
+                type = '';
             if (!lang)
                 this.navigate('news/' + this.app.params.defaultcontentlang);
+           
             var callback = function () {
                 this.currentLang = this.languages.get(lang ? lang : this.app.params.defaultcontentlang);
-                if (categorieID) {
-                    this.currentCategorie = this.categories.get(categorieID);
-                } else
-                    this.currentCategorie = null;
+                switch (type) {
+                    case 'article':
+                        if (objectID) {
+                            this.currentArticle = this.articles.get(objectID);
+                        } else
+                            this.currentArticle = null;
+                        break;
+                    case 'category':
+                    default:
+                        if (objectID) {
+                            this.currentCategorie = this.categories.get(objectID);
+                        } else
+                            this.currentCategorie = null;
+                        break;
+                } 
             };
             var currentPanel = this.app.currentPanel;
             if (this.app.currentPanel.loaded) {
Index: src/js/JEditor/App/routes_build.js
===================================================================
--- src/js/JEditor/App/routes_build.js	(révision 12295)
+++ src/js/JEditor/App/routes_build.js	(révision 12296)
@@ -18,5 +18,5 @@
  "icom":"icom",
  "restaurant":"restaurant",
  "feedget":"feedget",
- "news":"news(/:lang)(/:categorieID)"
+ "news":"news(/:lang)(/:type)(/:objectID)(/:zoneID)"
 });
Index: src/js/JEditor/App/routes_define.json
===================================================================
--- src/js/JEditor/App/routes_define.json	(révision 12295)
+++ src/js/JEditor/App/routes_define.json	(révision 12296)
@@ -18,5 +18,5 @@
   "icom":"icom",
   "restaurant":"restaurant",
   "feedget":"feedget",
-  "news": "news(/:lang)(/:categorieID)"
+  "news": "news(/:lang)(/:type)(/:objectID)(/:zoneID)"
 }
