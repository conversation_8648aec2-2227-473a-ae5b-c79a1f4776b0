Revision: r13468
Date: 2024-11-19 14:03:00 +0300 (tlt 19 Nov 2024) 
Author: frahajanirina 

## Commit message
Wishlist:IDEO3.2:News:Corrections textes news IDEO

## Files changed

## Full metadata
------------------------------------------------------------------------
r13468 | frahajanirina | 2024-11-19 14:03:00 +0300 (tlt 19 Nov 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/App/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/App/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/App/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/DashBoard/Templates/DashBoardTpl.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/main.less

Wishlist:IDEO3.2:News:Corrections textes news IDEO
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/App/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/App/nls/fr-ca/i18n.js	(révision 13467)
+++ src/js/JEditor/App/nls/fr-ca/i18n.js	(révision 13468)
@@ -16,5 +16,5 @@
     "disconnect": "vous avez été déconnecté",
     "Feedget":"Feedget",
     "Marketingauto": "Marketing Automation",
-    "News":"Actualités"
+    "News":"Articles"
 });
Index: src/js/JEditor/App/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/App/nls/fr-fr/i18n.js	(révision 13467)
+++ src/js/JEditor/App/nls/fr-fr/i18n.js	(révision 13468)
@@ -15,7 +15,7 @@
     "Quote" :"Quote",
     "disconnect": "vous avez été déconnecté",
     "Feedget":"Feedget",
-    "News":"Actualités",
+    "News":"Articles",
     "Marketingauto":"Marketing Automation"
 
 });
Index: src/js/JEditor/App/nls/i18n.js
===================================================================
--- src/js/JEditor/App/nls/i18n.js	(révision 13467)
+++ src/js/JEditor/App/nls/i18n.js	(révision 13468)
@@ -23,6 +23,6 @@
         "logs":"logs",
         "disconnect":"You have been disconnected",
         "Feedget":"Feedget",
-        "News":"News",
+        "News":"Articles",
         "Marketingauto":"Marketing Automation"
     }, "fr-fr":true, "fr-ca":true });
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 13467)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 13468)
@@ -363,7 +363,7 @@
                                             }
                                             else {
                                                 this.childViews.newsEditorView = new NewsEditorView({
-                                                    title : 'tous les articles',
+                                                    title : 'Tous les articles',
                                                     languages : this.languages,
                                                     currentLang : this.currentLang,
                                                     categorieCollection :  this.categories
Index: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 13467)
+++ src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 13468)
@@ -1,5 +1,5 @@
 define({
-	"newsTitle" : "Actualités",
+	"newsTitle" : "Articles",
 	"newsDesc": "Parcourez vos pages depuis le menu de gauche. Ajoutez du contenu et prévisualisez votre site à tout moment",
 	"newsAddACategory" : "Ajouter une catégorie",
 	"publishedArticles": "Article\(s\) publié\(s\)",
@@ -9,7 +9,7 @@
 	"newsAddArticle" : "Ajouter un article",
 	"emptyNewsLang": "Vous n'avez encore aucune article",
 	"howToAdd": "Commencez par créer une Catégorie ou un article",
-	"createCategorie" : "Creer une Catégorie",
+	"createCategorie" : "Créer une Catégorie",
 	"titleCategorie":"Titre de la catégorie",
 	"descriCategorie":"Description de la catégorie",
 	"cancel":"Annuler",
@@ -53,7 +53,7 @@
 	"resumecheckLegend"    :   "Afficher le résumé",
 
 	//config category
-	"configDesc" 			: "Ajustez les paramètres des Catégorie",
+	"configDesc" 			: "Ajustez les paramètres des catégories",
 	"metaTitleLabel" 		: "Méta Title",
 	"metaDescLabel" 		: "Méta Description",
 	"urlLabel" 				: "URL de la catégorie",
@@ -84,7 +84,7 @@
 	"save": "Sauvegarder",
 	"saveAction": "Sauvegarde",
 	"saveSuccesful": "Le contenu a été sauvegardé avec succès",
-	"saveError": "Une erreur s'est produite lors de la sauvegarde de la l'article",
+	"saveError": "Une erreur s'est produite lors de la sauvegarde de l'article",
     "RessourceRequired" : "Ajoutez d'abord une image",
 	"emptyZone": "La zone sélectionnée est vide",
 	"howToAddContent": "Cliquez sur &laquo; Ajouter du contenu &raquo;<br/> puis glissez un bloc dans cette zone pour créer une section",
@@ -98,7 +98,7 @@
 	 "titleArticle" : "Titre de l'article",
 	 "descriArticle": "Introduction de l'article",
 	 "DefaultMessageUploaderLogo": "Cliquez ici ou glissez-déposez une image",
-	 "saveAddArticle" : "Creer une article",
+	 "saveAddArticle" : "Créer un article",
 	 "categoryLangNotFound" : "Categorie qui n'existe pas encore au langue selectionner",
 	 "addArticle" : "Ajout Article",
 	 "errorCategoryTraduction" : "Pour traduire cet article, veuillez vous assurer que la catégorie existe bien dans le langue <strong><% lang %></strong>.",
Index: src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 13467)
+++ src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 13468)
@@ -1,8 +1,8 @@
 define({
-	"newsTitle" : "Actualités",
+	"newsTitle" : "Articles",
 	"newsDesc": "Parcourez vos pages depuis le menu de gauche. Ajoutez du contenu et prévisualisez votre site à tout moment",
 	"newsAddACategory" : "Ajouter une catégorie",
-	"publishedArticles": "Article\(s\) publié\(\)",
+	"publishedArticles": "Article\(s\) publié\(s\)",
 	"editedVersion" : "Editez une version",
 	"allArticles": "Tous les articles",
 	"config" : "Réglages",
@@ -9,7 +9,7 @@
 	"newsAddArticle" : "Ajouter un article",
 	"emptyNewsLang": "Vous n'avez encore aucune article",
 	"howToAdd": "Commencez par créer une Catégorie ou un article",
-	"createCategorie" : "Creer une Catégorie",
+	"createCategorie" : "Créer une Catégorie",
 	"titleCategorie":"Titre de la catégorie",
 	"descriCategorie":"Description de la catégorie",
 	"cancel":"Annuler",
@@ -53,7 +53,7 @@
 	"resumecheckLegend"    :   "Afficher le résumé",
 	
 	//config category
-	"configDesc" 			: "Ajustez les paramètres des Catégorie",
+	"configDesc" 			: "Ajustez les paramètres des catégories",
 	"metaTitleLabel" 		: "Méta Title",
 	"metaDescLabel" 		: "Méta Description",
 	"urlLabel" 				: "URL de la catégorie",
@@ -84,7 +84,7 @@
 	"save": "Sauvegarder",
 	"saveAction": "Sauvegarde",
 	"saveSuccesful": "Le contenu a été sauvegardé avec succès",
-	"saveError": "Une erreur s'est produite lors de la sauvegarde de la l'article",
+	"saveError": "Une erreur s'est produite lors de la sauvegarde de l'article",
     "RessourceRequired" : "Ajoutez d'abord une image",
 	"emptyZone": "La zone sélectionnée est vide",
 	"howToAddContent": "Cliquez sur &laquo; Ajouter du contenu &raquo;<br/> puis glissez un bloc dans cette zone pour créer une section",
@@ -98,7 +98,7 @@
 	 "titleArticle" : "Titre de l'article",
 	 "descriArticle": "Introduction de l'article",
 	 "DefaultMessageUploaderLogo": "Cliquez ici ou glissez-déposez une image",
-	 "saveAddArticle" : "Creer une article",
+	 "saveAddArticle" : "Créer un article",
 	 "categoryLangNotFound" : "Categorie qui n'existe pas encore au langue selectionner",
 	 "addArticle" : "Ajout Article",
 	 "errorCategoryTraduction" : "Pour traduire cet article, veuillez vous assurer que la catégorie existe bien dans le langue <strong><% lang %></strong>.",
Index: src/js/JEditor/NewsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/i18n.js	(révision 13467)
+++ src/js/JEditor/NewsPanel/nls/i18n.js	(révision 13468)
@@ -1,6 +1,6 @@
 define({
     "root": {
-	    "newsTitle" : "Actualités",
+	    "newsTitle" : "Articles",
 	    "newsDesc": "Parcourez vos pages depuis le menu de gauche. Ajoutez du contenu et prévisualisez votre site à tout moment",
         "newsAddACategory" : "Ajouter une catégorie",
         "publishedArticles": "Article\(s\) publié\(s\)",
@@ -10,7 +10,7 @@
         "newsAddArticle" : "Ajouter un article",
         "emptyNewsLang": "Vous n'avez encore aucune article",
         "howToAdd": "Commencez par créer une Catégorie ou un article",
-        "createCategorie" : "Creer une Catégorie",
+        "createCategorie" : "Créer une Catégorie",
         "titleCategorie":"Titre de la catégorie",
         "descriCategorie":"Description de la catégorie",
         "cancel":"Annuler",
@@ -83,7 +83,7 @@
         "save": "Sauvegarder",
         "saveAction": "Sauvegarde",
         "saveSuccesful": "Le contenu a été sauvegardé avec succès",
-        "saveError": "Une erreur s'est produite lors de la sauvegarde de la l'article",
+        "saveError": "Une erreur s'est produite lors de la sauvegarde de l'article",
         "RessourceRequired" : "Ajoutez d'abord une image",
         "emptyZone": "La zone sélectionnée est vide",
         "howToAddContent": "Cliquez sur &laquo; Ajouter du contenu &raquo;<br/> puis glissez un bloc dans cette zone pour créer une section",
@@ -97,7 +97,7 @@
         "titleArticle" : "Titre de l'article",
         "descriArticle": "Introduction de l'article",
         "DefaultMessageUploaderLogo": "Cliquez ici ou glissez-déposez une image",
-        "saveAddArticle" : "Creer une article",
+        "saveAddArticle" : "Créer un article",
         "dateProg" : "",
         "categoryLangNotFound" : "Categorie qui n'existe pas encore au langue selectionner",
         "addArticle" : "Ajout Article",
Index: src/js/JEditor/DashBoard/Templates/DashBoardTpl.html
===================================================================
--- src/js/JEditor/DashBoard/Templates/DashBoardTpl.html	(révision 13467)
+++ src/js/JEditor/DashBoard/Templates/DashBoardTpl.html	(révision 13468)
@@ -33,7 +33,7 @@
             -->
             <li>
                 <a href="#news" class="news panel-link" data-target="news">
-                    <span class="icon icon-News"></span>
+                    <span class="icon icon-News dashborard-icon-news"></span>
                     <span class="title"><%=__('news')%></span>
                     <span class="text">
                         <%=__('news_explained')%>
Index: src/less/main.less
===================================================================
--- src/less/main.less	(révision 13467)
+++ src/less/main.less	(révision 13468)
@@ -2931,4 +2931,7 @@
 .card-text-area {
   min-height: 130px;
 }
+.dashborard-icon-news {
+  color: #03a9f4;
+}
 
