Revision: r12584
Date: 2024-07-10 16:40:08 +0300 (lrb 10 Jol 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: correction bug ckeditor

## Files changed

## Full metadata
------------------------------------------------------------------------
r12584 | srazana<PERSON>lisoa | 2024-07-10 16:40:08 +0300 (lrb 10 Jol 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/ZoneToolBox.js

News: correction bug ckeditor
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Views/ZoneToolBox.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ZoneToolBox.js	(révision 12583)
+++ src/js/JEditor/NewsPanel/Views/ZoneToolBox.js	(révision 12584)
@@ -82,7 +82,6 @@
                 this.dom[this.cid].templateIndicator.removeClass('icon-template-ok').addClass('icon-template-broken').parent().addClass('active');
                 this.model.customized = true;
             }
-            this.childViews.saveButton.model.content.content = zone;
         },
         doUncustomize: function () {
             this.model.customized = false;
