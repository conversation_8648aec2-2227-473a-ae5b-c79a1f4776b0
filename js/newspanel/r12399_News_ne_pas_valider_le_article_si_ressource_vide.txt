Revision: r12399
Date: 2024-06-12 08:04:19 +0300 (lrb 12 Jon 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: ne pas valider le'article si ressource vide

## Files changed

## Full metadata
------------------------------------------------------------------------
r12399 | srazana<PERSON><PERSON>oa | 2024-06-12 08:04:19 +0300 (lrb 12 Jon 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/AddArticleView.js

News: ne pas valider le'article si ressource vide
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Views/AddArticleView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/AddArticleView.js	(révision 12398)
+++ src/js/JEditor/NewsPanel/Views/AddArticleView.js	(révision 12399)
@@ -190,6 +190,7 @@
                 valid = false;
             }
             if (!this.model.ressource) {
+                valid = false;
                 this.error({
                     title: translate("saveAction"),
                     message: translate("RessourceRequired")
