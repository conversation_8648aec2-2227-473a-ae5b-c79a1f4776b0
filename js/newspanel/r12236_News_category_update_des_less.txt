Revision: r12236
Date: 2024-04-24 17:05:45 +0300 (lrb 24 Apr 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
 News: category, update des less

## Files changed

## Full metadata
------------------------------------------------------------------------
r12236 | srazana<PERSON>lisoa | 2024-04-24 17:05:45 +0300 (lrb 24 Apr 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/less/imports/edit.less
   M /branches/ideo3_v2/integration_news/src/less/imports/news_panel/import/vars.less
   M /branches/ideo3_v2/integration_news/src/less/imports/news_panel/main.less
   M /branches/ideo3_v2/integration_news/src/less/imports/variables.less
   M /branches/ideo3_v2/integration_news/src/less/main.less

 News: category, update des less
------------------------------------------------------------------------

## Diff
Index: src/less/imports/edit.less
===================================================================
--- src/less/imports/edit.less	(révision 12235)
+++ src/less/imports/edit.less	(révision 12236)
@@ -33,6 +33,7 @@
     &.affix-top{top:0; bottom:0;}
 
 }
+#news-editor #content-editor[data-child-count="0"],
 #page-edit #content-editor[data-child-count="0"]{
     &>.sensor{
         height: 100%;
@@ -132,6 +133,7 @@
   background:transparent !important;
   font-size: 14px !important;
 }
+#news-editor #content-editor .editable-section .edition-menu span.menu-item.menu-group,
 #page-edit #content-editor .editable-section .edition-menu span.menu-item.menu-group {
     padding: 0;
     height: 39px;
@@ -141,6 +143,7 @@
     margin: 2px;
 }
 
+#news-editor #content-editor .editable-section .edition-menu span.menu-item.menu-group span.menu-item,
 #page-edit #content-editor .editable-section .edition-menu span.menu-item.menu-group span.menu-item {
     height: 20px;
     line-height: 20px;
@@ -147,9 +150,11 @@
     padding: 10px 5px;
 }
 
+#news-editor #content-editor .editable-section .edition-menu span.menu-item.menu-group span.menu-item>.icon,
 #page-edit #content-editor .editable-section .edition-menu span.menu-item.menu-group span.menu-item>.icon {
     line-height: 20px;
 }
+#news-editor #content-editor,
 #page-edit #content-editor{
     margin: auto;
     max-width:1200px;
Index: src/less/imports/news_panel/import/vars.less
===================================================================
--- src/less/imports/news_panel/import/vars.less	(révision 12235)
+++ src/less/imports/news_panel/import/vars.less	(révision 12236)
@@ -13,6 +13,10 @@
 @google-color	: #DC5442;
 @pinterest-color: #CB2128;
 
-
+@bluePrimairy   : #059fe2;
 @raleway 		: 'raleway';
-@opensans 		: 'open sans';
\ No newline at end of file
+@opensans 		: 'open sans';
+@publieColor 	: #41d963;
+@brouillonColor : #ff5505;
+@programColor   : #45bff7;
+@toogleactive   : #51cb8e;
\ No newline at end of file
Index: src/less/imports/news_panel/main.less
===================================================================
--- src/less/imports/news_panel/main.less	(révision 12235)
+++ src/less/imports/news_panel/main.less	(révision 12236)
@@ -1,6 +1,7 @@
 // basic less tools
 @import 'import/vars';
 @import '../common/import/mixins';
+@import '../variables.less';
 
 
 // base project settings
@@ -14,11 +15,53 @@
 @import '../common/module/error-message';
 
 
-.header, .sub-header__action, .sub-header__infos  {
-	background-color: #19a6be;
+.header, .sub-header__infos {
+	background-color: @newsColorLight;
+	.news-title{
+		color: #ddd;
+	}
 }
-
-.addCategory {
+.sub-header__action {
+	background-color:  @newsColorDark;
+	color: #ddd;
+}
+.categorie-nav-list{
+	display: flex;
+	justify-content: space-between;
+	border-bottom: 1px solid @grey;
+	padding: 10px;
+	margin: 0px 10px;
+	a {
+		cursor: pointer;
+	}
+	&.edit{
+		background: #fff;
+		font-weight: 600;
+		color: #020705;
+	}
+	.info .number {
+		background: @greyM;
+		padding: 3px 9px;
+		color:@greyXL ;
+		border-radius: 100%;
+		font-size: small;
+	}
+	.info .icon{
+		background: @newsColorLight;
+		color:@greyXL ;
+		padding: 5px 5px;
+		border-radius: 100%;
+		font-size: small;
+	}
+}
+.sideBar .addCategory span {
+	display: inline-block;
+	vertical-align: middle;
+	font-size: 30px;
+	margin-left: 1px;
+	margin-right: 6px;
+} 
+.addArticle , .addCategory {
 	background: 0 0;
 	border: none;
 	display: block;
@@ -27,7 +70,7 @@
 	font-family: Raleway,sans-serif;
 	font-size: 14px;
 	font-weight: 600;
-	color: #19a6be;
+	color: @newsColorLight;
 	padding-left: 36px;
 	line-height: 40px;
 	margin-top: 20px;
@@ -37,28 +80,8 @@
 	-ms-transition: color 300ms ease;
 	transition: color 300ms ease;
   }
-  
-.addArticle {
-	background: 0 0;
-	border: none;
-	display: block;
-	cursor: pointer;
-	text-decoration: none;
-	font-family: Raleway,sans-serif;
-	font-size: 14px;
-	font-weight: 600;
-	color: #19a6be;
-	padding-left: 36px;
-	line-height: 40px;
-	margin-top: 20px;
-	-webkit-transition: color 300ms ease;
-	-moz-transition: color 300ms ease;
-	-o-transition: color 300ms ease;
-	-ms-transition: color 300ms ease;
-	transition: color 300ms ease;
-  }
 
-  .news-container-header {
+.news-container-header {
     background: #fff;
     z-index: 2;
     margin-bottom: 30px;
@@ -77,4 +100,646 @@
         color: @grey;
         font-size: 22px;
     }
-  }
\ No newline at end of file
+	
+}
+
+.bordered {
+	border: 1px solid #bcbebd;
+	border-radius: 5px;
+	padding: 20px;
+	margin: auto;
+}
+
+.dis-table {
+	.flex_categorie{
+		display: flex;
+		width: 100%;
+		justify-content: space-between;
+		.upload-categorie{
+			height: 100%;
+			width: 300px;
+			.uploader{
+				background-color: #666867;
+				.message-wrapper{
+					position: absolute;
+					opacity: revert-layer;
+				}
+				 .preview {
+					float: left;
+					position: relative;
+					background-color:  #666867;
+					height: 100%;
+					width: 100%;
+					overflow: hidden;
+			}
+			}
+		}
+		.categorie-info{
+			height: 100%;
+			width: inherit;
+			padding-left: 25px;
+			h2{
+				margin-top: auto;
+			}
+			input, textarea{
+				width: 98%;
+				margin: 2px;
+				line-height: 35px;
+				background-color: #f4f4f4;
+				border: 1px solid #e1e1e1;
+				-webkit-border-radius: 3px;
+				-moz-border-radius: 3px;
+				border-radius: 3px;
+				-moz-background-clip: padding;
+				-webkit-background-clip: padding-box;
+				background-clip: padding-box;
+				font-family: 'raleway';
+				color: #999;
+				font-weight: normal;
+			}
+			
+			.btn.dropdown-toggle{
+				background-color: @grey;
+				text-align: left;
+				min-width: 189px;
+				line-height: 30px;
+				color: #fff;
+				.caret {
+					margin: 0;
+					display: block;
+					position: absolute;
+					top: 13px;
+					right: 8px;
+					border-top-color: #fff;
+				}
+			}
+			.btn-group.lang .dropdown-menu {
+				min-width: 205px;
+				background-color: #999;
+				-webkit-box-shadow: 0 5px 10px rgba(0,0,0,.25);
+				-moz-box-shadow: 0 5px 10px rgba(0,0,0,.25);
+				box-shadow: 0 5px 10px rgba(0,0,0,.25);
+				-webkit-background-clip: padding-box;
+				-moz-background-clip: padding-box;
+				background-clip: padding-box;
+				& li a {
+					display: block;
+					line-height: 30px;
+					margin: 0;
+					padding: 0 0 0 10px;
+					color:#f5f5f5;
+				}
+			
+				& li a:hover, & li a.active {
+					background-color: #ccc;
+					color: @newsColorLight;
+				}
+			}
+		}
+	}
+	.flex-button{
+		display: flex;
+		width: 100%;
+		justify-content:flex-end;
+		button{
+			background-color: @grey;
+			color: #f4f4f4;
+			border: 1px solid #bcbebd;
+			border-radius: 5px;
+			margin: 20px;
+			&.bg-blue{
+				background-color: @newsColorLight;
+			}
+		}
+	}
+}
+.flex-betwen{
+	display: flex;
+	width: 100%;
+	justify-content: space-between;
+}
+.bg-blue{
+	background-color: @newsColorLight;
+}
+#news-editor {
+	.clearfix();
+	position: relative;
+	overflow: hidden;
+	.lang .btn.active,
+	.lang .btn.disabled,
+	.lang .btn:active,
+	.lang .btn:focus,
+	.lang .btn:hover,
+	.lang .btn[disabled] {
+	background-color:#999
+	}
+	.header {
+	  & .label {
+		margin-left: 5px;
+	  }
+	  position: absolute;
+	  z-index: 3;
+	  width: 100%;
+	  padding: 0 0 0 261px;
+	  height: 142px;
+	  background: #fff;
+	  top: 0;
+	  -webkit-box-sizing: border-box;
+	  -moz-box-sizing: border-box;
+	  box-sizing: border-box;
+	  .transition(box-shadow 500ms ease);
+	  .transition(transform 0.3s linear);
+	  &.affix {
+		position: fixed;
+		top: 70px;
+		.box-shadow(0 0 10px fade(#000, 17%));
+  
+		.config .dropdown-menu li:hover ul {
+		  right: auto;
+		  left: 100%;
+		}
+  
+	  }
+	  &.affix-top {
+		top: 0;
+	  }
+	}
+	div.title {
+		margin: 0;
+		height: 72px;
+		line-height: 51px;
+		padding-top: 19px;
+		font-family: 'Raleway', sans-serif;
+		font-size: 26px;
+		font-weight: 200;
+		margin-right: 205px;
+		padding-left: 20px;
+  
+		i {
+		  cursor: pointer;
+		  vertical-align: top;
+		  margin-top: 18px;
+		  margin-right: 3px;
+		  font-size: 20px;
+		  color: #ddd;
+		  display: inline-block;
+		  &:hover {
+			color: #a3bec2;
+		  }
+  
+		}
+		.inherit-from {
+		  display: inline-block;
+		  overflow: hidden;
+		  font-size: 20px;
+		  vertical-align: text-bottom;
+		  color: #999;
+		  a {
+			color: @pageColorDarken;
+		  }
+		}
+		.text {
+		  cursor: pointer;
+		  max-width: 25vw;
+		  display: block;
+		  white-space: nowrap;
+		  overflow: hidden;
+		  /*text-overflow: ellipsis;*/
+		  border: 1px solid transparent;
+		  padding: 0 8px;
+		  max-height: 51px;
+		  color: @darkgrey;
+  
+		  &>.content {
+			display: inline-block;
+			max-width: ~"calc(100% - 32px)";
+			overflow: hidden;
+			text-overflow: ellipsis;
+		  }
+  
+		  &>.content:focus {
+			outline: none;
+			text-overflow: clip;
+		  }
+		  i {
+			visibility: hidden;
+			margin-right: 0;
+			margin-left: 12px;
+			color: inherit;
+			display: inline-block;
+		  }
+		  &[contenteditable] {
+			color: @darkgrey;
+			border: 1px solid @pageColor;
+  
+			i {
+			  visibility: hidden;
+			}
+			&:hover,
+			&.active {
+			  color: @darkgrey;
+			  border: 1px solid @pageColor;
+			  i {
+				visibility: hidden;
+			  }
+			}
+  
+		  }
+		  &:hover,
+		  &.active {
+			color: @grey;
+			border: 1px solid @lightgrey;
+			i {
+			  visibility: visible;
+			}
+		  }
+  
+		}
+		&.unlocked {
+		  i.icon-lock {
+			display: none;
+		  }
+		}
+		&.locked {
+		  i.icon-unlock {
+			display: none;
+		  }
+		}
+  
+	}
+	.config-preview {
+		position: absolute;
+		top: 21px;
+		right: 36px;
+		& .icon {
+		  display: inline-block;
+		}
+		& .btn {
+		  background: #fff;
+		}
+  
+	}
+	.button.preview {
+		color: #999;
+		min-width: 102px;
+		background-color: #fff;
+		i {
+		  margin-right: 8px;
+		}
+		&:hover {
+		 // .hsv-background(#ffffff;-5);
+		  color: #999;
+		}
+  
+	}
+	.config-preview {
+		.icon-params,
+		.icon-find {
+		  font-size: 20px;
+		  vertical-align: middle;
+		  position: relative;
+		  top: -2px;
+		}
+  
+	}
+	.config {
+		color: #999;
+  
+		.btn {
+		  min-width: 34px;
+		  background-color: #fff;
+		  color: #999;
+		}
+		.btn:hover,
+		.btn:focus,
+		.btn:active,
+		.btn.active,
+		.btn.disabled,
+		// .btn[disabled] {
+		//   .hsv-background(#ffffff,-5);
+		// }
+		.btn .caret {
+		  margin-top: 12px;
+		  border-top-color: @grey;
+  
+		}
+		.dropdown-menu,
+		.dropdown-menu ul {
+		  /*z-index: 0;*/
+		  margin-top: 4px;
+		  padding-top: 6px;
+		  min-width: 205px;
+		  background: #e5e5e5;
+		  .box-shadow(0 5px 10px rgba(0, 0, 0, 0.25));
+		 // .background-clip();
+		  list-style-type: none;
+		  padding-left: 0;
+		}
+		.dropdown-menu .caret-right {
+		  display: inline-block;
+		  width: 0;
+		  height: 0;
+		  vertical-align: top;
+		  border-left: 4px solid #999;
+		  border-right: 4px solid transparent;
+		  border-top: 4px solid transparent;
+		  border-bottom: 4px solid transparent;
+		  content: "";
+		  right: 0;
+		  margin-top: 9px;
+		  position: absolute;
+		}
+		.dropdown-menu li,
+		.dropdown-menu li ul li {
+		  border: none;
+		  position: relative;
+		}
+		.dropdown-menu li a,
+		.dropdown-menu li ul li a {
+		  color: #999;
+		  line-height: 26px;
+		}
+		.dropdown-menu li:hover,
+		.dropdown-menu li:hover>a,
+		.dropdown-menu li a:hover,
+		.dropdown-menu li a:focus,
+		.dropdown-menu .active a,
+		.dropdown-menu .active a:hover,
+		.dropdown-menu .active a:focus {
+		  background: #ccc;
+		  color: #777;
+		}
+		.dropdown-menu li ul {
+		  display: none;
+  
+		}
+		.dropdown-menu li:hover ul {
+		  display: block;
+		  position: absolute;
+		  //.hsv-background(@pageColor,-20);
+		  right: 100%;
+		  top: 0;
+		}
+  
+	}
+	.page {
+		.clearfix();
+	}
+	&.affix {
+		height: 60px;
+  
+		.zone-toolbox>.btn-group>.btn,
+		& .zone-toolbox>.btn-group>.btn-group {
+		  border: none;
+		}
+		.btn-group > .btn:last-child {
+		  .border-radius(4px);
+		}
+		.zone .dropdown-menu {
+		  margin-top: 13px;
+  
+		}
+		& .page {
+		  float: left;
+		  position: static;
+		}
+		& .zone .label,
+		& .page .label {
+		  display: none;
+		}
+		& .zone .btn.save .label,
+		& .zone .btn.add-content .label {
+		  display: inline-block;
+		}
+		& .zone {
+		  .btn {
+			min-width: 0;
+		  }
+		  .icon-zone {
+			display: inline-block;
+		  }
+  
+		}
+		& .editing {
+		  display: none;
+		}
+		& .config-preview {
+		  float: left;
+		  position: static;
+		  top: 0;
+		  margin-left: 20px;
+		}
+		& div.title .inherit-from {
+		  font-size: 14px;
+		  line-height: 50px;
+		  position: relative;
+		  top: -5px;
+		}
+		.zone .btn .caret {
+		  margin-top: 13px;
+		  margin-right: 0;
+		}
+		.add-save,
+		.config-preview,
+		div.zone {
+		  height: 60px;
+		  line-height: 60px;
+		  & .add {
+			width: 140px;
+			height: 30px;
+			line-height: 30px;
+			.text {
+			  display: inline-block;
+			}
+  
+		  }
+		  &.add-save {
+			margin-right: 36px;
+		  }
+  
+		}
+		& .button:not(.add),
+		& .btn {
+		  color: #999;
+		  height: 30px;
+		  /*padding:0 5px;*/
+		  text-align: center;
+		  line-height: 30px;
+		  background: transparent;
+		  min-width: 0;
+		}
+		& div.title {
+		  font-size: 20px;
+		  padding-top: 0;
+		  float: left;
+		  margin-right: 0;
+		  height: 60px;
+		  & .text.page-name {
+			margin-top: 5px;
+		  }
+		}
+	}
+	.main {
+		position: relative;
+		margin: 142px auto;
+		padding: 0 36px 0 297px;
+		font-family: Raleway,sans-serif;
+		.transition(transform 0.3s linear);
+		#content-editor .nothing-to-display{
+			position: relative;
+			margin: 142px 0;
+			//padding:0 36px 0 297px;
+			.transition(transform 0.3s linear);
+			font-family:'Raleway', sans-serif;
+			display:none;
+			min-height:695px;
+			margin:auto;
+			.wrapper{
+				display: table-cell;
+				height: 595px;
+				vertical-align: middle;
+			}
+			&.none-selected,&.empty{
+				width:fit-content;
+				margin:auto;
+				display: block;
+				.empty-content-lang:hover{
+					cursor:pointer;
+					.icon{
+						background:@pageColor;
+					}
+					.add-page{
+						color:@pageColor;
+					}
+				}
+				.icon{
+					margin:10px auto;
+					width:30px;
+					height:30px;
+					padding:10px;
+					background:#bababa;
+					.border-radius(25px);
+					font-size:30px;
+					text-align:center;
+					color:#ffffff;
+					line-height:30px;
+					.transition(background 0.3s);
+				}
+				.text-intro{
+					color:#333333;
+					display:block;
+					font-size:13px;
+					text-align:center;
+				}
+				.how-to{
+					display:block;
+					font-size:11px;
+					color:#999999;
+					text-align:center;
+					margin :10px;
+				}
+				.button-add{
+					display: flex;
+					align-items: center;
+					justify-content: space-between;
+					a{
+						text-align: center;
+						margin: 10px;
+						font-size: 14px;
+						font-weight: 600;
+						.transition(color 0.3s);
+						span span{
+							display: inline-block;
+							vertical-align: middle;
+							font-size: 30px;
+							margin-left: 1px;
+							margin-right: 6px;
+						}
+						.addArticle{
+							background-color: @newsColorLight;
+							color: @greyXL;
+							border-radius: 3px;
+							padding: 0px 40px !important;
+						}
+					}
+					
+				  }
+			}
+			.addCategory span, .addArticle span {
+				display: inline-block;
+				vertical-align: middle;
+				font-size: 30px;
+				margin-left: 1px;
+				margin-right: 6px;
+			}
+		}
+		.articlesList {
+			margin-top: 30px;
+		}
+	}
+}
+#news-table {
+    margin: auto;
+    #search-form {
+    margin-bottom: 10px;
+	}
+	.search-news{
+		background-color: #ddd;
+		border-radius: 5px;
+		border: 1px #aaa;
+		width: 90%;
+		line-height: 24px;
+	}
+	#data-table {
+		border-collapse: collapse;
+		width: 100%;
+	 th, td {
+		border: 1px solid #ddd;
+		padding: 8px;
+		text-align: left;
+		}
+		th {
+		cursor: pointer;
+		}
+		th:hover {
+			background-color: #f2f2f2;
+		}
+	}
+	.img-news img{
+		width: 50px;
+		height: 50px;
+		margin-right: 10px;
+		object-fit: cover;
+		border-radius: 5px;
+	}
+	/* bouton etat*/
+	.etat .btn{
+		padding: 6px;
+		color: #ddd;
+		font-size: small;
+		border-radius: 40%;
+	}
+	.green{
+		background-color: @publieColor
+	}
+	.orange{
+		background-color: @brouillonColor;
+	}
+	.bleu{
+		background-color: @programColor;
+	}
+	/*pour l'icon sort*/
+	.sort-icon {
+		display: inline-block;
+		margin-left: 5px; 
+		font-size: 12px; 
+		color: #999;
+	}
+
+	.sort-icon .asc::before {
+		content: "\25B2"; 
+	}
+	.sort-icon .desc::before {
+		content: "\25BC";
+	}
+}
\ No newline at end of file
Index: src/less/imports/variables.less
===================================================================
--- src/less/imports/variables.less	(révision 12235)
+++ src/less/imports/variables.less	(révision 12236)
@@ -28,8 +28,8 @@
 @evaluationColor:       #c2922c;
 @feedgetColorLight:     #2677d9;
 @feedgetColorDark:      #004599;
-@newsColorLight:        #1ed5f5;
-@newsColorDark:         #19a6be;
+@newsColorLight:        #03a9f4;
+@newsColorDark:         #0277ab;
 
 @contrastTextColor: 	rgba(0,0,0,0.85);
 
Index: src/less/main.less
===================================================================
--- src/less/main.less	(révision 12235)
+++ src/less/main.less	(révision 12236)
@@ -1874,7 +1874,10 @@
           background-color: @newsColorLight;
         }
         &.active {
-          background-color: @newsColorDark;
+          background-color: @newsColorLight;
+          .icon {
+            color: #ddd;
+          }
         }
       }
 
