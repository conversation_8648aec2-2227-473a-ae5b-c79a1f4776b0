Revision: r14028
Date: 2025-04-01 16:03:25 +0300 (tlt 01 Apr 2025) 
Author: frahajanirina 

## Commit message
Wishlist:News:fix upload image category

## Files changed

## Full metadata
------------------------------------------------------------------------
r14028 | frahajanirina | 2025-04-01 16:03:25 +0300 (tlt 01 Apr 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/CategorieAddView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/NewsEditorView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/UploadImageCategoryView.js

Wishlist:News:fix upload image category
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 14027)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 14028)
@@ -282,7 +282,6 @@
 
                                 //Upload view category
                                 uploadImgCategoryinTheSiteAndComputer : function(){
-                                    this.initUploadViewCategory();
                                     this.renderRightPanel(this.childViews.UploadImageCategoryView, 'noRender');
                                     this.rightPanelView.showContent(this.childViews.UploadImageCategoryView);
                                     this.$('.upload-categorie  .uploader .actions-wrapper').removeClass('visible');
Index: src/js/JEditor/NewsPanel/Views/CategorieAddView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(révision 14027)
+++ src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(révision 14028)
@@ -67,15 +67,7 @@
             this.listenTo(this.langDropDown, Events.ChoiceEvents.SELECT, this._onLanguageChange);
             this.fileCollection = new FileCollection();
             this.translations = translate.translations;
-            this.listenTo(Backbone, 'file:save', this._onFileSave);
-            this.listenTo(Backbone, 'file:init', this._onFileInit);
         },
-        _onFileSave: function(file) {
-            this.model.set('file', file);
-            this.model.set('ressource', file.id);
-            
-            this.render();
-        },
         _onFileInit: function() {
             this.model.file = this.model.lastState.file;
             this.model.ressource = this.model.lastState.ressource;
@@ -147,6 +139,8 @@
                     collection: this.fileCollection
                 });
             
+                this.listenTo(this.newsPanel.childViews.UploadImageCategoryView, 'file:save', this.render);
+                this.listenTo(this.newsPanel.childViews.UploadImageCategoryView, 'file:init', this._onFileInit);
                 this.listenTo(this.fileUploader, Events.FileUploaderEvents.UPLOAD, this._onUpload);
                 this.listenTo(this.fileUploader, Events.FileUploaderEvents.TOOBIG, this.onError);
 
Index: src/js/JEditor/NewsPanel/Views/NewsEditorView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 14027)
+++ src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 14028)
@@ -79,6 +79,7 @@
                 collection: this.options.categorieCollection
             });
             self = this;
+            this.newsPanel.initUploadViewCategory();
             this.listenTo(this.addCategorieView, Events.CategorieAddEvents.CATEGORY_ADD, _.bind(function(categorie) {
                 self.newsPanel.currentCategorie = categorie;
             }));
@@ -121,6 +122,7 @@
                 language : this.currentLang,
                 category : this.model
             });
+            this.newsPanel.initUploadViewCategory();
             this.categoryView.append(this.articlesListView.render().el);
          },
          renderAddArticle: function (cat){
Index: src/js/JEditor/NewsPanel/Views/UploadImageCategoryView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/UploadImageCategoryView.js	(révision 14027)
+++ src/js/JEditor/NewsPanel/Views/UploadImageCategoryView.js	(révision 14028)
@@ -40,7 +40,7 @@
 			});
             this.listenToOnce(this.fileUploader, Events.ListViewEvents.CHOOSE_FILE, this._useExistingFile);
             this.listenTo(this.fileUploader, Events.FileUploaderEvents.UPLOAD, this._onUpload);
-            this.listenTo(Backbone, 'file:save', this._checkEditFile);
+            this.listenTo(this.fileUploader, 'file:save', this._checkEditFile);
             
             return this;
         },
@@ -74,8 +74,8 @@
         _onUpload : function(file) {
             this.currentFile = file;
             this.model.ressource = file.id;
-            this.model.file = file;
-            Backbone.trigger('file:save', file);
+            this.model.file = null;
+            this.trigger('file:save', file);
                 
             this.render();  
 		},
@@ -82,9 +82,9 @@
         _useExistingFile : function(file) {
             this.currentFile = file;
             this.model.ressource = file.id;
-            this.model.file = file;
+            this.model.file = null;
             this.fileUploader.currentFile = file;
-            Backbone.trigger('file:save', file);
+            this.trigger('file:save', file);
 
             this.render();
 		},
@@ -96,7 +96,7 @@
         _onCancelClick: function() {
             this.model.cancel();
             this.newsPanel.hidesUploadCategoryView();
-            Backbone.trigger('file:init', this);
+            this.trigger('file:init', this);
         },
         updateDescriptionImg : function(event) {
             var lang = this.newsPanel.currentLang.id;
@@ -123,7 +123,7 @@
         _onSave : function(file) {
 			this.model.file = file;
             this.model.ressource = file.id;
-            Backbone.trigger('file:save', file);
+            this.trigger('file:save', file);
 			this.render();
 		},
     });
