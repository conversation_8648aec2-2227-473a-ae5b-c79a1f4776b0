Revision: r12581
Date: 2024-07-10 16:34:27 +0300 (lrb 10 Jol 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: style et traduction

## Files changed

## Full metadata
------------------------------------------------------------------------
r12581 | srazanandralisoa | 2024-07-10 16:34:27 +0300 (lrb 10 Jol 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/news_panel/main.less

News: style et traduction
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12580)
+++ src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12581)
@@ -16,9 +16,13 @@
 	"saveEdit" :"Enregistrer",
 	"saveAdd" : "Créer la catégorie",
 	"preview": "Aper\u00e7u",
-	"quitWithoutSaving": "Vous n'avez pas enregistr\u00e9 les modifications apport\u00e9es \u00e0 la catégorie, voulez-vous les enregistrer ?",
+	"quitWithoutSavingArt": "Votre article n'a pas \u00e9t\u00e9 enregistrer, voulez-vous les enregistrer ?",
+	"quitWithoutSavingCat": "Votre cat\u00e9gorie n'a pas \u00e9t\u00e9 enregistrer, voulez-vous les enregistrer ?",
 	"unsavedChanges": "sauvegarder les changements",
+	"confirmDeleteCategory":"Vous \u00eates sur le point de supprimer d\u00e9finitivement une catégorie",
+	"confirmDeleteArticle": "Vous \u00eates sur le point de supprimer d\u00e9finitivement un article",
 	// config global
+	"configGlobalDesc" : "Ajustez les paramétres des catégories/articles",
 	"styleDeNews" :   "Affichage de la liste des articles",
 	"styleDeNewsLegend" :   "Appliquez un style à la liste des articles",
 	"masonryLegend"         :   "Tuiles",
@@ -25,7 +29,7 @@
 	"gridLegend"            :   "Grille",
 	"listLegend"            :   "Liste",
 	"newsNombreImage"       :   "Nombre de colonne",
-	"newsNombreImageLegend" :   "Glissez pour ajuster le nombre de colonne affichées",
+	"newsNombreImageLegend" :   "Glissez pour ajuster le nombre de colonnes affichées",
 	"newsStyleAffichage"    :   "Style des articles",
 	"newsStyleAffichageDesc":   "Appliquez un style aux articles",
 	"FormatImage"           :   "Format de l'image",
@@ -113,6 +117,10 @@
 	"publishImmediate" : "Votre articke sera publié imméfiatement",
 	"publishArticle" : "Publier l'article",
 	"dateFormat":"Y-m-d",
+	"publishDone":"Article publié",
+	"programDone":"Article programmé",
+	"publishError": "Erreur de publier",
+	"errorPublishingInvalideElement": "Votre article n'est pas valid",
 
 	//available
 	"addContent":"Ajouter du contenu",
@@ -126,5 +134,20 @@
 	"advanced":"Blocs avancés",
 	"others":"Autres blocs",
 	"errorRenderDuring":"Une erreur nous empêche d'afficher ce bloc",
-	"oops":"Oups !"
+	"oops":"Oups !",
+
+	//preview
+	"resizeByDevice": "< Redimensionner par support",
+	"deviceMobile": "Mobile",
+	"deviceTablet": "Tablette",
+	"deviceDesktop": "Ordinateur",
+	"deviceLargeDesktop": "Ordinateur avec écran large ( > 1441px )",
+	"closePreview": "Fermer",
+	"saveVersion": "Restaurer",
+	"toggleFullscreen": "Taille réelle / Ajuster",
+	"cloningError": "Une erreur est survenue lors du clonage de la page, essayez d'actualiser la fenètre",
+	"batchCloningError": "Une erreur est survenue lors du clonage des page, essayez d'actualiser la fenêtre",
+	"layoutChanged": "Le layout a été changé avec succès.",
+	"success": "Effectué !",
+	"introPageTitle":"Mes pages",
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 12580)
+++ src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 12581)
@@ -16,9 +16,13 @@
 	"saveEdit" :"Enregistrer",
 	"saveAdd" : "Créer la catégorie",
 	"preview": "Aper\u00e7u",
-	"quitWithoutSaving": "Vous n'avez pas enregistr\u00e9 les modifications apport\u00e9es \u00e0 la catégorie, voulez-vous les enregistrer ?",
-    "unsavedChanges": "sauvegarder les changements",
+	"quitWithoutSavingArt": "Votre article n'a pas \u00e9t\u00e9 enregistrer, voulez-vous les enregistrer ?",
+	"quitWithoutSavingCat": "Votre cat\u00e9gorie n'a pas \u00e9t\u00e9 enregistrer, voulez-vous les enregistrer ?",
+    "unsavedChanges": "sauvegarder les modifications",
+	"confirmDeleteCategory":"Vous \u00eates sur le point de supprimer d\u00e9finitivement une catégorie",
+	"confirmDeleteArticle": "Vous \u00eates sur le point de supprimer d\u00e9finitivement un article",
 	// config global
+	"configGlobalDesc" : "Ajustez les paramétres des catégories/articles",
 	"styleDeNews" :   "Affichage de la liste des articles",
 	"styleDeNewsLegend" :   "Appliquez un style à la liste des articles",
 	"masonryLegend"         :   "Tuiles",
@@ -113,6 +117,10 @@
 	 "publishImmediate" : "Votre articke sera publié imméfiatement",
 	 "publishArticle" : "Publier l'article",
 	 "dateFormat":"Y-m-d",
+	 "publishDone":"Article publié",
+	 "programDone":"Article programmé",
+	 "publishError": "Erreur de publier",
+	 "errorPublishingInvalideElement": "Votre article n'est pas valid",
 	
 	 //available
 	 "addContent":"Ajouter du contenu",
@@ -126,5 +134,20 @@
 	 "advanced":"Blocs avancés",
 	 "others":"Autres blocs",
 	 "errorRenderDuring":"Une erreur nous empêche d'afficher ce bloc",
-	 "oops":"Oups !"
+	 "oops":"Oups !",
+
+	 //preview
+	 "resizeByDevice": "< Redimensionner par support",
+	 "deviceMobile": "Mobile",
+	 "deviceTablet": "Tablette",
+	 "deviceDesktop": "Ordinateur",
+	 "deviceLargeDesktop": "Ordinateur avec écran large ( > 1441px )",
+	 "closePreview": "Fermer",
+	 "saveVersion": "Restaurer",
+	 "toggleFullscreen": "Taille réelle / Ajuster",
+	 "cloningError": "Une erreur est survenue lors du clonage de la page, essayez d'actualiser la fenètre",
+	 "batchCloningError": "Une erreur est survenue lors du clonage des page, essayez d'actualiser la fenêtre",
+	 "layoutChanged": "Le layout a été changé avec succès.",
+	 "success": "Effectué !",
+	 "introPageTitle":"Mes pages",
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12580)
+++ src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12581)
@@ -17,16 +17,19 @@
         "saveEdit" :"Enregistrer",
         "saveAdd" : "Créer la catégorie",
         "preview": "Aper\u00e7u",
-        "quitWithoutSaving": "Vous n'avez pas enregistr\u00e9 les modifications apport\u00e9es \u00e0 la catégorie, voulez-vous les enregistrer ?",
+        "quitWithoutSavingArt": "Votre article n'a pas \u00e9t\u00e9 enregistrer, voulez-vous les enregistrer ?",
+        "quitWithoutSavingCat": "Votre cat\u00e9gorie n'a pas \u00e9t\u00e9 enregistrer, voulez-vous les enregistrer ?",
         "unsavedChanges": "sauvegarder les changements",
+        "confirmDeleteCategory":"Vous \u00eates sur le point de supprimer d\u00e9finitivement une catégorie",
+        "confirmDeleteArticle": "Vous \u00eates sur le point de supprimer d\u00e9finitivement un article",
         // config global
-        "styleDeNews" :   "Affichage de la liste des articles",
+        "configGlobalDesc" : "Ajustez les paramétres des catégories/articles","styleDeNews" :   "Affichage de la liste des articles",
         "styleDeNewsLegend" :   "Appliquez un style à la liste des articles",
         "masonryLegend"         :   "Tuiles",
         "gridLegend"            :   "Grille",
         "listLegend"            :   "Liste",
         "newsNombreImage"       :   "Nombre de colonne",
-        "newsNombreImageLegend" :   "Glissez pour ajuster le nombre de colonne affichées",
+        "newsNombreImageLegend" :   "Glissez pour ajuster le nombre de colonnes affichées",
         "newsStyleAffichage"    :   "Style des articles",
         "newsStyleAffichageDesc":   "Appliquez un style aux articles",
         "FormatImage"           :   "Format de l'image",
@@ -114,6 +117,10 @@
         "publishImmediate" : "Votre articke sera publié imméfiatement",
         "publishArticle" : "Publier l'article",
         "dateFormat":"Y-m-d",
+        "publishDone":"Article publié",
+        "programDone":"Article programmé",
+        "publishError": "Erreur de publier",
+        "errorPublishingInvalideElement": "Votre article n'est pas valid",
 
         //available
         "addContent":"Ajouter du contenu",
@@ -127,7 +134,22 @@
         "advanced":"Blocs avancés",
         "others":"Autres blocs",
         "errorRenderDuring":"Une erreur nous empêche d'afficher ce bloc",
-        "oops":"Oups !"
+        "oops":"Oups !",
+
+        //preview
+        "resizeByDevice": "< Redimensionner par support",
+        "deviceMobile": "Mobile",
+        "deviceTablet": "Tablette",
+        "deviceDesktop": "Ordinateur",
+        "deviceLargeDesktop": "Ordinateur avec écran large ( > 1441px )",
+        "closePreview": "Fermer",
+        "saveVersion": "Restaurer",
+        "toggleFullscreen": "Taille réelle / Ajuster",
+        "cloningError": "Une erreur est survenue lors du clonage de la page, essayez d'actualiser la fenètre",
+        "batchCloningError": "Une erreur est survenue lors du clonage des page, essayez d'actualiser la fenêtre",
+        "layoutChanged": "Le layout a été changé avec succès.",
+        "success": "Effectué !",
+        "introPageTitle":"Mes pages",
     },
     "fr-fr": true,
     "fr-ca": true
Index: src/less/imports/news_panel/main.less
===================================================================
--- src/less/imports/news_panel/main.less	(révision 12580)
+++ src/less/imports/news_panel/main.less	(révision 12581)
@@ -1,6 +1,7 @@
 // basic less tools
 @import 'import/vars';
 @import '../common/import/mixins';
+@import "../mixins.less";
 @import '../variables.less';
 
 
@@ -170,6 +171,139 @@
 		
 	}
 }
+
+.news-right-panel{
+    // Scrollbar Firefox
+    * {
+        scrollbar-color: #999999 #1a1a1a;
+        scrollbar-width: thin;
+    }
+    // Scrollbar Webkit Chrome/Edge/Safari
+    ::-webkit-scrollbar {
+        width: 12px;
+        height: 12px;
+    }
+    ::-webkit-scrollbar-track {
+        border-radius: 0;
+        background: #1a1a1a;
+        border: none;
+    }
+    ::-webkit-scrollbar-thumb {
+        border-radius: 16px;
+        background: #999999;
+        background-clip: padding-box;
+        border: 3px solid #1a1a1a;
+        box-shadow: none;
+        min-height: 50px;
+
+        &:hover {
+            background: #5b5b5b;
+            background-clip: border-box;
+            border: 3px solid #1a1a1a;
+        }
+    }
+	label .icon{
+		color: #5b5b5b;
+		font-size: 1em;
+	}
+	position: absolute;
+	top: 20px;
+	left: 20px;
+	right: 5px;
+	bottom: 20px;
+	.panel-head{
+		display: block;
+		border-bottom: 1px solid #3f3f3f;
+		padding-bottom: 10px;
+		span{
+			display: inline;
+			font-size: 1.2em;
+			font-weight: 600;
+			margin: 3px;
+			&.icon{
+				font-size: 1em;
+			}
+		}
+	}
+	.panel-content-intro{
+		border-bottom: 1px solid #3f3f3f;
+		font-size: 0.9em;
+		color: #999;
+	}
+    ///contenu
+    .panel-content {
+        position: absolute;
+        top: 80px;
+        bottom: 50px;
+        width: 100%;
+        overflow: auto;
+        overflow-x: hidden;
+        box-sizing: border-box;
+        padding-right: 3px;
+        padding-bottom: 20px;
+        scrollbar-gutter: stable;
+		display: block;
+        .panel-content-legend {
+            color: @ui-grey;
+            font-size: 0.8em;
+        }
+		.panel-content-content{
+			border-bottom: 1px solid #3f3f3f;
+			margin: 5px 0;
+			span{
+				display: inline;
+				margin: 3px;
+				&.label{
+					font-weight: 600;
+					font-size: 1.3em;
+				}
+			}
+			textarea, input {
+				background-color: inherit;
+				color: #999;
+				font-size: 0.9em;
+				}
+		}
+		.layouts{
+			width: 100%;
+			&.btn-group.open .btn.dropdown-toggle {
+				.hsv-background(@lightgrey,-40);
+			  }
+			.btn.dropdown-toggle{
+				text-align: left;
+				min-width: 90%;
+				line-height: 30px;
+				color: #fff;
+				.caret {
+					margin: 0;
+					display: block;
+					position: absolute;
+					top: 13px;
+					right: 8px;
+				}
+			}
+			.dropdown-menu {
+				width: 90%;
+				.hsv-background(@lightgrey,-30);
+				.box-shadow(0 5px 10px rgba(0, 0, 0, 0.25));
+				.background-clip();
+			  }
+			  .dropdown-menu > li {
+				border: none;
+			  }
+			  .dropdown-menu > li > a {
+				color: #fff;
+			  }
+			  .dropdown-menu > li > a:hover,
+			  .dropdown-menu > li > a:focus,
+			  .dropdown-menu > .active > a,
+			  .dropdown-menu > .active > a:hover,
+			  .dropdown-menu > .active > a:focus {
+				.hsv-background(@lightgrey,-20);
+			  }
+		}
+    }
+}
 .content-zone{
 	margin-top: 9rem;
 }
@@ -235,6 +369,27 @@
 		display: flex;
 		width: 100%;
 		justify-content: space-between;
+		.none-image{
+			width: 300px;
+			height: 220px;
+			border: 1px solid #e1e1e1;
+			position: relative;
+			background: #e1e1e1;
+			div{
+				content: "";
+				display: block;
+				position: absolute;
+				width: 40px;
+				height: 28px;
+				left: 50%;
+				top: 50%;
+				margin-left: -20px;
+				margin-top: -14px;
+				background-image: url(../../assets/img/sprite.png);
+				background-repeat: no-repeat;
+				background-position: -400px -140px;
+			}
+		}
 		.upload-categorie, .upload-article{
 			height: 100%;
 			width: 300px;
@@ -366,6 +521,9 @@
 			&.bg-blue{
 				background-color: @newsColorLight;
 			}
+			&.bg-red{
+				background-color: @red-pointer;
+			}
 		}
 	}
 }
@@ -432,7 +590,6 @@
 		padding-left: 20px;
   
 		i {
-		  cursor: pointer;
 		  vertical-align: top;
 		  margin-top: 18px;
 		  margin-right: 3px;
@@ -439,9 +596,6 @@
 		  font-size: 20px;
 		  color: #ddd;
 		  display: inline-block;
-		  &:hover {
-			color: #a3bec2;
-		  }
   
 		}
 		.inherit-from {
@@ -455,13 +609,10 @@
 		  }
 		}
 		.text {
-		  cursor: pointer;
 		  max-width: 25vw;
 		  display: block;
 		  white-space: nowrap;
 		  overflow: hidden;
-		  /*text-overflow: ellipsis;*/
-		  border: 1px solid transparent;
 		  padding: 0 8px;
 		  max-height: 51px;
 		  color: @darkgrey;
@@ -472,11 +623,6 @@
 			overflow: hidden;
 			text-overflow: ellipsis;
 		  }
-  
-		  &>.content:focus {
-			outline: none;
-			text-overflow: clip;
-		  }
 		  i {
 			visibility: hidden;
 			margin-right: 0;
@@ -491,24 +637,8 @@
 			i {
 			  visibility: hidden;
 			}
-			&:hover,
-			&.active {
-			  color: @darkgrey;
-			  border: 1px solid @pageColor;
-			  i {
-				visibility: hidden;
-			  }
-			}
   
 		  }
-		  &:hover,
-		  &.active {
-			color: @grey;
-			border: 1px solid @lightgrey;
-			i {
-			  visibility: visible;
-			}
-		  }
   
 		}
 		&.unlocked {
@@ -525,7 +655,7 @@
 	}
 	.config-preview {
 		position: absolute;
-		top: 21px;
+		top: 32px;
 		right: 36px;
 		& .icon {
 		  display: inline-block;
@@ -642,6 +772,7 @@
   
 	}
 	.page {
+		border-bottom: 1px solid #e1e1e1;
 		.clearfix();
 	}
 	.zone-toolbox .publish{
