Revision: r13765
Date: 2025-01-31 08:53:02 +0300 (zom 31 Jan 2025) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News IDEO: afficher les images news dans filepanel (partie JS)

## Files changed

## Full metadata
------------------------------------------------------------------------
r13765 | srazanandralisoa | 2025-01-31 08:53:02 +0300 (zom 31 Jan 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileDetailManager.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileList.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileManagerUIView.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileListView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileManagerUIView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/common/queries/height-based/file-panel.less
   M /branches/ideo3_v2/integration/src/less/imports/filePanel.less

News IDEO: afficher les images news dans filepanel (partie JS)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/FilePanel/Templates/fileDetailManager.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileDetailManager.html	(révision 13764)
+++ src/js/JEditor/FilePanel/Templates/fileDetailManager.html	(révision 13765)
@@ -27,7 +27,7 @@
             <span class="icon-download"></span>
             <span class="infobulles"><%= __("getFile")%></span>
         </a>
-        <% if(user.can("delete_file")){ %>
+        <% if(user.can("delete_file")&& isRemovable){ %>
         <a href="#" data-delete="<%=id%>">
             <span class="icon-bin"></span>
             <span class="infobulles"><%= __("deleteFile")%></span>
Index: src/js/JEditor/FilePanel/Templates/fileList.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileList.html	(révision 13764)
+++ src/js/JEditor/FilePanel/Templates/fileList.html	(révision 13765)
@@ -44,10 +44,11 @@
                 <%}%>
                 <span class="masque"></span>
                 <ul class="menu2">
+                    <%if(file.attributes.isRemovable){%> 
                         <li class="action delete" data-cid="<%=file.cid %>" >
                             <span class="icon-bin"></span>
                         </li>
-                    
+                    <%}%>
                     <li class="action goToFile" data-cid="<%=file.cid %>" >
                         <span class="icon-add"></span>
                     </li>
Index: src/js/JEditor/FilePanel/Templates/fileManagerUIView.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileManagerUIView.html	(révision 13764)
+++ src/js/JEditor/FilePanel/Templates/fileManagerUIView.html	(révision 13765)
@@ -14,6 +14,12 @@
     <span class="icon-file"></span>
     <span class="infobulles"><%= __("Files")%></span>
 </a>
+<% if(user.can('access_panel_news', __IDEO_NEWS__)) {%>
+<a href="#" class="filtres" id="news-files" data-filterBy="news">
+    <span class="icon-News"></span>
+    <span class="infobulles"><%= __("NewsFiles")%></span>
+</a>
+<%}%>
 
 <!-- TRIER PAR -->
 <div class="trier-par">
Index: src/js/JEditor/FilePanel/Views/FileListView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileListView.js	(révision 13764)
+++ src/js/JEditor/FilePanel/Views/FileListView.js	(révision 13765)
@@ -237,6 +237,11 @@
         this.collection.setOffset(0);
         this.collection.fetch({remove: true});
         },
+        onlyImagesNews : function() {
+            this.collection.setType('news');
+            this.collection.setOffset(0);
+            this.collection.fetch({remove: true});
+        },
         resetFilter :function(){
             this.collection.setType(null);
             this.collection.setOffset(0);
Index: src/js/JEditor/FilePanel/Views/FileManagerUIView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileManagerUIView.js	(révision 13764)
+++ src/js/JEditor/FilePanel/Views/FileManagerUIView.js	(révision 13765)
@@ -107,6 +107,9 @@
                         case 'photos':
                             this.listView.onlyImages();
                             break;
+                        case 'news':
+                            this.listView.onlyImagesNews();
+                            break;
                     }
                     var $target = $(event.currentTarget);
                     this.dom[this.cid].filters.removeClass('active');
@@ -122,6 +125,9 @@
                     }else if(type ==="file"){
                         this.$("#files").addClass("active");
                     }
+                    else if(type ==="news"){
+                        this.$("#news-files").addClass("active");
+                    }
                 },
                 onSelect: function(selected, length) {
                     this._super(selected, length);
Index: src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 13764)
+++ src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 13765)
@@ -133,4 +133,5 @@
     "browseIconTitle":"Parcourir la base d'icônes",
     "showIcons" : "Importer depuis une base d'images",
     "mimeNotsupporte": "non pris en charge. Échec de la création d'une ressource",
+    "NewsFiles" :"Entêtes articles",
 });
\ No newline at end of file
Index: src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 13764)
+++ src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 13765)
@@ -137,4 +137,5 @@
     "browseIconTitle":"Parcourir la base d'icônes",
     "showIcons" : "Importer depuis une base d'images",
     "mimeNotsupporte": "non pris en charge. Échec de la création d'une ressource",
+    "NewsFiles" :"Entêtes articles",
 });
\ No newline at end of file
Index: src/js/JEditor/FilePanel/nls/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/i18n.js	(révision 13764)
+++ src/js/JEditor/FilePanel/nls/i18n.js	(révision 13765)
@@ -139,6 +139,7 @@
         "browseIconTitle":"Browse the icon database",
         "showIcons" : "Import from image database",
         "mimeNotsupporte":"not supported. Create resource failed",
+        "NewsFiles" :"Article headers"
     },
     "fr-fr": true, "fr-ca":true
 })
\ No newline at end of file
Index: src/less/imports/common/queries/height-based/file-panel.less
===================================================================
--- src/less/imports/common/queries/height-based/file-panel.less	(révision 13764)
+++ src/less/imports/common/queries/height-based/file-panel.less	(révision 13765)
@@ -16,8 +16,8 @@
 		}
 
 		a.filtres:nth-child(1) { margin: 10px 1px 0 20px; }
-		a.filtres:nth-child(2) { margin: 10px 0 0 0; }
-		a.filtres:nth-child(3) { margin: 10px 0 0 1px; }
+		a.filtres:nth-child(2),a.filtres:nth-child(3) { margin: 10px 0 0 0; }
+		a.filtres:nth-child(4) { margin: 10px 0 0 1px; }
 
 		.trier-par { margin: 10px 20px; }
 		.separation { height: 50px; }
Index: src/less/imports/filePanel.less
===================================================================
--- src/less/imports/filePanel.less	(révision 13764)
+++ src/less/imports/filePanel.less	(révision 13765)
@@ -103,11 +103,11 @@
         .border-radius(4px 0 0 4px);
         line-height: 3;
     }
-    & a.filtres:nth-child(2){
+    & a.filtres:nth-child(2), a.filtres:nth-child(3){
         margin: 20px 0 0 0;
         line-height: 39px;
     }
-    & a.filtres:nth-child(3){
+    & a.filtres:nth-child(4){
         margin: 20px 0 0 1px;
         .border-radius(0 4px 4px 0);
         line-height: 37px;
