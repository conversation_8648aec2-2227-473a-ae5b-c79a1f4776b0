Revision: r14196
Date: 2025-05-06 09:51:45 +0300 (tlt 06 Mey 2025) 
Author: frahajanirina 

## Commit message
Wishlist:News: Bug boutons admin

## Files changed

## Full metadata
------------------------------------------------------------------------
r14196 | frahajanirina | 2025-05-06 09:51:45 +0300 (tlt 06 Mey 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/i18n.js

Wishlist:News: Bug boutons admin
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 14195)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 14196)
@@ -474,7 +474,7 @@
                                         this.childViews.categorieList.$('.wrapper').each(function() {
                                             categorieListHeight += $(this).height();
                                         })
-                                        var newHeight = availableSpace < categorieListHeight ? (windowHeight - categorieListOffset) : 'auto';
+                                        var newHeight = (windowHeight - categorieListOffset);
                                         categorieList.height(newHeight);
                                         this.childViews.categorieList.updateScrollables();
                                     }
Index: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 14195)
+++ src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 14196)
@@ -180,5 +180,6 @@
 	"fileUnauthorised": "Le ficher n\'a pas été importé. Ce type de fichier n\'est pas autorisé.",
 	"uploadLink": "Importer les articles",
 	"uploadSuccess": "L'article a été importé avec succès",
-	"uploadError": "Erreur lors du téléchargement de l'image de l'article"
+	"uploadError": "Erreur lors du téléchargement de l'image de l'article",
+	"editImage": "Retoucher l'image"
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 14195)
+++ src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 14196)
@@ -180,5 +180,6 @@
 	"fileUnauthorised": "Le ficher n\'a pas été importé. Ce type de fichier n\'est pas autorisé.",
 	"uploadLink": "Importer les articles",
 	"uploadSuccess": "L'article a été importé avec succès",
-	"uploadError": "Erreur lors du téléchargement de l'image de l'article"
+	"uploadError": "Erreur lors du téléchargement de l'image de l'article",
+	"editImage": "Retoucher l'image"
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/i18n.js	(révision 14195)
+++ src/js/JEditor/NewsPanel/nls/i18n.js	(révision 14196)
@@ -177,7 +177,8 @@
         "fileUnauthorised": "The file was not imported. This file type is not allowed.",
         "uploadLink": "Import articles",
         "uploadSuccess": "Article has been successfully imported",
-        "uploadError": "Failed to upload the article image"
+        "uploadError": "Failed to upload the article image",
+        "editImage": "Edit picture"
     },
     "fr-fr": true,
     "fr-ca": true
