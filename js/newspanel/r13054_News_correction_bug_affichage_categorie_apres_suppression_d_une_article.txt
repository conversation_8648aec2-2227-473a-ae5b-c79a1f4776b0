Revision: r13054
Date: 2024-09-18 14:36:58 +0300 (lrb 18 Sep 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: correction bug affichage categorie apres suppression d une article

## Files changed

## Full metadata
------------------------------------------------------------------------
r13054 | srazanandralisoa | 2024-09-18 14:36:58 +0300 (lrb 18 Sep 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/ArticleEditorView.js

News: correction bug affichage categorie apres suppression d une article
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 13053)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 13054)
@@ -700,7 +700,10 @@
                                             }
                                         });
                                     } 
-                                    else   this.currentCategorie = categorie;
+                                    else {
+                                        this.currentArticle = null;
+                                        this.currentCategorie = categorie;
+                                    }  
                                 },
                                 /**
                                  * Montre le panneaux de blocs disponibles
Index: src/js/JEditor/NewsPanel/Views/ArticleEditorView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticleEditorView.js	(révision 13053)
+++ src/js/JEditor/NewsPanel/Views/ArticleEditorView.js	(révision 13054)
@@ -71,7 +71,8 @@
             self = this;
             this.listenTo( this.articleDetail, Events.ArticleAddEvents.ADDED, _.bind(function(article) {
                 self.newsPanel.currentArticle = article;
-            })); 
+            }));
+            this.listenTo(this.model, 'destroy', this.onDestroy); 
         },
         checkAffixTop:function(){
             return (document.documentElement.clientHeight < 866 ? 90 : 230);
@@ -236,6 +237,10 @@
                 callback();
                 return this;
             }
+        },
+        onDestroy: function() {
+            this.stopListening();
+            Backbone.View.prototype.remove.call(this);
         }
     });
     Events.extend({
