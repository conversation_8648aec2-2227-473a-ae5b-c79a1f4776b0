Revision: r12237
Date: 2024-04-24 17:12:21 +0300 (lrb 24 Apr 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News : Create Read Update Category

## Files changed

## Full metadata
------------------------------------------------------------------------
r12237 | srazana<PERSON>lisoa | 2024-04-24 17:12:21 +0300 (lrb 24 Apr 2024) | 1 ligne
Chemins modifiés :
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/Articles.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/ArticlesCollection.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/Categorie.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/CategorieCollection.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/CategorieLang.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/NewsPanel.html
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/addArticle.html
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/articlesTable.html
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/categorieForm.html
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/categorieList.html
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/configCategorie.html
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/emptyArticle.html
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/newsEditor.html
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/AddArticleView.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/CategorieAddView.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/CategorieCollectionView.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/ConfigCategorieView.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/NewsEditorView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/nls/i18n.js

News : Create Read Update Category
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Templates/configCategorie.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/configCategorie.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Templates/configCategorie.html	(révision 12237)
@@ -0,0 +1,28 @@
+<div id="zone-versions-panel" class="version-collection scroll-container">
+    <header class="panel-head">
+        <span class="icon-refresh"></span>
+        <h1 class="panel-name"><%= __("versionOfZone")%></h1>
+    </header>
+    <div class="panel-content active">
+        <div class="panel-content-intro">
+            <%= __("availableDesc")%>
+        </div>
+    </div>
+    <div id="maxVersionWarning" class="warning">
+        <span class="icon-warning"></span><span><%= __("reachMaxVersionMsg") %></span>
+    </div>
+    <div class="option-content zone-versions availables">
+        <ul class="zone-version-items"></ul>
+        <div class="no-version">
+            <div class="icon">
+                <span class="icon-refresh"></span>
+            </div>
+            <span class="text-intro">
+                <%= __("noVersion") %>
+            </span>
+            <span class="how-to">
+                <%= __("howToZoneVersion") %>
+            </span>
+        </div>
+    </div>
+</div>
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Models/Articles.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Articles.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Models/Articles.js	(révision 12237)
@@ -0,0 +1,22 @@
+define([
+    "underscore",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Models/LockableModel"
+], function(_, Events,LockableModel) {
+    var Article = Content.extend(
+            /**
+             * @lends Article
+             */
+                    {
+                        defaults: {sectionCount: 0},
+                        urlRoot: __IDEO_API_PATH__ + '/news/content/',
+                        depSaved: 0,
+                        depError: 0,
+                        childrenAttribute: 'sections',
+                        childClass: Section,
+                    });
+
+            Article.prototype = _.extend(Article.prototype, LockableModel.prototype);
+            Article.SetAttributes(['sectionCount', 'name', 'sections', 'customized']);
+            return Article;
+        });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Models/ArticlesCollection.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/ArticlesCollection.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Models/ArticlesCollection.js	(révision 12237)
@@ -0,0 +1,35 @@
+define([
+    "collection!JEditor/Commons/Ancestors/Models/Collection",
+    "JEditor/PagePanel/Contents/Zones/Models/Zone"
+], function(Collection, Zone) {
+    var ArticlesCollection = Collection.extend(
+        /**
+         * @lends ArticleCollection.prototype
+         */
+                {
+                    url: __IDEO_API_PATH__ + '/news/Articles',
+                    //model: Article,
+                    set: function() {
+                        Collection.prototype.set.apply(this, arguments);
+                        this.each(function(model) {
+                            model.resetChildren();
+                        });
+                    },
+                    reset:function(){
+                        Collection.prototype.reset.apply(this, arguments);
+                        this.each(function(model) {
+                            model.resetChildren();
+                        });
+                    }
+                }
+        );
+
+        Object.defineProperties(ArticlesCollection.prototype, {
+            main: {
+                get: function() {
+                    return this.at ? this.at(0) : undefined;
+                }
+            }
+        });
+        return ArticlesCollection;
+    });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Models/Categorie.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Categorie.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Models/Categorie.js	(révision 12237)
@@ -0,0 +1,56 @@
+define([
+    "underscore",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Models/Model",
+    "JEditor/FilePanel/Models/FileCollection"
+],function(_,
+        Events,
+        Model,
+        FileCollection,
+){ var Categorie = Model.extend(
+                {
+                    
+                    urlRoot: __IDEO_API_PATH__ + '/news/category',
+                    defaults: {
+                        ressource: null,
+                        numberArticlesInCagetory:0,
+                        lang : {}
+                    },
+                   
+                    initialize: function() {
+                        this._super();
+                        this.file = null;
+                        this.lastState = this.toJSON();
+                        this.on(Events.BackboneEvents.SYNC, this._onSync);
+                    },
+                    _onSync: function() {
+                        this.lastState = this.toJSON();
+                    },
+                    hasUnsavedChanges: function() {
+                        return !_.isEqual(this.toJSON(), this.lastState);
+                    },
+                    cancel: function() {
+                        this.set(this.lastState);
+                    },
+                    getByLanguage :function (lang) {
+                        return this.lang[lang.id];
+                    },
+                    setByLanguage :function (lang, data) {
+                        return this.lang[lang.id] = data;
+                    },
+                    getFile: function () {
+                        if (this.file) return this.file ;
+                        if(!this.ressource) return null;
+                        this.fileCollection =  new FileCollection();
+                        this.fileCollection.setId(this.ressource);
+                        this.fileCollection.fetch({async:false});
+                        this.file =  this.fileCollection.get(this.ressource);
+                        return this.file;
+                    },
+                    parse : function(data) {
+                        return (data.data)? data.data : data;
+                    },
+                });
+            Categorie.SetAttributes(['ressource','lang','numberArticlesInCagetory']);
+        return Categorie;
+    });
Index: src/js/JEditor/NewsPanel/Models/CategorieCollection.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/CategorieCollection.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Models/CategorieCollection.js	(révision 12237)
@@ -0,0 +1,89 @@
+define([
+    "JEditor/Commons/Ancestors/Models/DBCollection",
+    "JEditor/Commons/Ancestors/Models/Collection",
+    "./Categorie"
+], function(
+        DBCollection,
+        Collection,
+        Categorie
+        ) {
+    var CategorieCollection = Collection.extend(
+            /**
+             * @lends CategorieCollection
+             */
+                    {
+                        /**
+                         * @constructs
+                         */
+                        // constructor: function(models, options) {
+                        //     if (options && options.lang)
+                        //         this.lang = options.lang;
+                        //     Collection.call(this, models, options);
+                        // },
+                        initialize: function () {
+                            Collection.prototype.initialize.apply(this, arguments);
+                        },
+                        onSync: function (coll,resp) {
+                           console.log(resp);
+                        },
+                        url: function() {
+                            return __IDEO_API_PATH__ + '/news/category';
+                        },
+                        parse: function (response) {
+                            return response.data; 
+                        },
+                        groupBy: function(iterator, context) {
+                            if (iterator!=='lang') {
+                                this._super();
+                            }
+                            else {
+                                var groups = {};
+                                var existingLanguages = {};
+
+                                this.forEach(function(model) {
+
+                                    var lang = model.get('lang');
+                                    
+                                    for (var langKey in lang) {
+                                        if (lang[langKey]) {
+                                            existingLanguages[langKey] = true;
+                                        }
+                                    }
+                                    // Utiliser la logique de regroupement personnalisée
+                                    var groupKey = this.determineGroup(lang, existingLanguages);
+                                   
+                                    
+                                    groupKey.forEach(element => {
+                                        if (!groups[element]) {
+                                            groups[element] = [];
+                                        }
+                                        groups[element].push(model);
+                                    });
+                                    
+                                    
+                                }, this); 
+                                var existingLanguagesKeys = Object.keys(existingLanguages);
+                                
+                                if (existingLanguagesKeys.length === 0) {
+                                    existingLanguagesKeys.push('other');
+                                }
+                                return groups;
+                            }
+                        },
+                        determineGroup: function(lang, existingLanguages) {
+                            langexist = [];
+                            for (var langKey in existingLanguages) {
+                                if (lang[langKey]) {
+                                    langexist.push(langKey);
+                                }
+                            }
+                            
+                            return langexist;
+                        },
+                        /**
+                         * Categorie
+                         */
+                        model: Categorie,
+                    });
+            return CategorieCollection;
+        });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Models/CategorieLang.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/CategorieLang.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Models/CategorieLang.js	(révision 12237)
@@ -0,0 +1,16 @@
+define(["JEditor/Commons/Ancestors/Models/Model"
+],function(Model){ 
+    var Categorie = Model.extend(
+    {
+        
+        defaults: {
+            title: "",
+            description: "",                
+            metaTitle:"", 
+            metaDescription: "",
+            urlCategory:"",
+        },
+    });
+    Categorie.SetAttributes(['title','description', 'metaTitle', 'metaDescription', "urlCategory"]);
+    return Categorie;
+});
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12236)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12237)
@@ -1,16 +1,47 @@
 define([
+    "jquery",
+	"underscore",
+    "JEditor/Commons/Events",
     "JEditor/Commons/Ancestors/Views/PanelView",
     "text!./Templates/NewsPanel.html",
     "JEditor/Commons/Languages/Views/LanguagesDropDown",
-    "./Views/NewsListView",
+    "./Views/CategorieAddView",
+    "./Views/CategorieCollectionView",
+    "./Views/ArticlesCollectionView",
+    "./Views/NewsEditorView",
+    "./Views/ConfigCategorieView",
+    "./Models/CategorieCollection",
+   // "./Models/ArticlesCollection",
+    "JEditor/App/Views/RightPanelView",
+
     "i18n!./nls/i18n"
 ],
-        function (PanelView, NewsPanelTemplate, LanguagesDropDown, NewsListView, translate) {
+        function (
+            $,
+            _,
+            Events,
+            PanelView, 
+            NewsPanelTemplate, 
+            LanguagesDropDown, 
+            CategorieAddView, 
+            CategorieCollectionView, 
+            ArticlesCollectionView,
+            NewsEditorView,
+            ConfigCategorieView,
+            CategorieCollection, 
+     //       ArticlesCollection,
+            RightPanelView,
+            translate
+            ) {
             var NewsPanel = PanelView.extend(
                     /**
                      * @lends JEditor.Panels.NewsPanel.prototype
                      */
                             {
+                                events : {
+                                    'click .addCategory':'onAddCategorieClick',
+                                    'click .addArticle':'onAddArticleClick'
+                                },
                                 cssFile: __IDEO_INTEGRATION_PATH__ + "/build/css/news_panel.css",
 
                                 constructor: function (options) {
@@ -23,8 +54,45 @@
                                 initialize: function () {
                                     this._super();
                                     this._template = this.buildTemplate(NewsPanelTemplate, translate);
+                                    this.rightPanelView = new RightPanelView();
+                                    this._byLang = {};
+                                    this.categories =  new CategorieCollection();
+                                    this.categories.fetch();
                                 },
                                 load: function () {
+                                    var loaded = 0;
+                                    var languagesCount = this.languages.length;
+                                  
+
+                                    this._byLang = this.categories.groupBy('lang');
+
+                                    this.currentList = this._byLang[this.currentLang.id];
+
+                                    this.on(Events.NewsPanelEvents.CATEGORIE_CHANGE, this._onCategorieChange); 
+
+                                    this.childViews.articlesList = new ArticlesCollectionView({
+                                        collection : this.articles,
+                                        language : this.currentLang,
+                                    });
+
+                                    this.childViews.newsEditorView = new NewsEditorView({
+                                        title : 'tout les articles',
+                                        usepreview : false,
+                                        languages : this.languages,
+                                        currentLang : this.currentLang,
+                                        categorieCollection :  this.categories,
+
+                                    });
+                                    this.childViews.configCategorieView = new ConfigCategorieView({
+                                        pagePanel : this
+                                    });
+                        
+                                    this.childViews.categorieList = new CategorieCollectionView({
+                                        collection : this.categories,
+                                        language : this.currentLang,
+                                        languages : this.languages
+                                    });
+                                    
                                     //load des langues
                                     this.childViews.langDropDown =  new LanguagesDropDown({
                                         collection : this.languages,
@@ -31,24 +99,303 @@
                                         _default : this.currentLang,
                                         defaultLabel : 'language'
                                     });
-
-                                    this.childViews.newsListView = new NewsListView();
+                                   
                                     this.listenTo(this.childViews.langDropDown, "selected:choice", this._onLangSelect);
-
-                                    //utiliser si erreur
-                                    // this.loadingError();
-                                    //toujours utiliser si cuccès
+                            
                                     this.loadingEnd();
                                 },
                                 /**
+                                 * met à jour la vue de page (zone du centre de l'écran)
+                                 */
+                                _onCategorieChange : function() {
+
+                                    this.dom.body.animate({
+                                        scrollTop : 0
+                                    }, 300);
+                                    var callback = _.bind(function() {
+                                        this.renderRightPanel();
+                                       
+                                            var windowHeight = this.dom.window.height();
+                                            var offset = this.$('#news-editor').offset().top - this.dom.window.scrollTop();
+                                            
+                                            this.$("#news-editor").css('minHeight', (windowHeight - offset) + 'px');
+                                            
+                                           // this.childViews.newsEditorView.load();
+                                           if (this.currentCategorie) { 
+                                                this.childViews.newsEditorView = new NewsEditorView({
+                                                    title : this.currentCategorie.lang[this.currentLang.id].title,
+                                                    model : this.currentCategorie,
+                                                    languages : this.languages,
+                                                    currentLang : this.currentLang,
+                                                    categorieCollection :  this.categories,
+                                                });
+                                                this.$("#news-editor").append(this.childViews.newsEditorView.el);
+                                                this.childViews.newsEditorView.renderCategorieView();
+                                                var url = 'news/' + this.currentLang.id + '/' + this.currentCategorie.id ;
+                                                this.app.params.lastUrl = url;
+                                                this.app.params.save();
+                                                this.app.router.navigate(url);
+                                            }
+                                            else {
+                                                this.childViews.newsEditorView = new NewsEditorView({
+                                                    title : 'tout les articles',
+                                                    languages : this.languages,
+                                                    currentLang : this.currentLang,
+                                                    categorieCollection :  this.categories,
+                                                });
+                                                this.$("#news-editor").append(this.childViews.newsEditorView.el);
+                                                this.childViews.newsEditorView.renderArticleList();
+                                            }
+                                            
+                                    }, this);
+                                    if (this.childViews.newsEditorView) {
+                                        this.listenToOnce(this.childViews.newsEditorView, Events.ViewEvents.HIDE, function() {
+                                            this.childViews.newsEditorView.remove();
+                                            callback();
+                                        });
+                                        this.childViews.newsEditorView.hide();
+                                    } else
+                                        callback();
+                                },
+                            
+                                showConfigCategorie : function(categorie) {
+                                    this.renderRightPanel();
+                                    this.rightPanelView.showContent(this.childViews.configCategorieView);
+                                    this.rightPanelView.showPanel();
+                                    return false;
+                                },
+                                /**
+                                 * Cache le panneau de blocs disponibles
+                                 */
+                                hideConfigCategorie : function() {
+                                    this.rightPanelView.hideContent(this.childViews.configCategorieView);
+                                    this.rightPanelView.hidePanel();
+                                },
+                                unload : function() {
+                                    this.listenToOnce(this.rightPanelView, Events.ViewEvents.HIDE, function() {
+                                        this.trigger(Events.PanelEvents.UNLOAD, this);
+                                    });
+                                  //  this.rightPanelView.hidePanel();
+                                },
+                                /**
+                                 * déclenché lors de la fin du chargement du panneau
+                                 *
+                                 * @param {JEditor.Panels.MyNewPanel}
+                                 *            loaded le panneau chargé
+                                */
+                                onLoad : function(loaded) {
+                                    // listeners
+                                    this.listenTo(this.childViews.langDropDown, Events.ChoiceEvents.SELECT, this._onLangSelect);
+                                    this.listenTo(this.childViews.categorieList, Events.ChoiceEvents.SELECT, this._onCategorieSelect);
+                                    this.listenTo(this.childViews.categorieList, 'render', this._scrollbar);
+                                    this.render();
+                                },
+                                /**
+                                 * Met à jour la scrollBar de la liste de page
+                                 *
+                                 * @private
+                                 */
+                                _scrollbar : function() {
+                                    var button = this.dom.newsPanel.addCategorieButtonDown
+                                    var categorieList = this.childViews.categorieList.$el;
+                                    if (categorieList.parent().length > 0 && this && button) {
+                                        var windowHeight = this.dom.window.height();
+                                        var categorieListOffset = categorieList.offsetParent().offset().top - this.dom.window.scrollTop() + categorieList.position().top + button.outerHeight(true);
+                                        var availableSpace = windowHeight - categorieListOffset;
+                                        var categorieListHeight = 0;
+                                        this.childViews.categorieList.$('.wrapper').each(function() {
+                                            categorieListHeight += $(this).height();
+                                        })
+                                        var newHeight = availableSpace < categorieListHeight ? (windowHeight - categorieListOffset) : 'auto';
+                                        categorieList.height(newHeight);
+                                        this.childViews.categorieList.updateScrollables();
+                                    }
+                                },
+                                /**
+                                * crée le rendu du panneau de droite
+                                * 
+                                */
+                                renderRightPanel : function(content, renderChild) {
+                                    // setup args
+                                    var content     = content || this.childViews.configCategorieView;
+                                    var renderChild = renderChild || true;
+                                    
+                                    this.rightPanelView.clear();
+                                    this.rightPanelView.setElement(this.dom.newsPanel.rightSidebar);
+                                    if (this.currentArticle) {
+                                        this.rightPanelView.addContent(content);
+                                        if (renderChild && renderChild !== 'noRender') {
+                                            content.render();
+                                        }
+                                        }
+                                    this.dom.newsPanel.rightSidebar.affix({
+                                        offset : {
+                                            top : this.checkAffixTop
+                                        }
+                                    });
+                                    return this;
+                                },
+                                /**
+                                * rendu de la liste des categories (gauche)
+                                */
+                                _renderCategorieList : function() {
+                                    try {
+                                        this.dom.window.off('scroll.pageDelegate');
+                                        this.dom.window.off('resize.pageDelegate');
+                                    } catch (e) {
+                                    }
+                                    this.childViews.categorieList.destroyScrollables();
+                                    this.$('aside .options').after(this.childViews.categorieList.render().el);
+                                    this.$('.options').append(this.childViews.langDropDown.render().el);
+                                    this.$('aside').affix({
+                                        offset : {
+                                            top : this.checkAffixTop
+                                        }
+                                    });
+                                    this.dom.window.scroll();
+                                },
+                                /**
+                                * test la hauteur de fenètre
+                                * return true si mise en page 'petite hauteur'
+                                */
+                                checkAffixTop:function(){
+                                    return (document.documentElement.clientHeight < 866 ? 90 : 230);
+                                },
+                                /**
+                                 * rendu de la liste des articles
+                                 */
+                                _renderArticlesList: function() {
+                                    this.$("#news-editor #content-editor").append(this.childViews.articlesList.render().el);
+                                },
+                                /**
+                                 * crée le rendu de la page courante
+                                 */
+                                _renderNewsEditor : function() {
+                                    this.$("#news-editor").append(this.childViews.newsEditorView.el);
+                                    this.childViews.newsEditorView.renderArticleList();
+                                    // this.childViews.newsEditorView.renderArticlePage();
+                                },
+                                onAddCategorieClick : function() {
+                                    this.childViews.newsEditorView.renderAddCategorie()
+                                },
+                                /**
+                                 * ajoute les variables à l'objet this.dom
+                                */
+                                setDOM : function() {
+                                    this._super();
+                                    this.dom.newsPanel.rightSidebar = this.$('#item-config');
+                                    this.dom.newsPanel.addCategorieButtonDown = this.$('.dialog-view-trigger.addCategory');
+                                },
+                                /**
                                  * @return JEditor.Panels.NewsPanel
                                  */
                                 render: function () {
-                                    this.$el.html(this._template());
-                                    this.$('.side-bar__lang').append(this.childViews.langDropDown.render().el);
-                                    this.$('.page-container').append(this.childViews.newsListView.render().el);
+                                    this.$el.html(this._template({
+                                        empty: true,
+                                        noneSelected: true,
+                                        canAddNews:true
+                                    }));
+                                   this._renderCategorieList();
+                                   this.setDOM();
+                                   this.renderRightPanel();
                                     return this;
-                                }
+                                },
+                                /**
+                                 * Au changement de langue
+                                 */
+                                _onLangSelect : function(view, lang) {
+                                    this.currentLang = lang;
+                                    var url = 'news/' + lang.id;
+                                    this.app.router.navigate(url);
+                                    this.app.params.lastUrl = url;
+                                    this.app.params.save();
+
+                                },
+                                onLangChange : function(lang, panel) {
+                                    if (this.loaded) {
+                                        this.childViews.langDropDown.current = this.currentLang;
+                                       // this.currentPage = null;
+                                        this.childViews.categorieList.lang = lang.id;
+                                        //this.childViews.addPageView.setLanguage(lang);
+                                        this.listenTo(this.childViews.categorieList, Events.ViewEvents.HIDE, function() {
+                                            this._renderCategorieList();
+                                            this.childViews.categorieList.render();
+                                            this.childViews.categorieList.show();
+                                        });
+                                        this.childViews.categorieList.hide();
+                                    }
+                                },
+                                _onCategorieSelect : function(view, categorie) {
+                                    if (this.currentCategorie && this.currentCategorie.hasUnsavedChanges()) {
+                                        this.confirmUnsaved({
+                                            message : translate("quitWithoutSaving"),
+                                            title : translate("unsavedChanges"),
+                                            type : 'delete-not-saved',
+                                            onYes : _.bind(function() {
+                                                this.currentCategorie.save();
+                                                this.currentCategorie = categorie;
+                                            }, this),
+                                            onNo : _.bind(function() {
+                                                
+                                                this.currentCategorie.cancel();
+                                                this.currentCategorie = categorie;
+                                            }, this),
+                                            options : {
+                                                dialogClass : 'delete no-close',
+                                                dontAskAgain : true
+                                            }
+                                        });
+                                    } else
+                                        this.currentCategorie = categorie;
+                                },
                             });
+                            Object.defineProperties(NewsPanel.prototype,
+                                {
+                                    /**
+                                     * La liste de Categorie
+                                     *
+                                     * @type{CategotieCollection}
+                                     */
+                                    currentCategorieList : {
+                                        get : function() {
+                                            return this._currentList;
+                                        },
+                                        set : function(currentList) {
+                                            this._currentList = currentList;
+                                            this.trigger(Events.PagePanelEvents.CATEGORIE_LIST_CHANGE, this, currentList, undefined);
+                                        }
+                                    },
+                                    /**
+                                     * La categorie courante
+                                     *@type{categorie}
+                                     */
+                                    currentCategorie : {
+                                        get : function() {
+                                            return this._currentCategorie;
+                                        },
+                                        set : function(currentCategorie) {
+                                            if (currentCategorie !== this._currentCategorie) {
+                                                this._currentCategorie = currentCategorie;
+                                                this.trigger(Events.NewsPanelEvents.CATEGORIE_CHANGE, this, currentCategorie, undefined);
+                                            }
+                                        }
+                                    },
+                                });
+                                Events.extend(
+                                    /**
+                                     * @namespace Events.NewsPanelEvents.prototype
+                                     */
+                                    {
+                                        NewsPanelEvents : {
+                                            /**
+                                             * Attend un callback avec (model, options)
+                                             */
+                                            CATEGORIE_CHANGE : 'change:currentCategorie',
+                                            /**
+                                             * Attend un callback avec (model, options)
+                                             */
+                                            CATEGORIE_LIST_CHANGE : 'change:currentList'
+                                        }
+                                    });
                     return NewsPanel;
-                });
+                });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Templates/NewsPanel.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/NewsPanel.html	(révision 12236)
+++ src/js/JEditor/NewsPanel/Templates/NewsPanel.html	(révision 12237)
@@ -20,25 +20,22 @@
     </div>
 </div>
 
-<div class="page-container">
-
-	<aside class="side-bar">
-        <div class="side-bar__lang">
-            <p class="lang-label"><%=__('editedVersion')%></p>
+<div class="page-container" id="news-editor">
+    <aside class="sideBar news">
+        <div class="options">
+            <div class="lang">
+                <p><%= __("editedVersion")%></p>
+                <!-- dropdown de langues -->
+            </div>
         </div>
-        <span class="side-bar__title"><%=__('sideInfo')%></span>
-        <div class="scroll-container  message-scroll-container">
-            <ul class="form-list">
-        	    <!-- insert form list here-->
-            </ul>
-        </div>
-        <a class="dialog-view-trigger addCategory">
-            <span>+</span><%=__("newsAddACategory")%>
-        </a>
-
-        <a class="dialog-view-trigger addArticle">
-            <span>+</span><%=__("newsAddArticle")%>
-        </a>
+        <!--liste des pages-->
+        <% if(canAddNews){ %>
+            <a class="dialog-view-trigger addCategory">
+                <span>+</span><%=__("newsAddACategory")%>
+            </a>
+        <% } %>
 	</aside>
-    <!-- list -->
+    <div id="item-config" class="panel-container"></div>
+  
+    </section>
 </div>
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Templates/addArticle.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/addArticle.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Templates/addArticle.html	(révision 12237)
@@ -0,0 +1,39 @@
+<div class="main">
+    <div class="dis-table bordered">
+        <div class="flex_categorie">
+            <div class="upload-categorie">
+                <!-- uploader -->
+            </div>
+            <div class="categorie-info">
+                <div class="flex-betwen">
+                    <div> 
+                        <h3> Créer une catégorie</h3>
+                    </div>
+                    <!-- Choix de la langue -->
+                    <div class="side-bar__lang bg-blue">
+
+                    </div>
+                </div> 
+            <div>
+                <div>
+                    <label>Label :</label> <br>
+                    <input class="field-input" name="label" value="" type="text" placeholder="Label">
+
+                </div>
+                <div>
+                    <label> Description :</label><br>
+                    <textarea name="description" placeholder="Description" class="field-input field-description"></textarea>
+
+                </div>
+            </div>
+            </div>
+        </div>
+        <div class="flex-button">
+            <button type="button"> annuler</button>
+            <button type="button bg-blue"> créer la categorie</button>
+            <!-- <button type="button"> Enregistrer</button> -->
+    
+        </div>
+    </div>
+    
+</div>
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Templates/articlesTable.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/articlesTable.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Templates/articlesTable.html	(révision 12237)
@@ -0,0 +1,81 @@
+
+
+<div id="news-table">
+	<form id="search-form">
+		<label>Search :</label>
+		<span class="search-news"> 
+			<i class="icon icon-find"></i>
+			<input type="text"  placeholder="Search...">
+
+		</span>
+	</form>
+	<table id="data-table">
+		<thead>
+			<tr>
+				<th data-sort="title">
+				   Title
+				<span class="sort-icon"> 
+					<span class="asc"></span>
+					<span class="desc"></span>
+				</span>
+				</th>
+				<th data-sort="author">Auteur</th>
+				<th data-sort="categorie">Catégorie</th>
+				<th data-sort="datepub">Date de publication</th>
+				<th data-sort="etat">Etat</th>
+				<th></th>
+			</tr>
+		</thead>
+		<tbody>
+			<tr>
+				<td class="title">
+					<span class="img-news"> <img src="https://preprod-ideo32.linkeo.ovh/logo-sm.png" alt="titre"></span>
+					<span>titre</span>
+				</td>
+				<td class="author">
+					Linkeo
+				</td>
+				<td class="categorie"> Catégorie 1</td>
+				<td class="datepub">
+					<span><i class="icon"></i></span>
+					<span></span>
+				</td>
+				<td class="etat">
+					<span class="btn green">Publié</span>
+					<span class="btn orange">Brouillon</span>
+					<span class="btn bleu">Programmé</span>
+				</td>
+				<td>
+					<span > active</span>
+					<span>edit</span>
+					<span>tdanslate</span>
+					<span>delete</span>
+				</td>
+			</tr>
+			<tr>
+				<td class="title">
+					<span class="img-news"> <img src="https://preprod-ideo32.linkeo.ovh/logo-sm.png" alt="titre"></span>
+					<span>titre</span>
+				</td>
+				<td class="author">
+					Linkeo
+				</td>
+				<td class="categorie"> Catégorie 1</td>
+				<td class="datepub">
+					<span><i class="icon"></i></span>
+					<span></span>
+				</td>
+				<td class="etat">
+					<span class="btn orange">Brouillon</span>
+					<span class="btn bleu">Programmé</span>
+				</td>
+				<td>
+					<span> active</span>
+					<span>edit</span>
+					<span>tdanslate</span>
+					<span>delete</span>
+				</td>
+			</tr>
+		</tbody>
+	</table>
+</div>

Property changes on: src/js/JEditor/NewsPanel/Templates/articlesTable.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/NewsPanel/Templates/categorieForm.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/categorieForm.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Templates/categorieForm.html	(révision 12237)
@@ -0,0 +1,42 @@
+<div>
+    <div class="dis-table bordered">
+        <div class="flex_categorie">
+            <div class="upload-categorie">
+                <!-- uploader -->
+            </div>
+            <div class="categorie-info">
+                <div class="flex-betwen">
+                    <div> 
+                        <% if(!edit){ %>  <h3><%=__('createCategorie')%> <% } %> </h3>
+                    </div>
+                    <!-- Choix de la langue -->
+                    <div class="side-bar__lang">
+
+                    </div>
+                </div> 
+            <div>
+                <div>
+                    <label> <%=__('titleCategorie')%> :</label> <br>
+                    <input class="cat-input neutral-input  bold" value="<%=detail.title%>" name="title" type="text" placeholder="Label">
+
+                </div>
+                <div>
+                    <label> <%=__('descriCategorie')%> :</label><br>
+                    <textarea name="description" placeholder="Description" class="cat-input neutral-input  bold" ><%=detail.description%></textarea>
+
+                </div>
+            </div>
+            </div>
+        </div>
+        <div class="flex-button">
+            <button class="button annuler"> <%=__('cancel')%></button>
+            <% if(edit){ %>
+            <button class="button saveCat bg-blue"> <%=__('saveEdit')%></button>
+            <% } else {  %> 
+            <button class="button saveNewCat bg-blue">   <%=__('saveAdd')%> </button>
+
+            <% } %>  
+        </div>
+    </div>
+    
+</div>
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Templates/categorieList.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/categorieList.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Templates/categorieList.html	(révision 12237)
@@ -0,0 +1,16 @@
+<div class="wrapper categorie">
+    <nav>
+        <ul>
+            <%_.each(content,function(categorie){ %>
+                <li class="categorie-nav-list">
+                    <a data-cid="<%= categorie.cid %>" > <%= categorie.lang[currentlang].title %> </a>
+                        <span class="info">
+                            <span class="number"><%= categorie.numberArticlesInCagetory %></span>
+                            <a class="icon icon-add" data-model="<%=categorie.cid%>"></a>
+                        </span>
+                </li>
+                <%
+            });%>
+        </ul>
+    </nav>
+</div>
Index: src/js/JEditor/NewsPanel/Templates/emptyArticle.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/emptyArticle.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Templates/emptyArticle.html	(révision 12237)
@@ -0,0 +1,25 @@
+<div class="nothing-to-display active empty" style="z-index: 1;">
+    <div class="wrapper">
+        <div class="icon">
+            <span class="icon-file"></span>
+        </div>
+        <span class="text-intro">
+            <%= __("emptyNewsLang") %>
+        </span>
+        <span class="how-to">
+            <%= __("howToAdd") %>
+        </span>
+        <div class="button-add">
+            <a>
+                <span class="addCategory">
+                    <span>+</span><%=__("newsAddACategory")%>
+                </span>
+            </a>
+            <a>
+                <span class="addArticle">
+                    <span>+</span><%=__("newsAddArticle")%>
+                </span>
+            </a>
+        </div>
+    </div>
+</div>
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Templates/newsEditor.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/newsEditor.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Templates/newsEditor.html	(révision 12237)
@@ -0,0 +1,35 @@
+ <header class="header">
+    <div class="page">
+        <div class="title">
+            <span class="text page-name">
+                <span class="content" ><%= title %></span> 
+            </span>
+        </div>
+        <div class="config-preview">
+            <div class="btn-group">
+            <div class="btn-group config">
+                <a class="btn dropdown-toggle page-action" href="#">
+                    <span class="icon icon-params"></span>
+                    <span class="label"><%=__('config')%></span>
+                    <span class="caret"></span>
+                </a>
+                <ul class="dropdown-menu">
+                </ul>
+            </div>
+                <%  if( usepreview ){ %> 
+                <button type="button" class="btn btn-default preview" >
+                    <i class="icon icon-find"></i>
+                    <span class="label"><%= __("preview")%></span>
+                </button>
+                <% } %> 
+            </div>
+        </div>
+    </div>
+    <div class="zone">
+
+    </div>
+</header>
+<div class="main">
+    <div id="content-editor">
+    </div>
+</div>
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Views/AddArticleView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/AddArticleView.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Views/AddArticleView.js	(révision 12237)
@@ -0,0 +1,71 @@
+define([
+    "JEditor/Commons/Ancestors/Views/BabblerView",
+    "text!../Templates/categorieForm.html",
+    "JEditor/FilePanel/Models/FileCollection",
+    "JEditor/Commons/Files/Views/FileUploaderView",
+    "JEditor/Commons/Languages/Views/LanguagesDropDown",
+    "i18n!../nls/i18n"
+  ], function (BabblerView, template, FileCollection, FileUploaderView, LanguagesDropDown, translate) {
+    var AddArticleView = BabblerView.extend({
+        initialize: function() {
+            this.template = this.buildTemplate(template, translate);
+            this.langDropDown =  new LanguagesDropDown({
+                collection : this.options.languageList,
+                _default : this.options.language,
+                defaultLabel : 'language'
+            });
+            this.listenTo(this.langDropDown, "selected:choice", this._onLangSelect);
+
+            const uploadParams = {
+                customStockEvent: '_parsestock_image',
+                acceptedTypes: ['image'],
+                acceptedExtensions: ['jpeg', 'jpg', 'png', 'svg'],
+                refusedExtensions: ['bmp'],
+                uploadUrl: __IDEO_UPLOADLOGO_URL__ ? __IDEO_UPLOADLOGO_URL__ : '/admin/resource-logo',
+            };
+            this.fileUploader = new FileUploaderView({
+                currentFile : null,
+                collection: this.fileCollection,
+                uploadParams,
+            });
+           
+            //this.listenTo(this.fileUploader, Events.FileUploaderEvents.UPLOAD, this._onUpload);
+            this.fileCollection = new FileCollection();
+            this.translations = translate.translations;
+        },
+        render: function () {
+           
+            this.$el.html(this.template());
+            this.$('.side-bar__lang').append(this.langDropDown.render().el);
+            this.$('.upload-categorie').append(this.fileUploader.render().el);
+            if (true) {
+                this.$('.upload-categorie .uploader .preview ').hide();
+            }
+            else this.$('.upload-categorie .uploader .preview .imagepreview').after('<span style="font-size:9px;display:block;margin-top:35px;color: #32ace0;text-align:center;">'+this.translations.DefaultMessageUploaderLogo+'</span>');
+
+           
+            return this;
+        },
+        _onUpload: function(file){
+            
+            this.currentFile = new File(file.attributes);
+           
+            this.model.set('Logo', this.currentFile);
+            this.model.save();
+            this.$('upload-categorie .uploader .view').addClass('done');
+            this.$('.upload-categorie .uploader .preview .imagepreview').css({opacity: 1});
+            this.$('.upload-categorie .uploader .preview .imagepreview .progressbar').css({width: 0});
+            if( this.currentFile === "svg"){
+                this.$('.upload-categorie .uploader .preview .imagepreview').css({
+                    backgroundSize: '100%',
+                    backgroundRepeat: 'no-repeat',
+                    backgroundPosition: 'center'
+                });
+            }   
+
+        },
+
+    });
+   
+    return AddArticleView;
+  });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 12237)
@@ -0,0 +1,51 @@
+
+define([
+    "jquery",
+    "underscore",
+    "text!../Templates/articlesTable.html",
+    "text!../Templates/emptyArticle.html",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/ListView",
+    "JEditor/Commons/Utils",
+    "i18n!../nls/i18n",
+    //not in params
+    "jqueryPlugins/dropdown"
+  ], function($,
+    _,
+    articlesList,
+    emptyArticle,
+    Events,
+    ListView,
+    Utils,
+    translate) {
+  
+    var ArticlesCollectionView = ListView.extend({
+      attributes: {
+        class: 'articlesList scroll-container'
+      },
+      events: {
+      },
+      language: null,
+      fadeInEffect: 'fadeIn',
+      fadeOutEffect: 'fadeOut',
+      initialize: function() {
+        this.options.i18n = true;
+        //  this._super();
+        this._template = this.buildTemplate(articlesList, translate);
+        this._emptyTemplate = this.buildTemplate(emptyArticle, translate);
+        this._current = null;
+        //this.listenTo(this.collection,"change",this.render);
+      },
+      render: function() {
+        this._super();
+        //this.scrollables();
+        return this;
+      },
+      show: function(animate) {
+        this._super(animate);
+        this.dom.window.scroll();
+      },
+    });
+  
+    return ArticlesCollectionView;
+  });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Views/CategorieAddView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(révision 12237)
@@ -0,0 +1,233 @@
+define([
+    "jquery",
+    "underscore",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/BabblerView",
+    "text!../Templates/categorieForm.html",
+    "JEditor/FilePanel/Models/FileCollection",
+    "JEditor/Commons/Files/Views/FileUploaderView",
+    "JEditor/Commons/Languages/Views/LanguagesDropDown",
+    "JEditor/NewsPanel/Models/Categorie",
+    "JEditor/NewsPanel/Models/CategorieLang",
+    "i18n!../nls/i18n"
+  ], function ($,
+    _,
+    Events,
+    BabblerView, 
+    template, 
+    FileCollection, 
+    FileUploaderView, 
+    LanguagesDropDown,
+    Categorie,
+    CategorieLang, 
+    translate
+    ) {
+    var CategorieAddView = BabblerView.extend({
+        events: {
+         'click button.saveNewCat' : 'addCategorie',
+         'click button.saveCat' : 'updateCategorie',
+         'click button.annuler' : 'cancel',
+         'click .upload-categorie ' : 'onlyComputer',
+         'change input[name="title"]': 'setTitle',
+         'change textarea[name="description"]': 'setDescription',
+         'click [data-language]': '_onLangClick',
+        },
+        currentCategorieLang : null,
+        initialize: function() {
+            this.template = this.buildTemplate(template, translate);
+            this.edit = false;
+            this.lang = this.options.language;
+            if (this.options.categorie) {
+                this.model = this.options.categorie
+                this.edit = true;
+                this.currentCategorieLang = this.model.getByLanguage(this.options.language);
+            } else{
+                this.model = new Categorie();
+                this.currentCategorieLang = new CategorieLang();
+            } 
+           // this.model.lang = this.options.language.id;
+            this.langDropDown =  new LanguagesDropDown({
+                collection : this.options.languageList,
+                _default : this.options.language,
+                defaultLabel : 'language'
+            });
+
+            this.listenTo(this.langDropDown, Events.ChoiceEvents.SELECT, this._onLanguageChange);
+            this.listenToOnce(this.model, Events.BackboneEvents.SYNC, this.onSave);
+
+            this.fileCollection = new FileCollection();
+            this.translations = translate.translations;
+        },
+        proccessAttribute : function() {
+
+            this.$('.upload-categorie .uploader').addClass('done');
+            this.$('.upload-categorie .uploader .preview .imagepreview').css({opacity: 1});
+            this.$('.upload-categorie .rigth-delete-image').show();
+            if(this.model.getFile() && this.model.getFile().ext === "svg"){    
+                this.$('.upload-categorie .uploader .preview .imagepreview').css({
+                    backgroundSize: '100%',
+                    backgroundRepeat: 'no-repeat'
+                  });
+ 
+            }  
+
+        },
+        onlyComputer : function(e){
+            e.preventDefault();
+            e.stopImmediatePropagation();
+            this.$('.upload-categorie  .uploader .actions-wrapper').removeClass('visible');
+            this.$('.upload-categorie  .uploader .actions-wrapper .actions .computer input:file.upload').trigger('click');
+        },
+        _onLanguageChange: function(view, lang) {
+            this.lang = lang;
+            if (this.model.id) {
+                this.currentCategorieLang = (this.model.getByLanguage(lang))? this.model.getByLanguage(lang) : new CategorieLang();
+               this.render(); 
+            }
+        },
+        setLanguage: function(language) {
+            this.langDropDown.setCurrent(language);
+        },
+        setDescription: function(event) {
+            var $target = $(event.currentTarget);
+            this.currentCategorieLang.description = $target.val();
+        },
+        setTitle: function(event) {
+            var $target = $(event.currentTarget);
+            this.currentCategorieLang.title = $target.val();
+        },
+        render: function () {
+            params = {
+                edit :  this.edit,
+                detail : this.currentCategorieLang 
+            }
+            this.$el.html(this.template(params));
+            this.$('.side-bar__lang').append(this.langDropDown.render().el);
+            
+            // uploader image
+            const uploadParams = {
+                customStockEvent: '_parsestock_image',
+                acceptedTypes: ['image'],
+                acceptedExtensions: ['jpeg', 'jpg', 'png', 'svg'],
+                refusedExtensions: ['bmp'],
+                uploadUrl: __IDEO_UPLOADLOGO_URL__ ? __IDEO_UPLOADLOGO_URL__ : '/admin/resource-logo',
+            };
+            this.fileUploader = new FileUploaderView({
+                currentFile : this.model.getFile(),
+                collection: this.fileCollection,
+                uploadParams,
+            });
+           
+            this.listenTo(this.fileUploader, Events.FileUploaderEvents.UPLOAD, this._onUpload);
+            this.listenTo(this.fileUploader, Events.FileUploaderEvents.TOOBIG, this.onError);
+
+            if (this.model.ressource) {
+                this.fileUploader.currentFile = this.model.getFile();
+            }
+            
+            this.$('.upload-categorie').append(this.fileUploader.el);
+			this.fileUploader.render();
+            this.proccessAttribute(); 
+
+            this.$('.upload-categorie .uploader .preview .imagepreview').after('<span style="font-size:9px;display:block;margin-top:35px;color: #32ace0;text-align:center;">'+this.translations.DefaultMessageUploaderLogo+'</span>');
+
+            return this;
+        },
+        
+        _onUpload: function(file){
+            if (file) {
+                this.currentFile = file;
+                this.model.ressource = file.id;
+                this.model.file = file;
+                this.$('upload-categorie .uploader .view').addClass('done');
+                this.$('.upload-categorie .uploader .preview .imagepreview').css({opacity: 1});
+                this.$('.upload-categorie .uploader .preview .imagepreview .progressbar').css({width: 0});
+                if( this.currentFile === "svg"){
+                    this.$('.upload-categorie .uploader .preview .imagepreview').css({
+                        backgroundSize: '100%',
+                        backgroundRepeat: 'no-repeat',
+                        backgroundPosition: 'center'
+                    });
+                }   
+            }
+
+            
+
+        },
+        onSave: function () {
+            console.log("test");
+            this.stopListening(this.model, Events.BackboneEvents.ERROR, this.onError);
+            this.trigger(Events.LoadEvents.LOAD_SUCCESS, this);
+            $.toast({
+                text: translate("saveSuccesful"), 
+                icon: 'icon-check-circle', 
+                type:'success',
+                appendTo:'#news-editor .main',
+                showHideTransition: 'fade', 
+                hideAfter: 5000, 
+                position: 'top-right', 
+                textAlign: 'left', 
+                allowToastClose:false,
+                loader:false
+            });
+                          
+        },
+        updateCategorie:function(e) {
+            e.preventDefault(); 
+            var title = this.$('input[name="title"]');
+            var description = this.$('textarea[name="description"]');
+            
+            if (title.val() =='') {
+                title.addClass('error');
+                return;
+
+            }
+            if (description.val() =='') {
+                description.addClass('error');
+                return;
+            }
+            this.model.lang[this.lang.id]= this.currentCategorieLang;
+            this.model.save()
+            this.updateColletion(this.model);
+        },
+        updateColletion: function(model){
+            var existingModel = this.collection.findWhere({ id: model.id });
+            if (existingModel){
+                existingModel.set(model.toJSON());
+            }
+            else this.collection.add(model);
+            this.onSave(),
+            this.render();
+        },
+      
+        addCategorie: function(e) {
+            e.preventDefault(); 
+            var title = this.$('input[name="title"]');
+            var description = this.$('textarea[name="description"]');
+            
+            if (title.val() =='') {
+                title.addClass('error');
+                return;
+
+            }
+            if (description.val() =='') {
+                description.addClass('error');
+                return;
+            }
+            this.model.lang[this.lang.id]= this.currentCategorieLang;
+            this.model.save();
+            this.updateColletion(this.model);
+            this.trigger(Events.CategorieAddEvents.CATEGORY_ADD, this);
+        },
+        
+    });
+    Events.extend(
+        {
+            CategorieAddEvents : {
+        
+                CATEGORY_ADD : 'add:newscategory',
+            }
+        });
+   
+    return CategorieAddView;
+  });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Views/CategorieCollectionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/CategorieCollectionView.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Views/CategorieCollectionView.js	(révision 12237)
@@ -0,0 +1,119 @@
+
+define([
+    "jquery",
+    "underscore",
+    "text!../Templates/categorieList.html",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/ListView",
+    "JEditor/Commons/Utils",
+    "i18n!../nls/i18n",
+    //not in params
+    "jqueryPlugins/dropdown"
+  ], function($,
+    _,
+    categorieList,
+    Events,
+    ListView,
+    Utils,
+    translate) {
+  
+    var CategorieCollectionView = ListView.extend({
+      attributes: {
+        class: 'categorieList scroll-container'
+      },
+      events: {
+        'click ul li.categorie-nav-list a[data-cid]': '_onCategorieClick',
+      },
+      language: null,
+      fadeInEffect: 'fadeIn',
+      fadeOutEffect: 'fadeOut',
+      initialize: function() {
+        this.options.i18n = true;
+        this._super();
+        this._template = this.buildTemplate(categorieList, translate);
+        this._emptyTemplate = this.buildTemplate(categorieList, translate);
+        this._current = null;
+        this.languages = this.options.languages.toJSON();
+        this.listenTo(this.collection,"change",this.render);
+        this.listenTo(this.collection,"add",this.render);
+        this.listenTo(this.collection,"remove",this.render);
+
+      },
+      // override masiso
+      render: function() {
+        this.undelegateEvents();
+        var list;
+        if (this.filteredBy !== null)
+            list = this.getFilteredList(this.currentList);
+        else
+            list = this.currentList;
+        if (this.groupedBy !== null) {
+            this.$el.empty();
+            var groups = this.getGroupedList(list);
+            if(groups.content){
+                var byLang = this.options.supportCollection.groupBy('lang');
+                if(byLang[this.lang]){
+                 var groupLPs = byLang[this.lang];
+                 if(groupLPs.length > 0 && localStorage.getItem( 'lpaccess') == "true") groups.lp = groupLPs;
+                }
+            }
+            this.groupOrder= ['content', 'lp','template'];
+            this.renderGroups(groups);
+
+        } else {
+            list = this.sortList(list);
+            if (list.length > 0) {
+                var params = _.extend({}, {content: list, currentlang : this.lang, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected, user: this.app.user}, this.addTemplateParams(this.collection, list));
+                this.$el.html(this._template(params));
+            }
+            else {
+                var params = _.extend({}, {content: list, currentlang : this.lang, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected}, this.addTemplateParams(this.collection, list));
+                this.$el.html(this._emptyTemplate(params));
+            }
+
+        }
+        this.delegateEvents();
+        this.scrollables();
+        return this;
+      },
+      show: function(animate) {
+        this._super(animate);
+        this.dom.window.scroll();
+      },
+      // override
+      getGroupedList: function(list) {
+        var languages = this.languages;
+        if (this.groupedBy !== null) {
+            if (this.groupedBy == 'lang') {
+              var groups = list.groupBy(function(item) {
+                  for (var i = 0; i < languages.length; i++) {
+                      if (item.attributes.lang && item.attributes.lang[languages[i].id]) {
+                          return languages[i].id;
+                      }
+                  }
+              });
+            }
+            else var groups = _.groupBy(list, this.groupedBy);
+            return groups;
+        }
+        else
+            return list;
+    },
+      /**
+       * Change La categorie courante de l'application
+       * @argument {jQuery.Event} event Evenement jQuery
+       * @private
+       */
+      _onCategorieClick: function(event) {
+        event.preventDefault();
+        var $target = $(event.currentTarget);
+        var cid = $target.data('cid');
+        this.$('.edit').removeClass('edit');
+        $target.parent().addClass('edit');
+        this.trigger(Events.ChoiceEvents.SELECT, this, this.collection.get(cid));
+        return false;
+      },
+    });
+  
+    return CategorieCollectionView;
+  });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Views/ConfigCategorieView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ConfigCategorieView.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Views/ConfigCategorieView.js	(révision 12237)
@@ -0,0 +1,129 @@
+define([
+    "jquery",
+	"underscore",
+	"JEditor/Commons/Events",
+	"JEditor/Commons/Ancestors/Views/View",
+    "JEditor/NewsPanel/Models/Categorie",
+    "JEditor/App/Config",
+    "text!JEditor/NewsPanel/Templates/configCategorie.html",
+    "JEditor/Commons/Ancestors/Views/SaveCancelPanel",
+    "i18n!JEditor/NewsPanel/nls/i18n",
+], function ($, _, Event, View, Categorie, Config, tpl, SaveCancelPanel, translate) {
+    
+    /**
+     * 
+     * @type ConfigCategorieView
+     */
+    var ConfigCategorieView = View.extend({
+
+        tagName: 'div',
+        className: 'right-panel-nav',
+        _transitionDelay: 250,
+        rendered: false,
+        _currentCid: null,
+        events: {
+            'click .button.save': '_onSaveClick', 
+            'click .button.cancel': '_onCancelClick', 
+            'click .panel-menu>a': '_onMenuItemClick'
+        },
+        _onSaveClick: function(event) {
+            if (this.save)
+                this.save();
+            this.hide();
+        },
+        _onCancelClick: function() {
+            if (this.cancel)
+                this.cancel();
+            this.hide();
+        },
+        initialize: function () {
+            return this;
+        },
+         /**
+         * affiche cette collection d'option
+         */
+         show: function(singleView) {
+            if (this.rendered === false)
+                this.render();
+            if (!singleView)
+                singleView = this._byCID[this._order[0]];
+            this.dom.window.on('scroll.rightPanelNavView', _.bind(this.updateHeight, this));
+            this.updateHeight();
+            this.showChild(singleView.cid);
+            singleView.updateScrollables();
+            this.rightPanelView.showPanel();
+            this._super();
+        },
+        updateHeight: function() {
+            var offset = this.$el.offset().top;
+            var scrollTop = this.dom.window.scrollTop();
+            var windowHeight = this.dom.window.height();
+            var height = windowHeight - (offset - scrollTop);
+            this.$el.height(height);
+            if (this._currentCid !== null)
+                this._byCID[this._currentCid].updateScrollables();
+        },
+        /**
+         * cache cette collection d'option
+         */
+        hide: function(hidePanel) {
+            hidePanel = hidePanel !== false ? true : false;
+            if (this.rendered === false)
+                this.render();
+            if (hidePanel && this.rightPanelView.isVisible())
+                this.rightPanelView.hidePanel();
+            this.dom.window.off('scroll.rightPanelNavView');
+            this._super();
+        },
+        /**
+         * Rendu de la view
+         */
+        render: function () {
+            this.undelegateEvents();
+            this.$el.addClass('available-items');
+            this.$el.html(this.template());
+            this.fetchVersions();
+            this.scrollables();
+            $ (window).on('scroll', _.bind(this.updateScrollables, this));
+            this.delegateEvents();
+            
+            return this;
+        },
+        save: function() {
+            this.collection.each(function(element, index, list) {
+                element.applyChanges();
+            });
+            this.trigger(Events.OptionCollectionViewEvents.SAVE, this);
+        },
+        /**
+         * annule les changements éfectuées depuis l'ouverture
+         */
+        cancel: function() {
+            this.collection.each(function(element, index, list) {
+                element.revertChanges();
+            });
+            _.each(this.views, function(view) {
+                view.render();
+            });
+            this.trigger(Events.OptionCollectionViewEvents.CANCEL, this);
+        },
+        /**
+         * Toggle le message empty quand il y a des versions ou pas
+         * 
+         * @returns {ConfigCategorieViewL#13.ConfigCategorieViewAnonym$1}
+         */
+        toggleEmptyVersion: function () {
+            // if no version
+            if ( this.collection.length === 0 ) {
+                this.$("#zone-versions-panel .no-version").show();
+            } else {
+                this.$("#zone-versions-panel .no-version").hide();
+            }
+            
+            return this;
+        }
+        
+    });
+    
+    return ConfigCategorieView;
+});
Index: src/js/JEditor/NewsPanel/Views/NewsEditorView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 12237)
@@ -0,0 +1,134 @@
+define([
+    "jquery",
+    "underscore",
+    "text!../Templates/newsEditor.html",
+    "JEditor/Commons/Events",
+    "JEditor/NewsPanel/Views/NewsEditorView",
+    "JEditor/Commons/Ancestors/Views/BabblerView",
+    "JEditor/Commons/Ancestors/Views/LoaderView",
+    "collection!JEditor/Commons/Languages/Models/ContentLanguageList",
+    "JEditor/NewsPanel/Models/ArticlesCollection",
+    "JEditor/NewsPanel/Views/ArticlesCollectionView",
+    "JEditor/NewsPanel/Views/CategorieAddView",
+    "JEditor/PagePanel/Contents/Zones/Views/ZoneToolBox",
+    "JEditor/PagePanel/Views/PagePreview",
+    "i18n!../nls/i18n",
+    //not in params
+    "jqueryPlugins/dropdown",
+    "jqueryPlugins/affix"
+], function($,
+     _, 
+     template, 
+     Events, 
+     NewsEditorView, 
+     BabblerView, 
+     LoaderView, 
+     ContentLanguageList, 
+     ArticlesCollection, 
+     ArticlesCollectionView, 
+     CategorieAddView, 
+     ZoneToolBox,
+     PagePreview,
+     translate
+     ) {
+   
+    var NewsEditorView = BabblerView.extend({
+        events: {
+            'click .btn.preview': 'preview',
+            'click a[data-action]': '_onActionClick',
+            'click .save-version': 'usePreviousVersion'
+        },
+        _loaded: 0,
+        attributes: {
+            id: "page-view"
+        },
+        fadeOutEffect: "fadeOut",
+        fadeInEffect: "fadeIn",
+        language: null,
+        initialize: function() {
+            this.options.i18n = true;
+            this._template = this.buildTemplate(template, translate);
+            this.pagePreview = new PagePreview();
+
+        },
+        renderArticleList :function (){
+            this.articlesListView = new ArticlesCollectionView({
+                collection : this.articles,
+                language : this.currentLang,
+            });
+            this.render();
+            this.$("#content-editor").html(this.articlesListView.render().el);
+        },
+
+        renderAddCategorie :function (){
+            this.addCategorieView = new CategorieAddView({
+                languageList : this.options.languages,
+                language : this.options.currentLang,
+                collection: this.options.categorieCollection
+            });
+            this.listenTo(this.addCategorieView, Events.CategorieAddEvents.CATEGORY_ADD, this.renderCategorieView);
+            this.options.title = 'Categorie';
+            this.options.usepreview = true;
+            this.render();
+            this.$("#content-editor").html(this.addCategorieView.render().el);
+         },
+
+         renderCategorieView :function (){
+            this.addCategorieView = new CategorieAddView({
+                languageList : this.options.languages,
+                language : this.options.currentLang,
+                categorie : this.model,
+                collection: this.options.categorieCollection
+            });
+            this.render();
+            this.$("#content-editor").prepend(this.addCategorieView.render().el);
+             // this.articles = ArticlesCollection.getInstance();
+             this.articlesListView = new ArticlesCollectionView({
+                collection : this.articles,
+                language : this.currentLang,
+            });
+            this.$("#content-editor").append(this.articlesListView.render().el);
+         },
+         renderArticlePage: function (){
+            this.options.usepreview = true;
+            this.render();
+            
+            if (this.zoneToolbox && true) {
+                this.$('.zone').prepend(this.zoneToolbox.el);
+                this.zoneToolbox.render();
+            }
+            if (this.sectionCollectionView) {
+                this.sectionCollectionView.setElement(this.$('#content-editor'));
+                this.sectionCollectionView.render();
+            }
+         },
+         remove: function () {
+            if (this.zoneToolbox)
+                this.zoneToolbox.remove();
+            if (this.pagePreview)
+                this.pagePreview.remove();
+            if (this.sectionCollectionView)
+                this.sectionCollectionView.remove();
+            BabblerView.prototype.remove.apply(this, arguments);
+        },
+        render: function() {
+            this.undelegateEvents();
+            this.$el.empty();
+           
+            this.$el.html(this._template({
+                 title : this.options.title,
+                 usepreview : this.options.usepreview || false
+            }));
+            this.$(".message").hide();
+            this.$('.zone-selector>.dropdown-toggle, .page-action').dropdown();
+            this.delegateEvents();
+
+            return this;
+        },
+        show: function(animate) {
+        this._super(animate);
+        this.dom.window.scroll();
+        },
+    });
+    return NewsEditorView;
+});
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12236)
+++ src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 12237)
@@ -6,6 +6,16 @@
 	"editedVersion" : "Editez une version",
 	"allArticles": "Tous les articles",
 	"config" : "Réglages",
-	"newsAddArticle" : "Ajouter un article"
-
+	"newsAddArticle" : "Ajouter un article",
+	"emptyNewsLang": "Vous n'avez encore aucune article",
+	"howToAdd": "Commencez par créer une Catégorie ou un article",
+	"createCategorie" : "Creer une Catégorie",
+	"titleCategorie":"Titre de la catégorie",
+	"descriCategorie":"Description de la catégorie",
+	"cancel":"Annuler",
+	"saveEdit" :"Enregistrer",
+	"saveAdd" : "Créer la catégorie",
+	"preview": "Aper\u00e7u",
+	"quitWithoutSaving": "Vous n'avez pas enregistr\u00e9 les modifications apport\u00e9es \u00e0 la catégorie, voulez-vous les enregistrer ?",
+	"unsavedChanges": "sauvegarder les changements",
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 12236)
+++ src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 12237)
@@ -6,6 +6,16 @@
 	"editedVersion" : "Editez une version",
 	"allArticles": "Tous les articles",
 	"config" : "Réglages",
-	"newsAddArticle" : "Ajouter un article"
-
+	"newsAddArticle" : "Ajouter un article",
+	"emptyNewsLang": "Vous n'avez encore aucune article",
+	"howToAdd": "Commencez par créer une Catégorie ou un article",
+	"createCategorie" : "Creer une Catégorie",
+	"titleCategorie":"Titre de la catégorie",
+	"descriCategorie":"Description de la catégorie",
+	"cancel":"Annuler",
+	"saveEdit" :"Enregistrer",
+	"saveAdd" : "Créer la catégorie",
+	"preview": "Aper\u00e7u",
+	"quitWithoutSaving": "Vous n'avez pas enregistr\u00e9 les modifications apport\u00e9es \u00e0 la catégorie, voulez-vous les enregistrer ?",
+    "unsavedChanges": "sauvegarder les changements",
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12236)
+++ src/js/JEditor/NewsPanel/nls/i18n.js	(révision 12237)
@@ -7,8 +7,18 @@
         "editedVersion" : "Editez une version",
         "allArticles": "Tous les articles",	
         "config" : "Réglages",
-        "newsAddArticle" : "Ajouter un article"
-
+        "newsAddArticle" : "Ajouter un article",
+        "emptyNewsLang": "Vous n'avez encore aucune article",
+        "howToAdd": "Commencez par créer une Catégorie ou un article",
+        "createCategorie" : "Creer une Catégorie",
+        "titleCategorie":"Titre de la catégorie",
+        "descriCategorie":"Description de la catégorie",
+        "cancel":"Annuler",
+        "saveEdit" :"Enregistrer",
+        "saveAdd" : "Créer la catégorie",
+        "preview": "Aper\u00e7u",
+        "quitWithoutSaving": "Vous n'avez pas enregistr\u00e9 les modifications apport\u00e9es \u00e0 la catégorie, voulez-vous les enregistrer ?",
+        "unsavedChanges": "sauvegarder les changements",
     },
     "fr-fr": true,
     "fr-ca": true
