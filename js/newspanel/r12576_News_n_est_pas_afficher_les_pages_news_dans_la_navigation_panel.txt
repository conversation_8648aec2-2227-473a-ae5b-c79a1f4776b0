Revision: r12576
Date: 2024-07-10 16:08:31 +0300 (lrb 10 Jol 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: n'est pas afficher les pages news dans la navigation panel

## Files changed

## Full metadata
------------------------------------------------------------------------
r12576 | srazanandralisoa | 2024-07-10 16:08:31 +0300 (lrb 10 Jol 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NavigationPanel/Views/DraggableMenuItemView.js

News: n'est pas afficher les pages news dans la navigation panel
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NavigationPanel/Views/DraggableMenuItemView.js
===================================================================
--- src/js/JEditor/NavigationPanel/Views/DraggableMenuItemView.js	(révision 12575)
+++ src/js/JEditor/NavigationPanel/Views/DraggableMenuItemView.js	(révision 12576)
@@ -103,7 +103,10 @@
             return element.getIcon();
         };
         var view = new DraggableMenuItemView(opts);
-        view.exclude(opts.collection.where({type: 'template'}));
+        var excludedItems = opts.collection.filter(function(item) {
+            return item.get('type') === 'template' || item.get('type') === 'news';
+        });
+        view.exclude(excludedItems);
         return view;
     };
     DraggableMenuItemView.fromMenus = function(opts) {
