Revision: r12678
Date: 2024-08-01 14:22:14 +0300 (lkm 01 Aog 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: Update active page apres publication article

## Files changed

## Full metadata
------------------------------------------------------------------------
r12678 | sraz<PERSON><PERSON><PERSON>oa | 2024-08-01 14:22:14 +0300 (lkm 01 Aog 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/PublishConfigView.js

News: Update active page apres publication article
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Views/PublishConfigView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/PublishConfigView.js	(révision 12677)
+++ src/js/JEditor/NewsPanel/Views/PublishConfigView.js	(révision 12678)
@@ -151,6 +151,10 @@
         },
         _onSaveClick: function(event) {
             if (this.save)
+                this.listenToOnce(this.model, Events.BackboneEvents.SYNC, _.bind(function(){
+                    if (!this._programmed)
+                    this.model.pageModel.attributes.actif = true;
+                }, this))
                 this.save();
             this.hide();
         },
