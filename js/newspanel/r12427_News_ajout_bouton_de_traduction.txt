Revision: r12427
Date: 2024-06-18 12:08:00 +0300 (tlt 18 Jon 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: ajout bouton de traduction

## Files changed

## Full metadata
------------------------------------------------------------------------
r12427 | sraz<PERSON><PERSON><PERSON><PERSON> | 2024-06-18 12:08:00 +0300 (tlt 18 Jon 2024) | 1 ligne
Chemins modifiés :
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/traduiceButton.html
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Utils
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Utils/NewsUtils.js
   A /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/TraduiceButton.js

News: ajout bouton de traduction
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Utils/NewsUtils.js
===================================================================
--- src/js/JEditor/NewsPanel/Utils/NewsUtils.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Utils/NewsUtils.js	(révision 12427)
@@ -0,0 +1,69 @@
+define([
+    'underscore',
+    'backbone',
+    'JEditor/Commons/Events',
+    "JEditor/PagePanel/Contents/Zones/Models/ZoneCollection"
+  ],
+  function(_, Backbone, Events, ZoneCollection) {
+    var NewsUtils = function() {
+
+    };
+    NewsUtils.prototype = {
+      getCloneName: function(model) {
+        if (!model.collection)
+          throw new Error("the Article should be part of a collection");
+        var title;
+        var duplicatedPattern = /\(([0-9]*)\)$/g;
+        if (!model.title.match(duplicatedPattern)) {
+          var count = 1;
+          title = model.title + ' (' + count + ')';
+          while (model.collection.findWhere({
+              title: title
+            })) {
+            count++;
+            title = title = model.title + ' (' + count + ')';
+          }
+        } else
+          title = model.title + ' (1)';
+        return title;
+      },
+      cloneNews: function(Article, lang, cloneName) {
+        if (!Article.collection)
+          throw new Error("the Article should be part of a collection");
+        var ArticleCollection = Article.collection;
+        var Article = ArticleCollection.get(Article);
+        var clone = Article.clone();
+    
+        var onError = function(model, resp, options) {
+          this.trigger(Events.NewsUtilsEvents.CLONING_ERROR, model, resp, options);
+        }
+        var onDone = function(clone) {
+          this.stopListening(clone, Events.BackboneEvents.ERROR);
+          if (ArticleCollection) {
+            ArticleCollection.add(clone);
+            this.trigger(Events.NewsUtilsEvents.CLONING_DONE, clone);
+          }    
+        };
+
+        this.listenToOnce(clone, Events.BackboneEvents.SYNC, _.bind(onDone, this))
+            .listenToOnce(clone, Events.BackboneEvents.ERROR, _.bind(onError, this));
+
+        if (!cloneName)
+        cloneName = this.getCloneName(Article);
+        clone.unset('id').unset('programmingDate').unset('publicationDate').unset('metaOpengraph').unset('page');
+        //delete clone.id, clone.programmingDate, clone.publicationDate, clone.metaOpengraph, clone.page;
+        clone.lang = lang;
+        clone.title = cloneName;
+        clone.save();
+        
+      }
+    };
+    Events.extend({
+      NewsUtilsEvents: {
+        CLONING_DONE: "ArticleUtils_cloning_done",
+        CLONING_ERROR: "ArticleUtils_cloning_error"
+      }
+    });
+    _.extend(NewsUtils.prototype, Backbone.Events);
+    return new NewsUtils();
+  });
Index: src/js/JEditor/NewsPanel/Templates/traduiceButton.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/traduiceButton.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Templates/traduiceButton.html	(révision 12427)
@@ -0,0 +1,17 @@
+
+
+<div>
+    <button class="btn dropdown-toggle" data-toggle="dropdown"><span class="icon icon-layout-ok"></span></span> <%= __("traduice")%></button>
+    <ul class="dropdown-menu">
+        <% languages.each(function(lang){ %>
+        <li>
+            <a data-action="traduiceNews" data-args="<%=lang.id%>" class="<%=lang.existe?'disabled':'enabled'%>" >
+                <%= lang.name+' ('+lang.country+')'%>  
+                <% if (lang.existe){ %>
+                    <span class="icon icon-check-circle"></span> 
+                <% } %>
+            </a>
+        </li>
+        <% }); %>
+    </ul>
+</div> 

Property changes on: src/js/JEditor/NewsPanel/Templates/traduiceButton.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/NewsPanel/Views/TraduiceButton.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/TraduiceButton.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Views/TraduiceButton.js	(révision 12427)
@@ -0,0 +1,147 @@
+define([
+    "jquery",
+    "underscore",
+    "text!../Templates/traduiceButton.html",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/BabblerView",
+    "JEditor/NewsPanel/Utils/NewsUtils",
+    "JEditor/NewsPanel/Models/ArticlesCollection",
+    "JEditor/Commons/Ancestors/Views/LoaderView"            ,
+    "i18n!../nls/i18n",
+    // not in params
+    "jqueryPlugins/toaster",
+    "jqueryPlugins/affix"
+], function ($,_,
+        traduiceButton,
+        Events,
+        BabblerView,
+        NewsUtils,
+        ArticlesCollection,
+        LoaderView,
+        translate) {
+    /**
+     * Vue contenant le bouton de traduction d'une article
+     * @class TraduiceButton
+     * @extends BabblerView
+     * @extends LoaderView
+     */
+    var TraduiceButton = BabblerView.extend(
+            /**
+             * @lends TraduiceButton.prototype
+             */
+                    {
+                        events: { 'click [data-action="traduiceNews"]': 'onActionClick'},
+                        attributes: {
+                            id: 'save-zone',
+                            class: 'btn btn-default button save',
+                            type: "button"
+                        },
+                        langs: null,
+                        tagName: "button",
+                        /**
+                         * initialize le bouton
+                         */
+                        initialize: function () {
+                            this._super();
+                            this._template = this.buildTemplate(traduiceButton, translate);
+                            this.languages = this.options.languageList;
+                            this.articles = ArticlesCollection.getInstance();
+                        },
+                        /*----------------------------
+                         * test la hauteur de fenètre
+                         * return true si mise en page 'petite hauteur'
+                         *----------------------------*/
+                        checkAffixTop:function(){
+                            return (document.documentElement.clientHeight < 866 ? 90 : 230);
+                        },
+                        /**
+                         * crée le rendu de la vue
+                         */
+                        render: function () {
+                            var currentArticles = this.articles.where({news: this.model.news}); // retourne une collection
+                            this.langs = this.languages.map(function(lang) {
+                                lang.existe = currentArticles.some(function(article) {
+                                    return article.get('lang') === lang.id;
+                                });
+                                return lang;
+                            });
+                            this.undelegateEvents();
+                            //end magic don't touch
+                            this.$el.empty();
+                            this.$el.html(this._template({languages: new Backbone.Collection(this.langs)}));
+                            this.dom[this.cid].dropdown = this.$('.dropdown-toggle').dropdown();
+                            //magic don't touch
+                            this.delegateEvents();
+                            //end magic don't touch
+                            return this;
+                        },
+
+                        onActionClick: function(event) {
+                            var $target = $(event.currentTarget);
+                            var action = $target.attr('data-action');
+                            var params = $target.attr('data-args');
+                            var className = $target.attr('class');
+                            if (className == 'disabled') {
+                                this.error({
+                                    title: translate("saveAction"),
+                                    message: translate("article dans la langue existe déjà")
+                                });
+                            }
+                            else if (this[action] && this[action].call) {
+                              this[action].call(this, params);
+                            }
+                        },
+                        traduiceNews: function (lang) {
+                            if (this.app.user.can('create_page')) {
+                                if (!this.model.getCategoryModel().lang[lang]) {
+                                    this.error({
+                                        title: translate("saveAction"),
+                                        message: translate("category pour la langue non trouvé")
+                                    });
+                                    return false;
+                                }
+                                this.setLoading(true);
+                                this.listenToOnce(NewsUtils, Events.NewsUtilsEvents.CLONING_DONE, this.onCloningComplete)
+                                        .listenToOnce(NewsUtils, Events.NewsUtilsEvents.CLONING_ERROR, this.onCloningError);
+                        
+                                try {
+                                    NewsUtils.cloneNews(this.model, lang);
+                                } catch (e) {
+                                    this.onCloningError();
+                                } 
+                               
+                            }
+                            else
+                                this.onCloningError();
+                           
+                            return false;
+                        },
+                        onCloningComplete: function (clone) {
+                            this.setLoading(false);
+                            $.toast({
+                                text: translate("translationSuccessfull"), 
+                                icon: 'icon-check-circle', 
+                                type:'success',
+                                appendTo:'#news-editor .zone .zone-toolbox',
+                                showHideTransition: 'fade', 
+                                hideAfter: 5000, 
+                                position: 'top-right', 
+                                textAlign: 'left', 
+                                allowToastClose:false,
+                                loader:false
+                            });
+                            this.render();
+                            return false;
+                        },
+                        onCloningError: function (model, resp, options) {
+                            this.setLoading(false);
+                            this.error({
+                                message: translate("cloningError"),
+                                title: translate("error")
+                            });
+                            this.render();
+                            return false;
+                        },
+                    });
+            return TraduiceButton;
+        });

Property changes on: src/js/JEditor/NewsPanel/Views/TraduiceButton.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
