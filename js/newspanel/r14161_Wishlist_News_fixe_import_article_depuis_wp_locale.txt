Revision: r14161
Date: 2025-04-25 15:06:47 +0300 (zom 25 Apr 2025) 
Author: frahajanirina 

## Commit message
Wishlist:News: fixe import article depuis wp locale

## Files changed

## Full metadata
------------------------------------------------------------------------
r14161 | frahajanirina | 2025-04-25 15:06:47 +0300 (zom 25 Apr 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/UploadArticleView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/i18n.js

Wishlist:News: fixe import article depuis wp locale
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Views/UploadArticleView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/UploadArticleView.js	(révision 14160)
+++ src/js/JEditor/NewsPanel/Views/UploadArticleView.js	(révision 14161)
@@ -65,18 +65,33 @@
                 processData: false,
                 success: function (response) {
                     self.setLoading(false);
-                    $.toast({
-                        text: translate("uploadSuccess"), 
-                        icon: 'icon-check-circle', 
-                        type:'success',
-                        appendTo:'#news-view .zone',
-                        showHideTransition: 'fade', 
-                        hideAfter: 5000, 
-                        position: {top :50,right:50},  
-                        textAlign: 'left', 
-                        allowToastClose:false,
-                        loader:false
-                    });
+                    if (response.isUrlImgInaccessible) {
+                        $.toast({
+                            text: translate("uploadError"), 
+                            icon: 'icon-check-circle', 
+                            type:'error',
+                            appendTo:'#news-view .zone',
+                            showHideTransition: 'fade', 
+                            hideAfter: 5000, 
+                            position: {top :50,right:50},  
+                            textAlign: 'left', 
+                            allowToastClose:false,
+                            loader:false
+                        });
+                    } else {
+                        $.toast({
+                            text: translate("uploadSuccess"), 
+                            icon: 'icon-check-circle', 
+                            type:'success',
+                            appendTo:'#news-view .zone',
+                            showHideTransition: 'fade', 
+                            hideAfter: 5000, 
+                            position: {top :50,right:50},  
+                            textAlign: 'left', 
+                            allowToastClose:false,
+                            loader:false
+                        });   
+                    }
                     Backbone.trigger('upload:success', response);
                 },
                 error: function (xhr) {
Index: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 14160)
+++ src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 14161)
@@ -179,5 +179,6 @@
 	"error": "Erreur",
 	"fileUnauthorised": "Le ficher n\'a pas été importé. Ce type de fichier n\'est pas autorisé.",
 	"uploadLink": "Importer les articles",
-	"uploadSuccess": "L'article a été importé avec succès"
+	"uploadSuccess": "L'article a été importé avec succès",
+	"uploadError": "Erreur lors du téléchargement de l'image de l'article"
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 14160)
+++ src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 14161)
@@ -179,5 +179,6 @@
 	"error": "Erreur",
 	"fileUnauthorised": "Le ficher n\'a pas été importé. Ce type de fichier n\'est pas autorisé.",
 	"uploadLink": "Importer les articles",
-	"uploadSuccess": "L'article a été importé avec succès"
+	"uploadSuccess": "L'article a été importé avec succès",
+	"uploadError": "Erreur lors du téléchargement de l'image de l'article"
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/i18n.js	(révision 14160)
+++ src/js/JEditor/NewsPanel/nls/i18n.js	(révision 14161)
@@ -176,7 +176,8 @@
         "error": "Error",
         "fileUnauthorised": "The file was not imported. This file type is not allowed.",
         "uploadLink": "Import articles",
-        "uploadSuccess": "Article has been successfully imported"
+        "uploadSuccess": "Article has been successfully imported",
+        "uploadError": "Failed to upload the article image"
     },
     "fr-fr": true,
     "fr-ca": true
