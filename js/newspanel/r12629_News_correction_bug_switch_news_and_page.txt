Revision: r12629
Date: 2024-07-26 09:27:22 +0300 (zom 26 Jol 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: correction bug switch news and page

## Files changed

## Full metadata
------------------------------------------------------------------------
r12629 | srazana<PERSON><PERSON>oa | 2024-07-26 09:27:22 +0300 (zom 26 Jol 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/AvailableView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js

News: correction bug switch news and page
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12628)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12629)
@@ -21,7 +21,7 @@
     "JEditor/NewsPanel/Views/AvailableView",
     "JEditor/NewsPanel/Views/PublishConfigView",
     "JEditor/NewsPanel/Articles/Versions/Views/VersionsCollectionView",
-    "JEditor/PagePanel/Contents/Blocks/Blocks",
+    "JEditor/NewsPanel/Articles/Blocks",
     "i18n!./nls/i18n",
     "jqueryPlugins/affix",
     "ckeditor"
@@ -201,7 +201,7 @@
                                             article.getCategoryModel();
                                         }
                                     });
-                                    this.currentArticle = null;
+                                    if (this.loaded) this.currentArticle = null;
                                 },
                                 onArticleAdd: function(article) {
                                     this.updateCategoryCounter(article, 1);
Index: src/js/JEditor/NewsPanel/Views/AvailableView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/AvailableView.js	(révision 12628)
+++ src/js/JEditor/NewsPanel/Views/AvailableView.js	(révision 12629)
@@ -20,6 +20,11 @@
         Utils,
         translate
         ) {
+            if (!Blocks) {
+                console.error("Le module 'Blocks' est undefined. Vérifie le chemin et la définition du module.");
+            } else {
+                console.log("Module 'Blocks' chargé avec succès.");
+            }
     var /**
      * La vue des blocs disponibles
      * @class AvailableView
Index: src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js	(révision 12628)
+++ src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js	(révision 12629)
@@ -20,6 +20,11 @@
         Utils,
         translate
         ) {
+            if (!Blocks) {
+                console.error("Le module 'Blocks' est undefined. Vérifie le chemin et la définition du module.");
+            } else {
+                console.log("Module 'Blocks' chargé avec succès.");
+            }
     var /**
      * La vue des blocs disponibles
      * @class AvailableView
