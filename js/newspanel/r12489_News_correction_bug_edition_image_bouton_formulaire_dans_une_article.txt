Revision: r12489
Date: 2024-06-25 09:55:16 +0300 (tlt 25 Jon 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
News: correction bug edition image, bouton, formulaire dans une article

## Files changed

## Full metadata
------------------------------------------------------------------------
r12489 | srazana<PERSON>lisoa | 2024-06-25 09:55:16 +0300 (tlt 25 Jon 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/Commons/Pages/Views/PageSelectorWrapper.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormTemplateList.js

News: correction bug edition image, bouton, formulaire dans une article
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Pages/Views/PageSelectorWrapper.js
===================================================================
--- src/js/JEditor/Commons/Pages/Views/PageSelectorWrapper.js	(révision 12488)
+++ src/js/JEditor/Commons/Pages/Views/PageSelectorWrapper.js	(révision 12489)
@@ -26,14 +26,20 @@
             this._pagesSupportByLang = this.supportpagecollection.groupBy('lang');
             this.role=this.app.user.role;
             this.childViews = {};
-           if (!this.pagePanel) {
+           if (this.filePanel) {
                panel = this.filePanel;
                this.currentLang = panel.currentLang;
                // this.childViews.pageSelector = new PageSelector({collection: this.collection, i18n: true, lang :this.app.currentPanel.currentLang.id});
-             }else {panel = this.pagePanel;
+             }else if(this.pagePanel){
+                panel = this.pagePanel;
                 this.currentLang = panel.currentLang;
                // this.childViews.pageSelector = new PageSelector({collection: this.collection, i18n: true, lang: this.app.currentPanel.currentLang.id});
             }
+            else{
+                panel = this.app.currentPanel;
+                this.currentLang = panel.currentLang;
+            }
+
             this.childViews.pageSelector = new PageSelector({collection: this.collection, i18n: true, lang: this.app.currentPanel.currentLang.id,supportpagecollection:this.supportpagecollection});
             this.childViews.langDropDown = new LanguagesDropDown({collection: panel.languages, _default: this.currentLang});
             this.listenTo(this.childViews.langDropDown, Events.ChoiceEvents.SELECT, this._setLang);
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormTemplateList.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormTemplateList.js	(révision 12488)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormTemplateList.js	(révision 12489)
@@ -6,7 +6,7 @@
         },
         initialize:function () {
             View.prototype.initialize.call(this);
-            this.lang = this.app.currentPanel.currentPage.lang;
+            this.lang = (this.app.currentPanel.currentPage)? this.app.currentPanel.currentPage.lang : this.app.currentPanel.currentArticle.lang;
             this.template=this.buildTemplate(tpl,i18n);
         },
         render:function () {
