Revision: r12371
Date: 2024-06-06 09:00:02 +0300 (lkm 06 Jon 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
NEWS: ajout configuration et choix layout (Partie JS)

## Files changed

## Full metadata
------------------------------------------------------------------------
r12371 | srazana<PERSON><PERSON>oa | 2024-06-06 09:00:02 +0300 (lkm 06 Jon 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/assets/ACLs/admin.json
   M /branches/ideo3_v2/integration_news/assets/ACLs/lpadmin.json
   M /branches/ideo3_v2/integration_news/assets/ACLs/root.json
   M /branches/ideo3_v2/integration_news/assets/ACLs/superadmin.json
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/NewsConfig.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/globalConfig.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Templates/publishConfig.html
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/GlobalConfigView.js
   M /branches/ideo3_v2/integration_news/src/less/imports/news_panel/main.less

NEWS: ajout configuration et choix layout (Partie JS)
------------------------------------------------------------------------

## Diff
Index: assets/ACLs/admin.json
===================================================================
--- assets/ACLs/admin.json	(révision 12370)
+++ assets/ACLs/admin.json	(révision 12371)
@@ -206,5 +206,9 @@
  "access_panel_news": {
     "value": true,
     "comparison": "==="
- }
+ },
+ "change_news_layout": {
+   "value": false,
+   "comparison": null
+  }
 }
Index: assets/ACLs/lpadmin.json
===================================================================
--- assets/ACLs/lpadmin.json	(révision 12370)
+++ assets/ACLs/lpadmin.json	(révision 12371)
@@ -206,6 +206,10 @@
      "access_panel_news": {
         "value": true,
         "comparison": "==="
+     },
+     "change_news_layout": {
+       "value": false,
+       "comparison": null
      }
    }
    
\ No newline at end of file
Index: assets/ACLs/root.json
===================================================================
--- assets/ACLs/root.json	(révision 12370)
+++ assets/ACLs/root.json	(révision 12371)
@@ -206,5 +206,9 @@
      "access_panel_news": {
         "value": true,
         "comparison": null
+     },
+     "change_news_layout": {
+       "value": true,
+       "comparison": null
      }
 }
Index: assets/ACLs/superadmin.json
===================================================================
--- assets/ACLs/superadmin.json	(révision 12370)
+++ assets/ACLs/superadmin.json	(révision 12371)
@@ -206,5 +206,9 @@
  "access_panel_news": {
     "value": true,
     "comparison": "==="
- }
+ },
+ "change_news_layout": {
+   "value": false,
+   "comparison": null
+  }
 }
Index: src/js/JEditor/NewsPanel/Models/NewsConfig.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/NewsConfig.js	(révision 12370)
+++ src/js/JEditor/NewsPanel/Models/NewsConfig.js	(révision 12371)
@@ -15,8 +15,28 @@
                         newsNbArticle: 3,
                         layout: null
                     },
+                    constructor: function () {
+                        if (arguments.callee.caller !== NewsConfig.getInstance)
+                            throw new TypeError("NewsConfig est un singleton et ne peut être instancié");
+                        Model.apply(this, arguments);
+                    },
     
                 });
             NewsConfig.SetAttributes(['newsStyle', 'newsFormat', 'newsStyleAff', 'newsNbArticle', 'layout']);
+            /**
+             * @static
+             * @private
+             */
+            NewsConfig.instance = null;
+            /**
+             * pour récupérer l'instance de la classe
+             * @returns {Config} l'instance de la classe
+             */
+            NewsConfig.getInstance = function () {
+                if (this.instance === null){
+                    this.instance = new NewsConfig();
+                }
+                return this.instance;
+            }
         return NewsConfig;
     });
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12370)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12371)
@@ -12,6 +12,7 @@
     "./Views/GlobalConfigView",
     "./Models/CategorieCollection",
     "./Models/ArticlesCollection",
+    "./Models/NewsConfig",
     "JEditor/Commons/Pages/Models/PageCollection",
     "./Models/Article",
     "JEditor/App/Views/RightPanelView",
@@ -34,6 +35,7 @@
             GlobalConfigView,
             CategorieCollection, 
             ArticlesCollection,
+            NewsConfig,
             PageCollection,
             Article,
             RightPanelView,
@@ -74,17 +76,20 @@
                                     this.categories = CategorieCollection.getInstance();
                                     this.articles =  ArticlesCollection.getInstance();
                                     this.pageCollection = PageCollection.getInstance();
-
+                                    this.newsConfig = NewsConfig.getInstance();
                                     function onLoaded() {
                                         loaded++;
-                                        if (loaded === 2) {
+                                        if (loaded === 3) {
                                         this.loadingEnd();
                                         }
                                     }
                                     this.listenToOnce(this.categories, "sync", onLoaded);
                                     this.listenToOnce(this.articles, "sync", onLoaded);
+                                    this.listenToOnce(this.newsConfig, "sync", onLoaded);
+
                                     this.categories.fetch();
                                     this.articles.fetch();
+                                    this.newsConfig.fetch()
                                 
                                     this.on(Events.NewsPanelEvents.CATEGORIE_CHANGE, this._onCategorieChange); 
                                     this.on(Events.NewsPanelEvents.ARTICLE_CHANGE, this._onArticleChange); 
@@ -255,6 +260,9 @@
                                     
                                     this.listenTo(this.childViews.categorieList, 'render', this._scrollbar);
                                     this.listenTo(this.childViews.newsEditorView, Events.NewsEditorViewEvents.SHOWRIGHTPANEL, this.showGlobalConfigView);
+                                    this.listenTo(this.childViews.globalConfigView, Events.GlobalConfigViewEvents.CANCEL, this.hidesGlobalConfigView);
+                                    this.listenTo(this.childViews.globalConfigView, Events.GlobalConfigViewEvents.SAVE, this.hidesGlobalConfigView);
+                                    
                                     this.render();
                                 },
                                 /**
@@ -316,6 +324,10 @@
                                     this.rightPanelView.showPanel();
                                     return false;
                                 },
+                                hidesGlobalConfigView : function() {
+                                    this.rightPanelView.hideContent(this.childViews.globalConfigView);
+                                    this.rightPanelView.hidePanel();
+                                },
                                 /**
                                 * rendu de la liste des categories (gauche)
                                 */
Index: src/js/JEditor/NewsPanel/Templates/globalConfig.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/globalConfig.html	(révision 12370)
+++ src/js/JEditor/NewsPanel/Templates/globalConfig.html	(révision 12371)
@@ -1,168 +1,174 @@
-<div id="news-configuration" class="news-configuration scroll-container">
+<div id="news-configuration " class="news-rigthpanel scroll-container">
     <header class="panel-head">
         <span class="icon icon-params"></span>
         <h1 class="panel-name"><%=__('config')%></h1>
     </header>
-    <div class="panel-content active">
-        <div class="panel-content-intro">
-            <%=__("configDesc")%>
+    <div class="gallery-template-option galleryStyle panel-content">
+
+        <div class="panel-content active">
+            <div class="panel-content-intro">
+                <%=__("configDesc")%>
+            </div>
         </div>
-    </div>
-    <div class="news-option-margin news-template">
-        <article class="panel-option">
-            <header>
-                <h3 class="option-name"><%=__("newsTemplate")%></h3>
-                <p class="panel-content-legend"><%=__("newsTemplateLegend")%></p>
-            </header>
-            <div class="option-content">
-                <div class="template-list">
-                        
+        <% if(canchangeLayout){ %>
+        <div class="news-option-margin news-template">
+            <article class="panel-option">
+                <header>
+                    <h3 class="option-name"><%=__("newsTemplate")%></h3>
+                    <p class="panel-content-legend"><%=__("newsTemplateLegend")%></p>
+                </header>
+                <div class="option-content">
+                    <div class="template-list">
+                            
+                    </div>
                 </div>
-            </div>
-        </article>
-    </div>
-    <div class="panel-option-container animated  mr15">
-        <article class="panel-option">
-            <header>
-                <h3 class="option-name"></span> <%=__("styleDeNews")%></h3>
-                <p class="panel-content-legend"><%=__("styleDeNewsLegend")%></p>
-            </header>
-            <div>
-                <div class="category-content radio-transformed styleDeNews">
-                    <% var _id= _.uniqueId('newsStyle'); %>
-                    <div><span class="effect-radio <%=(newsStyle===0)?'active':''%>" id="<%=_id %>" data-value="0" data-helper="masonry"><span class="helper"><span class="help"><%=__("masonryLegend")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-photocss-column"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
-                    <% var _id= _.uniqueId('newsStyle'); %>
-                    <div><span class="effect-radio <%=(newsStyle===1)?'active':''%>" id="<%=_id %>" data-value="1" data-helper="grid"><span class="helper"><span class="help"><%=__("gridLegend")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-photogrid-block"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
-                    <% var _id= _.uniqueId('newsStyle'); %>
-                    <div><span class="effect-radio <%=(newsStyle===2)?'active':''%>" id="<%=_id %>" data-value="2" data-helper="list"><span class="helper"><span class="help"><%=__("listLegend")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-nav-menu"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
+            </article>
+        </div>
+        <% } %>
+        <div class="panel-option-container animated  mr15">
+            <article class="panel-option">
+                <header>
+                    <h3 class="option-name"></span> <%=__("styleDeNews")%></h3>
+                    <p class="panel-content-legend"><%=__("styleDeNewsLegend")%></p>
+                </header>
+                <div>
+                    <div class="category-content radio-transformed styleDeNews">
+                        <% var _id= _.uniqueId('newsStyle'); %>
+                        <div><span class="effect-radio <%=(newsStyle==0)?'active':''%>" id="<%=_id %>" data-value="0" data-helper="masonry"><span class="helper"><span class="help"><%=__("masonryLegend")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-photocss-column"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
+                        <% var _id= _.uniqueId('newsStyle'); %>
+                        <div><span class="effect-radio <%=(newsStyle==1)?'active':''%>" id="<%=_id %>" data-value="1" data-helper="grid"><span class="helper"><span class="help"><%=__("gridLegend")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-photogrid-block"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
+                        <% var _id= _.uniqueId('newsStyle'); %>
+                        <div><span class="effect-radio <%=(newsStyle==2)?'active':''%>" id="<%=_id %>" data-value="2" data-helper="list"><span class="helper"><span class="help"><%=__("listLegend")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-nav-menu"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
+                    </div>
                 </div>
-            </div>
-        </article>
-        <article class="panel-option" id="formatImage" <%=(newsStyle===0)?'style="display:none"':''%>>
-            <header>
-                <h3 class="option-name"></span> <%=__("FormatImage")%></h3>
-                <p class="panel-content-legend"><%=__("FormatImageLegend")%></p>
-            </header>
-            <div>
-                <div class="category-content radio-transformed" id="Radioformatimage">
-                    <% var _id= _.uniqueId('formatImage'); %>
-                    <div><span class="effect-radio <%=(newsFormat=='landscape')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="landscape" data-helper="landscape"><span class="helper"><span class="help"><%=__("landscape")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-transition-alpha"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
-                    <% var _id= _.uniqueId('formatImage'); %>
-                    <div><span class="effect-radio <%=(newsFormat=='portrait')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="portrait" data-helper="portrait"><span class="helper"><span class="help"><%=__("portrait")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-image-portrait"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
-                    <% var _id= _.uniqueId('formatImage'); %>
-                    <div><span class="effect-radio <%=(newsFormat=='square')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="square" data-helper="square"><span class="helper"><span class="help"><%=__("square")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-image-square"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
+            </article>
+            <article class="panel-option" id="formatImage" <%=(newsStyle==0)?'style="display:none"':''%>>
+                <header>
+                    <h3 class="option-name"></span> <%=__("FormatImage")%></h3>
+                    <p class="panel-content-legend"><%=__("FormatImageLegend")%></p>
+                </header>
+                <div>
+                    <div class="category-content radio-transformed" id="Radioformatimage">
+                        <% var _id= _.uniqueId('formatImage'); %>
+                        <div><span class="effect-radio <%=(newsFormat=='landscape')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="landscape" data-helper="landscape"><span class="helper"><span class="help"><%=__("landscape")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-transition-alpha"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
+                        <% var _id= _.uniqueId('formatImage'); %>
+                        <div><span class="effect-radio <%=(newsFormat=='portrait')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="portrait" data-helper="portrait"><span class="helper"><span class="help"><%=__("portrait")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-image-portrait"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
+                        <% var _id= _.uniqueId('formatImage'); %>
+                        <div><span class="effect-radio <%=(newsFormat=='square')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="square" data-helper="square"><span class="helper"><span class="help"><%=__("square")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-image-square"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
+                    </div>
                 </div>
-            </div>
-        </article>
-    
-    </div>
-   
-    <div class="mr15 news-option-margin news-nbreImage">
-        <article class="panel-option">
-            <header>
-                <h3 class="option-name"><%=__("newsNombreImage")%></h3>
-                <p class="panel-content-legend"><%=__("newsNombreImageLegend")%></p>
-            </header>
-            <div class="option-content">
-                <div class="slider-container"><span class="icon-less"></span><div class="slider"></div><span class="icon-more"></span></div>
-            </div>
-        </article>
-    </div>
-    <div class="panel-option-container animated">
-        <article class="panel-option background-color">
-            <header>
-                <h3 class="option-name"><%=__("newsStyleAffichage")%></h3>
-                <p class="panel-content-legend"><%=__("newsStyleAffichageDesc")%></p>
-            </header>
-            <div class="option-content colors">
-                <%  var _id=_.uniqueId('newsStyleAffichage');
-                %>
-                    <input type="radio" class="select-box" name="AffichageCarrouselStyle" value="1" id="<%=_id %>" <%=(newsStyleAff==1)?'checked':''%>>
-                       <label for="<%=_id %>">
-                        <div class="wrapper">
-                            <div class="horizontal-wrap">
-                                <span class="icon-wrapper">
-                                    <span class="icon-collection-style1"></span>
-                                </span>
-                                <span class="name"><%=__("Style")%> 1</span>
-                                <span class="desc"><%=__("DescStyle1")%></span>
+            </article>
+        
+        </div>
+       
+        <div class="mr15 news-option-margin news-nbreImage">
+            <article class="panel-option">
+                <header>
+                    <h3 class="option-name"><%=__("newsNombreImage")%></h3>
+                    <p class="panel-content-legend"><%=__("newsNombreImageLegend")%></p>
+                </header>
+                <div class="option-content">
+                    <div class="slider-container"><span class="icon-less"></span><div class="slider"></div><span class="icon-more"></span></div>
+                </div>
+            </article>
+        </div>
+        <div class="panel-option-container animated">
+            <article class="panel-option background-color">
+                <header>
+                    <h3 class="option-name"><%=__("newsStyleAffichage")%></h3>
+                    <p class="panel-content-legend"><%=__("newsStyleAffichageDesc")%></p>
+                </header>
+                <div class="option-content colors">
+                    <%  var _id=_.uniqueId('newsStyleAffichage');
+                    %>
+                        <input type="radio" class="select-box" name="AffichageCarrouselStyle" value="1" id="<%=_id %>" <%=(newsStyleAff==1)?'checked':''%>>
+                           <label for="<%=_id %>">
+                            <div class="wrapper">
+                                <div class="horizontal-wrap">
+                                    <span class="icon-wrapper">
+                                        <span class="icon-collection-style1"></span>
+                                    </span>
+                                    <span class="name"><%=__("Style")%> 1</span>
+                                    <span class="desc"><%=__("DescStyle1")%></span>
+                                </div>
                             </div>
-                        </div>
-                    </label>
-                    
-                    <%  var _id=_.uniqueId('newsStyleAffichage');%>
-                    <input type="radio" class="select-box" name="AffichageCarrouselStyle" value="2" id="<%=_id %>" <%=(newsStyleAff==2)?'checked':''%>>
-                       <label for="<%=_id %>">
-                        <div class="wrapper">
-                            <div class="horizontal-wrap">
-                                <span class="icon-wrapper">
-                                    <span class="icon-collection-style2"></span>
-                                </span>
-                                <span class="name"><%=__("Style")%> 2</span>
-                                <span class="desc"><%=__("DescStyle2")%></span>
+                        </label>
+                        
+                        <%  var _id=_.uniqueId('newsStyleAffichage');%>
+                        <input type="radio" class="select-box" name="AffichageCarrouselStyle" value="2" id="<%=_id %>" <%=(newsStyleAff==2)?'checked':''%>>
+                           <label for="<%=_id %>">
+                            <div class="wrapper">
+                                <div class="horizontal-wrap">
+                                    <span class="icon-wrapper">
+                                        <span class="icon-collection-style2"></span>
+                                    </span>
+                                    <span class="name"><%=__("Style")%> 2</span>
+                                    <span class="desc"><%=__("DescStyle2")%></span>
+                                </div>
                             </div>
-                        </div>
-                    </label>
-                    
-                    <%  var _id=_.uniqueId('newsStyleAffichage');%>
-                    <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="3" id="<%=_id %>" <%=(newsStyleAff==3)?'checked':''%>>
-                       <label for="<%=_id %>">
-                        <div class="wrapper">
-                            <div class="horizontal-wrap">
-                                <span class="icon-wrapper">
-                                    <span class="icon-collection-style3"></span>
-                                </span>
-                                <span class="name"><%=__("Style")%> 3</span>
-                                <span class="desc"><%=__("DescStyle3")%></span>
+                        </label>
+                        
+                        <%  var _id=_.uniqueId('newsStyleAffichage');%>
+                        <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="3" id="<%=_id %>" <%=(newsStyleAff==3)?'checked':''%>>
+                           <label for="<%=_id %>">
+                            <div class="wrapper">
+                                <div class="horizontal-wrap">
+                                    <span class="icon-wrapper">
+                                        <span class="icon-collection-style3"></span>
+                                    </span>
+                                    <span class="name"><%=__("Style")%> 3</span>
+                                    <span class="desc"><%=__("DescStyle3")%></span>
+                                </div>
                             </div>
-                        </div>
-                    </label>
-    
-                    <%  var _id=_.uniqueId('newsStyleAffichage');%>
-                    <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="4" id="<%=_id %>" <%=(newsStyleAff==4)?'checked':''%>>
-                       <label for="<%=_id %>">
-                        <div class="wrapper">
-                            <div class="horizontal-wrap">
-                                <span class="icon-wrapper">
-                                    <span class="icon-collection-style4"></span>
-                                </span>
-                                <span class="name"><%=__("Style")%> 4</span>
-                                <span class="desc"><%=__("DescStyle4")%></span>
+                        </label>
+        
+                        <%  var _id=_.uniqueId('newsStyleAffichage');%>
+                        <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="4" id="<%=_id %>" <%=(newsStyleAff==4)?'checked':''%>>
+                           <label for="<%=_id %>">
+                            <div class="wrapper">
+                                <div class="horizontal-wrap">
+                                    <span class="icon-wrapper">
+                                        <span class="icon-collection-style4"></span>
+                                    </span>
+                                    <span class="name"><%=__("Style")%> 4</span>
+                                    <span class="desc"><%=__("DescStyle4")%></span>
+                                </div>
                             </div>
-                        </div>
-                    </label>
-    
-                    <%  var _id=_.uniqueId('newsStyleAffichage');%>
-                    <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="5" id="<%=_id %>" <%=(newsStyleAff==5)?'checked':''%>>
-                       <label for="<%=_id %>">
-                        <div class="wrapper">
-                            <div class="horizontal-wrap">
-                                <span class="icon-wrapper">
-                                    <span class="icon-collection-style5"></span>
-                                </span>
-                                <span class="name"><%=__("Style")%> 5</span>
-                                <span class="desc"><%=__("DescStyle5")%></span>
+                        </label>
+        
+                        <%  var _id=_.uniqueId('newsStyleAffichage');%>
+                        <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="5" id="<%=_id %>" <%=(newsStyleAff==5)?'checked':''%>>
+                           <label for="<%=_id %>">
+                            <div class="wrapper">
+                                <div class="horizontal-wrap">
+                                    <span class="icon-wrapper">
+                                        <span class="icon-collection-style5"></span>
+                                    </span>
+                                    <span class="name"><%=__("Style")%> 5</span>
+                                    <span class="desc"><%=__("DescStyle5")%></span>
+                                </div>
                             </div>
-                        </div>
-                    </label>
-    
-                    <%  var _id=_.uniqueId('newsStyleAffichage');%>
-                    <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="6" id="<%=_id %>" <%=(newsStyleAff==6)?'checked':''%>>
-                       <label for="<%=_id %>">
-                        <div class="wrapper">
-                            <div class="horizontal-wrap">
-                                <span class="icon-wrapper">
-                                    <span class="icon-collection-style6"></span>
-                                </span>
-                                <span class="name"><%=__("Style")%> 6</span>
-                                <span class="desc"><%=__("DescStyle6")%></span>
+                        </label>
+        
+                        <%  var _id=_.uniqueId('newsStyleAffichage');%>
+                        <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="6" id="<%=_id %>" <%=(newsStyleAff==6)?'checked':''%>>
+                           <label for="<%=_id %>">
+                            <div class="wrapper">
+                                <div class="horizontal-wrap">
+                                    <span class="icon-wrapper">
+                                        <span class="icon-collection-style6"></span>
+                                    </span>
+                                    <span class="name"><%=__("Style")%> 6</span>
+                                    <span class="desc"><%=__("DescStyle6")%></span>
+                                </div>
                             </div>
-                        </div>
-                    </label>
-                    
-            </div>
-        </article>
+                        </label>
+                        
+                </div>
+            </article>
+        </div>
     </div>
+    
 </div>
 <footer class="foot">
     <div class="button-group save-or-cancel" >
Index: src/js/JEditor/NewsPanel/Templates/publishConfig.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/publishConfig.html	(révision 12370)
+++ src/js/JEditor/NewsPanel/Templates/publishConfig.html	(révision 12371)
@@ -1,4 +1,4 @@
-<div id="meta-configuration" class="meta-configuration scrollbar-classic">
+<div id="meta-configuration" class="news-rigthpanel scrollbar-classic">
     <header class="panel-head">
         <span class="icon icon-rotation-right"></span>
         <h1 class="panel-name">Publication</h1>
@@ -9,43 +9,45 @@
         </div>
    
     <div class="option-content ">
-       <h4>
-        <span class="icon icon-calendar-line"></span>
-        <span>Programmez l'envoi</span>
-       </h4>
-        <div class="panel-content-intro"> Programmez une date de publication de l'article</div>
-        
-      <div class="batch">
-         <!-- toogle -->
-            <span class="switch batchActions">
-                <span></span>
-            </span>
-            <span class="labelnon">Non</span>
-            <span class="labeloui">Oui</span>
-        </div> 
-        <div class="btn-group-batch forProgram">
-            <div class="block-datepicker">
-                <div id="datepicker">
-                     <!-- datepicker -->
+        <article class="panel-option">
+            <header>
+                <h3 class="option-name"><%=__("newsNombreImage")%></h3>
+                <p class="panel-content-legend"><%=__("newsNombreImageLegend")%></p>
+            </header>
+            <div class="option-content">
+                <div class="batch">
+                    <!-- toogle -->
+                       <span class="switch batchActions">
+                           <span></span>
+                       </span>
+                       <span class="labelnon">Non</span>
+                       <span class="labeloui">Oui</span>
+                   </div> 
+                   <div class="btn-group-batch forProgram">
+                       <div class="block-datepicker">
+                           <div id="datepicker">
+                                <!-- datepicker -->
+                       </div>
+           
+                       <div>Votre article sera publié le <span class="dateprog"></span></div>
+                       <div>
+                           <button>
+                               <span class="icon icon-rotation-right"></span>
+                               <span>Programmer la publication</span>
+                           </button>
+                       </div>
+                   </div>
+                   <div class="forPub">
+                       <div>Votre article sera publié immédiatement</div>
+                       <div>
+                           <button>
+                               <span class="icon icon-rotation-right"></span>
+                               <span>Publié l'article</span>
+                           </button>
+                       </div>
+                   </div>
+               </div>
             </div>
-
-            <div>Votre article sera publié le <span class="dateprog"></span></div>
-            <div>
-                <button>
-                    <span class="icon icon-rotation-right"></span>
-                    <span>Programmer la publication</span>
-                </button>
-            </div>
-        </div>
-        <div class="forPub">
-            <div>Votre article sera publié immédiatement</div>
-            <div>
-                <button>
-                    <span class="icon icon-rotation-right"></span>
-                    <span>Publié l'article</span>
-                </button>
-            </div>
-        </div>
+        </article>
     </div>
-</div>
 </div>
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Views/GlobalConfigView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/GlobalConfigView.js	(révision 12370)
+++ src/js/JEditor/NewsPanel/Views/GlobalConfigView.js	(révision 12371)
@@ -4,14 +4,12 @@
 	"JEditor/Commons/Events",
 	"JEditor/Commons/Ancestors/Views/View",
     "JEditor/NewsPanel/Models/NewsConfig",
-    "JEditor/App/Config",
     "JEditor/Commons/Pages/Models/PageCollection",
     "text!JEditor/NewsPanel/Templates/globalConfig.html",
-    "JEditor/Commons/Ancestors/Views/SaveCancelPanel",
     "JEditor/PagePanel/Views/TemplateDropDownView",
 
     "i18n!JEditor/NewsPanel/nls/i18n",
-], function ($, _, Events, View, NewsConfig, Config, PageCollection, template, SaveCancelPanel,TemplateDropDown, translate) {
+], function ($, _, Events, View, NewsConfig, PageCollection, template,TemplateDropDown, translate) {
     
     /**
      * 
@@ -68,7 +66,7 @@
         },
         initialize: function() {
             this._super();
-            this.model = new NewsConfig();
+            this.model = NewsConfig.getInstance();
             this.pageCollection = PageCollection.getInstance();
             var defaultLayout = this.pageCollection.findWhere({type:"template",lang:this.options.language.id});
             this.layoutsDropDown = new TemplateDropDown({collection: this.pageCollection,_default:defaultLayout, language:this.options.language});
@@ -75,11 +73,11 @@
             this.layoutsDropDown.filter({type:'template',lang:this.options.language.id});
        
             this.template = this.buildTemplate(template, translate);
-            this.model.layout = this.layoutsDropDown.current.layout;
+            this.model.layout = this.layoutsDropDown.current.name;
             this.listenTo(this.layoutsDropDown, Events.ChoiceEvents.SELECT, this.onLayoutSelect);
         }, 
         onLayoutSelect: function(view, templatePage) {
-            this.model.layout = templatePage.layout;
+            this.model.layout = templatePage.name;
         },
         _onSaveClick: function(event) {
             if (this.save)
@@ -101,7 +99,8 @@
                 newsStyle:this.model.newsStyle,
                 newsFormat:this.model.newsFormat,
                 newsNbArticle:this.model.newsNbArticle,
-                newsStyleAff:this.model.newsStyleAff
+                newsStyleAff:this.model.newsStyleAff,
+                canchangeLayout: this.app.user.can("change_news_layout")
             };
             this.$el.html(this.template(templateVars));
             this.$('.news-nbreImage .slider').slider({
@@ -119,7 +118,7 @@
         },
         save: function() {
             this.model.save();
-            // this.trigger(Events.OptionCollectionViewEvents.SAVE, this);
+            this.trigger(Events.GlobalConfigViewEvents.SAVE, this);
         },
         /**
          * annule les changements éfectuées depuis l'ouverture
@@ -126,10 +125,16 @@
          */
         cancel: function() {
             this.model.cancel();
-          //  this.trigger(Events.OptionCollectionViewEvents.CANCEL, this);
+          this.trigger(Events.GlobalConfigViewEvents.CANCEL, this);
         }
         
     });
+    Events.extend({
+        GlobalConfigViewEvents: {
+            SAVE: 'save',
+            CANCEL: 'cancel'
+        }
+    });
     
     return GlobalConfigView;
 });
Index: src/less/imports/news_panel/main.less
===================================================================
--- src/less/imports/news_panel/main.less	(révision 12370)
+++ src/less/imports/news_panel/main.less	(révision 12371)
@@ -962,7 +962,7 @@
 	}
 }
 
-#meta-configuration, .news-configuration {
+#meta-configuration {
 	span.icon {
 		color: #515151;
 		margin-right: 10px;
@@ -1054,3 +1054,74 @@
    }
 
 }
+
+
+.news-rigthpanel {
+	margin: 15px;
+	.panel-head h1.panel-name {
+		font-size: 1.3em;
+		display: inline-block;
+		position: relative;
+		font-weight: 500;
+		margin: 0;
+	}
+	.panel-content {
+		position:absolute;
+		top:78px;
+		bottom:50px;
+		width:100%;
+		overflow:auto;
+		overflow-x:hidden;
+		box-sizing:border-box;
+		padding-right:3px;
+		padding-bottom:20px;
+		scrollbar-gutter:stable;
+	}
+	span.icon {
+		color: #515151;
+		margin-right: 10px;
+		display: inline;
+	}
+	.panel-option header h3.option-name {
+		font-size: 1.2em;
+		font-weight: 200;
+		margin: 0;
+	}
+	.panel-content .panel-content-legend, .panel-content-intro {
+		color: gray;
+		font-size: .8em;
+	}
+	.template-list .layouts{
+		margin-top: 25px;
+		width: 100%;
+		.dropdown-toggle{
+			text-align: left;
+			min-width: 90%;
+			line-height: 30px;
+			color: #fff;
+			.caret {
+				margin: 0;
+				display: block;
+				position: absolute;
+				top: 13px;
+				right: 8px;
+			}
+		}
+		.dropdown-menu {
+			top: auto;
+			bottom: 0 !important;
+			margin-bottom: 1px;
+			min-width: 90%;
+			background-color: #333;
+			box-shadow: 0 5px 10px rgba(0,0,0,.25);
+			background-clip: padding-box;
+			left: 36px;
+			li > a:hover{
+				background-color: #1a1a1a;
+			}
+		}
+		.btn-group.open .btn.dropdown-toggle {
+			background-color: #41ffbe;
+		  }
+	}
+}
\ No newline at end of file
