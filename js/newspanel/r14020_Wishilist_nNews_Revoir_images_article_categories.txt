Revision: r14020
Date: 2025-03-28 10:35:15 +0300 (zom 28 Mar 2025) 
Author: frahajanirina 

## Commit message
Wishilist:nNews:Revoir images article/categories

## Files changed

## Full metadata
------------------------------------------------------------------------
r14020 | frahajanirina | 2025-03-28 10:35:15 +0300 (zom 28 Mar 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Views/ReadOnlyFileListManagerView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Models/Article.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Models/Categorie.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/NewsPanel.js
   A /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/UploadCategoryImage.html
   A /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/UploadImageArticle.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/categorieForm.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/AddArticleView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/CategorieAddView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/UploadImageArticleView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/UploadImageCategoryView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/news_panel/main.less

Wishilist:nNews:Revoir images article/categories
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Files/Views/ReadOnlyFileListManagerView.js
===================================================================
--- src/js/JEditor/Commons/Files/Views/ReadOnlyFileListManagerView.js	(révision 14019)
+++ src/js/JEditor/Commons/Files/Views/ReadOnlyFileListManagerView.js	(révision 14020)
@@ -85,7 +85,7 @@
                         return this.options.showFilters = visible;
                 },
                 updateNumberFile :function(){
-                    var lengthResource  = this.listView.collection.countFile;
+                    var lengthResource  = (this.listView.collection.countFile > 0) ? this.listView.collection.countFile : 0;
                     var lengthCollection  = this.listView.collection.length;
                     var htmlCountFile="<span class=\"numFiles\">"+lengthCollection+"</span>"+translate('File(s)')+"<span class=\"numberFiles\"> "+lengthResource+"</span>";
                     this.$("#numberFiles").html(htmlCountFile);
Index: src/js/JEditor/NewsPanel/Models/Article.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Article.js	(révision 14019)
+++ src/js/JEditor/NewsPanel/Models/Article.js	(révision 14020)
@@ -32,7 +32,9 @@
                             content:null,
                             numberOfTranslation:0,
                             enseigne: null,
-                            userRole: null
+                            userRole: null,
+                            desc: null,
+                            langAltRessource: null
                         },
                         initialize: function() {
                             this._super();
@@ -87,7 +89,9 @@
                                 category:this.category,
                                 title : this.title,
                                 introduction : this.introduction,
-                                client: (this.content != null) ? this.content.client : null
+                                client: (this.content != null) ? this.content.client : null,
+                                file: this.getFile(),
+                                ressource: this.ressource
                             };
                             return returnValue;
                         },
@@ -98,6 +102,8 @@
                             this.set('title', this.lastState.title);
                             this.set('introduction', this.lastState.introduction);
                             this.content.client = this.lastState.client;
+                            this.set('file', this.lastState.file);
+                            this.set('ressource', this.lastState.ressource);
                             this.trigger(Events.ArticleEvents.ARTICLE_CANCEL, this);
                         },
                         cancelParams: function() {
@@ -136,7 +142,7 @@
                         }
                     });
                    
-            Article.SetAttributes(['category', 'state', 'numberOfTranslation','title', 'lang','ressource','introduction', 'publicationDate','programmingDate', 'metaTitle', 'metaOpengraph', "page", "content","news", "enseigne", "userRole"]);
+            Article.SetAttributes(['category', 'state', 'numberOfTranslation','title', 'lang','ressource','introduction', 'publicationDate','programmingDate', 'metaTitle', 'metaOpengraph', "page", "content","news", "enseigne", "userRole", "desc", "langAltRessource"]);
             Events.extend(
                 {
                     ArticleEvents : {
Index: src/js/JEditor/NewsPanel/Models/Categorie.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Categorie.js	(révision 14019)
+++ src/js/JEditor/NewsPanel/Models/Categorie.js	(révision 14020)
@@ -16,7 +16,9 @@
                     defaults: {
                         ressource: null,
                         numberArticlesInCagetory:0,
-                        lang : {}
+                        lang : {},
+                        desc: null,
+                        langAltRessource: null
                     },
                    
                     initialize: function() {
@@ -34,7 +36,8 @@
                        return !_.isEqual(this.toJSONData(), this.lastState);
                     },
                     cancel: function() {
-                        this.set('ressource',this.lastState.ressource)
+                        this.set('ressource',this.lastState.ressource);
+                        this.set('file',this.lastState.file);
                         var self = this
                         _.each(this.lastState.lang, function(langData, langKey) {
                            self.lang[langKey].title = langData.title;
@@ -68,6 +71,7 @@
                         var returnValue = {
                             id: this.id,
                             ressource:this.ressource,
+                            file: this.getFile(),
                             lang : {}
                         };
                         _.each(this.lang, function(langData, langKey) {
@@ -123,6 +127,6 @@
                     });
                 };
                 
-            Categorie.SetAttributes(['ressource','lang','numberArticlesInCagetory']);
+            Categorie.SetAttributes(['ressource','lang','numberArticlesInCagetory', 'desc', 'langAltRessource']);
         return Categorie;
     });
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 14019)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 14020)
@@ -21,6 +21,8 @@
     "JEditor/NewsPanel/Views/AvailableView",
     "JEditor/NewsPanel/Views/PublishConfigView",
     "JEditor/NewsPanel/Articles/Versions/Views/VersionsCollectionView",
+    "./Views/UploadImageArticleView",
+    "./Views/UploadImageCategoryView",
     "i18n!./nls/i18n",
     "jqueryPlugins/affix",
     "ckeditor"
@@ -48,6 +50,8 @@
             AvailableView,
             PublishConfigView,
             VersionsCollectionView,
+            UploadImageArticleView,
+            UploadImageCategoryView,
             translate
             ) {
             var NewsPanel = PanelView.extend(
@@ -61,7 +65,8 @@
                                     'click #available-blocks-trigger' : 'showAvailableBlocks',
                                     'click #publish-config-trigger' : 'showPublishConfig',
                                     'click #show-zone-version': 'showZoneVersionsPanel',
-                                    
+                                    'click .upload-article' : 'inTheSiteAndComputer',
+                                    'click .category-img' : 'uploadImgCategoryinTheSiteAndComputer',
                                 },
                                 cssFile: __IDEO_INTEGRATION_PATH__ + "/build/css/news_panel.css",
 
@@ -253,6 +258,48 @@
                                     }, this);
                                     this.categories.trigger('change');
                                 },
+
+                                //Upload view article
+                                inTheSiteAndComputer : function(){
+                                    this.initUploadViewArticle();
+                                    this.renderRightPanel(this.childViews.UploadImageArticleView, 'noRender');
+                                    this.rightPanelView.showContent(this.childViews.UploadImageArticleView);
+                                    this.$('.upload-article  .uploader .actions-wrapper').removeClass('visible');
+                                    this.rightPanelView.showPanel();
+                                    
+                                    return false;
+                                },
+                                initUploadViewArticle: function() {
+                                    this.childViews.UploadImageArticleView = new UploadImageArticleView({
+                                        newsPanel: this,
+                                        model:  this.childViews.newsEditorView.articleEditorView.model
+                                    });
+                                },
+                                hidesUploadViewArticle : function() {
+                                    this.rightPanelView.hideContent(this.childViews.UploadImageArticleView);
+                                    this.rightPanelView.hidePanel();
+                                },
+
+                                //Upload view category
+                                uploadImgCategoryinTheSiteAndComputer : function(){
+                                    this.initUploadViewCategory();
+                                    this.renderRightPanel(this.childViews.UploadImageCategoryView, 'noRender');
+                                    this.rightPanelView.showContent(this.childViews.UploadImageCategoryView);
+                                    this.$('.upload-categorie  .uploader .actions-wrapper').removeClass('visible');
+                                    this.rightPanelView.showPanel();
+                                    
+                                    return false;
+                                },
+                                initUploadViewCategory: function() {
+                                    this.childViews.UploadImageCategoryView = new UploadImageCategoryView({
+                                        newsPanel: this,
+                                        model: this.childViews.newsEditorView.addCategorieView.model
+                                    });
+                                },
+                                hidesUploadCategoryView : function() {
+                                    this.rightPanelView.hideContent(this.childViews.UploadImageCategoryView);
+                                    this.rightPanelView.hidePanel();
+                                },
                                 updateTranslationCount: function(article) {
                                    if (article.isNew()) {
                                         // Nouvel article ajouté
Index: src/js/JEditor/NewsPanel/Templates/UploadCategoryImage.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/UploadCategoryImage.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Templates/UploadCategoryImage.html	(révision 14020)
@@ -0,0 +1,38 @@
+<div class="panel-option-container animated image-option panel-upload">
+    <header class="panel-head">
+        <h1 class="panel-name">
+            <span class="icon-add-image"></span>
+            <%=__('addImage')%>
+        </h1>
+    </header>
+    <article class="panel-option image-option upload-img">
+        <div class="zone-upload">
+            
+        </div>
+        <div class="cropbtn">
+            <a>
+                <span class="icon"></span>
+                <%=__('editImage')%>
+            </a>
+        </div>
+        <div class="infos-img">
+            <input value="<%= desc %>" class="desc-img <%= disable ? 'disabled' : '' %>" data-name="desc" placeholder="<%= descPlaceholder %>" <%= disable ? 'disabled="disabled"' : '' %>/>
+        </div>
+    </article>
+</div>
+<footer class="foot apply-cancel">
+    <div class="button-group save-or-cancel">
+        <a class="button cancel" data-action="cancel">
+            <span class="wrapper">
+                <span class="icon"></span>
+                <span class="text"><%=__('cancel')%></span>
+            </span>
+        </a>
+        <a class="button save" data-action="save">
+            <span class="wrapper">
+                <span class="icon"></span>
+                <span class="text"><%=__('apply')%></span>
+            </span>
+        </a>
+    </div>
+</footer>
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Templates/UploadImageArticle.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/UploadImageArticle.html	(nonexistent)
+++ src/js/JEditor/NewsPanel/Templates/UploadImageArticle.html	(révision 14020)
@@ -0,0 +1,38 @@
+<div class="panel-option-container animated image-option panel-upload">
+    <header class="panel-head">
+        <h1 class="panel-name">
+            <span class="icon-add-image"></span>
+            <%=__('addImage')%>
+        </h1>
+    </header>
+    <article class="panel-option image-option upload-img">
+        <div class="zone-upload">
+            
+        </div>
+        <div class="cropbtn">
+            <a>
+                <span class="icon"></span>
+                <%=__('editImage')%>
+            </a>
+        </div>
+        <div class="infos-img">
+            <input value="<%= desc %>" class="desc-img <%= disable ? 'disabled' : '' %>" data-name="desc" placeholder="<%= descPlaceholder %>" <%= disable ? 'disabled="disabled"' : '' %>/>
+        </div>
+    </article>
+</div>
+<footer class="foot apply-cancel">
+    <div class="button-group save-or-cancel">
+        <a class="button cancel" data-action="cancel">
+            <span class="wrapper">
+                <span class="icon"></span>
+                <span class="text"><%=__('cancel')%></span>
+            </span>
+        </a>
+        <a class="button save" data-action="save">
+            <span class="wrapper">
+                <span class="icon"></span>
+                <span class="text"><%=__('apply')%></span>
+            </span>
+        </a>
+    </div>
+</footer>
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Templates/categorieForm.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/categorieForm.html	(révision 14019)
+++ src/js/JEditor/NewsPanel/Templates/categorieForm.html	(révision 14020)
@@ -1,7 +1,7 @@
 <div>
     <div class="dis-table bordered">
         <div class="flex_categorie">
-            <div class="upload-categorie">
+            <div class="upload-categorie category-img">
                 <% if(edit && fileUrl != ''){ %> <span class="icon-bin"> </span><% } %>
                 <!-- uploader -->
             </div>
Index: src/js/JEditor/NewsPanel/Views/AddArticleView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/AddArticleView.js	(révision 14019)
+++ src/js/JEditor/NewsPanel/Views/AddArticleView.js	(révision 14020)
@@ -33,7 +33,6 @@
             'click button.saveNewArticle' : 'addArticle',
             'click button.saveArticle' : 'updateArticle',
             'click button.annuler' : 'cancel',
-            'click .upload-article ' : 'onlyComputer',
             'change .article-info input[name="title"]': 'setTitle',
             'change .article-info textarea[name="introduction"]': 'setIntroduction',
            },
@@ -60,12 +59,6 @@
             }  
 
         },
-        onlyComputer : function(e){
-            e.preventDefault();
-            e.stopImmediatePropagation();
-            this.$('.upload-article  .uploader .actions-wrapper').removeClass('visible');
-            this.$('.upload-article  .uploader .actions-wrapper .actions .computer input:file.upload').trigger('click');
-        },
         _onLoaded: function() {
             this._currentCategory = (this.model.getCategoryModel())? this.model.getCategoryModel(): null;
             this.lang = this.options.language;
@@ -84,8 +77,22 @@
                     return model.lang[this.lang.id].title;
                 }, this)});
             this.listenTo(this._categoryDropdown, Events.ChoiceEvents.SELECT, this._onCategorySelected)
+            this.listenTo(Backbone, 'file:saveArticle', this._onFileSave);
+            this.listenTo(Backbone, 'file:init', this._onFileInit);
             this.render();
         },
+        _onFileSave: function(file) {
+            this.model.set('file', file);
+            this.model.set('ressource', file.id);
+            
+            this.render();
+        },
+        _onFileInit: function() {
+            this.model.file = this.model.lastState.file;
+            this.model.ressource = this.model.lastState.ressource;
+            
+            this.render();
+        },
         _onCategorySelected : function(view, selected) {
            var valid = true;
            var that =this;
Index: src/js/JEditor/NewsPanel/Views/CategorieAddView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(révision 14019)
+++ src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(révision 14020)
@@ -29,7 +29,6 @@
          'click button.saveNewCat' : 'addCategorie',
          'click button.saveCat' : 'updateCategorie',
          'click button.remove' : 'removeClick',
-         'click .upload-categorie ' : 'onlyComputer',
          'change input[name="title"]': 'setTitle',
          'change textarea[name="description"]': 'setDescription',
          'click [data-language]': '_onLangClick',
@@ -68,7 +67,21 @@
             this.listenTo(this.langDropDown, Events.ChoiceEvents.SELECT, this._onLanguageChange);
             this.fileCollection = new FileCollection();
             this.translations = translate.translations;
+            this.listenTo(Backbone, 'file:save', this._onFileSave);
+            this.listenTo(Backbone, 'file:init', this._onFileInit);
         },
+        _onFileSave: function(file) {
+            this.model.set('file', file);
+            this.model.set('ressource', file.id);
+            
+            this.render();
+        },
+        _onFileInit: function() {
+            this.model.file = this.model.lastState.file;
+            this.model.ressource = this.model.lastState.ressource;
+            
+            this.render();
+        },
         proccessAttribute : function() {
 
             this.$('.upload-categorie .uploader').addClass('done');
@@ -82,12 +95,6 @@
             }  
 
         },
-        onlyComputer : function(e){
-            e.preventDefault();
-            e.stopImmediatePropagation();
-            this.$('.upload-categorie  .uploader .actions-wrapper').removeClass('visible');
-            this.$('.upload-categorie  .uploader .actions-wrapper .actions .computer input:file.upload').trigger('click');
-        },
         _onLanguageChange: function(view, lang) {
             this.lang = lang;
             if (this.model.id) {
Index: src/js/JEditor/NewsPanel/Views/UploadImageArticleView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/UploadImageArticleView.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Views/UploadImageArticleView.js	(révision 14020)
@@ -0,0 +1,133 @@
+define([
+    "jquery",
+    "underscore",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/View",
+    "text!../Templates/UploadImageArticle.html",
+    "JEditor/FilePanel/Models/FileCollection",
+    "JEditor/Commons/Files/Views/FileUploaderView",
+    "JEditor/FilePanel/Views/ImageEditView",
+    "i18n!../nls/i18n"
+], function ($, _, Events, View, tpl, FileCollection, FileUploaderView, ImageEditView, translate) {
+    var UploadImageArticleView = View.extend({
+        
+        events: {
+            'click .apply-cancel .button.save': '_onSaveClick', 
+            'click .apply-cancel .button.cancel': '_onCancelClick',
+            'change .infos-img>input' : 'updateDescriptionImg',
+            'click .upload-img .cropbtn' : 'editImage'
+        },
+        /**
+         * Initialisation de la view
+         */
+        initialize: function () {
+            if (this.options.newsPanel)
+                this.newsPanel = this.options.newsPanel;
+            delete this.options.newsPanel;
+            View.prototype.initialize.apply(this, arguments);
+            this.template = this.buildTemplate(tpl, translate);
+
+            this.fileCollection = new FileCollection();
+            this.fileUploader = new FileUploaderView({
+				collection : this.fileCollection,
+				currentFile : this.model.getFile(),
+				uploadParams : {
+					customStockEvent : '_parsestock_image',
+					acceptedTypes : [ 'image' ],
+					acceptedExtensions : ['jpeg','jpg','gif','png'],
+                    refusedExtensions:['bmp']
+				}
+			});
+            this.listenToOnce(this.fileUploader, Events.ListViewEvents.CHOOSE_FILE, this._useExistingFile);
+            this.listenTo(this.fileUploader, Events.FileUploaderEvents.UPLOAD, this._onUpload);
+            this.listenTo(Backbone, 'file:saveArticle', this._checkEditFile);
+            
+            return this;
+        },
+        _checkEditFile: function() {
+            this.fileUploader.currentFile = this.model.file;
+        },
+        
+        /**
+         * Rendu de la view
+         */
+        render: function () {
+            this.undelegateEvents();
+            var disable = 'disabled',
+                descPlaceholder = translate('imageAlt'),
+                lang = this.newsPanel.currentLang.id;
+            if (this.model.file) {
+                var disable = '', 
+                    descPlaceholder = (this.model.file.desc && this.model.file.desc[lang]) ? this.model.file.desc[lang] : null;
+            }                
+            this.$el.html(this.template({
+                disable: disable,
+                descPlaceholder: descPlaceholder,
+                desc: this.model.desc
+            }));
+            this.$('.zone-upload').append(this.fileUploader.el);
+			this.fileUploader.render();
+            this.delegateEvents();
+            
+            return this;
+        },
+        _onUpload : function(file) {
+            this.currentFile = file;
+            this.model.ressource = file.id;
+            this.model.file = file;
+            Backbone.trigger('file:saveArticle', file);
+                
+            this.render();  
+		},
+        _useExistingFile : function(file) {
+            this.currentFile = file;
+            this.model.ressource = file.id;
+            this.model.file = file;
+            this.fileUploader.currentFile = file;
+            Backbone.trigger('file:saveArticle', file);
+
+            this.render();
+		},
+        _onSaveClick: function() {
+            this.newsPanel.hidesUploadViewArticle();
+
+            this.render();
+        },
+        _onCancelClick: function() {
+            this.model.file = this.model.lastState.file;
+            this.model.ressource = this.model.lastState.ressource;
+            this.newsPanel.hidesUploadViewArticle();
+            Backbone.trigger('file:init', this);
+        },
+        updateDescriptionImg : function(event) {
+            var lang = this.newsPanel.currentLang.id;
+            this.model.langAltRessource = lang;
+            var $target = $(event.currentTarget);
+            this.model.file.desc[lang] = $target.val();
+			this.model.desc = $target.val();
+		},
+        editImage : function() {
+			if (!this.model.file)
+				return;
+			var imageEditor, that = this;
+			imageEditor = new ImageEditView({
+				model : this.model.file.clone()
+			});
+
+			this.listenToOnce(imageEditor, Events.ImageEditorEvents.SAVE, this._onSave);
+			this.listenToOnce(imageEditor, Events.DialogEvents.CLOSE, function() {
+				that.stopListening(imageEditor);
+				imageEditor.remove();
+			});
+			imageEditor.open();
+		},
+        _onSave : function(file) {
+			this.model.file = file;
+            this.model.ressource = file.id;
+            Backbone.trigger('file:saveArticle', file);
+			this.render();
+		},
+    });
+    
+    return UploadImageArticleView;
+});
Index: src/js/JEditor/NewsPanel/Views/UploadImageCategoryView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/UploadImageCategoryView.js	(nonexistent)
+++ src/js/JEditor/NewsPanel/Views/UploadImageCategoryView.js	(révision 14020)
@@ -0,0 +1,132 @@
+define([
+    "jquery",
+    "underscore",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/View",
+    "text!../Templates/UploadCategoryImage.html",
+    "JEditor/FilePanel/Models/FileCollection",
+    "JEditor/Commons/Files/Views/FileUploaderView",
+    "JEditor/FilePanel/Views/ImageEditView",
+    "i18n!../nls/i18n"
+], function ($, _, Events, View, tpl, FileCollection, FileUploaderView, ImageEditView, translate) {
+    var UploadImageCategoryView = View.extend({
+        
+        events: {
+            'click .apply-cancel .button.save': '_onSaveClick', 
+            'click .apply-cancel .button.cancel': '_onCancelClick',
+            'change .infos-img>input' : 'updateDescriptionImg',
+            'click .upload-img .cropbtn' : 'editImage'
+        },
+        /**
+         * Initialisation de la view
+         */
+        initialize: function () {
+            if (this.options.newsPanel)
+                this.newsPanel = this.options.newsPanel;
+            delete this.options.newsPanel;
+            View.prototype.initialize.apply(this, arguments);
+            this.template = this.buildTemplate(tpl, translate);
+
+            this.fileCollection = new FileCollection();
+            this.fileUploader = new FileUploaderView({
+				collection : this.fileCollection,
+				currentFile : this.model.getFile(),
+				uploadParams : {
+					customStockEvent : '_parsestock_image',
+					acceptedTypes : [ 'image' ],
+					acceptedExtensions : ['jpeg','jpg','gif','png'],
+                    refusedExtensions:['bmp']
+				}
+			});
+            this.listenToOnce(this.fileUploader, Events.ListViewEvents.CHOOSE_FILE, this._useExistingFile);
+            this.listenTo(this.fileUploader, Events.FileUploaderEvents.UPLOAD, this._onUpload);
+            this.listenTo(Backbone, 'file:save', this._checkEditFile);
+            
+            return this;
+        },
+        _checkEditFile: function() {
+            this.fileUploader.currentFile = this.model.file;
+        },
+        
+        /**
+         * Rendu de la view
+         */
+        render: function () {
+            this.undelegateEvents();
+            var disable = 'disabled',
+                descPlaceholder = translate('imageAlt'),
+                lang = this.newsPanel.currentLang.id;
+            if (this.model.file) {
+                var disable = '', 
+                    descPlaceholder = (this.model.file.desc && this.model.file.desc[lang]) ? this.model.file.desc[lang] : null;
+            }
+            this.$el.html(this.template({
+                disable: disable,
+                descPlaceholder: descPlaceholder,
+                desc: this.model.desc
+            }));
+            this.$('.zone-upload').append(this.fileUploader.el);
+			this.fileUploader.render();
+            this.delegateEvents();
+            
+            return this;
+        },
+        _onUpload : function(file) {
+            this.currentFile = file;
+            this.model.ressource = file.id;
+            this.model.file = file;
+            Backbone.trigger('file:save', file);
+                
+            this.render();  
+		},
+        _useExistingFile : function(file) {
+            this.currentFile = file;
+            this.model.ressource = file.id;
+            this.model.file = file;
+            this.fileUploader.currentFile = file;
+            Backbone.trigger('file:save', file);
+
+            this.render();
+		},
+        _onSaveClick: function() {
+            this.newsPanel.hidesUploadCategoryView();
+
+            this.render();
+        },
+        _onCancelClick: function() {
+            this.model.cancel();
+            this.newsPanel.hidesUploadCategoryView();
+            Backbone.trigger('file:init', this);
+        },
+        updateDescriptionImg : function(event) {
+            var lang = this.newsPanel.currentLang.id;
+            this.model.langAltRessource = lang;
+            var $target = $(event.currentTarget);
+            this.model.file.desc[lang] = $target.val();
+			this.model.desc = $target.val();
+		},
+        editImage : function() {
+			if (!this.model.file)
+				return;
+			var imageEditor, that = this;
+			imageEditor = new ImageEditView({
+				model : this.model.file.clone()
+			});
+
+			this.listenToOnce(imageEditor, Events.ImageEditorEvents.SAVE, this._onSave);
+			this.listenToOnce(imageEditor, Events.DialogEvents.CLOSE, function() {
+				that.stopListening(imageEditor);
+				imageEditor.remove();
+			});
+			imageEditor.open();
+		},
+        _onSave : function(file) {
+			this.model.file = file;
+            this.model.ressource = file.id;
+            Backbone.trigger('file:save', file);
+			this.render();
+		},
+    });
+    
+    return UploadImageCategoryView;
+});
Index: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 14019)
+++ src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 14020)
@@ -171,5 +171,8 @@
 	"month": ["Janvier", "Février", "Mars", "Avril", "Mai", "Juin", "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"],
 	"day": ["Dimanche", "Lundi", "Mardi", "Mercredi", "Jeudi", "Vendredi", "Samedi"],
 	"dayMin": ["Di", "Lu", "Ma", "Me", "Je", "Ve", "Sa"],
-	"authorChangedDone": "L'auteur a été changé avec succès."
+	"authorChangedDone": "L'auteur a été changé avec succès.",
+	"imageAlt": "Texte alternatif / description",
+	"addImage"  :   "Ajouter une image",
+	"editImage": "Retoucher l'image"
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 14019)
+++ src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 14020)
@@ -171,5 +171,8 @@
 	"month": ["Janvier", "Février", "Mars", "Avril", "Mai", "Juin", "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"],
 	"day": ["Dimanche", "Lundi", "Mardi", "Mercredi", "Jeudi", "Vendredi", "Samedi"],
 	"dayMin": ["Di", "Lu", "Ma", "Me", "Je", "Ve", "Sa"],
-	"authorChangedDone": "L'auteur a été changé avec succès."
+	"authorChangedDone": "L'auteur a été changé avec succès.",
+	"imageAlt": "Texte alternatif / description",
+	"addImage"  :   "Ajouter une image",
+	"editImage": "Retoucher l'image"
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/i18n.js	(révision 14019)
+++ src/js/JEditor/NewsPanel/nls/i18n.js	(révision 14020)
@@ -168,7 +168,10 @@
         "month": ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"],
         "day": ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"],
         "dayMin": ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"],
-        "authorChangedDone": "The author has been changed successfully."
+        "authorChangedDone": "The author has been changed successfully.",
+        "imageAlt":"Alternative text / description",
+        "addImage" :  "Add an image",
+        "editImage":"Image editing"
     },
     "fr-fr": true,
     "fr-ca": true
Index: src/less/imports/news_panel/main.less
===================================================================
--- src/less/imports/news_panel/main.less	(révision 14019)
+++ src/less/imports/news_panel/main.less	(révision 14020)
@@ -1301,4 +1301,62 @@
 			background-color: #41ffbe;
 		  }
 	}
+}
+.panel-upload {
+	margin-left: 25px !important;
+	.upload-img{
+		.cropbtn {
+			padding: 20px;
+			background: #2f2f2f;
+			border-radius: 0 0 6px 6px;
+			a {
+			  cursor: pointer;
+			  display: block;
+			  height: 40px;
+			  background: #444;
+			  border-radius: 4px;
+			  text-align: center;
+			  font: 400 14px / 40px Raleway, sans-serif;
+			  color: #dadada;
+			  overflow: hidden;
+			  &:hover {
+				background: @pageColor;
+				color: @white;
+				.icon {
+					.sprite(24,6);
+				}
+			  }
+			}
+			.icon {
+			  display: inline-block;
+			  vertical-align: middle;
+			  width: 20px;
+			  height: 20px;
+			  margin-right: 10px;
+			  .sprite(24,5);
+			}
+		}
+		.infos-img {
+			margin-top: 20px;
+			.disabled {
+				cursor: not-allowed;
+			} 
+			input {
+				display: block;
+				background: 0 0;
+				width: 90%;
+				font: 200 16px Raleway, sans-serif;
+				color: #d6d6d6;
+				border: 1px solid transparent;
+				border-bottom: 1px solid #313131;
+				margin: 0;
+				padding: 17px 15px;
+			}
+		}
+	}
+}
+.apply-cancel {
+	top: 22px;
+	position: relative !important;
+	margin: 0 25px 0 25px !important;
 }
\ No newline at end of file
