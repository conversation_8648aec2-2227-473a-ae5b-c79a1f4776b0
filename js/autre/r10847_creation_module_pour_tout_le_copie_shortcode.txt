Revision: r10847
Date: 2023-04-19 16:53:58 +0300 (lrb 19 Apr 2023) 
Author: srazana<PERSON>lisoa 

## Commit message
creation module pour tout le copie shortcode

## Files changed

## Full metadata
------------------------------------------------------------------------
r10847 | srazanandralisoa | 2023-04-19 16:53:58 +0300 (lrb 19 Apr 2023) | 1 ligne
Chemins modifiés :
   A /branches/ideo3_v2/integration/src/js/JEditor/App/Messages/ClipboardModule.js
   M /branches/ideo3_v2/integration/src/js/JEditor/App/Messages/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/App/Messages/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/App/Messages/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/CollectionDetailView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileDetailView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NavigationPanel/Views/MenuEditView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormBuilderView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Views/MapOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/MarqueClientView.js

creation module pour tout le copie shortcode
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/App/Messages/ClipboardModule.js
===================================================================
--- src/js/JEditor/App/Messages/ClipboardModule.js	(nonexistent)
+++ src/js/JEditor/App/Messages/ClipboardModule.js	(révision 10847)
@@ -0,0 +1,52 @@
+// Définition du module ClipboardModule
+define(['jquery',  "i18n!./nls/i18n"], function($,translate) {
+    var ClipboardModule = {
+  
+      // La fonction pour copier le texte dans le presse-papiers
+      copyToClipboard: function(e, edit) {
+        var copyText = $(e.currentTarget).text();
+        // pour le shortcode du menupanel, suprimer certain mot du text
+        if (edit) {
+          var copyText = copyText.replace(edit, '').trim();
+        }
+            const clipboard = navigator.clipboard;
+            if (clipboard !== undefined && clipboard !== "undefined") {
+                navigator.clipboard.writeText(copyText.trim()).then(this.successfully($(e.currentTarget),e));
+            } 
+            else 
+            {
+                if (document.execCommand) 
+                {
+                    const el = document.createElement("input");
+                    el.value = copyText;
+                    document.body.append(el);
+                    el.select();
+                    el.setSelectionRange(0, value.length);
+                    if (document.execCommand("copy")) 
+                    {
+                        this.successfully();
+                    }
+                    el.remove();
+                }
+            }
+      },
+  
+      // La fonction pour afficher une notification de copie réussie
+      successfully: function(el, e) {
+        // Afficher une notification de copie réussie à l'utilisateur au meme position que le l'element cliquer 
+        var el = $('<div role="alert" aria-live="polite" class="toastcopy"><div class="jq-toast-single jq-icon-success"><span class="icon icon-check-circle"></span><span>'+translate('copy')+'</span></div></div>');
+        el.css('position', 'absolute');
+        el.css('left', $(e.currentTarget).position().left);
+        el.css('top', $(e.currentTarget).position().top);
+        el.css('width', '80px');
+        el.css('z-index', $(e.currentTarget).zIndex()+2);
+        $(e.currentTarget).before(el);
+        window.setTimeout(function(){
+            el.parent().find('.toastcopy').remove();
+        }, 2000);  
+      }
+    };
+  
+    // Retourner le module pour pouvoir être utilisé dans d'autres modules
+    return ClipboardModule;
+  });
Index: src/js/JEditor/App/Messages/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/App/Messages/nls/fr-ca/i18n.js	(révision 10846)
+++ src/js/JEditor/App/Messages/nls/fr-ca/i18n.js	(révision 10847)
@@ -1 +1 @@
-define({"dontAskAgain":"Ne plus me demander","cancel":"Annuler","confirm":"Confirmation demandée","no":"Non","yes":"Oui","error":"Erreur","notification":"Notification"});
\ No newline at end of file
+define({"dontAskAgain":"Ne plus me demander","cancel":"Annuler","confirm":"Confirmation demandée","no":"Non","yes":"Oui","error":"Erreur","notification":"Notification","copy":"copié"});
\ No newline at end of file
Index: src/js/JEditor/App/Messages/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/App/Messages/nls/fr-fr/i18n.js	(révision 10846)
+++ src/js/JEditor/App/Messages/nls/fr-fr/i18n.js	(révision 10847)
@@ -1 +1 @@
-define({"dontAskAgain":"Ne plus me demander","cancel":"Annuler","confirm":"Confirmation demandée","no":"Non","yes":"Oui","error":"Erreur","notification":"Notification"});
\ No newline at end of file
+define({"dontAskAgain":"Ne plus me demander","cancel":"Annuler","confirm":"Confirmation demandée","no":"Non","yes":"Oui","error":"Erreur","notification":"Notification","copy":"copié"});
\ No newline at end of file
Index: src/js/JEditor/App/Messages/nls/i18n.js
===================================================================
--- src/js/JEditor/App/Messages/nls/i18n.js	(révision 10846)
+++ src/js/JEditor/App/Messages/nls/i18n.js	(révision 10847)
@@ -1 +1 @@
-define({ "root": {"dontAskAgain":"Don't ask me again","cancel":"Cancel","confirm":"Please confirm","no":"No","yes":"Yes","error":"Error","notification":"Notification"}, "fr-fr":true, "fr-ca":true })
\ No newline at end of file
+define({ "root": {"dontAskAgain":"Don't ask me again","cancel":"Cancel","confirm":"Please confirm","no":"No","yes":"Yes","error":"Error","notification":"Notification","copy":"copied"}, "fr-fr":true, "fr-ca":true })
\ No newline at end of file
Index: src/js/JEditor/FilePanel/Views/CollectionDetailView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/CollectionDetailView.js	(révision 10846)
+++ src/js/JEditor/FilePanel/Views/CollectionDetailView.js	(révision 10847)
@@ -8,6 +8,7 @@
     "JEditor/Commons/Ancestors/Views/ListView",
     "JEditor/Commons/Utils",
     "JEditor/Commons/Files/Models/File",
+    "JEditor/App/Messages/ClipboardModule", 
     "i18n!../nls/i18n",
     //not in params
     "jqueryPlugins/uploader"
@@ -19,8 +20,8 @@
         CollectionDetailView,
         ListView,
         Utils,
-        File
-        ,
+        File,
+        ClipboardModule,
         translate) {
     var CollectionDetailView = ListView.extend({
         selected: {},
@@ -36,6 +37,7 @@
             'click .file .action.delete[data-cid]': 'deleteOne',
             'change input.collectionName': 'setName',
             'uploadercomplete .group-content': 'uploadercomplete',
+           // 'uploadertoobig .group-content': 'uploaderTooBig',
             'click [data-action="showuploader"]': 'showUploader',
             'sortstop .group-content': 'onSortStop',
             'click .shortcode-collection.shortcode' : 'copyToClipboard'
@@ -95,7 +97,7 @@
         render: function() {
             this._super();
             this.dom[this.cid].uploadZone = this.$('.group-content');
-            this.dom[this.cid].uploadZone.uploader({showMenu: false, maxFiles: -1, lang: translate.translations});
+            this.dom[this.cid].uploadZone.uploader({showMenu: false, alertmsg: true, maxFiles: -1, lang: translate.translations});
             this.dom[this.cid].showUploader = this.$('input.upload.button');
 
             this.dom[this.cid].uploadZone.sortable({items: '.menu-wrapper.file'});
@@ -125,6 +127,13 @@
 
             return false;
         },
+        // uploaderTooBig: function(e, data) {
+        //     var msg = data.error;
+        //     this.error({
+        //         title: translate("upload"),
+        //         message: msg
+        //     });
+        // },
         showUploader: function() {
             this.dom[this.cid].showUploader.trigger('click');
         },
@@ -141,32 +150,8 @@
             this.model.save();
         },
         copyToClipboard : function (e){
-            var copyText = $(e.currentTarget).text();
-            const clipboard = navigator.clipboard;
-            if (clipboard !== undefined && clipboard !== "undefined") {
-                navigator.clipboard.writeText(copyText.trim()).then(this.successfully($(e.currentTarget)));
-            } else {
-                if (document.execCommand) {
-                const el = document.createElement("input");
-                el.value = copyText;
-                document.body.append(el);
-
-                el.select();
-                el.setSelectionRange(0, value.length);
-
-                if (document.execCommand("copy")) {
-                    this.successfully();
-                }
-                el.remove();
-                }
-            }
+            ClipboardModule.copyToClipboard(e);
         },
-        successfully :function (el){
-            el.before('<div role="alert" aria-live="polite" style="left: 590px; top: auto;position: absolute;" class="toastcopy"><div class="jq-toast-single jq-icon-success" style="text-align: left; /*! display: none; */"><span class="icon icon-check-circle"></span><span>'+translate("copy")+'</span></div></div>')
-            window.setTimeout(function(){
-                el.parent().find('.toastcopy').remove();
-            }, 2000);
-        }
     });
     Events.extend({
         ListViewEvents: {
Index: src/js/JEditor/FilePanel/Views/FileDetailView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileDetailView.js	(révision 10846)
+++ src/js/JEditor/FilePanel/Views/FileDetailView.js	(révision 10847)
@@ -7,6 +7,7 @@
     "JEditor/Commons/Links/Models/Link",
     "collection!JEditor/Commons/Pages/Models/PageCollection",
     "JEditor/FilePanel/Views/LinkView",
+    "JEditor/App/Messages/ClipboardModule", 
     "i18n!../nls/i18n",
     //not in params
     "jqueryPlugins/dropdown",
@@ -20,6 +21,7 @@
                 Link,
                 PageCollection,
                 LinkView,
+                ClipboardModule,
                 translate
                 ) {
             var FileDetailView = View.extend({
@@ -122,32 +124,8 @@
                     this.model.save();
                 },
                 copyToClipboard : function (e){
-                    var copyText = $(e.currentTarget).text();
-                    const clipboard = navigator.clipboard;
-                    if (clipboard !== undefined && clipboard !== "undefined") {
-                        navigator.clipboard.writeText(copyText).then(this.successfully($(e.currentTarget)));
-                    } else {
-                        if (document.execCommand) {
-                        const el = document.createElement("input");
-                        el.value = copyText;
-                        document.body.append(el);
-
-                        el.select();
-                        el.setSelectionRange(0, value.length);
-
-                        if (document.execCommand("copy")) {
-                            this.successfully();
-                        }
-                        el.remove();
-                        }
-                    }
+                    ClipboardModule.copyToClipboard(e);
                 },
-                successfully :function (el){
-                    el.before('<div role="alert" aria-live="polite" style="left: 75%; top: auto;position: absolute;" class="toastcopy"><div class="jq-toast-single jq-icon-success" style="text-align: left; /*! display: none; */"><span class="icon icon-check-circle"></span><span>'+translate("copy")+'</span></div></div>')
-                    window.setTimeout(function(){
-                        el.parent().find('.toastcopy').remove();
-                    }, 2000);
-                }
             });
             Events.extend({
                 FileDetailManagerViewEvents: {
Index: src/js/JEditor/NavigationPanel/Views/MenuEditView.js
===================================================================
--- src/js/JEditor/NavigationPanel/Views/MenuEditView.js	(révision 10846)
+++ src/js/JEditor/NavigationPanel/Views/MenuEditView.js	(révision 10847)
@@ -13,6 +13,7 @@
   //"JEditor/NavigationPanel/Views/MenuItemEditView",
   "JEditor/NavigationPanel/Views/MenuItemView",
   "collection!JEditor/Commons/Menus/Models/MenuItemCollection",
+  "JEditor/App/Messages/ClipboardModule", 
   "i18n!../nls/i18n",
   //not in params
   "jqueryPlugins/dropdown",
@@ -33,6 +34,7 @@
   MenuItem,
   MenuItemView,
   MenuItemCollection,
+  ClipboardModule,
   translate) {
   var MenuEditView = BabblerView.extend({
     className: 'wrapper scrollable',
@@ -296,32 +298,8 @@
       return this.menuItemView.$el;
     },
     copyToClipboard : function (e){
-      var copyText = $(e.currentTarget).text();
-       var text = copyText.replace('shortcode:', '').trim();
-      const clipboard = navigator.clipboard;
-      if (clipboard !== undefined && clipboard !== "undefined") {
-          navigator.clipboard.writeText(text).then(this.successfully($(e.currentTarget)));
-      } else {
-          if (document.execCommand) {
-          const el = document.createElement("input");
-          el.value = copyText;
-          document.body.append(el);
-
-          el.select();
-          el.setSelectionRange(0, value.length);
-
-          if (document.execCommand("copy")) {
-              this.successfully();
-          }
-          el.remove();
-          }
-      }
-    },
-    successfully :function (el){
-        el.before('<div role="alert" aria-live="polite" style="right: 17%; width="fit-content"; top: auto;position: absolute;" class="toastcopy"><div class="jq-toast-single jq-icon-success" style="text-align: left; /*! display: none; */"><span class="icon icon-check-circle"></span><span>'+translate("copy")+'</span></div></div>')
-        window.setTimeout(function(){
-            el.parent().find('.toastcopy').remove();
-        }, 2000);
+        var removetext = 'shortcode:';
+        ClipboardModule.copyToClipboard(e,removetext);
     }
   });
   Object.defineProperties(MenuEditView.prototype, {
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormBuilderView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormBuilderView.js	(révision 10846)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormBuilderView.js	(révision 10847)
@@ -5,8 +5,9 @@
     "./FieldBuilderView",
     "./FormTemplateList",
     "text!../Templates/formBuilder.html",
+    "JEditor/App/Messages/ClipboardModule", 
     "i18n!../nls/i18n"
-], function(_,View,Field,FieldBuilder,FormTemplateList,formBuilderTemplate,i18n) {
+], function(_,View,Field,FieldBuilder,FormTemplateList,formBuilderTemplate, ClipboardModule, i18n) {
     var FormView = View.extend({
         events: {
             "change input[type=\"radio\"][name=\"col-count\"]":"setColumnCount",
@@ -128,32 +129,8 @@
             this.templateList.render();
         },
         copyToClipboard : function (e){
-            var copyText = $(e.currentTarget).text();
-            const clipboard = navigator.clipboard;
-            if (clipboard !== undefined && clipboard !== "undefined") {
-                navigator.clipboard.writeText(copyText).then(this.successfully($(e.currentTarget)));
-            } else {
-                if (document.execCommand) {
-                const el = document.createElement("input");
-                el.value = copyText;
-                document.body.append(el);
-
-                el.select();
-                el.setSelectionRange(0, value.length);
-
-                if (document.execCommand("copy")) {
-                    this.successfully();
-                }
-                el.remove();
-                }
-            }
+            ClipboardModule.copyToClipboard(e);
         },
-        successfully :function (el){
-            el.before('<div role="alert" aria-live="polite" style="left: 75%; top: auto;position: absolute;" class="toastcopy"><div class="jq-toast-single jq-icon-success" style="text-align: left; /*! display: none; */"><span class="icon icon-check-circle"></span><span>'+i18n("copy")+'</span></div></div>')
-            window.setTimeout(function(){
-                el.parent().find('.toastcopy').remove();
-            }, 2000);
-        }
     });
     return FormView;
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Views/MapOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Views/MapOptionView.js	(révision 10846)
+++ src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Views/MapOptionView.js	(révision 10847)
@@ -6,8 +6,9 @@
     "collection!JEditor/Commons/Addresses/Models/AddressCollection",
     "JEditor/PagePanel/Contents/Blocks/MapBlock/Views/MapPointView",
     "JEditor/Commons/Addresses/Models/Address",
+    "JEditor/App/Messages/ClipboardModule", 
     "i18n!../nls/i18n"
-], function($, addressOptions, Events,  AbstractOptionView, AddressCollection, MapPointView, Address, translate) {
+], function($, addressOptions, Events,  AbstractOptionView, AddressCollection, MapPointView, Address, ClipboardModule, translate) {
     var MapOptionView = AbstractOptionView.extend({
         optionType: 'address',
         events: {
@@ -69,32 +70,8 @@
             return this;
         },
         copyToClipboard : function (e){
-            var copyText = $(e.currentTarget).text();
-            const clipboard = navigator.clipboard;
-            if (clipboard !== undefined && clipboard !== "undefined") {
-                navigator.clipboard.writeText(copyText).then(this.successfully($(e.currentTarget)));
-            } else {
-                if (document.execCommand) {
-                const el = document.createElement("input");
-                el.value = copyText;
-                document.body.append(el);
-
-                el.select();
-                el.setSelectionRange(0, value.length);
-
-                if (document.execCommand("copy")) {
-                    this.successfully();
-                }
-                el.remove();
-                }
-            }
+            ClipboardModule.copyToClipboard(e);
         },
-        successfully :function (el){
-            el.before('<div role="alert" aria-live="polite" style="left: 75%; top: auto;position: absolute;" class="toastcopy"><div class="jq-toast-single jq-icon-success" style="text-align: left; /*! display: none; */"><span class="icon icon-check-circle"></span><span>'+translate("copy")+'</span></div></div>')
-            window.setTimeout(function(){
-                el.parent().find('.toastcopy').remove();
-            }, 2000);
-        }
     });
     return MapOptionView;
 });
\ No newline at end of file
Index: src/js/JEditor/ParamsPanel/Views/MarqueClientView.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 10846)
+++ src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 10847)
@@ -7,6 +7,7 @@
         "JEditor/Commons/Files/Models/File",
         'text!../Templates/MarqueClient.html',
         "JEditor/App/Messages/Confirm", 
+        "JEditor/App/Messages/ClipboardModule", 
         'i18n!../nls/i18n',
         "jqueryPlugins/uploader"
     ], function (
@@ -19,6 +20,7 @@
             File,
             template,
             Confirm,
+            ClipboardModule,
             translate) {
     var MarqueClient = View.extend({
         currentFile: null,
@@ -279,34 +281,7 @@
         },
 
         copyToClipboard : function (e){
-            var copyText = $(e.currentTarget).text();
-            const clipboard = navigator.clipboard;
-            if (clipboard !== undefined && clipboard !== "undefined") {
-                navigator.clipboard.writeText(copyText.trim()).then(this.successfully($(e.currentTarget)));
-            } 
-            else 
-            {
-                if (document.execCommand) 
-                {
-                    const el = document.createElement("input");
-                    el.value = copyText;
-                    document.body.append(el);
-                    el.select();
-                    el.setSelectionRange(0, value.length);
-                    if (document.execCommand("copy")) 
-                    {
-                        this.successfully();
-                    }
-                    el.remove();
-                }
-            }
-        },
-
-        successfully :function (el){
-            el.before('<div role="alert" aria-live="polite" style="top: auto;position: absolute;" class="toastcopy"><div class="jq-toast-single jq-icon-success" style="text-align: left; /*! display: none; */"><span class="icon icon-check-circle"></span><span>'+translate("copy")+'</span></div></div>')
-            window.setTimeout(function(){
-            el.parent().find('.toastcopy').remove();
-            }, 2000);
+            ClipboardModule.copyToClipboard(e);
         }
                 
 
