Revision: r10378
Date: 2023-02-10 10:53:57 +0300 (zom 10 Feb 2023) 
Author: noraja<PERSON><PERSON>lo 

## Commit message
WishList Ideo3.2 :Fix bug First Commit Liste Evoluion File suite r10377

## Files changed

## Full metadata
------------------------------------------------------------------------
r10378 | norajaonarivelo | 2023-02-10 10:53:57 +0300 (zom 10 Feb 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileList.html

WishList Ideo3.2 :Fix bug First Commit Liste Evoluion File suite r10377
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/FilePanel/Templates/fileList.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileList.html	(révision 10377)
+++ src/js/JEditor/FilePanel/Templates/fileList.html	(révision 10378)
@@ -1,5 +1,5 @@
 
-<div class="FileTop">
+ <div class="FileTop">
     <div class="my-files fileList">
         <div class="content scroll-container">
             <div class="group-content <%=content.length===0?'empty':''%>">
@@ -13,17 +13,17 @@
     
                 </div>
                 <!-- file add -->
-    
             </div>
+           
         </div>
     </div>
     
     <div class="warning-msg"><span class="icon-warning" style="color: #d42525;"></span> <span style="color: #d42525;"><%= __("imageWarning")%></span> <%= __("imageWarningMsg")%> <a href="https://www.tineye.com/" target="_blank">TinEye</a>.</div>
 </div>
-
 <div class="my-files fileList">
+   
+    
     <div class="content scroll-container">
-        <div class="group-content <%=content.length===0?'empty':''%>">
 
             <% for(var i=0; i< content.length; i++){
             var file = content[i];
@@ -50,10 +50,6 @@
 
             </div>
             <% } %>
-            <% } %>
-           
-            <!-- file add -->
-
-        </div>
+            <% } %>   
     </div>
 </div>
