Revision: r14259
Date: 2025-05-16 12:31:12 +0300 (zom 16 Mey 2025) 
Author: mpartaux 

## Commit message
update default table

## Files changed

## Full metadata
------------------------------------------------------------------------
r14259 | mpartaux | 2025-05-16 12:31:12 +0300 (zom 16 Mey 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/TableBlock.js

update default table
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/TableBlock.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/TableBlock.js	(révision 14258)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/TableBlock.js	(révision 14259)
@@ -8,7 +8,7 @@
      * @extends Block
      */
      var defaultContent=function(){
-         return '<table border="1" cellpadding="1" cellspacing="1" style="width:100%"><thead><tr><th scope="col">'+TableBlock.i18n('Title')+'</th><th scope="col">'+TableBlock.i18n('Title')+'</th></tr></thead><tbody><tr><td>&nbsp;</td><td>&nbsp;</td></tr><tr><td>&nbsp;</td><td>&nbsp;</td></tr></tbody></table>';
+         return '<table border="1" cellpadding="0" cellspacing="0" style="width:100%" class="table-style1"><thead><tr><th scope="col">'+TableBlock.i18n('Title')+'</th><th scope="col">'+TableBlock.i18n('Title')+'</th></tr></thead><tbody><tr><td>&nbsp;</td><td>&nbsp;</td></tr><tr><td>&nbsp;</td><td>&nbsp;</td></tr></tbody></table>';
      };
     var TableBlock = Block.extend(
             /**
