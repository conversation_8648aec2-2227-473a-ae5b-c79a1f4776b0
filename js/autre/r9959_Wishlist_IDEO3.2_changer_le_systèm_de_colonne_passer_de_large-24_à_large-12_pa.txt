Revision: r9959
Date: 2022-12-08 15:05:14 +0300 (lkm 08 Des 2022) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
Wishlist IDEO3.2: changer le systèm de colonne passer de large-24 à large-12 partie Js

## Files changed

## Full metadata
------------------------------------------------------------------------
r9959 | srazanandralisoa | 2022-12-08 15:05:14 +0300 (lkm 08 Des 2022) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Columns/Models/Column.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Sections/Models/Section.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Sections/Views/SectionView.js
   M /branches/ideo3_v2/integration/src/less/imports/edit.less

Wishlist IDEO3.2: changer le systèm de colonne passer de large-24 à large-12 partie Js
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Columns/Models/Column.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Columns/Models/Column.js	(révision 9958)
+++ src/js/JEditor/PagePanel/Contents/Columns/Models/Column.js	(révision 9959)
@@ -23,7 +23,7 @@
                          * la largeur d'une colonne (nombre<=24)
                          * @type Number
                          */
-                        width: 24,
+                        width: 12,
                         defaults: {contentType: 'column'},
                         /**
                          * Définit les options de bases de la colonne
Index: src/js/JEditor/PagePanel/Contents/Sections/Models/Section.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Sections/Models/Section.js	(révision 9958)
+++ src/js/JEditor/PagePanel/Contents/Sections/Models/Section.js	(révision 9959)
@@ -58,7 +58,7 @@
                                     var columns = this.children;
                                     for (var i = 0; i < this.children.length; i++) {
                                         var col = this.children[i];
-                                        col.width = 24 / this.children.length;
+                                        col.width = 12 / this.children.length;
                                         col.index = i;
                                     }
                                 },
@@ -86,15 +86,15 @@
                                 getMaxColWidth: function() {
                                     switch (this.children.length) {
                                         case 1:
-                                            return 24;
+                                            return 12;
                                         case 2:
-                                            return 18;
+                                            return 9;
                                         case 3:
-                                            return 12;
+                                            return 6;
                                         case 4:
-                                            return 6;
+                                            return 3;
                                         default:
-                                            return 24;
+                                            return 12;
                                     }
                                 },
                                 /**
@@ -103,15 +103,15 @@
                                 getMinColWidth: function() {
                                     switch (this.children.length) {
                                         case 1:
-                                            return 24;
+                                            return 12;
                                         case 2:
-                                            return 6;
+                                            return 3;
                                         case 3:
-                                            return 6;
+                                            return 3;
                                         case 4:
-                                            return  6;
+                                            return  3;
                                         default:
-                                            return 24;
+                                            return 12;
                                     }
                                 },
                                 translate: translate
Index: src/js/JEditor/PagePanel/Contents/Sections/Views/SectionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Sections/Views/SectionView.js	(révision 9958)
+++ src/js/JEditor/PagePanel/Contents/Sections/Views/SectionView.js	(révision 9959)
@@ -248,7 +248,7 @@
                         },
                         /**
                          * Appellée quand les colonnes ont été redimensionnées
-                         * calcule les nouvelles tailles (max=24)
+                         * calcule les nouvelles tailles (max=12)
                          * @todo revoir l'algo de cette méthode
                          * @private
                          */
@@ -257,7 +257,7 @@
                             var targetSize = ui.size.width;
                             var nextCols = this.options.nextCols;
                             var availableWidth = this.options.width;
-                            var scale = availableWidth / 24;
+                            var scale = availableWidth / 12;
                             var givenWidth = 0;
                             var minWidth = this.model.getMinColWidth();
                             var maxWidth = this.model.getMaxColWidth();
@@ -275,8 +275,8 @@
                                 var width = Math.round(col.width() / scale);
                                 width = width >= maxWidth ? maxWidth : width;
                                 width = width <= minWidth ? minWidth : width;
-                                if (index === self.dom[self.cid].cols.length - 1 && (24 - (givenWidth + width)) > 0) {
-                                    width = 24 - givenWidth;
+                                if (index === self.dom[self.cid].cols.length - 1 && (12 - (givenWidth + width)) > 0) {
+                                    width = 12 - givenWidth;
                                 }
                                 col.attr("style", '');
                                 var model = self.byModelCID[col.data('cid')].model;
@@ -315,7 +315,7 @@
                             }
                             var nextColumn = $target.nextAll('.col:first');
                             var nextWidth = 0 + nextColumn.width();
-                            var maxWidth = $target.width() + (nextWidth - (this.model.getMinColWidth() * this.options.width / 24));
+                            var maxWidth = $target.width() + (nextWidth - (this.model.getMinColWidth() * this.options.width / 12));
                             this.options.nextCols = $target.nextAll('.col');
                             this.options.nextColumnOriginalWidth = nextWidth;
                             this.options.nextColWidth = [];
@@ -325,7 +325,7 @@
                             });
                             this.options.siblingCols = $target.siblings('.col');
                             $target.resizable('option', 'maxWidth', maxWidth);
-                            $target.resizable('option', 'minWidth', this.model.getMinColWidth() * this.options.width / 24);
+                            $target.resizable('option', 'minWidth', this.model.getMinColWidth() * this.options.width / 12);
                         },
                         translate:translate
                     });
Index: src/less/imports/edit.less
===================================================================
--- src/less/imports/edit.less	(révision 9958)
+++ src/less/imports/edit.less	(révision 9959)
@@ -223,31 +223,25 @@
             width:100%;
             .clearfix();
             &[data-child-count="4"]{
-                .col[data-width="6"], .col.width-6{width:23.75%;}
+                .col[data-width="3"], .col.width-3{width:23.75%;}
             }
             &[data-child-count="3"]{
-                .col[data-width="6"], .col.width-6{width:24.00%;}
-                .col[data-width="7"], .col.width-7{width:28.00%;}
-                .col[data-width="8"], .col.width-8{width:32.00%;}
-                .col[data-width="9"], .col.width-9{width:36.00%;}
-                .col[data-width="10"], .col.width-10{width:40.00%;}
-                .col[data-width="11"], .col.width-11{width:44.00%;}
-                .col[data-width="12"], .col.width-12{width:48.00%;}
+                //96
+                .col[data-width="3"], .col.width-3{width:24.00%;}
+                .col[data-width="4"], .col.width-4{width:32.00%;}
+                .col[data-width="5"], .col.width-5{width:40.00%;}
+                .col[data-width="6"], .col.width-6{width:48.00%;}
+
             }
             &[data-child-count="2"]{
-                .col[data-width="6"], .col.width-6{width:24.25%;}
-                .col[data-width="7"], .col.width-7{width:28.29%;}
-                .col[data-width="8"], .col.width-8{width:32.33%;}
-                .col[data-width="9"], .col.width-9{width:36.38%;}
-                .col[data-width="10"], .col.width-10{width:40.42%;}
-                .col[data-width="11"], .col.width-11{width:44.46%;}
-                .col[data-width="12"], .col.width-12{width:48.50%;}
-                .col[data-width="13"], .col.width-13{width:52.54%;}
-                .col[data-width="14"], .col.width-14{width:56.58%;}
-                .col[data-width="15"], .col.width-15{width:60.63%;}
-                .col[data-width="16"], .col.width-16{width:64.67%;}
-                .col[data-width="17"], .col.width-17{width:68.71%;}
-                .col[data-width="18"], .col.width-18{width:72.75%;}
+                //97
+                .col[data-width="3"], .col.width-3{width:24.38%;}
+                .col[data-width="4"], .col.width-4{width:32.42%;}
+                .col[data-width="5"], .col.width-5{width:40.46%;}
+                .col[data-width="6"], .col.width-6{width:48.50%;}
+                .col[data-width="7"], .col.width-7{width:56.54%;}
+                .col[data-width="8"], .col.width-8{width:64.58%;}
+                .col[data-width="9"], .col.width-9{width:72.62%;}
             }
             &[data-child-count="1"]{
                 .col{width:98%;}
Index: src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js	(révision 9958)
+++ src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js	(révision 9959)
@@ -130,7 +130,7 @@
                                  */
                                 addToCurrentContainer: function (event) {
                                     var clone = $(event.currentTarget).data('model').clone();
-                                    var section = new Section({columns: [new Column({width: 24, blocks: [clone]})]});
+                                    var section = new Section({columns: [new Column({width: 12, blocks: [clone]})]});
                                     // check if zone have popup 
                                     var isPopup = false;
                                     var sections = this.pagePanel.currentZone.attributes.sections;
