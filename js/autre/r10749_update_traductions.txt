Revision: r10749
Date: 2023-03-28 15:51:58 +0300 (tlt 28 Mar 2023) 
Author: mpartaux 

## Commit message
update traductions

## Files changed

## Full metadata
------------------------------------------------------------------------
r10749 | mpartaux | 2023-03-28 15:51:58 +0300 (tlt 28 Mar 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/i18n.js

update traductions
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js	(révision 10748)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js	(révision 10749)
@@ -35,7 +35,7 @@
     "selectTypeLink"        :   "Selectionnez le type de lien souhaitée",
     "LinkImage"             :   "Ajouter le lien sur l'image",
     "LinkText"              :   "Ajouter le lien sur le texte",
-    "ButtonReadMore"        :   "Ajouter un bouton 'en savoir plus'",
+    "ButtonReadMore"        :   "Ajouter un bouton 'Consulter la page'",
     "carrouselHeight"       :   "Nombre d'image",
     "carrouselHeightDesc"   :   "Glissez pour ajuster le nombre d'images affichées",
     "carrouselStyleAffichage"   :   "Style des images",
@@ -47,7 +47,7 @@
     'square'                :   "Carré",
     "arrowImage"            :   "Afficher des flèches de navigation",
     "ShowArrow"             :   "Afficher les boutons de navigation",
-    "emptyCollection":"Votre collection d’images est vide.",
+    "emptyCollection"       :   "Votre collection d’images est vide.",
     "DescStyle1"           :  "Textes sous l'image", 
     "DescStyle2"           :  "Texte encadré sur l'image",
     "DescStyle3"           :  "Texte sur l'image",
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js	(révision 10748)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js	(révision 10749)
@@ -39,7 +39,7 @@
        "selectTypeLink"        :   "Select the type of link you want",
        "LinkImage"             :   "Add link to image",
        "LinkText"              :   "Add link to text",
-       "ButtonReadMore"        :   "Add a button 'read more'",
+       "ButtonReadMore"        :   "Add a button 'Visit the page'",
        "carrouselHeight"       :   "Number of images",
        "carrouselHeightDesc"   :   "Drag to adjust the number of images displayed",
        "carrouselStyleAffichage"   :    "Style of the images",
@@ -51,7 +51,7 @@
        'square'                :   "Square",
        "arrowImage"            :   "Display navigation arrows",
        "ShowArrow"             :   "Show navigation buttons",
-       "emptyCollection"      :"Your image collection is empty",
+       "emptyCollection"      :  "Your image collection is empty",
        "DescStyle1"           :  "Text under the image", 
        "DescStyle2"           :  "Text framed on the image",
        "DescStyle3"           :  "Text on the image",
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-ca/i18n.js	(révision 10748)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-ca/i18n.js	(révision 10749)
@@ -41,7 +41,7 @@
         "selectTypeLink"        :   "Selectionnez le type de lien souhaitée",
         "LinkImage"             :   "Ajouter le lien sur l'image",
         "LinkText"              :   "Ajouter le lien sur le texte",
-        "ButtonReadMore"        :   "Ajouter un bouton 'en savoir plus'",
+        "ButtonReadMore"        :   "Ajouter un bouton 'Consulter la page'",
         'landscape'             :   "Paysage",
         'portrait'              :   "Portrait",
         'square'                :   "Carré",
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-fr/i18n.js	(révision 10748)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-fr/i18n.js	(révision 10749)
@@ -41,7 +41,7 @@
         "selectTypeLink"        :   "Selectionnez le type de lien souhaitée",
         "LinkImage"             :   "Ajouter le lien sur l'image",
         "LinkText"              :   "Ajouter le lien sur le texte",
-        "ButtonReadMore"        :   "Ajouter un bouton 'en savoir plus'",
+        "ButtonReadMore"        :   "Ajouter un bouton 'Consulter la page'",
         'landscape'             :   "Paysage",
         'portrait'              :   "Portrait",
         'square'                :   "Carré",
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/i18n.js	(révision 10748)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/i18n.js	(révision 10749)
@@ -46,7 +46,7 @@
         "selectTypeLink"        :   "Select the type of link you want",
         "LinkImage"             :   "Add link to image",
         "LinkText"              :   "Add link to text",
-        "ButtonReadMore"        :   "Add a button 'read more'",
+        "ButtonReadMore"        :   "Add a button 'Visit the page'",
         "gridOption"            :       "Grid",
         "gridBlockOption"       :       "Grid options",
         "gridStyleOption"       :       "Style",
