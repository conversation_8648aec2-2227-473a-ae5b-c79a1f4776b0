Revision: r10313
Date: 2023-02-02 17:42:26 +0300 (lkm 02 Feb 2023) 
Author: anthony 

## Commit message
clean ckeditor custom style

## Files changed

## Full metadata
------------------------------------------------------------------------
r10313 | anthony | 2023-02-02 17:42:26 +0300 (lkm 02 Feb 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js

clean ckeditor custom style
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 10312)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 10313)
@@ -41,13 +41,7 @@
                             skin: 'ideo',
                             fontSize_sizes: 'tiny/0.75em;small/0.875em;large/1.25em;xlarge/1.5em;xxlarge/2em;xxxlarge/2.5em',
                             stylesSet: [
-                                // Block-level styles
-                                { name: 'Blue Title', element: 'h2', styles: { 'color': 'Blue' } },
-                                { name: 'Red Title' , element: 'h3', styles: { 'color': 'Red' } },
-                            
-                                // Inline styles
-                                { name: 'CSS Style', element: 'span', attributes: { 'class': 'my_style' } },
-                                { name: 'Marker: Yellow', element: 'span', styles: { 'background-color': 'Yellow' } }
+                                { name: 'CSS Style', element: 'span', attributes: { 'class': 'my_style' } }
                             ],
                             forcePasteAsPlainText: true,
                             pasteFromWordRemoveFontStyles: true,
