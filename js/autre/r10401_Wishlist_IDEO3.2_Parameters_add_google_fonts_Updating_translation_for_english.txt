Revision: r10401
Date: 2023-02-14 14:48:46 +0300 (tlt 14 Feb 2023) 
Author: jn.harison 

## Commit message
Wishlist IDEO3.2:Parameters:add google fonts:Updating translation for english

## Files changed

## Full metadata
------------------------------------------------------------------------
r10401 | jn.harison | 2023-02-14 14:48:46 +0300 (tlt 14 Feb 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/i18n.js

Wishlist IDEO3.2:Parameters:add google fonts:Updating translation for english
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 10400)
+++ src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 10401)
@@ -65,9 +65,9 @@
         "confirmDelete": "Vous êtes sur le point de supprimer<br/>définitivement l'élément :</br><strong><% name %></strong>",
         "fontsGoogle": "Google Fonts",
         "set_fonts_google": "Ajouter l\'import de google fonts au format &lt;link&gt;<br/>Toutes les fonts doivent être regroupées dans un même &lt;link&gt; <br/>Les deux &lt;link rel=\"preconnect\"... \"\"&gt; fournit sur Google Fonts sont obligatoires",
-        "Multiple_FontsGoogleApi": "Plusieurs fonts google détéctés",
+        "Multiple_FontsGoogleApi": "Plusieurs scripts <strong>&lt;link rel=\"preconnect\" href=\"https\:\/\/fonts.googleapis.com\"\&gt;</strong> détéctés",
         "Missing_FontsGoogleApi": "Script <strong>&lt;link rel=\"preconnect\" href=\"https\:\/\/fonts.googleapis.com\"\&gt;</strong>  manquant",
-        "Multiple_FontsGoogleStatic": "Multiple scripts <strong>&lt;link rel=\"preconnect\" href=\"https\:\/\/fonts.gstastic.com\" crossorigin\&gt;</strong> détectés",
+        "Multiple_FontsGoogleStatic": "Plusieurs scripts <strong>&lt;link rel=\"preconnect\" href=\"https\:\/\/fonts.gstastic.com\" crossorigin\&gt;</strong> détectés",
         "Missing_FontsGoogleStatic": "Script <strong>&lt;link rel=\"preconnect\" href=\"https\:\/\/fonts.gstatic.com\" crossorigin\&gt;</strong>  manquant",
         "Multiple_FontsGoogleApiSwap": "Plusieurs scripts <strong>&lt;link href=\"https\:\/\/fonts.googleapis.com\/css2\?...\"\&gt;</strong> avec l\'option display swap détectés",
         "Missing_FontsGoogleApiSwap": "Script  <strong>&lt;link href=\"https\:\/\/fonts.googleapis.com\/css2\?...\"\&gt;</strong> avec l\'option display swap manquant"
Index: src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 10400)
+++ src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 10401)
@@ -68,9 +68,9 @@
         "confirmDelete": "Vous êtes sur le point de supprimer<br/>définitivement l'élément :</br><strong><% name %></strong>",
         "fontsGoogle": "Google Fonts",
         "set_fonts_google": "Ajouter l\'import de google fonts au format &lt;link&gt;<br/>Toutes les fonts doivent être regroupées dans un même &lt;link&gt; <br/>Les deux &lt;link rel=\"preconnect\"... \"\"&gt; fournit sur Google Fonts sont obligatoires",
-        "Multiple_FontsGoogleApi": "Plusieurs fonts google détéctés",
+        "Multiple_FontsGoogleApi": "Plusieurs scripts <strong>&lt;link rel=\"preconnect\" href=\"https\:\/\/fonts.googleapis.com\"\&gt;</strong> détéctés",
         "Missing_FontsGoogleApi": "Script <strong>&lt;link rel=\"preconnect\" href=\"https\:\/\/fonts.googleapis.com\"\&gt;</strong>  manquant",
-        "Multiple_FontsGoogleStatic": "Multiple scripts <strong>&lt;link rel=\"preconnect\" href=\"https\:\/\/fonts.gstastic.com\" crossorigin\&gt;</strong> détectés",
+        "Multiple_FontsGoogleStatic": "Plusieurs scripts <strong>&lt;link rel=\"preconnect\" href=\"https\:\/\/fonts.gstastic.com\" crossorigin\&gt;</strong> détectés",
         "Missing_FontsGoogleStatic": "Script <strong>&lt;link rel=\"preconnect\" href=\"https\:\/\/fonts.gstatic.com\" crossorigin\&gt;</strong>  manquant",
         "Multiple_FontsGoogleApiSwap": "Plusieurs scripts <strong>&lt;link href=\"https\:\/\/fonts.googleapis.com\/css2\?...\"\&gt;</strong> avec l\'option display swap détectés",
         "Missing_FontsGoogleApiSwap": "Script  <strong>&lt;link href=\"https\:\/\/fonts.googleapis.com\/css2\?...\"\&gt;</strong> avec l\'option display swap manquant"
Index: src/js/JEditor/ParamsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 10400)
+++ src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 10401)
@@ -70,13 +70,13 @@
         "confirmDelete": "You are about to definitely <br/>suppress the element :</br><strong><% name %></strong>",
         "fontsGoogle": "Google Fonts",
         "set_fonts_google": "Add google fonts import in \<link\> format<br/>All fonts must be grouped in the same \<link\> <br/>The two \<link rel=\"preconnect\"... = \"\" \> rovided on Google Fonts are mandatory",
-        "Multiple_FontsGoogleApi": "Several google fonts detected",
-        "Missing_FontsGoogleApi": "Missing google font",
-        "Multiple_FontsGoogleStatic": "Several google static fonts detected",
-        "Missing_FontsGoogleStatic": "Missing google static font",
-        "Multiple_FontsGoogleApiSwap": "Several Font google API with display swap option detected",
-        "Missing_FontsGoogleApiSwap": "Font google API with missing display swap option"
-
+        "Multiple_FontsGoogleApi": "Several <strong>&lt;link rel=\"preconnect\" href=\"https\:\/\/fonts.googleapis.com\"\&gt;</strong> scripts detected",
+        "Missing_FontsGoogleApi": "Missing <strong>&lt;link rel=\"preconnect\" href=\"https\:\/\/fonts.googleapis.com\"\&gt;</strong>",
+        "Multiple_FontsGoogleStatic": "Several  <strong>&lt;link rel=\"preconnect\" href=\"https\:\/\/fonts.gstastic.com\" crossorigin\&gt;</strong> scripts detected",
+        "Missing_FontsGoogleStatic": "Missing <strong>&lt;link rel=\"preconnect\" href=\"https\:\/\/fonts.gstatic.com\" crossorigin\&gt;</strong>",
+        "Multiple_FontsGoogleApiSwap": "Several <strong>&lt;link href=\"https\:\/\/fonts.googleapis.com\/css2\?...\"\&gt;</strong> with display swap option detected",
+        "Missing_FontsGoogleApiSwap": "Missing <strong>&lt;link href=\"https\:\/\/fonts.googleapis.com\/css2\?...\"\&gt;</strong> with display swap missing"
+    
     },
     "fr-fr": true,
     "fr-ca": true
