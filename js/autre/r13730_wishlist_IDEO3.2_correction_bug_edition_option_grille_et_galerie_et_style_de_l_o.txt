Revision: r13730
Date: 2025-01-22 10:00:41 +0300 (lrb 22 Jan 2025) 
Author: sraz<PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: correction bug edition option grille et galerie et style de l'option pour changer le texte des boutons

## Files changed

## Full metadata
------------------------------------------------------------------------
r13730 | srazanandralisoa | 2025-01-22 10:00:41 +0300 (lrb 22 Jan 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/OptionTypeLienView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieOptionTypeLien.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieOptionTypeLienView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridOptionTypeLien.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridOptionTypeLienView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridOptionView.js
   M /branches/ideo3_v2/integration/src/less/imports/components.less

wishlist IDEO3.2: correction bug edition option grille et galerie et style de l'option pour changer le texte des boutons
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/OptionTypeLienView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/OptionTypeLienView.js	(révision 13729)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/OptionTypeLienView.js	(révision 13730)
@@ -8,7 +8,6 @@
 ], function($, CarrouselOptionTypeLien, Events, View, translate) {
     var OptionTypeLienView = View.extend({
         events: {
-            'change input[name="LinkType"]': '_onLinkTypeChnage',
             'change input[name="ButtonText"]': '_onButtonTextChnage',
         },
         initialize: function() {
@@ -25,17 +24,12 @@
             var $target = $(event.currentTarget);
             this.options.ButtonText = $target.val();
         },
-        hideOrShowButton: function (){
-            if(this.options.TypeLien == 3){
+        hideOrShowButton: function (TypeLien){
+            if(TypeLien == 3){
                 this.$('.button-text').show();
              }
              else this.$('.button-text').hide();
         },
-        _onLinkTypeChnage: function(event){
-            var $target = $(event.currentTarget);
-            this.options.TypeLien = $target.val();
-            this.hideOrShowButton();
-        },
         render: function() {
             this.BouttonMoreInfo=false;
             this.LinkText=false;
@@ -61,7 +55,7 @@
             this.undelegateEvents();
             this.$el.empty();
             this.$el.html(this._template({TypeLien: TypeLien,LinkText:this.LinkText,ButtonText:this.options.ButtonText,BouttonMoreInfo:this.BouttonMoreInfo}));
-            this.hideOrShowButton();
+            this.hideOrShowButton(TypeLien);
             this.delegateEvents();
             return this;
         },
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieOptionTypeLien.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieOptionTypeLien.html	(révision 13729)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieOptionTypeLien.html	(révision 13730)
@@ -1,7 +1,7 @@
 <p class="panel-legend"><%=__("selectTypeLink")%></p>
-<% var _id= "link_type1" %>
-<input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkType" value="1"  <%=(TypeLien==1)?"checked":""%> />
-<label  class="block-label" for="<%= _id %>" >
+<% var _id1=_.uniqueId("linkgalerie_type1") %>
+<input id="<%= _id1 %>" class="field-input  for--block-label" type="radio" name="LinkType" value="1"  <%=(TypeLien==1)?"checked":""%> />
+<label  class="block-label" for="<%= _id1 %>" >
     <div class="radio-wrapper">
     <span class="icon  icon-radio-active"></span>
     <span class="icon  icon-radio-inactive"></span>
@@ -9,9 +9,9 @@
     <div class="block-label-radio"><%= __("LinkImage")%></div>
 
 </label>
-<% var _id= "link_type2" %>
-<input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkType" value="2"  <%=(TypeLien==2)?"checked":""%>/>
-<label  class="block-label <%=(LinkText)?'carrouselTypeLinkDisable':''%>" for="<%= _id %>" >
+<% var _id2=_.uniqueId("linkgalerie_type2")  %>
+<input id="<%= _id2 %>" class="field-input  for--block-label" type="radio" name="LinkType" value="2"  <%=(TypeLien==2)?"checked":""%>/>
+<label  class="block-label <%=(LinkText)?'carrouselTypeLinkDisable':''%>" for="<%= _id2 %>" >
     <div class="radio-wrapper">
     <span class="icon  icon-radio-active"></span>
     <span class="icon  icon-radio-inactive"></span>
@@ -19,9 +19,9 @@
     <div class="block-label-radio"><%= __("LinkText")%></div>
 
 </label>
-<% var _id= "link_type3" %>
-<input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkType" value="3"  <%=(TypeLien==3)?"checked":""%> />
-<label  class="block-label <%=(BouttonMoreInfo)?'carrouselTypeLinkDisable':''%>" for="<%= _id %>" >
+<% var _id3=_.uniqueId("linkgalerie_type3")  %>
+<input id="<%= _id3 %>" class="field-input  for--block-label" type="radio" name="LinkType" value="3"  <%=(TypeLien==3)?"checked":""%> />
+<label  class="block-label <%=(BouttonMoreInfo)?'carrouselTypeLinkDisable':''%>" for="<%= _id3 %>" >
     <div class="radio-wrapper">
     <span class="icon  icon-radio-active"></span>
     <span class="icon  icon-radio-inactive"></span>
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieOptionTypeLienView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieOptionTypeLienView.js	(révision 13729)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieOptionTypeLienView.js	(révision 13730)
@@ -12,7 +12,6 @@
      */
     var galerieOptionTypeLienView = View.extend({
         events: {
-            'change input[name="LinkType"]': '_onLinkTypeChnage',
             'change input[name="ButtonText"]': '_onButtonTextChnage',
         },
         initialize: function() {
@@ -29,17 +28,12 @@
             var $target = $(event.currentTarget);
             this.options.ButtonText = $target.val();
         },
-        hideOrShowButton: function (){
-            if(this.options.galerieTypeLink == 3){
+        hideOrShowButton: function (TypeLink){
+            if(TypeLink == 3){
                 this.$('.button-text').show();
-                }
-                else this.$('.button-text').hide();
+            }
+            else this.$('.button-text').hide();
         },
-        _onLinkTypeChnage: function(event){
-            var $target = $(event.currentTarget);
-            this.options.galerieTypeLink = $target.val();
-            this.hideOrShowButton();
-        },
         render: function() {
             this.BouttonMoreInfo=false;
             this.LinkText=false;
@@ -51,6 +45,7 @@
                 this.LinkText=true;
                 if(TypeLien ==2){
                     TypeLien =1;
+                    this.trigger("ChangeTypeLien",TypeLien);
                 }
             }
             if(Action == 1){
@@ -57,6 +52,7 @@
                 this.BouttonMoreInfo =true;
                 if((TypeLien ==3) ||(TypeLien== 2 && Info==0)){
                     TypeLien =1;
+                    this.trigger("ChangeTypeLien",TypeLien);
                 }
             }
                 
@@ -63,7 +59,7 @@
             this.undelegateEvents();
             this.$el.empty();
             this.$el.html(this._template({TypeLien: TypeLien,LinkText:this.LinkText,ButtonText:this.options.ButtonText,BouttonMoreInfo:this.BouttonMoreInfo}));
-            this.hideOrShowButton();
+            this.hideOrShowButton(TypeLien);
             this.delegateEvents();
             return this;
         },
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieOptionView.js	(révision 13729)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieOptionView.js	(révision 13730)
@@ -28,7 +28,12 @@
             this.template = this.buildTemplate(template, translate);
             this.GalerieOptionTypeLien = new GalerieOptionTypeLienView(this.model);
             this.galerieField = new GalerieFieldView({model:this.model.galerie});
+            this.listenTo(this.GalerieOptionTypeLien,'ChangeTypeLien',this.ChangeGalerieTypeLien);
         },
+        ChangeGalerieTypeLien:function(TypeLien){
+            console.log(TypeLien);
+            this.model.galerieTypeLink = TypeLien;
+        },
         onAddClick: function(event) {
             this.galerieField.onAddClick(event);
         },
@@ -70,6 +75,7 @@
                     this.GalerieOptionTypeLien.render();
                 }else if(name === "LinkType"){
                     this.model.galerieTypeLink=parseInt(event.currentTarget.value);
+                    this.GalerieOptionTypeLien.render();
                 }
                 this.DeleteNameInputOnMoodel();
             },
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridOptionTypeLien.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridOptionTypeLien.html	(révision 13729)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridOptionTypeLien.html	(révision 13730)
@@ -1,7 +1,7 @@
 <p class="panel-legend"><%=__("selectTypeLink")%></p>
-<% var _id= "link_type1" %>
-<input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkType" value="1"  <%=(TypeLien==1)?"checked":""%> />
-<label  class="block-label" for="<%= _id %>" >
+<% var _id1=_.uniqueId("linkgrid_type1") %>
+<input id="<%= _id1 %>" class="field-input  for--block-label" type="radio" name="LinkType" value="1"  <%=(TypeLien==1)?"checked":""%> />
+<label  class="block-label" for="<%= _id1 %>" >
     <div class="radio-wrapper">
     <span class="icon  icon-radio-active"></span>
     <span class="icon  icon-radio-inactive"></span>
@@ -9,9 +9,9 @@
     <div class="block-label-radio"><%= __("LinkImage")%></div>
 
 </label>
-<% var _id= "link_type2" %>
-<input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkType" value="2"  <%=(TypeLien==2)?"checked":""%>/>
-<label  class="block-label <%=(LinkText)?'carrouselTypeLinkDisable':''%>" for="<%= _id %>" >
+<% var _id2=_.uniqueId("linkgrid_type2") %>
+<input id="<%= _id2 %>" class="field-input  for--block-label" type="radio" name="LinkType" value="2"  <%=(TypeLien==2)?"checked":""%>/>
+<label  class="block-label <%=(LinkText)?'carrouselTypeLinkDisable':''%>" for="<%= _id2 %>" >
     <div class="radio-wrapper">
     <span class="icon  icon-radio-active"></span>
     <span class="icon  icon-radio-inactive"></span>
@@ -19,9 +19,9 @@
     <div class="block-label-radio"><%= __("LinkText")%></div>
 
 </label>
-<% var _id= "link_type3" %>
-<input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkType" value="3"  <%=(TypeLien==3)?"checked":""%> />
-<label  class="block-label <%=(BouttonMoreInfo)?'carrouselTypeLinkDisable':''%>" for="<%= _id %>" >
+<% var _id3=_.uniqueId("linkgrid_type3") %>
+<input id="<%= _id3 %>" class="field-input  for--block-label" type="radio" name="LinkType" value="3"  <%=(TypeLien==3)?"checked":""%> />
+<label  class="block-label <%=(BouttonMoreInfo)?'carrouselTypeLinkDisable':''%>" for="<%= _id3 %>" >
     <div class="radio-wrapper">
     <span class="icon  icon-radio-active"></span>
     <span class="icon  icon-radio-inactive"></span>
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridOptionTypeLienView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridOptionTypeLienView.js	(révision 13729)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridOptionTypeLienView.js	(révision 13730)
@@ -8,7 +8,6 @@
 ], function($, gridOptionTypeLien, Events, View, translate) {
     var gridOptionTypeLienView = View.extend({
         events: {
-            'change input[name="LinkType"]': '_onLinkTypeChnage',
             'change input[name="ButtonText"]': '_onButtonTextChnage',
         },
         initialize: function() {
@@ -25,17 +24,12 @@
             var $target = $(event.currentTarget);
             this.options.ButtonText = $target.val();
         },
-        hideOrShowButton: function (){
-            if(this.options.grilleTypeLink == 3){
+        hideOrShowButton: function (TypeLien){
+            if(TypeLien == 3){
                 this.$('.button-text').show();
                 }
                 else this.$('.button-text').hide();
         },
-        _onLinkTypeChnage: function(event){
-            var $target = $(event.currentTarget);
-            this.options.grilleTypeLink = $target.val();
-            this.hideOrShowButton();
-        },
         render: function() {
             this.BouttonMoreInfo=false;
             this.LinkText=false;
@@ -47,6 +41,7 @@
                 this.LinkText=true;
                 if(TypeLien ==2){
                     TypeLien =1;
+                    this.trigger("ChangeTypeLien",TypeLien);
                 }
             }
             if(Action == 1){
@@ -53,6 +48,7 @@
                 this.BouttonMoreInfo =true;
                 if((TypeLien ==3) ||(TypeLien== 2 && Info==0)){
                     TypeLien =1;
+                    this.trigger("ChangeTypeLien",TypeLien);
                 }
             }
                 
@@ -59,7 +55,7 @@
             this.undelegateEvents();
             this.$el.empty();
             this.$el.html(this._template({TypeLien: TypeLien,LinkText:this.LinkText,ButtonText:this.options.ButtonText,BouttonMoreInfo:this.BouttonMoreInfo}));
-            this.hideOrShowButton();
+            this.hideOrShowButton(TypeLien);
             this.delegateEvents();
             return this;
         },
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridOptionView.js	(révision 13729)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridOptionView.js	(révision 13730)
@@ -31,7 +31,12 @@
                 this.template = this.buildTemplate(gridOption, this.translate);
                 this.GridOptionTypeLien =new GridOptionTypeLienView(this.model);
                 this.listenTo(this.model, 'change:fileGroup', this.onFileGroupChange);
+                this.listenTo(this.GridOptionTypeLien,'ChangeTypeLien',this.ChangeGridTypeLien);
             },
+            ChangeGridTypeLien:function(TypeLien){
+                console.log(TypeLien);
+                this.model.grilleTypeLink = TypeLien;
+            },
             _selectZoomOnClic: function(event) {
                 this.model.zoom = !this.model.zoom;
             },
@@ -70,6 +75,7 @@
                     this.GridOptionTypeLien.render();
                 }else if(name === "LinkType"){
                     this.model.grilleTypeLink=parseInt(event.currentTarget.value);
+                    this.GridOptionTypeLien.render();
                 }
                 this.DeleteNameInputOnMoodel();
             },
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js	(révision 13729)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js	(révision 13730)
@@ -133,6 +133,7 @@
                     this.CarrouselOptionTypeLien.render();
                 }else if(name === "LinkType"){
                     this.model.TypeLien= event.currentTarget.value;
+                    this.CarrouselOptionTypeLien.render();
                 }else if(name === "Arrow"){
                     var value = event.currentTarget.value;
                     if (this.model.Arrow == value ) {
Index: src/less/imports/components.less
===================================================================
--- src/less/imports/components.less	(révision 13729)
+++ src/less/imports/components.less	(révision 13730)
@@ -262,4 +262,28 @@
     &:last-child {
         border-bottom: none;
     }
-}
\ No newline at end of file
+}
+.button-text{
+    background-color: #666;
+    opacity: .5;
+    min-height: 85px;
+    padding: .5em .9em;
+    border-radius: 4px;
+    .panel-content-legend{
+        margin-left: 10px;
+        color: #fff !important;
+        margin-top: 8px;
+        font-size: 0.9em !important;
+    }
+    .buttonText-input{
+        width: 315px;
+        margin-left: 10px;
+        padding: .5em .9em;
+        border: none;
+        outline: none;
+        background-color: #000;
+        color: #999;
+        border-radius: 4px;
+        background-clip: padding-box;
+    }
+}
