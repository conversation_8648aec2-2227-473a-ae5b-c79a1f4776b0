Revision: r10843
Date: 2023-04-19 16:26:34 +0300 (lrb 19 Apr 2023) 
Author: mpartaux 

## Commit message
update color class names + panel-menu styles

## Files changed

## Full metadata
------------------------------------------------------------------------
r10843 | mpartaux | 2023-04-19 16:26:34 +0300 (lrb 19 Apr 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/less/imports/page_panel/module/block-options/advanced-option.less
   M /branches/ideo3_v2/integration/src/less/imports/panel.less

update color class names + panel-menu styles
------------------------------------------------------------------------

## Diff
Index: src/less/imports/page_panel/module/block-options/advanced-option.less
===================================================================
--- src/less/imports/page_panel/module/block-options/advanced-option.less	(révision 10842)
+++ src/less/imports/page_panel/module/block-options/advanced-option.less	(révision 10843)
@@ -11,45 +11,70 @@
 		color: @greyM;
 	}
 }
-.Surface0{
+
+.effect-radio {
+	[class^='surface'],
+	[class^='accent'],
+	[class^='lead'] {
+		display: block;
+		height: 100%;
+		border-radius: 4px;
+		overflow: hidden;
+		position: relative;
+	}
+
+	.container { // default color button
+		background: var(--surface0)!important;
+		color: var(--fg-color)!important;
+	}
+}
+
+span.icon-form-long_text {
+	font-size: 2rem;
+	display: inline-block;
+	margin-top: 0.75rem;
+}
+
+
+.surface0{
 	background: var(--surface0);
 	color: var(--fg-color);
 } 
-.Surface1 {
+.surface1 {
 	background: var(--surface1);
 	color: var(--fg-color);
 }
-.Surface2 {
+.surface2 {
  	background: var(--surface2); 
 	color: var(--fg-color);
 }	
-.Surface3 {
+.surface3 {
 	background: var(--surface3); 
 	color: var(--fg-color);
 }
-.Surface4 {
+.surface4 {
 	background: var(--surface4); 
 	color: var(--fg-color);
 } 
-.Surface5 {
+.surface5 {
 	background: var(--surface5); 
 	color: var(--fg-color);
 } 
 
-.Accent0 {
+.accent0 {
 	background: var(--accent-surface0); 
 	color: var(--accent-text0);
 } 
 
-.Accent1 {
+.accent1 {
 	background: var(--accent-surface1); 
 	color: var(--accent-text1);
 } 
-.Lead0 {
+.lead0 {
 	background: var(--lead-surface0); 
 	color: var(--lead-text0);
 } 
-.Lead1 {
+.lead1 {
 	background: var(--lead-surface1); 
 	color: var(--lead-text1);
 } 
\ No newline at end of file
Index: src/less/imports/panel.less
===================================================================
--- src/less/imports/panel.less	(révision 10842)
+++ src/less/imports/panel.less	(révision 10843)
@@ -345,13 +345,16 @@
 ///menu
 nav.panel-menu{
     &.inactive{visibility:hidden;}
-    padding:0;
+    padding:0 2rem 0 0; 
     a{display:inline-block; padding:18px 0 15px;}
-    a + a {margin-left: 32px;}
+    // a + a {margin-left: 32px;}
     a span.indic{border: 3px transparent solid; width:0; height:0; border-left: 3px solid #FFFFFF; display:block; float: left;content: ""; position:relative; top:8px;margin-right: 2px;}
     a:hover span.indic{border-left: 3px solid @pageColor;}
     a.active span.indic{border-left: 3px transparent solid;border-top: 3px solid @pageColor; margin-right: 5px;top:9px;}
 
+    display: flex;
+    justify-content: space-evenly;
+
 }
 
 
