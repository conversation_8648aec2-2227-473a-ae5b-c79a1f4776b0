Revision: r11023
Date: 2023-06-13 15:38:21 +0300 (tlt 13 Jon 2023) 
Author: norajaonarivelo 

## Commit message
Wishlist Champ Formulaire coté js

## Files changed

## Full metadata
------------------------------------------------------------------------
r11023 | norajaonarivelo | 2023-06-13 15:38:21 +0300 (tlt 13 Jon 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/Field.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/formBuilder.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FieldView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/form_block/contentOptions.less

Wishlist Champ Formulaire coté js
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/Field.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/Field.js	(révision 11022)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/Field.js	(révision 11023)
@@ -1,5 +1,5 @@
 define(["JEditor/Commons/Ancestors/Models/Model"], function(Model) {
-    var allowedTypes = ["text", "number", "checkbox", "radio", "textarea", "email", "select", "tel", "separator","name","file","recipientlist","date"];
+    var allowedTypes = ["text", "number", "checkbox", "radio", "textarea", "email", "select", "tel", "separator","name","file","recipientlist","date","url","time","datetime-local"];
     var Field = Model.extend({
         defaults: function() {
             var ret = {
@@ -17,6 +17,9 @@
             Model.prototype.initialize.apply(this,arguments);
             if(this.type==="name")
                 this.mandatory=true;
+            else if(this.type === "url"){
+                this.placeholder ="https://www.exemple.com";
+            }
             this.on("change:type",function () {
                 if(this.type==="name")
                     this.mandatory=true;
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/formBuilder.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/formBuilder.html	(révision 11022)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/formBuilder.html	(révision 11023)
@@ -46,6 +46,12 @@
                     </a>
                 </li>
                 <li>
+                    <a href="#" class="" data-type="datetime-local">
+                        <span class="icon-form-scheddule"></span>
+                        <%=__("DateTimeField")%>
+                    </a>
+                </li>
+                <li>
                     <a href="#" class="" data-type="date">
                         <span class="icon-form-calendar"></span>
                         <%=__("dateField")%>
@@ -52,6 +58,13 @@
                     </a>
                 </li>
                 <li>
+                    <a href="#" class="" data-type="time">
+                        <span class="icon-form-time"></span>
+                        <%=__("timeField")%>
+                    </a>
+                </li>
+                
+                <li>
                     <a href="#" class="" data-type="number">
                         <span class="icon-form-number"></span>
                         <%=__("numberField")%>
@@ -93,6 +106,12 @@
                         <%=__("fileField")%>
                      </a>
                 </li>
+                <li>
+                    <a href="#" class="" data-type="url">
+                        <span class="icon-link"></span>
+                        <%=__("urlField")%>
+                     </a>
+                </li>
             </ul>
         </div>
     </div>
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FieldView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FieldView.js	(révision 11022)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FieldView.js	(révision 11023)
@@ -26,7 +26,10 @@
             radio:radioTpl,
             file:textTpl,
             recipientlist:selectTpl,
-            date:textTpl
+            date:textTpl,
+            url:textTpl,
+            time:textTpl,
+            "datetime-local":textTpl
         },
         initialize:function () {
             View.prototype.initialize.call(this);
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js	(révision 11022)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js	(révision 11023)
@@ -31,6 +31,7 @@
     "nameField":"Nom",
     "emailField":"Courriel",
     "telField":"Téléphone",
+    "urlField"  : "Url",
     "numberField":"Nombre",
     "selectField":"Menu déroulant",
     "radioField":"Boutons radios",
@@ -72,5 +73,9 @@
     "proximite" : "Commerces de proximité",
     "mailInternaute" : "Envoyer un mail de récéption à l'internaute",
     "copy":"Copié", 
-
+    "url"       :   "Url",
+    "DateTimeField" :   "Date et heure ",
+    "datetime-local"    :   "date et heure ",
+    "timeField" :   "Heure",
+    "time"      :   "heure",
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js	(révision 11022)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js	(révision 11023)
@@ -31,6 +31,7 @@
     "nameField":"Nom",
     "emailField":"e-mail",
     "telField":"Téléphone",
+    "urlField"  : "Url",
     "numberField":"Nombre",
     "selectField":"Menu déroulant",
     "radioField":"Boutons radios",
@@ -72,5 +73,9 @@
     "proximite" : "Commerces de proximité",
     "mailInternaute" : "Envoyer un mail de récéption à l'internaute",
     "copy":"Copié",
-
+    "url"       :   "Url",
+    "DateTimeField" :   "Date et heure ",
+    "datetime-local"    :   "date et heure ",
+    "timeField" :   "Heure",
+    "time"      :   "heure",
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js	(révision 11022)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js	(révision 11023)
@@ -77,7 +77,13 @@
         "proximite" : "Local shops",
         "mailInternaute" : "Send notification to user",
         "copy":"Copied",
-        "styles"    :   "Styles"
+        "styles"    :   "Styles",
+        "urlField"  : "Url",
+        "url"       :   "Url",
+        "timeField" :   "Time",
+        "time"      :   "time",
+        "DateTimeField" :   "Date and time",
+        "datetime-local"    :   "date and time"
     },
     "fr-fr":true,
     "fr-ca": true
Index: src/less/imports/form_block/contentOptions.less
===================================================================
--- src/less/imports/form_block/contentOptions.less	(révision 11022)
+++ src/less/imports/form_block/contentOptions.less	(révision 11023)
@@ -115,6 +115,7 @@
                 margin: 5px;
                 background: #1a1a1a;
                 height: 30px;
+                &.url,
                 &.text,
                 &.name,
                 &.date,
@@ -142,6 +143,13 @@
                 .underline {
                     display: none;
                 }
+                &.time,
+                &.datetime-local {
+                    .options,
+                    .placeholder {
+                        display: none;
+                    }
+                }
                 &.separator {
                     .underline {
                         display: block;
