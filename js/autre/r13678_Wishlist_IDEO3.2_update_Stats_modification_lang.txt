Revision: r13678
Date: 2024-12-26 15:02:30 +0300 (lkm 26 Des 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
Wishlist IDEO3.2: update Stats (modification lang)

## Files changed

## Full metadata
------------------------------------------------------------------------
r13678 | srazana<PERSON>lisoa | 2024-12-26 15:02:30 +0300 (lkm 26 Des 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/DashBoard/DashBoard.js
   M /branches/ideo3_v2/integration/src/js/JEditor/StatsPanel/StatsPanel.js

Wishlist IDEO3.2: update Stats (modification lang)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/DashBoard/DashBoard.js
===================================================================
--- src/js/JEditor/DashBoard/DashBoard.js	(révision 13677)
+++ src/js/JEditor/DashBoard/DashBoard.js	(révision 13678)
@@ -125,7 +125,7 @@
                             var vhostId = __IDEO_VHOST_ID__ ? __IDEO_VHOST_ID__ : '';
                             var codebouton = __IDEO_SITE_CODE_BOUTON__;
                           
-                            url = 'https://stats-gui-back-office.linkeo.ovh/dashboard?codebouton=' + codebouton + '&vhid=' + vhostId + '&lang=' + this.app.uiLanguage.id;
+                            url = 'https://stats-gui-back-office.linkeo.ovh/dashboard?codebouton=' + codebouton + '&vhid=' + vhostId + '&lang=' + this.app.uiLanguage.id.substr(0, 2);
 
                             //code
                             this.setDOM();//création des variables this.dom[]
Index: src/js/JEditor/StatsPanel/StatsPanel.js
===================================================================
--- src/js/JEditor/StatsPanel/StatsPanel.js	(révision 13677)
+++ src/js/JEditor/StatsPanel/StatsPanel.js	(révision 13678)
@@ -92,7 +92,7 @@
                             var key = __IDEO_VHOST_ID_KEY__ ? __IDEO_VHOST_ID_KEY__ : __IDEO_CODE_BOUTON__;
                             var vhostId = __IDEO_VHOST_ID__ ? __IDEO_VHOST_ID__ : '';
                             var codebouton = __IDEO_SITE_CODE_BOUTON__;
-                            var lang = this.app.uiLanguage.id ;
+                            var lang = this.app.uiLanguage.id.substr(0, 2);
                             
                             url = `https://stats-gui-back-office.linkeo.ovh/?codebouton=${codebouton}&vhid=${vhostId}&lang=${lang}`;
                         
