Revision: r10179
Date: 2023-01-20 14:57:36 +0300 (zom 20 Jan 2023) 
Author: norajaonarivelo 

## Commit message
IDEO3.2 Bug import de mise en page

## Files changed

## Full metadata
------------------------------------------------------------------------
r10179 | norajaonarivelo | 2023-01-20 14:57:36 +0300 (zom 20 Jan 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Views/PageView.js

IDEO3.2 Bug import de mise en page
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Views/PageView.js
===================================================================
--- src/js/JEditor/PagePanel/Views/PageView.js	(révision 10178)
+++ src/js/JEditor/PagePanel/Views/PageView.js	(révision 10179)
@@ -325,7 +325,7 @@
                                         title: translate("saveAction"),
                                         message: translate("successImportAction"),
                                     });
-                                    page.load();
+                                    page.pagePanel.trigger(Events.PagePanelEvents.PAGE_CHANGE);
                                 },
                                 //event if the upload+import page is a failure
                                 onUploadFail: function() {
@@ -334,7 +334,7 @@
                                         title: translate("saveAction"),
                                         message: translate("failedImportAction"),
                                     });
-                                    page.load();
+                                    page.pagePanel.trigger(Events.PagePanelEvents.PAGE_CHANGE);
                                 },
                             });
                             this.dom.showUploader = this.$('input.upload.button');
