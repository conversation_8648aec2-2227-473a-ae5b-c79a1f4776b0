Revision: r13183
Date: 2024-10-09 15:16:40 +0300 (lrb 09 Okt 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishilist: amelioration uploader(retour)

## Files changed

## Full metadata
------------------------------------------------------------------------
r13183 | s<PERSON><PERSON><PERSON><PERSON>oa | 2024-10-09 15:16:40 +0300 (lrb 09 Okt 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileListView.js

wishilist: amelioration uploader(retour)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Files/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/Commons/Files/nls/fr-ca/i18n.js	(révision 13182)
+++ src/js/JEditor/Commons/Files/nls/fr-ca/i18n.js	(révision 13183)
@@ -18,7 +18,7 @@
     "defaultUploaderMessageFile":"Cliquez ici ou glissez-déposez pour remplacer le fichier",
     "browseImageOnComputer":"Parcourir les dossiers <br>sur mon ordinateur",
     "imagesFromStock":"Parcourir la base d'images<br> de mon site",
-    "importFailBadType":"Impossible d'importer le fichier %name% car il représente un danger potentiel",
+    "importFailBadType":"Le ficher %name% n'a pas été importé. Ce type de fichier n'est pas autorisé.",
     "progressLoadingFiles":"Veuillez patienter pendant le chargement des fichiers",
     "uploadFailTooBig": 'Le fichier "%name%" (%filesize% mo ) est trop volumineux pour être importé (limite %limite% mo)',"uploadFailErrorsOccured":"Des erreurs ont eu lieu pendant le transfert de fichier",
     "uploadSuccess":"Le transfert des fichiers s'est déroulé avec succès",
Index: src/js/JEditor/Commons/Files/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/Commons/Files/nls/fr-fr/i18n.js	(révision 13182)
+++ src/js/JEditor/Commons/Files/nls/fr-fr/i18n.js	(révision 13183)
@@ -18,7 +18,7 @@
     "defaultUploaderMessageFile":"Cliquez ici ou glissez-déposez pour remplacer le fichier",
     "browseImageOnComputer":"Parcourir les dossiers <br>sur mon ordinateur",
     "imagesFromStock":"Parcourir la base d'images<br> de mon site",
-    "importFailBadType":"Impossible d'importer le fichier %name% car il représente un danger potentiel",
+    "importFailBadType": "Le ficher %name% n'a pas été importé. Ce type de fichier n'est pas autorisé.",
     "progressLoadingFiles":"Veuillez patienter pendant le chargement des fichiers",
     "uploadFailTooBig":'Le fichier "%name%" (%filesize% mo ) est trop volumineux pour être importé (limite %limite% mo)',
     "uploadFailErrorsOccured":"Des erreurs ont eu lieu pendant le transfert de fichier",
Index: src/js/JEditor/Commons/Files/nls/i18n.js
===================================================================
--- src/js/JEditor/Commons/Files/nls/i18n.js	(révision 13182)
+++ src/js/JEditor/Commons/Files/nls/i18n.js	(révision 13183)
@@ -18,7 +18,7 @@
     "defaultUploaderMessageFile":"Click here or drag-and-drop to change the file",
     "browseImageOnComputer":"Browse directories <br> on my computer",
     "imagesFromStock":"Browse pictures <br> on my site",
-    "importFailBadType":"Impossible to import file%name % due to a potential harm",
+    "importFailBadType":"The file %name% has not been uploaded. This file type is not authorized.",
     "progressLoadingFiles":"Please wait during file loading",
     "uploadFailTooBig":  'The file "%name%" (%filesize% mo )  is too large to be imported (limit %limite% mo)',
     "uploadFailErrorsOccured":"There were errors during file transfer",
Index: src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js
===================================================================
--- src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 13182)
+++ src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 13183)
@@ -56,7 +56,7 @@
             };
             $.event.props.push('dataTransfer');
             var settings = {
-                acceptedTypes: ['image', 'audio', 'video','font'],
+                acceptedTypes: ['image', 'audio', 'video','font', 'application'],
                 acceptedExtensions: ['.png', '.jpeg', '.jpg', '.gif', '.svg', '.webp', '.mp4', '.pdf', '.doc', '.dot', '.docx', '.ppt', '.pot', '.json', '.ttf', '.otf', '.woff', '.woff2', '.eot'],
                 refusedExtensions: ['.php', '.pl', '.rb', '.exe'],
                 refusedTypes: ['exploit'],
@@ -96,7 +96,7 @@
                     defaultUploaderMessage: 'Cliquez ici ou glissez-déposez pour remplacer l\'image',
                     browseImageOnComputer: 'Parcourir les dossiers <br>sur mon ordinateur',
                     imagesFromStock: 'Parcourir la base d\'images<br> de mon site',
-                    importFailBadType: 'Impossible d\'importer le fichier %name% car il représente un danger potentiel',
+                    importFailBadType: 'Le ficher %name% n\'a pas été importé. Ce type de fichier n\'est pas autorisé.',
                     progressLoadingFiles: 'Veuillez patienter pendant le chargement des fichiers',
                     uploadFailTooBig: 'Le fichier "%name%" (%filesize% mo ) est trop volumineux pour être importé (limite %limite% mo)' ,
                     uploadFailCorrupted: 'Echec du chargement. Le fichier semble corrompu',
Index: src/js/JEditor/FilePanel/Views/FileListView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileListView.js	(révision 13182)
+++ src/js/JEditor/FilePanel/Views/FileListView.js	(révision 13183)
@@ -210,15 +210,11 @@
         },
         render: function() {
             this._super();
-            var acceptedtypes =  ['image', 'audio', 'font']
             if(!this.app.user.can("delete_file")){
                 this.$("li.action.delete").replaceWith('<li class="" style="visibility: hidden;"></li>');
             }
-            if(this.app.user.can("upload_video")){
-               acceptedtypes.push('video/mp4');
-            }
             this.dom[this.cid].uploadZone = this.$('.group-content');
-            this.dom[this.cid].uploadZone.uploader({showMenu: false, maxFiles: -1, lang: translate.translations, acceptedTypes: acceptedtypes});
+            this.dom[this.cid].uploadZone.uploader({showMenu: false, maxFiles: -1, lang: translate.translations});
             this.dom[this.cid].showUploader = this.$('input.upload.button');
             return this;
         },
