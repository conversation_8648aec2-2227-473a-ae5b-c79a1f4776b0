Revision: r13535
Date: 2024-11-27 14:33:56 +0300 (lrb 27 Nov 2024) 
Author: frahajanirina 

## Commit message
Wishlist:IDEO3.2:Mod<PERSON>le de formulaire Taxi

## Files changed

## Full metadata
------------------------------------------------------------------------
r13535 | frahajanirina | 2024-11-27 14:33:56 +0300 (lrb 27 Nov 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/FormTemplate.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormTemplateList.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js

Wishlist:IDEO3.2:Modèle de formulaire Taxi
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/FormTemplate.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/FormTemplate.js	(révision 13534)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/FormTemplate.js	(révision 13535)
@@ -699,6 +699,95 @@
                     "underline": false
                 }]
             ]
+        },
+        taxi: {
+            "name": "Formulaire de réservation",
+            "buttonText": "Envoyer",
+            "action": "Send",
+            "successMessage": null,
+            "columns": [
+                [{
+                    "type": "separator",
+                    "label": "Demande de réservation",
+                    "description": "Pour les réservations immédiates préférez le téléphone : [[contact_calltracking|format=html]]",
+                    "mandatory": false,
+                    "underline": true
+                }, {
+                    "type": "datetime-local",
+                    "label": "Date",
+                    "description": "Précisez la date et heure du départ",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "text",
+                    "label": "Départ",
+                    "description": "Adresse du lieu de départ",
+                    "placeholder": "133 Chemin de la plaine, 75000 Paris",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "text",
+                    "label": "Arrivée",
+                    "description": "Adresse du lieu d'arrivée",
+                    "placeholder": "133 Chemin de la plaine, 75000 Paris",
+                    "mandatory": true,
+                    "underline": true
+                }, {
+                    "type": "number",
+                    "label": "Passagers",
+                    "description": "Indiquez le nombre de passagers (maximum 4)",
+                    "placeholder": "#",
+                    "options": {
+                        min: 1,
+                        max: 4
+                    },
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "number",
+                    "label": "Bagages",
+                    "description": "Indiquez le nombre de bagages (maximum 8)",
+                    "placeholder": "#",
+                    "options": {
+                        min: 1,
+                        max: 8
+                    },
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "textarea",
+                    "label": "Précisions",
+                    "description": "Précisez votre demande",
+                    "placeholder": "Bonjour, ...",
+                    "mandatory": false,
+                    "underline": false
+                }, {
+                    "type": "separator",
+                    "mandatory": false,
+                    "underline": true
+                }, {
+                    "type": "name",
+                    "label": "Nom",
+                    "description": "Saisissez votre nom complet",
+                    "placeholder": "John Doe",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "tel",
+                    "label": "Téléphone",
+                    "description": "Saisissez votre numéro de téléphone",
+                    "placeholder": "06########",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "email",
+                    "label": "E-mail",
+                    "description": "Saisissez votre adresse email",
+                    "placeholder": "<EMAIL>",
+                    "mandatory": true,
+                    "underline": false
+                }]
+            ]
         }
     },
     fr_CA: {
@@ -1401,6 +1490,95 @@
                     "underline": false
                 }]
             ]
+        },
+        taxi: {
+            "name": "Formulaire de réservation",
+            "buttonText": "Envoyer",
+            "action": "Send",
+            "successMessage": null,
+            "columns": [
+                [{
+                    "type": "separator",
+                    "label": "Demande de réservation",
+                    "description": "Pour les réservations immédiates préférez le téléphone : [[contact_calltracking|format=html]]",
+                    "mandatory": false,
+                    "underline": true
+                }, {
+                    "type": "datetime-local",
+                    "label": "Date",
+                    "description": "Précisez la date et heure du départ",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "text",
+                    "label": "Départ",
+                    "description": "Adresse du lieu de départ",
+                    "placeholder": "133 Chemin de la plaine, 75000 Paris",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "text",
+                    "label": "Arrivée",
+                    "description": "Adresse du lieu d'arrivée",
+                    "placeholder": "133 Chemin de la plaine, 75000 Paris",
+                    "mandatory": true,
+                    "underline": true
+                }, {
+                    "type": "number",
+                    "label": "Passagers",
+                    "description": "Indiquez le nombre de passagers (maximum 4)",
+                    "placeholder": "#",
+                    "options": {
+                        min: 1,
+                        max: 4
+                    },
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "number",
+                    "label": "Bagages",
+                    "description": "Indiquez le nombre de bagages (maximum 8)",
+                    "placeholder": "#",
+                    "options": {
+                        min: 1,
+                        max: 8
+                    },
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "textarea",
+                    "label": "Précisions",
+                    "description": "Précisez votre demande",
+                    "placeholder": "Bonjour, ...",
+                    "mandatory": false,
+                    "underline": false
+                }, {
+                    "type": "separator",
+                    "mandatory": false,
+                    "underline": true
+                }, {
+                    "type": "name",
+                    "label": "Nom",
+                    "description": "Saisissez votre nom complet",
+                    "placeholder": "John Doe",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "tel",
+                    "label": "Téléphone",
+                    "description": "Saisissez votre numéro de téléphone",
+                    "placeholder": "06########",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "email",
+                    "label": "E-mail",
+                    "description": "Saisissez votre adresse email",
+                    "placeholder": "<EMAIL>",
+                    "mandatory": true,
+                    "underline": false
+                }]
+            ]
         }
     },
     en_CA: {
@@ -2103,6 +2281,95 @@
                     "underline": false
                 }]
             ]
+        },
+        taxi: {
+            "name": "Reservation form",
+            "buttonText": "Send",
+            "action": "Send",
+            "successMessage": null,
+            "columns": [
+                [{
+                    "type": "separator",
+                    "label": "Reservation request",
+                    "description": "For immediate reservations prefer the telephone : [[contact_calltracking|format=html]]",
+                    "mandatory": false,
+                    "underline": true
+                }, {
+                    "type": "datetime-local",
+                    "label": "Date",
+                    "description": "Specify the date and time of departure",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "text",
+                    "label": "Departure",
+                    "description": "Address of departure location",
+                    "placeholder": "133 Chemin de la plaine, 75000 Paris",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "text",
+                    "label": "Arrival",
+                    "description": "Arrival address",
+                    "placeholder": "133 Chemin de la plaine, 75000 Paris",
+                    "mandatory": true,
+                    "underline": true
+                }, {
+                    "type": "number",
+                    "label": "Passengers",
+                    "description": "Indicate the number of passengers (maximum 4)",
+                    "placeholder": "#",
+                    "options": {
+                        min: 1,
+                        max: 4
+                    },
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "number",
+                    "label": "Luggage",
+                    "description": "Indicate the number of pieces of luggage (maximum 8)",
+                    "placeholder": "#",
+                    "options": {
+                        min: 1,
+                        max: 8
+                    },
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "textarea",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
+                    "mandatory": false,
+                    "underline": false
+                }, {
+                    "type": "separator",
+                    "mandatory": false,
+                    "underline": true
+                }, {
+                    "type": "name",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "tel",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "06########",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "email",
+                    "label": "E-mail",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
+                    "mandatory": true,
+                    "underline": false
+                }]
+            ]
         }
     },
     en_FR: {
@@ -2805,6 +3072,95 @@
                     "underline": false
                 }]
             ]
+        },
+        taxi: {
+            "name": "Reservation form",
+            "buttonText": "Send",
+            "action": "Send",
+            "successMessage": null,
+            "columns": [
+                [{
+                    "type": "separator",
+                    "label": "Reservation request",
+                    "description": "For immediate reservations prefer the telephone : [[contact_calltracking|format=html]]",
+                    "mandatory": false,
+                    "underline": true
+                }, {
+                    "type": "datetime-local",
+                    "label": "Date",
+                    "description": "Specify the date and time of departure",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "text",
+                    "label": "Departure",
+                    "description": "Address of departure location",
+                    "placeholder": "133 Chemin de la plaine, 75000 Paris",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "text",
+                    "label": "Arrival",
+                    "description": "Arrival address",
+                    "placeholder": "133 Chemin de la plaine, 75000 Paris",
+                    "mandatory": true,
+                    "underline": true
+                }, {
+                    "type": "number",
+                    "label": "Passengers",
+                    "description": "Indicate the number of passengers (maximum 4)",
+                    "placeholder": "#",
+                    "options": {
+                        min: 1,
+                        max: 4
+                    },
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "number",
+                    "label": "Luggage",
+                    "description": "Indicate the number of pieces of luggage (maximum 8)",
+                    "placeholder": "#",
+                    "options": {
+                        min: 1,
+                        max: 8
+                    },
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "textarea",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
+                    "mandatory": false,
+                    "underline": false
+                }, {
+                    "type": "separator",
+                    "mandatory": false,
+                    "underline": true
+                }, {
+                    "type": "name",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "tel",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "06########",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "email",
+                    "label": "E-mail",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
+                    "mandatory": true,
+                    "underline": false
+                }]
+            ]
         }
     },
     en_AU: {
@@ -3507,6 +3863,95 @@
                     "underline": false
                 }]
             ]
+        },
+        taxi: {
+            "name": "Reservation form",
+            "buttonText": "Send",
+            "action": "Send",
+            "successMessage": null,
+            "columns": [
+                [{
+                    "type": "separator",
+                    "label": "Reservation request",
+                    "description": "For immediate reservations prefer the telephone : [[contact_calltracking|format=html]]",
+                    "mandatory": false,
+                    "underline": true
+                }, {
+                    "type": "datetime-local",
+                    "label": "Date",
+                    "description": "Specify the date and time of departure",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "text",
+                    "label": "Departure",
+                    "description": "Address of departure location",
+                    "placeholder": "133 Chemin de la plaine, 75000 Paris",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "text",
+                    "label": "Arrival",
+                    "description": "Arrival address",
+                    "placeholder": "133 Chemin de la plaine, 75000 Paris",
+                    "mandatory": true,
+                    "underline": true
+                }, {
+                    "type": "number",
+                    "label": "Passengers",
+                    "description": "Indicate the number of passengers (maximum 4)",
+                    "placeholder": "#",
+                    "options": {
+                        min: 1,
+                        max: 4
+                    },
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "number",
+                    "label": "Luggage",
+                    "description": "Indicate the number of pieces of luggage (maximum 8)",
+                    "placeholder": "#",
+                    "options": {
+                        min: 1,
+                        max: 8
+                    },
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "textarea",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
+                    "mandatory": false,
+                    "underline": false
+                }, {
+                    "type": "separator",
+                    "mandatory": false,
+                    "underline": true
+                }, {
+                    "type": "name",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "tel",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "06########",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "email",
+                    "label": "E-mail",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
+                    "mandatory": true,
+                    "underline": false
+                }]
+            ]
         }
     },
     en_AE: {
@@ -4201,6 +4646,95 @@
                     "underline": false
                 }]
             ]
+        },
+        taxi: {
+            "name": "Reservation form",
+            "buttonText": "Send",
+            "action": "Send",
+            "successMessage": null,
+            "columns": [
+                [{
+                    "type": "separator",
+                    "label": "Reservation request",
+                    "description": "For immediate reservations prefer the telephone : [[contact_calltracking|format=html]]",
+                    "mandatory": false,
+                    "underline": true
+                }, {
+                    "type": "datetime-local",
+                    "label": "Date",
+                    "description": "Specify the date and time of departure",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "text",
+                    "label": "Departure",
+                    "description": "Address of departure location",
+                    "placeholder": "133 Chemin de la plaine, 75000 Paris",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "text",
+                    "label": "Arrival",
+                    "description": "Arrival address",
+                    "placeholder": "133 Chemin de la plaine, 75000 Paris",
+                    "mandatory": true,
+                    "underline": true
+                }, {
+                    "type": "number",
+                    "label": "Passengers",
+                    "description": "Indicate the number of passengers (maximum 4)",
+                    "placeholder": "#",
+                    "options": {
+                        min: 1,
+                        max: 4
+                    },
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "number",
+                    "label": "Luggage",
+                    "description": "Indicate the number of pieces of luggage (maximum 8)",
+                    "placeholder": "#",
+                    "options": {
+                        min: 1,
+                        max: 8
+                    },
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "textarea",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
+                    "mandatory": false,
+                    "underline": false
+                }, {
+                    "type": "separator",
+                    "mandatory": false,
+                    "underline": true
+                }, {
+                    "type": "name",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "tel",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "06########",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "email",
+                    "label": "E-mail",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
+                    "mandatory": true,
+                    "underline": false
+                }]
+            ]
         }
     },
     en_US: {
@@ -4903,6 +5437,95 @@
                     "underline": false
                 }]
             ]
+        },
+        taxi: {
+            "name": "Reservation form",
+            "buttonText": "Send",
+            "action": "Send",
+            "successMessage": null,
+            "columns": [
+                [{
+                    "type": "separator",
+                    "label": "Reservation request",
+                    "description": "For immediate reservations prefer the telephone : [[contact_calltracking|format=html]]",
+                    "mandatory": false,
+                    "underline": true
+                }, {
+                    "type": "datetime-local",
+                    "label": "Date",
+                    "description": "Specify the date and time of departure",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "text",
+                    "label": "Departure",
+                    "description": "Address of departure location",
+                    "placeholder": "133 Chemin de la plaine, 75000 Paris",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "text",
+                    "label": "Arrival",
+                    "description": "Arrival address",
+                    "placeholder": "133 Chemin de la plaine, 75000 Paris",
+                    "mandatory": true,
+                    "underline": true
+                }, {
+                    "type": "number",
+                    "label": "Passengers",
+                    "description": "Indicate the number of passengers (maximum 4)",
+                    "placeholder": "#",
+                    "options": {
+                        min: 1,
+                        max: 4
+                    },
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "number",
+                    "label": "Luggage",
+                    "description": "Indicate the number of pieces of luggage (maximum 8)",
+                    "placeholder": "#",
+                    "options": {
+                        min: 1,
+                        max: 8
+                    },
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "textarea",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
+                    "mandatory": false,
+                    "underline": false
+                }, {
+                    "type": "separator",
+                    "mandatory": false,
+                    "underline": true
+                }, {
+                    "type": "name",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "tel",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "06########",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "email",
+                    "label": "E-mail",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
+                    "mandatory": true,
+                    "underline": false
+                }]
+            ]
         }
     },
     es_FR: {
@@ -5605,6 +6228,95 @@
                     "underline": false
                 }]
             ]
+        },
+        taxi: {
+            "name": "Forma de reserva",
+            "buttonText": "Enviar",
+            "action": "Send",
+            "successMessage": null,
+            "columns": [
+                [{
+                    "type": "separator",
+                    "label": "Solicitud de reserva",
+                    "description": "Para reservas inmediatas preferir el teléfono : [[contact_calltracking|format=html]]",
+                    "mandatory": false,
+                    "underline": true
+                }, {
+                    "type": "datetime-local",
+                    "label": "Fecha",
+                    "description": "Especifique la fecha y hora de salida",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "text",
+                    "label": "Partida",
+                    "description": "Dirección del lugar de salida",
+                    "placeholder": "133 Chemin de la plaine, 75000 Paris",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "text",
+                    "label": "Llegada",
+                    "description": "Dirección de llegada",
+                    "placeholder": "133 Chemin de la plaine, 75000 Paris",
+                    "mandatory": true,
+                    "underline": true
+                }, {
+                    "type": "number",
+                    "label": "Pasajeros",
+                    "description": "Indicar el número de pasajeros (máximo 4)",
+                    "placeholder": "#",
+                    "options": {
+                        min: 1,
+                        max: 4
+                    },
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "number",
+                    "label": "Equipaje",
+                    "description": "Indique el número de piezas de equipaje (máximo 8)",
+                    "placeholder": "#",
+                    "options": {
+                        min: 1,
+                        max: 8
+                    },
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "textarea",
+                    "label": "Detalles",
+                    "description": "Especifique su solicitud",
+                    "placeholder": "Hola, ...",
+                    "mandatory": false,
+                    "underline": false
+                }, {
+                    "type": "separator",
+                    "mandatory": false,
+                    "underline": true
+                }, {
+                    "type": "name",
+                    "label": "Nombre",
+                    "description": "Introduzca su nombre completo",
+                    "placeholder": "John Doe",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "tel",
+                    "label": "Teléfono",
+                    "description": "Introduzca su número de teléfono",
+                    "placeholder": "06########",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "email",
+                    "label": "Correo electrónico",
+                    "description": "Introduzca su dirección de correo electrónico",
+                    "placeholder": "<EMAIL>",
+                    "mandatory": true,
+                    "underline": false
+                }]
+            ]
         }
     },
     es_CA: {
@@ -6307,6 +7019,95 @@
                     "underline": false
                 }]
             ]
+        },
+        taxi: {
+            "name": "Forma de reserva",
+            "buttonText": "Enviar",
+            "action": "Send",
+            "successMessage": null,
+            "columns": [
+                [{
+                    "type": "separator",
+                    "label": "Solicitud de reserva",
+                    "description": "Para reservas inmediatas preferir el teléfono : [[contact_calltracking|format=html]]",
+                    "mandatory": false,
+                    "underline": true
+                }, {
+                    "type": "datetime-local",
+                    "label": "Fecha",
+                    "description": "Especifique la fecha y hora de salida",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "text",
+                    "label": "Partida",
+                    "description": "Dirección del lugar de salida",
+                    "placeholder": "133 Chemin de la plaine, 75000 Paris",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "text",
+                    "label": "Llegada",
+                    "description": "Dirección de llegada",
+                    "placeholder": "133 Chemin de la plaine, 75000 Paris",
+                    "mandatory": true,
+                    "underline": true
+                }, {
+                    "type": "number",
+                    "label": "Pasajeros",
+                    "description": "Indicar el número de pasajeros (máximo 4)",
+                    "placeholder": "#",
+                    "options": {
+                        min: 1,
+                        max: 4
+                    },
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "number",
+                    "label": "Equipaje",
+                    "description": "Indique el número de piezas de equipaje (máximo 8)",
+                    "placeholder": "#",
+                    "options": {
+                        min: 1,
+                        max: 8
+                    },
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "textarea",
+                    "label": "Detalles",
+                    "description": "Especifique su solicitud",
+                    "placeholder": "Hola, ...",
+                    "mandatory": false,
+                    "underline": false
+                }, {
+                    "type": "separator",
+                    "mandatory": false,
+                    "underline": true
+                }, {
+                    "type": "name",
+                    "label": "Nombre",
+                    "description": "Introduzca su nombre completo",
+                    "placeholder": "John Doe",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "tel",
+                    "label": "Teléfono",
+                    "description": "Introduzca su número de teléfono",
+                    "placeholder": "06########",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "email",
+                    "label": "Correo electrónico",
+                    "description": "Introduzca su dirección de correo electrónico",
+                    "placeholder": "<EMAIL>",
+                    "mandatory": true,
+                    "underline": false
+                }]
+            ]
         }
     },
     es_US: {
@@ -7009,6 +7810,95 @@
                     "underline": false
                 }]
             ]
+        },
+        taxi: {
+            "name": "Forma de reserva",
+            "buttonText": "Enviar",
+            "action": "Send",
+            "successMessage": null,
+            "columns": [
+                [{
+                    "type": "separator",
+                    "label": "Solicitud de reserva",
+                    "description": "Para reservas inmediatas preferir el teléfono : [[contact_calltracking|format=html]]",
+                    "mandatory": false,
+                    "underline": true
+                }, {
+                    "type": "datetime-local",
+                    "label": "Fecha",
+                    "description": "Especifique la fecha y hora de salida",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "text",
+                    "label": "Partida",
+                    "description": "Dirección del lugar de salida",
+                    "placeholder": "133 Chemin de la plaine, 75000 Paris",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "text",
+                    "label": "Llegada",
+                    "description": "Dirección de llegada",
+                    "placeholder": "133 Chemin de la plaine, 75000 Paris",
+                    "mandatory": true,
+                    "underline": true
+                }, {
+                    "type": "number",
+                    "label": "Pasajeros",
+                    "description": "Indicar el número de pasajeros (máximo 4)",
+                    "placeholder": "#",
+                    "options": {
+                        min: 1,
+                        max: 4
+                    },
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "number",
+                    "label": "Equipaje",
+                    "description": "Indique el número de piezas de equipaje (máximo 8)",
+                    "placeholder": "#",
+                    "options": {
+                        min: 1,
+                        max: 8
+                    },
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "textarea",
+                    "label": "Detalles",
+                    "description": "Especifique su solicitud",
+                    "placeholder": "Hola, ...",
+                    "mandatory": false,
+                    "underline": false
+                }, {
+                    "type": "separator",
+                    "mandatory": false,
+                    "underline": true
+                }, {
+                    "type": "name",
+                    "label": "Nombre",
+                    "description": "Introduzca su nombre completo",
+                    "placeholder": "John Doe",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "tel",
+                    "label": "Teléfono",
+                    "description": "Introduzca su número de teléfono",
+                    "placeholder": "06########",
+                    "mandatory": true,
+                    "underline": false
+                }, {
+                    "type": "email",
+                    "label": "Correo electrónico",
+                    "description": "Introduzca su dirección de correo electrónico",
+                    "placeholder": "<EMAIL>",
+                    "mandatory": true,
+                    "underline": false
+                }]
+            ]
         }
     },
     nl_FR: {
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormTemplateList.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormTemplateList.js	(révision 13534)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormTemplateList.js	(révision 13535)
@@ -6,7 +6,7 @@
         },
         initialize:function () {
             View.prototype.initialize.call(this);
-            this.lang = (this.app.currentPanel.currentPage)? this.app.currentPanel.currentPage.lang : this.app.currentPanel.currentArticle.lang;
+            this.lang = (this.app.params) ? this.app.params.id : this.app.currentPanel.currentArticle.lang;
             this.template=this.buildTemplate(tpl,i18n);
         },
         render:function () {
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js	(révision 13534)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js	(révision 13535)
@@ -90,5 +90,6 @@
     "min"  :"Min",
     "max" : "Max",
     "renderPageThankYou": "Renvoyer vers une page de remerciement",
-    "errorWithoutThankYouPage": "Vous n’avez pas sélectionné la page de remerciement. Vous devez sélectionner une page ou désactiver l’option."
+    "errorWithoutThankYouPage": "Vous n’avez pas sélectionné la page de remerciement. Vous devez sélectionner une page ou désactiver l’option.",
+    "taxi": "Réservation de taxi"
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js	(révision 13534)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js	(révision 13535)
@@ -90,5 +90,6 @@
     "min"  :"Min",
     "max" : "Max",
     "renderPageThankYou": "Renvoyer vers une page de remerciement",
-    "errorWithoutThankYouPage": "Vous n’avez pas sélectionné la page de remerciement. Vous devez sélectionner une page ou désactiver l’option."
+    "errorWithoutThankYouPage": "Vous n’avez pas sélectionné la page de remerciement. Vous devez sélectionner une page ou désactiver l’option.",
+    "taxi": "Réservation de taxi"
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js	(révision 13534)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js	(révision 13535)
@@ -96,7 +96,8 @@
         "min"  :"Min",
         "max" : "Max",
         "renderPageThankYou": "Link to a thank you page",
-        "errorWithoutThankYouPage": "You did not select the thank you page. You must select a page or disable the option."
+        "errorWithoutThankYouPage": "You did not select the thank you page. You must select a page or disable the option.",
+        "taxi": "Taxi reservation"
     },
     "fr-fr":true,
     "fr-ca": true
