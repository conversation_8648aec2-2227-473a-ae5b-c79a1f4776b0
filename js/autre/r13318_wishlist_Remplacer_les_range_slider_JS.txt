Revision: r13318
Date: 2024-10-22 12:25:51 +0300 (tlt 22 Okt 2024) 
Author: traj<PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist : Remplacer les range slider JS

## Files changed

## Full metadata
------------------------------------------------------------------------
r13318 | traj<PERSON><PERSON><PERSON><PERSON> | 2024-10-22 12:25:51 +0300 (tlt 22 Okt 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselStyle.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselStyleOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieStyleOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridStyleOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridStyleOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Templates/loopStyle.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopStyleOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/Templates/fadeOptions.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/Templates/stylesOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/Views/FadeOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/Views/StylesOptionView.js
   M /branches/ideo3_v2/integration/src/less/imports/panel.less
   A /branches/ideo3_v2/integration/src/less/imports/slider.css
   M /branches/ideo3_v2/integration/src/less/main.less

wishlist : Remplacer les range slider JS
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselOption.html	(révision 13317)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselOption.html	(révision 13318)
@@ -50,7 +50,6 @@
         <div class="defilement">
           <p class="panel-legend"><%=__("autoPlay")%></p>
           <div class="option-content">
-            
             <% var _id=_.uniqueId('autoplay') %>
             <input type="checkbox" class="blue-bg Autoplay" name="Autoplay" id="<%=_id %>" <%=Autoplay?'checked="checked"':''%>>
             <label for="<%=_id %>">
@@ -60,7 +59,6 @@
               </span>
               <span class="text"><%=__("activeAutoPlay")%></span>
             </label>
-
             <div id="CentreEcran-Block">
               <% var _id=_.uniqueId('CentreEcran') %>
               <input type="checkbox" class="blue-bg CentreEcran" name="CentreEcran" id="<%=_id %>" <%=CentreEcran?'checked="checked"':''%>>
@@ -72,7 +70,6 @@
                 <span class="text"><%=__("centreEcran")%></span>
               </label>
             </div>
-            
           </div>
         </div>
         <div class="defilement-cran">
@@ -79,9 +76,13 @@
           <p class="panel-legend"><%=__("duration")%></p>
           <div class="option-content">
             <div class="slider-container">
-              <span class="icon-less"></span>
-              <div class="slider"></div>
-              <span class="icon-more"></span>
+              <input type="range" class="slider" list="carouselDelay-list" min="1" max="4">
+              <datalist id="carouselDelay-list">
+                  <option value="3000" label="3 sec"></option>
+                  <option value="4000" label="4 sec"></option>
+                  <option value="5000" label="5 sec"></option>
+                  <option value="6000" label="6 sec"></option>
+              </datalist>
             </div>
         </div>
         </div>
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselStyle.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselStyle.html	(révision 13317)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselStyle.html	(révision 13318)
@@ -21,7 +21,16 @@
             <p class="panel-content-legend"><%= __("carrouselHeightDesc")%></p>
         </header>
         <div class="option-content">
-            <div class="slider-container"><span class="icon-less"></span><div class="slider"></div><span class="icon-more"></span></div>
+            <div class="slider-container">
+                <input type="range" class="slider" list="numbPictures-list" min="1" max="5" >
+                <datalist id="numbPictures-list">
+                  <option value="1" label="1"></option>
+                  <option value="2" label="2"></option>
+                  <option value="3" label="3"></option>
+                  <option value="4" label="4"></option>
+                  <option value="5" label="5"></option>
+                </datalist>
+            </div>
         </div>
     </article>
 </div>
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js	(révision 13317)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js	(révision 13318)
@@ -26,7 +26,7 @@
                 'click input[type=radio]':'_onChangeRadio',
                 'change .Autoplay': '_onChangeAutoplay',
                 'change .CentreEcran':'_onChangeCentreEcran',
-                'slidechange .slider': 'onSliderChange',
+                'change .defilement-cran .slider': '_onDurationChange',
                 },
             className: 'carrousel-option-home panel-content',
             initialize: function() {
@@ -87,15 +87,10 @@
                 this.dom[this.cid].autoplay = this.$('.defilement');
                 this.dom[this.cid].cran = this.$('.defilement-cran');
                 this._showHide();
-                var slider = this.$('.defilement-cran .slider');
-                slider.slider({
-                    min: 3000,
-                    max: 6000,
-                    step: 1000,
-                    value:this.model.Duration,
-                    range:"min"
-                });
-                this.dom[this.cid].slider = slider;
+
+                $(".defilement-cran .slider").val(this.model.Duration);
+
+               
                 this.scrollables({
                     advanced:{ autoScrollOnFocus: false }
                 });
@@ -111,10 +106,8 @@
             _onChangeCentreEcran: function(event){
                 this.model.CentreEcran = !this.model.CentreEcran;
             },
-            onSliderChange: function(e,ui) {
-                var value = ui.value;
-                this.model.Duration=value;
-                return false;
+            _onDurationChange: function(event){
+                this.model.Duration = parseInt( $(event.currentTarget).val() ) ;
             },
             _showHide :function(){
                 this.dom[this.cid].autoplay.hide();
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselStyleOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselStyleOptionView.js	(révision 13317)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselStyleOptionView.js	(révision 13318)
@@ -19,9 +19,9 @@
                         tagName: "div",
                         className: "gallery-template-option galleryStyle panel-content ",
                         events: {
-                            'slidechange .slider'   :   'onSliderChange',
                             'change input[type="radio"].select-box': '_onStyleAffichageChange',
-                            'click .effect-radio': '_onChangeFormatImage'
+                            'click .effect-radio': '_onChangeFormatImage',
+                            'change .carrousel-height .slider': '_onNombreImageChange',
                         },
                         /**
                          * initialise l'objet
@@ -43,15 +43,10 @@
                             var $target = $(event.currentTarget);
                             this.model.StyleAffichage = $target.val();
                         },
+                         _onNombreImageChange: function(event){
+                            this.model.NombreImage = parseInt( $(event.currentTarget).val() ) ;
+                        },
                         /**
-                         * Slider change
-                         */
-                         onSliderChange: function(event,ui){
-                            var value = ui.value;
-                            this.model.NombreImage=value;
-                            return false;
-                         },
-                        /**
                          * affichage de l'alignement du texte
                          */
                         toggleTextAlignment:function () {
@@ -68,15 +63,8 @@
                             };
                             this.$el.html(this.template(templateVars));
                             this.$('.category-content').radio();
-                            var slider = this.$('.carrousel-height .slider');
-                            slider.slider({
-                                min: 1,
-                                max: 5,
-                                step: 1,
-                                value:this.model.NombreImage,
-                                range:"min"
-                            });
-                            this.dom[this.cid].slider = slider;
+                            $(".carrousel-height .slider").val(this.model.NombreImage);
+                            
                             this.scrollables();
                             return this;
                         }
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html	(révision 13317)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html	(révision 13318)
@@ -38,7 +38,16 @@
             <p class="panel-content-legend"><%= __("galerieNombreImageLegend")%></p>
         </header>
         <div class="option-content">
-            <div class="slider-container"><span class="icon-less"></span><div class="slider"></div><span class="icon-more"></span></div>
+            <div class="slider-container">
+                <input type="range" class="slider" list="numbPictures-list" min="1" max="5" >
+                <datalist id="numbPictures-list">
+                  <option value="1" label="1"></option>
+                  <option value="2" label="2"></option>
+                  <option value="3" label="3"></option>
+                  <option value="4" label="4"></option>
+                  <option value="5" label="5"></option>
+                </datalist>
+            </div>
         </div>
     </article>
 </div>
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieStyleOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieStyleOptionView.js	(révision 13317)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieStyleOptionView.js	(révision 13318)
@@ -18,8 +18,8 @@
         events: {
             'click .stylleDegalerie div .effect-radio'   : '_onChangeStylleImage',
             'click #Radioformatimage div .effect-radio'       : '_onChangeFormatImage',
-            'slidechange .slider'   :   '_onSliderChange',
             'change input[type="radio"].select-box': '_onStyleAffichageChange',
+            'change .galerie-nbreImage .slider': '_onNombreImageChange',
         },
         _onChangeStylleImage : function(event){
             this.$(".effect-radio").removeClass("active");
@@ -39,14 +39,9 @@
             var $target = $(event.currentTarget);
             this.model.galerieStyleAff = $target.val();
         },
-        /**
-        * chagement du slider
-        */
-        _onSliderChange: function(event,ui){
-            var value = ui.value;
-            this.model.galerieNbreImage=value;
-            return false;
-         },
+        _onNombreImageChange: function(event){
+            this.model.galerieNbreImage = parseInt( $(event.currentTarget).val() ) ;
+        },
         _onChangeFormatImage : function(event){
             this.$(".formatImage-radio").removeClass("active");
             var $target = $(event.currentTarget);
@@ -68,13 +63,9 @@
                 galerieStyleAff:this.model.galerieStyleAff
             };
             this.$el.html(this.template(templateVars));
-            this.$('.galerie-nbreImage .slider').slider({
-                range: "min",
-                value: this.model.galerieNbreImage,
-                min: 1,
-                max: 5,
-                step: 1
-            });
+
+            $(".galerie-nbreImage .slider").val(this.model.galerieNbreImage);
+            
             this.scrollables();
             this.delegateEvents();
             return this;
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridStyleOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridStyleOption.html	(révision 13317)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridStyleOption.html	(révision 13318)
@@ -40,7 +40,17 @@
             <p class="panel-content-legend"><%= __("gridNombreImageLegend")%></p>
         </header>
         <div class="option-content">
-            <div class="slider-container"><span class="icon-less"></span><div class="slider"></div><span class="icon-more"></span></div>
+            <!-- <div class="slider-container"><span class="icon-less"></span><div class="slider"></div><span class="icon-more"></span></div> -->
+            <div class="slider-container">
+                <input type="range" class="slider" list="numbPictures-list" min="1" max="5" >
+                <datalist id="numbPictures-list">
+                  <option value="1" label="1"></option>
+                  <option value="2" label="2"></option>
+                  <option value="3" label="3"></option>
+                  <option value="4" label="4"></option>
+                  <option value="5" label="5"></option>
+                </datalist>
+            </div>
         </div>
     </article>
 </div>
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridStyleOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridStyleOptionView.js	(révision 13317)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridStyleOptionView.js	(révision 13318)
@@ -22,8 +22,8 @@
                 events: {
                     'click .stylleDeGrille div .effect-radio'   : '_onChangeStylleImage',
                     'click #Radioformatimage div .effect-radio'       : '_onChangeFormatImage',
-                    'slidechange .slider'   :   '_onSliderChange',
                     'change input[type="radio"].select-box': '_onStyleAffichageChange',
+                    'change .grid-nbreImage .slider': '_onNombreImageChange',
                 },
                 _onChangeStylleImage : function(event){
                     this.$(".effect-radio").removeClass("active");
@@ -44,14 +44,9 @@
                     var $target = $(event.currentTarget);
                     this.model.grilleStyleAff = $target.val();
                 },
-                /**
-                * Slider change
-                */
-                _onSliderChange: function(event,ui){
-                    var value = ui.value;
-                    this.model.grilleNbreImage=value;
-                    return false;
-                 },
+                _onNombreImageChange: function(event){
+                    this.model.grilleNbreImage = parseInt( $(event.currentTarget).val() ) ;
+                },
                 _onChangeFormatImage : function(event){
                     this.$(".formatImage-radio").removeClass("active");
                     var $target = $(event.currentTarget);
@@ -73,13 +68,9 @@
                         grilleStyleAff:this.model.grilleStyleAff
                     };
                     this.$el.html(this.template(templateVars));
-                    this.$('.grid-nbreImage .slider').slider({
-                        range: "min",
-                        value: this.model.grilleNbreImage,
-                        min: 1,
-                        max: 5,
-                        step: 1
-                    });
+
+                    $(".grid-nbreImage .slider").val(this.model.grilleNbreImage);
+
                     this.scrollables();
                     this.delegateEvents();
                     return this;
Index: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Templates/loopStyle.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Templates/loopStyle.html	(révision 13317)
+++ src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Templates/loopStyle.html	(révision 13318)
@@ -147,11 +147,14 @@
             <p class="panel-content-legend"><%= __("escapeSizeDesc")%></p>
         </header>
         <div class="option-content  ">
-            <div class="slider-container spacing-slider">
-                <span class="icon-less"></span>
-                <div class="slider spacing-size">
-                </div>
-                <span class="icon-more"></span>
+            <div class="slider-container">
+                <input type="range" class="slider spacing-size" list="loopSpacing-list" min="1" max="4">
+                <datalist id="loopSpacing-list">
+                    <option value="1" label="0"></option>
+                    <option value="2" label="m"></option>
+                    <option value="3" label="l"></option>
+                    <option value="4" label="xl"></option>
+                </datalist>
             </div>
         </div>
     </article>
@@ -164,10 +167,12 @@
         </header>
         <div class="option-content">
             <div class="slider-container">
-                <span class="icon-less"></span>
-                <div class="slider speed-slider">
-                </div>
-                <span class="icon-more"></span>
+                <input type="range" class="slider speed-slider" list="loopSpeed-list" min="1" max="3" >
+                <datalist id="loopSpeed-list">
+                    <option value="1" label="min"></option>
+                    <option value="2" label="normal"></option>
+                    <option value="3" label="max"></option>
+                </datalist>
             </div>
         </div>
     </article>
Index: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopStyleOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopStyleOptionView.js	(révision 13317)
+++ src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopStyleOptionView.js	(révision 13318)
@@ -20,8 +20,8 @@
                         tagName: "div",
                         className: "gallery-template-option galleryStyle panel-content ",
                         events: {
-                            'slidechange .slider.spacing-size': '_setEscapeSize',
-                            'slidechange .slider.speed-slider': '_setSpeedLoop',
+                            'change .slider.spacing-size': '_onSpacingChange',
+                            'change .slider.speed-slider': '_onSpeedChange',
                             'click input[name="colorFilter"]': 'clickColorFilter',
                             'change input[type="radio"].select-box': '_onStyleAffichageChange',
                             'click .effect-radio': '_onChangeFormatImage',
@@ -34,6 +34,12 @@
                             this._super();
                             this.template = this.buildTemplate(loopStyle, translate);
                         },
+                        _onSpacingChange: function(event){
+                            this.model.escapeSize = parseInt( $(event.currentTarget).val() ) ;
+                        },
+                        _onSpeedChange: function(event){
+                            this.model.speedLoop = parseInt( $(event.currentTarget).val() ) ;
+                        },
                         _onChangeFormatImage:function(event){
                            this.$(".effect-radio").removeClass("active");
                             var $target = $(event.currentTarget);
@@ -87,25 +93,6 @@
                                 $('.loop-speed').show();
                             }
                         },
-                        /**
-                         * Slider change
-                         */
-                        _setSpeedLoop: function(event,ui){
-                            var value = ui.value;
-                            this.model.speedLoop = value;
-                            return false;
-                         },
-                          /**
-                         * Slider change
-                         */
-                        _setEscapeSize: function(event,ui){
-                            var value = ui.value;
-                            this.model.escapeSize = value;
-                            return false;
-                         },
-                        /**
-                         * actualise l'affichage de la vue
-                         */
                         render: function() {
                             //magique
                            // this.undelegateEvents();
@@ -128,21 +115,10 @@
                                 $('.loop-speed').hide();
                             }
                            
-                            this.dom[this.cid].slider = this.$('.loop-speed .slider');
-                            this.dom[this.cid].slider.slider({
-                                min: 1,
-                                max: 3,
-                                step: 1,
-                                value: this.model.speedLoop,
-                                range:"min"
-                            });
-                            this.dom[this.cid].spacingSlider = this.$('.loop-spacing .slider');
-                            this.dom[this.cid].spacingSlider.slider({range: "min",
-                                value: this.model.escapeSize,
-                                min: 1,
-                                max: 4,
-                                step: 1
-                            });
+                            $(".loop-speed .slider").val(this.model.speedLoop);
+
+                            $(".loop-spacing .slider").val(this.model.escapeSize);
+
                             this._controlSolidBg(this.$('input[name="colorFilter"]'));
                             this.scrollables();
                             return this;
Index: src/js/JEditor/PagePanel/Contents/Options/Templates/fadeOptions.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/Templates/fadeOptions.html	(révision 13317)
+++ src/js/JEditor/PagePanel/Contents/Options/Templates/fadeOptions.html	(révision 13318)
@@ -103,12 +103,16 @@
             <p class="option-description"><%= __("delayDesc")%></p>
         </header>
         <div class="option-content">
-            <!--slider-->
             <div class="slider-container delay-slider">
-                <span class="less icon"></span>
-                <div class="slider delay">
-                </div>
-                <span class="more icon"></span>
+                <input class="slider delay" type="range" list="animationDelay-list" min="1" max="6" >
+                <datalist id="animationDelay-list">
+                    <option value="1" label="0 sec"></option>
+                    <option value="2" label="0.3 sec"></option>
+                    <option value="3" label="0.6 sec"></option>
+                    <option value="4" label="0.9 sec"></option>
+                    <option value="5" label="1.2 sec"></option>
+                    <option value="6" label="1.5 sec"></option>
+                </datalist>
             </div>
             <!--end slider-->
         </div>
@@ -121,12 +125,16 @@
             <p class="option-description"><%= __("durationDesc")%></p>
         </header>
         <div class="option-content">
-            <!--slider-->
             <div class="slider-container duration-slider">
-                <span class="less icon"></span>
-                <div class="slider duration">
-                </div>
-                <span class="more icon"></span>
+                <input class="slider duration" type="range" list="animationDuration-list" min="1" max="6" >
+                <datalist id="animationDuration-list">
+                    <option value="1" label="0.5 sec"></option>
+                    <option value="2" label="1 sec"></option>
+                    <option value="3" label="1.5 sec"></option>
+                    <option value="4" label="2 sec"></option>
+                    <option value="5" label="2.5 sec"></option>
+                    <option value="6" label="3 sec"></option>
+                </datalist>
             </div>
             <!-- end slider-->
         </div>
Index: src/js/JEditor/PagePanel/Contents/Options/Templates/stylesOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/Templates/stylesOption.html	(révision 13317)
+++ src/js/JEditor/PagePanel/Contents/Options/Templates/stylesOption.html	(révision 13318)
@@ -64,7 +64,15 @@
             <p class="panel-content-legend"><%= __("shadeWornDesc")%></p>
         </header>
         <div class="option-content">
-            <div class="slider-container"><span class="icon-less"></span><div class="slider" id="shadeWorn"></div><span class="icon-more"></span></div>
+            <div class="slider-container">
+                <input type="range" class="slider" list="shadeWorn-list" min="0" max="3">
+                <datalist id="shadeWorn-list">
+                    <option value="0" label=""></option>
+                    <option value="1" label="||"></option>
+                    <option value="2" label="|||"></option>
+                    <option value="3" label="||||"></option>
+                </datalist>
+            </div>
         </div>
     </article>
      <!-- Ombre porté end-->
@@ -75,7 +83,18 @@
             <p class="panel-content-legend"><%= __("contentSizeDesc")%></p>
         </header>
         <div class="option-content">
-            <div class="slider-container"><span class="icon-less"></span><div class="slider" id="contentSize"></div><span class="icon-more"></span></div>
+            <div class="slider-container">
+                <input type="range" class="slider"  list="contentSize-list" min="1" max="7">
+                <datalist id="contentSize-list">
+                    <option value="1" label="xs"></option>
+                    <option value="2" label="s"></option>
+                    <option value="3" label="m"></option>
+                    <option value="4" label="l"></option>
+                    <option value="5" label="xl"></option>
+                    <option value="6" label="2xl"></option>
+                    <option value="7" label="3xl"></option>
+                </datalist>
+            </div>
         </div>
     </article>
      <!-- Taille du contenu end -->
Index: src/js/JEditor/PagePanel/Contents/Options/Views/FadeOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/Views/FadeOptionView.js	(révision 13317)
+++ src/js/JEditor/PagePanel/Contents/Options/Views/FadeOptionView.js	(révision 13318)
@@ -20,12 +20,18 @@
                                 translate: translate,
                                 events: {
                                     'change input[type="radio"].select-box': '_onTypeChange',
-                                    'slide .slider.delay': '_setDelay',
-                                    'slide .slider.duration': '_setDuration'
+                                    'change .slider.delay': '_onDelayChange',
+                                    'change .slider.duration': '_onDurationChange'
                                 },
                                 /**
                                  * initialise la vue
                                  */
+                                _onDurationChange: function(event){
+                                    this.model.duration = parseInt( $(event.currentTarget).val() ) ;
+                                },
+                                _onDelayChange: function(event){
+                                    this.model.delay = parseInt( $(event.currentTarget).val() ) ;
+                                },
                                 initialize: function() {
                                     this._super();
                                     this._template = this.buildTemplate(fadeOptionsTpl, translate);
@@ -37,42 +43,12 @@
                                     var $target = $(event.currentTarget);
                                     this.model.type = parseInt($target.val());
                                 },
-                                /**
-                                 * change le delay
-                                */
-                                _setDelay: function(e, data) {
-                                    this.model.delay = data.value;
-                                },
-                                /**
-                                 * change le duration
-                                 */
-                                _setDuration: function(e, data) {
-                                    this.model.duration = data.value;
-                                },
-                                /**
-                                 * actualise la vue
-                                 */
                                 render: function() {
                                     this.$el.html(this._template(this.model.toJSON()));
-                                    var sliderDuration = this.$('.duration-slider .slider');
-                                    sliderDuration.slider({
-                                        min: 1,
-                                        max: 6,
-                                        step: 1,
-                                        value:this.model.duration,
-                                        range:"min"
-                                    });
-                                    var sliderDetails = this.$('.delay-slider .slider');
-                                    sliderDetails.slider({
-                                        min: 1,
-                                        max: 6,
-                                        step: 1,
-                                        value:this.model.delay,
-                                        range:"min"
-                                    });
-                                    this.dom[this.cid].sliderDuration = sliderDuration;
-                                    this.dom[this.cid].sliderDetails = sliderDetails;
 
+                                    $(".duration-slider .slider").val(this.model.duration);
+                                    $(".delay-slider .slider").val(this.model.delay);
+
                                     this.scrollables();
                                     return this;
                                 }
Index: src/js/JEditor/PagePanel/Contents/Options/Views/StylesOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/Views/StylesOptionView.js	(révision 13317)
+++ src/js/JEditor/PagePanel/Contents/Options/Views/StylesOptionView.js	(révision 13318)
@@ -35,6 +35,8 @@
                                 events: {
                                     'click  div .effect-radio'   : '_onChangeStyleColor',
                                     'slide .slider': '_onSliderChange',
+                                    'change .shadeWorn .slider': '_onShadeWornChange',
+                                    'change .contentSize .slider': '_onContentSizeChange',
                                     'click input[name=parentSWidth]': '_onChangeSectionWidth',
                                     'click input[name=childSWidth]': '_onChangeSectionScreenWidth',
                                     // 'slidechange .slider': '_onSliderChange',
@@ -81,10 +83,10 @@
                                     this._toggleScreenWidthContainer(defaultParentSWidth);
                                     //Size content 1 -> 7
                                     var defaultContentSize =(this.model.contentSize)? (this.model.getContentSizeKeyByValue(this.model.contentSize)) :this.model.getDefaultConst().SizeContent;
-                                    this._slideOption(".contentSize",1,7,defaultContentSize);
+                                    $(".contentSize .slider").val(defaultContentSize);
                                     //Shade Worn 0 -> 3
                                     var defaultShadeWorn = (this.model.shadeWorn)? (this.model.getShadeWornKeyByValue(this.model.shadeWorn)) : this.model.getDefaultConst().ShadowWorn; 
-                                    this._slideOption(".shadeWorn" ,0,3,defaultShadeWorn);
+                                    $(".shadeWorn .slider").val(defaultShadeWorn);
 
                                     this.scrollables();
                                     return this;
@@ -147,7 +149,12 @@
                                  * changement des valeurs depuis un slider
                                  * @private
                                  */
-
+                                _onContentSizeChange: function(event){
+                                    this.model.setContentSize(parseInt( $(event.currentTarget).val() ));
+                                },
+                                _onShadeWornChange: function(event){
+                                    this.model.setShadeWorn(parseInt( $(event.currentTarget).val() ));
+                                },
                                 _onSliderChange: function(event, ui) {
                                    var id = event.target.id;
                                     if(id ==="contentSize")
Index: src/less/imports/panel.less
===================================================================
--- src/less/imports/panel.less	(révision 13317)
+++ src/less/imports/panel.less	(révision 13318)
@@ -242,9 +242,6 @@
 
 //sliders
 .slider-container {
-    height: 20px;
-    margin: 20px 0;
-    position: relative;
 
     span.icon {
         display: block;
Index: src/less/imports/slider.css
===================================================================
--- src/less/imports/slider.css	(nonexistent)
+++ src/less/imports/slider.css	(révision 13318)
@@ -0,0 +1,54 @@
+.slider-container {
+    height: 40px;
+    margin: 0 0 20px;
+    position: relative;
+}
+.slider-container input[type=range] {
+    accent-color: #34d399;
+    box-sizing: border-box;
+    width: 100%;
+}
+.slider-container datalist {
+    display: flex;
+    justify-content: space-between;
+    width: 100%;
+    font-size: 12px;
+    color: gray;
+    font-variant-numeric: lining-nums;
+}
+.slider-container option {
+    width: 12px;
+    display: flex;
+    justify-content: center;
+}
+.slider-container option:first-child {
+    justify-content: start;
+}
+.slider-container option:last-child {
+    justify-content: end;
+}
+
+/* specifique contentSize */
+.slider-container datalist#contentSize-list,
+.slider-container datalist#spacing-list {
+    text-transform: uppercase;
+}
+
+/* specifique shadeWorn */
+.slider-container datalist#shadeWorn-list {
+    flex-direction: column;
+    writing-mode: vertical-lr;
+    align-items: start;
+}
+.slider-container datalist#shadeWorn-list option {
+    align-items: center;
+    justify-content: start;
+}
+.slider-container datalist#shadeWorn-list option:first-child {
+    align-items: start;
+    justify-content: start;
+}
+.slider-container datalist#shadeWorn-list option:last-child {
+    align-items: end;
+    justify-content: start;
+}
\ No newline at end of file
Index: src/less/main.less
===================================================================
--- src/less/main.less	(révision 13317)
+++ src/less/main.less	(révision 13318)
@@ -2,6 +2,7 @@
 @import (inline) "./imports/animations.css";
 @import (inline) "./imports/linkeoplayer-0.20.css";
 @import (inline) "./imports/owl.carousel.css";
+@import (inline) "./imports/slider.css";
 @import "./imports/variables.less";
 @import "./imports/animations.less";
 @import "./imports/mixins.less";
