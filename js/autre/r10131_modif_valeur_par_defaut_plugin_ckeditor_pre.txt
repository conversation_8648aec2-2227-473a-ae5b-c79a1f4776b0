Revision: r10131
Date: 2023-01-11 17:22:38 +0300 (lrb 11 Jan 2023) 
Author: anthony 

## Commit message
modif valeur par defaut plugin ckeditor pre

## Files changed

## Full metadata
------------------------------------------------------------------------
r10131 | anthony | 2023-01-11 17:22:38 +0300 (lrb 11 Jan 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js

modif valeur par defaut plugin ckeditor pre
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 10130)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 10131)
@@ -53,6 +53,8 @@
                             pasteFromWordRemoveFontStyles: true,
                             language:window.userLocale.split('-')[0],
                             preDefaultTitle: translate("defaultAccordionTitle"),
+                            preDefaultMonospace: false,
+                            preDefaultWrap: true,
                             allowedContent: true,
                             format_tags: 'p;h2;h3;h4;h5;h6',
                             entities:false,
