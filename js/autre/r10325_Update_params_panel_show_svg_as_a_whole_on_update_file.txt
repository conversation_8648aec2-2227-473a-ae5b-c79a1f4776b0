Revision: r10325
Date: 2023-02-03 11:48:52 +0300 (zom 03 Feb 2023) 
Author: jn.harison 

## Commit message
Update params panel:show svg as a whole on update file

## Files changed

## Full metadata
------------------------------------------------------------------------
r10325 | jn.harison | 2023-02-03 11:48:52 +0300 (zom 03 Feb 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/MarqueClientView.js

Update params panel:show svg as a whole on update file
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Views/MarqueClientView.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 10324)
+++ src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 10325)
@@ -58,6 +58,11 @@
             this.$('.menu-wrapper-logo .uploader .preview .imagepreview').css({opacity: 1});
             this.$('.menu-wrapper-logo .uploader .preview .imagepreview .progressbar').css({width: 0});
             this.$('.group-content-logo .rigth-delete-image').show();
+            if(this.model.attributes.Logo.ext === "svg"){
+                this.$('.menu-wrapper-logo .uploader .preview .imagepreview').css({backgroundSize:'100%'});
+                this.$('.menu-wrapper-logo .uploader .preview .imagepreview').css({backgroundRepeat:'no-repeat'});
+                this.$('.menu-wrapper-logo .uploader .preview .imagepreview').css({backgroundPosition:'center'});
+            }      
         },
 
         _onUpload2: function(file) {
@@ -71,6 +76,11 @@
             this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview').css({opacity: 1});
             this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview .progressbar').css({width: 0});
             this.$('.group-content-logo-small .rigth-delete-image').show();
+            if(this.model.attributes.LogoSmall.ext === "svg"){
+                this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview').css({backgroundSize:'100%'});
+                this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview').css({backgroundRepeat:'no-repeat'});
+                this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview').css({backgroundPosition:'center'});
+            }   
         },
 
         _onUpload3: function(file) {
@@ -84,8 +94,12 @@
             this.$('.menu-wrapper-favicon .uploader .preview .imagepreview').css({opacity: 1});
             this.$('.menu-wrapper-favicon .uploader .preview .imagepreview .progressbar').css({width: 0});
             this.$('.group-content-favicon .rigth-delete-image').show();
+            if(this.model.attributes.Favicon.ext === "svg"){
+                this.$('.menu-wrapper-favicon .uploader .preview .imagepreview').css({backgroundSize:'100%'});
+                this.$('.menu-wrapper-favicon .uploader .preview .imagepreview').css({backgroundRepeat:'no-repeat'});
+                this.$('.menu-wrapper-favicon .uploader .preview .imagepreview').css({backgroundPosition:'center'});
+            }   
         },
-
         _onlyComputerLogo: function(e) {
             e.preventDefault();
             e.stopImmediatePropagation();
@@ -111,7 +125,7 @@
             this.$('.menu-wrapper-favicon .uploader .actions-wrapper').removeClass('visible');
             this.$('.menu-wrapper-favicon .uploader .actions-wrapper .actions .computer input:file.upload').trigger('click');
         },
-
+        
         render: function () {
             this.$el.html(this._template(this.model.toJSON()));
             if(this.model.attributes.Logo && (!Array.isArray(this.model.attributes.Logo)))
