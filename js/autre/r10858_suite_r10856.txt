Revision: r10858
Date: 2023-04-27 11:44:21 +0300 (lkm 27 Apr 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
suite r10856

## Files changed

## Full metadata
------------------------------------------------------------------------
r10858 | srazanandralisoa | 2023-04-27 11:44:21 +0300 (lkm 27 Apr 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Models/Params.js

suite r10856
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Models/Params.js
===================================================================
--- src/js/JEditor/ParamsPanel/Models/Params.js	(révision 10857)
+++ src/js/JEditor/ParamsPanel/Models/Params.js	(révision 10858)
@@ -86,6 +86,14 @@
                 $('#customData').val(model.attributes['JsonLdDefaultData']);
             }
         },
+        validateSocialNetworks:function (attributes) {
+            if (attributes) { 
+                 var type = attributes.type ;
+                 if (attributes.url && (!attributes.url.match || !attributes.url.match(regExes[type])))
+                    return false;
+                 else return true;
+            }
+        },
         validate: function (attributes, options) {
             var socialNetworks = ["facebookUrl", "mybusinessUrl", "twitterUrl", "pinterestUrl", "youtubeUrl", "linkedinUrl", "viadeoUrl", "instagramUrl","skypeUrl","theforkUrl","tiktokUrl","tripadvisorUrl","wazeUrl","whatsappUrl","slideshareUrl"];
             //en gros c'est ça dans une boucle:
