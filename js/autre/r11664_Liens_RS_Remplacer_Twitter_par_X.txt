Revision: r11664
Date: 2023-12-05 08:38:41 +0300 (tlt 05 Des 2023) 
Author: rrakotoarinelina 

## Commit message
Liens RS : Remplacer Twitter par X

## Files changed

## Full metadata
------------------------------------------------------------------------
r11664 | rrakotoarinelina | 2023-12-05 08:38:41 +0300 (tlt 05 Des 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Models/Params.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Templates/ListSocialNetwork.html
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Templates/SocialNetwork.html
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/SocialNetworks.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/i18n.js

Liens RS : Remplacer Twitter par X
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Models/Params.js
===================================================================
--- src/js/JEditor/ParamsPanel/Models/Params.js	(révision 11663)
+++ src/js/JEditor/ParamsPanel/Models/Params.js	(révision 11664)
@@ -17,7 +17,9 @@
     var RGPDLinkRegex=/^(http(s)?:\/\/www\.|http(s)?:\/\/)[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\#[a-z0-9]+)?(\/[^"'<>]*)?$/;
     var regExes = {
         facebookUrl:/^(https?\:\/\/)?(www\.)?facebook\.com\/(.*)/,
-        twitterUrl:/^(https?\:\/\/)?(www\.)?twitter\.com\/(?:#!\/)?([a-zA-Z0-9\._-]+)/,
+        // twitterUrl:/^(https?\:\/\/)?(www\.)?twitter\.com\/(?:#!\/)?([a-zA-Z0-9\._-]+)/,
+        twitterUrl:/^(https?\:\/\/)?(www\.)?(twitter|x)\.com\/(?:#!\/)?([a-zA-Z0-9\._-]+)/,
+        xUrl:/^(https?\:\/\/)?(www\.)?(twitter|x)\.com\/(?:#!\/)?([a-zA-Z0-9\._-]+)/,
         //googleUrl:/^(https?\:\/\/)([^\.]+\.)?plus.google.com\/((u\/0\/)?(?:[^\/]*))?/,
         mybusinessUrl:/^(https?\:\/\/)?(www\.)?goo(\.)?gl(e\.com)?(\/maps\/.+)|^(https?\:\/\/)?maps\.app\.goo\.gl\/(.*)/,
         pinterestUrl:/^(https?\:\/\/)?([a-zA-Z]*\.)?pinterest\.(fr|com)\/([a-zA-Z0-9\._-]+)\/?/,
@@ -41,6 +43,7 @@
         defaults: {
             facebookUrl: null,
             twitterUrl: null,
+            xUrl: null,
             //googleUrl: null,
             mybusinessUrl: null,
             pinterestUrl: null,
@@ -76,7 +79,7 @@
         },
         initialize: function () {
             this._super();
-            this.on("change:facebookUrl change:twitterUrl change:pinterestUrl change:mybusinessUrl change:youtubeUrl change:linkedinUrl change:viadeoUrl change:instagramUrl change:skypeUrl change:theforkUrl change:tiktokUrl change:tripadvisorUrl change:wazeUrl change:whatsappUrl change:slideshareUrl", this.onSocialNetworkUrlChange);
+            this.on("change:facebookUrl change:twitterUrl change:xUrl change:pinterestUrl change:mybusinessUrl change:youtubeUrl change:linkedinUrl change:viadeoUrl change:instagramUrl change:skypeUrl change:theforkUrl change:tiktokUrl change:tripadvisorUrl change:wazeUrl change:whatsappUrl change:slideshareUrl", this.onSocialNetworkUrlChange);
             this.on("change:MarqueClient", this.onMarqueClientChange);
         },
         onSocialNetworkUrlChange: function (model) {
@@ -83,7 +86,7 @@
             var changed = this.changedAttributes();
             var that = this;
             _.each(changed, function (value, key) {
-                if ((key === "facebookUrl" || key === "twitterUrl" || key === "pinterestUrl" || key === "mybusinessUrl" || key==="instagramUrl"||key==="youtubeUrl"||key==="linkedinUrl"||key==="viadeoUrl" || key==="skypeUrl" || key==="theforkUrl" || key ==="tiktokUrl" || key ==="tripadvisorUrl" || key==="wazeUrl" || key==="whatsappUrl" || key==="slideshareUrl")) {
+                if ((key === "facebookUrl" || key === "twitterUrl" || key === "xUrl" || key === "pinterestUrl" || key === "mybusinessUrl" || key==="instagramUrl"||key==="youtubeUrl"||key==="linkedinUrl"||key==="viadeoUrl" || key==="skypeUrl" || key==="theforkUrl" || key ==="tiktokUrl" || key ==="tripadvisorUrl" || key==="wazeUrl" || key==="whatsappUrl" || key==="slideshareUrl")) {
                     that.trigger(Events.SettingsEvents.SOCIAL_NETWORK__CHANGE, key, value, model);
                 }
             });
@@ -110,7 +113,7 @@
             
        
 
-            var socialNetworks = ["facebookUrl", "mybusinessUrl", "twitterUrl", "pinterestUrl", "youtubeUrl", "linkedinUrl", "viadeoUrl", "instagramUrl","skypeUrl","theforkUrl","tiktokUrl","tripadvisorUrl","wazeUrl","whatsappUrl","slideshareUrl"];
+            var socialNetworks = ["facebookUrl", "mybusinessUrl", "twitterUrl", "xUrl", "pinterestUrl", "youtubeUrl", "linkedinUrl", "viadeoUrl", "instagramUrl","skypeUrl","theforkUrl","tiktokUrl","tripadvisorUrl","wazeUrl","whatsappUrl","slideshareUrl"];
             //en gros c'est ça dans une boucle:
             for (var i = 0; i < socialNetworks.length; i++) {             
                 if (attributes[socialNetworks[i]] && (!attributes[socialNetworks[i]].match || !attributes[socialNetworks[i]].match(regExes[socialNetworks[i]]) || !attributes[socialNetworks[i]].match(quotesRegex)))
Index: src/js/JEditor/ParamsPanel/Templates/ListSocialNetwork.html
===================================================================
--- src/js/JEditor/ParamsPanel/Templates/ListSocialNetwork.html	(révision 11663)
+++ src/js/JEditor/ParamsPanel/Templates/ListSocialNetwork.html	(révision 11664)
@@ -22,14 +22,11 @@
                        
                     </span>
                    
-                <% } if( social.type =="twitterUrl") { %>
+                <% } if( social.type =="twitterUrl" || social.type =="xUrl"  ) { %>
                     <span class="inline-params__name  twitter">
-                        <svg width="50px" height="50px" viewBox="0 0 50 50" enable-background="new 0 0 50 50" xml:space="preserve">
-                            <path fill="#2CAAE1" d="M42.188,0H7.813C3.497,0,0,3.5,0,7.813v34.375C0,46.502,3.497,50,7.813,50h34.375C46.502,50,50,46.502,50,42.188V7.813C50,3.5,46.502,0,42.188,0z M34.941,19.903c0.01,0.22,0.015,0.439,0.015,0.662c0,6.778-5.159,14.594-14.593,14.594c-2.898,0-5.592-0.849-7.863-2.304c0.403,0.048,0.81,0.069,1.224,0.069c2.405,0,4.614-0.817,6.369-2.194c-2.243-0.039-4.138-1.522-4.79-3.561c0.313,0.061,0.633,0.091,0.964,0.091c0.469,0,0.922-0.061,1.351-0.18c-2.344-0.47-4.114-2.545-4.114-5.029c0-0.021,0-0.043,0-0.063c0.693,0.384,1.483,0.616,2.324,0.641c-1.376-0.919-2.282-2.49-2.282-4.27c0-0.94,0.253-1.819,0.695-2.579c2.528,3.104,6.309,5.146,10.571,5.359c-0.087-0.376-0.133-0.767-0.133-1.169c0-2.832,2.297-5.127,5.13-5.127c1.474,0,2.808,0.622,3.743,1.617c1.167-0.229,2.266-0.656,3.258-1.242c-0.385,1.196-1.196,2.203-2.256,2.835c1.037-0.122,2.026-0.396,2.945-0.806C36.813,18.277,35.943,19.18,34.941,19.903z"
-                            />
-                        </svg>
+                       <svg width="20" height="20" viewBox="0 0 20 20" fill="#000" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M3.1252 0H16.8752C18.6008 0 20 1.4 20 3.1252V16.8752C20 18.6008 18.6008 20 16.8752 20H3.1252C1.3988 20 0 18.6008 0 16.8752V3.1252C0 1.4 1.3988 0 3.1252 0ZM16.5459 3L11.3335 8.92867L17 17H12.8309L9.01369 11.5636L4.235 17H3L8.46604 10.7834L3 3H7.16908L10.7829 8.14671L15.3109 3H16.5459ZM9.08675 10.0762L9.64142 10.8518L13.4131 16.1334H15.3103L10.687 9.66391L10.1347 8.88837L6.57759 3.91102H4.68037L9.08675 10.0762Z"/></svg>
                         <% 
-                            var Twitter= (social.title ==="")?__("Twitter"):social.title;
+                            var Twitter= (social.title ==="")?__("X"):social.title;
                         %>
                         <input class="content rs" type="text" name="<%=social.type%>_<%=i %>" value='<%-Twitter%>' data-titleId="<%=social.type%>_<%=i %>"  data-reseaux="Twitter"/>
                     </span> 
Index: src/js/JEditor/ParamsPanel/Templates/SocialNetwork.html
===================================================================
--- src/js/JEditor/ParamsPanel/Templates/SocialNetwork.html	(révision 11663)
+++ src/js/JEditor/ParamsPanel/Templates/SocialNetwork.html	(révision 11664)
@@ -34,11 +34,8 @@
                 </a>
             </li>
             <li>
-                <a  data-id="twitterUrl" class="twitter">
-                    <svg width="20px" height="20px" viewBox="0 0 50 50" enable-background="new 0 0 50 50" xml:space="preserve">
-                        <path fill="#2CAAE1" d="M42.188,0H7.813C3.497,0,0,3.5,0,7.813v34.375C0,46.502,3.497,50,7.813,50h34.375C46.502,50,50,46.502,50,42.188V7.813C50,3.5,46.502,0,42.188,0z M34.941,19.903c0.01,0.22,0.015,0.439,0.015,0.662c0,6.778-5.159,14.594-14.593,14.594c-2.898,0-5.592-0.849-7.863-2.304c0.403,0.048,0.81,0.069,1.224,0.069c2.405,0,4.614-0.817,6.369-2.194c-2.243-0.039-4.138-1.522-4.79-3.561c0.313,0.061,0.633,0.091,0.964,0.091c0.469,0,0.922-0.061,1.351-0.18c-2.344-0.47-4.114-2.545-4.114-5.029c0-0.021,0-0.043,0-0.063c0.693,0.384,1.483,0.616,2.324,0.641c-1.376-0.919-2.282-2.49-2.282-4.27c0-0.94,0.253-1.819,0.695-2.579c2.528,3.104,6.309,5.146,10.571,5.359c-0.087-0.376-0.133-0.767-0.133-1.169c0-2.832,2.297-5.127,5.13-5.127c1.474,0,2.808,0.622,3.743,1.617c1.167-0.229,2.266-0.656,3.258-1.242c-0.385,1.196-1.196,2.203-2.256,2.835c1.037-0.122,2.026-0.396,2.945-0.806C36.813,18.277,35.943,19.18,34.941,19.903z"
-                        />
-                    </svg>
+                <a  data-id="xUrl" class="x">
+                    <svg width="20" height="20" viewBox="0 0 20 20" fill="#000" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M3.1252 0H16.8752C18.6008 0 20 1.4 20 3.1252V16.8752C20 18.6008 18.6008 20 16.8752 20H3.1252C1.3988 20 0 18.6008 0 16.8752V3.1252C0 1.4 1.3988 0 3.1252 0ZM16.5459 3L11.3335 8.92867L17 17H12.8309L9.01369 11.5636L4.235 17H3L8.46604 10.7834L3 3H7.16908L10.7829 8.14671L15.3109 3H16.5459ZM9.08675 10.0762L9.64142 10.8518L13.4131 16.1334H15.3103L10.687 9.66391L10.1347 8.88837L6.57759 3.91102H4.68037L9.08675 10.0762Z"/></svg>
                     <%=__("Twitter")%>
                 </a>
             </li>
@@ -52,7 +49,7 @@
                                 s-0.967-2.156-2.156-2.156c-1.19,0-2.155,0.967-2.155,2.156S13.962,16.748,15.152,16.748z M25.313,0H4.688C2.098,0,0,2.1,0,4.688
                                 v20.625C0,27.901,2.098,30,4.688,30h20.625C27.901,30,30,27.901,30,25.313V4.688C30,2.1,27.901,0,25.313,0z M23,20.551h-0.002
                                 c0,1.347-1.102,2.449-2.449,2.449h-11.1C8.102,23,7,21.897,7,20.551V9.449C7,8.102,8.102,7,9.449,7h11.102
-                                C21.896,7,23,8.102,23,9.449V20.551z" />
+                                C21.896,7,23,8F.102,23,9.449V20.551z" />
                     </svg>
                     <%=__("instagram")%>
                 </a>
Index: src/js/JEditor/ParamsPanel/Views/SocialNetworks.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/SocialNetworks.js	(révision 11663)
+++ src/js/JEditor/ParamsPanel/Views/SocialNetworks.js	(révision 11664)
@@ -23,6 +23,7 @@
             this.networks = {
                 facebookUrl: 'facebook',
                 twitterUrl: 'twitter',
+                xUrl: 'x',
                 mybusinessUrl: 'googlemybusiness',
                 pinterestUrl: 'pinterest',
                 viadeoUrl: 'viadeo',
@@ -45,7 +46,6 @@
             // remettre à vide l'autre attribut direct dans le model params ex this.model.facebookUrl
             // on note que ce model n'est pas enregistré dans la BD mais on le garde juste pour la validation
             this.model.set(data.type , "");
-            console.log(this.model.get("SocialNetworksUrl"));
             this.model.save();
             this.render();
         },
Index: src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 11663)
+++ src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 11664)
@@ -9,6 +9,7 @@
         "Invalid_theforkUrl":"L'url renseignée ne semble pas valide",
         "Invalid_facebookUrl":"L'url renseignée ne semble pas valide",
         "Invalid_twitterUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_xUrl":"L'url renseignée ne semble pas valide",
         "Invalid_googleUrl":"L'url renseignée ne semble pas valide",
         "Invalid_mybusinessUrl":"L'url renseignée ne semble pas valide",
         "Invalid_pinterestUrl":"L'url renseignée ne semble pas valide",
@@ -25,7 +26,8 @@
         "Invalid_PVYoutubeId":"L'identifant de la vidéo youtube ne semble pas valide",
         "socialNetworksDesc":"Définissez les adresses de vos pages sur les réseaux sociaux.",
         "Facebook":"Facebook",
-        "Twitter":"Twitter",
+        "Twitter":"X(Twitter)",
+        "X":"X",
         "Pinterest":"Pinterest",
         "Google":"Google +",
         "copypastecodeSocialNetworks":"Copiez et collez ce code dans le bloc HTML où vous souhaitez afficher les liens vers vos réseaux sociaux.",
Index: src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 11663)
+++ src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 11664)
@@ -9,6 +9,7 @@
         "Invalid_theforkUrl":"L'url renseignée ne semble pas valide",
         "Invalid_facebookUrl":"L'url renseignée ne semble pas valide",
         "Invalid_twitterUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_xUrl":"L'url renseignée ne semble pas valide",
         "Invalid_googleUrl":"L'url renseignée ne semble pas valide",
         "Invalid_mybusinessUrl":"L'url renseignée ne semble pas valide",
         "Invalid_pinterestUrl":"L'url renseignée ne semble pas valide",
@@ -26,7 +27,8 @@
         "socialNetworksDesc":"Définissez les adresses de vos pages sur les réseaux sociaux.",
         "Skype":    "Skype",
         "Facebook":"Facebook",
-        "Twitter":"Twitter",
+        "Twitter":"X(Twitter)",
+        "X":"X",
         "Pinterest":"Pinterest",
         "Google":"Google +",
         "copypastecodeSocialNetworks":"Copiez et collez ce code dans le bloc HTML où vous souhaitez afficher les liens vers vos réseaux sociaux.",
Index: src/js/JEditor/ParamsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 11663)
+++ src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 11664)
@@ -10,6 +10,7 @@
         "Invalid_theforkUrl":"This is not a valid url",
         "Invalid_facebookUrl":"This is not a valid url",
         "Invalid_twitterUrl":"This is not a valid url",
+        "Invalid_xUrl":"This is not a valid url",
         "Invalid_googleUrl":"This is not a valid url",
         "Invalid_mybusinessUrl":"This is not a valid url",
         "Invalid_pinterestUrl":"This is not a valid url",
@@ -33,7 +34,8 @@
         'Slideshare'    :   "Slideshare",
         "Skype":    "Skype",
         "Facebook":"Facebook",
-        "Twitter":"Twitter",
+        "X":"X",
+        "Twitter":"X(Twitter)",
         "Pinterest":"Pinterest",
         "Google":"Google +",
         "Mybusiness":"Google My Business",
