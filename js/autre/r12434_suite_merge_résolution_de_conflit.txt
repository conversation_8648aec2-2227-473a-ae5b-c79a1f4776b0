Revision: r12434
Date: 2024-06-18 17:15:44 +0300 (tlt 18 Jon 2024) 
Author: jn.harison 

## Commit message
suite merge:résolution de conflit

## Files changed

## Full metadata
------------------------------------------------------------------------
r12434 | jn.harison | 2024-06-18 17:15:44 +0300 (tlt 18 Jon 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/App/routes/main.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/App/routes_build.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/App/routes_define.json
   M /branches/ideo3_v2/integration_news/src/js/JEditor/DashBoard/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/DashBoard/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/DashBoard/nls/i18n.js

suite merge:résolution de conflit
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/App/routes/main.js
===================================================================
--- src/js/JEditor/App/routes/main.js	(révision 12433)
+++ src/js/JEditor/App/routes/main.js	(révision 12434)
@@ -40,7 +40,7 @@
                 quote,
                 icom,
                 restaurant,
-                news
+                news,
                 feedget,
                 marketingauto
                 )
@@ -64,7 +64,7 @@
                 quote:quote,
                 icom:icom,
                 restaurant:restaurant,
-                news:news
+                news:news,
                 feedget:feedget,
                 marketingauto:marketingauto
             };
Index: src/js/JEditor/App/routes_build.js
===================================================================
--- src/js/JEditor/App/routes_build.js	(révision 12433)
+++ src/js/JEditor/App/routes_build.js	(révision 12434)
@@ -17,7 +17,7 @@
  "quote":"quote",
  "icom":"icom",
  "restaurant":"restaurant",
- "news":"news(/:lang)(/:type)(/:objectID)(/:contentID)"
+ "news":"news(/:lang)(/:type)(/:objectID)(/:contentID)",
  "feedget":"feedget",
  "marketingauto":"marketingauto"
 });
Index: src/js/JEditor/App/routes_define.json
===================================================================
--- src/js/JEditor/App/routes_define.json	(révision 12433)
+++ src/js/JEditor/App/routes_define.json	(révision 12434)
@@ -17,7 +17,7 @@
   "quote":"quote",
   "icom":"icom",
   "restaurant":"restaurant",
-  "news": "news(/:lang)(/:type)(/:objectID)(/:contentID)"
+  "news": "news(/:lang)(/:type)(/:objectID)(/:contentID)",
   "feedget":"feedget",
   "marketingauto":"marketingauto"
 }
Index: src/js/JEditor/DashBoard/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/DashBoard/nls/fr-ca/i18n.js	(révision 12433)
+++ src/js/JEditor/DashBoard/nls/fr-ca/i18n.js	(révision 12434)
@@ -37,7 +37,7 @@
     "Feedget": "Feedget",
     "feedget_explained":"Affichez sur votre site vos posts Facebook / Instagram et vos avis GMB",
     "news": "Actualités",
-    "news_explained": "Parcourez vos pages depuis le menu de gauche. Ajoutez du contenu et prévisualisez votre site a tout moment"
+    "news_explained": "Parcourez vos pages depuis le menu de gauche. Ajoutez du contenu et prévisualisez votre site a tout moment",
     "Marketingauto":"Marketing Automation",
     "marketingauto_explained":"Gestion de votre base clients et automatisation d'actions",
 });
Index: src/js/JEditor/DashBoard/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/DashBoard/nls/fr-fr/i18n.js	(révision 12433)
+++ src/js/JEditor/DashBoard/nls/fr-fr/i18n.js	(révision 12434)
@@ -37,7 +37,7 @@
     "Feedget": "Feedget",
     "feedget_explained":"Affichez sur votre site vos posts Facebook / Instagram et vos avis GMB",
     "news": "Actualités",
-    "news_explained": "Parcourez vos pages depuis le menu de gauche. Ajoutez du contenu et prévisualisez votre site a tout moment"
+    "news_explained": "Parcourez vos pages depuis le menu de gauche. Ajoutez du contenu et prévisualisez votre site a tout moment",
     "Marketingauto":"Marketing Automation",
     "marketingauto_explained":"Gestion de votre base clients et automatisation d'actions",
 });
Index: src/js/JEditor/DashBoard/nls/i18n.js
===================================================================
--- src/js/JEditor/DashBoard/nls/i18n.js	(révision 12433)
+++ src/js/JEditor/DashBoard/nls/i18n.js	(révision 12434)
@@ -45,7 +45,7 @@
       "Feedget": "Feedget",
       "feedget_explained":"Display your Facebook / Instagram posts and GMB reviews on your website",
       "news": "News",
-      "news_explained": "Browse your pages from the left-hand menu. Add content and preview your site at any time"
+      "news_explained": "Browse your pages from the left-hand menu. Add content and preview your site at any time",
       "Marketingauto":"Marketing Automation",
       "marketingauto_explained":"Manage your customer base and automate marketing actions",
    },
