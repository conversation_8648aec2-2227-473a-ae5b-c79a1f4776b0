Revision: r11142
Date: 2023-07-07 11:59:14 +0300 (zom 07 Jol 2023) 
Author: norajaonarivelo 

## Commit message
Fix :r11138  modification du controller regex url FB 

## Files changed

## Full metadata
------------------------------------------------------------------------
r11142 | norajaonarivelo | 2023-07-07 11:59:14 +0300 (zom 07 Jol 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/VideoBlock/Views/VideoInfoOptionView.js

Fix :r11138  modification du controller regex url FB 
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/VideoBlock/Views/VideoInfoOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/VideoBlock/Views/VideoInfoOptionView.js	(révision 11141)
+++ src/js/JEditor/PagePanel/Contents/Blocks/VideoBlock/Views/VideoInfoOptionView.js	(révision 11142)
@@ -29,7 +29,7 @@
                         replace: 'https://www.dailymotion.com/embed/video/$1',
                     },
                     facebook :{
-                        pattern: /^(?:https?:\/\/|\/\/)?(?:www\.)?facebook\.com\/(.*)\/videos\/(\d+)/,
+                        pattern: /^(?:https?:\/\/|\/\/)?(?:www\.)?(?:[a-z]{2}-[a-z]{2}\.)?facebook\.com\/(.*)\/videos\/(\d+)/,
                         replace: 'https://www.facebook.com/plugins/video.php?height=314&href=https%3A%2F%2Fwww.facebook.com%2F$1%2Fvideos%2F$2%2F&show_text=false&width=560&t=0'
                     }
                 },
