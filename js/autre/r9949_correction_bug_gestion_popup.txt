Revision: r9949
Date: 2022-12-08 07:33:11 +0300 (lkm 08 Des 2022) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
correction bug gestion popup

## Files changed

## Full metadata
------------------------------------------------------------------------
r9949 | srazanandralisoa | 2022-12-08 07:33:11 +0300 (lkm 08 Des 2022) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/Views/PopupOptionView.js

correction bug gestion popup
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Options/Views/PopupOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/Views/PopupOptionView.js	(révision 9948)
+++ src/js/JEditor/PagePanel/Contents/Options/Views/PopupOptionView.js	(révision 9949)
@@ -87,13 +87,14 @@
                                 setBoundingDate: function (event) {
                                     var $target, date, ts;
                                     $target = $(event.currentTarget);
-                                    date = $target.datepicker("getDate");
+                                    date = $target.datepicker({ dateFormat: 'dd/mm/yyyy' }).val();
+                                   // date = $target.datepicker("getDate");
                                     switch ($target.attr('name')) {
                                         case "to":
-                                            this.model.setTo(date.toLocaleDateString());
+                                            this.model.setTo(date);
                                             break;
                                         case "from":
-                                            this.model.setFrom(date.toLocaleDateString());
+                                            this.model.setFrom(date);
                                             break;
                                     }
                                 },
