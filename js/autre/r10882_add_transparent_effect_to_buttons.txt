Revision: r10882
Date: 2023-05-04 12:31:54 +0300 (lkm 04 Mey 2023) 
Author: mpartaux 

## Commit message
add 'transparent' effect to buttons

## Files changed

## Full metadata
------------------------------------------------------------------------
r10882 | mpartaux | 2023-05-04 12:31:54 +0300 (lkm 04 Mey 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/less/imports/button_block/button_color.less
   M /branches/ideo3_v2/integration/src/less/imports/page_panel/module/block-options/advanced-option.less

add 'transparent' effect to buttons
------------------------------------------------------------------------

## Diff
Index: src/less/imports/page_panel/module/block-options/advanced-option.less
===================================================================
--- src/less/imports/page_panel/module/block-options/advanced-option.less	(révision 10881)
+++ src/less/imports/page_panel/module/block-options/advanced-option.less	(révision 10882)
@@ -24,7 +24,10 @@
 	}
 
 	.container { // default color button
-		background: var(--surface0)!important;
+		background-color: #ffffff!important;
+		background-image:  repeating-linear-gradient(45deg, #eaeaea 25%, transparent 25%, transparent 75%, #eaeaea 75%, #eaeaea), repeating-linear-gradient(45deg, #eaeaea 25%, #ffffff 25%, #ffffff 75%, #eaeaea 75%, #eaeaea)!important;
+		background-position: 0 0, 10px 10px!important;
+		background-size: 20px 20px!important;
 		color: var(--fg-color)!important;
 	}
 }
Index: src/less/imports/button_block/button_color.less
===================================================================
--- src/less/imports/button_block/button_color.less	(révision 10881)
+++ src/less/imports/button_block/button_color.less	(révision 10882)
@@ -13,9 +13,14 @@
     --fg-actions: var(--accent-text1);
 }
 .contour{
-    --bg-actions: white;
     --border-actions: var(--fg-links);
     --fg-actions: var(--fg-links);
+    .container {
+        background-color: #ffffff!important;
+        background-image:  repeating-linear-gradient(45deg, #eaeaea 25%, transparent 25%, transparent 75%, #eaeaea 75%, #eaeaea), repeating-linear-gradient(45deg, #eaeaea 25%, #ffffff 25%, #ffffff 75%, #eaeaea 75%, #eaeaea)!important;
+        background-position: 0 0, 10px 10px!important;
+        background-size: 20px 20px!important;
+    }
 }
 .pastel-lead{
     --bg-actions: var(--lead-surface0); 
@@ -28,7 +33,13 @@
     --fg-actions: var(--lead-text1);
 }
 .contour-lead{
-    --bg-actions: white;
     --border-actions: var(--fg-lead);
     --fg-actions: var(--fg-lead);
-}
\ No newline at end of file
+    .container {
+        background-color: #ffffff!important;
+        background-image:  repeating-linear-gradient(45deg, #eaeaea 25%, transparent 25%, transparent 75%, #eaeaea 75%, #eaeaea), repeating-linear-gradient(45deg, #eaeaea 25%, #ffffff 25%, #ffffff 75%, #eaeaea 75%, #eaeaea)!important;
+        background-position: 0 0, 10px 10px!important;
+        background-size: 20px 20px!important;
+    }
+}
+
