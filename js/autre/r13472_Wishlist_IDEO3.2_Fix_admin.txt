Revision: r13472
Date: 2024-11-19 16:02:47 +0300 (tlt 19 Nov 2024) 
Author: frahajanirina 

## Commit message
Wishlist:IDEO3.2:Fix admin

## Files changed

## Full metadata
------------------------------------------------------------------------
r13472 | frahajanirina | 2024-11-19 16:02:47 +0300 (tlt 19 Nov 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/DashBoard/Templates/DashBoardTpl.html
   M /branches/ideo3_v2/integration/src/js/JEditor/DashBoard/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/DashBoard/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/DashBoard/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/dashboard.less

Wishlist:IDEO3.2:Fix admin
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/DashBoard/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/DashBoard/nls/fr-ca/i18n.js	(révision 13471)
+++ src/js/JEditor/DashBoard/nls/fr-ca/i18n.js	(révision 13472)
@@ -40,5 +40,5 @@
     "news_explained": "Parcourez vos pages depuis le menu de gauche. Ajoutez du contenu et prévisualisez votre site a tout moment",
     "Marketingauto":"Marketing Automation",
     "marketingauto_explained":"Gestion de votre base clients et automatisation d'actions",
-    "viewstats":"View statistics >",
+    "viewstats":"Voir les statistiques",
 });
Index: src/js/JEditor/DashBoard/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/DashBoard/nls/fr-fr/i18n.js	(révision 13471)
+++ src/js/JEditor/DashBoard/nls/fr-fr/i18n.js	(révision 13472)
@@ -40,5 +40,5 @@
     "news_explained": "Parcourez vos pages depuis le menu de gauche. Ajoutez du contenu et prévisualisez votre site a tout moment",
     "Marketingauto":"Marketing Automation",
     "marketingauto_explained":"Gestion de votre base clients et automatisation d'actions",
-    "viewstats":"Voir les statistiques >",
+    "viewstats":"Voir les statistiques",
 });
Index: src/js/JEditor/DashBoard/nls/i18n.js
===================================================================
--- src/js/JEditor/DashBoard/nls/i18n.js	(révision 13471)
+++ src/js/JEditor/DashBoard/nls/i18n.js	(révision 13472)
@@ -48,7 +48,7 @@
       "news_explained": "Browse your pages from the left-hand menu. Add content and preview your site at any time",
       "Marketingauto":"Marketing Automation",
       "marketingauto_explained":"Manage your customer base and automate marketing actions",
-      "viewstats":"View statistics >",
+      "viewstats":"View statistics",
    },
    "fr-fr":true,
    "fr-ca":true
Index: src/js/JEditor/DashBoard/Templates/DashBoardTpl.html
===================================================================
--- src/js/JEditor/DashBoard/Templates/DashBoardTpl.html	(révision 13471)
+++ src/js/JEditor/DashBoard/Templates/DashBoardTpl.html	(révision 13472)
@@ -241,9 +241,14 @@
 <div class="stats">
     <div class="row">
         <% if(!env){ %>
-        <iframe width="1024" height="495" src="<%=url%>" scrolling="no" style="border:none;"></iframe>
+        <iframe width="1024" height="495" src="<%=url%>" scrolling="no" style="border:none;" class="stats-dashboard"></iframe>
         <div class="gotostats">
-            <a href="#stats" class="gotostats"><%=__('viewstats')%></a>
+            <button class="btn-stats <%= lang == 'fr' ? 'btn-stats-fr' : 'btn-stats-en' %>">
+                <svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="22" height="22" viewBox="0 0 32 32">
+                    <path d="M16.036 24.723c-6.046 0-11.541-4.159-14.991-9.216 3.481-4.881 8.976-8.228 14.991-8.228 6.027 0 11.54 3.29 14.919 8.197-3.203 5.338-8.687 9.247-14.919 9.247zM4.21 15.548c2.888 3.567 7.153 6.632 11.826 6.632 6.365 0 10.485-4.77 11.835-6.604-2.871-3.377-7.095-5.757-11.835-5.757-6.158 0-10.353 4.053-11.826 5.729zM16.167 10.964c-2.781 0-5.036 2.254-5.036 5.036s2.254 5.036 5.036 5.036c2.782 0 5.037-2.255 5.037-5.036s-2.255-5.036-5.037-5.036zM16.163 15.999c-0.711 0.711-1.863 0.711-2.574 0-0.711-0.71-0.711-1.863 0-2.574 0.711-0.71 1.863-0.71 2.574 0 0.711 0.71 0.711 1.863 0 2.574z" fill="#ffff"/>
+                </svg>
+                <a href="#stats" class="gotostats"><%=__('viewstats')%></a>
+            </button>
         </div>
         <% } %>
 
Index: src/less/imports/dashboard.less
===================================================================
--- src/less/imports/dashboard.less	(révision 13471)
+++ src/less/imports/dashboard.less	(révision 13472)
@@ -260,5 +260,27 @@
 		font-size: 20px;
 		color: black;
 	}
+	.stats-dashboard {
+		color-scheme: auto;
+	}
+	.btn-stats a {
+		color: #fff;
+	}
+	.btn-stats {
+		margin: 24px auto;
+		display: flex;
+		justify-content: space-between;
+		border-radius: 5px;
+		padding: 15px 13px;
+		background-color: #000;
+		line-height: 21px;
+		border: none;
+	}
+	.btn-stats-fr {
+		width: 255px;
+	}
+	.btn-stats-en {
+		width: 200px;
+	}
 
 }
\ No newline at end of file
