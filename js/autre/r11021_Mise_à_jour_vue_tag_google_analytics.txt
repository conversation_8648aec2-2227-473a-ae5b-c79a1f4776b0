Revision: r11021
Date: 2023-06-13 14:31:25 +0300 (tlt 13 Jon 2023) 
Author: norajaonarivelo 

## Commit message
Mise à jour vue tag google analytics

## Files changed

## Full metadata
------------------------------------------------------------------------
r11021 | norajaonarivelo | 2023-06-13 14:31:25 +0300 (tlt 13 Jon 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/dev/#librairies/module/GStats/config/module.config.php
   M /branches/ideo3_v2/dev/#librairies/module/GStats/src/GStats/View/Renderer/GStatsRenderer.php
   M /branches/ideo3_v2/dev/#librairies/vendor/CoreConfigurationService/config/module.config.php
   M /branches/ideo3_v2/dev/#librairies/vendor/CoreConfigurationService/src/CoreConfigurationService/Entity/Settings.php
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Models/Params.js

Mise à jour vue tag google analytics
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Models/Params.js
===================================================================
--- src/js/JEditor/ParamsPanel/Models/Params.js	(révision 11020)
+++ src/js/JEditor/ParamsPanel/Models/Params.js	(révision 11021)
@@ -3,7 +3,7 @@
     "JEditor/Commons/Events",
     "i18n!../nls/i18n"
 ], function (Model, Events, translate) {
-    var GARegex=/^(UA|YT|MO)-[0-9]{1,}-[0-9]{1,}$/;
+    var GARegex=/^(G-\w{10}$)|((UA|YT|MO)-[0-9]{1,}-[0-9]{1,}$)/;
     var GTMRegex=/^(GTM)-[a-zA-Z0-9]{1,}$/;
     var FBPixelRegex=/^[0-9]*$/;
     var FBDomainRegex=/^[^\s]{1,}$/;
