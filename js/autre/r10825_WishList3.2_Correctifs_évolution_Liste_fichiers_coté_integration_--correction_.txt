Revision: r10825
Date: 2023-04-17 16:44:02 +0300 (lts 17 Apr 2023) 
Author: norajaonarivelo 

## Commit message
WishList3.2 :Correctifs  évolution Liste fichiers coté integration --correction retour

## Files changed

## Full metadata
------------------------------------------------------------------------
r10825 | norajaonarivelo | 2023-04-17 16:44:02 +0300 (lts 17 Apr 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Templates/selectFileList.html
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Templates/selectFileListManager.html
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Views/ReadOnlyFileListManagerView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Views/SelectFileView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileManagerUIView.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileManagerUIView.js
   M /branches/ideo3_v2/integration/src/less/imports/filePanel.less

WishList3.2 :Correctifs  évolution Liste fichiers coté integration --correction retour
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Files/Templates/selectFileList.html
===================================================================
--- src/js/JEditor/Commons/Files/Templates/selectFileList.html	(révision 10824)
+++ src/js/JEditor/Commons/Files/Templates/selectFileList.html	(révision 10825)
@@ -2,7 +2,7 @@
     file = content[i];
     %>
     <%if(!file.isLogo){%>
-        <div class="wrapper oneFile <%=(selected && selected==file.id)?'selected':''%> <%=(file.previewClass())%>" data-id="<%=file.id%>" <%if(file.isImg()){%>style="background-image: url(<%= file.thumb %>);"<%}%>>
+        <div class="wrapper oneFile <%=(selected && selected[file.id])?'selected':''%> <%=(file.previewClass())%>" data-id="<%=file.id%>" <%if(file.isImg()){%>style="background-image: url(<%= file.thumb %>);"<%}%>>
              <span class="select"><span class="icon-check"></span></span>
 
             <%if(!file.isImg()){%>
Index: src/js/JEditor/Commons/Files/Templates/selectFileListManager.html
===================================================================
--- src/js/JEditor/Commons/Files/Templates/selectFileListManager.html	(révision 10824)
+++ src/js/JEditor/Commons/Files/Templates/selectFileListManager.html	(révision 10825)
@@ -1,5 +1,5 @@
 <div class="header">
-    <span class="wrapper">
+    <span class="wrapper" id="numberFiles">
         <%
         if(content.length>0){%>
             <span class="numFiles"><%=content.length%></span><%=__('File(s)')%> <span class="numberFiles"><%=content[0].attributes.countFile%></span>
@@ -30,7 +30,7 @@
         <div class="trier-par">
             <%= __('sortBy')%> :
             <a class="sort-dropdown-toggle" href="#">
-                <span class="text"><%= __('fileSortcreatedAtDesc')%></span>
+                <span class="text" id="texteTriePar"><%= __('fileSortcreatedAtDesc')%></span>
                 <span class="caret"></span>
             </a>
             <ul class="dropdown-menu">
Index: src/js/JEditor/Commons/Files/Views/ReadOnlyFileListManagerView.js
===================================================================
--- src/js/JEditor/Commons/Files/Views/ReadOnlyFileListManagerView.js	(révision 10824)
+++ src/js/JEditor/Commons/Files/Views/ReadOnlyFileListManagerView.js	(révision 10825)
@@ -84,6 +84,12 @@
                     else
                         return this.options.showFilters = visible;
                 },
+                updateNumberFile :function(){
+                    var lengthResource  = this.listView.collection.countFile;
+                    var lengthCollection  = this.listView.collection.length;
+                    var htmlCountFile="<span class=\"numFiles\">"+lengthCollection+"</span>"+translate('File(s)')+"<span class=\"numberFiles\"> "+lengthResource+"</span>";
+                    this.$("#numberFiles").html(htmlCountFile);
+                },
                 activeIconFiltre: function(type){
                     if(type ==="all"){
                         this.$("#all-files").addClass("active");
Index: src/js/JEditor/Commons/Files/Views/SelectFileView.js
===================================================================
--- src/js/JEditor/Commons/Files/Views/SelectFileView.js	(révision 10824)
+++ src/js/JEditor/Commons/Files/Views/SelectFileView.js	(révision 10825)
@@ -14,6 +14,7 @@
                 all: true,
                 initialize: function() {
                     this._super();
+
                     this._template = this.buildTemplate(selectFile, translate);
                     this.childViews.listView = new ReadOnlyFileListView({collection: this.collection, allowMultipleSelect: this.options.allowMultipleSelect});
                     this.childViews.listManagerView = new FileListManagerView({collection: this.collection, listView: this.childViews.listView});
@@ -42,6 +43,7 @@
                 onSync: function () {
                     this.setLoading(false);
                     this.loadMoreView();  
+                    this.childViews.listManagerView.updateNumberFile();
                 },
                 loadMoreView :function (){
                     if(this.collection.hasmore){
@@ -55,7 +57,6 @@
                 loadMore: function(){
                     if(this.collection.hasmore){
                         this.collection.fetch({remove: false});
-                        this.render();
                     }
                 },
                 onSelect: function(file, length, collection) {
Index: src/js/JEditor/FilePanel/Templates/fileManagerUIView.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileManagerUIView.html	(révision 10824)
+++ src/js/JEditor/FilePanel/Templates/fileManagerUIView.html	(révision 10825)
@@ -31,7 +31,7 @@
     </ul>
 </div>
 <!-- COUNTFILE -->
-<div class="countFile">
+<div class="countFile" style="display: none;">
     <span></span>
 </div>
 <!-- SEPARATION -->
Index: src/js/JEditor/FilePanel/Views/FileManagerUIView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileManagerUIView.js	(révision 10824)
+++ src/js/JEditor/FilePanel/Views/FileManagerUIView.js	(révision 10825)
@@ -45,7 +45,12 @@
                 },
                 updateCount: function() {
                     var fileLength=this.collection.length;
-                    this.$('.countFile span').html("<strong>"+fileLength+"</strong><span> "+translate("FileOn")+" "+this.collection.countFile+" </span>");
+                    if(fileLength >0){
+                        this.$('.countFile').show();
+                        this.$('.countFile span').html("<strong>"+fileLength+"</strong><span> "+translate("FileOn")+" "+this.collection.countFile+" </span>");
+                    }else{
+                        this.$('.countFile').hide();
+                    }
                 },
                 _onSortByClick: function(event) {
                     var $target = $(event.currentTarget);
Index: src/less/imports/filePanel.less
===================================================================
--- src/less/imports/filePanel.less	(révision 10824)
+++ src/less/imports/filePanel.less	(révision 10825)
@@ -922,20 +922,18 @@
 
 }
 /* CountFile*/
-.countFile {
+.top-bar .countFile {
+    padding: 6px 10px;
     float: left;
-    position: relative;
-    margin: 20px 30px;
-    padding: 0 10px;
+    margin: 20px;
     color: #888;
-    line-height: 30px;
-    background-color: #fff;
-    -webkit-border-radius: 4px;
-    -moz-border-radius: 4px;
-    border-radius: 4px;
-    font-weight: 500;
-    font-size: 16px;
+    background-color: #ffff;
 }
+@media screen and (max-height: 865px){
+    .top-bar .countFile {
+        margin: 10px 20px;
+    }
+}
 /* LOAD MORE */
 .flex-load-more {
     display: flex;
