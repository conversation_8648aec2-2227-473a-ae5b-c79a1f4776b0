Revision: r11054
Date: 2023-06-19 09:21:15 +0300 (lts 19 Jon 2023) 
Author: rrakotoarinelina 

## Commit message
Update options des row

## Files changed

## Full metadata
------------------------------------------------------------------------
r11054 | rrakotoarinelina | 2023-06-19 09:21:15 +0300 (lts 19 Jon 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/dev/#librairies/module/GestionContenuUtil/src/GestionContenuUtil/Services/OptionParserService.php
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/Templates/stylesOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/Views/StylesOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/nls/i18n.js

Update options des row
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Options/Templates/stylesOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/Templates/stylesOption.html	(révision 11053)
+++ src/js/JEditor/PagePanel/Contents/Options/Templates/stylesOption.html	(révision 11054)
@@ -75,19 +75,128 @@
     </article>
      <!-- Taille du contenu end -->
 
-     <!-- largueur de la section start -->
-     <% if(contentType ==="section"){%>
-     <article class="panel-option sectionWidth">
-        <header>
-            <h3 class="option-name"><%= __("sectionWidth")%></h3>
-            <p class="panel-content-legend"><%= __("sectionWidthDesc")%></p>
-        </header>
-        <div class="option-content">
-            <div class="slider-container"><span class="icon-less"></span><div class="slider" id="sectionWidth"></div><span class="icon-more"></span></div>
-        </div>
-    </article>
-    <%}%>
-     <!-- largueur de la section end -->
+          <!-- largueur de la section start -->
+          <% if(contentType ==="section"){%>
+            <article class="panel-option sectionWidth">
+               <header>
+                   <h3 class="option-name"><%= __("sectionWidth")%></h3>
+                   <p class="panel-content-legend"><%= __("sectionWidthDesc")%></p>
+               </header>
+               <div class="option-content">
+                   <!-- <div class="slider-container"><span class="icon-less"></span><div class="slider" id="sectionWidth"></div><span class="icon-more"></span></div> -->
+              
+                   <!-- remplacement par bouton -->
+                   <div class="controlPanel-selector">
+                       <div class="option radio">
+                               <div class="button-size-radio ">
+                                   <label for="tight" class="inline-block-label">
+                                       <input id="tight" type="radio" class="field-input " value="tight" name="parentSWidth" <%=defaultParentSWidth==="tight"? ' checked="checked" ':'' %>  />
+                                       
+                                       <div class="inline-block-label__top">
+                                           <span class="icon-tight"></span>
+                                       </div>
+                                       <div class="inline-block-label__bottom">
+                                           <span class="icon-radio-inactive"></span>
+                                           <span class="icon-radio-active"></span>
+                                       </div>
+                                   </label>
+                                   <label for="broad" class="inline-block-label">
+                                       <input id="broad" type="radio" class="field-input " value="" name="parentSWidth"  <%=defaultParentSWidth==="broad"? ' checked="checked" ':'' %>/>
+                                       <div class="inline-block-label__top">
+                                           <span class="icon-broad"></span>
+                                       </div>
+                                       <div class="inline-block-label__bottom">
+                                           <span class="icon-radio-inactive"></span>
+                                           <span class="icon-radio-active"></span>
+                                       </div>
+                                   </label>
+                                   <label for="wide" class="inline-block-label">
+                                       <input id="wide" type="radio" class="field-input " value="wide" name="parentSWidth" <%=defaultParentSWidth==="wide"? ' checked="checked" ':'' %> />
+                                       <div class="inline-block-label__top">
+                                           <span class="icon-wide"></span>
+                                       </div>
+                                       <div class="inline-block-label__bottom">
+                                           <span class="icon-radio-inactive"></span>
+                                           <span class="icon-radio-active"></span>
+                                       </div>
+                                   </label>
+                                   <label for="screen" class="inline-block-label">
+                                       <input id="screen" type="radio" class="field-input " value="screen" name="parentSWidth" <%=defaultParentSWidth==="screen"? ' checked="checked" ':'' %>  />
+                                       <div class="inline-block-label__top">
+                                           <span class="icon-screen"></span>
+                                       </div>
+                                       <div class="inline-block-label__bottom">
+                                           <span class="icon-radio-inactive"></span>
+                                           <span class="icon-radio-active"></span>
+                                       </div>
+                                   </label>
+                               </div>
+                       </div>
+                   </div>
+                   <!-- fin remplacement -->
+               </div>
+           </article>
+           <article class="panel-option sectionScreenWidth">
+               <header>
+                   <h3 class="option-name"><%= __("sectionScreenWidth")%></h3>
+                   <p class="panel-content-legend"><%= __("sectionScreenWidthDesc")%></p>
+               </header>
+               <div class="option-content">
+                   <!-- <div class="slider-container"><span class="icon-less"></span><div class="slider" id="sectionWidth"></div><span class="icon-more"></span></div> -->
+              
+                   <!-- remplacement par bouton -->
+                   <div class="controlPanel-selector">
+                       <div class="option radio">
+                               <div class="button-size-radio ">
+                                   <label for="full-tight" class="inline-block-label">
+                                       <input id="full-tight" type="radio" class="field-input" value="full-tight" name="childSWidth" <%=defaultChildSWidth==="full-tight"? ' checked="checked" ':'' %>  />
+                                       
+                                       <div class="inline-block-label__top">
+                                           <span class="icon-full-tight"></span>
+                                       </div>
+                                       <div class="inline-block-label__bottom">
+                                           <span class="icon-radio-inactive"></span>
+                                           <span class="icon-radio-active"></span>
+                                       </div>
+                                   </label>
+                                   <label for="full-broad" class="inline-block-label">
+                                       <input id="full-broad" type="radio" class="field-input" value="full-broad" name="childSWidth"  <%=defaultChildSWidth==="full-broad"? ' checked="checked" ':'' %>/>
+                                       <div class="inline-block-label__top">
+                                           <span class="icon-full-broad"></span>
+                                       </div>
+                                       <div class="inline-block-label__bottom">
+                                           <span class="icon-radio-inactive"></span>
+                                           <span class="icon-radio-active"></span>
+                                       </div>
+                                   </label>
+                                   <label for="full-wide" class="inline-block-label">
+                                       <input id="full-wide" type="radio" class="field-input" value="full-wide" name="childSWidth" <%=defaultChildSWidth==="full-wide"? ' checked="checked" ':'' %> />
+                                       <div class="inline-block-label__top">
+                                           <span class="icon-full-wide"></span>
+                                       </div>
+                                       <div class="inline-block-label__bottom">
+                                           <span class="icon-radio-inactive"></span>
+                                           <span class="icon-radio-active"></span>
+                                       </div>
+                                   </label>
+                                   <label for="full-screen" class="inline-block-label">
+                                       <input id="full-screen" type="radio" class="field-input " value="screen" name="childSWidth" <%=defaultChildSWidth=="screen"? ' checked="checked" ':'' %>  />
+                                       <div class="inline-block-label__top">
+                                           <span class="icon-full-screen"></span>
+                                       </div>
+                                       <div class="inline-block-label__bottom">
+                                           <span class="icon-radio-inactive"></span>
+                                           <span class="icon-radio-active"></span>
+                                       </div>
+                                   </label>
 
+                               </div>
+                       </div>
+                   </div>
+                   <!-- fin remplacement -->
+               </div>
+           </article>
+           <%}%>
+            <!-- largueur de la section end -->
 </div>
 
Index: src/js/JEditor/PagePanel/Contents/Options/Views/StylesOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/Views/StylesOptionView.js	(révision 11053)
+++ src/js/JEditor/PagePanel/Contents/Options/Views/StylesOptionView.js	(révision 11054)
@@ -35,6 +35,8 @@
                                 events: {
                                     'click  div .effect-radio'   : '_onChangeStyleColor',
                                     'slide .slider': '_onSliderChange',
+                                    'click input[name=parentSWidth]': '_onChangeSectionWidth',
+                                    'click input[name=childSWidth]': '_onChangeSectionScreenWidth',
                                     // 'slidechange .slider': '_onSliderChange',
                                     // 'radiochange .panel-option-container.borders': '_onRadioChange',
                                     // 'changeminicolors input.border-color': '_onBorderColorChange'
@@ -55,8 +57,28 @@
                                     var ContentType =this.model.content.contentType;
                                     var defaultColorOption=(this.model.getColorOption())?this.model.getColorOption():'';
                                     if(ContentType ==="formBlock" && defaultColorOption==="")
-                                        defaultColorOption="surface0";
-                                    this.$el.html(this._template({contentType:ContentType,classColor:this.model.getConst().classColor,defaultColorOption:defaultColorOption}));
+                                    defaultColorOption="surface0";
+                                    
+                                    //utilisation autre attribut pour que l'insertion value manuelle dans l'attribut sectionWidth soit possible
+                                    var defaultParentSWidth =   "";
+                                    var defaultChildSWidth =  "";
+                                    var sectionClasses = ['full-broad', 'full-wide', 'full-tight'];
+                                    if ((typeof this.model.sectionWidth !== "undefined" ) ) {
+                                        if (sectionClasses.indexOf(this.model.sectionWidth ) !== -1) {
+                                            // La valeur existe dans le tableau
+                                            defaultParentSWidth = "screen";
+                                            defaultChildSWidth = this.model.sectionWidth;
+                                        }else{
+                                            defaultParentSWidth = (typeof this.model.sectionWidth !== "undefined" && this.model.sectionWidth !== "") ? this.model.sectionWidth : "broad";
+                                            defaultChildSWidth = "screen";
+                                        }
+                                    }else{
+                                        defaultParentSWidth = "broad";
+                                        defaultChildSWidth = "screen";
+                                    }
+
+                                    this.$el.html(this._template({contentType:ContentType,classColor:this.model.getConst().classColor,defaultColorOption:defaultColorOption,defaultParentSWidth:defaultParentSWidth,defaultChildSWidth:defaultChildSWidth}));
+                                    this._toggleScreenWidthContainer(defaultParentSWidth);
                                     //Size content 1 -> 7
                                     var defaultContentSize =(this.model.contentSize)? (this.model.getContentSizeKeyByValue(this.model.contentSize)) :this.model.getDefaultConst().SizeContent;
                                     this._slideOption(".contentSize",1,7,defaultContentSize);
@@ -63,11 +85,6 @@
                                     //Shade Worn 0 -> 3
                                     var defaultShadeWorn = (this.model.shadeWorn)? (this.model.getShadeWornKeyByValue(this.model.shadeWorn)) : this.model.getDefaultConst().ShadowWorn; 
                                     this._slideOption(".shadeWorn" ,0,3,defaultShadeWorn);
-                                    //Section Width Show only section 0 -> 3
-                                    var defaultSectionWidth =   (this.model.sectionWidth) ? (this.model.getSectionWidthKeyByValue(this.model.sectionWidth)) : this.model.getDefaultConst().SectionWidth;
-                                    if(ContentType ==="section")
-                                        this._slideOption(".sectionWidth",0,3,defaultSectionWidth );
-                                    
 
                                     this.scrollables();
                                     return this;
@@ -139,8 +156,39 @@
                                         this.model.setShadeWorn(parseInt(ui.value));
                                     else if(id === "sectionWidth")
                                         this.model.setSectionWidth(parseInt(ui.value));
+                                    
+                                },
+                                /**
+                                 * changement des valeurs section depuis boutons
+                                 * @private
+                                 */
+                                _onChangeSectionWidth:function(event){
+                                    //quelque part l'evenement est géré, 
+                                    //on n'a pas besoin de mettre a jour modele ici
+                                    var sectionWidthDraft = $(event.currentTarget).val();
+                                    this._toggleScreenWidthContainer(sectionWidthDraft);
+                                    this.model.sectionWidth = sectionWidthDraft;
 
-                                    
+                                },
+                                /**
+                                 * changement des valeurs enfant de screen
+                                 * @private
+                                 */
+                                _onChangeSectionScreenWidth:function(event){
+                                    var sectionScreenWidth = $(event.currentTarget).val();
+                                    this.model.sectionWidth = sectionScreenWidth;
+                                },
+                                /**
+                                 * cacher ou montrer child du bouton screen
+                                 *  @private
+                                 */
+
+                                _toggleScreenWidthContainer:function(value){
+                                    if ( value ===  "screen" ) {
+                                        this.$(".sectionScreenWidth").css("display", "block");
+                                    }else{
+                                        this.$(".sectionScreenWidth").css("display", "none");
+                                    }
                                 }
                             });
                     StylesOptionView.translate = translate;
Index: src/js/JEditor/PagePanel/Contents/Options/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/nls/fr-ca/i18n.js	(révision 11053)
+++ src/js/JEditor/PagePanel/Contents/Options/nls/fr-ca/i18n.js	(révision 11054)
@@ -81,5 +81,7 @@
     "contentSizeDesc" : "Glisser pour ajuster la taille du contenu.<br>(Cette option n'a pas d'effet dans la zone d'administration)",
     "sectionWidth"  :"Largeur de la section",
     "sectionWidthDesc"  : "Glisser pour ajuster la largeur de la section (cette option n'a pas d'effet dans la zone d'administration).",
+    "sectionScreenWidth"  :"Content alignment",
+    "sectionScreenWidthDesc"  : "adjust content alignment on the site grid (this option has no effect in the administration area)",
     "aucune"  : "Aucune"
 });
Index: src/js/JEditor/PagePanel/Contents/Options/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/nls/fr-fr/i18n.js	(révision 11053)
+++ src/js/JEditor/PagePanel/Contents/Options/nls/fr-fr/i18n.js	(révision 11054)
@@ -81,5 +81,7 @@
     "contentSizeDesc" : "Glisser pour ajuster la taille du contenu.<br>(Cette option n'a pas d'effet dans la zone d'administration)",
     "sectionWidth"  :"Largeur de la section",
     "sectionWidthDesc"  : "Glisser pour ajuster la largeur de la section (cette option n'a pas d'effet dans la zone d'administration).",
+    "sectionScreenWidth"  :"Alignement du contenu",
+    "sectionScreenWidthDesc"  : "Ajuster l'alignement du contenu sur la grille du site (cette option n'a pas d'effet dans la zone d'administration)",
     "aucune"  : "Aucune"
 });
Index: src/js/JEditor/PagePanel/Contents/Options/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/nls/i18n.js	(révision 11053)
+++ src/js/JEditor/PagePanel/Contents/Options/nls/i18n.js	(révision 11054)
@@ -83,6 +83,8 @@
     "contentSizeDesc" : "Drag to adjust the size of the content.<br>(This option has no effect in the adminstration area)",
     "sectionWidth"  :"Section width",
     "sectionWidthDesc"  : "Slide to adjust the width of the section.<br>(This option has no effect in the adminstration area)",
+    "sectionScreenWidth"  :"Content alignment",
+    "sectionScreenWidthDesc"  : "adjust content alignment on the site grid (this option has no effect in the administration area)",
     "aucune"  : "None"
   },
   "fr-fr": true,
