Revision: r10305
Date: 2023-02-02 10:47:57 +0300 (lkm 02 Feb 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
correction image logo qui s'affiche dans le panel fichier

## Files changed

## Full metadata
------------------------------------------------------------------------
r10305 | srazana<PERSON>lisoa | 2023-02-02 10:47:57 +0300 (lkm 02 Feb 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/dev/#librairies/vendor/CoreRessourceManager/src/CoreRessourceManager/Controller/ResourceLogoRestController.php
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/MarqueClientView.js

correction image logo qui s'affiche dans le panel fichier
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Views/MarqueClientView.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 10304)
+++ src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 10305)
@@ -151,7 +151,7 @@
 					acceptedTypes : [ 'image' ],
 					acceptedExtensions : ['jpeg','jpg','gif','png', 'svg'],
                     refusedExtensions:['bmp'],
-                    uploadUrl : __IDEO_UPLOADLOGO_URL__? __IDEO_UPLOADLOGO_URL__:'/admin/resource-setting',
+                    uploadUrl : __IDEO_UPLOADLOGO_URL__? __IDEO_UPLOADLOGO_URL__:'/admin/resource-logo',
 				},
 
 			});
@@ -165,7 +165,7 @@
 					acceptedTypes : [ 'image' ],
 					acceptedExtensions : ['jpeg','jpg','gif','png', 'svg'],
                     refusedExtensions:['bmp'],
-                    uploadUrl : __IDEO_UPLOADLOGO_URL__? __IDEO_UPLOADLOGO_URL__:'/admin/resource-setting',
+                    uploadUrl : __IDEO_UPLOADLOGO_URL__? __IDEO_UPLOADLOGO_URL__:'/admin/resource-logo',
 				}
 			});
 
@@ -178,7 +178,7 @@
 					acceptedTypes : [ 'image' ],
 					acceptedExtensions : ['jpeg','jpg','png'],
                     refusedExtensions:['bmp'],
-                    uploadUrl : __IDEO_UPLOADLOGO_URL__? __IDEO_UPLOADLOGO_URL__:'/admin/resource-setting',
+                    uploadUrl : __IDEO_UPLOADLOGO_URL__? __IDEO_UPLOADLOGO_URL__:'/admin/resource-logo',
 				}
 			});
 
