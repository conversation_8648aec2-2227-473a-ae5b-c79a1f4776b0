Revision: r10317
Date: 2023-02-02 19:54:53 +0300 (lkm 02 Feb 2023) 
Author: anthony 

## Commit message
chargement de ideo3-back.css dans les context menu de ckeditor

## Files changed

## Full metadata
------------------------------------------------------------------------
r10317 | anthony | 2023-02-02 19:54:53 +0300 (lkm 02 Feb 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js

chargement de ideo3-back.css dans les context menu de ckeditor
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 10316)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 10317)
@@ -40,6 +40,9 @@
                             extraPlugins: 'abbr,pre,ideolink,ideostyler,sourcedialog',
                             skin: 'ideo',
                             fontSize_sizes: 'tiny/0.75em;small/0.875em;large/1.25em;xlarge/1.5em;xxlarge/2em;xxxlarge/2.5em',
+                            contentsCss: [
+                                __IDEO_CSS_PATH__ + 'ideo3-back.css'
+                            ],
                             stylesSet: [
                                 { name: 'Highlight text 1', element: 'span', attributes: { 'class': 'text--hightlight-surface-mid' } }
                             ],
