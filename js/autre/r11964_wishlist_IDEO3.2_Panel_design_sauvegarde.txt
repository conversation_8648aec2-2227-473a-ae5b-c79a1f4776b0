Revision: r11964
Date: 2024-02-21 08:57:51 +0300 (lrb 21 Feb 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2 : Panel design sauvegarde

## Files changed

## Full metadata
------------------------------------------------------------------------
r11964 | sraz<PERSON><PERSON><PERSON>oa | 2024-02-21 08:57:51 +0300 (lrb 21 Feb 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/DesignPanel/Views/ConstantResourceEditorView.js

wishlist IDEO3.2 : Panel design sauvegarde
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/DesignPanel/Views/ConstantResourceEditorView.js
===================================================================
--- src/js/JEditor/DesignPanel/Views/ConstantResourceEditorView.js	(révision 11963)
+++ src/js/JEditor/DesignPanel/Views/ConstantResourceEditorView.js	(révision 11964)
@@ -17,10 +17,12 @@
         ressources: {},
         editedResource: '',
         editedResourceContent: '',
+        currentRef : '',
         initialize: function() {
             this._super();
             this._template = this.buildTemplate(constantResourceEditor, translate);
             this.setEdited = _.bind(this.setEdited, this);
+            $(document).on("keydown.saveHandler",_.bind(this.ctrlsHandler,this));
         },
 
         render: function() {
@@ -66,6 +68,7 @@
             this.stopListening(resource, Events.BackboneEvents.SYNC, this.setEditor);
             this.stopListening(resource, Events.BackboneEvents.ERROR, this.setEditor);
             if (resource.resource_ref) {
+                this.currentRef = resource.resource_ref;
                 this.stopListening(this.dom[this.cid].aceEditor, 'change', this.setEdited);
                 this.editedResource = resource.resource_name;
                 this.editedResourceContent = resource.resource_expression;
@@ -93,8 +96,18 @@
             }
             return false;
         },
+        ctrlsHandler:function(event){
+            var ret;
+            if(event.keyCode===83&& event.ctrlKey){
+                if (this.currentRef) this.onSaveResource()
+                    ret= false;
+            }
+            return ret;
+        },
         onSaveResource: function(event) {
-            var resourceRef = $(event.currentTarget).attr('data-resourceref');
+            if (event) {
+                var resourceRef = $(event.currentTarget).attr('data-resourceref');
+            }else var resourceRef = this.currentRef;
             var resource = this.ressources[this.editedResource];
 
             console.log(resource.resource_ref);
