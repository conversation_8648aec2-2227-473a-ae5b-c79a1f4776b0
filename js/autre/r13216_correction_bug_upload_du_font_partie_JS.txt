Revision: r13216
Date: 2024-10-16 12:30:28 +0300 (lrb 16 Okt 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
correction bug upload du font partie JS

## Files changed

## Full metadata
------------------------------------------------------------------------
r13216 | sraz<PERSON><PERSON><PERSON>oa | 2024-10-16 12:30:28 +0300 (lrb 16 Okt 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js

correction bug upload du font partie JS
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js
===================================================================
--- src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 13215)
+++ src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 13216)
@@ -326,7 +326,8 @@
                 var filesize = Math.ceil(files[i].size  / (1024 * 1024));
                 if (this.options.maxFiles !== -1 && (i >= this.options.maxFiles))
                     break; //Yeah, this is a breakup reason
-                if (!(this.acceptedTypes.test(files[i].type.toLowerCase()) && (this.acceptedExt && this.acceptedExt.test(files[i].name.toLowerCase()))) ||
+                    //certain type est vide 
+                if (!((this.acceptedTypes.test(files[i].type.toLowerCase()) || files[i].type.toLowerCase() === '' ) && (this.acceptedExt && this.acceptedExt.test(files[i].name.toLowerCase()))) ||
                         (this.refusedExt.test(files[i].name.toLowerCase()) || this.refusedTypes.test(files[i].type.toLowerCase()))) {
                     message = this.options.lang.importFailBadType ? this.options.lang.importFailBadType : this.options.lang.error;
                     this.errors.push(message.replace('%name%', files[i].name));
