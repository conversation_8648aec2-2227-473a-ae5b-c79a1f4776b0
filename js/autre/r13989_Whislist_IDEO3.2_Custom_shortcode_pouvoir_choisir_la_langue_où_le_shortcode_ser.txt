Revision: r13989
Date: 2025-03-25 09:26:14 +0300 (tlt 25 Mar 2025) 
Author: rrak<PERSON>arinelina 

## Commit message
Whislist IDEO3.2 : Custom shortcode : pouvoir choisir la langue où le shortcode sera appliqué - partie JS

## Files changed

## Full metadata
------------------------------------------------------------------------
r13989 | rrakotoarinelina | 2025-03-25 09:26:14 +0300 (tlt 25 Mar 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/ParamsPanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/CustomShortcode.js

Whislist IDEO3.2 : Custom shortcode : pouvoir choisir la langue où le shortcode sera appliqué - partie JS
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/ParamsPanel.js
===================================================================
--- src/js/JEditor/ParamsPanel/ParamsPanel.js	(révision 13988)
+++ src/js/JEditor/ParamsPanel/ParamsPanel.js	(révision 13989)
@@ -104,7 +104,7 @@
                                     if(this.app.user.can('createshortcode')) {
                                         this.menuEntries. customShortcode = 
                                         {
-                                            object: new CustomShortcode({model:this.params}),
+                                            object: new CustomShortcode({model:this.params, languages : this.languages}),
                                             icon:"icon-link",
                                             title:translate("CustomShortcode")
                                         }
Index: src/js/JEditor/ParamsPanel/Views/CustomShortcode.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/CustomShortcode.js	(révision 13988)
+++ src/js/JEditor/ParamsPanel/Views/CustomShortcode.js	(révision 13989)
@@ -136,11 +136,6 @@
                     });
                },
                 render: function () {
-                    console.log("invalid string length");
-                    console.log(this.languages); 
-
-                    console.log(" invalid string length this.currentList"); 
-                    console.log(this.currentList); 
                    
                     this.undelegateEvents();
                     this.$el.html(this._template({ listeShortcode: (this.currentList != null? this.currentList :[]), languages: this.languages}));
