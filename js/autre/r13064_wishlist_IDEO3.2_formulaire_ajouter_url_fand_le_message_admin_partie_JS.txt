Revision: r13064
Date: 2024-09-19 10:10:25 +0300 (lkm 19 Sep 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: formulaire, ajouter url fand le message admin (partie JS) 

## Files changed

## Full metadata
------------------------------------------------------------------------
r13064 | srazanandralisoa | 2024-09-19 10:10:25 +0300 (lkm 19 Sep 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/MessagePanel/Templates/message.html
   M /branches/ideo3_v2/integration/src/js/JEditor/MessagePanel/Views/Message.js

wishlist IDEO3.2: formulaire, ajouter url fand le message admin (partie JS) 
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/MessagePanel/Templates/message.html
===================================================================
--- src/js/JEditor/MessagePanel/Templates/message.html	(révision 13063)
+++ src/js/JEditor/MessagePanel/Templates/message.html	(révision 13064)
@@ -34,10 +34,14 @@
     <div class="the-message">
         <p>
             <% var datas = message.get("datas");
-            for(var i=0; i< datas.length; i++){%>
+            for(var i=0; i< datas.length; i++){ 
+                if (datas[i].label != 'pageUrl'){%>
               <div><b><%=datas[i].label%></b> : <%=datas[i].value%></div>
-            <%}%>
+            <%}}%>
         </p>
+        <br>
+        <br>
+        <% if (pageUrl!= ''){%><div><b><%=__('sendBy')%></b> : <%=pageUrl%></div> <%}%>
     </div>
 
     <!-- message attachment -->
Index: src/js/JEditor/MessagePanel/Views/Message.js
===================================================================
--- src/js/JEditor/MessagePanel/Views/Message.js	(révision 13063)
+++ src/js/JEditor/MessagePanel/Views/Message.js	(révision 13064)
@@ -40,8 +40,15 @@
     },
     render: function() {
       this.undelegateEvents();
+      function findUrl(item) {
+        return item.label === "pageUrl";
+      }
+      var datasArray = Object.values(this.model.attributes.datas);
+      var pageUrl = datasArray.find(findUrl);
+
       this.$el.html(this.template({
         message: this.model,
+        pageUrl: pageUrl ? pageUrl.value : "",
         moment: moment,
         openned: this.openned
       }));
