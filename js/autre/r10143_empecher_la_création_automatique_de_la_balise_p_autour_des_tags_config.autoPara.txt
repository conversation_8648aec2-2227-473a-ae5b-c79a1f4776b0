Revision: r10143
Date: 2023-01-16 13:33:29 +0300 (lts 16 Jan 2023) 
Author: anthony 

## Commit message
empecher la création automatique de la balise p autour des tags (config.autoParagraph = false)

## Files changed

## Full metadata
------------------------------------------------------------------------
r10143 | anthony | 2023-01-16 13:33:29 +0300 (lts 16 Jan 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js

empecher la création automatique de la balise p autour des tags (config.autoParagraph = false)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 10142)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 10143)
@@ -58,6 +58,7 @@
                             allowedContent: true,
                             format_tags: 'p;h2;h3;h4;h5;h6',
                             entities:false,
+                            autoParagraph:false,
                             title:false
                         },
                         changeTimeout: null,
