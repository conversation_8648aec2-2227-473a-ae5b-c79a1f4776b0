Revision: r10383
Date: 2023-02-10 16:43:01 +0300 (zom 10 Feb 2023) 
Author: anthony 

## Commit message
back merge evolution file list (inte)

## Files changed

## Full metadata
------------------------------------------------------------------------
r10383 | anthony | 2023-02-10 16:43:01 +0300 (zom 10 Feb 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Templates/selectFileListManager.html
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Views/ReadOnlyFileListManagerView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileDetail.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileList.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileListView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileManagerUIView.js
   M /branches/ideo3_v2/integration/src/less/imports/filePanel.less

back merge evolution file list (inte)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Files/Templates/selectFileListManager.html
===================================================================
--- src/js/JEditor/Commons/Files/Templates/selectFileListManager.html	(révision 10382)
+++ src/js/JEditor/Commons/Files/Templates/selectFileListManager.html	(révision 10383)
@@ -26,7 +26,7 @@
         <div class="trier-par">
             <%= __('sortBy')%> :
             <a class="sort-dropdown-toggle" href="#">
-                <span class="text"><%= __('fileSortcreatedAtDesc')%></span>
+                <span class="text"><%= __('fileSort')%></span>
                 <span class="caret"></span>
             </a>
             <ul class="dropdown-menu">
Index: src/js/JEditor/Commons/Files/Views/ReadOnlyFileListManagerView.js
===================================================================
--- src/js/JEditor/Commons/Files/Views/ReadOnlyFileListManagerView.js	(révision 10382)
+++ src/js/JEditor/Commons/Files/Views/ReadOnlyFileListManagerView.js	(révision 10383)
@@ -11,8 +11,8 @@
                 },
                 defaultOptions: {
                     showFilters: true,
-                    sortBy: 'createdAt',
-                    sortOrder: 'desc'
+                    sortBy: '',
+                    sortOrder: ''
                 },
                 initialize: function() {
                     this._super();
@@ -27,7 +27,7 @@
                     this.options.sortBy = data.sortby;
                     this.options.sortOrder = data.sortorder;
                     if (this.listView.sortBy)
-                        this.listView.sortBy(data.sortby, data.sortorder === 'desc');
+                        this.listView.sortBy(data.sortby, data.sortorder === 'asc');
                     this.dom[this.cid].sortDropdownItems.removeClass('active');
                     this.dom[this.cid].sortDropdownParent.removeClass('open');
                     $target.addClass('active');
Index: src/js/JEditor/FilePanel/Templates/fileDetail.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileDetail.html	(révision 10382)
+++ src/js/JEditor/FilePanel/Templates/fileDetail.html	(révision 10383)
@@ -56,8 +56,17 @@
                             </p>
                         </div>
                     </span>
-
-
+                    <% if(isRemovable===false || isInBlock ===true) {%>
+                        <span class="actions informations">
+                            <span class="icon-information"></span>
+                            <div class="infobulles">
+                                <p class="title"><%= __("locked")%> :</p>
+                                <p>
+                                    <%= __("lockimage")%>
+                                </p>
+                            </div>
+                        </span>
+                    <% } %>
                     <%if(isFavicon){%>
                         <span class="actions informations">
                             <span class="icon-information"></span>
Index: src/js/JEditor/FilePanel/Templates/fileList.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileList.html	(révision 10382)
+++ src/js/JEditor/FilePanel/Templates/fileList.html	(révision 10383)
@@ -1,29 +1,6 @@
-
- <div class="FileTop">
-    <div class="my-files fileList">
-        <div class="content scroll-container">
-            <div class="group-content <%=content.length===0?'empty':''%>">
-                <div class=" add-file" data-action="showuploader">
-    
-                    <span class="icon">
-                        <span class="icon-hexagon"></span>
-                        <span class="icon-add"></span>
-                    </span>
-                    <%= __("addFile")%>
-    
-                </div>
-                <!-- file add -->
-            </div>
-           
-        </div>
-    </div>
-    
-    <div class="warning-msg"><span class="icon-warning" style="color: #d42525;"></span> <span style="color: #d42525;"><%= __("imageWarning")%></span> <%= __("imageWarningMsg")%> <a href="https://www.tineye.com/" target="_blank">TinEye</a>.</div>
-</div>
 <div class="my-files fileList">
-   
-    
     <div class="content scroll-container">
+        <div class="group-content <%=content.length===0?'empty':''%>">
 
             <% for(var i=0; i< content.length; i++){
             var file = content[i];
@@ -50,6 +27,20 @@
 
             </div>
             <% } %>
-            <% } %>   
+            <% } %>
+            <div class="menu-wrapper add-file" data-action="showuploader">
+
+                <span class="icon">
+                    <span class="icon-hexagon"></span>
+                    <span class="icon-add"></span>
+                </span>
+                <%= __("addFile")%>
+
+            </div>
+            <!-- file add -->
+
+        </div>
     </div>
 </div>
+
+<div class="warning-msg"><span class="icon-warning" style="color: #d42525;"></span> <span style="color: #d42525;"><%= __("imageWarning")%></span> <%= __("imageWarningMsg")%> <a href="https://www.tineye.com/" target="_blank">TinEye</a>.</div>
\ No newline at end of file
Index: src/js/JEditor/FilePanel/Views/FileListView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileListView.js	(révision 10382)
+++ src/js/JEditor/FilePanel/Views/FileListView.js	(révision 10383)
@@ -81,7 +81,7 @@
                 this.render();
             }
             else {
-                this.sortedBy = (property==="createdAt")?"id":property;
+                this.sortedBy = property;
                 this.sortAsc = asc;
                 this.currentList = this.sortList(this.currentList);
             }
Index: src/js/JEditor/FilePanel/Views/FileManagerUIView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileManagerUIView.js	(révision 10382)
+++ src/js/JEditor/FilePanel/Views/FileManagerUIView.js	(révision 10383)
@@ -25,8 +25,8 @@
                 events: {
                     'click [data-action="addSelectionToNewCollection"]': 'addSelectionToNewCollection',
                 },
-                sortBy: 'createdAt',
-                sortOrder: 'desc',
+                sortBy: '',
+                sortOrder: '',
                 initialize: function() {
                     this._super();
                     this._template = this.buildTemplate(fileManagerUIView, translate);
Index: src/less/imports/filePanel.less
===================================================================
--- src/less/imports/filePanel.less	(révision 10382)
+++ src/less/imports/filePanel.less	(révision 10383)
@@ -772,33 +772,7 @@
 #files .file:hover .select ,#files .file.selected .select {
     opacity: 1;
 }
-.menu-wrapper.add-file {
-    width: 91px;
-    height: 91px;
-    padding: 14px;
-    border: 1px solid #ddd;
-    color: #989898;
-    line-height: 14px;
-    font-size: 12px;
-    text-align: center;
-    cursor: pointer;
-    -webkit-transition: all .3s ease-in-out;
-    -moz-transition: all .3s ease-in-out;
-    -o-transition: all .3s ease-in-out;
-    -ms-transition: all .3s ease-in-out;
-    transition: all .3s ease-in-out;
-  }
-  .menu-wrapper.add-file> .icon > .icon-add {
-    position: absolute;
-    font-size: 14px;
-    top: 6px;
-    left: 0;
-    color: #FFFFFF;
-    right: 0;
-  }
-  .menu-wrapper.add-file> .icon > .icon-hexagon {
-    font-size: 26px;
-  }
+
 #files .my-files .file {
     width: 120px; height: 120px;
     position: relative;
@@ -929,17 +903,14 @@
 }
 
 /* ADD-FILE */
-.FileTop {
-    margin: auto 211px;
-  }
 .my-files .add-file {
-    width: auto; height: 120px;
+    width: 91px; height: 91px;
     padding: 14px;
     border: 1px solid #dddddd;
 
     color: #989898;
-    line-height: 60px;
-    font-size: 16px;
+    line-height: 14px;
+    font-size: 12px;
     text-align: center;
 
     cursor: pointer;
@@ -957,13 +928,13 @@
     }
 
     & .icon-hexagon {
-        font-size: 34px;
+        font-size: 26px;
     }
 
     & .icon-add {
         position: absolute;
         font-size: 14px;
-        top: 15px; left: 0;right: 0;
+        top: 6px; left: 39px;
         color: #FFFFFF;
     }
 
