Revision: r10737
Date: 2023-03-23 18:48:04 +0300 (lkm 23 Mar 2023) 
Author: mpartaux 

## Commit message
add buttons style4 & style5 for carousel & grid + update CSS

## Files changed

## Full metadata
------------------------------------------------------------------------
r10737 | mpartaux | 2023-03-23 18:48:04 +0300 (lkm 23 Mar 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselStyle.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridStyleOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/gallery_style.less

add buttons style4 & style5 for carousel & grid + update CSS
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselStyle.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselStyle.html	(révision 10736)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselStyle.html	(révision 10737)
@@ -75,6 +75,34 @@
                         </div>
                     </div>
                 </label>
+
+                <%  var _id=_.uniqueId('carrouselStyleAffichage');%>
+                <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="4" id="<%=_id %>" <%=(StyleAffichage==4)?'checked':''%>>
+                   <label for="<%=_id %>">
+                    <div class="wrapper">
+                        <div class="vertical-wrap">
+                            <span class="icon-wrapper">
+                                <span class="icon-collection-style4"></span>
+                            </span>
+                            <span class="name"><%= __("Style")%> 4</span>
+                            <span class="desc"><%= __("DescStyle4")%></span>
+                        </div>
+                    </div>
+                </label>
+
+                <%  var _id=_.uniqueId('carrouselStyleAffichage');%>
+                <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="5" id="<%=_id %>" <%=(StyleAffichage==5)?'checked':''%>>
+                   <label for="<%=_id %>">
+                    <div class="wrapper">
+                        <div class="vertical-wrap">
+                            <span class="icon-wrapper">
+                                <span class="icon-collection-style5"></span>
+                            </span>
+                            <span class="name"><%= __("Style")%> 5</span>
+                            <span class="desc"><%= __("DescStyle5")%></span>
+                        </div>
+                    </div>
+                </label>
                 
         </div>
     </article>
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridStyleOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridStyleOption.html	(révision 10736)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridStyleOption.html	(révision 10737)
@@ -93,6 +93,34 @@
                         </div>
                     </div>
                 </label>
+
+                <%  var _id=_.uniqueId('gridStyleAffichage');%>
+                <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="4" id="<%=_id %>" <%=(grilleStyleAff==4)?'checked':''%>>
+                   <label for="<%=_id %>">
+                    <div class="wrapper">
+                        <div class="vertical-wrap">
+                            <span class="icon-wrapper">
+                                <span class="icon-collection-style4"></span>
+                            </span>
+                            <span class="name"><%= __("Style")%> 4</span>
+                            <span class="desc"><%= __("DescStyle4")%></span>
+                        </div>
+                    </div>
+                </label>
+
+                <%  var _id=_.uniqueId('gridStyleAffichage');%>
+                <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="5" id="<%=_id %>" <%=(grilleStyleAff==5)?'checked':''%>>
+                   <label for="<%=_id %>">
+                    <div class="wrapper">
+                        <div class="vertical-wrap">
+                            <span class="icon-wrapper">
+                                <span class="icon-collection-style5"></span>
+                            </span>
+                            <span class="name"><%= __("Style")%> 5</span>
+                            <span class="desc"><%= __("DescStyle5")%></span>
+                        </div>
+                    </div>
+                </label>
                 
         </div>
     </article>
Index: src/less/imports/gallery_style.less
===================================================================
--- src/less/imports/gallery_style.less	(révision 10736)
+++ src/less/imports/gallery_style.less	(révision 10737)
@@ -9,9 +9,11 @@
             width:49%;
             display:inline-block;
             background-color:#2F2F2F;
+            margin: 0;
             &>.wrapper{
                 display:table;
                 margin:auto;
+                width: 87%;
                 &>.vertical-wrap{
                     display:table-cell;
                     height:130px;
@@ -24,7 +26,7 @@
                         font-weight:400;
                     }
                     &>.desc{
-                        color:#808080;
+                        color:#bcbcbc;
                         display:block;
                     }
                 }
@@ -37,7 +39,8 @@
             &+label{
                 background-color:@pageColor;
                 &>.wrapper>.vertical-wrap>.desc{
-                    color:#206973;
+                    color:#0b2428;
+                    font-weight: 500;
                 }
             }
         }
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js	(révision 10736)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js	(révision 10737)
@@ -50,5 +50,7 @@
     "emptyCollection":"Votre collection d’images est vide.",
     "DescStyle1"           :  "Textes sous l'image", 
     "DescStyle2"           :  "Texte encadré sur l'image",
-    "DescStyle3"           :  "Texte sur l'image"             
+    "DescStyle3"           :  "Texte sur l'image",
+    "DescStyle4"           :  "Textes sous l'image avec bordures",
+    "DescStyle5"           :  "Textes sous l'image avec images arrondies"             
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js	(révision 10736)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js	(révision 10737)
@@ -54,7 +54,9 @@
        "emptyCollection"      :"Your image collection is empty",
        "DescStyle1"           :  "Text under the image", 
        "DescStyle2"           :  "Text framed on the image",
-       "DescStyle3"           :  "Text on the image"             
+       "DescStyle3"           :  "Text on the image",
+       "DescStyle4"           :  "Text under the image with borders",
+       "DescStyle5"           :  "Text under the image with rounded pictures"      
     },
     "fr-fr":true,
     "fr-ca": true
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-ca/i18n.js	(révision 10736)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-ca/i18n.js	(révision 10737)
@@ -48,5 +48,7 @@
         "editMyGrid"            :   "Modifier ma grille ",
         "DescStyle1"           :  "Textes sous l'image", 
         "DescStyle2"           :  "Texte encadré sur l'image",
-        "DescStyle3"           :  "Texte sur l'image"      
+        "DescStyle3"           :  "Texte sur l'image",
+        "DescStyle4"           :  "Textes sous l'image avec bordures",
+        "DescStyle5"           :  "Textes sous l'image avec images arrondies"      
     });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-fr/i18n.js	(révision 10736)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-fr/i18n.js	(révision 10737)
@@ -48,5 +48,7 @@
         "editMyGrid"            :   "Modifier ma grille ",
         "DescStyle1"           :  "Textes sous l'image", 
         "DescStyle2"           :  "Texte encadré sur l'image",
-        "DescStyle3"           :  "Texte sur l'image"      
+        "DescStyle3"           :  "Texte sur l'image",
+        "DescStyle4"           :  "Textes sous l'image avec bordures",
+        "DescStyle5"           :  "Textes sous l'image avec images arrondies"      
     });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/i18n.js	(révision 10736)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/i18n.js	(révision 10737)
@@ -57,5 +57,7 @@
         "editMyGrid"            :   "Edit my Grid",
         "DescStyle1"           :  "Text under the image", 
         "DescStyle2"           :  "Text framed on the image",
-        "DescStyle3"           :  "Text on the image"    
+        "DescStyle3"           :  "Text on the image",
+        "DescStyle4"           :  "Text under the image with borders",
+        "DescStyle5"           :  "Text under the image with rounded pictures"    
 }, "fr-fr":true, "fr-ca":true })
\ No newline at end of file
