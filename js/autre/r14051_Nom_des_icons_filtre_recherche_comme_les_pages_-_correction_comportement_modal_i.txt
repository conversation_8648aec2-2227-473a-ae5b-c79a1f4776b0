Revision: r14051
Date: 2025-04-07 14:01:00 +0300 (lts 07 Apr 2025) 
Author: rrakotoarinelina 

## Commit message
Nom des icons + filtre recherche (comme les pages) - correction comportement modal icon bizarre dans safari

## Files changed

## Full metadata
------------------------------------------------------------------------
r14051 | rrakotoarinelina | 2025-04-07 14:01:00 +0300 (lts 07 Apr 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js

Nom des icons + filtre recherche (comme les pages) - correction comportement modal icon bizarre dans safari
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js	(révision 14050)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js	(révision 14051)
@@ -86,6 +86,7 @@
                          *  
                          */
                         _onClickBrowseIcons:function(event){
+                            event.stopPropagation();
                             var usedIcon = (this.model.icon !== '' ? this.model.icon: '');
                             this.selectIconDialog.getIcons(usedIcon);
                             this.selectIconDialog.open();
