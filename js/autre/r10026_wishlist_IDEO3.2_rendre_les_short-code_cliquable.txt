Revision: r10026
Date: 2022-12-21 09:48:11 +0300 (lrb 21 Des 2022) 
Author: sraz<PERSON><PERSON><PERSON><PERSON> 

## Commit message
 wishlist IDEO3.2: rendre les short-code cliquable

## Files changed

## Full metadata
------------------------------------------------------------------------
r10026 | srazanandralisoa | 2022-12-21 09:48:11 +0300 (lrb 21 Des 2022) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/CollectionDetailView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NavigationPanel/Views/MenuEditView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NavigationPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NavigationPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NavigationPanel/nls/i18n.js

 wishlist IDEO3.2: rendre les short-code cliquable
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/FilePanel/Views/CollectionDetailView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/CollectionDetailView.js	(révision 10025)
+++ src/js/JEditor/FilePanel/Views/CollectionDetailView.js	(révision 10026)
@@ -38,6 +38,7 @@
             'uploadercomplete .group-content': 'uploadercomplete',
             'click [data-action="showuploader"]': 'showUploader',
             'sortstop .group-content': 'onSortStop',
+            'click .shortcode-collection.shortcode' : 'copyToClipboard'
         },
         initialize: function() {
             this._super();
@@ -137,6 +138,33 @@
             this.collection.sort();
             this.currentList = this.collection.toArray();
             this.model.save();
+        },
+        copyToClipboard : function (e){
+            var copyText = $(e.currentTarget).text();
+            const clipboard = navigator.clipboard;
+            if (clipboard !== undefined && clipboard !== "undefined") {
+                navigator.clipboard.writeText(copyText.trim()).then(this.successfully($(e.currentTarget)));
+            } else {
+                if (document.execCommand) {
+                const el = document.createElement("input");
+                el.value = copyText;
+                document.body.append(el);
+
+                el.select();
+                el.setSelectionRange(0, value.length);
+
+                if (document.execCommand("copy")) {
+                    this.successfully();
+                }
+                el.remove();
+                }
+            }
+        },
+        successfully :function (el){
+            el.before('<div role="alert" aria-live="polite" style="left: 590px; top: auto;position: absolute;" class="toastcopy"><div class="jq-toast-single jq-icon-success" style="text-align: left; /*! display: none; */"><span class="icon icon-check-circle"></span><span>'+translate("copy")+'</span></div></div>')
+            window.setTimeout(function(){
+                el.parent().find('.toastcopy').remove();
+            }, 2000);
         }
     });
     Events.extend({
Index: src/js/JEditor/NavigationPanel/Views/MenuEditView.js
===================================================================
--- src/js/JEditor/NavigationPanel/Views/MenuEditView.js	(révision 10025)
+++ src/js/JEditor/NavigationPanel/Views/MenuEditView.js	(révision 10026)
@@ -53,6 +53,7 @@
 
       'sortstop .sortable': "onSortStop",
       'click .save-button.active': 'onSaveClick',
+      'click .shortcode' : 'copyToClipboard'
     },
    
     onLoadSuccess: function () {
@@ -287,6 +288,34 @@
     },
     getSortable: function () {
       return this.menuItemView.$el;
+    },
+    copyToClipboard : function (e){
+      var copyText = $(e.currentTarget).text();
+       var text = copyText.replace('shortcode:', '').trim();
+      const clipboard = navigator.clipboard;
+      if (clipboard !== undefined && clipboard !== "undefined") {
+          navigator.clipboard.writeText(text).then(this.successfully($(e.currentTarget)));
+      } else {
+          if (document.execCommand) {
+          const el = document.createElement("input");
+          el.value = copyText;
+          document.body.append(el);
+
+          el.select();
+          el.setSelectionRange(0, value.length);
+
+          if (document.execCommand("copy")) {
+              this.successfully();
+          }
+          el.remove();
+          }
+      }
+    },
+    successfully :function (el){
+        el.before('<div role="alert" aria-live="polite" style="right: 17%; width="fit-content"; top: auto;position: absolute;" class="toastcopy"><div class="jq-toast-single jq-icon-success" style="text-align: left; /*! display: none; */"><span class="icon icon-check-circle"></span><span>'+translate("copy")+'</span></div></div>')
+        window.setTimeout(function(){
+            el.parent().find('.toastcopy').remove();
+        }, 2000);
     }
   });
   Object.defineProperties(MenuEditView.prototype, {
Index: src/js/JEditor/NavigationPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NavigationPanel/nls/fr-ca/i18n.js	(révision 10025)
+++ src/js/JEditor/NavigationPanel/nls/fr-ca/i18n.js	(révision 10026)
@@ -57,5 +57,6 @@
 	"placeholderHref":"http://google.com",
 	"myPageslpsupport" :"Mes pages supports",
 	"emptylp":" Page support vide",
+	"copy":"Copié",
 
 });
Index: src/js/JEditor/NavigationPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NavigationPanel/nls/fr-fr/i18n.js	(révision 10025)
+++ src/js/JEditor/NavigationPanel/nls/fr-fr/i18n.js	(révision 10026)
@@ -57,4 +57,5 @@
 	"placeholderHref":"http://google.com",
 	"myPageslpsupport" :"Mes pages supports",
 	"emptylp":" Page support vide",
+	"copy":"Copié",
 });
Index: src/js/JEditor/NavigationPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NavigationPanel/nls/i18n.js	(révision 10025)
+++ src/js/JEditor/NavigationPanel/nls/i18n.js	(révision 10026)
@@ -58,6 +58,7 @@
 		"introNavEnd":"Let's go !",
 		"myPageslpsupport" :"My support pages",
 		"emptylp":"Empty support page",
+        "copy":"Copied",
 	},
 	"fr-fr":true, "fr-ca":true
 });
