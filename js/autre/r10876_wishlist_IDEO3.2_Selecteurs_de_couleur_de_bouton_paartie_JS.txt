Revision: r10876
Date: 2023-05-02 12:07:57 +0300 (tlt 02 Mey 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: Selecteurs de couleur de bouton paartie JS

## Files changed

## Full metadata
------------------------------------------------------------------------
r10876 | srazanandralisoa | 2023-05-02 12:07:57 +0300 (tlt 02 Mey 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonStyleOption.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonBlock.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonStyleOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js
   A /branches/ideo3_v2/integration/src/less/imports/button_block/button_color.less
   M /branches/ideo3_v2/integration/src/less/imports/button_block/main.less
   M /branches/ideo3_v2/integration/src/less/imports/button_block/styleOptions.less

wishlist IDEO3.2: Selecteurs de couleur de bouton paartie JS
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonStyleOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonStyleOption.js	(révision 10875)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonStyleOption.js	(révision 10876)
@@ -5,6 +5,7 @@
 
 ], function(_,Events, AbstractOption) {
     var allowedSizes=["tiny","small","medium","large"];
+    var allowedColors=["pastel","vibrante","contour","pastel-lead","vibrante-lead","contour-lead"];
     var allowedButtonAlign=["align-left","align-center","align-right","full-width"];
     var allowedTextAlign=["text-left","text-right","text-center"];
 
@@ -21,7 +22,7 @@
              * @lends ImageOptionss
              */
                     {
-                        defaults: {optionType: 'ButtonStyleOption', priority: 80, size:'medium', buttonAlignment:'align-center', textAlignment:'text-center'},
+                        defaults: {optionType: 'ButtonStyleOption', priority: 80, size:'medium', buttonAlignment:'align-center', textAlignment:'text-center', color:'pastel'},
                         initialize: function() {
                             this._super();
                         },
@@ -38,8 +39,11 @@
                             if(allowedTextAlign.lastIndexOf(attributes.textAlignment)<=-1){
                                 return {field: "textAlignment", message: translate("Invalid_textAlignment" )};
                             }
+                            if(allowedColors.lastIndexOf(attributes.color)<=-1){
+                                return {field: "color", message: translate("Invalid_color" )};
+                            }
                         }
                     });
-            ButtonStyleOption.SetAttributes(['size', 'buttonAlignment', 'textAlignment']);
+            ButtonStyleOption.SetAttributes(['size', 'buttonAlignment', 'textAlignment','color']);
             return ButtonStyleOption;
         });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonBlock.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonBlock.html	(révision 10875)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonBlock.html	(révision 10876)
@@ -1,4 +1,4 @@
-<div class="block-button <%=size%> <%=buttonAlignment%> <%=textAlignment%>">
+<div class="block-button <%=size%> <%=buttonAlignment%> <%=textAlignment%> <%=color%>">
   <a href="#" class="button">
     <span class="txt"><span><%=text%></span></span>
   </a>
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js	(révision 10875)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js	(révision 10876)
@@ -29,7 +29,8 @@
                             var sizeButton = this.model.options.ButtonStyleOption.size;
                             var alignButton = this.model.options.ButtonStyleOption.buttonAlignment;
                             var alignText = this.model.options.ButtonStyleOption.textAlignment;
-                            this.$('.content').append(this._contentTemplate({size: sizeButton, text: textButton, buttonAlignment:alignButton, textAlignment:alignText}));
+                            var color = this.model.options.ButtonStyleOption.color;
+                            this.$('.content').append(this._contentTemplate({size: sizeButton, text: textButton, buttonAlignment:alignButton, textAlignment:alignText, color :color}));
                             return this;
                         },
                         renderOptions: function(model, options) {
@@ -36,10 +37,11 @@
                             var sizeButton = this.model.options.ButtonStyleOption.size;
                             var alignButton = this.model.options.ButtonStyleOption.buttonAlignment;
                             var alignText = this.model.options.ButtonStyleOption.textAlignment;
-                            this.$(".block-button").attr("class", "block-button " + sizeButton + " " + alignButton + " " + alignText);
+                            var color = this.model.options.ButtonStyleOption.color;
+                            this.$(".block-button").attr("class", "block-button " + sizeButton + " " + alignButton + " " + alignText + " " + color);
                             var textButton = this.model.options.ButtonOption.text?this.model.options.ButtonOption.text:translate('button');
                             this.$(".txt span").text(textButton);
-                            this.$(".block-button").attr("class", "block-button " + sizeButton + " " + alignButton + " " + alignText);
+                            this.$(".block-button").attr("class", "block-button " + sizeButton + " " + alignButton + " " + alignText + " " + color);
                             return this;
                         }
                     });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js	(révision 10875)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js	(révision 10876)
@@ -10,6 +10,7 @@
                         tagName: "div",
                         className: "panel-content button-style-panel ",
                         events: {
+                            'click .button-color .effect-radio': "_onChangeColor"
                         },
                         /**
                          * initialise l'objet
@@ -27,14 +28,26 @@
                             this.$el.mCustomScrollbar('update');
                         },
                         /**
+                         * action changer le color du boutton
+                         * @param {*} event 
+                         */
+                        _onChangeColor:function(event){
+                            this.$(".effect-radio").removeClass("active");
+                             var $target = $(event.currentTarget);
+                             $target.addClass("active");
+                             var value = $target.attr("data-value");
+                             this.model.color=value;
+                         },  
+                        /**
                          * actualise l'affichage de la vue
                          */
                         render: function() {
-                            var templateVars = {size:this.model.size, buttonAlignment:this.model.buttonAlignment, textAlignment:this.model.textAlignment};
+                            var templateVars = {size:this.model.size, buttonAlignment:this.model.buttonAlignment, textAlignment:this.model.textAlignment, color:this.model.color};
                             this.$el.html(this.template(templateVars));
                             this.scrollables();
                             return this;
                         }
+                        
             }
     );
 
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js	(révision 10875)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js	(révision 10876)
@@ -27,5 +27,13 @@
     "rightAlignText": "Aligné à droite",
     "ButtonOption": "Bouton",
     "ButtonStyleOption": "Style",
-    "button":"Bouton"
+    "button":"Bouton",
+    "colorButton":"Couleur du bouton",
+    "colorButtonLegend":"Appliquez une coleur au bouton",
+    "pastel":"Pastel",
+    "vibrante":"Vibrante",
+    "contour":"Contour",
+    "pastel-lead":"Pastel Lead",
+    "vibrante-lead":"Vibrante Lead",
+    "contour-lead":"Contour Lead"
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js	(révision 10875)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js	(révision 10876)
@@ -27,5 +27,14 @@
     "rightAlignText": "Aligné à droite",
     "ButtonOption": "Bouton",
     "ButtonStyleOption": "Style",
-    "button":"Bouton"
+    "button":"Bouton",
+    "colorButton":"Couleur du bouton",
+    "colorButtonLegend":"Appliquez une coleur au bouton",
+    "pastel":"Pastel",
+    "vibrante":"Vibrante",
+    "contour":"Contour",
+    "pastel-lead":"Pastel Lead",
+    "vibrante-lead":"Vibrante Lead",
+    "contour-lead":"Contour Lead"
+
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js	(révision 10875)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js	(révision 10876)
@@ -28,7 +28,15 @@
         "rightAlignText": "Right-aligned",
         "ButtonOption": "Button",
         "ButtonStyleOption": "Style",
-        "button":"Button"
+        "button":"Button",
+        "colorButton":"Button color",
+        "colorButtonLegend":"Apply a color to the button",
+        "pastel":"Pastel",
+        "vibrant":"Vibrant",
+        "outline":"Outline",
+        "pastel-lead":"Pastel Lead",
+        "vibrante-lead":"Vibrante Lead",
+        "contour-lead":"Contour Lead"
     },
     "fr-fr": true,
     "fr-ca": true
Index: src/less/imports/button_block/main.less
===================================================================
--- src/less/imports/button_block/main.less	(révision 10875)
+++ src/less/imports/button_block/main.less	(révision 10876)
@@ -1,3 +1,4 @@
 @import "./styleOptions.less";
 @import "./button_options.less";
 @import "./button_block.less";
+@import "./button_color.less";
Index: src/less/imports/button_block/styleOptions.less
===================================================================
--- src/less/imports/button_block/styleOptions.less	(révision 10875)
+++ src/less/imports/button_block/styleOptions.less	(révision 10876)
@@ -49,3 +49,77 @@
         }
     }
 }
+.button-color{
+    .category-content.radio-transformed{
+        .clearfix();
+        width:90%;
+        margin:auto;
+        padding:5%;
+        background:#2f2f2f;
+    }
+    .drop-down-wrapper .btn-group,.drop-down-wrapper .btn.dropdown-toggle,.drop-down-wrapper .dropdown-menu {
+        width:100%;
+        background:#444444;
+        &>li{
+            border-top:1px solid #555555;
+            &:first-child{
+                border:none;
+            }
+            &>a:hover{
+                background:#666666;
+            }
+        }
+    }
+    .drop-down-wrapper .btn.dropdown-toggle{
+        padding:5px 0;
+        background:@pageColor;
+        color:#ffffff;
+        &>span{
+            position:relative;
+            &.text{
+                float:left;
+                left:10px;
+            }
+            &.caret{
+                float:right;
+                right:10px;
+            }
+        }
+    }
+    .category-content.radio-transformed>div{
+        width:33.3%;
+        float:left;
+        & .color-radio{
+            & .container{
+                background:#222222;
+            }
+            &.active .container{
+                background:@pageColor;
+            }
+        }
+        & .container{
+
+
+        }
+    }
+    .template-selector-wrapper{
+        .btn-group > .btn:active{
+            z-index:initial;
+        }
+        .category-content.radio-transformed{
+            z-index:999;
+        }
+        .color-radio .icon{
+            font-size:30px;
+        }
+    }
+}
+.color-radio .icon, .color-switcher .icon {
+    font-size: 1.5em;
+}
+.color-radio .switch-container .radio, .color-radio .switch-container .switch, .color-switcher .switch-container .radio, .color-switcher .switch-container .switch {
+    margin-top: 5px;
+}
+.color-radio.active .radio, .color-radio.active:hover .radio, .color-switcher.active .radio, .color-switcher.active:hover .radio {
+    background-position: -1px -24px;
+}
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonStyleOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonStyleOption.html	(révision 10875)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonStyleOption.html	(révision 10876)
@@ -212,3 +212,108 @@
         </div>
     </article>
 </div>
+<article class="panel-option animations button-color">
+    <header>
+        <h3 class="option-name"><%=__("colorButton")%></h3>
+        <span class="panel-content-legend"> <%=__("colorButtonLegend")%></span>
+    </header>
+    <div class="category-content radio-transformed">
+        <%  var _id= _.uniqueId('sizeButton'); %>
+        <div class="color-radio">
+            <span class="effect-radio pastel <%=(color==='pastel')?'active':''%>" id="radio_color<%=_id%>" data-value="pastel" data-helper="pastel">
+                <span class="helper">
+                    <span class="help"><%=__("pastel")%></span>
+                    <span class="bottom"></span>
+                </span>
+                <span class="container">
+                    <span class="icon icon-form-long_text"></span>
+                    <span class="switch-container">
+                        <span class="radio">
+                            <span></span>
+                        </span>
+                    </span>
+                </span>
+            </span>
+        </div> 
+        <div class="color-radio">
+            <span class="effect-radio vibrante <%=(color==='vibrante')?'active':''%>" id="radio_color<%=_id%>" data-value="vibrante" data-helper="vibrante">
+                <span class="helper">
+                    <span class="help"><%=__("vibrante")%></span>
+                    <span class="bottom"></span>
+                </span>
+                <span class="container">
+                    <span class="icon icon-form-long_text"></span>
+                    <span class="switch-container">
+                        <span class="radio">
+                            <span></span>
+                        </span>
+                    </span>
+                </span>
+            </span>
+        </div> 
+        <div class="color-radio">
+            <span class="effect-radio contour <%=(color==='contour')?'active':''%>" id="radio_color<%=_id%>" data-value="contour" data-helper="contour">
+                <span class="helper">
+                    <span class="help"><%=__("contour")%></span>
+                    <span class="bottom"></span>
+                </span>
+                <span class="container">
+                    <span class="icon icon-form-long_text"></span>
+                    <span class="switch-container">
+                        <span class="radio">
+                            <span></span>
+                        </span>
+                    </span>
+                </span>
+            </span>
+        </div>
+        <div class="color-radio">
+            <span class="effect-radio pastel-lead <%=(color==='pastel-lead')?'active':''%>" id="radio_color<%=_id%>" data-value="pastel-lead" data-helper="pastel-lead">
+                <span class="helper">
+                    <span class="help"><%=__("pastel-lead")%></span>
+                    <span class="bottom"></span>
+                </span>
+                <span class="container">
+                    <span class="icon icon-form-long_text"></span>
+                    <span class="switch-container">
+                        <span class="radio">
+                            <span></span>
+                        </span>
+                    </span>
+                </span>
+            </span>
+        </div> 
+        <div class="color-radio">
+            <span class="effect-radio vibrante-lead <%=(color==='vibrante-lead')?'active':''%>" id="radio_color<%=_id%>" data-value="vibrante-lead" data-helper="vibrante-lead">
+                <span class="helper">
+                    <span class="help"><%=__("vibrante-lead")%></span>
+                    <span class="bottom"></span>
+                </span>
+                <span class="container">
+                    <span class="icon icon-form-long_text"></span>
+                    <span class="switch-container">
+                        <span class="radio">
+                            <span></span>
+                        </span>
+                    </span>
+                </span>
+            </span>
+        </div> 
+        <div class="color-radio">
+            <span class="effect-radio contour-lead <%=(color==='contour-lead')?'active':''%>" id="radio_color<%=_id%>" data-value="contour-lead" data-helper="contour-lead">
+                <span class="helper">
+                    <span class="help"><%=__("contour-lead")%></span>
+                    <span class="bottom"></span>
+                </span>
+                <span class="container">
+                    <span class="icon icon-form-long_text"></span>
+                    <span class="switch-container">
+                        <span class="radio">
+                            <span></span>
+                        </span>
+                    </span>
+                </span>
+            </span>
+        </div>  
+    </div>
+</article>
Index: src/less/imports/button_block/button_color.less
===================================================================
--- src/less/imports/button_block/button_color.less	(nonexistent)
+++ src/less/imports/button_block/button_color.less	(révision 10876)
@@ -0,0 +1,28 @@
+.color-radio, .block-button{
+    background-color: var(--bg-actions);
+    color: var(--fg-actions);
+    border: 2px solid var(--border-actions);
+    box-sizing: border-box;
+}
+.vibrante{
+    --bg-actions: var(--accent-surface1);
+    --border-actions: var(--accent-border);
+    --fg-actions: var(--accent-text1);
+}
+.contour{
+    --bg-actions: transparent;
+     --fg-actions: var(--fg-links);
+}
+.pastel-lead{
+    --bg-actions: var(--lead-surface0); 
+    --border-actions: var(--lead-border);
+    --fg-actions: var(--lead-text0);
+}
+.vibrante-lead{
+    --bg-actions: var(--lead-surface1);
+    --border-actions: var(--lead-border); 
+    --fg-actions: var(--lead-text1);
+}
+.contour-lead{
+    --bg-actions: transparent; --border-actions: var(--lead-border); --fg-actions: var(--fg-lead);
+}
\ No newline at end of file
