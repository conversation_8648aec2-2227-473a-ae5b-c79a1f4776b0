Revision: r10416
Date: 2023-02-15 19:44:43 +0300 (lrb 15 Feb 2023) 
Author: mpartaux 

## Commit message
update default forms FR, EN, ES

## Files changed

## Full metadata
------------------------------------------------------------------------
r10416 | mpartaux | 2023-02-15 19:44:43 +0300 (lrb 15 Feb 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/FormTemplate.js

update default forms FR, EN, ES
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/FormTemplate.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/FormTemplate.js	(révision 10415)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/FormTemplate.js	(révision 10416)
@@ -1,7 +1,7 @@
 define({
     fr_FR: {
         commerce: {
-            "name": "",
+            "name": "Formulaire de contact",
             "buttonText": "Envoyer",
             "action": "Send",
             "successMessage": null,
@@ -13,11 +13,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Demande",
-                    "description": "",
+                    "description": "Choisissez une option",
                     "placeholder": "",
                     "options": [
                         "Demande de devis / tarifs",
@@ -30,8 +30,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Précisions",
-                    "description": "",
-                    "placeholder": "Précisez votre demande",
+                    "description": "Précisez votre demande",
+                    "placeholder": "Bonjour, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -46,8 +46,8 @@
                 }, {
                     "type": "name",
                     "label": "Nom",
-                    "description": "",
-                    "placeholder": "Votre nom complet",
+                    "description": "Saisissez votre nom complet",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -54,8 +54,8 @@
                 }, {
                     "type": "tel",
                     "label": "Téléphone",
-                    "description": "",
-                    "placeholder": "Votre numéro de téléphone",
+                    "description": "Saisissez votre numéro de téléphone",
+                    "placeholder": "06########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -62,8 +62,8 @@
                 }, {
                     "type": "email",
                     "label": "E-mail",
-                    "description": "",
-                    "placeholder": "Votre adresse e-mail",
+                    "description": "Saisissez votre adresse email",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -71,7 +71,7 @@
             ]
         },
         construction: {
-            "name": "",
+            "name": "Formulaire de contact",
             "buttonText": "Envoyer",
             "action": "Send",
             "successMessage": null,
@@ -83,11 +83,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Demande",
-                    "description": "",
+                    "description": "Choisissez une option",
                     "placeholder": "",
                     "options": [
                         "Demande de devis",
@@ -98,8 +98,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Précisions",
-                    "description": "",
-                    "placeholder": "Précisez votre demande",
+                    "description": "Précisez votre demande",
+                    "placeholder": "Bonjour, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -114,8 +114,8 @@
                 }, {
                     "type": "name",
                     "label": "Nom",
-                    "description": "",
-                    "placeholder": "Votre nom complet",
+                    "description": "Saisissez votre nom complet",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -122,8 +122,8 @@
                 }, {
                     "type": "tel",
                     "label": "Téléphone",
-                    "description": "",
-                    "placeholder": "Votre numéro de téléphone",
+                    "description": "Saisissez votre numéro de téléphone",
+                    "placeholder": "06########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -130,8 +130,8 @@
                 }, {
                     "type": "email",
                     "label": "E-mail",
-                    "description": "",
-                    "placeholder": "Votre adresse e-mail",
+                    "description": "Saisissez votre adresse email",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -151,11 +151,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Demande",
-                    "description": "",
+                    "description": "Choisissez une option",
                     "placeholder": "",
                     "options": [
                         "Demande de réservation",
@@ -167,8 +167,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Précisions",
-                    "description": "",
-                    "placeholder": "Précisez votre demande",
+                    "description": "Précisez votre demande",
+                    "placeholder": "Bonjour, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -183,8 +183,8 @@
                 }, {
                     "type": "name",
                     "label": "Nom",
-                    "description": "",
-                    "placeholder": "Votre nom complet",
+                    "description": "Saisissez votre nom complet",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -191,8 +191,8 @@
                 }, {
                     "type": "tel",
                     "label": "Téléphone",
-                    "description": "",
-                    "placeholder": "Votre numéro de téléphone",
+                    "description": "Saisissez votre numéro de téléphone",
+                    "placeholder": "06########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -199,8 +199,8 @@
                 }, {
                     "type": "email",
                     "label": "E-mail",
-                    "description": "",
-                    "placeholder": "Votre adresse e-mail",
+                    "description": "Saisissez votre adresse email",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -220,11 +220,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Demande",
-                    "description": "",
+                    "description": "Choisissez une option",
                     "placeholder": "",
                     "options": [
                         "Demande de rendez-vous",
@@ -236,8 +236,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Précisions",
-                    "description": "",
-                    "placeholder": "Précisez votre demande",
+                    "description": "Précisez votre demande",
+                    "placeholder": "Bonjour, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -252,8 +252,8 @@
                 }, {
                     "type": "name",
                     "label": "Nom",
-                    "description": "",
-                    "placeholder": "Votre nom complet",
+                    "description": "Saisissez votre nom complet",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -260,8 +260,8 @@
                 }, {
                     "type": "tel",
                     "label": "Téléphone",
-                    "description": "",
-                    "placeholder": "Votre numéro de téléphone",
+                    "description": "Saisissez votre numéro de téléphone",
+                    "placeholder": "06########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -268,8 +268,8 @@
                 }, {
                     "type": "email",
                     "label": "E-mail",
-                    "description": "",
-                    "placeholder": "Votre adresse e-mail",
+                    "description": "Saisissez votre adresse email",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -289,11 +289,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Demande",
-                    "description": "",
+                    "description": "Choisissez une option",
                     "placeholder": "",
                     "options": [
                         "Demande de rendez-vous",
@@ -306,8 +306,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Précisions",
-                    "description": "",
-                    "placeholder": "Précisez votre demande",
+                    "description": "Précisez votre demande",
+                    "placeholder": "Bonjour, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -322,8 +322,8 @@
                 }, {
                     "type": "name",
                     "label": "Nom",
-                    "description": "",
-                    "placeholder": "Votre nom complet",
+                    "description": "Saisissez votre nom complet",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -330,8 +330,8 @@
                 }, {
                     "type": "tel",
                     "label": "Téléphone",
-                    "description": "",
-                    "placeholder": "Votre numéro de téléphone",
+                    "description": "Saisissez votre numéro de téléphone",
+                    "placeholder": "06########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -338,8 +338,8 @@
                 }, {
                     "type": "email",
                     "label": "E-mail",
-                    "description": "",
-                    "placeholder": "Votre adresse e-mail",
+                    "description": "Saisissez votre adresse email",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -359,11 +359,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Demande",
-                    "description": "",
+                    "description": "Choisissez une option",
                     "placeholder": "",
                     "options": [
                         "Achat / Vente",
@@ -376,8 +376,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Précisions",
-                    "description": "",
-                    "placeholder": "Précisez votre demande",
+                    "description": "Précisez votre demande",
+                    "placeholder": "Bonjour, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -392,8 +392,8 @@
                 }, {
                     "type": "name",
                     "label": "Nom",
-                    "description": "",
-                    "placeholder": "Votre nom complet",
+                    "description": "Saisissez votre nom complet",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -400,8 +400,8 @@
                 }, {
                     "type": "tel",
                     "label": "Téléphone",
-                    "description": "",
-                    "placeholder": "Votre numéro de téléphone",
+                    "description": "Saisissez votre numéro de téléphone",
+                    "placeholder": "06########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -408,8 +408,8 @@
                 }, {
                     "type": "email",
                     "label": "E-mail",
-                    "description": "",
-                    "placeholder": "Votre adresse e-mail",
+                    "description": "Saisissez votre adresse email",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -429,11 +429,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Demande",
-                    "description": "",
+                    "description": "Choisissez une option",
                     "placeholder": "",
                     "options": [
                         "Demande de rendez-vous",
@@ -445,8 +445,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Précisions",
-                    "description": "",
-                    "placeholder": "Précisez votre demande",
+                    "description": "Précisez votre demande",
+                    "placeholder": "Bonjour, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -461,8 +461,8 @@
                 }, {
                     "type": "name",
                     "label": "Nom",
-                    "description": "",
-                    "placeholder": "Votre nom complet",
+                    "description": "Saisissez votre nom complet",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -469,8 +469,8 @@
                 }, {
                     "type": "tel",
                     "label": "Téléphone",
-                    "description": "",
-                    "placeholder": "Votre numéro de téléphone",
+                    "description": "Saisissez votre numéro de téléphone",
+                    "placeholder": "06########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -477,8 +477,8 @@
                 }, {
                     "type": "email",
                     "label": "E-mail",
-                    "description": "",
-                    "placeholder": "Votre adresse e-mail",
+                    "description": "Saisissez votre adresse email",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -498,11 +498,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Demande",
-                    "description": "",
+                    "description": "Choisissez une option",
                     "placeholder": "",
                     "options": [
                         "Demande de réservation",
@@ -514,8 +514,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Précisions",
-                    "description": "",
-                    "placeholder": "Précisez votre demande",
+                    "description": "Précisez votre demande",
+                    "placeholder": "Bonjour, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -530,8 +530,8 @@
                 }, {
                     "type": "name",
                     "label": "Nom",
-                    "description": "",
-                    "placeholder": "Votre nom complet",
+                    "description": "Saisissez votre nom complet",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -538,8 +538,8 @@
                 }, {
                     "type": "tel",
                     "label": "Téléphone",
-                    "description": "",
-                    "placeholder": "Votre numéro de téléphone",
+                    "description": "Saisissez votre numéro de téléphone",
+                    "placeholder": "06########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -546,8 +546,8 @@
                 }, {
                     "type": "email",
                     "label": "E-mail",
-                    "description": "",
-                    "placeholder": "Votre adresse e-mail",
+                    "description": "Saisissez votre adresse email",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -567,11 +567,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Demande",
-                    "description": "",
+                    "description": "Choisissez une option",
                     "placeholder": "",
                     "options": [
                         "Questions sur les produits et services",
@@ -583,8 +583,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Précisions",
-                    "description": "",
-                    "placeholder": "Précisez votre demande",
+                    "description": "Précisez votre demande",
+                    "placeholder": "Bonjour, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -599,8 +599,8 @@
                 }, {
                     "type": "name",
                     "label": "Nom",
-                    "description": "",
-                    "placeholder": "Votre nom complet",
+                    "description": "Saisissez votre nom complet",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -607,8 +607,8 @@
                 }, {
                     "type": "tel",
                     "label": "Téléphone",
-                    "description": "",
-                    "placeholder": "Votre numéro de téléphone",
+                    "description": "Saisissez votre numéro de téléphone",
+                    "placeholder": "06########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -615,8 +615,8 @@
                 }, {
                     "type": "email",
                     "label": "E-mail",
-                    "description": "",
-                    "placeholder": "Votre adresse e-mail",
+                    "description": "Saisissez votre adresse email",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -636,11 +636,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Demande",
-                    "description": "",
+                    "description": "Choisissez une option",
                     "placeholder": "",
                     "options": [
                         "Demande de tarifs / Commande événement",
@@ -652,8 +652,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Précisions",
-                    "description": "",
-                    "placeholder": "Précisez votre demande",
+                    "description": "Précisez votre demande",
+                    "placeholder": "Bonjour, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -668,8 +668,8 @@
                 }, {
                     "type": "name",
                     "label": "Nom",
-                    "description": "",
-                    "placeholder": "Votre nom complet",
+                    "description": "Saisissez votre nom complet",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -676,8 +676,8 @@
                 }, {
                     "type": "tel",
                     "label": "Téléphone",
-                    "description": "",
-                    "placeholder": "Votre numéro de téléphone",
+                    "description": "Saisissez votre numéro de téléphone",
+                    "placeholder": "06########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -684,8 +684,8 @@
                 }, {
                     "type": "email",
                     "label": "E-mail",
-                    "description": "",
-                    "placeholder": "Votre adresse e-mail",
+                    "description": "Saisissez votre adresse email",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -695,7 +695,7 @@
     },
     fr_CA: {
         commerce: {
-            "name": "",
+            "name": "Formulaire de contact",
             "buttonText": "Envoyer",
             "action": "Send",
             "successMessage": null,
@@ -707,11 +707,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Demande",
-                    "description": "",
+                    "description": "Choisissez une option",
                     "placeholder": "",
                     "options": [
                         "Demande de soumission / tarifs",
@@ -724,8 +724,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Précisions",
-                    "description": "",
-                    "placeholder": "Précisez votre demande",
+                    "description": "Précisez votre demande",
+                    "placeholder": "Bonjour, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -740,8 +740,8 @@
                 }, {
                     "type": "name",
                     "label": "Nom",
-                    "description": "",
-                    "placeholder": "Votre nom complet",
+                    "description": "Saisissez votre nom complet",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -748,8 +748,8 @@
                 }, {
                     "type": "tel",
                     "label": "Téléphone",
-                    "description": "",
-                    "placeholder": "Votre numéro de téléphone",
+                    "description": "Saisissez votre numéro de téléphone",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -756,8 +756,8 @@
                 }, {
                     "type": "email",
                     "label": "Courriel",
-                    "description": "",
-                    "placeholder": "Votre courriel",
+                    "description": "Saisissez votre courriel",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -765,7 +765,7 @@
             ]
         },
         construction: {
-            "name": "",
+            "name": "Formulaire de contact",
             "buttonText": "Envoyer",
             "action": "Send",
             "successMessage": null,
@@ -777,11 +777,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Demande",
-                    "description": "",
+                    "description": "Choisissez une option",
                     "placeholder": "",
                     "options": [
                         "Demande de soumission",
@@ -792,8 +792,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Précisions",
-                    "description": "",
-                    "placeholder": "Précisez votre demande",
+                    "description": "Précisez votre demande",
+                    "placeholder": "Bonjour, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -808,8 +808,8 @@
                 }, {
                     "type": "name",
                     "label": "Nom",
-                    "description": "",
-                    "placeholder": "Votre nom complet",
+                    "description": "Saisissez votre nom complet",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -816,8 +816,8 @@
                 }, {
                     "type": "tel",
                     "label": "Téléphone",
-                    "description": "",
-                    "placeholder": "Votre numéro de téléphone",
+                    "description": "Saisissez votre numéro de téléphone",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -824,8 +824,8 @@
                 }, {
                     "type": "email",
                     "label": "Courriel",
-                    "description": "",
-                    "placeholder": "Votre courriel",
+                    "description": "Saisissez votre courriel",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -833,7 +833,7 @@
             ]
         },
         restaurant: {
-            "name": "",
+            "name": "Formulaire de contact",
             "buttonText": "Envoyer",
             "action": "Send",
             "successMessage": null,
@@ -845,11 +845,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Demande",
-                    "description": "",
+                    "description": "Choisissez une option",
                     "placeholder": "",
                     "options": [
                         "Demande de réservation",
@@ -861,8 +861,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Précisions",
-                    "description": "",
-                    "placeholder": "Précisez votre demande",
+                    "description": "Précisez votre demande",
+                    "placeholder": "Bonjour, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -877,8 +877,8 @@
                 }, {
                     "type": "name",
                     "label": "Nom",
-                    "description": "",
-                    "placeholder": "Votre nom complet",
+                    "description": "Saisissez votre nom complet",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -885,8 +885,8 @@
                 }, {
                     "type": "tel",
                     "label": "Téléphone",
-                    "description": "",
-                    "placeholder": "Votre numéro de téléphone",
+                    "description": "Saisissez votre numéro de téléphone",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -893,8 +893,8 @@
                 }, {
                     "type": "email",
                     "label": "Courriel",
-                    "description": "",
-                    "placeholder": "Votre courriel",
+                    "description": "Saisissez votre courriel",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -902,7 +902,7 @@
             ]
         },
         automobile: {
-            "name": "",
+            "name": "Formulaire de contact",
             "buttonText": "Envoyer",
             "action": "Send",
             "successMessage": null,
@@ -914,11 +914,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Demande",
-                    "description": "",
+                    "description": "Choisissez une option",
                     "placeholder": "",
                     "options": [
                         "Demande de rendez-vous",
@@ -930,8 +930,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Précisions",
-                    "description": "",
-                    "placeholder": "Précisez votre demande",
+                    "description": "Précisez votre demande",
+                    "placeholder": "Bonjour, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -946,8 +946,8 @@
                 }, {
                     "type": "name",
                     "label": "Nom",
-                    "description": "",
-                    "placeholder": "Votre nom complet",
+                    "description": "Saisissez votre nom complet",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -954,8 +954,8 @@
                 }, {
                     "type": "tel",
                     "label": "Téléphone",
-                    "description": "",
-                    "placeholder": "Votre numéro de téléphone",
+                    "description": "Saisissez votre numéro de téléphone",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -962,8 +962,8 @@
                 }, {
                     "type": "email",
                     "label": "Courriel",
-                    "description": "",
-                    "placeholder": "Votre courriel",
+                    "description": "Saisissez votre courriel",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -971,7 +971,7 @@
             ]
         },
         banque: {
-            "name": "",
+            "name": "Formulaire de contact",
             "buttonText": "Envoyer",
             "action": "Send",
             "successMessage": null,
@@ -983,11 +983,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Demande",
-                    "description": "",
+                    "description": "Choisissez une option",
                     "placeholder": "",
                     "options": [
                         "Demande de rendez-vous",
@@ -1000,8 +1000,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Précisions",
-                    "description": "",
-                    "placeholder": "Précisez votre demande",
+                    "description": "Précisez votre demande",
+                    "placeholder": "Bonjour, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -1016,8 +1016,8 @@
                 }, {
                     "type": "name",
                     "label": "Nom",
-                    "description": "",
-                    "placeholder": "Votre nom complet",
+                    "description": "Saisissez votre nom complet",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1024,8 +1024,8 @@
                 }, {
                     "type": "tel",
                     "label": "Téléphone",
-                    "description": "",
-                    "placeholder": "Votre numéro de téléphone",
+                    "description": "Saisissez votre numéro de téléphone",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1032,8 +1032,8 @@
                 }, {
                     "type": "email",
                     "label": "Courriel",
-                    "description": "",
-                    "placeholder": "Votre courriel",
+                    "description": "Saisissez votre courriel",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1041,7 +1041,7 @@
             ]
         },
         immobilier: {
-            "name": "",
+            "name": "Formulaire de contact",
             "buttonText": "Envoyer",
             "action": "Send",
             "successMessage": null,
@@ -1053,11 +1053,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Demande",
-                    "description": "",
+                    "description": "Choisissez une option",
                     "placeholder": "",
                     "options": [
                         "Achat / Vente",
@@ -1070,8 +1070,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Précisions",
-                    "description": "",
-                    "placeholder": "Précisez votre demande",
+                    "description": "Précisez votre demande",
+                    "placeholder": "Bonjour, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -1086,8 +1086,8 @@
                 }, {
                     "type": "name",
                     "label": "Nom",
-                    "description": "",
-                    "placeholder": "Votre nom complet",
+                    "description": "Saisissez votre nom complet",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1094,8 +1094,8 @@
                 }, {
                     "type": "tel",
                     "label": "Téléphone",
-                    "description": "",
-                    "placeholder": "Votre numéro de téléphone",
+                    "description": "Saisissez votre numéro de téléphone",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1102,8 +1102,8 @@
                 }, {
                     "type": "email",
                     "label": "Courriel",
-                    "description": "",
-                    "placeholder": "Votre courriel",
+                    "description": "Saisissez votre courriel",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1111,7 +1111,7 @@
             ]
         },
         sante: {
-            "name": "",
+            "name": "Formulaire de contact",
             "buttonText": "Envoyer",
             "action": "Send",
             "successMessage": null,
@@ -1123,11 +1123,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Demande",
-                    "description": "",
+                    "description": "Choisissez une option",
                     "placeholder": "",
                     "options": [
                         "Demande de rendez-vous",
@@ -1139,8 +1139,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Précisions",
-                    "description": "",
-                    "placeholder": "Précisez votre demande",
+                    "description": "Précisez votre demande",
+                    "placeholder": "Bonjour, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -1155,8 +1155,8 @@
                 }, {
                     "type": "name",
                     "label": "Nom",
-                    "description": "",
-                    "placeholder": "Votre nom complet",
+                    "description": "Saisissez votre nom complet",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1163,8 +1163,8 @@
                 }, {
                     "type": "tel",
                     "label": "Téléphone",
-                    "description": "",
-                    "placeholder": "Votre numéro de téléphone",
+                    "description": "Saisissez votre numéro de téléphone",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1171,8 +1171,8 @@
                 }, {
                     "type": "email",
                     "label": "Courriel",
-                    "description": "",
-                    "placeholder": "Votre courriel",
+                    "description": "Saisissez votre courriel",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1180,7 +1180,7 @@
             ]
         },
         hotel: {
-            "name": "",
+            "name": "Formulaire de contact",
             "buttonText": "Envoyer",
             "action": "Send",
             "successMessage": null,
@@ -1192,11 +1192,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Demande",
-                    "description": "",
+                    "description": "Choisissez une option",
                     "placeholder": "",
                     "options": [
                         "Demande de réservation",
@@ -1208,8 +1208,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Précisions",
-                    "description": "",
-                    "placeholder": "Précisez votre demande",
+                    "description": "Précisez votre demande",
+                    "placeholder": "Bonjour, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -1224,8 +1224,8 @@
                 }, {
                     "type": "name",
                     "label": "Nom",
-                    "description": "",
-                    "placeholder": "Votre nom complet",
+                    "description": "Saisissez votre nom complet",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1232,8 +1232,8 @@
                 }, {
                     "type": "tel",
                     "label": "Téléphone",
-                    "description": "",
-                    "placeholder": "Votre numéro de téléphone",
+                    "description": "Saisissez votre numéro de téléphone",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1240,8 +1240,8 @@
                 }, {
                     "type": "email",
                     "label": "Courriel",
-                    "description": "",
-                    "placeholder": "Votre courriel",
+                    "description": "Saisissez votre courriel",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1249,7 +1249,7 @@
             ]
         },
         secteurpublic: {
-            "name": "",
+            "name": "Formulaire de contact",
             "buttonText": "Envoyer",
             "action": "Send",
             "successMessage": null,
@@ -1261,11 +1261,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Demande",
-                    "description": "",
+                    "description": "Choisissez une option",
                     "placeholder": "",
                     "options": [
                         "Questions sur les produits et services",
@@ -1277,8 +1277,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Précisions",
-                    "description": "",
-                    "placeholder": "Précisez votre demande",
+                    "description": "Précisez votre demande",
+                    "placeholder": "Bonjour, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -1293,8 +1293,8 @@
                 }, {
                     "type": "name",
                     "label": "Nom",
-                    "description": "",
-                    "placeholder": "Votre nom complet",
+                    "description": "Saisissez votre nom complet",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1301,8 +1301,8 @@
                 }, {
                     "type": "tel",
                     "label": "Téléphone",
-                    "description": "",
-                    "placeholder": "Votre numéro de téléphone",
+                    "description": "Saisissez votre numéro de téléphone",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1309,8 +1309,8 @@
                 }, {
                     "type": "email",
                     "label": "Courriel",
-                    "description": "",
-                    "placeholder": "Votre courriel",
+                    "description": "Saisissez votre courriel",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1318,7 +1318,7 @@
             ]
         },
         proximite: {
-            "name": "",
+            "name": "Formulaire de contact",
             "buttonText": "Envoyer",
             "action": "Send",
             "successMessage": null,
@@ -1330,11 +1330,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Demande",
-                    "description": "",
+                    "description": "Choisissez une option",
                     "placeholder": "",
                     "options": [
                         "Demande de tarifs / Commande événement",
@@ -1346,8 +1346,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Précisions",
-                    "description": "",
-                    "placeholder": "Précisez votre demande",
+                    "description": "Précisez votre demande",
+                    "placeholder": "Bonjour, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -1362,8 +1362,8 @@
                 }, {
                     "type": "name",
                     "label": "Nom",
-                    "description": "",
-                    "placeholder": "Votre nom complet",
+                    "description": "Saisissez votre nom complet",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1370,8 +1370,8 @@
                 }, {
                     "type": "tel",
                     "label": "Téléphone",
-                    "description": "",
-                    "placeholder": "Votre numéro de téléphone",
+                    "description": "Saisissez votre numéro de téléphone",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1378,8 +1378,8 @@
                 }, {
                     "type": "email",
                     "label": "Courriel",
-                    "description": "",
-                    "placeholder": "Votre courriel",
+                    "description": "Saisissez votre courriel",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1389,7 +1389,7 @@
     },
     en_CA: {
         commerce: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -1401,11 +1401,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Quote request / prices",
@@ -1418,8 +1418,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -1434,8 +1434,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1442,8 +1442,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1450,8 +1450,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1459,7 +1459,7 @@
             ]
         },
         construction: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -1471,11 +1471,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Quote request",
@@ -1486,8 +1486,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -1502,8 +1502,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1510,8 +1510,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1518,8 +1518,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1527,7 +1527,7 @@
             ]
         },
         restaurant: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -1539,11 +1539,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Reservation request",
@@ -1555,8 +1555,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -1571,8 +1571,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1579,8 +1579,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1587,8 +1587,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1596,7 +1596,7 @@
             ]
         },
         automobile: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -1608,11 +1608,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Schedule an appointment",
@@ -1624,8 +1624,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -1640,8 +1640,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1648,8 +1648,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1656,8 +1656,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1665,7 +1665,7 @@
             ]
         },
         banque: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -1677,11 +1677,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Schedule an appointment",
@@ -1694,8 +1694,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -1710,8 +1710,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1718,8 +1718,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1726,8 +1726,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1735,7 +1735,7 @@
             ]
         },
         immobilier: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -1747,11 +1747,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Buy / Sell",
@@ -1764,8 +1764,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -1780,8 +1780,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1788,8 +1788,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1796,8 +1796,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1805,7 +1805,7 @@
             ]
         },
         sante: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -1817,11 +1817,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Schedule an appointment",
@@ -1833,8 +1833,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -1849,8 +1849,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1857,8 +1857,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1865,8 +1865,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1874,7 +1874,7 @@
             ]
         },
         hotel: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -1886,11 +1886,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Reservation request",
@@ -1902,8 +1902,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -1918,8 +1918,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1926,8 +1926,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1934,8 +1934,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1943,7 +1943,7 @@
             ]
         },
         secteurpublic: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -1955,11 +1955,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Questions about products and services",
@@ -1971,8 +1971,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -1987,8 +1987,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -1995,8 +1995,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2003,8 +2003,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2012,7 +2012,7 @@
             ]
         },
         proximite: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -2024,11 +2024,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Price request / Order for an event",
@@ -2040,8 +2040,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -2056,8 +2056,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2064,8 +2064,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2072,8 +2072,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2083,7 +2083,7 @@
     },
     en_FR: {
         commerce: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -2095,11 +2095,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Quote request / prices",
@@ -2112,8 +2112,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -2128,8 +2128,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2136,8 +2136,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2144,8 +2144,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2153,7 +2153,7 @@
             ]
         },
         construction: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -2165,11 +2165,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Quote request",
@@ -2180,8 +2180,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -2196,8 +2196,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2204,8 +2204,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2212,8 +2212,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2221,7 +2221,7 @@
             ]
         },
         restaurant: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -2233,11 +2233,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Reservation request",
@@ -2249,8 +2249,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -2265,8 +2265,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2273,8 +2273,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2281,8 +2281,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2290,7 +2290,7 @@
             ]
         },
         automobile: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -2302,11 +2302,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Schedule an appointment",
@@ -2318,8 +2318,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -2334,8 +2334,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2342,8 +2342,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2350,8 +2350,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2359,7 +2359,7 @@
             ]
         },
         banque: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -2371,11 +2371,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Schedule an appointment",
@@ -2388,8 +2388,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -2404,8 +2404,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2412,8 +2412,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2420,8 +2420,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2429,7 +2429,7 @@
             ]
         },
         immobilier: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -2441,11 +2441,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Buy / Sell",
@@ -2458,8 +2458,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -2474,8 +2474,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2482,8 +2482,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2490,8 +2490,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2499,7 +2499,7 @@
             ]
         },
         sante: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -2511,11 +2511,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Schedule an appointment",
@@ -2527,8 +2527,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -2543,8 +2543,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2551,8 +2551,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2559,8 +2559,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2568,7 +2568,7 @@
             ]
         },
         hotel: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -2580,11 +2580,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Reservation request",
@@ -2596,8 +2596,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -2612,8 +2612,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2620,8 +2620,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2628,8 +2628,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2637,7 +2637,7 @@
             ]
         },
         secteurpublic: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -2649,11 +2649,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Questions about products and services",
@@ -2665,8 +2665,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -2681,8 +2681,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2689,8 +2689,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2697,8 +2697,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2706,7 +2706,7 @@
             ]
         },
         proximite: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -2718,11 +2718,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Price request / Order for an event",
@@ -2734,8 +2734,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -2750,8 +2750,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2758,8 +2758,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2766,8 +2766,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2777,7 +2777,7 @@
     },
     en_AU: {
         commerce: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -2789,11 +2789,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Quote request / prices",
@@ -2806,8 +2806,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -2822,8 +2822,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2830,8 +2830,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2838,8 +2838,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2847,7 +2847,7 @@
             ]
         },
         construction: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -2859,11 +2859,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Quote request",
@@ -2874,8 +2874,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -2890,8 +2890,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2898,8 +2898,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2906,8 +2906,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2915,7 +2915,7 @@
             ]
         },
         restaurant: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -2927,11 +2927,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Reservation request",
@@ -2943,8 +2943,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -2959,8 +2959,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2967,8 +2967,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2975,8 +2975,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -2984,7 +2984,7 @@
             ]
         },
         automobile: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -2996,11 +2996,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Schedule an appointment",
@@ -3012,8 +3012,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -3028,8 +3028,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3036,8 +3036,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3044,8 +3044,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3053,7 +3053,7 @@
             ]
         },
         banque: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -3065,11 +3065,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Schedule an appointment",
@@ -3082,8 +3082,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -3098,8 +3098,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3106,8 +3106,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3114,8 +3114,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3123,7 +3123,7 @@
             ]
         },
         immobilier: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -3135,11 +3135,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Buy / Sell",
@@ -3152,8 +3152,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -3168,8 +3168,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3176,8 +3176,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3184,8 +3184,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3193,7 +3193,7 @@
             ]
         },
         sante: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -3205,11 +3205,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Schedule an appointment",
@@ -3221,8 +3221,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -3237,8 +3237,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3245,8 +3245,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3253,8 +3253,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3262,7 +3262,7 @@
             ]
         },
         hotel: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -3274,11 +3274,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Reservation request",
@@ -3290,8 +3290,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -3306,8 +3306,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3314,8 +3314,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3322,8 +3322,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3331,7 +3331,7 @@
             ]
         },
         secteurpublic: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -3343,11 +3343,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Questions about products and services",
@@ -3359,8 +3359,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -3375,8 +3375,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3383,8 +3383,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3391,8 +3391,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3400,7 +3400,7 @@
             ]
         },
         proximite: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -3412,11 +3412,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Price request / Order for an event",
@@ -3428,8 +3428,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -3444,8 +3444,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3452,8 +3452,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3460,8 +3460,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3471,7 +3471,7 @@
     },
     en_AE: {
         commerce: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -3483,11 +3483,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Quote request / prices",
@@ -3500,8 +3500,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -3516,8 +3516,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3524,8 +3524,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3532,8 +3532,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3541,7 +3541,7 @@
             ]
         },
         construction: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -3553,11 +3553,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Quote request",
@@ -3568,8 +3568,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -3584,8 +3584,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3592,8 +3592,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3600,8 +3600,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3609,7 +3609,7 @@
             ]
         },
         restaurant: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -3621,11 +3621,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Reservation request",
@@ -3637,8 +3637,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -3653,8 +3653,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3661,8 +3661,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3669,8 +3669,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3678,7 +3678,7 @@
             ]
         },
         automobile: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -3690,11 +3690,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Schedule an appointment",
@@ -3706,8 +3706,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -3722,8 +3722,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3730,8 +3730,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3738,8 +3738,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3747,7 +3747,7 @@
             ]
         },
         banque: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -3759,11 +3759,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Schedule an appointment",
@@ -3776,8 +3776,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -3792,8 +3792,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3800,8 +3800,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3808,8 +3808,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3817,7 +3817,7 @@
             ]
         },
         immobilier: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -3829,11 +3829,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Buy / Sell",
@@ -3846,8 +3846,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -3862,8 +3862,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3870,8 +3870,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3878,8 +3878,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3887,7 +3887,7 @@
             ]
         },
         sante: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -3899,11 +3899,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Schedule an appointment",
@@ -3915,8 +3915,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -3931,8 +3931,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3939,8 +3939,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3947,8 +3947,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -3956,7 +3956,7 @@
             ]
         },
         hotel: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -3968,11 +3968,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Reservation request",
@@ -3984,8 +3984,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -4000,8 +4000,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4008,8 +4008,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4016,8 +4016,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4025,7 +4025,7 @@
             ]
         },
         secteurpublic: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -4037,11 +4037,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Questions about products and services",
@@ -4053,8 +4053,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -4069,8 +4069,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4077,8 +4077,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4085,8 +4085,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4094,7 +4094,7 @@
             ]
         },
         proximite: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -4106,11 +4106,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Price request / Order for an event",
@@ -4122,8 +4122,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -4138,8 +4138,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4146,8 +4146,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4154,8 +4154,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4165,7 +4165,7 @@
     },
     en_US: {
         commerce: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -4177,11 +4177,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Quote request / prices",
@@ -4194,8 +4194,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -4210,8 +4210,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4218,8 +4218,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4226,8 +4226,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4235,7 +4235,7 @@
             ]
         },
         construction: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -4247,11 +4247,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Quote request",
@@ -4262,8 +4262,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -4278,8 +4278,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4286,8 +4286,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4294,8 +4294,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4303,7 +4303,7 @@
             ]
         },
         restaurant: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -4315,11 +4315,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Reservation request",
@@ -4331,8 +4331,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -4347,8 +4347,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4355,8 +4355,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4363,8 +4363,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4372,7 +4372,7 @@
             ]
         },
         automobile: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -4384,11 +4384,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Schedule an appointment",
@@ -4400,8 +4400,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -4416,8 +4416,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4424,8 +4424,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4432,8 +4432,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4441,7 +4441,7 @@
             ]
         },
         banque: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -4453,11 +4453,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Schedule an appointment",
@@ -4470,8 +4470,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -4486,8 +4486,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4494,8 +4494,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4502,8 +4502,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4511,7 +4511,7 @@
             ]
         },
         immobilier: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -4523,11 +4523,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Buy / Sell",
@@ -4540,8 +4540,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -4556,8 +4556,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4564,8 +4564,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4572,8 +4572,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4581,7 +4581,7 @@
             ]
         },
         sante: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -4593,11 +4593,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Schedule an appointment",
@@ -4609,8 +4609,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -4625,8 +4625,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4633,8 +4633,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4641,8 +4641,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4650,7 +4650,7 @@
             ]
         },
         hotel: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -4662,11 +4662,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Reservation request",
@@ -4678,8 +4678,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -4694,8 +4694,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4702,8 +4702,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4710,8 +4710,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4719,7 +4719,7 @@
             ]
         },
         secteurpublic: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -4731,11 +4731,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Questions about products and services",
@@ -4747,8 +4747,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -4763,8 +4763,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4771,8 +4771,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4779,8 +4779,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4788,7 +4788,7 @@
             ]
         },
         proximite: {
-            "name": "",
+            "name": "Contact form",
             "buttonText": "Send",
             "action": "Send",
             "successMessage": null,
@@ -4800,11 +4800,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Request",
-                    "description": "",
+                    "description": "Choose an option",
                     "placeholder": "",
                     "options": [
                         "Price request / Order for an event",
@@ -4816,8 +4816,8 @@
                 }, {
                     "type": "textarea",
                     "label": "Details",
-                    "description": "",
-                    "placeholder": "Specify your request",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -4832,8 +4832,8 @@
                 }, {
                     "type": "name",
                     "label": "Name",
-                    "description": "",
-                    "placeholder": "Your full name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4840,8 +4840,8 @@
                 }, {
                     "type": "tel",
                     "label": "Phone",
-                    "description": "",
-                    "placeholder": "Your phone number",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4848,8 +4848,8 @@
                 }, {
                     "type": "email",
                     "label": "Email",
-                    "description": "",
-                    "placeholder": "Your email address",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4859,7 +4859,7 @@
     },
     es_FR: {
         commerce: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -4871,11 +4871,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Solicitud de presupuesto / precios",
@@ -4887,9 +4887,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -4903,25 +4903,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4929,7 +4929,7 @@
             ]
         },
         construction: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -4941,11 +4941,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Solicitud de presupuesto",
@@ -4955,9 +4955,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -4971,25 +4971,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -4997,7 +4997,7 @@
             ]
         },
         restaurant: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -5009,11 +5009,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Solicitud de reserva",
@@ -5024,9 +5024,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -5040,25 +5040,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -5066,7 +5066,7 @@
             ]
         },
         automobile: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -5078,11 +5078,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Programe una cita",
@@ -5093,9 +5093,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -5109,25 +5109,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -5135,7 +5135,7 @@
             ]
         },
         banque: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -5147,11 +5147,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Programe una cita",
@@ -5163,9 +5163,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -5179,25 +5179,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -5205,7 +5205,7 @@
             ]
         },
         immobilier: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -5217,11 +5217,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Compra / Venta",
@@ -5233,9 +5233,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -5249,25 +5249,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -5275,7 +5275,7 @@
             ]
         },
         sante: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -5287,11 +5287,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Programe una cita",
@@ -5302,9 +5302,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -5318,25 +5318,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -5344,7 +5344,7 @@
             ]
         },
         hotel: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -5356,11 +5356,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Solicitud de reserva",
@@ -5371,9 +5371,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -5387,25 +5387,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -5413,7 +5413,7 @@
             ]
         },
         secteurpublic: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -5425,11 +5425,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Preguntas sobre productos y servicios",
@@ -5440,9 +5440,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -5456,25 +5456,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -5482,7 +5482,7 @@
             ]
         },
         proximite: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -5494,11 +5494,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Solicitud de precio / orden para un evento",
@@ -5509,9 +5509,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -5525,25 +5525,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -5553,7 +5553,7 @@
     },
     es_CA: {
         commerce: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -5565,11 +5565,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Solicitud de presupuesto / precios",
@@ -5581,9 +5581,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -5597,25 +5597,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -5623,7 +5623,7 @@
             ]
         },
         construction: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -5635,11 +5635,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Solicitud de presupuesto",
@@ -5649,9 +5649,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -5665,25 +5665,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -5691,7 +5691,7 @@
             ]
         },
         restaurant: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -5703,11 +5703,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Solicitud de reserva",
@@ -5718,9 +5718,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -5734,25 +5734,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -5760,7 +5760,7 @@
             ]
         },
         automobile: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -5772,11 +5772,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Programe una cita",
@@ -5787,9 +5787,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -5803,25 +5803,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -5829,7 +5829,7 @@
             ]
         },
         banque: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -5841,11 +5841,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Programe una cita",
@@ -5857,9 +5857,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -5873,25 +5873,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -5899,7 +5899,7 @@
             ]
         },
         immobilier: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -5911,11 +5911,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Compra / Venta",
@@ -5927,9 +5927,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -5943,25 +5943,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -5969,7 +5969,7 @@
             ]
         },
         sante: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -5981,11 +5981,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Programe una cita",
@@ -5996,9 +5996,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -6012,25 +6012,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -6038,7 +6038,7 @@
             ]
         },
         hotel: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -6050,11 +6050,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Solicitud de reserva",
@@ -6065,9 +6065,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -6081,25 +6081,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -6107,7 +6107,7 @@
             ]
         },
         secteurpublic: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -6119,11 +6119,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Preguntas sobre productos y servicios",
@@ -6134,9 +6134,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -6150,25 +6150,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -6176,7 +6176,7 @@
             ]
         },
         proximite: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -6188,11 +6188,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Solicitud de precio / orden para un evento",
@@ -6203,9 +6203,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -6219,25 +6219,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -6247,7 +6247,7 @@
     },
     es_US: {
         commerce: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -6259,11 +6259,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Solicitud de presupuesto / precios",
@@ -6275,9 +6275,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -6291,25 +6291,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -6317,7 +6317,7 @@
             ]
         },
         construction: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -6329,11 +6329,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Solicitud de presupuesto",
@@ -6343,9 +6343,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -6359,25 +6359,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -6385,7 +6385,7 @@
             ]
         },
         restaurant: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -6397,11 +6397,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Solicitud de reserva",
@@ -6412,9 +6412,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -6428,25 +6428,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -6454,7 +6454,7 @@
             ]
         },
         automobile: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -6466,11 +6466,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Programe una cita",
@@ -6481,9 +6481,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -6497,25 +6497,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -6523,7 +6523,7 @@
             ]
         },
         banque: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -6535,11 +6535,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Programe una cita",
@@ -6551,9 +6551,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -6567,25 +6567,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -6593,7 +6593,7 @@
             ]
         },
         immobilier: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -6605,11 +6605,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Compra / Venta",
@@ -6621,9 +6621,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -6637,25 +6637,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -6663,7 +6663,7 @@
             ]
         },
         sante: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -6675,11 +6675,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Programe una cita",
@@ -6690,9 +6690,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -6706,25 +6706,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -6732,7 +6732,7 @@
             ]
         },
         hotel: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -6744,11 +6744,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Solicitud de reserva",
@@ -6759,9 +6759,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -6775,25 +6775,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -6801,7 +6801,7 @@
             ]
         },
         secteurpublic: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -6813,11 +6813,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Preguntas sobre productos y servicios",
@@ -6828,9 +6828,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -6844,25 +6844,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
@@ -6870,7 +6870,7 @@
             ]
         },
         proximite: {
-            "name": "",
+            "name": "Forma de contacto",
             "buttonText": "Enviar",
             "action": "Send",
             "successMessage": null,
@@ -6882,11 +6882,11 @@
                     "placeholder": "",
                     "options": [],
                     "mandatory": false,
-                    "underline": false
+                    "underline": true
                 }, {
                     "type": "select",
                     "label": "Solicitud",
-                    "description": "",
+                    "description": "Elija una opción",
                     "placeholder": "",
                     "options": [
                         "Solicitud de precio / orden para un evento",
@@ -6897,9 +6897,9 @@
                     "underline": false
                 }, {
                     "type": "textarea",
-                    "label": "Detalles",
-                    "description": "",
-                    "placeholder": "especifique su solicitud",
+                    "label": "Details",
+                    "description": "Specify your request",
+                    "placeholder": "Hello, ...",
                     "options": [],
                     "mandatory": false,
                     "underline": false
@@ -6913,25 +6913,25 @@
                     "underline": true
                 }, {
                     "type": "name",
-                    "label": "Nombre",
-                    "description": "",
-                    "placeholder": "Su nombre completo",
+                    "label": "Name",
+                    "description": "Please enter your full name",
+                    "placeholder": "John Doe",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "tel",
-                    "label": "Teléfono",
-                    "description": "",
-                    "placeholder": "Su número de teléfono",
+                    "label": "Phone",
+                    "description": "Please enter your phone number",
+                    "placeholder": "##########",
                     "options": [],
                     "mandatory": true,
                     "underline": false
                 }, {
                     "type": "email",
-                    "label": "Correo electrónico",
-                    "description": "",
-                    "placeholder": "Su correo electrónico",
+                    "label": "Email",
+                    "description": "Please enter your email address",
+                    "placeholder": "<EMAIL>",
                     "options": [],
                     "mandatory": true,
                     "underline": false
