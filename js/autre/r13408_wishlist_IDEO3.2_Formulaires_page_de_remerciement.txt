Revision: r13408
Date: 2024-10-29 15:08:07 +0300 (tlt 29 Okt 2024) 
Author: frahajanirina 

## Commit message
wishlist IDEO3.2: Formulaires : page de remerciement

## Files changed

## Full metadata
------------------------------------------------------------------------
r13408 | frahajanirina | 2024-10-29 15:08:07 +0300 (tlt 29 Okt 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/Form.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/formOptions.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Models/NewsletterOption.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Templates/newsletterOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Views/NewsletterOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/i18n.js

wishlist IDEO3.2: Formulaires : page de remerciement
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/Form.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/Form.js	(révision 13407)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/Form.js	(révision 13408)
@@ -1,4 +1,4 @@
-define(["underscore", "JEditor/Commons/Ancestors/Models/Model", "./Field", "./Recipient"], function(_, Model, Field, Recipient) {
+define(["underscore", "JEditor/Commons/Ancestors/Models/Model", "./Field", "./Recipient", "JEditor/Commons/Links/Models/Link"], function(_, Model, Field, Recipient, Link) {
     var Form = Model.extend({
         defaults: function() {
             var ret = {
@@ -14,7 +14,9 @@
                     "email": "[[contact_email]]"
                 }],
                 useCaptcha: "google",
-                sendMailToInternaute: false
+                sendMailToInternaute: false,
+                link : null,
+                renderPageThankYou: false
             };
             return ret;
         },
@@ -92,6 +94,9 @@
                 recipient.index = index;
                 return recipient;
             });
+            if (!(this.link instanceof Link)) {
+                this.link = new Link(this.link||{});
+            }
         },
         snapshot: function() {
             this.savedState = this.toJSON();
@@ -248,7 +253,7 @@
             });
             return ret;
         }
-    }).setAttributes(["name", "columns", "buttonText", "useCaptcha","sendMailToInternaute","action", "recipients", "successMessage"]);
+    }).setAttributes(["name", "columns", "buttonText", "useCaptcha","sendMailToInternaute","action", "recipients", "successMessage", "link", "renderPageThankYou"]);
     Form.MAX_COLS_COUNT = 2;
     return Form;
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/formOptions.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/formOptions.html	(révision 13407)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/formOptions.html	(révision 13408)
@@ -19,10 +19,13 @@
             <label ><%= __("sendButtonText")%></label>
             <input type="text" class="field-input" name="buttonText" value="<%= form.buttonText %>" />
         </div>
-        <div class="form-row">
+        <div class="form-row success-message">
             <label for="successmessage"><%= __("successMessage")%></label>
             <textarea id="successmessage" class="field-input" name="successMessage"><%=form.successMessage%></textarea>
         </div>
+        <div class="thank-you-page">
+
+        </div>
         <!--  Mail internaute (Fonctionnalité à revoir)
         <div class="form-row mail-reception <%(form.sendMailToInternaute)?'checked':'' %>">
             <span class="checkbox-wrap" style="background-color: inherit;">
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormOptionView.js	(révision 13407)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormOptionView.js	(révision 13408)
@@ -2,11 +2,17 @@
     "JEditor/Commons/Ancestors/Views/View",
     "./FormBuilderView",
     "text!JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/formOptions.html",
-    "i18n!../nls/i18n"
-], function(View, FormBuilderView,formOptions, translate) {
+    "i18n!../nls/i18n",
+    "JEditor/Commons/Links/Views/LinkView",
+    "collection!JEditor/Commons/Pages/Models/PageCollection",
+    "JEditor/Commons/Links/Models/LinkType"
+], function(View, FormBuilderView,formOptions, translate, LinkView, PageCollection, LinkType) {
     var FormOptionView = View.extend({
         events: {
-            "click .mail-reception": "onMailReceptionClick"
+            "click .mail-reception": "onMailReceptionClick",
+            'radiopanelchange': '_onClickRenderThankYouPage',
+            "click .rm-thank-you-page": "_onClickRemoveThankYouPage",
+            "click .add-thank-you-page": "_onClickAddThankYouPage",
         },
         
         className: 'panel-content form-panel form scroll-container',
@@ -60,8 +66,46 @@
                 this.setStatusCheckBox("checked")
             else 
                  this.setStatusCheckBox("unchecked")
+            this._checkThankYouPage();
 
             return this;
+        },
+        _checkThankYouPage : function (){
+            this._linkSelectorView = new LinkView({
+                model: this.model.link,
+                pageCollection: PageCollection.getInstance()
+            });
+            this.thankYouPage = $('.thank-you-page');
+            this.thankYouPage.append(this._linkSelectorView.render({
+                model: this.model.link
+            }).el);
+            $('.thank-you-page .panel-radio').filter('[data-value="'+LinkType.EXTERNAL+'"],[data-value="'+LinkType.FILE+'"], [data-value="'+LinkType.NONE+'"], [data-value="'+LinkType.IMAGE+'"], [data-value="'+LinkType.NEWS+'"]').hide();
+            $('.thank-you-page .panel-radio').filter('[data-value="'+LinkType.PAGE+'"]').find('.radio-label').contents().filter(function() {
+                return this.nodeType === 3;
+            }).replaceWith(translate("renderPageThankYou"));        
+
+            if (this.model.renderPageThankYou) {
+                $('.thank-you-page .panel-radio').filter('[data-value="'+LinkType.PAGE+'"]').removeClass('add-thank-you-page').addClass('selected').addClass('rm-thank-you-page');
+                $('.success-message').hide();
+            } else {
+                $('.thank-you-page .panel-radio').filter('[data-value="'+LinkType.PAGE+'"]').removeClass('rm-thank-you-page').removeClass('selected').addClass('add-thank-you-page');
+                $('.success-message').show();
+            }
+        },
+        _onClickRenderThankYouPage: function() {
+            this.model.renderPageThankYou = !this.model.renderPageThankYou;
+
+            this.render();
+        },
+        _onClickRemoveThankYouPage: function() {
+            this.model.renderPageThankYou = false;
+
+            this.render();
+        },
+        _onClickAddThankYouPage: function() {
+            this.model.renderPageThankYou = true;
+            
+            this.render();
         }
     });
     return FormOptionView;
Index: src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Models/NewsletterOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Models/NewsletterOption.js	(révision 13407)
+++ src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Models/NewsletterOption.js	(révision 13408)
@@ -3,8 +3,9 @@
     "JEditor/Commons/Events",
     "JEditor/PagePanel/Contents/Options/Models/AbstractOption",
     "JEditor/PagePanel/Contents/Zones/Models/ZoneDependency",
-    "i18n!../nls/i18n"
-], function(_, Events, AbstractOption,ZoneDependency, translate) {
+    "i18n!../nls/i18n",
+    "JEditor/Commons/Links/Models/Link"
+], function(_, Events, AbstractOption,ZoneDependency, translate, Link) {
     /**
      * Les Options des blocs NewsletterOption
      * @class NewsletterOption
@@ -14,14 +15,17 @@
              * @lends NewsletterOption
              */
                     {
-                        defaults: {optionType: 'NewsletterOption', priority: 70, askNames:true ,buttonText: null,successMessage: null,useCaptcha: "google"},
+                        defaults: {optionType: 'NewsletterOption', priority: 70, askNames:true ,buttonText: null,successMessage: null,useCaptcha: "google", link : null, renderPageThankYou: false},
                         initialize: function() {
                             this._super();
                             var zone = this.getZone();
+                            if (!(this.link instanceof Link)) {
+                                this.link = new Link(this.link||{});
+                            }
                         },
 
                     });
-            NewsletterOption.SetAttributes(['askNames','buttonText','successMessage','useCaptcha']);
+            NewsletterOption.SetAttributes(['askNames','buttonText','successMessage','useCaptcha', 'link', 'renderPageThankYou']);
             return NewsletterOption;
         });
 
Index: src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Templates/newsletterOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Templates/newsletterOption.html	(révision 13407)
+++ src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Templates/newsletterOption.html	(révision 13408)
@@ -37,13 +37,16 @@
                 </label>
             <input type="text" class="field-input" name="buttonText" value="<%= buttonText %>" />
         </div>
-        <div class="form-row">
+        <div class="form-row success-message">
             <label for="successMessage">
                 <%= __("confirmationMessage")%> 
                 </label>
             <textarea id="successMessage" class="field-input" name="successMessage"><%= successMessage %></textarea>
         </div>
+        <div class="thank-you-page">
 
+        </div>
+
         <header>
             <div class="panel-content-intro ">
                 <h3 class="panel-content-title address option-name"><span class="icon-mail"></span>
Index: src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Views/NewsletterOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Views/NewsletterOptionView.js	(révision 13407)
+++ src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Views/NewsletterOptionView.js	(révision 13408)
@@ -12,8 +12,11 @@
     "underscore",
     "text!../Templates/newsletterOption.html",
     "JEditor/PagePanel/Contents/Options/Views/AbstractOptionView",
-    "i18n!../nls/i18n"
-], function($, _, newsletterOptionTemplate, AbstractOptionView, translate) {
+    "i18n!../nls/i18n",
+    "JEditor/Commons/Links/Views/LinkView",
+    "collection!JEditor/Commons/Pages/Models/PageCollection",
+    "JEditor/Commons/Links/Models/LinkType"
+], function($, _, newsletterOptionTemplate, AbstractOptionView, translate, LinkView, PageCollection, LinkType) {
 
     /**
      * Vue des Options du bloc newsletter
@@ -30,7 +33,10 @@
                         className: "panel-content button-panel ",
                         events: {
 
-                            'change input[type="checkbox"].ask-names': '_onChangeAskNames', 
+                            'change input[type="checkbox"].ask-names': '_onChangeAskNames',
+                            'radiopanelchange': '_onClickRenderThankYouPage',
+                            "click .rm-thank-you-page": "_onClickRemoveThankYouPage",
+                            "click .add-thank-you-page": "_onClickAddThankYouPage", 
                         },
                         /**
                          * initialise l'objet
@@ -63,8 +69,45 @@
                             var templateVars = {newsletterOption: this.model.attributes, buttonText : buttonText, successMessage : successMessage};
                             this.$el.html(this.template(templateVars));
                             // this.$el.append(this._linkSelectorView.render({model:this.model.link}).el);
+                            this._checkThankYouPage();
                             this.scrollables();
                             return this;
+                        },
+                        _checkThankYouPage : function (){
+                            this._linkSelectorView = new LinkView({
+                                model: this.model.link,
+                                pageCollection: PageCollection.getInstance()
+                            });
+                            this.thankYouPage = $('.thank-you-page');
+                            this.thankYouPage.append(this._linkSelectorView.render({
+                                model: this.model.link
+                            }).el);
+                            $('.thank-you-page .panel-radio').filter('[data-value="'+LinkType.EXTERNAL+'"],[data-value="'+LinkType.FILE+'"], [data-value="'+LinkType.NONE+'"], [data-value="'+LinkType.IMAGE+'"], [data-value="'+LinkType.NEWS+'"]').hide();
+                            $('.thank-you-page .panel-radio').filter('[data-value="'+LinkType.PAGE+'"]').find('.radio-label').contents().filter(function() {
+                                return this.nodeType === 3;
+                            }).replaceWith(translate("renderPageThankYou"));
+                            if (this.model.renderPageThankYou) {
+                                $('.thank-you-page .panel-radio').filter('[data-value="'+LinkType.PAGE+'"]').removeClass('add-thank-you-page').addClass('selected').addClass('rm-thank-you-page');
+                                $('.success-message').hide();
+                            } else {
+                                $('.thank-you-page .panel-radio').filter('[data-value="'+LinkType.PAGE+'"]').removeClass('rm-thank-you-page').removeClass('selected').addClass('add-thank-you-page');
+                                $('.success-message').show();
+                            }
+                        },
+                        _onClickRenderThankYouPage: function() {
+                            this.model.renderPageThankYou = !this.model.renderPageThankYou;
+                
+                            this.render();
+                        },
+                        _onClickRemoveThankYouPage: function() {
+                            this.model.renderPageThankYou = false;
+                
+                            this.render();
+                        },
+                        _onClickAddThankYouPage: function() {
+                            this.model.renderPageThankYou = true;
+                            
+                            this.render();
                         }
                     });
 
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js	(révision 13407)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js	(révision 13408)
@@ -89,4 +89,5 @@
     "numberMax" : "Maximum",
     "min"  :"Min",
     "max" : "Max",
+    "renderPageThankYou": "Renvoyer vers une page de remerciement"
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js	(révision 13407)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js	(révision 13408)
@@ -88,5 +88,6 @@
     "numberMin" : "Minimum",
     "numberMax" : "Maximum",
     "min"  :"Min",
-    "max" : "Max"
+    "max" : "Max",
+    "renderPageThankYou": "Renvoyer vers une page de remerciement"
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js	(révision 13407)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js	(révision 13408)
@@ -95,6 +95,7 @@
         "numberMax" : "Maximum",
         "min"  :"Min",
         "max" : "Max",
+        "renderPageThankYou": "Link to a thank you page"
     },
     "fr-fr":true,
     "fr-ca": true
Index: src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/fr-ca/i18n.js	(révision 13407)
+++ src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/fr-ca/i18n.js	(révision 13408)
@@ -15,4 +15,5 @@
     "askNames" : "Demander le nom et le prénom",
     "send":"Envoyer",
     "successMessage":"Merci, votre message nous a été transmis avec succès",
+    "renderPageThankYou": "Renvoyer vers une page de remerciement"
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/fr-fr/i18n.js	(révision 13407)
+++ src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/fr-fr/i18n.js	(révision 13408)
@@ -15,4 +15,5 @@
     "askNames" : "Demander le nom et le prénom",
     "send":"Envoyer",
     "successMessage":"Merci, votre message nous a été transmis avec succès",
+    "renderPageThankYou": "Renvoyer vers une page de remerciement"
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/i18n.js	(révision 13407)
+++ src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/i18n.js	(révision 13408)
@@ -17,5 +17,6 @@
         "askNames" : "Ask for first and last name",
         "send" : "Send",
         "successMessage" : "Your message has been sent successfully",
+        "renderPageThankYou": "Link to a thank you page"
 
     }, "fr-fr": true, "fr-ca": true});
\ No newline at end of file
