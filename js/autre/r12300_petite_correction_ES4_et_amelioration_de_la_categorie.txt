Revision: r12300
Date: 2024-05-16 10:38:25 +0300 (lkm 16 Mey 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
petite correction ES4 et amelioration de la categorie

## Files changed

## Full metadata
------------------------------------------------------------------------
r12300 | sraz<PERSON><PERSON><PERSON>oa | 2024-05-16 10:38:25 +0300 (lkm 16 Mey 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/Categorie.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/CategorieCollection.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/CategorieLang.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/CategorieAddView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/NewsEditorView.js

petite correction ES4 et amelioration de la categorie
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Models/Categorie.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Categorie.js	(révision 12299)
+++ src/js/JEditor/NewsPanel/Models/Categorie.js	(révision 12300)
@@ -6,7 +6,7 @@
 ],function(_,
         Events,
         Model,
-        FileCollection,
+        FileCollection
 ){ var Categorie = Model.extend(
                 {
                     
Index: src/js/JEditor/NewsPanel/Models/CategorieCollection.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/CategorieCollection.js	(révision 12299)
+++ src/js/JEditor/NewsPanel/Models/CategorieCollection.js	(révision 12300)
@@ -15,13 +15,16 @@
                         /**
                          * @constructs
                          */
-                        // constructor: function(models, options) {
-                        //     if (options && options.lang)
-                        //         this.lang = options.lang;
-                        //     Collection.call(this, models, options);
-                        // },
-                        initialize: function () {
-                            Collection.prototype.initialize.apply(this, arguments);
+                        constructor: function() {
+                            if (arguments.callee.caller !== CategorieCollection.getInstance)
+                                throw new TypeError("Impossible d'instancier un Panneau, veuillez utiliser la méthode statique MaClass.getInstance()");
+                            else {
+                                Collection.apply(this, arguments);
+                                if (!this.fetched) {
+                                    this.fetch();
+                                    this.fetched = true;
+                                }
+                            }
                         },
                         onSync: function (coll,resp) {
                            console.log(resp);
@@ -52,8 +55,7 @@
                                     // Utiliser la logique de regroupement personnalisée
                                     var groupKey = this.determineGroup(lang, existingLanguages);
                                    
-                                    
-                                    groupKey.forEach(element => {
+                                    groupKey.forEach( function(element) {
                                         if (!groups[element]) {
                                             groups[element] = [];
                                         }
@@ -85,5 +87,12 @@
                          */
                         model: Categorie,
                     });
+                    CategorieCollection.instance = null;
+                    CategorieCollection.getInstance = function() {
+                        if (this.instance === null)
+                            this.instance = new CategorieCollection();
+                        return this.instance;
+
+                    };
             return CategorieCollection;
         });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Models/CategorieLang.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/CategorieLang.js	(révision 12299)
+++ src/js/JEditor/NewsPanel/Models/CategorieLang.js	(révision 12300)
@@ -1,6 +1,6 @@
 define(["JEditor/Commons/Ancestors/Models/Model"
 ],function(Model){ 
-    var Categorie = Model.extend(
+    var CategorieLang = Model.extend(
     {
         
         defaults: {
@@ -8,9 +8,9 @@
             description: "",                
             metaTitle:"", 
             metaDescription: "",
-            urlCategory:"",
+            url:""
         },
     });
-    Categorie.SetAttributes(['title','description', 'metaTitle', 'metaDescription', "urlCategory"]);
-    return Categorie;
+    CategorieLang.SetAttributes(['title','description', 'metaTitle', 'metaDescription', "url"]);
+    return CategorieLang;
 });
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12299)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 12300)
@@ -11,9 +11,11 @@
     "./Views/NewsEditorView",
     "./Views/GlobalConfigView",
     "./Models/CategorieCollection",
-   // "./Models/ArticlesCollection",
+    "./Models/ArticlesCollection",
+    "./Models/Article",
     "JEditor/App/Views/RightPanelView",
-
+    "JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView",
+    "JEditor/PagePanel/Contents/Blocks/Blocks",
     "i18n!./nls/i18n"
 ],
         function (
@@ -29,7 +31,11 @@
             NewsEditorView,
             GlobalConfigView,
             CategorieCollection, 
+            ArticlesCollection,
+            Article,
             RightPanelView,
+            AvailableView,
+            Blocks,
             translate
             ) {
             var NewsPanel = PanelView.extend(
@@ -39,7 +45,9 @@
                             {
                                 events : {
                                     'click .addCategory':'onAddCategorieClick',
-                                    'click .addArticle':'onAddArticleClick'
+                                    'click .addArticle':'onAddArticleClick',
+                                    'click #available-blocks-trigger' : 'showAvailableBlocks',
+                                    'click #show-zone-version': 'showZoneVersionsPanel',
                                 },
                                 cssFile: __IDEO_INTEGRATION_PATH__ + "/build/css/news_panel.css",
 
@@ -55,25 +63,22 @@
                                     this._template = this.buildTemplate(NewsPanelTemplate, translate);
                                     this.rightPanelView = new RightPanelView();
                                     this._byLang = {};
-                                    this.categories =  new CategorieCollection();
-                                    this.categories.fetch();
+                                    this.categories = CategorieCollection.getInstance();
+                                    this.articles =  ArticlesCollection.getInstance();
                                 },
                                 load: function () {
-                                    var loaded = 0;
-                                    var languagesCount = this.languages.length;
-                                  
 
-                                    this._byLang = this.categories.groupBy('lang');
-
-                                    this.currentList = this._byLang[this.currentLang.id];
-
                                     this.on(Events.NewsPanelEvents.CATEGORIE_CHANGE, this._onCategorieChange); 
+                                    this.on(Events.NewsPanelEvents.ARTICLE_CHANGE, this._onArticleChange); 
 
                                     this.childViews.articlesList = new ArticlesCollectionView({
                                         collection : this.articles,
-                                        language : this.currentLang,
+                                        language : this.currentLang
                                     });
 
+                                    this.childViews.availableBlocksView = new AvailableView({
+                                        pagePanel : this
+                                    });
                                     this.childViews.newsEditorView = new NewsEditorView({
                                         title : 'tout les articles',
                                         usepreview : false,
@@ -106,6 +111,61 @@
                                 /**
                                  * met à jour la vue de page (zone du centre de l'écran)
                                  */
+                                _onArticleChange: function() {
+
+                                    this.dom.body.animate({
+                                        scrollTop : 0
+                                    }, 300);
+                                    var callback = _.bind(function() {
+                                        this.renderRightPanel();
+                                        // this.switchToArticle();
+                                            var windowHeight = this.dom.window.height();
+                                            var offset = this.$('#news-editor').offset().top - this.dom.window.scrollTop();
+                                            
+                                            this.$("#news-editor").css('minHeight', (windowHeight - offset) + 'px');
+                                           if (this.currentArticle) { 
+                                            this.childViews.newsEditorView = new NewsEditorView({
+                                                title : 'Article',
+                                                model : this.currentArticle,
+                                                languages : this.languages,
+                                                currentLang : this.currentLang,
+                                                categorieCollection :  this.categories
+                                            });
+                                            this.$("#news-editor").append(this.childViews.newsEditorView.el);
+                                            this.childViews.newsEditorView.renderArticlePage();
+                                            var url = 'news/' + this.currentLang.id + '/article/' + this.currentArticle.id ;
+                                            this.app.params.lastUrl = url;
+                                            this.app.params.save();
+                                            this.app.router.navigate(url);
+                                            }
+                                            else {
+                                            this.childViews.newsEditorView = new NewsEditorView({
+                                                title : 'tout les articles',
+                                                languages : this.languages,
+                                                currentLang : this.currentLang,
+                                                categorieCollection :  this.categories
+                                            });
+                                            this.$("#news-editor").append(this.childViews.newsEditorView.el);
+                                            this.childViews.newsEditorView.renderArticleList();
+                                        }
+                                        if (this.childViews.newsEditorView.articlesListView)
+                                            this.listenTo(this.childViews.newsEditorView.articlesListView, Events.ChoiceEvents.SELECT, this._onArticleSelect);
+                                    }, this);
+                                    
+                                    if (this.childViews.newsEditorView) {
+                                        this.listenToOnce(this.childViews.newsEditorView, Events.ViewEvents.HIDE, function() {
+                                            this.childViews.newsEditorView.remove();
+                                            callback();
+                                        });
+                                        this.childViews.newsEditorView.hide();
+                                    
+                                    } else
+                                        callback();
+                                },
+                            
+                                /**
+                                 * met à jour la vue de page (zone du centre de l'écran)
+                                 */
                                 _onCategorieChange : function() {
 
                                     this.dom.body.animate({
@@ -113,24 +173,22 @@
                                     }, 300);
                                     var callback = _.bind(function() {
                                         this.renderRightPanel();
-                                       
                                             var windowHeight = this.dom.window.height();
                                             var offset = this.$('#news-editor').offset().top - this.dom.window.scrollTop();
                                             
                                             this.$("#news-editor").css('minHeight', (windowHeight - offset) + 'px');
                                             
-                                           // this.childViews.newsEditorView.load();
                                            if (this.currentCategorie) { 
                                                 this.childViews.newsEditorView = new NewsEditorView({
-                                                    title : this.currentCategorie.lang[this.currentLang.id].title,
+                                                    title : (this.currentCategorie.lang[this.currentLang.id])?this.currentCategorie.lang[this.currentLang.id].title: 'Categorie',
                                                     model : this.currentCategorie,
                                                     languages : this.languages,
                                                     currentLang : this.currentLang,
-                                                    categorieCollection :  this.categories,
+                                                    categorieCollection :  this.categories
                                                 });
                                                 this.$("#news-editor").append(this.childViews.newsEditorView.el);
                                                 this.childViews.newsEditorView.renderCategorieView();
-                                                var url = 'news/' + this.currentLang.id + '/' + this.currentCategorie.id ;
+                                                var url = 'news/' + this.currentLang.id + '/category/' + this.currentCategorie.id ;
                                                 this.app.params.lastUrl = url;
                                                 this.app.params.save();
                                                 this.app.router.navigate(url);
@@ -140,12 +198,13 @@
                                                     title : 'tout les articles',
                                                     languages : this.languages,
                                                     currentLang : this.currentLang,
-                                                    categorieCollection :  this.categories,
+                                                    categorieCollection :  this.categories
                                                 });
                                                 this.$("#news-editor").append(this.childViews.newsEditorView.el);
                                                 this.childViews.newsEditorView.renderArticleList();
                                             }
-                                            
+                                            if (this.childViews.newsEditorView.articlesListView)
+                                                this.listenTo(this.childViews.newsEditorView.articlesListView, Events.ChoiceEvents.SELECT, this._onArticleSelect);
                                     }, this);
                                     if (this.childViews.newsEditorView) {
                                         this.listenToOnce(this.childViews.newsEditorView, Events.ViewEvents.HIDE, function() {
@@ -204,13 +263,13 @@
                                 */
                                 renderRightPanel : function(content, renderChild) {
                                     // setup args
-                                    var content     = content || this.childViews.globalConfigView;
+                                    var content     = content || this.childViews.availableBlocksView;
                                     var renderChild = renderChild || true;
                                     
                                     this.rightPanelView.clear();
                                     this.rightPanelView.setElement(this.dom.newsPanel.rightSidebar);
-                                    if (!this.currentCategorie) {
-                                        this.rightPanelView.addContent(content);
+                                    if (this.currentArticle) {
+                                        this.rightPanelView.addContent(content);      
                                         if (renderChild && renderChild !== 'noRender') {
                                             content.render();
                                         }
@@ -258,23 +317,12 @@
                                 checkAffixTop:function(){
                                     return (document.documentElement.clientHeight < 866 ? 90 : 230);
                                 },
-                                /**
-                                 * rendu de la liste des articles
-                                 */
-                                _renderArticlesList: function() {
-                                    this.$("#news-editor #content-editor").append(this.childViews.articlesList.render().el);
-                                },
-                                /**
-                                 * crée le rendu de la page courante
-                                 */
-                                _renderNewsEditor : function() {
-                                    this.$("#news-editor").append(this.childViews.newsEditorView.el);
-                                    this.childViews.newsEditorView.renderArticleList();
-                                    // this.childViews.newsEditorView.renderArticlePage();
-                                },
                                 onAddCategorieClick : function() {
                                     this.childViews.newsEditorView.renderAddCategorie()
                                 },
+                                onAddArticleClick : function() {
+                                   this.childViews.newsEditorView.renderAddArticle()
+                                },
                                 /**
                                  * ajoute les variables à l'objet this.dom
                                 */
@@ -292,9 +340,11 @@
                                         noneSelected: true,
                                         canAddNews:true
                                     }));
+                                   this.$("#news-editor").append(this.childViews.newsEditorView.el);
                                    this._renderCategorieList();
                                    this.setDOM();
                                    this.renderRightPanel();
+                                 
                                     return this;
                                 },
                                 /**
@@ -312,6 +362,7 @@
                                     if (this.loaded) {
                                         this.childViews.langDropDown.current = this.currentLang;
                                         this.currentCategorie = null;
+                                        this.currentArticle = null;
                                         this.childViews.categorieList.lang = lang.id;
                                         this.listenTo(this.childViews.categorieList, Events.ViewEvents.HIDE, function() {
                                             this._renderCategorieList();
@@ -321,6 +372,44 @@
                                         this.childViews.categorieList.hide();
                                     }
                                 },
+                                _onArticleSelect : function(view, article) {
+                                    if (this.currentCategorie && this.currentCategorie.hasUnsavedChanges()) {
+                                        this.confirmUnsaved({
+                                            message : translate("quitWithoutSaving"),
+                                            title : translate("unsavedChanges"),
+                                            type : 'delete-not-saved',
+                                            onYes : _.bind(function() {
+                                                this.currentCategorie.save();
+                                                this.currentArticle = article;
+                                            }, this),
+                                            onNo : _.bind(function() {
+                                                this.currentCategorie.cancel();
+                                                this.currentArticle = article;
+                                            }, this),
+                                            options : {
+                                                dialogClass : 'delete no-close',
+                                                dontAskAgain : true
+                                            }
+                                        });
+                                    } else if ((this.currentArticle && this.currentArticle.hasUnsavedChanges()) || 
+                                    (this.currentZone && this.currentZone.hasUnsavedChanges())) {
+                                        this.confirmUnsaved({
+                                            message : translate("quitWithoutSaving"),
+                                            title : translate("unsavedChanges"),
+                                            type : 'delete-not-saved',
+                                            onYes : _.bind(function() {
+                                                this.currentArticle.save();
+                                                this.currentZone.save()
+                                                this.currentArticle = article;
+                                            }, this),
+                                            options : {
+                                                dialogClass : 'delete no-close',
+                                                dontAskAgain : true
+                                            }
+                                        }); 
+                                    } else
+                                     this.currentArticle = article;
+                                },
                                 _onCategorieSelect : function(view, categorie) {
                                     if (this.currentCategorie && this.currentCategorie.hasUnsavedChanges()) {
                                         this.confirmUnsaved({
@@ -344,6 +433,27 @@
                                     } else
                                         this.currentCategorie = categorie;
                                 },
+                                /**
+                                 * Montre le panneaux de blocs disponibles
+                                 *
+                                 * @argument {JEditor.PagesPanel.Contents.Columns.Column|undefined}
+                                 *           column Au clic, Si une colonne est sélectionnée (referer),
+                                 *           le bloc disponible est ajouté en bas de celle-ci, sinon, on
+                                 *           crée une nouvelle section rien que pour lui
+                                */
+                                showAvailableBlocks : function(column) {
+                                    this.renderRightPanel();
+                                    this.rightPanelView.showContent(this.childViews.availableBlocksView);
+                                    this.rightPanelView.showPanel();
+                                    return false;
+                                },
+                                /**
+                                * Cache le panneau de blocs disponibles
+                                */
+                                hideAvailableBlocks : function() {
+                                    this.rightPanelView.hideContent(this.childViews.availableBlocksView);
+                                    this.rightPanelView.hidePanel();
+                                },
                             });
                             Object.defineProperties(NewsPanel.prototype,
                                 {
@@ -358,7 +468,7 @@
                                         },
                                         set : function(currentList) {
                                             this._currentList = currentList;
-                                            this.trigger(Events.PagePanelEvents.CATEGORIE_LIST_CHANGE, this, currentList, undefined);
+                                            this.trigger(Events.NewsPanelEvents.CATEGORIE_LIST_CHANGE, this, currentList, undefined);
                                         }
                                     },
                                     /**
@@ -376,6 +486,40 @@
                                             }
                                         }
                                     },
+                                     /**
+                                     * La liste de Article
+                                     *
+                                     * @type{ArticleCollection}
+                                     */
+                                     currentArticlesList : {
+                                        get : function() {
+                                            return this._currentListArticle;
+                                        },
+                                        set : function(currentListArticle) {
+                                            this._currentListArticle = currentListArticle;
+                                            this.trigger(Events.NewsPanelEvents.ARTICLE_LIST_CHANGE, this, currentListArticle, undefined);
+                                        }
+                                    },
+                                    /**
+                                     * Article courante
+                                     *@type{Article}
+                                     */
+                                    currentArticle : {
+                                        get : function() {
+                                            return this._currentArticle;
+                                        },
+                                        set : function(currentArticle) {
+                                            if (currentArticle !== this._currentArticle) {
+                                                this._currentArticle = currentArticle;
+                                                this.trigger(Events.NewsPanelEvents.ARTICLE_CHANGE, this, currentArticle, undefined);
+                                            }
+                                        }
+                                    },
+                                    currentZone : {
+                                        get : function() {
+                                            return this.childViews.newsEditorView.currentZone;
+                                        }
+                                    }
                                 });
                                 Events.extend(
                                     /**
@@ -390,7 +534,15 @@
                                             /**
                                              * Attend un callback avec (model, options)
                                              */
-                                            CATEGORIE_LIST_CHANGE : 'change:currentList'
+                                            CATEGORIE_LIST_CHANGE : 'change:currentList',
+                                            /**
+                                             * Attend un callback avec (model, options)
+                                             */
+                                            ARTICLE_CHANGE : 'change:currentArticle',
+                                            /**
+                                             * Attend un callback avec (model, options)
+                                             */
+                                            ARTICLE_LIST_CHANGE : 'change:currentListArticle'
                                         }
                                     });
                     return NewsPanel;
Index: src/js/JEditor/NewsPanel/Views/CategorieAddView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(révision 12299)
+++ src/js/JEditor/NewsPanel/Views/CategorieAddView.js	(révision 12300)
@@ -104,19 +104,14 @@
             this.$el.html(this.template(params));
             this.$('.side-bar__lang').append(this.langDropDown.render().el);
             
-            // uploader image
-            const uploadParams = {
+            this.fileUploader = new FileUploaderView({
                 customStockEvent: '_parsestock_image',
                 acceptedTypes: ['image'],
                 acceptedExtensions: ['jpeg', 'jpg', 'png', 'svg', 'webp'],
                 refusedExtensions: ['bmp'],
                 uploadUrl: __IDEO_UPLOADLOGO_URL__ ? __IDEO_UPLOADLOGO_URL__ : '/admin/resource-logo',
-            };
-
-            this.fileUploader = new FileUploaderView({
                 currentFile : this.model.getFile(),
-                collection: this.fileCollection,
-                uploadParams,
+                collection: this.fileCollection
             });
            
             this.listenTo(this.fileUploader, Events.FileUploaderEvents.UPLOAD, this._onUpload);
@@ -211,9 +206,6 @@
             e.stopImmediatePropagation();
             
             if (this._checkInput()) {
-                // // initialisation de la valeur meta du model
-                // this.currentCategorieLang.setMeta();
-
                 this.model.lang[this.lang.id] = this.currentCategorieLang;
                 this.model.save();
 
@@ -221,7 +213,7 @@
                 this.collection.trigger('change');
 
                 this.onSave();
-                this.trigger(Events.CategorieAddEvents.CATEGORY_ADD, this);
+                this.trigger(Events.CategorieAddEvents.CATEGORY_ADD, this.model);
             }
             return ;
         },
Index: src/js/JEditor/NewsPanel/Views/NewsEditorView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 12299)
+++ src/js/JEditor/NewsPanel/Views/NewsEditorView.js	(révision 12300)
@@ -8,11 +8,15 @@
     "JEditor/Commons/Ancestors/Views/LoaderView",
     "collection!JEditor/Commons/Languages/Models/ContentLanguageList",
     "JEditor/NewsPanel/Models/ArticlesCollection",
+    "JEditor/NewsPanel/Models/CategorieCollection",
     "JEditor/NewsPanel/Views/ArticlesCollectionView",
     "JEditor/NewsPanel/Views/CategorieAddView",
+    "JEditor/NewsPanel/Views/AddArticleView",
     "JEditor/NewsPanel/Views/ConfigCategorieView",
     "JEditor/PagePanel/Contents/Zones/Views/ZoneToolBox",
     "JEditor/PagePanel/Views/PagePreview",
+    "JEditor/NewsPanel/Views/ArticleEditorView",
+    
     "i18n!../nls/i18n",
     //not in params
    // "jqueryPlugins/dropdown",
@@ -26,11 +30,15 @@
      LoaderView, 
      ContentLanguageList, 
      ArticlesCollection, 
+     CategorieCollection,
      ArticlesCollectionView, 
      CategorieAddView,
+     AddArticleView,
      ConfigCategorieView, 
+
      ZoneToolBox,
      PagePreview,
+     ArticleEditorView,
      translate
      ) {
    
@@ -43,7 +51,7 @@
         },
         _loaded: 0,
         attributes: {
-            id: "page-view"
+            id: "news-view"
         },
         fadeOutEffect: "fadeOut",
         fadeInEffect: "fadeIn",
@@ -53,15 +61,17 @@
             this._template = this.buildTemplate(template, translate);
             this.pagePreview = new PagePreview();
             this.currentLang = this.options.currentLang;
-
+            this.languageList = this.options.languages,
+            this.articles = ArticlesCollection.getInstance();
+            this.categories = CategorieCollection.getInstance();
         },
         renderArticleList :function (){
             this.articlesListView = new ArticlesCollectionView({
                 collection : this.articles,
-                language : this.currentLang,
+                language : this.currentLang
             });
             this.render();
-            this.$("#content-editor").html(this.articlesListView.render().el);
+            this.articleView.html(this.articlesListView.render().el);
         },
 
         renderAddCategorie :function (){
@@ -70,11 +80,14 @@
                 language : this.options.currentLang,
                 collection: this.options.categorieCollection
             });
-            this.listenTo(this.addCategorieView, Events.CategorieAddEvents.CATEGORY_ADD, this.renderCategorieView);
+            self = this;
+            this.listenTo(this.addCategorieView, Events.CategorieAddEvents.CATEGORY_ADD, _.bind(function(categorie) {
+                self.newsPanel.currentCategorie = categorie;
+            }));
             this.options.title = 'Categorie';
             this.options.usepreview = true;
             this.render();
-            this.$("#content-editor").html(this.addCategorieView.render().el);
+            this.categoryView.html(this.addCategorieView.render().el);
          },
 
          renderCategorieView :function (){
@@ -83,16 +96,17 @@
                 languageList : this.options.languages,
                 language : this.options.currentLang,
                 categorie : this.model,
-                collection: this.options.categorieCollection
+                collection: this.categories
             });
             this.render();
-            this.$("#content-editor").prepend(this.addCategorieView.render().el);
-             // this.articles = ArticlesCollection.getInstance();
+            this.categoryView.prepend(this.addCategorieView.render().el);
+            
              this.articlesListView = new ArticlesCollectionView({
                 collection : this.articles,
                 language : this.currentLang,
+                category : this.model
             });
-            this.$("#content-editor").append(this.articlesListView.render().el);
+            this.categoryView.append(this.articlesListView.render().el);
             self = this
             this.listenTo(this.addCategorieView, Events.CategorieAddEvents.LANG_CHANGED, _.bind(function(lang) {
                 self.currentLang = lang;
@@ -101,26 +115,20 @@
          },
          renderArticlePage: function (){
             this.options.usepreview = true;
+            this.articleEditorView = new ArticleEditorView({
+                languageList : this.languageList,
+                language : this.currentLang,
+                categorieCollection :  this.categories,
+                newsPanel : this,
+                model : this.model,
+                zoneID : this.model.content
+            });
+            this.articleEditorView.load();
             this.render();
-            
-            if (this.zoneToolbox && true) {
-                this.$('.zone').prepend(this.zoneToolbox.el);
-                this.zoneToolbox.render();
-            }
-            if (this.sectionCollectionView) {
-                this.sectionCollectionView.setElement(this.$('#content-editor'));
-                this.sectionCollectionView.render();
-            }
+            this.articleView.append(this.articleEditorView.render().el);
+            this.$('.zone-selector>.dropdown-toggle, .page-action').dropdown();
+
          },
-         remove: function () {
-            if (this.zoneToolbox)
-                this.zoneToolbox.remove();
-            if (this.pagePreview)
-                this.pagePreview.remove();
-            if (this.sectionCollectionView)
-                this.sectionCollectionView.remove();
-            BabblerView.prototype.remove.apply(this, arguments);
-        },
         render: function() {
             this.undelegateEvents();
             this.$el.empty();
@@ -129,8 +137,9 @@
                  title : this.options.title,
                  usepreview : this.options.usepreview || false
             }));
+            this.categoryView = this.$("#category-editor");
+            this.articleView = this.$("#article-editor");
             this.$(".message").hide();
-         //   this.$('.zone-selector>.dropdown-toggle, .page-action').dropdown();
             this.delegateEvents();
 
             return this;
@@ -164,8 +173,8 @@
                 );
                
                 rightPanelView.showContent(configCategorieView);
-                this.listenToOnce(configCategorieView, Events.ConfigCategorieVIewEvents.SAVE, _.bind(onClose, this))
-                    .listenToOnce(configCategorieView, Events.ConfigCategorieVIewEvents.CANCEL, _.bind(onClose, this));
+                this.listenToOnce(configCategorieView, Events.ConfigCategorieViewEvents.SAVE, _.bind(onClose, this))
+                    .listenToOnce(configCategorieView, Events.ConfigCategorieViewEvents.CANCEL, _.bind(onClose, this));
               
             } catch (e) {
                 console.error(e);
