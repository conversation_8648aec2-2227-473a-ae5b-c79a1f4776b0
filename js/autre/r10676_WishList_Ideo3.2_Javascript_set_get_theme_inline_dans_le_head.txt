Revision: r10676
Date: 2023-03-17 13:36:39 +0300 (zom 17 Mar 2023) 
Author: norajaonarivelo 

## Commit message
WishList Ideo3.2: Javascript set/get theme inline dans le head

## Files changed

## Full metadata
------------------------------------------------------------------------
r10676 | norajaonarivelo | 2023-03-17 13:36:39 +0300 (zom 17 Mar 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/dev/#librairies/common_config/const.inc.php
   A /branches/ideo3_v2/dev/#librairies/public/theme
   A /branches/ideo3_v2/dev/#librairies/public/theme/js
   A /branches/ideo3_v2/dev/#librairies/public/theme/js/theme.js
   M /branches/ideo3_v2/dev/#librairies/vendor/CoreConfigurationService/config/module.config.php
   M /branches/ideo3_v2/dev/#librairies/vendor/CoreConfigurationService/src/CoreConfigurationService/Controller/IndexController.php
   M /branches/ideo3_v2/dev/#librairies/vendor/CoreMoteurDeRendu/src/CoreMoteurDeRendu/Core/AmbianceEngineRenderer.php
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/ParamsPanel.js
   A /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Templates/ThemeSite.html
   A /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/ThemeSiteView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/i18n.js

WishList Ideo3.2: Javascript set/get theme inline dans le head
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Templates/ThemeSite.html
===================================================================
--- src/js/JEditor/ParamsPanel/Templates/ThemeSite.html	(nonexistent)
+++ src/js/JEditor/ParamsPanel/Templates/ThemeSite.html	(révision 10676)
@@ -0,0 +1,39 @@
+<div class="main-content__wrapper  presentationvideo-settings">
+    <!--
+        ******************************************
+        créer nouvelle childView Donnée structuree,
+        y déplacer ce contenu
+        ******************************************
+        -->
+        <div class="ideo-title">
+            <h1>
+                <%=__('ThemeTitle') %> 
+                <span><%=__('Themedescription') %></span>
+            </h1>
+        </div>
+    
+        <div class="inline-params  thin-border  radius  shadow">
+            <span class="inline-params__name">
+                <%=__('ThemeLabel') %> 
+            </span>
+            <label>
+                    <!-- JsonLdActive : on , null => Standard format utilisé,
+                                        off => Personnalisé
+                                        disabled => Désactivé
+                    -->
+                <span class="custom-input">
+                   <select id="ChoseThemeId" name="JsonLdActive" class="neutral-input  bold">
+                       <option value="light" <%=(WebSiteTheme==='light') ? 'selected' : '' %>><%=__('lightLabel')%></option>
+                       <option value="dark" <%=(WebSiteTheme==='dark') ? 'selected' : '' %>><%=__('darkLabel')%></option>
+                   </select>
+                </span>
+            </label>
+        </div>
+       
+        
+        <!-- 
+        ******************
+        end new childView
+        ******************
+        -->
+</div>
\ No newline at end of file
Index: src/js/JEditor/ParamsPanel/Views/ThemeSiteView.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/ThemeSiteView.js	(nonexistent)
+++ src/js/JEditor/ParamsPanel/Views/ThemeSiteView.js	(révision 10676)
@@ -0,0 +1,41 @@
+define([
+    'jquery',
+    "underscore",
+    'JEditor/Commons/Ancestors/Views/View', 
+    'JEditor/Commons/Events',
+     'text!../Templates/ThemeSite.html',
+     'i18n!../nls/i18n'
+    ], function ($, _, View, Events, template,translate) {
+    var ThemeSiteView = View.extend({
+        events: {
+            'change #ChoseThemeId': 'onThemeChange'
+        },
+        initialize: function () {
+            this._super();
+            this._template = this.buildTemplate(template,translate);
+             this.listenTo(this.model, Events.BackboneEvents.CHANGE, this.onChange);
+        },
+        onChange: function (model) {
+            var changes=model.changedAttributes();
+            for (var key in changes) {
+                var value=changes[key];
+                var container = this.$("input.field-input[name=\"" + key + "\"]").parents(".inline-params");
+                var action = value ? "removeClass" : "addClass";
+                container[action]("disabled");
+            }
+        },
+        render: function () {
+            console.log(this.model.toJSON());
+            this.undelegateEvents();
+            this.$el.html(this._template(this.model.toJSON()));
+            this.delegateEvents();
+            return this;
+        },
+        onThemeChange : function() {
+            var value = this.$('#ChoseThemeId').val() ; 
+            this.model.attributes['WebSiteTheme'] = value;
+            this.model.save();
+        },
+    });
+    return ThemeSiteView;
+});
\ No newline at end of file
Index: src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 10675)
+++ src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 10676)
@@ -84,5 +84,10 @@
         "collection": "Collection",
         "outline": "Outline(défaut)",
         "solid": "Solid",
-        "duotone": "Duotone"
+        "duotone": "Duotone",
+        "ThemeTitle" : "Choix du thème",
+        "Themedescription" :    "Choisissez s'il faut applique le thème sombre (Dark Mode) par défaut sur le site",
+        "ThemeLabel":   "Thème",
+        "lightLabel":   "Claire (défault)",
+        "darkLabel" :   "Sombre"
     });
Index: src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 10675)
+++ src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 10676)
@@ -88,5 +88,10 @@
         "collection": "Collection",
         "outline": "Outline(défaut)",
         "solid": "Solid",
-        "duotone": "Duotone"
+        "duotone": "Duotone",
+        "ThemeTitle" : "Choix du thème",
+        "Themedescription" :    "Choisissez s'il faut applique le thème sombre (Dark Mode) par défaut sur le site",
+        "ThemeLabel":   "Thème",
+        "lightLabel":   "Claire (défault)",
+        "darkLabel" :   "Sombre"
     });
Index: src/js/JEditor/ParamsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 10675)
+++ src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 10676)
@@ -96,7 +96,12 @@
         "collection": "Collection",
         "outline": "Outline(default)",
         "solid": "Solid",
-        "duotone": "Duotone"
+        "duotone": "Duotone",
+        "ThemeTitle" : "Choice of theme",
+        "Themedescription" :    "Choose whether to apply the default Dark Mode theme to the site",
+        "ThemeLabel":   "Theme",
+        "lightLabel":   "Light (default)",
+        "darkLabel" :   "Darl"
     },
     "fr-fr": true,
     "fr-ca": true
Index: src/js/JEditor/ParamsPanel/ParamsPanel.js
===================================================================
--- src/js/JEditor/ParamsPanel/ParamsPanel.js	(révision 10675)
+++ src/js/JEditor/ParamsPanel/ParamsPanel.js	(révision 10676)
@@ -12,11 +12,12 @@
     "./Views/MarqueClientView",
     "./Views/FontsGoogleView",
     "./Views/IconsCollectionView",
+    "./Views/ThemeSiteView",
     "./Models/Params",
     //hidden
     "jqueryui/datepicker"
 ],
-        function ($, _, PanelView, Events, ParamsPanelTemplate, translate, SocialNetworks,SEOView, MobileStoreView, PolitiqueConfidentialiteView,DonneeStructureeView, MarqueClientView, FontsGoogleView, IconsCollectionView, Params) {
+        function ($, _, PanelView, Events, ParamsPanelTemplate, translate, SocialNetworks,SEOView, MobileStoreView, PolitiqueConfidentialiteView,DonneeStructureeView, MarqueClientView, FontsGoogleView, IconsCollectionView,ThemeSiteView,Params) {
             var SocialPanel = PanelView.extend(
                     /**
                      * @lends JEditor.Panels.MessagePanel.prototype
@@ -87,6 +88,11 @@
                                             object: new PolitiqueConfidentialiteView({model:this.params}),
                                             icon:"icon-link",
                                             title:translate("PolitiqueConfidentialite")
+                                        },
+                                        themeSite:{
+                                            object: new ThemeSiteView({model:this.params}),
+                                            icon:'',
+                                            title:translate("ThemeTitle")
                                         }
                                     };
                                     if(this.app.user.can('view_jsonLd')) {
