Revision: r10190
Date: 2023-01-20 19:30:45 +0300 (zom 20 Jan 2023) 
Author: anthony 

## Commit message
fix nom obligatoire dans formulaire

## Files changed

## Full metadata
------------------------------------------------------------------------
r10190 | anthony | 2023-01-20 19:30:45 +0300 (zom 20 Jan 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/FieldBuilder.html

fix nom obligatoire dans formulaire
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/FieldBuilder.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/FieldBuilder.html	(révision 10189)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/FieldBuilder.html	(révision 10190)
@@ -38,7 +38,7 @@
         </div>
 
         <div class="checkbox mandatory">
-            <input id="mandatory-<%=field.cid%>" name="mandatory" class="field-input" <%=field.mandatory?'checked="checked"':''%> type="checkbox" value="1" <%=field.type==='name'?'disabled':''%> />
+            <input id="mandatory-<%=field.cid%>" name="mandatory" class="field-input" <%=field.mandatory?'checked="checked"':''%> type="checkbox" value="1" />
             <label class="checkbox-lbl" for="mandatory-<%=field.cid%>">
                 <span class="switch">
                     <span></span>
