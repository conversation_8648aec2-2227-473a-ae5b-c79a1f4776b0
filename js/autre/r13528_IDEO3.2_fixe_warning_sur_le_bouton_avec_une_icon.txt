Revision: r13528
Date: 2024-11-27 10:42:27 +0300 (lrb 27 Nov 2024) 
Author: frahajanirina 

## Commit message
IDEO3.2:fixe warning sur le bouton avec une icon

## Files changed

## Full metadata
------------------------------------------------------------------------
r13528 | frahajanirina | 2024-11-27 10:42:27 +0300 (lrb 27 Nov 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonBlock.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js

IDEO3.2:fixe warning sur le bouton avec une icon
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonBlock.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonBlock.html	(révision 13527)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonBlock.html	(révision 13528)
@@ -5,7 +5,7 @@
     </span>
     <span class="txt blk-button__label"><span><%=text%></span></span>
   </a>
-  <span class="txt-warning">
+  <span class="txt-warning <%=(isDifferentText && (textOnMobile == null || textOnMobile == '')) ? '' : 'hidden'%>">
     <span class="icon-warning"></span> <%= __("btnTextOnMobileWarning")%>
   </span>
 </div>
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js	(révision 13527)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js	(révision 13528)
@@ -25,30 +25,16 @@
                             this._super();
                             this._contentTemplate = this.buildTemplate(template, translate);
                             this.buttonOption = this.model.options.ButtonOption;
-                            this.listenTo(this.buttonOption, "radio:change", this.onChangeRadio); 
+                            this.listenTo(this.buttonOption, "radio:change", this.render); 
                             this.listenTo(this.buttonOption, "input:change", this.onInputChange);
                             this.delegateEvents();                   
                         },
-                        onChangeRadio: function() {
-                            var isDifferentText, textOnMobile, txtWarning;
-
-                            isDifferentText = this.model.options.ButtonOption.isDifferentText;
-                            textOnMobile = this.model.options.ButtonOption.textOnMobile;
-                            txtWarning = this.$('.txt-warning');
-
-                            if (isDifferentText && (textOnMobile == null || textOnMobile == '')) {
-                                txtWarning.show();
-                                
-                            } else {
-                                txtWarning.hide();
-                            }
-                        },
                         onInputChange: function(length){
                             var txtWarning = this.$('.txt-warning');
                             if (length === 0) {
-                                txtWarning.show();
+                                txtWarning.removeClass('hidden');
                             } else {
-                                txtWarning.hide();
+                                txtWarning.addClass('hidden');
                             }
                         },
                         _onLoad: function() {
@@ -64,6 +50,8 @@
                             var alignButton = this.model.options.ButtonStyleOption.buttonAlignment;
                             var alignText = this.model.options.ButtonStyleOption.textAlignment;
                             var color = this.model.options.ButtonStyleOption.color;
+                            var isDifferentText = this.model.options.ButtonOption.isDifferentText;
+                            var textOnMobile = this.model.options.ButtonOption.textOnMobile;
 
                             this.$(".blk-button__label").css('margin-top','');
                             this.$(".blk-button__icon").empty();
@@ -75,12 +63,12 @@
                                     } else {
                                         iconContent = svg.content;
                                         this.$(".blk-button__label").css('margin-top','-15px');
-                                        this.$('.content').append(this._contentTemplate({icon: iconContent,size: sizeButton, text: textButton, buttonAlignment:alignButton, textAlignment:alignText, color :color}));
+                                        this.$('.content').append(this._contentTemplate({icon: iconContent,size: sizeButton, text: textButton, buttonAlignment:alignButton, textAlignment:alignText, color :color, isDifferentText: isDifferentText, textOnMobile: textOnMobile}));
                                          return this;
                                     }
                                 },this));
                             }else{
-                                this.$('.content').append(this._contentTemplate({icon: iconContent,size: sizeButton, text: textButton, buttonAlignment:alignButton, textAlignment:alignText, color :color}));
+                                this.$('.content').append(this._contentTemplate({icon: iconContent,size: sizeButton, text: textButton, buttonAlignment:alignButton, textAlignment:alignText, color :color, isDifferentText: isDifferentText, textOnMobile: textOnMobile}));
                                 return this;
                             }
                             
@@ -109,13 +97,6 @@
                             this.$(".block-button").attr("class", "block-button " + sizeButton + " " + alignButton + " " + alignText + " " + color);
                             var textButton = this.model.options.ButtonOption.text?this.model.options.ButtonOption.text:translate('button');
                             this.$(".txt span").text(textButton);
-                            var isDifferentText = this.model.options.ButtonOption.isDifferentText;
-                            var textOnMobile = this.model.options.ButtonOption.textOnMobile;
-                            if (isDifferentText && (textOnMobile == null || textOnMobile == '')) {
-                                this.$('.txt-warning').show();
-                            } else {
-                                this.$('.txt-warning').hide();
-                            }
                             return this;
                         },
                         edit: function() {
