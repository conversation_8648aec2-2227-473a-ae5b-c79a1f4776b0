Revision: r9996
Date: 2022-12-14 13:53:03 +0300 (lrb 14 Des 2022) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2 : upload video partie JS

## Files changed

## Full metadata
------------------------------------------------------------------------
r9996 | sraz<PERSON><PERSON><PERSON>oa | 2022-12-14 13:53:03 +0300 (lrb 14 Des 2022) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Models/File.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileDetail.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileDetailView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileListView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/i18n.js

wishlist IDEO3.2 : upload video partie JS
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Files/Models/File.js
===================================================================
--- src/js/JEditor/Commons/Files/Models/File.js	(révision 9995)
+++ src/js/JEditor/Commons/Files/Models/File.js	(révision 9996)
@@ -48,6 +48,16 @@
                                         return true;
                                     return false;
                                 },
+                                 /**
+                                 * 
+                                 * @returns {Boolean}
+                                 */
+                                  isVideo: function() {
+                                    var regexpImg = /^video/;
+                                    if (regexpImg.test(this.mimeType))
+                                        return true;
+                                    return false;
+                                },
                                 /**
                                  * 
                                  * @returns {Boolean}
Index: src/js/JEditor/FilePanel/Templates/fileDetail.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileDetail.html	(révision 9995)
+++ src/js/JEditor/FilePanel/Templates/fileDetail.html	(révision 9996)
@@ -12,6 +12,17 @@
             <%if(!isImg){%>
             <span class="image <%=previewClass%>">
                 <div class="property">
+                    <%if(isVideo){%>
+                        <span class="actions informations">
+                            <span class="icon-video"></span>
+                            <div class="infobulles">
+                                <p class="title"><%= __("Infos")%> :</p>
+                                <p>
+                                    <%= __("imageVideo")%>
+                                </p>
+                            </div>
+                        </span>
+                    <%}%>
                     <span class="actions informations">
                         <span class="icon-information"></span>
                         <div class="infobulles">
@@ -101,6 +112,8 @@
             <% if(user.can('display_file_url')){ %>
                 <%if(isImg){%>
                     <div class="shortcode">ressources/images/<%=name%></div>
+                <% }if(isVideo){%>
+                    <div class="shortcode">ressources/videos/<%=name%></div>
                 <% }else {%>
                     <div class="shortcode">ressources/fichiers/<%=name%></div>
                 <%}%>
Index: src/js/JEditor/FilePanel/Views/FileDetailView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileDetailView.js	(révision 9995)
+++ src/js/JEditor/FilePanel/Views/FileDetailView.js	(révision 9996)
@@ -52,7 +52,7 @@
 
                     this._super();
                     var currentLang = this.options.languages.get(this.lang);
-                    var params = {originalFileUrl: '', lang: currentLang, languages: this.options.languages.models, previous: this.previous, next: this.next, isFavicon: this.model.isFavicon(), isImg: this.model.isImg(), previewClass: this.model.previewClass()};
+                    var params = {originalFileUrl: '', lang: currentLang, languages: this.options.languages.models, previous: this.previous, next: this.next, isFavicon: this.model.isFavicon(), isImg: this.model.isImg(), isVideo: this.model.isVideo(), previewClass: this.model.previewClass()};
                     if (this.model.originalFile) {
                         var originalFile = this.options.fileList.get(this.model.originalFile);
                         params.originalFileUrl = originalFile.fileUrl;
Index: src/js/JEditor/FilePanel/Views/FileListView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileListView.js	(révision 9995)
+++ src/js/JEditor/FilePanel/Views/FileListView.js	(révision 9996)
@@ -134,7 +134,7 @@
                 this.$("li.action.delete").replaceWith('<li class="" style="visibility: hidden;"></li>');
             }
             this.dom[this.cid].uploadZone = this.$('.group-content');
-            this.dom[this.cid].uploadZone.uploader({showMenu: false, maxFiles: -1, lang: translate.translations});
+            this.dom[this.cid].uploadZone.uploader({showMenu: false, maxFiles: -1, lang: translate.translations, acceptedTypes: ['image', 'audio', 'video/mp4']});
             this.dom[this.cid].showUploader = this.$('input.upload.button');
             return this;
         },
Index: src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 9995)
+++ src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 9996)
@@ -121,6 +121,7 @@
     "openLink":"Url custom",
     "sendToPage":"Url vers une page du site",
     "selectPage":"Sélectionnez une page",
-    "introUrl" : "Ajouter une url utilisable dans une galerie (data-url)"
+    "introUrl" : "Ajouter une url utilisable dans une galerie (data-url)",
+    "imageVideo": "C'est un fichier vidéo",
     
 });
\ No newline at end of file
Index: src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 9995)
+++ src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 9996)
@@ -125,5 +125,6 @@
     "selectPage":"Sélectionnez une page",
     "lockimage":"Cette image est vérrouillée. Elle est utilisé dans un block image ou une collection. Pour supprimer définitivement l'image, commencez par retirer l'image de la page ou de la collection.",
     "locked" : "Verrouilée",
-    "introUrl" : "Ajouter une url utilisable dans une galerie (data-url)"
+    "introUrl" : "Ajouter une url utilisable dans une galerie (data-url)",
+    "imageVideo": "C'est un fichier vidéo",
 });
\ No newline at end of file
Index: src/js/JEditor/FilePanel/nls/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/i18n.js	(révision 9995)
+++ src/js/JEditor/FilePanel/nls/i18n.js	(révision 9996)
@@ -127,7 +127,8 @@
         "selectPage":"Select a page",
         "lockimage" : "This image is locked. It is used in an image block or a collection. To permanently delete the image, first remove the image from the page or collection.",
         "locked" : "Locked",
-        "introUrl" : "Add a url usable in a gallery (data-url)"
+        "introUrl" : "Add a url usable in a gallery (data-url)",
+        "imageVideo": "C'est un fichier vidéo",
     },
     "fr-fr": true, "fr-ca":true
 })
\ No newline at end of file
