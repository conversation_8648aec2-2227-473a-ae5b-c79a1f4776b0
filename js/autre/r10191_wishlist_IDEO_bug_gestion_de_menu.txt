Revision: r10191
Date: 2023-01-23 09:58:30 +0300 (lts 23 Jan 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO :bug gestion de menu 

## Files changed

## Full metadata
------------------------------------------------------------------------
r10191 | sraz<PERSON><PERSON><PERSON>oa | 2023-01-23 09:58:30 +0300 (lts 23 Jan 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Menus/Models/MenuItem.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NavigationPanel/Views/MenuItemView.js

wishlist IDEO :bug gestion de menu 
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Menus/Models/MenuItem.js
===================================================================
--- src/js/JEditor/Commons/Menus/Models/MenuItem.js	(révision 10190)
+++ src/js/JEditor/Commons/Menus/Models/MenuItem.js	(révision 10191)
@@ -189,7 +189,7 @@
         }
         for (var i = 0; i < menuItems.length; i++) {
           var item = menuItems[i].cid ? menuItems[i] : this.contents[this.byCid[menuItems[i]]];
-          var index = this.byCid[item.cid];
+          var index = this.contents.indexOf(item);
           this.contents.splice(index, 1);
         }
         this.refreshContent();
Index: src/js/JEditor/NavigationPanel/Views/MenuItemView.js
===================================================================
--- src/js/JEditor/NavigationPanel/Views/MenuItemView.js	(révision 10190)
+++ src/js/JEditor/NavigationPanel/Views/MenuItemView.js	(révision 10191)
@@ -212,7 +212,7 @@
             this.$el.children('li').remove(".sensor");
         },
         makedropable: function (){
-            this.$el.children('li').each(function( index ) {
+            this.$el.children('li').each(function(index ) {
                 if(index==0)
                     $(this).before('<li class="sensor" data-index="'+index+'"></li>');
                 $(this).after('<li class="sensor" data-index="'+(index+1)+'"></li>');  
@@ -267,13 +267,14 @@
                                         item.position = i;
                                         item.depth = depth;
                                         item.parent = parent;
-                                        if (hierarchy[i].children){
-                                            noeud = true;
-                                            item.contents = recurse(hierarchy[i].children, collection, item);
-                                            noeud = false;
-                                        }     
-                                        else
-                                            item.contents = [];
+                                        // droppable at the  first list not in the child 
+                                        // if (hierarchy[i].children){
+                                        //     noeud = true;
+                                        //     item.contents = recurse(hierarchy[i].children, collection, item);
+                                        //     noeud = false;
+                                        // }     
+                                        // else
+                                        //     item.contents = [];
                                     
                                         collection.add(item);
                                         menuItems.push(item);
