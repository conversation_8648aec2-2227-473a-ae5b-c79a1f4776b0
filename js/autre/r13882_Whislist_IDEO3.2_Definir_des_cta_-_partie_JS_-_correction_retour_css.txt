Revision: r13882
Date: 2025-02-20 09:28:38 +0300 (lkm 20 Feb 2025) 
Author: rrakotoarinelina 

## Commit message
Whislist IDEO3.2 : Definir des cta - partie JS - correction retour css

## Files changed

## Full metadata
------------------------------------------------------------------------
r13882 | rrakotoarinelina | 2025-02-20 09:28:38 +0300 (lkm 20 Feb 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonCTAOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/listeCTA.html
   M /branches/ideo3_v2/integration/src/less/imports/button_block/styleOptions.less

Whislist IDEO3.2 : De<PERSON>ir des cta - partie JS - correction retour css
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonCTAOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonCTAOption.html	(révision 13881)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonCTAOption.html	(révision 13882)
@@ -10,16 +10,13 @@
             </span>
         </header>
         <div class="option-content">
-            <button class="page-selector btn-add-cta">
-                <span class="icon-add"></span>
-                <span class="label"><%=__("addCTA")%></span>
-            </button>
-            <br>
+            <div class="cta-action-button btn-add-cta">
+                <a><span class="icon-add"></span><%=__("addCTA")%></a>
+            </div>
             <% if (ctaNotEmpty) { %>
-            <button class="dialog-view-trigger page-selector btn-update-cta">
-                <span class="icon-refresh"></span>
-                <span class="label"><%=__("updateExistingCTA")%></span>
-            </button>
+            <div class="cta-action-button btn-update-cta">
+                <a><span class="icon-refresh"></span><%=__("updateExistingCTA")%></a>
+            </div>
             <% } %>
         </div>
     </article>
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/listeCTA.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/listeCTA.html	(révision 13881)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/listeCTA.html	(révision 13882)
@@ -10,8 +10,8 @@
 
             <span class="effect-radio <%= cta.color %> <%=(buttonShortcode && buttonShortcode==cta.shortcode)?'selected':''%>"" id="cta_<%= _id %>" data-value="<%= cta.shortcode %>">
             <span class="container">
-            <span class="cta-text"><%= cta.text %></span><br>
-            <span class=""><%= cta.href %></span>
+            <span class="cta-text"><%= cta.text %></span>
+            <span class="cta-link"><%= cta.href %></span>
             <span class="switch-container">
                 <span class="radio">
                     <span></span>
Index: src/less/imports/button_block/styleOptions.less
===================================================================
--- src/less/imports/button_block/styleOptions.less	(révision 13881)
+++ src/less/imports/button_block/styleOptions.less	(révision 13882)
@@ -244,7 +244,7 @@
     font-weight: bold;
   }
   
-  /* Dialog Styles */
+  /* updadte cta Dialog Styles */
   .ui-dialog.updateCTA {
     position: fixed !important;
   }
@@ -298,6 +298,23 @@
     display: block;
 }
 
+.cta-radio-container .effect-radio .container .cta-text {
+    display: block;
+    overflow: hidden;
+    text-overflow: ellipsis;
+    white-space: nowrap;
+    margin-bottom: 3px;
+    font-weight: 700;
+  }
+  
+  .cta-radio-container .effect-radio .container .cta-link {
+    display: block;
+    overflow: hidden;
+    text-overflow: ellipsis;
+    white-space: nowrap;
+    font-weight: normal;
+    font-size: 90%;
+  }
 
 //onglet cta 
 .cta-list-content{
@@ -306,3 +323,35 @@
 .btn-add-cta{
     margin-bottom: 10px;
 }
+
+.create-call-to-action {
+    .cta-action-button {
+        a {
+            cursor: pointer;
+            display: block;
+            height: 40px;
+            background: #444;
+            border-radius: 4px;
+            text-align: center;
+            font: 400 14px/40px Raleway, sans-serif;
+            color: #dadada;
+            overflow: hidden;
+
+            &:hover {
+                background: #34d399;
+                color: #fff;
+            }
+
+            .icon {
+                &-add,
+                &-refresh {
+                    display: inline-block;
+                    vertical-align: middle;
+                    width: 20px;
+                    height: 20px;
+                    margin-right: 10px;
+                }
+            }
+        }
+    }
+}
