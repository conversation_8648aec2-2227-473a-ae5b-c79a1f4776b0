Revision: r11533
Date: 2023-11-08 12:01:23 +0300 (lrb 08 Nov 2023) 
Author: rrakotoarinelina 

## Commit message
Sélection d'une icon pour les boutons (partie js)

## Files changed

## Full metadata
------------------------------------------------------------------------
r11533 | rrakotoarinelina | 2023-11-08 12:01:23 +0300 (lrb 08 Nov 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonStyleOption.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/Svg.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/SvgCollection.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonStyleOption.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/selectIcon.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/SelectIconView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js
   A /branches/ideo3_v2/integration/src/less/imports/blockButton.less
   M /branches/ideo3_v2/integration/src/less/main.less

Sélection d'une icon pour les boutons (partie js)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonStyleOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonStyleOption.js	(révision 11532)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonStyleOption.js	(révision 11533)
@@ -26,9 +26,9 @@
                         initialize: function() {
                             this._super();
                         },
-                        revertChanges: function() {
-                                this.value = this.lastValue;
-                        },
+                        // revertChanges: function() {
+                        //         this.value = this.lastValue;
+                        // },
                         validate: function(attributes, options) {
                             if(allowedSizes.lastIndexOf(attributes.size)<=-1){
                                 return {field: "size", message: translate("Invalid_size" )};
@@ -44,6 +44,6 @@
                             }
                         }
                     });
-            ButtonStyleOption.SetAttributes(['size', 'buttonAlignment', 'textAlignment','color']);
+            ButtonStyleOption.SetAttributes(['icon','size', 'buttonAlignment', 'textAlignment','color']);
             return ButtonStyleOption;
         });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/Svg.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/Svg.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/Svg.js	(révision 11533)
@@ -0,0 +1,33 @@
+define([
+    "JEditor/Commons/Ancestors/Models/Model",
+
+], function (Model) {
+
+    var Svg = Model.extend({
+          /**
+         * @constructs
+         */
+        // constructor: function(options) {
+            
+        //     console.log('attributes');
+        //     console.log(options);
+        //     this.collectionName = 'outline';
+        //     this.svgName = '';
+        //     // console.log(PageCollection.caller);
+        //     if (options && options.collectionName && options.svgName){
+        //         this.collectionName = options.collectionName;
+        //         this.svgName = options.svgName;
+        //     }
+        // },
+        defaults: {
+            'name': '',
+            'content': ''
+        },
+        // url: function() {
+        //     //  __IDEO_API_PATH__ + "/resources-svgcollection/" + this.name;
+        //      return __IDEO_API_PATH__ + "/resources-svgcollection/" + this.collectionName +"/"+ this.svgName
+        // },
+    });
+
+    return Svg;
+});
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/SvgCollection.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/SvgCollection.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/SvgCollection.js	(révision 11533)
@@ -0,0 +1,40 @@
+define([
+	"JEditor/Commons/Events",
+	"JEditor/Commons/Ancestors/Models/Collection",
+    "JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/Svg"
+],function(
+    Events,
+	Collection,
+	Svg
+){
+var /**
+ * 
+ * @class SvgCollection
+ * @extends Collection
+ * @property {MapPoint} model Le type de modèle à créer
+ */
+SvgCollection = Collection.extend(
+        /**
+         * @lends MapPointCollection.prototype
+         */
+                {
+                    model: Svg,
+                    constructor: function(options) {
+                        this.name = options.name || 'outline';
+                        this.svgName = options.svgName || '';
+                    },
+                    /**
+                     * initialize l'objet
+                     */
+                    initialize: function() {
+                    },
+                    url: function() {
+                        return __IDEO_API_PATH__ + "/resources-svgcollection/" + this.name + ( this.svgName != '' ?  "/" + this.svgName :'' );
+                    },
+                    parse: function(response) {
+                        return response.data; // assuming the array of files is in the 'data' property of the response
+                    }
+
+                });
+return SvgCollection;
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonStyleOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonStyleOption.html	(révision 11532)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonStyleOption.html	(révision 11533)
@@ -1,3 +1,49 @@
+ <style>
+    /* button icon  */
+    /* .selected-icon {
+  display: flex;
+  justify-content: center;
+  align-items: center;
+  font-size: 50px;
+}
+
+.selected-icon svg {
+  width: 100px;
+  height: 100px;
+} */
+</style> 
+ <div class="panel-option-container animated button-icon">
+    <article class="panel-option">
+        <header>
+            <h3 class="option-name">
+                <span class="icon-button-block_icon icon-midsize"></span>
+                <%=__("iconButton")%>
+            </h3>
+            <span class="panel-content-legend">
+                <%=__("iconButtonLegend")%>
+            </span>
+        </header>
+        <div class="option-content">
+            <% var _id=_.uniqueId('iconButton') %>
+                <input type="checkbox" class="blue-bg show-browse-icon-button" name="show-browse-icon-button" id="<%=_id %>" <%= icon !== null && icon !== "" ? 'checked="checked"' : '' %> >
+                <label for="<%=_id %>">
+                    <span class="checkbox-wrapper">
+                        <span class="icon-unchecked"></span>
+                        <span class="icon-checked"></span>
+                    </span>
+                    <span class="text"><%=__("showIconLegend")%></span>
+                </label>
+                <span class="selected-icon-wrapper"></span>
+                <div class="action-options radio-group btn-browse-icons" >
+                    <button class="dialog-view-trigger page-selector btn-browse-icons">
+                        <span class="icon-page"></span>
+                        <span class="label"><%=__("browseIconsLegend")%></span>
+                    </button>
+                </div>
+            </div>
+    </article>
+</div> 
+
 <div class="panel-option-container animated button-size">
     <article class="panel-option">
         <header>
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/selectIcon.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/selectIcon.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/selectIcon.html	(révision 11533)
@@ -0,0 +1,15 @@
+
+    <div class="option-content">
+        <div class="grid-container-icon">
+            <%for(i=0;i<content.length;i++){
+                svg = content[i];
+                %>
+                <div  class="box-icon <%=selected == svg.name ? 'selected-icon':''%>" data-name="<%= svg.name%>"  >
+                        <span class="wrapper-icon">
+                            <%= svg.content%>
+                        </span>
+                </div>
+                <%}%>
+        </div>
+    </div>
+
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js	(révision 11532)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js	(révision 11533)
@@ -1,9 +1,15 @@
 define( [
     "jquery",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/SaveCancelPanel",
     "text!../Templates/buttonStyleOption.html",
     "JEditor/PagePanel/Contents/Options/Views/AbstractOptionView",
+    "JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/SelectIconView",
+    "JEditor/ParamsPanel/Models/Params",
+    "JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/SvgCollection",
+    "JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/Svg",
     "i18n!../nls/i18n"
-], function($, buttonStyleOption, AbstractOptionView, translate) {
+], function($,Events,SaveCancelPanel, buttonStyleOption, AbstractOptionView,SelectIconView,Params, SvgCollection,Svg,translate) {
     var ButtonStyleOptionView = AbstractOptionView.extend(
             {
                 optionType: 'ButtonStyleOption',
@@ -11,7 +17,9 @@
                         className: "panel-content button-style-panel ",
                         events: {
                             'click .button-color .effect-radio': "_onChangeColor",
-                            "click input[name='buttonAlignment']:checked" : "cancelButtonAligment"
+                            "click input[name='buttonAlignment']:checked" : "cancelButtonAligment",
+                            'change input[type="checkbox"].show-browse-icon-button': '_onChangeShowIcon',
+                            'click .btn-browse-icons': "_onClickBrowseIcons",
                         },
                         /**
                          * initialise l'objet
@@ -20,6 +28,9 @@
                             this._super();
                             this.template = this.buildTemplate(buttonStyleOption, translate);
                             this.listenTo(this.model,"change:buttonAlignment",this.toggleTextAlignment);
+                            this.selectIconDialog = new SelectIconView();
+                            this.listenTo( this.selectIconDialog,Events.ButtonEvents.SELECT_ICON,this.onSelectedIcon);
+                            
                         },
                         /**
                          * affichage de l'alignement du texte
@@ -50,19 +61,89 @@
                              $target.addClass("active");
                              var value = $target.attr("data-value");
                              this.model.color=value;
-                         },  
+                         },
+                         
+                         /**
+                          * afficher ou pas le bouton parcourir les icones
+                          * 
+                          */
+
+                         _onChangeShowIcon:function(event){
+                             var input = event.target;
+                             if ( input.checked ) {
+                                $('.btn-browse-icons').show(); 
+                                this.selectedIconEl.show();
+                            } else {
+                                $('.btn-browse-icons').hide();
+                                this.selectedIconEl.hide();
+                                this.selectedIconEl.empty();
+                                this.model.icon="";
+                             }
+                        },
+
                         /**
+                         * trigger le modal de selection d'iĉone à utiliser
+                         *  
+                         */
+                        _onClickBrowseIcons:function(event){
+                            this.selectIconDialog.fetchIcons();
+                            this.selectIconDialog.open();
+                        },
+
+                        onSelectedIcon:function(event){
+                            this.selectedIconEl.empty();
+                            this.selectedIconEl.append(event.content);
+                            this.model.icon=event.name;
+                        },
+
+                        getIconContent:function(svgName){
+                            this.params = new Params();
+                            //fetch le nom du repertoire collection icon utilisé
+                            this.value = this.params.fetch();
+                            this.value.done(_.bind(function(data) {
+                                var nomRepertoire  = data.IconsCollection;
+                                var svgIconObj = new SvgCollection({"name":nomRepertoire,"svgName":svgName});
+                                svgIconObj = svgIconObj.fetch();
+                                // extraction icones
+                                svgIconObj.done(_.bind(function(data) {
+                                    this.selectedIconEl.html(data.content);
+                                    this.selectedIconEl.show();
+                                    $('.btn-browse-icons').show(); 
+                                },this)).fail(function(error) {
+                                    console.log(error);
+                                });
+                
+                            },this)).fail(function(error) {
+                                // Handle any errors that occurred during the fetch() method
+                                console.log(error);
+                            });
+                
+                        },
+
+                        /**
                          * actualise l'affichage de la vue
                          */
                         render: function() {
-                            var templateVars = {size:this.model.size, buttonAlignment:this.model.buttonAlignment, textAlignment:this.model.textAlignment, color:this.model.color};
+                            var templateVars = {icon:this.model.icon,size:this.model.size, buttonAlignment:this.model.buttonAlignment, textAlignment:this.model.textAlignment, color:this.model.color};
                             this.$el.html(this.template(templateVars));
                             this.scrollables();
+                            this.selectedIconEl = $('.selected-icon-wrapper');
+                            //cacher selected-icon au chargement
+                            this.selectedIconEl.hide();
+                            $('.btn-browse-icons').hide(); 
+                            if(this.model.icon != ""){
+                                this.getIconContent(this.model.icon);
+                            }  
                             return this;
                         }
                         
             }
     );
+    Events.extend({
+        ButtonEvents: {
+            SELECT_ICON: 'selectIcon',
+        },
+    })
 
     return ButtonStyleOptionView;
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/SelectIconView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/SelectIconView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/SelectIconView.js	(révision 11533)
@@ -0,0 +1,95 @@
+define([
+    "jquery",
+    "underscore",
+    "text!../Templates/selectIcon.html",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/DialogView",
+    "JEditor/ParamsPanel/Models/Params",
+    "JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/SvgCollection",
+    "JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView",
+    "i18n!../nls/i18n",
+    //not in params
+    "jqueryPlugins/dropdown"
+], function($, _, selectIcon, Events, DialogView,Params,SvgCollection,ButtonStyleOptionView, translate) {
+    var SelectIconView = DialogView.extend({
+        className: 'selectIcon',
+        events: {
+            'click .box-icon': 'onSelect',
+            // 'click [data-select]': '_onSelectClick',
+        },
+        currentList: [],
+        constructor: function(options) {
+            var opts = _.extend({
+                title: translate("browseIconTitle"),
+                buttons: [
+                    {
+                        text: translate("choose"),
+                        class: 'okay',
+                        click: _.bind(this.onOk, this)
+                    },
+                    {
+                        text: translate("cancel"),
+                        class: 'cancel',
+                        click: _.bind(this.onCancel, this)
+                    }
+                ],
+                width: 720,
+                height: 600
+            }, options);
+            return DialogView.call(this, opts);
+        },
+        initialize: function(options) {
+            this._super();
+            this._template = this.buildTemplate(selectIcon, translate);
+
+        },
+        fetchIcons: function(){
+            $('.selectIcon').addClass("loading");
+            this.params = new Params();
+            this.value = this.params.fetch();
+            this.value.done(_.bind(function(data) {
+                var svgCollectionObj = new SvgCollection({"name":data.IconsCollection});
+                svgCollectionObj = svgCollectionObj.fetch();
+                //extraction des icones
+                svgCollectionObj.done(_.bind(function(response) {
+                    this.svgCollection = response;
+                    currentList = this.svgCollection;
+                    this.trigger('data:fetched');
+                    $('.selectIcon').removeClass("loading");
+                },this)).fail(function(error) {
+                    console.log(error);
+                });
+
+            },this)).fail(function(error) {
+                console.log(error);
+            });
+
+            this.listenTo(this, 'data:fetched', this.render);
+        },
+        render: function() {
+            var data = (this.svgCollection === undefined ? [] : this.svgCollection);
+            this._super();
+            this.undelegateEvents();
+             this.$el.html(this._template({content:data, selected: this.selected}));
+            this.delegateEvents();
+            return this;
+        },
+        onCancel: function() {
+            $('.box-icon').css("background-color", "transparent");
+            this.$el.dialog('close');
+        },
+        onOk: function() {
+            const svgObj = this.svgCollection.find(item => item.name === this.selected);
+            this.trigger(Events.ButtonEvents.SELECT_ICON, svgObj );
+            this.$el.dialog('close');
+        },
+        onSelect: function(e){
+            $('.box-icon').css("background-color", "transparent");
+            var $target = $(e.currentTarget);
+            $target.css('background-color', '#41ffbe');
+            var id = $target.data('name');
+            this.selected = id;
+        }
+    });
+    return SelectIconView;
+});
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js	(révision 11532)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js	(révision 11533)
@@ -8,6 +8,10 @@
     "cancel": "Annuler",
     "image": "Image",
     "buttonBlockOption": "Options du bouton",
+    "iconButton":"Icône",
+    "iconButtonLegend":"Sélectionnez l'icône du bouton ici",
+    "browseIconsLegend":"Parcourir les icônes",
+    "showIconLegend": "Afficher l'icône",
     "sizeButton": "Taille du bouton",
     "sizeButtonLegend": "Sélectionnez la taille de votre bouton ici",
     "tinyButton": "Petit",
@@ -35,5 +39,7 @@
     "contour":"Contour",
     "pastel-lead":"Pastel Lead",
     "vibrante-lead":"Vibrante Lead",
-    "contour-lead":"Contour Lead"
+    "contour-lead":"Contour Lead",
+    "browseIconTitle":"Browse the icon database",
+    "choose":"Choose"
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js	(révision 11532)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js	(révision 11533)
@@ -8,6 +8,10 @@
     "cancel": "Annuler",
     "image": "Image",
     "buttonBlockOption": "Options du bouton",
+    "iconButton":"Icône",
+    "iconButtonLegend":"Sélectionnez l'icône du bouton ici",
+    "browseIconsLegend":"Parcourir les icônes",
+    "showIconLegend": "Afficher l'icône",
     "sizeButton": "Taille du bouton",
     "sizeButtonLegend": "Sélectionnez la taille de votre bouton ici",
     "tinyButton": "Petit",
@@ -35,6 +39,8 @@
     "contour":"Contour",
     "pastel-lead":"Pastel Lead",
     "vibrante-lead":"Vibrante Lead",
-    "contour-lead":"Contour Lead"
+    "contour-lead":"Contour Lead",
+    "browseIconTitle":"Parcourir la base d'icônes",
+    "choose":"Choisir"
 
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js	(révision 11532)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js	(révision 11533)
@@ -9,6 +9,10 @@
         "cancel": "Cancel",
         "image": "Image",
         "buttonBlockOption": "Button options",
+        "iconButton": "Icon",
+        "iconButtonLegend": "Select button icon here",
+        "browseIconsLegend":"Browse icons",
+        "showIconLegend": "Show icon",
         "sizeButton": "Button size",
         "sizeButtonLegend": "Select the size of your button here.",
         "tinyButton": "Small",
@@ -36,7 +40,9 @@
         "outline":"Outline",
         "pastel-lead":"Pastel Lead",
         "vibrante-lead":"Vibrante Lead",
-        "contour-lead":"Contour Lead"
+        "contour-lead":"Contour Lead",
+        "browseIconTitle":"Browse the icon database",
+        "choose":"Choose"
     },
     "fr-fr": true,
     "fr-ca": true
Index: src/less/imports/blockButton.less
===================================================================
--- src/less/imports/blockButton.less	(nonexistent)
+++ src/less/imports/blockButton.less	(révision 11533)
@@ -0,0 +1,33 @@
+/* button icon  */
+.selected-icon-wrapper {
+    display: flex;
+    justify-content: center;
+    align-items: center;
+    font-size: 50px;
+  }
+  
+  .selected-icon-wrapper svg {
+    width: 100px;
+    height: 100px;
+  }
+  
+  .option-content .grid-container-icon {
+    display: grid;
+    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
+    grid-gap: 5px;
+  }
+  
+  .option-content .box-icon {
+    padding: 5px;
+    text-align: center;
+    border: 1px solid black;
+    font-size: 50px;
+  }
+  .option-content .box-icon svg {
+    width:  50px;
+    height:  50px;
+  }
+  .option-content .selected-icon{
+      background-color: #41ffbe;
+  }
+ 
\ No newline at end of file

Property changes on: src/less/imports/blockButton.less
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/less/main.less
===================================================================
--- src/less/main.less	(révision 11532)
+++ src/less/main.less	(révision 11533)
@@ -23,6 +23,7 @@
 @import "./imports/imageEditor.less";
 @import "./imports/panel_gallery_animations.less";
 @import "./imports/gallery_style.less";
+@import "./imports/blockButton.less";
 @import "./imports/htmlEditor.less";
 @import "./imports/navigation.less";
 @import "./imports/editImageDialog.less";
@@ -2900,4 +2901,5 @@
       color: #999 !important;
     }
   }
-}
\ No newline at end of file
+}
+
