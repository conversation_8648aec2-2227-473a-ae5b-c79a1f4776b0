Revision: r9942
Date: 2022-12-06 12:56:35 +0300 (tlt 06 Des 2022) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2 Facade Method pour les bloc video (supresssion checkbox autoplay) 

## Files changed

## Full metadata
------------------------------------------------------------------------
r9942 | srazanandralisoa | 2022-12-06 12:56:35 +0300 (tlt 06 Des 2022) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/VideoBlock/Templates/videoInfoOption.html

wishlist IDEO3.2 Facade Method pour les bloc video (supresssion checkbox autoplay) 
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/VideoBlock/Templates/videoInfoOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/VideoBlock/Templates/videoInfoOption.html	(révision 9941)
+++ src/js/JEditor/PagePanel/Contents/Blocks/VideoBlock/Templates/videoInfoOption.html	(révision 9942)
@@ -10,10 +10,6 @@
         <div class="option">
             <input type="text" class="link video-url" value="<%= url%>" name="video-url" placeholder="" />
         </div>
-        <div class="option">
-            <% var _id=_.uniqueId('autoplay') %>
-            <input type="checkbox" id="<%=_id%>" class="autoplay" name="autoplay" <%= autoplay?'checked="checked"':''%>/><label for="<%=_id%>" class="label"><span class="checkbox"><span class="icon-checked"></span><span class="icon-unchecked"></span></span><%= __("autoplay") %></label>
-        </div>
         <div class="providerAnalysis">
             <div class="wrapper">
                 <span class="effect-radio provider small <%=provider%>"><span class="container"></span></span>
