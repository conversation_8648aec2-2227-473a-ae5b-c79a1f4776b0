Revision: r11886
Date: 2024-02-15 15:50:25 +0300 (lkm 15 Feb 2024) 
Author: mpartaux 

## Commit message
add contrast colors option

## Files changed

## Full metadata
------------------------------------------------------------------------
r11886 | mpartaux | 2024-02-15 15:50:25 +0300 (lkm 15 Feb 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/Models/StylesOption.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/Templates/stylesOption.html
   M /branches/ideo3_v2/integration/src/less/imports/page_panel/module/block-options/advanced-option.less

add contrast colors option
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Options/Models/StylesOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/Models/StylesOption.js	(révision 11885)
+++ src/js/JEditor/PagePanel/Contents/Options/Models/StylesOption.js	(révision 11886)
@@ -160,6 +160,8 @@
                     "accent1" : "Accent 1",
                     "lead0" : "Lead 0",
                     "lead1" : "Lead 1",
+                    "contrast0" : "Contrast 0",
+                    "contrast1" : "Contrast 1"
                 },
             },
             //StylesOption.SetAttributes(['contentSize', 'shadeWorn','sectonWidx']);
Index: src/js/JEditor/PagePanel/Contents/Options/Templates/stylesOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/Templates/stylesOption.html	(révision 11885)
+++ src/js/JEditor/PagePanel/Contents/Options/Templates/stylesOption.html	(révision 11886)
@@ -30,7 +30,7 @@
             _.each(classColor,function(value,key){%>
                 <%  var _id=_.uniqueId('couleur');%>
 
-                <% if (key === 'accent0' || key === 'lead0') { %>
+                <% if (key === 'accent0' || key === 'lead0' || key === 'contrast0') { %>
                     <div style="clear: both;">
                 <% } else { %>
                     <div>
Index: src/less/imports/page_panel/module/block-options/advanced-option.less
===================================================================
--- src/less/imports/page_panel/module/block-options/advanced-option.less	(révision 11885)
+++ src/less/imports/page_panel/module/block-options/advanced-option.less	(révision 11886)
@@ -15,7 +15,8 @@
 .effects .effect-radio {
 	[class^='surface'],
 	[class^='accent'],
-	[class^='lead'] {
+	[class^='lead'],
+	[class^='contrast'] {
 		display: block;
 		height: 100%;
 		border-radius: 4px;
@@ -105,7 +106,15 @@
 .lead1 {
 	background: var(--lead-surface1); 
 	color: var(--lead-text1);
+}
+.contrast0 {
+	background: var(--contrast-surface0); 
+	color: var(--contrast-text0);
 } 
+.contrast1 {
+	background: var(--contrast-surface1); 
+	color: var(--contrast-text1);
+} 
 
 .color-container {
 	.normal{
