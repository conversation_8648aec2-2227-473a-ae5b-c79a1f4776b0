Revision: r10221
Date: 2023-01-25 15:43:55 +0300 (lrb 25 Jan 2023) 
Author: sraz<PERSON><PERSON><PERSON><PERSON> 

## Commit message
bug images generees webp avec canal alpha et SVG

## Files changed

## Full metadata
------------------------------------------------------------------------
r10221 | srazanandralisoa | 2023-01-25 15:43:55 +0300 (lrb 25 Jan 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/dev/#librairies/module/GestionContenuUtil/src/GestionContenuUtil/Core/BlockModuleHandlers/BlockImageHandler.php
   M /branches/ideo3_v2/dev/#librairies/vendor/CoreRessourceManager/src/CoreRessourceManager/Service/ResourceUploader.php
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageOptionsView.js

bug images generees webp avec canal alpha et SVG
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageOptionsView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageOptionsView.js	(révision 10220)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageOptionsView.js	(révision 10221)
@@ -148,6 +148,7 @@
 			});
 			this.$el.html(this._template(templateVars));
 			this.$('.image-option header').after(this.fileUploader.el);
+			if(this.model.file.ext==='svg')this.$('.figcaption-img').hide();
 			this.fileUploader.render();
 			this._linkSelectorView = new LinkView({
 				model : this.model.link,
