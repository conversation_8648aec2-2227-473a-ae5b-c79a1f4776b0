Revision: r10045
Date: 2022-12-22 11:37:17 +0300 (lkm 22 Des 2022) 
Author: jn.harison 

## Commit message
Wishlist IDEO3.2:Creating mark client fonctionnality

## Files changed

## Full metadata
------------------------------------------------------------------------
r10045 | jn.harison | 2022-12-22 11:37:17 +0300 (lkm 22 Des 2022) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileDetailManager.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileDetailManagerView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Models/Params.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/ParamsPanel.js
   A /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Templates/MarqueClient.html
   A /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/MarqueClientView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/params_panel/module/inline-social.less

Wishlist IDEO3.2:Creating mark client fonctionnality
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Models/Params.js
===================================================================
--- src/js/JEditor/ParamsPanel/Models/Params.js	(révision 10044)
+++ src/js/JEditor/ParamsPanel/Models/Params.js	(révision 10045)
@@ -48,14 +48,18 @@
             JsonLdDefaultData: null , 
             JsonLdCustomData: null ,
             SocialNetworksUrl :null,
+            MarqueClient: null,
+            Logo: null,
+            LogoSmall: null,
+            Favicon: null
         },
         initialize: function () {
             this._super();
             this.on("change:facebookUrl change:twitterUrl change:pinterestUrl change:mybusinessUrl change:youtubeUrl change:linkedinUrl change:viadeoUrl change:instagramUrl", this.onSocialNetworkUrlChange);
+            this.on("change:MarqueClient", this.onMarqueClientChange);
         },
         onSocialNetworkUrlChange: function (model) {
             var changed = this.changedAttributes();
-            console.log(changed);
             var that = this;
             _.each(changed, function (value, key) {
                 if ((key === "facebookUrl" || key === "twitterUrl" || key === "pinterestUrl" || key === "mybusinessUrl" || key==="instagramUrl"||key==="youtubeUrl"||key==="linkedinUrl"||key==="viadeoUrl")) {
@@ -63,6 +67,14 @@
                 }
             });
         },
+        onMarqueClientChange: function(model) {
+            var changed = this.changedAttributes();
+            var valueJsonLd = $("#typeStructuredData").val();
+            if( valueJsonLd=="on")
+            {
+                $('#customData').val(model.attributes['JsonLdDefaultData']);
+            }
+        },
         validate: function (attributes, options) {
             var socialNetworks = ["facebookUrl", "mybusinessUrl", "twitterUrl", "pinterestUrl", "youtubeUrl", "linkedinUrl", "viadeoUrl", "instagramUrl"];
             //en gros c'est ça dans une boucle:
Index: src/js/JEditor/FilePanel/Templates/fileDetailManager.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileDetailManager.html	(révision 10044)
+++ src/js/JEditor/FilePanel/Templates/fileDetailManager.html	(révision 10045)
@@ -6,16 +6,6 @@
 
     <div class="right-actions">
         <% if(isImg){%>
-        <a href="#" style="width: 100px;" data-action="setAsLogo" data-id="<%=id%>">
-            <span class="icon-hexagon"></span>
-            <%= __("setAsLogoAction")%>
-            <span class="infobulles"><%= __("setAsLogo")%></span>
-        </a> 
-        <a href="#" style="width: 100px;" data-action="fileFlag">
-            <span class="icon-asterix"></span>
-            <%= __("Favicon")%>
-            <span class="infobulles"> <%= __("btnFavicon")%></span>
-        </a>
         <a href="#" data-action="focus" data-id="<%=id%>">
             <span class="icon-interest-point"></span>
             <%= __("editFocusAction")%>
Index: src/js/JEditor/FilePanel/Views/FileDetailManagerView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileDetailManagerView.js	(révision 10044)
+++ src/js/JEditor/FilePanel/Views/FileDetailManagerView.js	(révision 10045)
@@ -30,8 +30,6 @@
         'uploadercomplete a[data-replace]': 'uploadercomplete',
         'click a[data-replace]': 'showUploader',
         'click a[data-action="focus"]': 'editFocusPoint',
-        'click a[data-action="fileFlag"]': 'fileFlagClick', 
-        'click a[data-action="setAsLogo"]': 'setAsLogoClick'
     },
     initialize: function() {
         this._super();
@@ -81,17 +79,7 @@
             message: translate("onFocusPointSave")
         });
     },
-    setAsLogoClick: function(e) {
-        e.preventDefault() ;
-        e.stopImmediatePropagation();
-        if (this.FileDetailView) {
-            this.FileDetailView.model.desc['logo'] = true;
-            this.FileDetailView.model.desc['flag'] = false;
-            this.FileDetailView.model.save()
-            location.reload();
-        }
-        
-    },
+   
     onFocusPointSaveError: function() {
         this.error({
             message: translate("onFocusPointSaveError")
@@ -172,15 +160,7 @@
         }
         return false;
     },
-    fileFlagClick: function(e) {
-        e.preventDefault()   ; 
-        e.stopImmediatePropagation();
-        if (this.FileDetailView) {
-            this.FileDetailView.model.desc['flag'] = true;
-            this.FileDetailView.model.save();
-        }
-        location.reload();
-    }
+
    
 });
 Events.extend({
Index: src/js/JEditor/ParamsPanel/ParamsPanel.js
===================================================================
--- src/js/JEditor/ParamsPanel/ParamsPanel.js	(révision 10044)
+++ src/js/JEditor/ParamsPanel/ParamsPanel.js	(révision 10045)
@@ -9,11 +9,12 @@
     "./Views/MobileStoreView",
     "./Views/PolitiqueConfidentialiteView",
     "./Views/DonneeStructureeView",
+    "./Views/MarqueClientView",
     "./Models/Params",
     //hidden
     "jqueryui/datepicker"
 ],
-        function ($, _, PanelView, Events, ParamsPanelTemplate, translate, SocialNetworks,SEOView, MobileStoreView, PolitiqueConfidentialiteView,DonneeStructureeView, Params) {
+        function ($, _, PanelView, Events, ParamsPanelTemplate, translate, SocialNetworks,SEOView, MobileStoreView, PolitiqueConfidentialiteView,DonneeStructureeView, MarqueClientView, Params) {
             var SocialPanel = PanelView.extend(
                     /**
                      * @lends JEditor.Panels.MessagePanel.prototype
@@ -69,6 +70,11 @@
                                             object: new PolitiqueConfidentialiteView({model:this.params}),
                                             icon:"icon-link",
                                             title:translate("PolitiqueConfidentialite")
+                                        },
+                                        MarqueClient:{
+                                            object: new MarqueClientView({model:this.params}),
+                                            icon:"icon-html",
+                                            title:translate("MarqueClient") 
                                         }
                                        
                                     };
Index: src/js/JEditor/ParamsPanel/Templates/MarqueClient.html
===================================================================
--- src/js/JEditor/ParamsPanel/Templates/MarqueClient.html	(nonexistent)
+++ src/js/JEditor/ParamsPanel/Templates/MarqueClient.html	(révision 10045)
@@ -0,0 +1,81 @@
+  
+    <div class="main-content__wrapper ">
+        <!--
+            ******************************************
+            créer nouvelle childView marque client,
+            y déplacer ce contenu
+            ******************************************
+            -->
+            <div class="ideo-title">
+                <h1>
+                    <%=__("MarqueClient")%>
+                    <span><%=__("set_marque_client")%></span>
+                </h1>
+            </div>
+        
+            <div class="inline-params thin-border  radius  shadow">
+                <span class="inline-params__name">
+                   Enseigne:
+                </span>
+                <label>
+                    <span class="custom-input-left">
+                        <input type="text" data-autosave="true" name="MarqueClient" class="field-input neutral-input  bold" value="<%=MarqueClient%>"/>
+                    </span>
+                </label>
+                <label>
+                    [[contact_enseigne]]
+                </label>
+            </div>
+    
+            <div class="my-files fileList">
+                <div class="content scroll-container">
+                    <div class="inline-params thin-border radius shadow">
+                        <div class="inline-logo">
+                            <div class="group-content-logo">
+                                <span class="inline-params__nameLogo">
+                                    <%=__("Add_logo")%>
+                                </span>
+                                <div class="menu-wrapper-logo file image">
+                                    <header></header>
+                                </div>
+                                
+                                <span class="inline-params__nameLogoFooter">
+                                    [[contact_logo]]
+                                </span>
+                            </div>      
+                            <div class="group-content-logo-small">
+                                <span class="inline-params__nameLogo">
+                                    <%=__("Add_logo_small")%>
+                                </span>
+                                <div class="menu-wrapper-logoSmall file image">
+                                    <header></header>
+                                </div>
+                                <span class="inline-params__nameLogoFooter">
+                                    [[contact_logo|format=small]]
+                                </span>
+                            </div>
+                            <div class="group-content-favicon">
+                                <span class="inline-params__nameLogo">
+                                    <%=__("Add_favicon")%>
+                                </span>
+                                <div class="menu-wrapper-favicon file image">
+                                    <header></header>
+                                </div>
+                            </div>
+                            
+                        </div>
+                       
+                    </div>
+                    <div class="inline-params thin-border radius shadow">
+                       
+                       
+                    </div>
+                    
+                </div>
+            </div>
+            <!-- 
+            ******************
+            end new childView
+            ******************
+            -->
+        </div>
\ No newline at end of file

Property changes on: src/js/JEditor/ParamsPanel/Templates/MarqueClient.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/ParamsPanel/Views/MarqueClientView.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(nonexistent)
+++ src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 10045)
@@ -0,0 +1,196 @@
+define(['jquery', 
+        "underscore", 
+        'JEditor/Commons/Ancestors/Views/View', 
+        'JEditor/Commons/Events', 
+        "collection!JEditor/Commons/Files/Models/FileDBCollection",
+        "JEditor/Commons/Files/Views/FileUploaderView",
+        "JEditor/Commons/Files/Models/File",
+        'text!../Templates/MarqueClient.html',
+        'i18n!../nls/i18n',
+        "jqueryPlugins/uploader"
+    ], function (
+            $, 
+            _, 
+            View, 
+            Events, 
+            FileDBCollection,
+            FileUploaderView,
+            File,
+            template,
+            translate) {
+    var MarqueClient = View.extend({
+        currentFile: null,
+        currentFile2: null,
+        currentFileFavicon: null,
+        tagName: "div",
+        className: "panel-content image-panel image",
+        events: {
+            'uploadercomplete .uploader': '_onUploaded',
+            'click .menu-wrapper-logo ' : '_onlyComputerLogo',
+            'click .menu-wrapper-logoSmall ' : '_onlyComputerLogoSmall',
+            'click .menu-wrapper-favicon ' : '_onlyComputerFavicon',
+        },
+
+        initialize: function () {
+            
+            this._super();
+            this.model=this.options.model;
+            this.currentFileLogo = null;
+            this.currentFileLogoSmall = null;
+            this.currentFileFavicon = null;
+            this._template = this.buildTemplate(template,translate);
+            this.fileCollection = FileDBCollection.getInstance();
+            this.translations = translate.translations;
+         },
+         
+        _onUpload: function(file) {
+            file.attributes.isLogo = "logo";
+            this.currentFileLogo = new File(file.attributes);
+            this.model.set('Logo', this.currentFileLogo);
+            this.model.set('LogoSmall', this.currentFileLogoSmall);
+            this.model.set('Favicon', this.currentFileFavicon);
+            this.model.save();
+            this.$('.group-content .uploader .view').addClass('done');
+        },
+
+        _onUpload2: function(file) {
+            file.attributes.isLogo = "logoSmall";
+            this.currentFileLogoSmall = new File(file.attributes);
+            this.model.set('Logo', this.currentFileLogo);
+            this.model.set('LogoSmall', this.currentFileLogoSmall);
+            this.model.set('Favicon', this.currentFileFavicon);
+            this.model.save();
+            this.$('.group-content-logo-small .uploader .view').addClass('done');
+        },
+
+        _onUpload3: function(file) {
+            file.attributes.isLogo = "favicon";
+            this.currentFileFavicon = new File(file.attributes);
+            this.model.set('Logo', this.currentFileLogo);
+            this.model.set('LogoSmall', this.currentFileLogoSmall);
+            this.model.set('Favicon', this.currentFileFavicon);
+            this.model.save();
+            this.$('.group-content-favicon .uploader .view').addClass('done');
+        },
+
+        _onlyComputerLogo: function(e) {
+            e.preventDefault();
+            e.stopImmediatePropagation();
+            this.$('.menu-wrapper-logo .uploader .actions-wrapper').removeClass('visible');
+            this.$('.menu-wrapper-logo .uploader .actions-wrapper .actions .computer input:file.upload').trigger('click');
+        },
+
+        
+        _onlyComputerLogoSmall: function(e) {
+            e.preventDefault();
+            e.stopImmediatePropagation();
+            this.$('.menu-wrapper-logoSmall .uploader .actions-wrapper').removeClass('visible');
+            this.$('.menu-wrapper-logoSmall .uploader .actions-wrapper .actions .computer input:file.upload').trigger('click'); 
+        },
+
+        _onlyComputerFavicon: function(e) {
+            e.preventDefault();
+            e.stopImmediatePropagation();
+            this.$('.menu-wrapper-favicon .uploader .actions-wrapper').removeClass('visible');
+            this.$('.menu-wrapper-favicon .uploader .actions-wrapper .actions .computer input:file.upload').trigger('click');
+        },
+
+        render: function () {
+            this.$el.html(this._template(this.model.toJSON()));
+            if(this.model.attributes.Logo && (!Array.isArray(this.model.attributes.Logo)))
+            {
+                this.currentFileLogo = this.model.attributes.Logo;
+                this.currentFileLogo = new File(this.currentFileLogo);
+            }
+            else
+            {
+                this.currentFileLogo = null;
+            }
+            if(this.model.attributes.LogoSmall && (!Array.isArray(this.model.attributes.LogoSmall)))
+            {
+                this.currentFileLogoSmall = this.model.attributes.LogoSmall;
+                this.currentFileLogoSmall = new File(this.currentFileLogoSmall);
+            }
+            else
+            {
+                this.currentFileLogoSmall = null;
+            }
+            if(this.model.attributes.Favicon && (!Array.isArray(this.model.attributes.Favicon)))
+            {
+                this.currentFileFavicon = this.model.attributes.Favicon;
+                this.currentFileFavicon = new File(this.currentFileFavicon);
+            }
+            else
+            {
+                this.currentFileFavicon = null;
+            }
+           
+              //Logo
+              this.fileUploader = new FileUploaderView({
+                currentFile: this.currentFileLogo,
+                collection: this.fileCollection,
+				uploadParams : {
+					customStockEvent : '_parsestock_image',
+					acceptedTypes : [ 'image' ],
+					acceptedExtensions : ['jpeg','jpg','gif','png', 'svg'],
+                    refusedExtensions:['bmp'],
+				},
+
+			});
+
+            //Logo small
+            this.fileUploader2 = new FileUploaderView({
+                currentFile: this.currentFileLogoSmall,
+                collection: this.fileCollection,
+				uploadParams : {
+					customStockEvent : '_parsestock_image',
+					acceptedTypes : [ 'image' ],
+					acceptedExtensions : ['jpeg','jpg','gif','png', 'svg'],
+                    refusedExtensions:['bmp'],
+				}
+			});
+
+              //Favicon
+              this.fileUploader3 = new FileUploaderView({
+                currentFile: this.currentFileFavicon,
+                collection: this.fileCollection,
+				uploadParams : {
+					customStockEvent : '_parsestock_image',
+					acceptedTypes : [ 'image' ],
+					acceptedExtensions : ['jpeg','jpg','png'],
+                    refusedExtensions:['bmp'],
+				}
+			});
+
+            this.listenTo(this.fileUploader, Events.FileUploaderEvents.UPLOAD, this._onUpload);
+            this.listenTo(this.fileUploader2, Events.FileUploaderEvents.UPLOAD, this._onUpload2);
+            this.listenTo(this.fileUploader3, Events.FileUploaderEvents.UPLOAD, this._onUpload3);
+
+            this.$('.inline-logo .group-content-logo header').after(this.fileUploader.el);
+            this.$('.inline-logo .group-content-logo-small header').after(this.fileUploader2.el);
+            this.$('.inline-logo .group-content-favicon header').after(this.fileUploader3.el);
+			this.fileUploader.render();
+            this.fileUploader2.render();
+            this.fileUploader3.render();
+            if(this.model.attributes.Logo && (!Array.isArray(this.model.attributes.Logo))){this.$('.group-content-logo .uploader').addClass('done');}
+            if(this.model.attributes.LogoSmall && (!Array.isArray(this.model.attributes.LogoSmall))){this.$('.group-content-logo-small .uploader').addClass('done');}
+            if(this.model.attributes.Favicon && (!Array.isArray(this.model.attributes.Favicon))){this.$('.group-content-favicon .uploader').addClass('done');}
+            this.$('.menu-wrapper-logo .uploader .preview .imagepreview').after('<span style="font-size:9px;display:block;margin-top:35px;color: #666;text-align:center;">'+this.translations.DefaultMessageUploaderLogo+'</span>');
+            this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview').after('<span style="font-size:9px;display:block;margin-top:35px;color: #666;text-align:center;">'+this.translations.DefaultMessageUploaderLogo+'</span>');
+            this.$('.menu-wrapper-favicon .uploader .preview .imagepreview').after('<span style="font-size:9px;display:block;margin-top:35px;color: #666;text-align:center;">'+this.translations.DefaultMessageUploaderLogo+'</span>');
+            return this;
+        },
+
+        
+    });
+
+    Events.extend({
+        ListViewEvents: {
+            UPLOADER_COMPLETE: 'uploaderComplete',
+            FILE_CLEARVIEW: 'fileClearView',
+            CHOOSE_IMG: 'selectImage',
+            
+        },
+    })
+    return MarqueClient;
+});
\ No newline at end of file

Property changes on: src/js/JEditor/ParamsPanel/Views/MarqueClientView.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 10044)
+++ src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 10045)
@@ -54,8 +54,13 @@
         "standardFormat": "Standard actif(défaut)",
         "customFormat" : "Custom actif",
         "schemaJson":"Schéma de données JsonLD",
-        "JsonDisabled" : "Désactivé"
+        "JsonDisabled" : "Désactivé",
+        "MarqueClient": "Marque client",
+        "Add_logo": "Ajouter le logo",
+        "Add_logo_small": "Ajouter le logo \"small\"",
+        "Add_favicon": "Ajouter le favicon",
+        "set_marque_client": "Renseignez les informations de Marque du client tel que l'enseigne, le logo ...",
+        "DefaultMessageUploaderLogo": "Cliquez ici ou glissez-déposez une image"
 
 
-
     });
Index: src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 10044)
+++ src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 10045)
@@ -58,4 +58,11 @@
         "confirmDeleteSocial": "Vous \u00eates sur le point de supprimer d\u00e9finitivement un r\u00e9seau social</br><strong><% name %></strong>",
         "deleteAction": "Supprimer",
         "addPage":"Ajouter une page",
+        "MarqueClient": "Marque client",
+        "Add_logo": "Ajouter le logo",
+        "Add_logo_small": "Ajouter le logo \"small\"",
+        "Add_favicon": "Ajouter le favicon",
+        "set_marque_client": "Renseignez les informations de Marque du client tel que l'enseigne, le logo ...",
+        "DefaultMessageUploaderLogo": "Cliquez ici ou glissez-déposez une image"
+
     });
Index: src/js/JEditor/ParamsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 10044)
+++ src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 10045)
@@ -59,7 +59,13 @@
         "JsonDisabled":"Disabled",
         "confirmDeleteSocial": "You are about to permanently delete a social network</br><strong><% name %></strong>",
         "deleteAction": "Delete",
-        "addPage":"Add a page"
+        "addPage":"Add a page",
+        "MarqueClient": "Client Mark",
+        "Add_logo": "Add logo",
+        "Add_logo_small": "Add logo \"small\"",
+        "Add_favicon": "Add favicon",
+        "set_marque_client": "Fill in the customer's brand information such as the brand name, the logo ...",
+        "DefaultMessageUploaderLogo": "Click here or drag and drop an image"
     },
     "fr-fr": true,
     "fr-ca": true
Index: src/less/imports/params_panel/module/inline-social.less
===================================================================
--- src/less/imports/params_panel/module/inline-social.less	(révision 10044)
+++ src/less/imports/params_panel/module/inline-social.less	(révision 10045)
@@ -91,4 +91,128 @@
 		color: currentColor;
 		font-family: 'raleway';
 		border: none;
-	  }
\ No newline at end of file
+	  }
+
+	  .menu-wrapper-logo {
+        margin: 30px;
+        -webkit-transform: scale(1.3);
+        -moz-transform: scale(1.3);
+        -ms-transform: scale(1.3);
+        -o-transform: scale(1.3);
+        transform: scale(1.3);
+    }
+
+    .menu-wrapper-logoSmall {
+        -webkit-transform: scale(1);
+        -moz-transform: scale(1);
+        -ms-transform: scale(1);
+        -o-transform: scale(1);
+        transform: scale(1);
+    }
+
+    .menu-wrapper-favicon {
+        -webkit-transform: scale(1);
+        -moz-transform: scale(1);
+        -ms-transform: scale(1);
+        -o-transform: scale(1);
+        transform: scale(1);
+    }
+    
+    .inline-params__nameLogo{
+        margin-left: 25px;
+        font-size: 1.2em;
+        color: #666;
+    }
+    
+    .inline-params__nameLogoFooter{
+        margin-left: 25px;
+        font-size: 1.2em;
+    }
+    
+    .inline-logo{
+        display: flex;
+    }
+
+    .custom-input-left{
+        display: inline-block;
+        padding: 0 15px;
+        line-height: 35px;
+        position: relative;
+        z-index: 1;
+        background-color: #f4f4f4;
+        border: 1px solid #e1e1e1;
+        -webkit-border-radius: 3px;
+        -moz-border-radius: 3px;
+        border-radius: 3px;
+        -moz-background-clip: padding;
+        -webkit-background-clip: padding-box;
+        background-clip: padding-box;
+        font-family: 'raleway';
+        color: #999;
+        font-weight: normal;
+    }
+
+    .inline-params .custom-input-left {
+        width: 55%;
+        min-width: 300px;
+        margin-top: 20px;
+        margin-right: 20px;
+    }
+
+    .group-content-logo .uploader{
+        line-height: 10px;
+        margin-left: 20px;
+        margin-right: 5px;
+        border: 1px solid #666;
+        background-color: #ffffff;
+        content: 'cliquez ici ou glissez-déposez une image';
+    }
+
+    .group-content-logo-small .uploader{
+        line-height: 10px;
+        margin-left: 20px;
+        margin-right: 20px;;
+        border: 1px solid #666;
+        background-color: #ffffff;
+        content: 'cliquez ici ou glissez-déposez une image';
+    }
+
+    .group-content-favicon .uploader{
+        line-height: 10px;
+        margin-left: 20px;
+        margin-right: 5px;
+        border: 1px solid #666;
+        background-color: #ffffff;
+        content: 'cliquez ici ou glissez-déposez une image';
+    }
+
+    .group-content-logo .uploader .preview:after{
+        top:15%;
+        width: 80px;
+        line-height: 15px;
+        font-size: 9px;
+    }
+
+    .group-content-logo-small .uploader .preview:after{
+        top:15%;
+        width: 80px;
+        line-height: 15px;
+        font-size: 9px;
+    }
+
+    .group-content-favicon .uploader .preview:after{
+        top:15%;
+        width: 80px;
+        line-height: 15px;
+        font-size: 9px;
+    }
+
+    
+    .my-files .menu-wrapper-logo{
+        margin: 30px;
+        -webkit-transform: scale(1.3);
+        -moz-transform: scale(1.3);
+        -ms-transform: scale(1.3);
+        -o-transform: scale(1.3);
+        transform: scale(1.3);
+    }
\ No newline at end of file
