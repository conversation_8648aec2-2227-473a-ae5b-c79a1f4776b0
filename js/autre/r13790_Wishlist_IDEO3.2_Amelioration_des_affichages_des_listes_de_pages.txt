Revision: r13790
Date: 2025-02-05 08:56:35 +0300 (lrb 05 Feb 2025) 
Author: frahajanirina 

## Commit message
Wishlist IDEO3.2: Amelioration des affichages des listes de pages

## Files changed

## Full metadata
------------------------------------------------------------------------
r13790 | frahajanirina | 2025-02-05 08:56:35 +0300 (lrb 05 Feb 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Templates/pagePanel.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/main.less

Wishlist IDEO3.2: Amelioration des affichages des listes de pages
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Templates/pagePanel.html
===================================================================
--- src/js/JEditor/PagePanel/Templates/pagePanel.html	(révision 13789)
+++ src/js/JEditor/PagePanel/Templates/pagePanel.html	(révision 13790)
@@ -71,7 +71,7 @@
                             </tr>
                         <% } else { %>
                             <% _.each(content, function(page) { %>
-                                <% if ((page.type !== 'news' && canAccess) || (page.type !== 'news' && page.type !== 'template')) { %>
+                                <% if (page.type !== 'news' && page.type !== 'template') { %>
                                     <tr data-cid="<%= page.cid %>" class="data-tr <%= !page.active ? 'disabled' : ''%>">
                                         <td>
                                             <% var icons = {
@@ -82,11 +82,25 @@
                                                 5: 'icon-star',
                                                 6: 'icon-newsletter-icon',
                                                 7: 'icon-interest-point',
-                                                0: 'icon-file',
-                                                8: 'icon-file'
+                                                0: 'icon-page',
+                                                8: 'icon-landing-page'
                                             };
-                                            var iconClass = icons[page.home] || 'icon-file'; %>
+                                                if (page.locked) {
+                                                    var iconClass = 'icon-lock';
+                                                } else {
+                                                    var iconClass = icons[page.home] || 'icon-page';    
+                                                }
+                                            %>
                                             <span class="icon <%= iconClass %>"></span>
+                                            <% if (page.home === 0) { %>
+                                                <span class="type-name"><%= __('contentPage') %></span>
+                                            <% } else if (page.home === 7) { %>
+                                                <span class="type-name"><%= __('pagelp') %></span>
+                                            <% } else if (page.home === 8) { %>
+                                                <span class="type-name"><%= __('landingPage') %></span>
+                                            <% } else { %>
+                                                <span class="type-name"><%= page.name %></span>
+                                            <% } %>                                            
                                         </td>
                                         <td class="title"><%= page.name %></td>
                                         <td>
Index: src/js/JEditor/PagePanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/nls/fr-ca/i18n.js	(révision 13789)
+++ src/js/JEditor/PagePanel/nls/fr-ca/i18n.js	(révision 13790)
@@ -133,6 +133,7 @@
     "none_result": "Aucune page correspond à la recherche",
     "title" : "Titre",
     "allPages": "Toutes les pages",
-    "icon": "Icônes",
-    "action": "Action"
+    "icon": "Type",
+    "action": "Action",
+    "contentPage": "Page de contenu"
 });
Index: src/js/JEditor/PagePanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/nls/fr-fr/i18n.js	(révision 13789)
+++ src/js/JEditor/PagePanel/nls/fr-fr/i18n.js	(révision 13790)
@@ -139,6 +139,7 @@
     "none_result": "Aucune page correspond à la recherche",
     "title" : "Titre",
     "allPages": "Toutes les pages",
-    "icon": "Icônes",
-    "action": "Action"
+    "icon": "Type",
+    "action": "Action",
+    "contentPage": "Page de contenu"
 });
Index: src/js/JEditor/PagePanel/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/nls/i18n.js	(révision 13789)
+++ src/js/JEditor/PagePanel/nls/i18n.js	(révision 13790)
@@ -137,8 +137,9 @@
         "none_result": "No pages match the search",
         "title" : "Title",
         "allPages": "All pages",
-        "icon": "Icon",
-        "action": "Action"
+        "icon": "Type",
+        "action": "Action",
+        "contentPage": "Content page"
     },
     "fr-fr": true, "fr-ca":true
 });
Index: src/less/main.less
===================================================================
--- src/less/main.less	(révision 13789)
+++ src/less/main.less	(révision 13790)
@@ -3066,6 +3066,9 @@
   td {
     padding: 25px 8px;
   }
+  .type-name {
+    font-size: 12px;
+  }
 }
 .title-bar {
   border-bottom: 1px solid #e1e1e1;
