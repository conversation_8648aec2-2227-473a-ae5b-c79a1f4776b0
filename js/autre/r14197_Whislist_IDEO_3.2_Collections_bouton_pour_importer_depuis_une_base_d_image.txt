Revision: r14197
Date: 2025-05-06 11:49:19 +0300 (tlt 06 Mey 2025) 
Author: rrakotoarinelina 

## Commit message
Whislist IDEO 3.2 : Collections : bouton pour importer depuis une base d'image

## Files changed

## Full metadata
------------------------------------------------------------------------
r14197 | rrakotoarinelina | 2025-05-06 11:49:19 +0300 (tlt 06 Mey 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/collectionDetail.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/CollectionDetailView.js
   M /branches/ideo3_v2/integration/src/less/imports/filePanel.less

Whislist IDEO 3.2 : Collections : bouton pour importer depuis une base d'image
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/FilePanel/Templates/collectionDetail.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/collectionDetail.html	(révision 14196)
+++ src/js/JEditor/FilePanel/Templates/collectionDetail.html	(révision 14197)
@@ -41,7 +41,7 @@
             </div>
 
             <% } %>
-            <div class="menu-wrapper add-file" data-action="showuploader">
+            <div class="menu-wrapper add-file upload" data-action="showuploader">
 
                 <span class="icon">
                     <span class="icon-hexagon"></span>
@@ -50,6 +50,16 @@
                 <%= __("addFile")%>
 
             </div>
+
+            <div class="menu-wrapper add-file import" data-action="showIcons">
+
+                <span class="icon">
+                    <span class="icon-hexagon"></span>
+                    <span class="icon-add"></span>
+                </span>
+                <%= __("showIcons")%>
+
+            </div>
             <!-- file add -->
 
         </div>
Index: src/js/JEditor/FilePanel/Views/CollectionDetailView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/CollectionDetailView.js	(révision 14196)
+++ src/js/JEditor/FilePanel/Views/CollectionDetailView.js	(révision 14197)
@@ -12,6 +12,8 @@
     "JEditor/FilePanel/Models/FileCollection",
     "JEditor/Commons/Files/Views/FileSelectorDialog",
     "JEditor/FilePanel/Views/CollectionTitleView",
+    "JEditor/Commons/Svgs/Views/SelectLabelView",
+    "JEditor/FilePanel/Mixins/SvgLabelsHandlerMixin",
     "i18n!../nls/i18n",
     //not in params
     "jqueryPlugins/uploader"
@@ -28,8 +30,10 @@
         FileCollection,
         FileSelectorDialog,
         CollectionTitleView,
+        SelectLabelView,
+        SvgLabelsHandlerMixin,
         translate) {
-    var CollectionDetailView = ListView.extend({
+    var CollectionDetailView = ListView.extend(_.extend({}, SvgLabelsHandlerMixin, {
         selected: {},
         selectedLength: 0,
         attributes: {
@@ -44,7 +48,8 @@
             'uploadercomplete .group-content': 'uploadercomplete',
             'click [data-action="showuploader"]': 'showUploader',
             'sortstop .group-content': 'onSortStop',
-            'click .file .action.replaceFile[data-cid]': 'replaceFile'   
+            'click .file .action.replaceFile[data-cid]': 'replaceFile',
+            'click [data-action="showIcons"]': 'showIcons',   
         },
         initialize: function() {
             this._super();
@@ -56,6 +61,7 @@
                 model: this.model,
                 languages : this.options.languages
             });
+            this.initializeIcons();
         },
         toggleSelected: function(e) {
             var $target = $(e.currentTarget);
@@ -128,6 +134,13 @@
             }
             return false;
         },
+        handleImportLabelSuccess: function(data) {
+            var file = new File(data);
+            this.collection.add(file);
+            this.trigger(Events.ListViewEvents.UPLOADER_COMPLETE, file, this.model);
+             // sauver le model pour persister dans la collection
+            this.model.save();
+        },
         /**
          * affichage de la message d'erreur
          * @param {*} msg 
@@ -180,8 +193,9 @@
                 selectFileView.imagesOnly();
                 selectFileView.open();
             return false;
-        },  
-    });
+        }, 
+
+    }));
     Events.extend({
         ListViewEvents: {
             GOTO_COLLECTIONLIST: 'clickCollection',
Index: src/less/imports/filePanel.less
===================================================================
--- src/less/imports/filePanel.less	(révision 14196)
+++ src/less/imports/filePanel.less	(révision 14197)
@@ -884,7 +884,6 @@
     opacity: 1;
 }
 .menu-wrapper.add-file {
-    width: 91px  !important;
     height: 91px !important;
     padding: 14px !important;
     border: 1px solid #ddd;
@@ -892,8 +891,17 @@
     line-height: 14px !important;
     font-size: 12px !important;
     text-align: center;
-
+    &.upload {
+        // Specific styles for upload1
+        width: 180px  !important;
+    }
+    
+    &.import {
+        width: 91px  !important;
+        // Specific styles for upload2
+      }
   }
+  
   .menu-wrapper.add-file> .icon > .icon-add {
     position: absolute;
     font-size: 14px;
