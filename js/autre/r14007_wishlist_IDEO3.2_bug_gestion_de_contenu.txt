Revision: r14007
Date: 2025-03-27 12:52:47 +0300 (lkm 27 Mar 2025) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: bug gestion de contenu

## Files changed

## Full metadata
------------------------------------------------------------------------
r14007 | sraz<PERSON><PERSON><PERSON>oa | 2025-03-27 12:52:47 +0300 (lkm 27 Mar 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Models/ImageOptions.js

wishlist IDEO3.2: bug gestion de contenu
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Models/ImageOptions.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Models/ImageOptions.js	(révision 14006)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Models/ImageOptions.js	(révision 14007)
@@ -49,7 +49,8 @@
                 }
             },
             onLoad: function() {
-                this.file = this.fileCollection.get(this.file) || new Image(this.file);
+                if (!(this.file instanceof File)) 
+                    this.file = this.fileCollection.get(this.file) || new Image(this.file);
                 if (!(this.link instanceof Link))
                     this.link = new Link(this.link, {
                         context: this.file
