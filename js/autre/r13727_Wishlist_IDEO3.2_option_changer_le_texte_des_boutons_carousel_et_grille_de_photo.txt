Revision: r13727
Date: 2025-01-20 14:54:18 +0300 (lts 20 Jan 2025) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
Wishlist IDEO3.2: option changer le texte des boutons carousel et grille de photo(partie js suite)

## Files changed

## Full metadata
------------------------------------------------------------------------
r13727 | srazanandralisoa | 2025-01-20 14:54:18 +0300 (lts 20 Jan 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js

Wishlist IDEO3.2: option changer le texte des boutons carousel et grille de photo(partie js suite)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js	(révision 13726)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js	(révision 13727)
@@ -39,7 +39,9 @@
        "selectTypeLink"        :   "Select the type of link you want",
        "LinkImage"             :   "Add link to image",
        "LinkText"              :   "Add link to text",
-       "ButtonReadMore"        :   "Add a button 'Visit the page'",
+       "ButtonReadMore"        :   "Add link to button",
+       "ButtonText"            :   "Visit the page",
+       "buttonTextLegend"      :   "Button text",
        "carrouselHeight"       :   "Number of images",
        "carrouselHeightDesc"   :   "Drag to adjust the number of images displayed",
        "carrouselStyleAffichage"   :    "Image style",
