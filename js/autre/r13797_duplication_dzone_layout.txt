Revision: r13797
Date: 2025-02-06 08:55:36 +0300 (lkm 06 Feb 2025) 
Author: t<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
duplication dzone layout

## Files changed

## Full metadata
------------------------------------------------------------------------
r13797 | trajaonarivelo | 2025-02-06 08:55:36 +0300 (lkm 06 Feb 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/dev/#librairies/module/Gestioncontenu/autoload_classmap.php
   M /branches/ideo3_v2/dev/#librairies/module/Gestioncontenu/config/acl.config.php
   M /branches/ideo3_v2/dev/#librairies/module/Gestioncontenu/config/module.config.php
   A /branches/ideo3_v2/dev/#librairies/module/Gestioncontenu/src/Gestioncontenu/Controller/LayoutContentCopyController.php
   M /branches/ideo3_v2/dev/#librairies/vendor/CoreAccess/config/acl.config.php
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Zones/Templates/PageLayoutCollectionView.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Zones/Templates/ZoneToolbox.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Zones/Views/PageLayoutCollectionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Zones/Views/ZoneToolBox.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Zones/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Zones/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Zones/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Views/PageView.js
   M /branches/ideo3_v2/integration/src/less/imports/page_panel/module/checkbox.less

duplication dzone layout
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Zones/Templates/PageLayoutCollectionView.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Zones/Templates/PageLayoutCollectionView.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Zones/Templates/PageLayoutCollectionView.html	(révision 13797)
@@ -0,0 +1,50 @@
+<div id="zone-appliquer-panel" class="version-collection scroll-container">
+    <header class="panel-head">
+        <span class="icon icon-rotation-right" ></span>
+        <h1 class="panel-name"><%= __("appliquerLayoutTitre")%></h1>
+    </header>
+    <div class="panel-content active">
+        <div class="panel-content-intro">
+            <%= __("appliquerLayoutDescription")%> <strong id="nom_zone"><%= nomZone %></strong>
+        </div>
+    </div>
+    <div class="option-content availables">
+        <div class="appliquer-layout">
+            <ul>
+                <% layouts.forEach(function(item) {
+                    if( idCurrentLayout != item.id ){
+                    %>
+                    <li>
+                        <input type="checkbox" class="layoutchoix blue-bg" id="layout_<%= item.id %>" name="layout_<%= item.id %>" value="<%= item.id %>">
+                        <label for="layout_<%= item.id %>">
+                            <span class="checkbox-wrapper">
+                                <span class="icon-unchecked"></span>
+                                <span class="icon-checked"></span>
+                            </span>
+                            <span class="text"><strong><%= item.name %></strong></span>
+                        </label>
+                    </li>
+                <%      }
+                    }); 
+                 %>
+            </ul>
+        
+        </div>
+    </div>
+    <footer class="foot">
+        <div class="button-group save-or-cancel">
+            <a class="button cancel" data-action="cancel">
+                <span class="wrapper">
+                    <span class="icon"></span>
+                    <span class="text"><%= __("appliquerLayoutAnnuler")%></span>
+                </span>
+            </a>
+            <a class="button save" data-action="save">
+                <span class="wrapper">
+                    <span class="icon"></span>
+                    <span class="text"><%= __("appliquerLayoutAppliquer")%></span>
+                </span>
+            </a>
+        </div>
+    </footer>
+</div>
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Zones/Templates/ZoneToolbox.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Zones/Templates/ZoneToolbox.html	(révision 13796)
+++ src/js/JEditor/PagePanel/Contents/Zones/Templates/ZoneToolbox.html	(révision 13797)
@@ -1,5 +1,10 @@
 <span class="label"><%=__("zoneToolboxLabel")%></span>
 <div class="btn-group <%=className%>">
     <button type="button" class="btn btn-default template-link-indicator <%=zone.customized?'clickable':''%>"><span class="icon <%= (zone.customized?'icon-layout-broken':'icon-layout-ok')%>"></span><% if(zone.customized){%><span class="label"><%=__('backToTemplate')%></span><% } %></button>
+    <% if( type == 'template' ) { %>
+        <button id="appliquer-aux-layout" class="btn btn-default button appliquer view" type="button" style="color: #27808c;">
+            <span class="icon icon-rotation-right"></span><span class="label"><%= __("appliquerLayoutTitre")%></span>
+        </button>
+    <% } %>
     <button type="button" class="btn btn-default add-content" id="available-blocks-trigger"><span class="icon icon-add"></span><span class="label"><%=__("addContent")%></span></button>
 </div>
Index: src/js/JEditor/PagePanel/Contents/Zones/Views/ZoneToolBox.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Zones/Views/ZoneToolBox.js	(révision 13796)
+++ src/js/JEditor/PagePanel/Contents/Zones/Views/ZoneToolBox.js	(révision 13797)
@@ -9,6 +9,10 @@
 ],
         function (BabblerView, Events, SaveButton, SwitchVersionButton, ZoneDropdown, template, translate) {
             var ZoneToolbox = BabblerView.extend({
+                type: "",
+                setType: function(t){
+                    this.type = t;
+                },
                 className: "zone-toolbox",
                 events: {'click .template-link-indicator.clickable': 'uncustomize'},
                 initialize: function () {
@@ -31,7 +35,7 @@
                 render: function () {
                     this._super();
                     this.undelegateEvents();
-                    this.$el.html(this._template({zone: this.model, className: this.app.user.can("uncustomize_zone") ? "" : "no-uncustomize"}));
+                    this.$el.html(this._template({type: this.type , zone: this.model, className: this.app.user.can("uncustomize_zone") ? "" : "no-uncustomize"}));
                     this.$('.add-content').before(this.childViews.saveButton.el);
                     if (this.app.user.can("restore_zone")) {
                         this.$('.btn-group').prepend(this.childViews.switchVersionButton.el);
Index: src/js/JEditor/PagePanel/Views/PageView.js
===================================================================
--- src/js/JEditor/PagePanel/Views/PageView.js	(révision 13796)
+++ src/js/JEditor/PagePanel/Views/PageView.js	(révision 13797)
@@ -15,12 +15,13 @@
     "JEditor/PagePanel/Contents/Zones/Models/Zone",
     "JEditor/PagePanel/Utils/PageUtils",
     "i18n!../nls/i18n",
+    "JEditor/PagePanel/Contents/Zones/Views/PageLayoutCollectionView",
     //not in params
     "jqueryPlugins/dropdown",
     "jqueryPlugins/affix"
      
 ], function ($, _, pageView, Events, PageView, BabblerView, PageTitleField,
-        PagePreview, ContentZoneView, LoaderView, ZoneToolbox, ContentLanguageList, PageCollection, Zone, PageUtils, translate) {
+        PagePreview, ContentZoneView, LoaderView, ZoneToolbox, ContentLanguageList, PageCollection, Zone, PageUtils, translate, PageLayoutCollectionView) {
     /**
      * Vue de la page courante
      * @class PageView
@@ -37,7 +38,8 @@
                         events: {
                             'click .btn.preview': 'preview',
                             'click a[data-action]': '_onActionClick',
-                            'click .save-version': 'usePreviousVersion'
+                            'click .save-version': 'usePreviousVersion',
+                            'click #appliquer-aux-layout':'appliquerAuxLayout'
                         },
                         _loaded: 0,
                         attributes: {
@@ -47,6 +49,7 @@
                         fadeInEffect: "fadeIn",
                         zonesFetched: false,
                         selectedZoneVersion: null,
+                        listeLayouts: null,
                         initialize: function () {
                             this._super();
                             this.currentZoneID = this.options.zoneID || null;
@@ -60,11 +63,32 @@
                                 this.zoneToolbox = new ZoneToolbox({
                                     zones: this.model.zones
                                 });
+                                
+                                // type ( page ou layout )
+                                var modelJson = this.model.toJSON();
+                                this.zoneToolbox.setType(modelJson.type);
+                                // type
+                                
                                 this.listenTo(this.zoneToolbox.childViews.zoneDropdown, Events.ChoiceEvents.SELECT, this.onZoneSelect);
                             }
                             this.pagePreview = new PagePreview();
                             //this.on(Events.LoadEvents.LOAD_SUCCESS, this.render);
                         },
+                        appliquerAuxLayout: function(){
+
+                            var pageLayoutCollectionView = new PageLayoutCollectionView();
+                            pageLayoutCollectionView.setListeLayouts( this.listeLayouts );
+                            pageLayoutCollectionView.setNomZone(this.zoneToolbox.childViews.zoneDropdown._currentLabel);
+                            pageLayoutCollectionView.setRefZoneName(this.zoneToolbox.childViews.zoneDropdown.current.attributes.refZoneName);
+                            pageLayoutCollectionView.setJsonZone( JSON.stringify(this.currentZone) );
+                            pageLayoutCollectionView.setIdCurrentLayout(this.model.attributes.id ); 
+                            pageLayoutCollectionView.setPageView( this );
+                            this.pagePanel.renderRightPanel( pageLayoutCollectionView , 'noRender');
+                            this.pagePanel.rightPanelView.showContent( pageLayoutCollectionView );
+                            this.pagePanel.rightPanelView.showPanel();
+                            
+                            return false;
+                        },
                         /*----------------------------
                          * test la hauteur de fenètre
                          * return true si mise en page 'petite hauteur'
@@ -263,6 +287,7 @@
                          * met à jour l'affichage
                          */
                         render: function () {
+                            this.listeLayouts = PageCollection.getInstance().where({type: "template", lang: this.model.lang});
                             
                             this.undelegateEvents();
                             $('.main-list-page').hide();
Index: src/less/imports/page_panel/module/checkbox.less
===================================================================
--- src/less/imports/page_panel/module/checkbox.less	(révision 13796)
+++ src/less/imports/page_panel/module/checkbox.less	(révision 13797)
@@ -80,4 +80,34 @@
 			color: #fff;
 		}
 	}
+}
+
+.appliquer-layout{
+	input[type='checkbox']:checked {
+			accent-color: #34d399;
+	}
+	ul {
+		list-style: none;
+	}
+	.text{
+		text-align: left !important;
+  		left: 60px !important;
+	}
+}
+
+#zone-appliquer-panel{
+	.icon-rotation-right
+	{
+		display: inline-flex;
+	}
+	ul{
+		padding: 0 0 0 0px;
+	}
+	.panel-content{
+		width: 350px !important;
+	}
+	.foot{
+		position: relative !important;
+		top: 20px;
+	}
 }
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Zones/Views/PageLayoutCollectionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Zones/Views/PageLayoutCollectionView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Zones/Views/PageLayoutCollectionView.js	(révision 13797)
@@ -0,0 +1,120 @@
+define([
+    "jquery",
+    "underscore",
+    "JEditor/Commons/Ancestors/Views/View",
+    "text!JEditor/PagePanel/Contents/Zones/Templates/PageLayoutCollectionView.html",
+    "i18n!JEditor/PagePanel/Contents/Zones/nls/i18n",
+    "collection!JEditor/Commons/Pages/Models/PageCollection",
+], function ($, _, View, tpl, translate, PageCollection) {
+    
+    /**
+     * Vue qui gère les layout
+     * 
+     * @type PageLayoutCollectionView
+     */
+    var PageLayoutCollectionView = View.extend({
+        
+        events: {
+            'click .save': 'copyLayoutContent',
+            'click .cancel': 'closeRightPanel'
+        },
+        pageView: null,
+        listeLayouts: null,
+        nomZone: null,
+        refZoneName: null,
+        jsonZone: null,
+        idCurrentLayout: null,
+        setJsonZone: function(json){
+            this.jsonZone = json;
+        },
+        setListeLayouts: function(liste){
+            this.listeLayouts = liste;
+        },
+        setNomZone: function(nom){
+            this.nomZone = nom;
+        },
+        setRefZoneName: function(r){
+            this.refZoneName = r ;
+        },
+        /*
+        setIdCurrentDzone: function(id){
+            this.idCurrentDzone = id;
+        },
+        */
+        setIdCurrentLayout: function(id){
+            this.idCurrentLayout = id ;
+        },
+        setPageView: function(p){
+            this.pageView = p ;
+        },
+        closeRightPanel: function(){
+            this.pageView.pagePanel.rightPanelView.hidePanel();
+        },
+        copyLayoutContent: function(){
+
+            var listeIdsLAyoutSelectionne = [];
+            $('input[type="checkbox"].layoutchoix').each(function() {
+                if ($(this).prop('checked')) {
+                    listeIdsLAyoutSelectionne.push($(this).val());
+                }
+            });
+
+            // envoie des donnees vers php
+            var formData = new FormData();
+            for (var i = 0; i < listeIdsLAyoutSelectionne.length; i++) 
+            {
+                formData.append('liste[]', listeIdsLAyoutSelectionne[i]);
+            }
+            formData.append('refZoneName',this.refZoneName);
+            formData.append('json',this.jsonZone);
+            
+            var pageMere = this.pageView;
+            pageMere.onLoadStart();
+            $.ajax({
+                url: __IDEO_API_PATH__+ '/layout-copy' , 
+                type: "POST",
+                data: formData,
+                processData: false, // Nécessaire pour traiter FormData
+                contentType: false, // Nécessaire pour FormData
+                success: function (response) {
+                    pageMere.onLoadSuccess();
+                },
+                error: function (xhr, status, error) {
+                    pageMere.onLoadError();
+                },
+              });
+            
+            return false;
+            
+        },
+        /**
+         * Initialisation de la view
+         * 
+         * @returns {PageLayoutCollectionView}
+         */
+        initialize: function () {
+            View.prototype.initialize.apply(this, arguments);
+            this.template = this.buildTemplate(tpl, translate);
+            
+            return this;
+        },
+        
+        /**
+         * Rendu de la view
+         * 
+         * @returns {PageLayoutCollectionView}
+         */
+        render: function () {
+            this.undelegateEvents();
+            this.$el.addClass('available-items');
+            this.$el.html(this.template( { layouts: this.listeLayouts , nomZone: this.nomZone , idCurrentLayout: this.idCurrentLayout } ) );
+            
+            this.delegateEvents();
+            return this;
+        },
+        
+        
+    });
+    
+    return PageLayoutCollectionView;
+});
Index: src/js/JEditor/PagePanel/Contents/Zones/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Zones/nls/fr-ca/i18n.js	(révision 13796)
+++ src/js/JEditor/PagePanel/Contents/Zones/nls/fr-ca/i18n.js	(révision 13797)
@@ -1,5 +1,9 @@
 define({
   "save": "Sauvegarder",
+  "appliquerLayoutTitre": "Appliquer aux layouts",
+  "appliquerLayoutDescription": "Vous avez la possibilité de dupliquer le contenu de la dzone dans les autres layouts. Sélectionnez les layouts auquels vous souhaitez appliquer le contenu de la zone",
+  "appliquerLayoutAnnuler": "Annuler",
+  "appliquerLayoutAppliquer": "Appliquer",
   "saveAction": "Sauvegarde",
   "saveSuccesful": "Le contenu a été sauvegardé avec succès",
   "saveError": "Une erreur s'est produite lors de la sauvegarde de la page",
Index: src/js/JEditor/PagePanel/Contents/Zones/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Zones/nls/fr-fr/i18n.js	(révision 13796)
+++ src/js/JEditor/PagePanel/Contents/Zones/nls/fr-fr/i18n.js	(révision 13797)
@@ -1,5 +1,9 @@
 define({
   "save": "Sauvegarder",
+  "appliquerLayoutTitre": "Appliquer aux layouts",
+  "appliquerLayoutDescription": "Vous avez la possibilité de dupliquer le contenu de la dzone dans les autres layouts. Sélectionnez les layouts auquels vous souhaitez appliquer le contenu de la zone",
+  "appliquerLayoutAnnuler": "Annuler",
+  "appliquerLayoutAppliquer": "Appliquer",
   "saveAction": "Sauvegarde",
   "saveSuccesful": "Le contenu a été sauvegardé avec succès",
   "saveError": "Une erreur s'est produite lors de la sauvegarde de la page",
Index: src/js/JEditor/PagePanel/Contents/Zones/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Zones/nls/i18n.js	(révision 13796)
+++ src/js/JEditor/PagePanel/Contents/Zones/nls/i18n.js	(révision 13797)
@@ -1,6 +1,10 @@
 define({
   "root": {
     "save": "Save",
+    "appliquerLayoutTitre": "Apply to layouts",
+    "appliquerLayoutDescription": "You can duplicate the dzone content in other layouts. Select the layouts to which you wish to apply the content of the zone",
+    "appliquerLayoutAnnuler": "Cancel",
+    "appliquerLayoutAppliquer": "Apply",
     "saveAction": "Save",
     "saveSuccesful": "The content has been successfully saved",
     "saveError": "An error occurred while saving the page",
