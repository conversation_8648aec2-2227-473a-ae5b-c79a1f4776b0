Revision: r11909
Date: 2024-02-16 18:58:46 +0300 (zom 16 Feb 2024) 
Author: mpartaux 

## Commit message
update options styles

## Files changed

## Full metadata
------------------------------------------------------------------------
r11909 | mpartaux | 2024-02-16 18:58:46 +0300 (zom 16 Feb 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonStyleOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Templates/imageStyles.html
   M /branches/ideo3_v2/integration/src/less/imports/button_block/styleOptions.less
   M /branches/ideo3_v2/integration/src/less/imports/page_panel/module/block-options/advanced-option.less
   M /branches/ideo3_v2/integration/src/less/imports/page_panel/module/block-options/inline-block-label.less
   M /branches/ideo3_v2/integration/src/less/imports/page_panel/module/radio.less
   M /branches/ideo3_v2/integration/src/less/imports/panel.less
   M /branches/ideo3_v2/integration/src/less/imports/panel_map_styles.less

update options styles
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonStyleOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonStyleOption.html	(révision 11908)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonStyleOption.html	(révision 11909)
@@ -30,6 +30,117 @@
     </article>
 </div> 
 
+<div class="panel-option-container animated button-color">
+    <article class="panel-option">
+        <header>
+            <h3 class="option-name">
+                <span class="icon-drop"></span>
+                <%=__("colorButton")%>
+            </h3>
+            <span class="panel-content-legend"> <%=__("colorButtonLegend")%></span>
+        </header>
+        <div class="category-content radio-transformed">
+            <%  var _id= _.uniqueId('sizeButton'); %>
+            <div class="color-radio">
+                <span class="effect-radio pastel <%=(color==='pastel')?'active':''%>" id="radio_color<%=_id%>" data-value="pastel" data-helper="pastel">
+                    <span class="helper">
+                        <span class="help"><%=__("pastel")%></span>
+                        <span class="bottom"></span>
+                    </span>
+                    <span class="container">
+                        <span class="icon-form-long_text"></span>
+                        <span class="switch-container">
+                            <span class="radio">
+                                <span></span>
+                            </span>
+                        </span>
+                    </span>
+                </span>
+            </div> 
+            <div class="color-radio">
+                <span class="effect-radio vibrante <%=(color==='vibrante')?'active':''%>" id="radio_color<%=_id%>" data-value="vibrante" data-helper="vibrante">
+                    <span class="helper">
+                        <span class="help"><%=__("vibrante")%></span>
+                        <span class="bottom"></span>
+                    </span>
+                    <span class="container">
+                        <span class="icon-form-long_text"></span>
+                        <span class="switch-container">
+                            <span class="radio">
+                                <span></span>
+                            </span>
+                        </span>
+                    </span>
+                </span>
+            </div> 
+            <div class="color-radio">
+                <span class="effect-radio contour <%=(color==='contour')?'active':''%>" id="radio_color<%=_id%>" data-value="contour" data-helper="contour">
+                    <span class="helper">
+                        <span class="help"><%=__("contour")%></span>
+                        <span class="bottom"></span>
+                    </span>
+                    <span class="container">
+                        <span class="icon-form-long_text"></span>
+                        <span class="switch-container">
+                            <span class="radio">
+                                <span></span>
+                            </span>
+                        </span>
+                    </span>
+                </span>
+            </div>
+            <div class="color-radio">
+                <span class="effect-radio pastel-lead <%=(color==='pastel-lead')?'active':''%>" id="radio_color<%=_id%>" data-value="pastel-lead" data-helper="pastel-lead">
+                    <span class="helper">
+                        <span class="help"><%=__("pastel-lead")%></span>
+                        <span class="bottom"></span>
+                    </span>
+                    <span class="container">
+                        <span class="icon-form-long_text"></span>
+                        <span class="switch-container">
+                            <span class="radio">
+                                <span></span>
+                            </span>
+                        </span>
+                    </span>
+                </span>
+            </div> 
+            <div class="color-radio">
+                <span class="effect-radio vibrante-lead <%=(color==='vibrante-lead')?'active':''%>" id="radio_color<%=_id%>" data-value="vibrante-lead" data-helper="vibrante-lead">
+                    <span class="helper">
+                        <span class="help"><%=__("vibrante-lead")%></span>
+                        <span class="bottom"></span>
+                    </span>
+                    <span class="container">
+                        <span class="icon-form-long_text"></span>
+                        <span class="switch-container">
+                            <span class="radio">
+                                <span></span>
+                            </span>
+                        </span>
+                    </span>
+                </span>
+            </div> 
+            <div class="color-radio">
+                <span class="effect-radio contour-lead <%=(color==='contour-lead')?'active':''%>" id="radio_color<%=_id%>" data-value="contour-lead" data-helper="contour-lead">
+                    <span class="helper">
+                        <span class="help"><%=__("contour-lead")%></span>
+                        <span class="bottom"></span>
+                    </span>
+                    <span class="container">
+                        <span class="icon-form-long_text"></span>
+                        <span class="switch-container">
+                            <span class="radio">
+                                <span></span>
+                            </span>
+                        </span>
+                    </span>
+                </span>
+            </div>  
+        </div>
+    </article>
+</div>
+
 <div class="panel-option-container animated button-size">
     <article class="panel-option">
         <header>
@@ -244,108 +355,4 @@
         </div>
     </article>
 </div>
-<article class="panel-option animations button-color">
-    <header>
-        <h3 class="option-name"><%=__("colorButton")%></h3>
-        <span class="panel-content-legend"> <%=__("colorButtonLegend")%></span>
-    </header>
-    <div class="category-content radio-transformed">
-        <%  var _id= _.uniqueId('sizeButton'); %>
-        <div class="color-radio">
-            <span class="effect-radio pastel <%=(color==='pastel')?'active':''%>" id="radio_color<%=_id%>" data-value="pastel" data-helper="pastel">
-                <span class="helper">
-                    <span class="help"><%=__("pastel")%></span>
-                    <span class="bottom"></span>
-                </span>
-                <span class="container">
-                    <span class="icon-form-long_text"></span>
-                    <span class="switch-container">
-                        <span class="radio">
-                            <span></span>
-                        </span>
-                    </span>
-                </span>
-            </span>
-        </div> 
-        <div class="color-radio">
-            <span class="effect-radio vibrante <%=(color==='vibrante')?'active':''%>" id="radio_color<%=_id%>" data-value="vibrante" data-helper="vibrante">
-                <span class="helper">
-                    <span class="help"><%=__("vibrante")%></span>
-                    <span class="bottom"></span>
-                </span>
-                <span class="container">
-                    <span class="icon-form-long_text"></span>
-                    <span class="switch-container">
-                        <span class="radio">
-                            <span></span>
-                        </span>
-                    </span>
-                </span>
-            </span>
-        </div> 
-        <div class="color-radio">
-            <span class="effect-radio contour <%=(color==='contour')?'active':''%>" id="radio_color<%=_id%>" data-value="contour" data-helper="contour">
-                <span class="helper">
-                    <span class="help"><%=__("contour")%></span>
-                    <span class="bottom"></span>
-                </span>
-                <span class="container">
-                    <span class="icon-form-long_text"></span>
-                    <span class="switch-container">
-                        <span class="radio">
-                            <span></span>
-                        </span>
-                    </span>
-                </span>
-            </span>
-        </div>
-        <div class="color-radio">
-            <span class="effect-radio pastel-lead <%=(color==='pastel-lead')?'active':''%>" id="radio_color<%=_id%>" data-value="pastel-lead" data-helper="pastel-lead">
-                <span class="helper">
-                    <span class="help"><%=__("pastel-lead")%></span>
-                    <span class="bottom"></span>
-                </span>
-                <span class="container">
-                    <span class="icon-form-long_text"></span>
-                    <span class="switch-container">
-                        <span class="radio">
-                            <span></span>
-                        </span>
-                    </span>
-                </span>
-            </span>
-        </div> 
-        <div class="color-radio">
-            <span class="effect-radio vibrante-lead <%=(color==='vibrante-lead')?'active':''%>" id="radio_color<%=_id%>" data-value="vibrante-lead" data-helper="vibrante-lead">
-                <span class="helper">
-                    <span class="help"><%=__("vibrante-lead")%></span>
-                    <span class="bottom"></span>
-                </span>
-                <span class="container">
-                    <span class="icon-form-long_text"></span>
-                    <span class="switch-container">
-                        <span class="radio">
-                            <span></span>
-                        </span>
-                    </span>
-                </span>
-            </span>
-        </div> 
-        <div class="color-radio">
-            <span class="effect-radio contour-lead <%=(color==='contour-lead')?'active':''%>" id="radio_color<%=_id%>" data-value="contour-lead" data-helper="contour-lead">
-                <span class="helper">
-                    <span class="help"><%=__("contour-lead")%></span>
-                    <span class="bottom"></span>
-                </span>
-                <span class="container">
-                    <span class="icon-form-long_text"></span>
-                    <span class="switch-container">
-                        <span class="radio">
-                            <span></span>
-                        </span>
-                    </span>
-                </span>
-            </span>
-        </div>  
-    </div>
-</article>
+
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Templates/imageStyles.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Templates/imageStyles.html	(révision 11908)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Templates/imageStyles.html	(révision 11909)
@@ -1,4 +1,4 @@
-<style>
+<!-- <style>
    .image-style .option-content .wrapper{
     width: 33.33333333%;
     float: left;
@@ -5,7 +5,7 @@
     text-align: center;
    } 
 
-</style>
+</style> -->
 <div class="panel-option-container animated">
     <article class="panel-option image-style" >
         <header>
Index: src/less/imports/button_block/styleOptions.less
===================================================================
--- src/less/imports/button_block/styleOptions.less	(révision 11908)
+++ src/less/imports/button_block/styleOptions.less	(révision 11909)
@@ -56,6 +56,7 @@
         margin:auto;
         padding:5%;
         background:#2f2f2f;
+        border-radius: 8px;
     }
     .drop-down-wrapper .btn-group,.drop-down-wrapper .btn.dropdown-toggle,.drop-down-wrapper .dropdown-menu {
         width:100%;
@@ -89,13 +90,61 @@
     .category-content.radio-transformed>div{
         width:33.3%;
         float:left;
-        & .color-radio{
+        & .effect-radio{
+
+            margin: 5px auto;
+            height: 40px;
+            width: 100px;
+
+            &.active {
+                color: #0b2428;
+                box-shadow: 0px 0px 0px 2px #0b2428, 0px 0px 0px 5px #34d399;
+                border-radius: 4px;
+            }
+
+            & .helper {
+                color: #fff;
+            }
+
             & .container{
                 background:#222222;
+                display: flex;
+                justify-content: end;
             }
             &.active .container{
                 background:@pageColor;
             }
+            & .switch-container {
+                display: none;
+                // display: flex;
+                // height: auto;
+                // line-height: normal;
+                // padding-inline: 5px;
+                // align-items: center;
+                // justify-content: center;
+                // position: relative;
+                // width: auto;
+
+                // & .radio {
+                //     margin-top: auto;
+                // }
+            }
+            & .icon-form-long_text {
+                flex-grow: 1;
+                margin-top: 0;
+                display: inline-flex;
+                justify-content: center;
+                align-items: center;
+            }
+            & .icon {
+                flex-grow: 1;
+                display: flex;
+                justify-content: center;
+                align-items: center;
+                height: 100%;
+                margin: 0;
+                position: initial;
+            }
         }
         & .container{
 
Index: src/less/imports/page_panel/module/block-options/advanced-option.less
===================================================================
--- src/less/imports/page_panel/module/block-options/advanced-option.less	(révision 11908)
+++ src/less/imports/page_panel/module/block-options/advanced-option.less	(révision 11909)
@@ -17,7 +17,7 @@
 	[class^='accent'],
 	[class^='lead'],
 	[class^='contrast'] {
-		display: block;
+		// display: block;
 		height: 100%;
 		border-radius: 4px;
 		overflow: hidden;
@@ -24,6 +24,7 @@
 		position: relative;
 
 		display: flex;
+		padding: 4px 0;
 	}
 
 	.container { // default color button
Index: src/less/imports/page_panel/module/block-options/inline-block-label.less
===================================================================
--- src/less/imports/page_panel/module/block-options/inline-block-label.less	(révision 11908)
+++ src/less/imports/page_panel/module/block-options/inline-block-label.less	(révision 11909)
@@ -1,11 +1,11 @@
 .inline-block-label { 
 	display: block;
 	float: left;
-	width: 20%;
-	margin-right: 6.5% !important;
+	width: 18%;
+	margin: 0 3% !important;
 	.border-radius(3px)!important;
 
-	overflow: hidden;
+	// overflow: hidden;
 	cursor: pointer;
 
 	font-weight: normal !important;
@@ -26,6 +26,7 @@
 	display: block;
 	background-color: @greyXD;
 	.transition-background();
+	border-radius: 4px;
 
 	[class^='i-'], [class^='icon-'] {
 		display: block;
@@ -54,21 +55,22 @@
 }
 
 .inline-block-label__bottom {
-	display: block;
-	background-color: #fff;
+	// display: block;
+	// background-color: #fff;
 
-	.icon-radio-inactive { display: block; }
-	.icon-radio-active	{ display: none; }
+	// .icon-radio-inactive { display: block; }
+	// .icon-radio-active	{ display: none; }
 
-	.icon-radio-inactive,
-	.icon-radio-active {
-		margin: 0 auto;
-		padding: .3em 0;
+	// .icon-radio-inactive,
+	// .icon-radio-active {
+	// 	margin: 0 auto;
+	// 	padding: .3em 0;
 		
-		text-align: center;
-		font-size: 16px;
-		color: @black;
+	// 	text-align: center;
+	// 	font-size: 16px;
+	// 	color: @black;
 
-		.transition-color();
-	}
+	// 	.transition-color();
+	// }
+	display: none;
 }
\ No newline at end of file
Index: src/less/imports/page_panel/module/radio.less
===================================================================
--- src/less/imports/page_panel/module/radio.less	(révision 11908)
+++ src/less/imports/page_panel/module/radio.less	(révision 11909)
@@ -24,11 +24,10 @@
 	}
 }
 
-
 // radio modifiers
 input[type='radio']:checked + .inline-label__container {
 
-	background-color: @main; 
+	background-color: @main;
 
 	.radio-wrap { background-color: lighten(@main, 10%); }
 
@@ -43,6 +42,8 @@
 
 input[type='radio']:checked + .inline-block-label__top {
 	background-color: @main;
+	box-shadow: 0px 0px 0px 2px #0b2428, 0px 0px 0px 5px #34d399;
+  border-radius: 4px;
 }
 
 input[type='radio']:checked + .inline-block-label__top + .inline-block-label__bottom {
Index: src/less/imports/panel.less
===================================================================
--- src/less/imports/panel.less	(révision 11908)
+++ src/less/imports/panel.less	(révision 11909)
@@ -10,30 +10,55 @@
 @import "effectradio.less";
 @import "panel_image_image.less";
 
-.panel-container{
-    input[type="text"]+label,label+input[type="text"],textarea{
-        border:none;
+.panel-container {
+
+    input[type="text"]+label,
+    label+input[type="text"],
+    textarea {
+        border: none;
         display: block;
-        width:100%;
+        width: 100%;
     }
-    input[type="text"]{min-height:20px;}
-    input[type="text"], textarea{
-        background:white;
+
+    input[type="text"] {
+        min-height: 20px;
+    }
+
+    input[type="text"],
+    textarea {
+        background: white;
         .border-radius();
-        padding:5px;
-        &:focus,&:active{.box-shadow(0 0 10px @pageColor);outline-width: 0; }
+        padding: 5px;
+
+        &:focus,
+        &:active {
+            .box-shadow(0 0 10px @pageColor);
+            outline-width: 0;
+        }
     }
-    textarea{resize:vertical;}
-    label{font-weight:lighter; font-size:0.8em; margin: 10px 0;}
+
+    textarea {
+        resize: vertical;
+    }
+
+    label {
+        font-weight: lighter;
+        font-size: 0.8em;
+        margin: 10px 0;
+    }
 }
 
 //.panel-head + .panel-content{margin-top: 30px;}
-.border-selector{.clearfix();}
-#right-panel footer,#item-config footer{
+.border-selector {
+    .clearfix();
+}
+
+#right-panel footer,
+#item-config footer {
     position: absolute;
-    bottom:20px;
-    left:0;
-    right:0;
+    bottom: 20px;
+    left: 0;
+    right: 0;
     padding: 0;
     height: auto;
     line-height: 100px;
@@ -42,84 +67,107 @@
 }
 
 ////displays
-.column-3{
+.column-3 {
     .clearfix();
-    .column{display: block;
-            float:left;
-            width:33%;
-            text-align: center;
+
+    .column {
+        display: block;
+        float: left;
+        width: 33%;
+        text-align: center;
     }
-    .row{.clearfix();}
+
+    .row {
+        .clearfix();
+    }
 }
-.column-5{
+
+.column-5 {
     .clearfix();
-    .column{
-        display:block;
-        float:left;
-        width:20%;
+
+    .column {
+        display: block;
+        float: left;
+        width: 20%;
         text-align: center;
     }
 }
 
 
-.panel-container{
+.panel-container {
 
-        // Scrollbar Firefox
-        * {
-            scrollbar-color: #999999 #1a1a1a;
-            scrollbar-width: thin;
-        }
-    
-        // Scrollbar Webkit Chrome/Edge/Safari
-        ::-webkit-scrollbar {
-            width: 12px;
-            height: 12px;
-        }
-    
-        ::-webkit-scrollbar-track {
-            border-radius: 0;
-            background: #1a1a1a;
-            border: none;
-        }
-    
-        ::-webkit-scrollbar-thumb {
-            border-radius: 16px;
-            background: #999999;
-            background-clip: padding-box;
+    // Scrollbar Firefox
+    * {
+        scrollbar-color: #999999 #1a1a1a;
+        scrollbar-width: thin;
+    }
+
+    // Scrollbar Webkit Chrome/Edge/Safari
+    ::-webkit-scrollbar {
+        width: 12px;
+        height: 12px;
+    }
+
+    ::-webkit-scrollbar-track {
+        border-radius: 0;
+        background: #1a1a1a;
+        border: none;
+    }
+
+    ::-webkit-scrollbar-thumb {
+        border-radius: 16px;
+        background: #999999;
+        background-clip: padding-box;
+        border: 3px solid #1a1a1a;
+        box-shadow: none;
+        min-height: 50px;
+
+        &:hover {
+            background: #5b5b5b;
+            background-clip: border-box;
             border: 3px solid #1a1a1a;
-            box-shadow: none;
-            min-height: 50px;
-    
-            &:hover {
-                background: #5b5b5b;
-                background-clip: border-box;
-                border: 3px solid #1a1a1a;
-            }
         }
-    
+    }
+
     .tabbed-view {
-        overflow:hidden;
+        overflow: hidden;
     }
-    .tabbed-view,.available-items{
-        position:absolute;
-        top:20px;
-        left:20px;
-        right:5px;
-        bottom:20px;
+
+    .tabbed-view,
+    .available-items {
+        position: absolute;
+        top: 20px;
+        left: 20px;
+        right: 5px;
+        bottom: 20px;
     }
+
     background: @headerColor;
     color: @white;
-    font-family: 'Raleway', sans-serif;
+    font-family: 'Raleway',
+    sans-serif;
+
     //height:100%;
-    a,a:visited,a:active{color:inherit; text-decoration:none;}
-    a:hover,a.active{color:@pageColor;}
+    a,
+    a:visited,
+    a:active {
+        color: inherit;
+        text-decoration: none;
+    }
+
+    a:hover,
+    a.active {
+        color: @pageColor;
+    }
+
     // padding:20px;
     min-width:380px;
 }
+
 // UIs////////////////////
 
-.drop-down{
-    .arrow{
+.drop-down {
+    .arrow {
         .arrow-down();
 
         position: absolute;
@@ -126,25 +174,40 @@
         right: 10px;
         top: 15px;
     }
+
     display:block;
     left:0;
     padding:10px;
-    ul.form-list{
+
+    ul.form-list {
         list-style-type: none;
-        padding:0;
-        li{padding:10px;}
+        padding: 0;
+
+        li {
+            padding: 10px;
+        }
     }
 }
 
 
-span.icon{display:block}
+span.icon {
+    display: block
+}
 
-span.close{
-    border:1px solid @ui-grey;
-    display:block;
-    width:14px; height:14px;
+span.close {
+    border: 1px solid @ui-grey;
+    display: block;
+    width: 14px;
+    height: 14px;
     .border-radius();
-    .icon{.sprite(4,0); width:10px; height:10px; margin:2px}
+
+    .icon {
+        .sprite(4, 0);
+        width: 10px;
+        height: 10px;
+        margin: 2px
+    }
+
     cursor:pointer;
 }
 
@@ -154,8 +217,9 @@
     height: 10px;
     .border-radius(5px);
     margin-right: 5px;
-    .hsv-background(@panel-font-grey;+10);
-    span{
+    .hsv-background(@panel-font-grey; +10);
+
+    span {
         display: block;
         float: left;
         width: 8px;
@@ -166,33 +230,79 @@
         .box-shadow(1px);
     }
 }
-.effect-switcher.active .switch, .switch.active{
-    span{float:right;}
+
+.effect-switcher.active .switch,
+.switch.active {
+    span {
+        float: right;
+    }
+
     background-color:@pageColor;
 }
 
 //sliders
-.slider-container{
-    height:20px;
-    margin:20px 0;
-    position:relative;
-    span.icon{display:block; height:20px; width:20px;position:absolute; top:0;}
-    .ui-slider{background:#000000; .border-radius(); height:5px; margin:0 30px;top:7px; position:relative;}
-    span.less.icon,.icon-less{left:0; position:absolute; font-size:20px; top:0;}
-    span.more.icon,.icon-more{right:0; position:absolute; font-size:20px;top:0;}
-    .ui-slider-range{
-        background:@pageColor;
-        height:100%;
+.slider-container {
+    height: 20px;
+    margin: 20px 0;
+    position: relative;
+
+    span.icon {
+        display: block;
+        height: 20px;
+        width: 20px;
+        position: absolute;
+        top: 0;
+    }
+
+    .ui-slider {
+        background: #000000;
         .border-radius();
+        height: 5px;
+        margin: 0 30px;
+        top: 7px;
+        position: relative;
     }
-    .ui-slider-handle{display:block; width:15px; height:15px; .border-radius(0.6em);.hsv-background(@pageColor, -10); left:50%; position:relative; margin-left:-5px;cursor:pointer;}
+
+    span.less.icon,
+    .icon-less {
+        left: 0;
+        position: absolute;
+        font-size: 20px;
+        top: 0;
+    }
+
+    span.more.icon,
+    .icon-more {
+        right: 0;
+        position: absolute;
+        font-size: 20px;
+        top: 0;
+    }
+
+    .ui-slider-range {
+        background: @pageColor;
+        height: 100%;
+        .border-radius();
+    }
+
+    .ui-slider-handle {
+        display: block;
+        width: 15px;
+        height: 15px;
+        .border-radius(0.6em);
+        .hsv-background(@pageColor, -10);
+        left: 50%;
+        position: relative;
+        margin-left: -5px;
+        cursor: pointer;
+    }
 }
 
 
 /////Panel///////////////
 
-.panel-head{
-    h1.panel-name{
+.panel-head {
+    h1.panel-name {
         font-size: 1.3em;
         display: inline-block;
         position: relative;
@@ -199,27 +309,44 @@
         font-weight: 500;
         margin: 0;
     }
+
     .clearfix();
-    span.close{float:right;}
-    h2.panel-content-title{
-        display:inline;
-        font-size:1.2em;
-        font-weight:normal;
+
+    span.close {
+        float: right;
     }
+
+    h2.panel-content-title {
+        display: inline;
+        font-size: 1.2em;
+        font-weight: normal;
+    }
+
     position:relative;
 }
 
 .panel-legend {
-    color:@ui-grey;
-    font-size:0.9em;
+    color: @ui-grey;
+    font-size: 0.9em;
 }
-.panel-content-intro h2{
-    font-weight:200;
+
+.panel-content-intro h2 {
+    font-weight: 200;
 }
-.panel-content-intro h2 .icon{height:25px; width:25px; float: left; margin-right:5px;}
-.fx-panel .panel-content-intro h2 .icon{.sprite(24,3);}
-.tabbed-view{
 
+.panel-content-intro h2 .icon {
+    height: 25px;
+    width: 25px;
+    float: left;
+    margin-right: 5px;
+}
+
+.fx-panel .panel-content-intro h2 .icon {
+    .sprite(24, 3);
+}
+
+.tabbed-view {
+
     // Scrollbar Firefox
     * {
         scrollbar-color: #999999 #1a1a1a;
@@ -254,45 +381,58 @@
     }
 
     ///contenu
-    .panel-content{
-        position:absolute;
-        top:78px;
-        bottom:50px;
-        width:100%;
-        overflow:auto;
-        overflow-x:hidden;
+    .panel-content {
+        position: absolute;
+        top: 78px;
+        bottom: 50px;
+        width: 100%;
+        overflow: auto;
+        overflow-x: hidden;
         box-sizing: border-box;
         padding-right: 3px;
+        padding-bottom: 20px;
         scrollbar-gutter: stable;
 
-        .panel-content-legend{
-            color:@ui-grey;
-            font-size:0.8em;
+        .panel-content-legend {
+            color: @ui-grey;
+            font-size: 0.8em;
         }
+
         ///display when active
         display:none;
-        &.active{display:block;}
+
+        &.active {
+            display: block;
+        }
     }
 }
-.option-title{
+
+.option-title {
     // border-bottom: 1px solid @panel-font-grey;
 }
-.panel-menu,  .panel-option-container, .panel-content-intro{
+
+.panel-menu,
+.panel-option-container,
+.panel-content-intro {
     // border-bottom: 1px solid @panel-font-grey;
-    padding:20px 0;
+    padding: 20px 0;
 }
-.option-content{
+
+.option-content {
     .clearfix();
-    position:relative;
-    .description{
-        position:absolute;
-        line-height:14px;
+    position: relative;
+
+    .description {
+        position: absolute;
+        line-height: 14px;
     }
 }
-.option-content.colors{
+
+.option-content.colors {
     .clearfix();
-    position:relative;
-    .description{
+    position: relative;
+
+    .description {
         position: absolute;
         line-height: 25px;
         top: 25px;
@@ -302,94 +442,128 @@
         color: #7F7F7F;
     }
 }
-.panel-content-intro{
+
+.panel-content-intro {
     padding: 19px 0;
-    p{ margin: 0 }
-    h2 + p{ margin-top: 5px; }
+
+    p {
+        margin: 0
+    }
+
+    h2+p {
+        margin-top: 5px;
+    }
 }
-.panel-content .panel-option-container:last-child{border:none;}
+
+.panel-content .panel-option-container:last-child {
+    border: none;
+}
+
 ///panel option containers
-.panel-option-container{
+.panel-option-container {
 
-    &.switchable{
-        .transform(translate(500px,0));
+    &.switchable {
+        .transform(translate(500px, 0));
         //opacity:0;
         .transition(~"transform .3s linear, width .3s linear");
-        &.active{
-            .transform(translate(0,0));
+
+        &.active {
+            .transform(translate(0, 0));
             //  opacity:1;
             .transition(~"transform .3s linear, opacity .3s linear");
         }
     }
-    .option-title{padding: 20px 0;
-                  .option-icon{float:left; margin-right:10px;}
+
+    .option-title {
+        padding: 20px 0;
+
+        .option-icon {
+            float: left;
+            margin-right: 10px;
+        }
     }
 }
-.available-items{
-    &>.scroll-container{
-        height:100%;
-        & .panel-content{
-            width:305px;
-            margin:auto;
+
+.available-items {
+    &>.scroll-container {
+        height: 100%;
+
+        & .panel-content {
+            width: 305px;
+            margin: auto;
         }
     }
-    header.panel-head{
-        h1.panel-name{
-            left:0;
+
+    header.panel-head {
+        h1.panel-name {
+            left: 0;
         }
-        span.icon-add{
-            color:#515151;
+
+        span.icon-add {
+            color: #515151;
         }
+
         border-bottom:1px solid #515151;
         padding-bottom:20px;
     }
-    h2.option-title{
+
+    h2.option-title {
         font-size: 1.5em;
         text-align: center;
         border: none;
         font-weight: normal;
     }
-    .panel-content{
+
+    .panel-content {
         width: 305px;
         margin: auto;
-        .panel-content-intro{
-            color:#727272;
-            font-size:0.9em;
-            text-align:center;
+
+        .panel-content-intro {
+            color: #727272;
+            font-size: 0.9em;
+            text-align: center;
         }
-        .option-content.availables{
-            & div.items{
+
+        .option-content.availables {
+            & div.items {
                 .clearfix();
-                &>.available.block{
-                    cursor:move;
+
+                &>.available.block {
+                    cursor: move;
                 }
             }
+
             margin-bottom:100px;
         }
     }
 
 }
-div.available.block{
+
+div.available.block {
     width: 97px;
-    height:97px;
+    height: 97px;
     background: #383838;
     margin: 2px;
     color: #ffffff;
     float: left;
-    .container{
-        height:97px;
-        width:97px;
-        display:table-cell;
-        vertical-align:middle;
+
+    .container {
+        height: 97px;
+        width: 97px;
+        display: table-cell;
+        vertical-align: middle;
     }
-    &.ui-draggable-dragging{
-        opacity:0.5;
+
+    &.ui-draggable-dragging {
+        opacity: 0.5;
     }
-    .icon{
-        font-size:25px;
-        text-align:center;
+
+    .icon {
+        font-size: 25px;
+        text-align: center;
     }
-    .text{
+
+    .text {
         display: block;
         text-align: center;
         font-size: 14px;
@@ -396,20 +570,28 @@
         margin: 5px;
     }
 }
-.border-selector{
-    div.first{.columns(3);width:250px;float:left;}
-    div.second{
-        width:115px;
-        float:left;
-        &>.minicolors{
-            margin:10px auto;
-            display:block;
-            width:60px;
-            float:right;
+
+.border-selector {
+    div.first {
+        .columns(3);
+        width: 250px;
+        float: left;
+    }
+
+    div.second {
+        width: 115px;
+        float: left;
+
+        &>.minicolors {
+            margin: 10px auto;
+            display: block;
+            width: 60px;
+            float: right;
         }
-        & .minicolors-panel{
-            top:10px;
-            left:-275px;
+
+        & .minicolors-panel {
+            top: 10px;
+            left: -275px;
         }
     }
 }
@@ -417,15 +599,43 @@
 
 
 ///menu
-nav.panel-menu{
-    &.inactive{visibility:hidden;}
-    padding:0 2rem 0 0; 
-    a{display:inline-block; padding:18px 0 15px;}
+nav.panel-menu {
+    &.inactive {
+        visibility: hidden;
+    }
+
+    padding:0 2rem 0 0;
+
+    a {
+        display: inline-block;
+        padding: 18px 0 15px;
+    }
+
     // a + a {margin-left: 32px;}
-    a span.indic{border: 3px transparent solid; width:0; height:0; border-left: 3px solid #FFFFFF; display:block; float: left;content: ""; position:relative; top:8px;margin-right: 2px;}
-    a:hover span.indic{border-left: 3px solid @pageColor;}
-    a.active span.indic{border-left: 3px transparent solid;border-top: 3px solid @pageColor; margin-right: 5px;top:9px;}
+    a span.indic {
+        border: 3px transparent solid;
+        width: 0;
+        height: 0;
+        border-left: 3px solid #FFFFFF;
+        display: block;
+        float: left;
+        content: "";
+        position: relative;
+        top: 8px;
+        margin-right: 2px;
+    }
 
+    a:hover span.indic {
+        border-left: 3px solid @pageColor;
+    }
+
+    a.active span.indic {
+        border-left: 3px transparent solid;
+        border-top: 3px solid @pageColor;
+        margin-right: 5px;
+        top: 9px;
+    }
+
     display: flex;
     justify-content: space-evenly;
 
@@ -432,286 +642,437 @@
 }
 
 
+.image-style .option-content .wrapper{
+    float: left;
+    text-align: center;
+    width: 18%;
+    margin: 0 3% !important;
+    font-size: .8em;
+} 
 
 
+
 ////Interrupteurs
 
-.effect-switcher,.effect-radio{
+.effect-switcher,
+.effect-radio {
 
-    cursor:pointer;
+    cursor: pointer;
 
 
-    margin:10px auto;
-    position:relative;
+    margin: 10px auto;
+    position: relative;
     text-align: center;
 
-    display:block;
-    width:90px; height:90px;
-    & .radio{.radio_normal();}
-    &.active .radio,&.active:hover .radio{.radio_active();}
-    &:hover .radio{.radio_hover();}
-    .container{background:@panel-font-grey; display:block;overflow:hidden;height:100%; .border-radius(); position:relative;}
-    .helper{
+    display: block;
+    // width: 90px;
+    // height: 90px;
 
-        .hsv-background(@pageColor,-20);
-        font-size:0.8em;
-        padding:10px;
-        position:absolute;
-        width:120px;
-        left:50%;
+    & .radio {
+        .radio_normal();
+    }
+
+    &.active .radio,
+    &.active:hover .radio {
+        .radio_active();
+    }
+
+    &:hover .radio {
+        .radio_hover();
+    }
+
+    .container {
+        background: @panel-font-grey;
+        display: block;
+        overflow: hidden;
+        height: 100%;
+        .border-radius();
+        position: relative;
+
+        display: flex;
+        justify-content: center;
+        align-items: center;
+        padding: 4px 0;
+    }
+
+    .helper {
+
+        .hsv-background(@pageColor, -20);
+        font-size: 0.8em;
+        padding: 10px;
+        position: absolute;
+        width: 120px;
+        left: 50%;
         opacity: 0;
-        margin-left:-65px;
+        margin-left: -65px;
 
         display: none;
         .border-radius();
-        .transition(0.3s opacity linear 0.5s,0.1s top linear 0.5s);
-        .help{display: block;
-              text-align: center;}
-        .bottom{.arrow-down(4px,@pageColor,-20); display:block; position:absolute; bottom:-4px;left:50%; margin-left:-8px;}
+        .transition(0.3s opacity linear 0.5s, 0.1s top linear 0.5s);
+
+        .help {
+            display: block;
+            text-align: center;
+        }
+
+        .bottom {
+            .arrow-down(4px, @pageColor, -20);
+            display: block;
+            position: absolute;
+            bottom: -4px;
+            left: 50%;
+            margin-left: -8px;
+        }
     }
-    &:hover .helper{
-        top:-50px;
-        display:block;
-        opacity:1;
-        .transition(0.3s opacity linear 0.5s ,0.1s top linear 0.5s);
+
+    &:hover .helper {
+        top: -50px;
+        display: block;
+        opacity: 1;
+        .transition(0.3s opacity linear 0.5s, 0.1s top linear 0.5s);
     }
-    &.active .container{
-        background:@pageColor;
+
+    &.active .container {
+        background: @pageColor;
+        box-shadow: 0px 0px 0px 2px #0b2428, 0px 0px 0px 5px #34d399;
     }
 
 
-    .icon{display: block; width:35px; height:35px; top:15px; left:50%; margin-left:-17px;  position:relative;}
-    .switch-container{
-        display:block;
-        height:30px;
-        background:@white;
-        position:absolute;
-        bottom:0;
-        width:100%;
-        line-height: 30px;
-        .switch,.radio{margin:auto;}
+    .icon {
+        display: block;
+        font-size: 1.8em;
+        padding: .3em 0;
+        // width: 35px;
+        // height: 35px;
+        // top: 15px;
+        // left: 50%;
+        // margin-left: -17px;
+        // position: relative;
+    }
 
-        .border-radius(0,0,4px,4px);
+    .switch-container {
+        // display:block;
+        // height:30px;
+        // background:@white;
+        // position:absolute;
+        // bottom:0;
+        // width:100%;
+        // line-height: 30px;
+        // .switch,.radio{margin:auto;}
+
+        // .border-radius(0,0,4px,4px);
+        display: none;
     }
-    &.small{
-        width:60px;
-        height:60px;
-        .switch-container{
-            height:30px;
+
+    &.small {
+        width: 60px;
+        height: 60px;
+
+        .switch-container {
+            height: 30px;
             line-height: 30px;
-            .radio{
-                position:relative;
-                top:5px;
+
+            .radio {
+                position: relative;
+                top: 5px;
             }
         }
     }
 }
+
 .panel-content-intro h2.address {
-    &>span[class^="icon-"]{
-        margin-right:5px;
-        color:#535353;
+    &>span[class^="icon-"] {
+        margin-right: 5px;
+        color: #535353;
     }
 }
-.panel-option{
-    margin-bottom: 1em;
-    header{
+
+.panel-option {
+    // margin-bottom: 1em;
+
+    header {
         // border-bottom: 1px solid #313131;
         border-top: 1px solid #313131;
         padding: 1em 0 0;
         margin-bottom: 1em;
-        h3.option-name{
-            &>span[class^="icon-"]{
-                margin-right:5px;
-                color:#535353;
+
+        h3.option-name {
+            &>span[class^="icon-"] {
+                margin-right: 5px;
+                color: #535353;
             }
+
             font-size:1.2em;
             font-weight: 200;
             margin:0;
         }
-        p.option-description{
-            font-size:0.9em;
-            color:@ui-grey;
-            margin:0;
+
+        p.option-description {
+            font-size: 0.9em;
+            color: @ui-grey;
+            margin: 0;
         }
     }
-    header.inline{
-        border-bottom:none;
-        h3.option-name,p.option-description{display:inline;}
+
+    header.inline {
+        border-bottom: none;
+
+        h3.option-name,
+        p.option-description {
+            display: inline;
+        }
     }
 }
-.dashed-button{
-    border:1px dashed @ui-grey;
+
+.dashed-button {
+    border: 1px dashed @ui-grey;
     display: block;
-    background:transparent;
-    margin:20px auto;
+    background: transparent;
+    margin: 20px auto;
     .border-radius();
     height: 40px;
-    line-height:30px;
-    color:@pageColor;
-    padding:0 20px;
-    font-size:0.8em;
-    &,& *{cursor:pointer;}
+    line-height: 30px;
+    color: @pageColor;
+    padding: 0 20px;
+    font-size: 0.8em;
 
-    &.cancel{
-        & .placeholder{color:@errorColor;}
+    &,
+    & * {
+        cursor: pointer;
+    }
+
+    &.cancel {
+        & .placeholder {
+            color: @errorColor;
+        }
+
         & .placeholder:first-letter {
             display: block;
             float: left;
-            margin-right:10px;
-            font-size:20px;
+            margin-right: 10px;
+            font-size: 20px;
             font-weight: bold;
-            margin-top:-3px;
+            margin-top: -3px;
         }
     }
-    & .placeholder{color:@pageColor; display:block; height:100%;line-height: 40px;}
+
+    & .placeholder {
+        color: @pageColor;
+        display: block;
+        height: 100%;
+        line-height: 40px;
+    }
+
     & .placeholder:first-letter {
         display: block;
         float: left;
-        margin-right:10px;
-        font-size:45px;
-        width:15px;
+        margin-right: 10px;
+        font-size: 45px;
+        width: 15px;
     }
 }
 
 /////////////////////map panel
 
-.panel-head{
-    span.icon.panel-img{ width:23px; height:30px; position:absolute; }
+.panel-head {
+    span.icon.panel-img {
+        width: 23px;
+        height: 30px;
+        position: absolute;
+    }
 }
 
-.map-panel .panel-head{ span.icon.panel-img{ .sprite(16,9); } }
+.map-panel .panel-head {
+    span.icon.panel-img {
+        .sprite(16, 9);
+    }
+}
 
 ////adress
 
 /////Button double
-.button-double{
-    position:relative;
+.button-double {
+    position: relative;
     display: block;
     line-height: 30px;
-    cursor:pointer;
-    font-weight:lighter;
+    cursor: pointer;
+    font-weight: lighter;
     font-size: 0.9em;
-    overflow:hidden;
+    overflow: hidden;
     .clearfix();
-    .hsv-background(@headerColor,+10);
+    .hsv-background(@headerColor, +10);
     .border-radius();
-    .right{
-        display:block;
-        float:right;
-        .hsv-background(@headerColor,+20);
+
+    .right {
+        display: block;
+        float: right;
+        .hsv-background(@headerColor, +20);
         font-weight: bold;
-        width:30px;
+        width: 30px;
         text-align: center;
 
     }
-    .left{text-align:center; position:absolute; width:100%;}
-    &:hover{
+
+    .left {
+        text-align: center;
+        position: absolute;
+        width: 100%;
+    }
+
+    &:hover {
         .hsv-background(@headerColor; +20);
-        & .right{.hsv-background(@headerColor; +10);}
-        & .left{}
-        & .right,.left{color:white;}
+
+        & .right {
+            .hsv-background(@headerColor; +10);
+        }
+
+        & .left {}
+
+        & .right,
+        .left {
+            color: white;
+        }
     }
-    &:hover .right,&:hover .left{color:white;}
 
+    &:hover .right,
+    &:hover .left {
+        color: white;
+    }
+
 }
-.double-input{
-    font-size:0.8em;
+
+.double-input {
+    font-size: 0.8em;
     .border-radius();
-    overflow:hidden;
-    position:relative;
-    &.deletable{
-        .delete{
+    overflow: hidden;
+    position: relative;
+
+    &.deletable {
+        .delete {
             display: inline-block;
-            cursor:pointer;
-            color:@white;
-            position:absolute;
-            right:5px;
-            padding:5px;
-            line-height:5px;
+            cursor: pointer;
+            color: @white;
+            position: absolute;
+            right: 5px;
+            padding: 5px;
+            line-height: 5px;
         }
     }
 
-    &>*{
-        padding:5px;
-        display:block;
-        &.top{
+    &>* {
+        padding: 5px;
+        display: block;
+
+        &.top {
             .hsv-background(@pageColor; -20);
         }
-        &.bottom{
-            .hsv-background(@headerColor,+10);
+
+        &.bottom {
+            .hsv-background(@headerColor, +10);
         }
     }
 }
-.save-or-cancel{
+
+.save-or-cancel {
     overflow: hidden;
     .border-radius();
-    &:hover .text{color:white;}
+
+    &:hover .text {
+        color: white;
+    }
+
     .columns(2);
-    .button{
-        padding:0;
-        line-height:30px;
+
+    .button {
+        padding: 0;
+        line-height: 30px;
         .border-radius(0);
-        .wrapper{
-            display:block;
-            .icon{
-                display:inline-block;
-                position:relative;
 
+        .wrapper {
+            display: block;
+
+            .icon {
+                display: inline-block;
+                position: relative;
+
             }
-            .text{
-                display:inline-block;
-                line-height:1em;
+
+            .text {
+                display: inline-block;
+                line-height: 1em;
             }
         }
     }
-    .cancel{
+
+    .cancel {
         .hsv-background(@pageColor; -20);
-        .icon{
+
+        .icon {
             .cancel-icon();
-            top:2px;
+            top: 2px;
         }
-        &:hover{
+
+        &:hover {
             .hsv-background(@pageColor; -30);
         }
     }
-    .save{
-        background:@pageColor;
-        &:hover{
+
+    .save {
+        background: @pageColor;
+
+        &:hover {
             .hsv-background(@pageColor; -10);
         }
-        .icon{
+
+        .icon {
             .save-icon();
-            top:3px;
+            top: 3px;
         }
     }
 }
-.warning{
+
+.warning {
     .clearfix();
-    margin:10px;
-    .icon{
+    margin: 10px;
+
+    .icon {
         .warning-icon();
-        float:left;
+        float: left;
     }
-    .text{margin-left:40px; display:block; line-height:1em; text-align: justify;}
+
+    .text {
+        margin-left: 40px;
+        display: block;
+        line-height: 1em;
+        text-align: justify;
+    }
 }
 
-.item-picker{
-    width:200px;
+.item-picker {
+    width: 200px;
     margin: auto;
-    .row{
+
+    .row {
         .columns(2);
-        a.form-element{
-            display:block;
-            &, & *{cursor: move;}
-            span.wrapper{
-                position:relative;
-                margin:1px;
-                display:block;
-                width:78px;
-                height:78px;
+
+        a.form-element {
+            display: block;
+
+            &,
+            & * {
+                cursor: move;
+            }
+
+            span.wrapper {
+                position: relative;
+                margin: 1px;
+                display: block;
+                width: 78px;
+                height: 78px;
                 padding: 10px;
             }
-            span.text{
+
+            span.text {
                 font-weight: lighter;
                 font-size: 0.8em;
                 line-height: 1em;
@@ -724,62 +1085,121 @@
                 left: 0;
                 width: 100%;
                 margin: auto;
-                color:#FFFFFF;
-                &:hover{color:#FFFFFF;};
+                color: #FFFFFF;
+
+                &:hover {
+                    color: #FFFFFF;
+                }
+
+                ;
             }
-            .icon{margin:auto;position: relative;top:20px;}
-            &.date .icon{.item-picker-calendar();}
-            &.email .icon{.item-picker-email();}
-            &.tel .icon{.item-picker-phone();top:15px;}
-            &.input .icon{.item-picker-short-text();top:25px;}
-            &.textarea .icon{.item-picker-textarea();}
-            &.select .icon{.item-picker-select();}
-            &.list .icon{.item-picker-people();}
-            &.checkbox .icon{.item-picker-checkbox();}
-            &.radio .icon{.item-picker-radio();}
-            &.div .icon{.item-picker-separator();}
-            &.upload .icon{.item-picker-file();}
-            &.number .icon{.item-picker-number();}
+
+            .icon {
+                margin: auto;
+                position: relative;
+                top: 20px;
+            }
+
+            &.date .icon {
+                .item-picker-calendar();
+            }
+
+            &.email .icon {
+                .item-picker-email();
+            }
+
+            &.tel .icon {
+                .item-picker-phone();
+                top: 15px;
+            }
+
+            &.input .icon {
+                .item-picker-short-text();
+                top: 25px;
+            }
+
+            &.textarea .icon {
+                .item-picker-textarea();
+            }
+
+            &.select .icon {
+                .item-picker-select();
+            }
+
+            &.list .icon {
+                .item-picker-people();
+            }
+
+            &.checkbox .icon {
+                .item-picker-checkbox();
+            }
+
+            &.radio .icon {
+                .item-picker-radio();
+            }
+
+            &.div .icon {
+                .item-picker-separator();
+            }
+
+            &.upload .icon {
+                .item-picker-file();
+            }
+
+            &.number .icon {
+                .item-picker-number();
+            }
         }
     }
 }
-.carrouselTypeLinkDisable{
-    pointer-events:none;
-    opacity :.5;
+
+.carrouselTypeLinkDisable {
+    pointer-events: none;
+    opacity: .5;
 }
-.carrousel-option-home{
-    .btn.select-design{
-        background-color:#444444;
-        width:360px;
-        margin:auto;
-        color:#ffffff;
-        text-align:center;
-        height:35px;
-        display:table-cell;
-        margin-top:40px;
-        &:hover{
-            background-color:@pageColor;
+
+.carrousel-option-home {
+    .btn.select-design {
+        background-color: #444444;
+        width: 360px;
+        margin: auto;
+        color: #ffffff;
+        text-align: center;
+        height: 35px;
+        display: table-cell;
+        margin-top: 40px;
+
+        &:hover {
+            background-color: @pageColor;
         }
     }
-    .btn-group.files-action,.btn-group.design-action{
-        &.design-action{
-            margin-bottom:40px;
+
+    .btn-group.files-action,
+    .btn-group.design-action {
+        &.design-action {
+            margin-bottom: 40px;
         }
+
         height:120px;
         width:360px;
         margin:auto;
-        &>.btn{
-            height:100%;
-            overflow:hidden;
+
+        &>.btn {
+            height: 100%;
+            overflow: hidden;
         }
-        .btn.image-count,.btn.design-icon{
-            &.btn.design-icon{
-                font-size:30px;
+
+        .btn.image-count,
+        .btn.design-icon {
+            &.btn.design-icon {
+                font-size: 30px;
             }
-            &>div{
-                &>span{
-                    display:inline-block;
+
+            &>div {
+                &>span {
+                    display: inline-block;
                 }
+
                 display: table-cell;
                 height: 120px;
                 vertical-align:middle;
@@ -786,164 +1206,214 @@
                 width: 120px;
                 text-align:center;
             }
-            & *{
+
+            & * {
                 vertical-align: middle;
             }
+
             background-color:#444444;
             color:#ffffff;
             width:120px;
-            & .icon-image{
-                font-size:25px;
+
+            & .icon-image {
+                font-size: 25px;
             }
-            & .count{
+
+            & .count {
                 position: relative;
                 top: -4px;
                 margin-right: 5px;
                 font-size: 25px;
             }
-            & .icon-hexagon-add{
-                display:none;
+
+            & .icon-hexagon-add {
+                display: none;
             }
         }
-        .btn.action{
-            padding:0;
-            &>div{
-                background-color:#2f2f2f;
-                height:120px;
-                width:240px;
-                vertical-align:middle;
-                display:table-cell;
-                &>div{
-                    &>.intro{
-                        color:#ffffff;
+
+        .btn.action {
+            padding: 0;
+
+            &>div {
+                background-color: #2f2f2f;
+                height: 120px;
+                width: 240px;
+                vertical-align: middle;
+                display: table-cell;
+
+                &>div {
+                    &>.intro {
+                        color: #ffffff;
                     }
-                    &>.action-desc{
+
+                    &>.action-desc {
                         color: #999999;
                     }
                 }
             }
         }
-        &:hover{
-            & .btn.image-count,& .btn.design-icon{
-                background-color:@pageColor;
-                & .icon-hexagon-add{
-                    display:inline-block;
-                    font-size:35px;
+
+        &:hover {
+
+            & .btn.image-count,
+            & .btn.design-icon {
+                background-color: @pageColor;
+
+                & .icon-hexagon-add {
+                    display: inline-block;
+                    font-size: 35px;
                 }
-                & .icon-image,& .count{
-                    display:none;
+
+                & .icon-image,
+                & .count {
+                    display: none;
                 }
             }
         }
     }
 }
-.carrousel-template-option{
-    .category-content.radio-transformed{
+
+.carrousel-template-option {
+    .category-content.radio-transformed {
         .clearfix();
-        width:90%;
-        margin:auto;
-        padding:5%;
-        background:#2f2f2f;
+        width: 90%;
+        margin: auto;
+        padding: 5%;
+        background: #2f2f2f;
     }
-    .drop-down-wrapper .btn-group,.drop-down-wrapper .btn.dropdown-toggle,.drop-down-wrapper .dropdown-menu {
-        width:100%;
-        background:#444444;
-        &>li{
-            border-top:1px solid #555555;
-            &:first-child{
-                border:none;
+
+    .drop-down-wrapper .btn-group,
+    .drop-down-wrapper .btn.dropdown-toggle,
+    .drop-down-wrapper .dropdown-menu {
+        width: 100%;
+        background: #444444;
+
+        &>li {
+            border-top: 1px solid #555555;
+
+            &:first-child {
+                border: none;
             }
-            &>a:hover{
-                background:#666666;
+
+            &>a:hover {
+                background: #666666;
             }
         }
     }
-    .drop-down-wrapper .btn.dropdown-toggle{
-        padding:5px 0;
-        background:@pageColor;
-        color:#ffffff;
-        &>span{
-            position:relative;
-            &.text{
-                float:left;
-                left:10px;
+
+    .drop-down-wrapper .btn.dropdown-toggle {
+        padding: 5px 0;
+        background: @pageColor;
+        color: #ffffff;
+
+        &>span {
+            position: relative;
+
+            &.text {
+                float: left;
+                left: 10px;
             }
-            &.caret{
-                float:right;
-                right:10px;
+
+            &.caret {
+                float: right;
+                right: 10px;
             }
         }
     }
-    .category-content.radio-transformed>div{
-        width:33.3%;
-        float:left;
-        & .effect-radio{
-            & .container{
-                background:#222222;
+
+    .category-content.radio-transformed>div {
+        width: 33.3%;
+        float: left;
+
+        & .effect-radio {
+            & .container {
+                background: #222222;
             }
-            &.active .container{
-                background:@pageColor;
+
+            &.active .container {
+                background: @pageColor;
             }
         }
-        & .container{
 
+        & .container {}
+    }
 
+    .template-selector-wrapper {
+        .btn-group>.btn:active {
+            z-index: initial;
         }
-    }
-    .template-selector-wrapper{
-        .btn-group > .btn:active{
-            z-index:initial;
+
+        .category-content.radio-transformed {
+            z-index: 999;
         }
-        .category-content.radio-transformed{
-            z-index:999;
+
+        .effect-radio .icon {
+            font-size: 30px;
         }
-        .effect-radio .icon{
-            font-size:30px;
-        }
     }
 }
-.effect-radio .icon, .effect-switcher .icon {
+
+.effect-radio .icon,
+.effect-switcher .icon {
     font-size: 1.5em;
 }
-.effect-radio .switch-container .radio, .effect-radio .switch-container .switch, .effect-switcher .switch-container .radio, .effect-switcher .switch-container .switch {
+
+.effect-radio .switch-container .radio,
+.effect-radio .switch-container .switch,
+.effect-switcher .switch-container .radio,
+.effect-switcher .switch-container .switch {
     margin-top: 5px;
 }
-.effect-radio.active .radio, .effect-radio.active:hover .radio, .effect-switcher.active .radio, .effect-switcher.active:hover .radio {
+
+.effect-radio.active .radio,
+.effect-radio.active:hover .radio,
+.effect-switcher.active .radio,
+.effect-switcher.active:hover .radio {
     background-position: -1px -24px;
 }
-.gallery-option-home{
-    .btn.select-design{
-        background-color:#444444;
-        width:360px;
-        margin:auto;
-        color:#ffffff;
-        text-align:center;
-        height:35px;
-        display:table-cell;
-        margin-top:40px;
-        &:hover{
-            background-color:@pageColor;
+
+.gallery-option-home {
+    .btn.select-design {
+        background-color: #444444;
+        width: 360px;
+        margin: auto;
+        color: #ffffff;
+        text-align: center;
+        height: 35px;
+        display: table-cell;
+        margin-top: 40px;
+
+        &:hover {
+            background-color: @pageColor;
         }
     }
-    .btn-group.files-action,.btn-group.design-action{
-        &.design-action{
-            margin-bottom:40px;
+
+    .btn-group.files-action,
+    .btn-group.design-action {
+        &.design-action {
+            margin-bottom: 40px;
         }
+
         height:120px;
         width:360px;
         margin:auto;
-        &>.btn{
-            height:100%;
-            overflow:hidden;
+
+        &>.btn {
+            height: 100%;
+            overflow: hidden;
         }
-        .btn.image-count,.btn.design-icon{
-            &.btn.design-icon{
-                font-size:30px;
+
+        .btn.image-count,
+        .btn.design-icon {
+            &.btn.design-icon {
+                font-size: 30px;
             }
-            &>div{
-                &>span{
-                    display:inline-block;
+
+            &>div {
+                &>span {
+                    display: inline-block;
                 }
+
                 display: table-cell;
                 height: 120px;
                 vertical-align:middle;
@@ -950,79 +1420,101 @@
                 width: 120px;
                 text-align:center;
             }
-            & *{
+
+            & * {
                 vertical-align: middle;
             }
+
             background-color:#444444;
             color:#ffffff;
             width:120px;
-            & .icon-image{
-                font-size:25px;
+
+            & .icon-image {
+                font-size: 25px;
             }
-            & .count{
+
+            & .count {
                 position: relative;
                 top: -4px;
                 margin-right: 5px;
                 font-size: 25px;
             }
-            & .icon-hexagon-add{
-                display:none;
+
+            & .icon-hexagon-add {
+                display: none;
             }
         }
-        .btn.action{
-            padding:0;
-            &>div{
-                background-color:#2f2f2f;
-                height:120px;
-                width:240px;
-                vertical-align:middle;
-                display:table-cell;
-                &>div{
-                    &>.intro{
-                        color:#ffffff;
+
+        .btn.action {
+            padding: 0;
+
+            &>div {
+                background-color: #2f2f2f;
+                height: 120px;
+                width: 240px;
+                vertical-align: middle;
+                display: table-cell;
+
+                &>div {
+                    &>.intro {
+                        color: #ffffff;
                     }
-                    &>.action-desc{
+
+                    &>.action-desc {
                         color: #999999;
                     }
                 }
             }
         }
-        &:hover{
-            & .btn.image-count,& .btn.design-icon{
-                background-color:@pageColor;
-                & .icon-hexagon-add{
-                    display:inline-block;
-                    font-size:35px;
+
+        &:hover {
+
+            & .btn.image-count,
+            & .btn.design-icon {
+                background-color: @pageColor;
+
+                & .icon-hexagon-add {
+                    display: inline-block;
+                    font-size: 35px;
                 }
-                & .icon-image,& .count{
-                    display:none;
+
+                & .icon-image,
+                & .count {
+                    display: none;
                 }
             }
         }
     }
 }
-.gallery-design-select{
+
+.gallery-design-select {
     padding-bottom: 38px;
-    .action-options{
-        .col{
-            float:left;
-            height:65px;
-            line-height:65px;
-            text-align:center;
-            &.category{
-                width:40%;
+
+    .action-options {
+        .col {
+            float: left;
+            height: 65px;
+            line-height: 65px;
+            text-align: center;
+
+            &.category {
+                width: 40%;
             }
-            &.icon{
-                width:25%;
-                font-size:30px;
+
+            &.icon {
+                width: 25%;
+                font-size: 30px;
             }
-            &.colors{
-                width:25%;
+
+            &.colors {
+                width: 25%;
             }
-            &.edit{
-                &:hover{
-                    background:@pageColor;
+
+            &.edit {
+                &:hover {
+                    background: @pageColor;
                 }
+
                 cursor:pointer;
                 width:10%;
             }
@@ -1029,77 +1521,103 @@
         }
     }
 }
-.gallery-template-option{
-    .category-content.radio-transformed{
+
+.gallery-template-option {
+    .category-content.radio-transformed {
         .clearfix();
-        width:90%;
-        margin:auto;
-        padding:5%;
-        background:#2f2f2f;
+        width: 90%;
+        margin: auto;
+        padding: 5%;
+        background: #2f2f2f;
+        border-radius: 8px;
+        margin-bottom: 15px;
     }
-    .drop-down-wrapper .btn-group,.drop-down-wrapper .btn.dropdown-toggle,.drop-down-wrapper .dropdown-menu {
-        width:100%;
-        background:#444444;
-        &>li{
-            border-top:1px solid #555555;
-            &:first-child{
-                border:none;
+
+    .drop-down-wrapper .btn-group,
+    .drop-down-wrapper .btn.dropdown-toggle,
+    .drop-down-wrapper .dropdown-menu {
+        width: 100%;
+        background: #444444;
+
+        &>li {
+            border-top: 1px solid #555555;
+
+            &:first-child {
+                border: none;
             }
-            &>a:hover{
-                background:#666666;
+
+            &>a:hover {
+                background: #666666;
             }
         }
     }
-    .drop-down-wrapper .btn.dropdown-toggle{
-        padding:5px 0;
-        background:@pageColor;
-        color:#ffffff;
-        &>span{
-            position:relative;
-            &.text{
-                float:left;
-                left:10px;
+
+    .drop-down-wrapper .btn.dropdown-toggle {
+        padding: 5px 0;
+        background: @pageColor;
+        color: #ffffff;
+
+        &>span {
+            position: relative;
+
+            &.text {
+                float: left;
+                left: 10px;
             }
-            &.caret{
-                float:right;
-                right:10px;
+
+            &.caret {
+                float: right;
+                right: 10px;
             }
         }
     }
-    .category-content.radio-transformed>div{
-        width:33.3%;
-        float:left;
-        & .effect-radio{
 
-            margin: .5em auto;
-            height: 45px;
+    .category-content.radio-transformed>div {
+        width: 33.3%;
+        float: left;
 
+        & .effect-radio {
+
+            margin: 5px auto;
+            // height: 40px;
+            width: 100px;
+
             &.active {
                 color: #0b2428;
+                box-shadow: 0px 0px 0px 2px #0b2428, 0px 0px 0px 5px #34d399;
+                border-radius: 4px;
             }
 
-            & .container{
-                background:#222222;
+            & .helper {
+                color: #fff;
+            }
+
+            & .container {
+                background: #222222;
                 display: flex;
                 justify-content: end;
             }
-            &.active .container{
-                background:@pageColor;
+
+            &.active .container {
+                background: @pageColor;
             }
+
             & .switch-container {
-                display: flex;
-                height: auto;
-                line-height: normal;
-                padding-inline: 5px;
-                align-items: center;
-                justify-content: center;
-                position: relative;
-                width: auto;
+                display: none;
+                // display: flex;
+                // height: auto;
+                // line-height: normal;
+                // padding-inline: 5px;
+                // align-items: center;
+                // justify-content: center;
+                // position: relative;
+                // width: auto;
 
-                & .radio {
-                    margin-top: auto;
-                }
+                // & .radio {
+                //     margin-top: auto;
+                // }
             }
+
             & .icon-form-long_text {
                 flex-grow: 1;
                 margin-top: 0;
@@ -1107,6 +1625,7 @@
                 justify-content: center;
                 align-items: center;
             }
+
             & .icon {
                 flex-grow: 1;
                 display: flex;
@@ -1117,36 +1636,39 @@
                 position: initial;
             }
         }
-        & .container{
 
+        & .container {}
+    }
 
+    .template-selector-wrapper {
+        .btn-group>.btn:active {
+            z-index: initial;
         }
-    }
-    .template-selector-wrapper{
-        .btn-group > .btn:active{
-            z-index:initial;
+
+        .category-content.radio-transformed {
+            z-index: 999;
         }
-        .category-content.radio-transformed{
-            z-index:999;
+
+        .effect-radio .icon {
+            font-size: 30px;
         }
-        .effect-radio .icon{
-            font-size:30px;
-        }
     }
 }
+
 //bleu checkbox
-input[type="checkbox"].blue-bg{
-    display:none;
+input[type="checkbox"].blue-bg {
+    display: none;
 }
-input[type="checkbox"].blue-bg{
-    &+label{
-        background:#2f2f2f;
-        display:block;
-        height:40px;
+
+input[type="checkbox"].blue-bg {
+    &+label {
+        background: #2f2f2f;
+        display: block;
+        height: 40px;
         .border-radius();
         overflow: hidden;
 
-        &>span.checkbox-wrapper{
+        &>span.checkbox-wrapper {
             display: block;
             position: absolute;
             width: 40px;
@@ -1154,37 +1676,44 @@
             line-height: 40px;
             text-align: center;
             font-size: 18px;
-            background:#444444;
-            color:#2f2f2f;
-            &>span.icon-checked{
-                display:none;
+            background: #444444;
+            color: #2f2f2f;
+
+            &>span.icon-checked {
+                display: none;
             }
-            &>span.icon-unchecked{
-                display:inline;
+
+            &>span.icon-unchecked {
+                display: inline;
             }
         }
-        &>.text{
-            position:absolute;
-            left:40px;
-            right:0;
-            top:0;
-            bottom:0;
+
+        &>.text {
+            position: absolute;
+            left: 40px;
+            right: 0;
+            top: 0;
+            bottom: 0;
             text-align: center;
             line-height: 40px;
         }
     }
+
     //checked
-    &:checked{
-        &+label{
-            background:@pageColor;
-            &>span.checkbox-wrapper{
-                background:#27808c;
-                color:#ffffff;
-                &>span.icon-unchecked{
-                    display:none;
+    &:checked {
+        &+label {
+            background: @pageColor;
+
+            &>span.checkbox-wrapper {
+                background: #27808c;
+                color: #ffffff;
+
+                &>span.icon-unchecked {
+                    display: none;
                 }
-                &>span.icon-checked{
-                    display:inline;
+
+                &>span.icon-checked {
+                    display: inline;
                 }
             }
         }
@@ -1192,56 +1721,80 @@
 }
 
 .galleryAnimations {
-    .checkbox-panel .additional{
-        background:#444444;
-        padding:10px;
+    .checkbox-panel .additional {
+        background: #444444;
+        padding: 10px;
     }
-    .transition-wrapper>div{
-        float:left;
-        width:33.33%;
+
+    .transition-wrapper>div {
+        float: left;
+        width: 33.33%;
     }
-    input[type="checkbox"].panel-checkbox{
-        &+label{
+
+    input[type="checkbox"].panel-checkbox {
+        &+label {
             display: block;
             height: 40px;
             line-height: 40px;
-            background:#2F2F2F;
-            margin-bottom:0;
-            & .wrapper{
-                position:absolute;
-                left:0;
-                width:40px;
-                height:40px;
-                background:#444444;
-                text-align:center;
-                &>span{
-                    line-height:40px;
-                    font-size:15px;
+            background: #2F2F2F;
+            margin-bottom: 0;
+
+            & .wrapper {
+                position: absolute;
+                left: 0;
+                width: 40px;
+                height: 40px;
+                background: #444444;
+                text-align: center;
+
+                &>span {
+                    line-height: 40px;
+                    font-size: 15px;
                 }
             }
-            & .text{
-                position:absolute;
-                left:45px;
-                right:5px;
+
+            & .text {
+                position: absolute;
+                left: 45px;
+                right: 5px;
             }
-            & .icon-checked{display:none;}
-            & .icon-unchecked{display:block;}
+
+            & .icon-checked {
+                display: none;
+            }
+
+            & .icon-unchecked {
+                display: block;
+            }
         }
-        &+label+.checkbox-panel{
-            display:none;
-            background:#2f2f2f;
+
+        &+label+.checkbox-panel {
+            display: none;
+            background: #2f2f2f;
         }
-        &:checked+label{
-            background:@pageColor;
-            & .wrapper{
+
+        &:checked+label {
+            background: @pageColor;
+
+            & .wrapper {
                 background: #27808c;
             }
-            & .icon-checked{display:block;}
-            & .icon-unchecked{display:none;}
+
+            & .icon-checked {
+                display: block;
+            }
+
+            & .icon-unchecked {
+                display: none;
+            }
         }
-        &:checked+label+.checkbox-panel{
-            display:block;
-            &>div{padding:10px;}
+
+        &:checked+label+.checkbox-panel {
+            display: block;
+
+            &>div {
+                padding: 10px;
+            }
         }
     }
-}
+}
\ No newline at end of file
Index: src/less/imports/panel_map_styles.less
===================================================================
--- src/less/imports/panel_map_styles.less	(révision 11908)
+++ src/less/imports/panel_map_styles.less	(révision 11909)
@@ -4,6 +4,10 @@
             width:33%;
             float:left;
         }
+        .effect-radio {
+            height: 40px;
+            width: 90px;
+        }
         .effect-radio .container{
              background:url('../../assets/img/map-style.jpg') no-repeat;
         }
