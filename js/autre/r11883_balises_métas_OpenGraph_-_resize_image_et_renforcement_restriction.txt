Revision: r11883
Date: 2024-02-15 15:09:08 +0300 (lkm 15 Feb 2024) 
Author: rrakotoarinelina 

## Commit message
balises métas OpenGraph - resize image et renforcement restriction 

## Files changed

## Full metadata
------------------------------------------------------------------------
r11883 | rrakotoarinelina | 2024-02-15 15:09:08 +0300 (lkm 15 Feb 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js

balises métas OpenGraph - resize image et renforcement restriction 
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js
===================================================================
--- src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 11882)
+++ src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 11883)
@@ -103,7 +103,7 @@
                     uploadFailErrorsOccured: 'Des erreurs ont eu lieu pendant le transfert de fichier',
                     uploadSuccess: 'Le transfert des fichiers s\'est déroulé avec succès',
                     uploadFailServerError: 'Impossible de transférer le fichier %name% à cause de l\'erreur: "%error%"',
-                    uploadFaviconCorrupted: 'Le format SVG n\'est sont pas autorisés pour les favicons',
+                    uploadFaviconCorrupted: 'Le format SVG n\'est pas autorisé pour les favicons',
                     uploadOpenGraphFail: 'Echec du chargement. L\'image doit être de 1200x630 pixels',
                 }
             };
@@ -353,6 +353,12 @@
                     var svgHasScript = false;
                     fileReader.onload = (function(file) {
                         return function(e) {
+
+                            // on a répété des codes car fileReader.onload et img.onload sont des opérations asynchrone.
+                            // On ne peut pas appeler une fonction dans une operation asyncrhone,
+                            // sinon on aura des comportements anormales
+                            // l'upload est lancée par ce code :   "uploader._upload.call(uploader);"
+
                             var preview = $('<div class="preview">\n\
                                             <div>\n\
                                                 <div class="progressbar">\n\
@@ -363,72 +369,136 @@
                             var realPreview = preview.children('div:not(.abort)');
                             var uploadInfos = {name: file.name, value: file, size: file.size, preview: preview, uploaded: 0,code:e.target.result,type: file.type};
                             if (uploader._isImage(file)) {
+
+                                realPreview.addClass('imagepreview');
+                                realPreview.css({background: 'url(' + this.result + ') center center', backgroundSize: 'cover'});
+
                                 var decodedData = atob(uploadInfos.code.split(',')[1]);
-                                if (decodedData.includes('<script')) {
-                                    message = uploader.options.lang.uploadFailCorrupted;
-                                    uploader.errors.push(message);
-                                    files.rejected++;
-                                    if (files.length === 1){
-                                        uploader._onComplete();
-                                    }
-                                }else{
-                                    realPreview.addClass('imagepreview');
-                                    realPreview.css({background: 'url(' + this.result + ') center center', backgroundSize: 'cover'});
-                                }
 
-                                //checker si favicon et empecher upload svg
-                                if(uploadInfos.type === 'image/svg+xml' && uploader.options.isFavicon){
-                                    message = uploader.options.lang.uploadFaviconCorrupted;
-                                    uploader.errors.push(message);
-                                    files.rejected++;
-                                    if (files.length === 1){
-                                        uploader._onComplete();
-                                    }
-                                }
-
-                                   //check pour opengrah
-                                if(uploader.options.isOpengraph){
+                                 //check pour open graph
+                                if(uploader.options.isOpengraph)
+                                { 
                                     //prendre les dimensions de l'image
                                     var imageUrl = uploadInfos.code; 
                                     var img = new Image();
                                     img.src = imageUrl;
+
                                     img.onload = function() {
-                                        if (img.width != 1200 || img.height != 630) {
+                                        
+                                        if (img.width >= 1200 && img.height >= 630) {
+
+                                             //---------------------code répété-------------------------------
+                                            realPreview.show();
+                                            realPreview.addClass('shown');
+                                            $frag.append(preview);
+                                            uploader.previews[file.name] = preview;
+                                
+                                            uploadInfos.index = uploader.dataArray.push(uploadInfos) - 1;
+                                            preview.data('uploadId', uploadInfos.index);
+                                            uploader._trigger('previewcreated', null, {uploader: uploader, previewElement: preview, previews: uploader.previews});
+                                            uploader.element.prepend($frag);
+                                            if (uploader.options.maxFiles !== -1) {
+                                                while ((uploader.uploaded.length + uploader.uploading) > uploader.options.maxFiles) {
+                                                    var data = uploader.uploaded.shift();
+                                                    data.preview.remove();
+                                                }
+                                            }
+                                             //------------------------------------------------------
+                                            uploader._upload.call(uploader);
+
+                                        }else{
                                             message = uploader.options.lang.uploadOpenGraphFail;
                                             uploader.errors.push(message);
                                             files.rejected++;
                                             if (files.length === 1)
                                                 uploader._onComplete();
+                                        
+                                            //---------------------code répété-------------------------------
+                                            realPreview.show();
+                                            realPreview.addClass('shown');
+                                            $frag.append(preview);
+                                            uploader.previews[file.name] = preview;
+                                
+                                            uploadInfos.index = uploader.dataArray.push(uploadInfos) - 1;
+                                            preview.data('uploadId', uploadInfos.index);
+                                            uploader._trigger('previewcreated', null, {uploader: uploader, previewElement: preview, previews: uploader.previews});
+                                            uploader.element.prepend($frag);
+                                            if (uploader.options.maxFiles !== -1) {
+                                                while ((uploader.uploaded.length + uploader.uploading) > uploader.options.maxFiles) {
+                                                    var data = uploader.uploaded.shift();
+                                                    data.preview.remove();
+                                                }
+                                            }
+                                             //------------------------------------------------------
                                             
                                         }
                                     }
+                                }else{
+
+                                    var doUpload = true;             
+                                    if (decodedData.includes('<script')) {
+                                        message = uploader.options.lang.uploadFailCorrupted;
+                                        doUpload = false;
+                                        
+                                    } else  if(uploadInfos.type === 'image/svg+xml' && uploader.options.isFavicon){  //checker si favicon et empecher upload svg
+                                        message = uploader.options.lang.uploadFaviconCorrupted;
+                                        doUpload = false;
+                                    }
+                                    //---------------------code répété-------------------------------
+                                    realPreview.show();
+                                    realPreview.addClass('shown');
+                                    $frag.append(preview);
+                                    uploader.previews[file.name] = preview;
+                        
+                                    uploadInfos.index = uploader.dataArray.push(uploadInfos) - 1;
+                                    preview.data('uploadId', uploadInfos.index);
+                                    uploader._trigger('previewcreated', null, {uploader: uploader, previewElement: preview, previews: uploader.previews});
+                                    uploader.element.prepend($frag);
+                                    if (uploader.options.maxFiles !== -1) {
+                                        while ((uploader.uploaded.length + uploader.uploading) > uploader.options.maxFiles) {
+                                            var data = uploader.uploaded.shift();
+                                            data.preview.remove();
+                                        }
+                                    }
+                                     //------------------------------------------------------
+
+                                    if(!doUpload){
+                                        uploader.errors.push(message);
+                                        files.rejected++;
+                                        if (files.length === 1)
+                                            uploader._onComplete();
+                                    }else{
+                                        uploader._upload.call(uploader);
+                                    }
+
                                 }
 
-
                             }
                             else {
                                 realPreview.addClass('filepreview');
                                 realPreview.append('<p>' + file.name + '</p>');
                                 realPreview.append('<div class="file ' + uploader._getFileExt(file) + '">.' + uploader._getFileExt(file) + '</div>');
-                            }
-                            realPreview.show();
-                            realPreview.addClass('shown');
-                            $frag.append(preview);
-                            uploader.previews[file.name] = preview;
+                                
+                                //---------------------code répété-------------------------------
+                                realPreview.show();
+                                realPreview.addClass('shown');
+                                $frag.append(preview);
+                                uploader.previews[file.name] = preview;
 
-                            uploadInfos.index = uploader.dataArray.push(uploadInfos) - 1;
-                            preview.data('uploadId', uploadInfos.index);
-                            uploader._trigger('previewcreated', null, {uploader: uploader, previewElement: preview, previews: uploader.previews});
-                            //if (uploader.dataArray.length + files.rejected === files.length) {
-                            uploader.element.prepend($frag);
-                            if (uploader.options.maxFiles !== -1) {
-                                while ((uploader.uploaded.length + uploader.uploading) > uploader.options.maxFiles) {
-                                    var data = uploader.uploaded.shift();
-                                    data.preview.remove();
+                                uploadInfos.index = uploader.dataArray.push(uploadInfos) - 1;
+                                preview.data('uploadId', uploadInfos.index);
+                                uploader._trigger('previewcreated', null, {uploader: uploader, previewElement: preview, previews: uploader.previews});
+                                uploader.element.prepend($frag);
+                                if (uploader.options.maxFiles !== -1) {
+                                    while ((uploader.uploaded.length + uploader.uploading) > uploader.options.maxFiles) {
+                                        var data = uploader.uploaded.shift();
+                                        data.preview.remove();
+                                    }
                                 }
+                                //------------------------------------------------------
+                                uploader._upload.call(uploader);
                             }
-                            uploader._upload.call(uploader);
-                            //}
+
                         };
                     })(files[i]);
                     fileReader.readAsDataURL(files[i]);
