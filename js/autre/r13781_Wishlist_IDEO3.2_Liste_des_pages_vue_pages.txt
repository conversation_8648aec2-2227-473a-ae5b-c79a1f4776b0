Revision: r13781
Date: 2025-02-04 11:40:59 +0300 (tlt 04 Feb 2025) 
Author: frahajanirina 

## Commit message
Wishlist IDEO3.2: Liste des pages vue pages

## Files changed

## Full metadata
------------------------------------------------------------------------
r13781 | frahajanirina | 2025-02-04 11:40:59 +0300 (tlt 04 Feb 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/main.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/PagePanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Templates/pagePanel.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Views/PageCollectionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Views/PageLpManagerView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Views/PageView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/main.less

Wishlist IDEO3.2: Liste des pages vue pages
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/main.js
===================================================================
--- src/js/JEditor/Commons/main.js	(révision 13780)
+++ src/js/JEditor/Commons/main.js	(révision 13781)
@@ -23,6 +23,7 @@
     "JEditor/Commons/Ancestors/Views/TabbedView",
     "JEditor/Commons/Ancestors/Views/View",
     "JEditor/Commons/Basefile/Models/LabelsCollection",
+    "JEditor/Commons/Basefile/Models/SocialsCollection",
     "JEditor/Commons/Basefile/Models/Svg",
     "JEditor/Commons/Events",
     "JEditor/Commons/Files/Models/File",
@@ -40,6 +41,7 @@
     "JEditor/Commons/Files/Views/ReadOnlyFileListManagerView",
     "JEditor/Commons/Files/Views/ReadOnlyFileListView",
     "JEditor/Commons/Files/Views/SelectFileView",
+    "JEditor/Commons/Files/Views/SocialSvgSelectorDialog",
     "JEditor/Commons/Languages/Models/ContentLanguageList",
     "JEditor/Commons/Languages/Models/Language",
     "JEditor/Commons/Languages/Models/LanguageCollection",
Index: src/js/JEditor/PagePanel/Views/PageCollectionView.js
===================================================================
--- src/js/JEditor/PagePanel/Views/PageCollectionView.js	(révision 13780)
+++ src/js/JEditor/PagePanel/Views/PageCollectionView.js	(révision 13781)
@@ -171,6 +171,7 @@
           type: 'delete',
           onOk: _.bind(function() {
             this.deleteModel(model);
+            this.trigger("remove:page", this);
           }, this),
           options: {
             dialogClass: 'delete no-close',
@@ -261,6 +262,9 @@
     render: function(search) {
       this._super();
       this.hideNews();
+      if(!this.app.user.can('access_layout')) {
+        this.$('.wrapper.template').html('');
+      }
       if (search) {
         // tout les pages typés
         var listPage = ['home', 'contact', 'legal', 'plan', 'avis', 'newsletter'];
@@ -295,6 +299,7 @@
         return regex.test(pageName);
       };
       this.render(keyword);
+      this.trigger("search:page", keyword);
     },
     
     hideNews: function() {
Index: src/js/JEditor/PagePanel/Views/PageLpManagerView.js
===================================================================
--- src/js/JEditor/PagePanel/Views/PageLpManagerView.js	(révision 13780)
+++ src/js/JEditor/PagePanel/Views/PageLpManagerView.js	(révision 13781)
@@ -25,6 +25,7 @@
         if(localStorage.getItem( 'lpaccess') == "true")
             localStorage.setItem( 'lpaccess', false);
         else localStorage.setItem( 'lpaccess', true);
+        this.trigger("lp:toggle", this);
         this.options.listView.render();
         return false;
       },
Index: src/js/JEditor/PagePanel/PagePanel.js
===================================================================
--- src/js/JEditor/PagePanel/PagePanel.js	(révision 13780)
+++ src/js/JEditor/PagePanel/PagePanel.js	(révision 13781)
@@ -48,7 +48,11 @@
 			'click #available-blocks-trigger' : 'showAvailableBlocks',
                         'click #show-zone-version': 'showZoneVersionsPanel',
 			'click .addPage,.empty-content-lang':'onAddPageClick',
-			'input #form-search-page input': 'searchPage'
+			'input #form-search-page input': 'searchPage',
+			'click .icon-bin': '_onRemoveClick',
+			'click .icon-edit': '_onEditClick',
+			'click  .switch': '_onSwitchClick',
+			'click th[data-column]': 'onSort'
 		},
 		cssFile: __IDEO_INTEGRATION_PATH__ + "/build/css/page_panel.css",
 
@@ -116,6 +120,10 @@
 			this.listenTo(this.childViews.langDropDown, Events.ChoiceEvents.SELECT, this._onLangSelect);
 			this.listenTo(this.childViews.pageList, Events.ChoiceEvents.SELECT, this._onPageSelect);
 			this.listenTo(this.childViews.pageList, 'render', this._scrollbar);
+			this.listenTo(this.childViews.pageLpManager, "lp:toggle", this.render);
+			this.listenTo(this.childViews.pageList, "search:page", this._onSerch);
+			this.listenTo(this.childViews.pageList, "remove:page", this.render);
+			this.listenTo(this.pages, "change", this.render);
 			this.render();
 		},
 		/**
@@ -161,6 +169,96 @@
 
 			this.loadingEnd();
 		},
+		/*
+		 * recherche d'une ou plusieurs pages
+		*/
+		_onSerch: function(val) {
+			var rows = this.$el.find('tbody tr');
+			var hasResult = false;
+
+			// Supprime la ligne d'une recherche précédente (si aucun résultat)
+			$('#all-data-table tbody').find('.no-result-row').remove();
+
+			rows.each(function() {
+				var rowText = $(this).find('td.title').text().toLowerCase();
+				var match = rowText.includes(val.trim().toLowerCase());
+
+				// Affiche ou masque la ligne en fonction du résultat de la recherche
+				$(this).toggle(match);
+
+				if (match) hasResult = true;
+			});
+
+			// Si aucun résultat
+			if (!hasResult) {
+				$('#all-data-table tbody').append('<tr class="no-result-row"><td colspan="100%">'+translate('none_result')+'</td></tr>');
+			}
+		},
+		_onRemoveClick: function(event) {
+			this.childViews.pageList._onDeleteClick(event);
+		},
+		_onEditClick: function(event) {
+			this.childViews.pageList._onPageClick(event);
+		},
+		
+		/*
+		 *	tri par ordre alphabétique (titre de la page)
+		*/
+		onSort: function(event) {
+			$(event.currentTarget).find('.sort-icon').removeClass('hidden');
+			var table = $(event.currentTarget).closest('table');
+			var tbody = table.find('tbody');
+			var column = $(event.currentTarget).data('column');
+			var order = $(event.currentTarget).data('order');
+			var $sortIcon = $(event.currentTarget).find('.sort-icon').find('span');
+			if (order === 'asc') {
+				$sortIcon.removeClass('desc');
+				$sortIcon.addClass('asc');
+			} else {
+				$sortIcon.removeClass('asc');
+				$sortIcon.addClass('desc');
+			}
+		
+			var rows = tbody.find('tr').toArray();
+		
+			rows.sort(function(a, b) {
+				var aText = $(a).find('td').eq(column).text().trim();
+				var bText = $(b).find('td').eq(column).text().trim();
+		
+				var aVal = isNaN(aText) ? aText.toLowerCase() : parseFloat(aText);
+				var bVal = isNaN(bText) ? bText.toLowerCase() : parseFloat(bText);
+		
+				return order === 'asc' ? (aVal > bVal ? 1 : -1) : (aVal < bVal ? 1 : -1);
+			});
+		
+			$(event.currentTarget).data('order', order === 'asc' ? 'desc' : 'asc');
+		
+			tbody.append(rows);
+		},		
+		_onSwitchClick: function(event) {
+			var $target = $(event.currentTarget);
+			var pageId = $target.data('cid');
+			var $page = $target.parents('tr');
+			var pageModel = this.childViews.pageList.collection.get(pageId);
+				switch (pageModel.home) {
+				case 0:
+					pageModel.active = !pageModel.active;
+					pageModel.save();
+					$page.toggleClass('disabled');
+					break;
+				case 1:
+					this.notify({
+					message: translate("deleteHome"),
+					title: translate("warning")
+					});
+					break;
+				default:
+					pageModel.active = !pageModel.active;
+					pageModel.save();
+					$page.toggleClass('disabled');
+					break;
+			}
+		},
 		unload : function() {
 			this.listenToOnce(this.rightPanelView, Events.ViewEvents.HIDE, function() {
 				this.trigger(Events.PanelEvents.UNLOAD, this);
@@ -341,10 +439,25 @@
 		
 		render : function() {
 			this.undelegateEvents();
+			var bylangSupport = this.pageSupports.groupBy('lang');
+			var currentListSupport = (bylangSupport[this.currentLang.id] !== undefined) ? bylangSupport[this.currentLang.id]: [];
+			var page = this.childViews.pageList.currentList;
+			var isUserCanAccessLpS = this.app.user.can('access_lpsupport_page');
+			var isUserCanAccessLayout = this.app.user.can('access_layout');
+			var isUserCanAccessRmPage = this.app.user.can('delete_page');
+
+			if(isUserCanAccessLpS && localStorage.getItem('lpaccess') == 'true') {
+				var page = page.concat(currentListSupport);
+			}
 			this.$el.html(this._template({
-				empty : this._byLang[this.currentLang.id].length === 0,
-				noneSelected : !this.currentPage,
-				canAddPage:this.user.can("create_page")
+				empty: this._byLang[this.currentLang.id].length === 0,
+				noneSelected: !this.currentPage,
+				canAddPage: this.user.can("create_page"),
+				content: page,
+				sortAsc: this.childViews.pageList.sortAsc,
+				sortedBy: this.childViews.pageList.sortedBy,
+				canDelete: isUserCanAccessRmPage,
+				canAccess: isUserCanAccessLayout
 			}));
 			this.dom.pagePanel.nothingToDisplay = this.$('.nothing-to-display');
 
@@ -446,7 +559,7 @@
 				this.childViews.pageList.lang = lang.id;
 				//this.childViews.addPageView.setLanguage(lang);
 				this.listenTo(this.childViews.pageList, Events.ViewEvents.HIDE, function() {
-					this._renderPageList();
+					this.render();
 					this.childViews.pageList.render();
 					this.childViews.pageList.show();
 				});
Index: src/js/JEditor/PagePanel/Templates/pagePanel.html
===================================================================
--- src/js/JEditor/PagePanel/Templates/pagePanel.html	(révision 13780)
+++ src/js/JEditor/PagePanel/Templates/pagePanel.html	(révision 13781)
@@ -38,31 +38,76 @@
     </aside>
     <div id="item-config" class="panel-container"></div>
     <div class="nothing-to-display main <%= empty?'active empty':noneSelected?'active none-selected':'' %>">
-        <div class="wrapper">
-            <a class="empty-content-lang" title="<%= __("addPage") %>">
-                <div class="icon">
-                    <span class="icon-file"></span>
-                </div>
-                <span class="text-intro">
-                    <%= __("emptyContentLang") %>
+    </div>
+    <header class="header affix-top">
+        <div class="page title-bar">
+            <div class="title top-title">
+                <span class="page-name">
+                    <span><%=__('allPages')%></span>
                 </span>
-                <span class="how-to">
-                    <%= __("howToAddPage") %>
-                </span>
-                <span class="add-page" ><%= __("addPage") %></span>
-            </a>
-            <a class="none-selected">
-                <div class="icon">
-                    <span class="icon-file"></span>
-                </div>
-                <span class="text-intro">
-                    <%= __("noPageSelected") %>
-                </span>
-                <span class="how-to">
-                    <%= __("howToSelectPage") %>
-                </span>
-            </a>
+            </div>
         </div>
+    </header>
+    <div class="main-list-page">
+        <div id="content-editor">
+            <div class="page-list">
+                <table id="all-data-table">
+                    <thead>
+                        <tr>
+                            <th class="page-icon"><%=__('icon')%></th>
+                            <th data-column="1" data-order="desc" class="title-page">
+                                <%=__('title')%>
+                                <span class="sort-icon hidden"> 
+                                    <span class="asc"></span>
+                                </span>
+                            </th>
+                            <th class="action-page"><%=__('action')%></th>
+                        </tr>
+                    </thead>
+                    <tbody>
+                        <% if (content.length === 0) { %> 
+                            <tr>
+                                <td colspan="7" class="no-result"><%=__('none_result')%></td>
+                            </tr>
+                        <% } else { %>
+                            <% _.each(content, function(page) { %>
+                                <% if ((page.type !== 'news' && canAccess) || (page.type !== 'news' && page.type !== 'template')) { %>
+                                    <tr data-cid="<%= page.cid %>" class="data-tr <%= !page.active ? 'disabled' : ''%>">
+                                        <td>
+                                            <% var icons = {
+                                                1: 'icon-home',
+                                                2: 'icon-mail',
+                                                3: 'icon-legal',
+                                                4: 'icon-site-map',
+                                                5: 'icon-star',
+                                                6: 'icon-newsletter-icon',
+                                                7: 'icon-interest-point',
+                                                0: 'icon-file',
+                                                8: 'icon-file'
+                                            };
+                                            var iconClass = icons[page.home] || 'icon-file'; %>
+                                            <span class="icon <%= iconClass %>"></span>
+                                        </td>
+                                        <td class="title"><%= page.name %></td>
+                                        <td>
+                                            <% if (page.type === 'content') { %>
+                                                <span class="switch" data-cid="<%= page.cid %>">
+                                                    <span></span>
+                                                </span>
+                                            <% } %>
+                                            <span class="icon-edit" data-cid="<%= page.cid %>"></span>
+                                            <% if (canDelete && page.type === 'content') { %>
+                                                <span class="icon-bin" data-model="<%= page.cid %>"></span>
+                                            <% } %>
+                                        </td>
+                                    </tr>
+                                <% } %>
+                            <% }); %>
+                        <% } %>
+                    </tbody>
+                </table>
+            </div>
+        </div>
     </div>
 
 
Index: src/js/JEditor/PagePanel/Views/PageView.js
===================================================================
--- src/js/JEditor/PagePanel/Views/PageView.js	(révision 13780)
+++ src/js/JEditor/PagePanel/Views/PageView.js	(révision 13781)
@@ -265,6 +265,7 @@
                         render: function () {
                             
                             this.undelegateEvents();
+                            $('.main-list-page').hide();
                             this.$el.empty();
                             this.$el.html(this._template({
                                 user: this.app.user,
Index: src/js/JEditor/PagePanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/nls/fr-ca/i18n.js	(révision 13780)
+++ src/js/JEditor/PagePanel/nls/fr-ca/i18n.js	(révision 13781)
@@ -129,5 +129,10 @@
     "showLpActions":"Afficher les pages supports",
     "landingPage" : "Landing page",
     "urlText" : "Ouvrir la page 🡥",
-    "filter": "Filtre ..."
+    "filter": "Filtre ...",
+    "none_result": "Aucune page correspond à la recherche",
+    "title" : "Titre",
+    "allPages": "Toutes les pages",
+    "icon": "Icônes",
+    "action": "Action"
 });
Index: src/js/JEditor/PagePanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/nls/fr-fr/i18n.js	(révision 13780)
+++ src/js/JEditor/PagePanel/nls/fr-fr/i18n.js	(révision 13781)
@@ -135,5 +135,10 @@
     "showLpActions":"Afficher les pages supports",
     "landingPage" : "Landing page",
     "urlText" : "Ouvrir la page 🡥",
-    "filter": "Filtre ..."
+    "filter": "Filtre ...",
+    "none_result": "Aucune page correspond à la recherche",
+    "title" : "Titre",
+    "allPages": "Toutes les pages",
+    "icon": "Icônes",
+    "action": "Action"
 });
Index: src/js/JEditor/PagePanel/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/nls/i18n.js	(révision 13780)
+++ src/js/JEditor/PagePanel/nls/i18n.js	(révision 13781)
@@ -133,7 +133,12 @@
         "showLpActions":"Show supports pages",
         "landingPage" : "Landing page",
         "urlText" : "Open the webpage 🡥",
-        "filter": "Filter ..."
+        "filter": "Filter ...",
+        "none_result": "No pages match the search",
+        "title" : "Title",
+        "allPages": "All pages",
+        "icon": "Icon",
+        "action": "Action"
     },
     "fr-fr": true, "fr-ca":true
 });
Index: src/less/main.less
===================================================================
--- src/less/main.less	(révision 13780)
+++ src/less/main.less	(révision 13781)
@@ -2979,4 +2979,111 @@
 .page-search::placeholder {
   color: #fff;
 }
-
+.data-tr {
+  .switch {
+    display: inline-block;
+    width: 24px;
+    height: 14px;
+    background-color: #34d399;
+    border-radius: 5px;
+    cursor: pointer;
+    span {
+      display: block;
+      float: right;
+      width: 10px;
+      height: 12px;
+      background: #fff;
+      margin: 1px;
+      border-radius: 4px;
+      box-shadow: -1px 0 1px 0 rgba(0,0,0,.1);
+    }
+  }
+  &.disabled {
+    color: darkGray;
+  }
+  &.disabled .switch {
+    background-color: #b2b2b2;
+    cursor: pointer;
+    span {
+      float: none;
+      box-shadow: none;
+    }
+  }
+  .icon-bin, .icon-edit {
+    cursor: pointer;
+  }
+}
+#all-data-table {
+  .sort-icon {
+    position: relative;
+    margin-left: 5px;
+    font-size: 12px;
+    color: #999;
+    .asc::before,
+    .desc::before {
+      position: absolute;
+      opacity: 0.5;
+    }
+    .asc::before {
+      content: "\25B2";
+      bottom: 8px;
+    }
+    .desc::before {
+      content: "\25BC";
+      top: 2px;
+    }
+    .active {
+      opacity: 0.8;
+    }
+  }
+  .title-page, 
+  .action-page, 
+  .page-icon {
+    font-weight: 500;
+    font-size: 22px;
+    text-align: left;
+    border-top: 1px solid #ddd;
+    padding: 15px 8px;
+  }
+  .title-page {
+    cursor: pointer;
+  }
+  .action-page, 
+  .page-icon {
+    text-align: center;
+  }
+  th:first-child, td:first-child {
+    border-left: 1px solid #ddd;
+    text-align: center;
+  }
+  th:last-child, td:last-child {
+    border-right: 1px solid #ddd;
+    text-align: center;
+  }
+  tr {
+    border-bottom: 1px solid #ddd;
+  }
+  td {
+    padding: 25px 8px;
+  }
+}
+.title-bar {
+  border-bottom: 1px solid #e1e1e1;
+}
+.top-title {
+  margin-left: 15px !important;
+}
+.main-list-page {
+  margin: -550px 0px 142px 250px;
+}
+#content-editor .page-list{
+  display: flex;
+  justify-content: center;
+}
+#all-data-table {
+  width: 100%;
+  border-collapse: collapse;
+}
+#all-data-table .no-result {
+  text-align: center;
+}
