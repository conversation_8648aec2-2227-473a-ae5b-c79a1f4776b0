Revision: r11315
Date: 2023-09-21 12:08:52 +0300 (lkm 21 Sep 2023) 
Author: rrakotoarinelina 

## Commit message
Cleaner SVG lors de l'upload

## Files changed

## Full metadata
------------------------------------------------------------------------
r11315 | rrakotoarinelina | 2023-09-21 12:08:52 +0300 (lkm 21 Sep 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/dev/#librairies/vendor/CoreRessourceManager/src/CoreRessourceManager/Controller/ResourceLogoRestController.php
   M /branches/ideo3_v2/dev/#librairies/vendor/CoreRessourceManager/src/CoreRessourceManager/Controller/ResourceRestController.php
   M /branches/ideo3_v2/dev/#librairies/vendor/CoreRessourceManager/src/CoreRessourceManager/Service/ResourceUploader.php
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/MarqueClientView.js

Cleaner SVG lors de l'upload
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js
===================================================================
--- src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 11314)
+++ src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 11315)
@@ -97,6 +97,7 @@
                     importFailBadType: 'Impossible d\'importer le fichier %name% car il représente un danger potentiel',
                     progressLoadingFiles: 'Veuillez patienter pendant le chargement des fichiers',
                     uploadFailTooBig: 'Le fichier "%name%" (%filesize% mo ) est trop volumineux pour être importé (limite %limite% mo)' ,
+                    uploadFailCorrupted: 'Echec du chargement. Le fichier semble corrompu',
                     uploadFailErrorsOccured: 'Des erreurs ont eu lieu pendant le transfert de fichier',
                     uploadSuccess: 'Le transfert des fichiers s\'est déroulé avec succès',
                     uploadFailServerError: 'Impossible de transférer le fichier %name% à cause de l\'erreur: "%error%"'
@@ -345,6 +346,7 @@
                     fileReader = new FileReader();
                     this.uploading++;
                     this.currentUploadSize += files[i].size;
+                    var svgHasScript = false;
                     fileReader.onload = (function(file) {
                         return function(e) {
                             var preview = $('<div class="preview">\n\
@@ -355,10 +357,20 @@
                                             </div>\n\
                                             <div class="action abort"></div></div>');
                             var realPreview = preview.children('div:not(.abort)');
-                            var uploadInfos = {name: file.name, value: file, size: file.size, preview: preview, uploaded: 0};
+                            var uploadInfos = {name: file.name, value: file, size: file.size, preview: preview, uploaded: 0,code:e.target.result};
                             if (uploader._isImage(file)) {
-                                realPreview.addClass('imagepreview');
-                                realPreview.css({background: 'url(' + this.result + ') center center', backgroundSize: 'cover'});
+                                var decodedData = atob(uploadInfos.code.split(',')[1]);
+                                if (decodedData.includes('<script>')) {
+                                    message = uploader.options.lang.uploadFailCorrupted;
+                                    uploader.errors.push(message);
+                                    files.rejected++;
+                                    if (files.length === 1){
+                                        uploader._onComplete();
+                                    }
+                                }else{
+                                    realPreview.addClass('imagepreview');
+                                    realPreview.css({background: 'url(' + this.result + ') center center', backgroundSize: 'cover'});
+                                }
                             }
                             else {
                                 realPreview.addClass('filepreview');
Index: src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 11314)
+++ src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 11315)
@@ -67,6 +67,7 @@
     "progressLoadingFiles": "Veuillez patienter pendant le chargement des fichiers",
     "uploadFailTooBig":  'Le fichier "%name%" (%filesize% mo ) est trop volumineux pour être importé (limite %limite% mo)',
     "uploadFailErrorsOccured": "Des erreurs ont eu lieu pendant le transfert de fichier",
+    "uploadFailCorrupted": "Echec du chargement. Le fichier semble corrompu",
     "uploadSuccess": "Le transfert des fichiers s'est déroulé avec succès",
     "uploadFailServerError": "Impossible de transférer le fichier %name% à cause de l'erreur: %error%",
     "save": "Sauvegarder",
Index: src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 11314)
+++ src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 11315)
@@ -69,6 +69,7 @@
     "progressLoadingFiles": "Veuillez patienter pendant le chargement des fichiers",
     "uploadFailTooBig":  'Le fichier "%name%" (%filesize% mo ) est trop volumineux pour être importé (limite %limite% mo)',
     "uploadFailErrorsOccured": "Des erreurs ont eu lieu pendant le transfert de fichier",
+    "uploadFailCorrupted": "Echec du chargement. Le fichier semble corrompu",
     "uploadSuccess": "Le transfert des fichiers s'est déroulé avec succès",
     "uploadFailServerError": "Impossible de transférer le fichier %name% à cause de l'erreur: %error%",
     "save": "Sauvegarder",
Index: src/js/JEditor/FilePanel/nls/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/i18n.js	(révision 11314)
+++ src/js/JEditor/FilePanel/nls/i18n.js	(révision 11315)
@@ -70,6 +70,7 @@
         "progressLoadingFiles": "Please wait during file loading",
         "uploadFailTooBig":  'The file "%name%" (%filesize% mo )  is too large to be imported (limit %limite% mo)',
         "uploadFailErrorsOccured": "Errors occurred during file transfer",
+        "uploadFailCorrupted": "Upload failed. File appears corrupted",
         "uploadSuccess": "File transfer successful",
         "uploadFailServerError": "Impossible to transfer file %name% due to: %error%",
         "save": "Save",
Index: src/js/JEditor/ParamsPanel/Views/MarqueClientView.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 11314)
+++ src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 11315)
@@ -7,6 +7,7 @@
         "JEditor/Commons/Files/Models/File",
         'text!../Templates/MarqueClient.html',
         "JEditor/App/Messages/Confirm", 
+        "JEditor/App/Messages/Error", 
         "JEditor/App/Messages/ClipboardModule", 
         'i18n!../nls/i18n',
         "jqueryPlugins/uploader"
@@ -20,6 +21,7 @@
             File,
             template,
             Confirm,
+            Error,
             ClipboardModule,
             translate) {
     var MarqueClient = View.extend({
@@ -62,9 +64,10 @@
         },
 
         upload: function(file,fileType){
-
+            
             if (this["currentFile" + fileType]) this._deleteLastFile(fileType);
             this["currentFile" + fileType] = new File(file.attributes);
+           
             this.model.set('Logo', this.currentFileLogo);
             this.model.set('LogoSmall', this.currentFileLogoSmall);
             this.model.set('Favicon', this.currentFileFavicon);
@@ -187,10 +190,17 @@
               });
 
 
+
             this.listenTo(this.fileUploader, Events.FileUploaderEvents.UPLOAD, this._onUpload);
             this.listenTo(this.fileUploader2, Events.FileUploaderEvents.UPLOAD, this._onUpload2);
             this.listenTo(this.fileUploader3, Events.FileUploaderEvents.UPLOAD, this._onUpload3);
 
+            // en cas echec d'upload on re-render les marqueClients
+            // this.listenTo(this.fileUploader, Events.FileUploaderEvents.TOOBIG, this.render);
+            this.listenTo(this.fileUploader, Events.FileUploaderEvents.TOOBIG, this.onError);
+            this.listenTo(this.fileUploader2, Events.FileUploaderEvents.TOOBIG, this.onError);
+            this.listenTo(this.fileUploader3, Events.FileUploaderEvents.TOOBIG, this.onError);
+
             this.$('.inline-logo .group-content-logo header').after(this.fileUploader.el);
             this.$('.inline-logo .group-content-logo-small header').after(this.fileUploader2.el);
             this.$('.inline-logo .group-content-favicon header').after(this.fileUploader3.el);
@@ -212,9 +222,18 @@
             this.$('.menu-wrapper-favicon .uploader .preview .imagepreview').after('<span style="font-size:9px;display:block;margin-top:35px;color: #666;text-align:center;">'+this.translations.DefaultMessageUploaderLogo+'</span>');
             return this;
         },
+        onError: function(data){
+            var errorMessage = data.error.replace(/<li>/g, "").replace(/<\/li>/g, "");
+            //affichage message d'erreur venant de data (trigger dans FileUploaderView)
+            this.error({message:errorMessage});
+            this.render();
+        },
         confirm: function(params) {
             this.app.messageDelegate.set(new Confirm(params));
         },
+        error :function(params){
+            this.app.messageDelegate.set(new Error(params));
+        },
         deleteOne: function(e) {
             e.stopImmediatePropagation();
             var $target = $(e.currentTarget);
