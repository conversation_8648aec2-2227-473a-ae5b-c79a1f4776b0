Revision: r12068
Date: 2024-03-08 14:26:53 +0300 (zom 08 Mar 2024) 
Author: rrakotoarinelina 

## Commit message
form : champs 'date range' - mise en place labels

## Files changed

## Full metadata
------------------------------------------------------------------------
r12068 | rrakotoarinelina | 2024-03-08 14:26:53 +0300 (zom 08 Mar 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/FieldBuilder.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/Fields/DateRange.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FieldBuilderView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FieldView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/form_block/contentOptions.less

form : champs 'date range' - mise en place labels
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/FieldBuilder.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/FieldBuilder.html	(révision 12067)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/FieldBuilder.html	(révision 12068)
@@ -14,6 +14,15 @@
         <label>
             <%=__("description")%> :</label>
         <textarea name="description" placeholder="Description" class="field-input field-description"><%= field.description %></textarea>
+
+        <div class="labels-daterange" <%= field.type !== 'daterange' ? "style='display:none;'":'' %>>
+                <label >
+                <%=__("labelStartDate")%> :</label>
+                <input class="field-input" name="labelstartdate" value="<%= dateRangeLabels.startDate %>" type="text" placeholder="<%=__('Label')%>">
+                <label><%=__("labelEndDate")%> :</label>
+                <input class="field-input" name="labelenddate" value="<%= dateRangeLabels.endDate %>" type="text" placeholder="<%=__('Label')%>">
+        </div>
+
         <div class="options">
             <label>
             <%=__("formFieldOptionsMessage")%> :</label>
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/Fields/DateRange.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/Fields/DateRange.html	(révision 12067)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/Fields/DateRange.html	(révision 12068)
@@ -1,11 +1,13 @@
-<label><%=field.mandatory?field.label+' *':field.label%><% if(field.description){%><span class="label-desc"><%=field.description%></span><%}%></label>
-<div class="daterange-container">
-  <div class="daterange-element">
-      <span class="from-text"><%= __("from") %></span>
+<fieldset class="form-fieldset data-row">
+  <legend><%=field.mandatory?field.label+' *':field.label%> <% if(field.description){%><span class="label-desc"><%=field.description%></span><%}%></legend>
+<div class="form-date-range">
+  <div class="form-date-range__option">
+      <label class="from-text"><%=labels.startDate%></label>
       <input type="date" placeholder="<%=field.placeholder%>" <%= field.required?'required="required"':''%> />
     </div>
-    <div class="daterange-element">
-      <span class="to-text"><%= __("to") %></span>
+    <div class="form-date-range__option">
+      <label class="to-text"><%=labels.endDate%></label>
       <input type="date" placeholder="<%=field.placeholder%>" <%= field.required?'required="required"':''%> />
     </div>
   </div>
+</fieldset>
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FieldBuilderView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FieldBuilderView.js	(révision 12067)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FieldBuilderView.js	(révision 12068)
@@ -7,6 +7,7 @@
             "click .delete":"onDeleteClick",
             'click input[name="previous-date"].field-input': 'onChangePreviousDate',
         },
+        
         tagName:"li",
         className:function () {
             return "form-element "+this.model.type;
@@ -37,9 +38,12 @@
                 var previousDateValue  = this.model.options[this.checkPreviousDateOption(this.model.options)];
                 previousDate = (previousDateValue === 'previousDate_true') ? true : false;
             }
+            var labels = {
+                startDate :  (typeof this.model.attributes.labelstartdate != 'undefined')? this.model.attributes.labelstartdate : ""  ,
+                endDate : (typeof this.model.attributes.labelenddate != 'undefined')? this.model.attributes.labelenddate : ""
+            }
+            this.$el.html(this.template({field:this.model, previousDate:previousDate,dateRangeLabels: labels }));
 
-            this.$el.html(this.template({field:this.model, previousDate:previousDate }));
-
             return this;
         },
         edit:function () {
@@ -46,7 +50,15 @@
             if( this.$el.hasClass("editing") ) {
                 this.cancel();
             } else {
+                //pour daterange, pour pouvoir revenir à ""
+                if(this.model.type == 'daterange'){
+                    if( typeof this.model.attributes['labelstartdate'] == 'undefined')
+                        this.model.attributes['labelstartdate'] = "";
+                    if( typeof this.model.attributes['labelenddate'] == 'undefined')
+                        this.model.attributes['labelenddate'] = "";
+                }
                 this.savedState = this.model.toJSON();
+
                 this.$el.addClass("editing");
                 this.$el.parentsUntil('.col-setup','.column-container').addClass("editing");
             }
@@ -56,6 +68,7 @@
             var isChecked = input.checked;
             this.setPreviousDateValue(this.model.options, "previousDate_"+isChecked);
         },
+
         //return l'index de l'un de ces string dans le tableau model.options
         checkPreviousDateOption: function (options) {
             return options.findIndex((function(item) {
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FieldView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FieldView.js	(révision 12067)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FieldView.js	(révision 12068)
@@ -39,12 +39,57 @@
             this.template = this.buildTemplate(daterangeTpl,i18n);
         },
         render:function () {
+ 
             if (this.model.type == "daterange") {
-                this.$el.html(this.template({field:this.model}));
+                this.copyLabelsAttributsValuesToOptions(this.model);
+                var obj = {
+                    startDate :  (typeof this.model.attributes.labelstartdate != 'undefined')? this.model.attributes.labelstartdate : ""  ,
+                    endDate : (typeof this.model.attributes.labelenddate != 'undefined')? this.model.attributes.labelenddate : ""
+                }
+                this.$el.html(this.template({field:this.model, labels : obj}));
             }else{
                 this.$el.html(this.templates[this.model.type]({field:this.model}));
             }
             return this;
+        },
+        //cette fonction ajoute des objets dans l'array model.attributs.options
+        copyLabelsAttributsValuesToOptions:function(model){
+
+            var key = "";
+            var labelValue = "";
+            var modelAttributes = model.attributes;
+            var optionsAttributes = modelAttributes.options;
+            if(modelAttributes.hasOwnProperty('labelstartdate')){
+                key = "labelstartdate";
+                labelValue = modelAttributes['labelstartdate'];
+                this.addItemToOptions(optionsAttributes,key,labelValue);
+                
+            }
+            if(modelAttributes.hasOwnProperty('labelenddate')){
+                key = "labelenddate";
+                labelValue = modelAttributes['labelenddate'];
+                this.addItemToOptions(optionsAttributes,key,labelValue);
+            }
+       
+        },
+
+        addItemToOptions: function(options, key, labelValue){
+
+            var optionsHasLabel = false;
+            for (var i = 0; i < options.length; i++) {
+                if (typeof options[i] === 'object' && options[i].hasOwnProperty(key)) {
+                    //changement de valeur si objet déjà dans options
+                    options[i][key] = labelValue;
+                    optionsHasLabel = true;
+                    break;
+                }
+            }
+            //ajout de l'objet si pas encore dans options
+            if (!optionsHasLabel) {
+                var obj = {};
+                obj[key] = labelValue;
+                options.push(obj);
+            }
         }
     });
     return FieldView;
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js	(révision 12067)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js	(révision 12068)
@@ -4,6 +4,8 @@
     "formUnderline":"souligné",
     "label":"Label",
     "Label":"Label",
+    "labelStartDate":"Label date de début",
+    "labelEndDate":"Label date de fin",
     "description":"Description",
     "placeholder":"Placeholder",
     "Placeholder":"Placeholder",
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js	(révision 12067)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js	(révision 12068)
@@ -4,6 +4,8 @@
     "formUnderline":"souligné",
     "label":"Label",
     "Label":"Label",
+    "labelStartDate":"Label date de début",
+    "labelEndDate":"Label date de fin",
     "description":"Description",
     "placeholder":"Placeholder",
     "Placeholder":"Placeholder",
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js	(révision 12067)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js	(révision 12068)
@@ -7,6 +7,8 @@
         "formUnderline":"is underlined",
         "label":"Label",
         "Label":"Label",
+        "labelStartDate":"Label start date",
+        "labelEndDate":"Label end date",
         "description":"Description",
         "placeholder":"Placeholder",
         "Placeholder":"Placeholder",
Index: src/less/imports/form_block/contentOptions.less
===================================================================
--- src/less/imports/form_block/contentOptions.less	(révision 12067)
+++ src/less/imports/form_block/contentOptions.less	(révision 12068)
@@ -136,6 +136,7 @@
                 &.file,
                 &.recipientlist,
                 &.date,
+                &.daterange,
                 &.checkbox {
                     .placeholder {
                         display: none;
@@ -257,13 +258,13 @@
     }
 }
 
-.daterange-container{
-    display: flex;
-    flex-direction: row;
-    align-items: stretch;
-    width: 100%;
+.form-date-range{
+	display: flex;
+	flex-direction: row;
+	align-items: stretch;
+	width: 100%;
 
-    .daterange-element{
+    .form-date-range__option{
         display: flex;
         align-items: center;
         width: 100%;
@@ -271,12 +272,14 @@
         .from-text{
             margin-right:5px;
             vertical-align: middle;
+            white-space: nowrap;
         }
-
+        
         .to-text{
             margin-right: 5px;
             margin-left: 5px;
             vertical-align: middle;
+            white-space: nowrap;
         }
     }
 }
\ No newline at end of file
