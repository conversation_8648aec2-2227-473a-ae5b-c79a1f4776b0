Revision: r13673
Date: 2024-12-26 12:27:02 +0300 (lkm 26 Des 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
Wishlist IDEO3.2: update Stats

## Files changed

## Full metadata
------------------------------------------------------------------------
r13673 | s<PERSON><PERSON><PERSON><PERSON>oa | 2024-12-26 12:27:02 +0300 (lkm 26 Des 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/DashBoard/DashBoard.js
   M /branches/ideo3_v2/integration/src/js/JEditor/StatsPanel/StatsPanel.js

Wishlist IDEO3.2: update Stats
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/DashBoard/DashBoard.js
===================================================================
--- src/js/JEditor/DashBoard/DashBoard.js	(révision 13672)
+++ src/js/JEditor/DashBoard/DashBoard.js	(révision 13673)
@@ -119,18 +119,13 @@
                                         break;
                                 }
                             }
-                            
                             //url pour iframe 
                             //check si codebouton non fr 
-                            var codeboutonsNonFr = /^(AGCCND|LNKUSA|LNKAUS)/.exec(__IDEO_SITE_CODE_BOUTON__);
+                           
                             var vhostId = __IDEO_VHOST_ID__ ? __IDEO_VHOST_ID__ : '';
                             var codebouton = __IDEO_SITE_CODE_BOUTON__;
-                            var url;
-                            if (codeboutonsNonFr != null) {
-                                url = 'https://stats.linkeo.ovh/index.php?id=' + key + '&ideo3=1&action=dashboard&lang=' + lang;
-                            } else {
-                                url = 'https://stats-gui-back-office.linkeo.ovh/dashboard?codebouton=' + codebouton + '&vhid=' + vhostId;
-                            }
+                          
+                            url = 'https://stats-gui-back-office.linkeo.ovh/dashboard?codebouton=' + codebouton + '&vhid=' + vhostId + '&lang=' + this.app.uiLanguage.id;
 
                             //code
                             this.setDOM();//création des variables this.dom[]
Index: src/js/JEditor/StatsPanel/StatsPanel.js
===================================================================
--- src/js/JEditor/StatsPanel/StatsPanel.js	(révision 13672)
+++ src/js/JEditor/StatsPanel/StatsPanel.js	(révision 13673)
@@ -92,25 +92,10 @@
                             var key = __IDEO_VHOST_ID_KEY__ ? __IDEO_VHOST_ID_KEY__ : __IDEO_CODE_BOUTON__;
                             var vhostId = __IDEO_VHOST_ID__ ? __IDEO_VHOST_ID__ : '';
                             var codebouton = __IDEO_SITE_CODE_BOUTON__;
-                            var prefixCodeboutonsNonFr = ["AGCCND", "LNKUSA", "LNKAUS"];
-                            var lang = (this.app.uiLanguage.id.substr(0, 2) == "fr") ? "fr" : "en";
-                            var url = "";
-
-                            var startsWithNonFr = false;
-
-                            for (var i = 0; i < prefixCodeboutonsNonFr.length; i++) {
-                                var prefix = prefixCodeboutonsNonFr[i];
-                                if (codebouton.indexOf(prefix) === 0) {
-                                    startsWithNonFr = true;
-                                    break;
-                                }
-                            }
+                            var lang = this.app.uiLanguage.id ;
                             
-                            if (startsWithNonFr) {
-                                url = `https://stats.linkeo.ovh/index.php?action=roi&id=${key}&ideo3=1&lang=${lang}`;
-                            } else {
-                                url = `https://stats-gui-back-office.linkeo.ovh/?codebouton=${codebouton}&vhid=${vhostId}`;
-                            }
+                            url = `https://stats-gui-back-office.linkeo.ovh/?codebouton=${codebouton}&vhid=${vhostId}&lang=${lang}`;
+                        
 
                             // recuperation de la langue pour transmettre à l'iframe stats
 
