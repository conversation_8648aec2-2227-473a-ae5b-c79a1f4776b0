Revision: r14037
Date: 2025-04-02 11:11:35 +0300 (lrb 02 Apr 2025) 
Author: rrakotoarinelina 

## Commit message
Whislist IDEO3.2 : Nom des icons + filtre recherche (comme les pages)

## Files changed

## Full metadata
------------------------------------------------------------------------
r14037 | rrakotoarinelina | 2025-04-02 11:11:35 +0300 (lrb 02 Apr 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/App/App.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/nls/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/Models
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/Models/BaseSvgCollection.js
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/Models/IconsSvgCollection.js
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/Models/LabelsSvgCollection.js
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/Models/SocialsSvgCollection.js
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/Models/Svg.js
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/Templates
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/Templates/selectIcon.html
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/Templates/selectLabel.html
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/Templates/selectSocial.html
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/Views
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/Views/SelectIconView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/Views/SelectLabelView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/Views/SelectSocialView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/nls
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/nls/de-de
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/nls/de-de/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/nls/en-au
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/nls/en-au/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/nls/en-us
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/nls/en-us/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/nls/es-es
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/nls/es-es/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/nls/fr-ca
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/nls/fr-ca/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/nls/fr-fr
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/nls/fr-fr/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/main.js
   A /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Mixins
   A /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Mixins/SvgLabelsHandlerMixin.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileListView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardBlockView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardListView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/main.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/PagePanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/ParamsPanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/ListSocialNetworks.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/SocialNetworks.js
   M /branches/ideo3_v2/integration/src/less/main.less

Whislist IDEO3.2 : Nom des icons + filtre recherche (comme les pages)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/App/App.js
===================================================================
--- src/js/JEditor/App/App.js	(révision 14036)
+++ src/js/JEditor/App/App.js	(révision 14037)
@@ -21,12 +21,15 @@
   "text!./Templates/application.html",
   "text!./Templates/appLoadingTemplate.html",
   "moment",
+  "JEditor/Commons/Svgs/Models/IconsSvgCollection",
+  "JEditor/Commons/Svgs/Models/LabelsSvgCollection",
+  "JEditor/Commons/Svgs/Models/SocialsSvgCollection",
   //not in params
   "jqueryPlugins/affix",
   "moment-fr","moment-fr-ca","moment-en-au","moment-en-ca"
 ], function($, _, Konami, User, Events, BabblerView, Model, Collection, View, MessageDelegate, MessageView,
   Config, Router, Cookie, Language, Params, UILanguageList, LanguagesDropDown,
-  translate, applicationTpl, loadingTemplate,moment) {
+  translate, applicationTpl, loadingTemplate,moment,IconsSvgCollection,LabelsSvgCollection,SocialsSvgCollection) {
   /**
    * CLass de l'application de base
    * class App
@@ -141,6 +144,12 @@
         this.listenTo(this.router, Events.BackboneEvents.ROUTE, this._onRoute);
         this.params.fetch();
         this.paramsSetting.fetch();
+        this.iconsSvgCollection = IconsSvgCollection.getInstance();
+			  this.iconsSvgCollection.fetch();
+        this.labelsSvgCollection = LabelsSvgCollection.getInstance();
+        this.labelsSvgCollection.fetch();
+        this.socialsSvgCollection = SocialsSvgCollection.getInstance();
+        this.socialsSvgCollection.fetch();
       },
       //step 1
       initUser: function() {
Index: src/js/JEditor/Commons/Files/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/Commons/Files/nls/fr-ca/i18n.js	(révision 14036)
+++ src/js/JEditor/Commons/Files/nls/fr-ca/i18n.js	(révision 14037)
@@ -42,5 +42,4 @@
     "edit" :"Éditer",
     "delete":"Supprimer",
     "allF"  :"Toutes",
-    "browseIconTitle":"Parcourir la base d'icônes",
 });
\ No newline at end of file
Index: src/js/JEditor/Commons/Files/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/Commons/Files/nls/fr-fr/i18n.js	(révision 14036)
+++ src/js/JEditor/Commons/Files/nls/fr-fr/i18n.js	(révision 14037)
@@ -43,5 +43,4 @@
     "edit" :"Éditer",
     "delete":"Supprimer",
     "allF"  :"Toutes",
-    "browseIconTitle":"Parcourir la base d'icônes",
 });
\ No newline at end of file
Index: src/js/JEditor/Commons/Files/nls/i18n.js
===================================================================
--- src/js/JEditor/Commons/Files/nls/i18n.js	(révision 14036)
+++ src/js/JEditor/Commons/Files/nls/i18n.js	(révision 14037)
@@ -44,5 +44,4 @@
     "delete": "Delete",
     "allF" : "All",
     "noneF":"None",
-    "browseIconTitle":"Browse the icon database",
 }, "fr-fr":true, "fr-ca":true })
\ No newline at end of file
Index: src/js/JEditor/Commons/Svgs/Models/BaseSvgCollection.js
===================================================================
--- src/js/JEditor/Commons/Svgs/Models/BaseSvgCollection.js	(nonexistent)
+++ src/js/JEditor/Commons/Svgs/Models/BaseSvgCollection.js	(révision 14037)
@@ -0,0 +1,33 @@
+define([
+    "JEditor/Commons/Ancestors/Models/Collection",
+    "JEditor/Commons/Svgs/Models/Svg",
+], function( Collection, Svg ) {
+    
+    var BaseSvgCollection = Collection.extend({
+        model: Svg,
+        
+        constructor: function(options) {
+            
+            Collection.apply(this, arguments);
+            this.svgName = (options && options.svgName) ? options.svgName : '';
+            this.type = this.constructor.type;
+        },
+
+        url: function() {
+            return __IDEO_API_PATH__ + "/resources-svgcollection/" + this.type + (this.svgName ? "/" + this.svgName : '');
+        },
+
+        parse: function(response) {
+            return response;
+        },
+
+        getSvgByName: function(selectedIcon) {
+            var svgObj = this.toJSON().filter(function(item) {
+                return item.name === selectedIcon;
+            })[0];
+            return svgObj;
+        }
+    });
+
+    return BaseSvgCollection;
+});
\ No newline at end of file
Index: src/js/JEditor/Commons/Svgs/Models/IconsSvgCollection.js
===================================================================
--- src/js/JEditor/Commons/Svgs/Models/IconsSvgCollection.js	(nonexistent)
+++ src/js/JEditor/Commons/Svgs/Models/IconsSvgCollection.js	(révision 14037)
@@ -0,0 +1,25 @@
+define([
+    "JEditor/Commons/Svgs/Models/BaseSvgCollection",
+    "JEditor/ParamsPanel/Models/Params",
+],function(
+	BaseSvgCollection,
+    Params
+){
+var IconsSvgCollection = BaseSvgCollection.extend({
+        constructor: function(options) {
+            BaseSvgCollection.apply(this, arguments);
+        }
+    });
+    this.params = Params.getInstance();
+    this.type =  this.params.attributes.IconsCollection || 'outline';
+    IconsSvgCollection.type = this.type;
+    IconsSvgCollection.instance = null;
+    IconsSvgCollection.getInstance = function() {
+        if (!this.instance) {
+            this.instance = new IconsSvgCollection();
+        }
+        return this.instance;
+    };
+
+    return IconsSvgCollection;
+});
\ No newline at end of file
Index: src/js/JEditor/Commons/Svgs/Models/LabelsSvgCollection.js
===================================================================
--- src/js/JEditor/Commons/Svgs/Models/LabelsSvgCollection.js	(nonexistent)
+++ src/js/JEditor/Commons/Svgs/Models/LabelsSvgCollection.js	(révision 14037)
@@ -0,0 +1,19 @@
+define([
+    "JEditor/Commons/Svgs/Models/BaseSvgCollection",
+],function(BaseSvgCollection){
+var LabelsSvgCollection = BaseSvgCollection.extend({
+        constructor: function(options) {
+            BaseSvgCollection.apply(this, arguments);
+        }
+    });
+    LabelsSvgCollection.type = 'labels';
+    LabelsSvgCollection.instance = null;
+    LabelsSvgCollection.getInstance = function() {
+        if (!this.instance) {
+            this.instance = new LabelsSvgCollection();
+        }
+        return this.instance;
+    };
+
+    return LabelsSvgCollection;
+});
\ No newline at end of file
Index: src/js/JEditor/Commons/Svgs/Models/SocialsSvgCollection.js
===================================================================
--- src/js/JEditor/Commons/Svgs/Models/SocialsSvgCollection.js	(nonexistent)
+++ src/js/JEditor/Commons/Svgs/Models/SocialsSvgCollection.js	(révision 14037)
@@ -0,0 +1,21 @@
+define([
+    "JEditor/Commons/Svgs/Models/BaseSvgCollection",
+],function(
+	BaseSvgCollection
+){
+var SocialsSvgCollection = BaseSvgCollection.extend({
+        constructor: function(options) {
+            BaseSvgCollection.apply(this, arguments);
+        }
+    });
+    SocialsSvgCollection.type = 'social';
+    SocialsSvgCollection.instance = null;
+    SocialsSvgCollection.getInstance = function() {
+        if (!this.instance) {
+            this.instance = new SocialsSvgCollection();
+        }
+        return this.instance;
+    };
+
+    return SocialsSvgCollection;
+});
\ No newline at end of file
Index: src/js/JEditor/Commons/Svgs/Models/Svg.js
===================================================================
--- src/js/JEditor/Commons/Svgs/Models/Svg.js	(nonexistent)
+++ src/js/JEditor/Commons/Svgs/Models/Svg.js	(révision 14037)
@@ -0,0 +1,15 @@
+define([
+    "JEditor/Commons/Ancestors/Models/Model",
+
+], function (Model) {
+
+    var Svg = Model.extend({
+        
+        defaults: {
+            'name': '',
+            'content': '',
+        },
+    });
+
+    return Svg;
+});

Property changes on: src/js/JEditor/Commons/Svgs/Models/Svg.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/Commons/Svgs/Templates/selectIcon.html
===================================================================
--- src/js/JEditor/Commons/Svgs/Templates/selectIcon.html	(nonexistent)
+++ src/js/JEditor/Commons/Svgs/Templates/selectIcon.html	(révision 14037)
@@ -0,0 +1,19 @@
+<div class="container-icon option-content">
+    <div id="form-search-svg">
+        <span class="search-svg-selector">
+            <input type="text" class="svg-search" placeholder="Recherche ..." value="">
+        </span>
+    </div>
+    <div class="grid-container-icon">
+        <%for(i=0;i < content.length;i++){
+            svg = content[i];
+            %>
+            <div  class="box-icon <%=selected === svg.name ? 'selected-icon':''%>" data-name="<%= svg.name%>"  >
+                    <span class="wrapper-icon">
+                        <%= svg.content%>
+                    </span>
+            </div>
+            <%}%>
+    </div>
+</div>
+
Index: src/js/JEditor/Commons/Svgs/Templates/selectLabel.html
===================================================================
--- src/js/JEditor/Commons/Svgs/Templates/selectLabel.html	(nonexistent)
+++ src/js/JEditor/Commons/Svgs/Templates/selectLabel.html	(révision 14037)
@@ -0,0 +1,35 @@
+<div class="container-icon option-content baseFile">
+    <header class="popup-bar"> 
+        <a href="javascript:;" class="onglet mycollections active" data-switchto="labels">
+            Labels
+        </a>
+        <!-- <a href="javascript:;" class="onglet mycollections " data-switchto="LinkeoStock">
+            LinkeoStock
+        </a> -->
+   </header>
+    <div id="form-search-svg">
+        <span class="search-svg-selector">
+            <input type="text" class="svg-search" placeholder="Recherche ..." value="">
+        </span>
+    </div>
+    <div class="grid-container-icon content">
+        <%for(i=0;i<content.length;i++){
+            file = content[i];
+            %>
+            <%if (type === 'labels'){%>
+                <div  class="oneFile <%=selected === file.name ? 'selected-icon':''%>" data-name="<%= file.name%>" >
+                    <span class="select"><span class="icon-check"></span></span>
+                    <span class="wrapper-icon">
+                        <%= file.content%>
+                    </span>
+                </div>
+            <%} else {%>
+                <div  class="oneFile <%=selected === file.name ? 'selected-icon':''%>" style="background-image: url(<%=file.url%>);" data-url="<%=file.url%>" >
+                    <span class="select"><span class="icon-check"></span></span>
+                </div>
+           <%}%>
+           
+            <%}%>
+    </div>
+</div>
+
Index: src/js/JEditor/Commons/Svgs/Templates/selectSocial.html
===================================================================
--- src/js/JEditor/Commons/Svgs/Templates/selectSocial.html	(nonexistent)
+++ src/js/JEditor/Commons/Svgs/Templates/selectSocial.html	(révision 14037)
@@ -0,0 +1,14 @@
+<div class="container-icon option-content baseFile">
+    <div class="grid-container-icon content">
+        <%for(i=0;i<content.length;i++){
+            file = content[i]; %>
+            <div  class="oneFile <%=selected === file.name ? 'selected-icon':''%>" data-name="<%= file.name%>" data-id="<%=i%>" >
+                <span class="select"><span class="icon-check"></span></span>
+                <span class="wrapper-icon">
+                    <%= file.content%>
+                </span>
+            </div>
+            <%}%>
+    </div>
+</div>
+
Index: src/js/JEditor/Commons/Svgs/Views/SelectIconView.js
===================================================================
--- src/js/JEditor/Commons/Svgs/Views/SelectIconView.js	(nonexistent)
+++ src/js/JEditor/Commons/Svgs/Views/SelectIconView.js	(révision 14037)
@@ -0,0 +1,89 @@
+define([
+    "jquery",
+    "underscore",
+    "text!../Templates/selectIcon.html",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/DialogView",
+    "JEditor/Commons/Svgs/Models/IconsSvgCollection",
+    "i18n!../nls/i18n",
+    //not in params
+    "jqueryPlugins/dropdown"
+], function($, _, selectIcon, Events, DialogView,IconsSvgCollection, translate) {
+    var SelectIconView = DialogView.extend({
+        className: 'selectIcon',
+        events: {
+            'click .box-icon': 'onSelect',
+            'input .svg-search': 'onSearch',
+            // 'click [data-select]': '_onSelectClick',
+        },
+        currentList: [],
+        constructor: function(options) {
+            var opts = _.extend({
+                title: translate("browseIconTitle"),
+                buttons: [
+                    {
+                        text: translate("choose"),
+                        class: 'okay',
+                        click: _.bind(this.onOk, this)
+                    },
+                    {
+                        text: translate("cancel"),
+                        class: 'cancel',
+                        click: _.bind(this.onCancel, this)
+                    }
+                ],
+                width: 720,
+                height: 600
+            }, options);
+            return DialogView.call(this, opts);
+        },
+        initialize: function(options) {
+            this._super();
+            this._template = this.buildTemplate(selectIcon, translate);
+
+        },
+        getIcons: function(usedIcon){
+            this.iconsSvgCollection = IconsSvgCollection.getInstance();
+            this.selected = (usedIcon !== '' ? usedIcon : '');
+            this.render();
+        },
+        render: function() {
+           
+            var data = (this.iconsSvgCollection === undefined ? [] : this.iconsSvgCollection.toJSON());
+            this._super();
+            this.undelegateEvents();
+             this.$el.html(this._template({content:data, selected: this.selected}));
+            this.delegateEvents();
+            return this;
+        },
+        onCancel: function() {
+            $('.box-icon').css("background-color", "transparent");
+            this.$el.dialog('close');
+        },
+        onOk: function() {
+            var selectedIcon = this.selected;
+            var svgObj = this.iconsSvgCollection.getSvgByName(selectedIcon);
+            this.trigger(Events.ButtonEvents.SELECT_ICON, svgObj );
+            this.$el.dialog('close');
+        },
+        onSelect: function(e){
+            $('.box-icon').css("background-color", "transparent");
+            var $target = $(e.currentTarget);
+            $target.css('background-color', '#41ffbe');
+            var name = $target.data('name');
+            this.selected = name;
+        },
+        onSearch: function(e) {
+            var searchTerm = $(e.currentTarget).val().toLowerCase();
+            $('.box-icon').each(function() {
+                var iconName = $(this).data('name').toLowerCase();
+                if (iconName.includes(searchTerm)) {
+                    $(this).css('display', 'block');
+                } else {
+                    $(this).css('display', 'none');
+                }
+            });
+        },
+    });
+    return SelectIconView;
+});
Index: src/js/JEditor/Commons/Svgs/Views/SelectLabelView.js
===================================================================
--- src/js/JEditor/Commons/Svgs/Views/SelectLabelView.js	(nonexistent)
+++ src/js/JEditor/Commons/Svgs/Views/SelectLabelView.js	(révision 14037)
@@ -0,0 +1,114 @@
+define([
+    "jquery",
+    "underscore",
+    "text!../Templates/selectLabel.html",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/DialogView",
+    "JEditor/Commons/Svgs/Models/LabelsSvgCollection",
+    "i18n!../nls/i18n",
+    //not in params
+    "jqueryPlugins/dropdown"
+], function($, _, selectIcon, Events, DialogView,LabelsSvgCollection, translate) {
+    var SelectLabelView = DialogView.extend({
+        className: 'selectIcon',
+        events: {
+            'click .oneFile': 'onSelect',
+            'click a[data-switchto]': '_switchListCollection',
+            'input .svg-search' : 'onSvgSearch'
+        },
+        currentList: [],
+        currentType : 'labels',
+        constructor: function(options) {
+            if (!options)
+            var options = {};
+            options.width = 750;
+            options.title = translate('browseLabelTitle')
+            options.height = 600;
+            options.allowMultipleSelect = true;
+            options.buttons = [
+                {
+                    class: 'okay',
+                    text: translate("okay"),
+                    click: _.bind(this.onOk, this)
+                },
+                {
+                    text: translate("cancel"),
+                    class: 'cancel',
+                    click: _.bind(this.onCancel, this)
+                }
+            ]
+            DialogView.call(this, options);
+            this.on('open close', this.onToggle);
+        },
+        initialize: function(options) {
+            this._super();
+            this._template = this.buildTemplate(selectIcon, translate);
+            this.currentList = this.options.svgList;
+            this.render();
+        },
+        getIcons: function(){
+            this.labelsSvgCollection =  LabelsSvgCollection.getInstance();
+            this.render();
+        },
+        _switchListCollection : function(e){
+            var $target = $(e.currentTarget);
+            this.dom[this.cid].type.removeClass('active');
+            $target.addClass('active');
+            this.currentType = $target.data('switchto');
+        },
+        render: function() {
+            this.selected = {};
+            this.selectedLength = 0;
+            var data = (this.labelsSvgCollection === undefined ? [] : this.labelsSvgCollection.toJSON());
+            this._super();
+            this.undelegateEvents();
+            this.$el.html(this._template({content:data , type:'labels', selected: this.selected}));
+            this.dom[this.cid].type = this.$('.onglet.mycollections');
+            this.delegateEvents();
+            return this;
+        },
+        onSvgSearch: function(e){
+            var searchTerm = value = e.target.value.toLowerCase();
+                $('.grid-container-icon .oneFile').each(function() {
+                    var iconName = $(this).data('name').toLowerCase();
+                    if (iconName.includes(searchTerm)) {
+                        $(this).show();
+                    } else {
+                        $(this).hide();
+                    }
+                });
+        },
+        onCancel: function() {
+            $('.box-icon').css("background-color", "transparent");
+            this.selected = {};
+            this.$el.dialog('close');
+        },
+        onOk: function() {
+            this.trigger(Events.ListViewEvents.SELECT_ICON, this.selected);
+            this.$el.dialog('close');
+        },
+        onSelect: function(e) {
+            var $target = $(e.currentTarget);
+            var name = $target.data('name');
+            var content = $target.find('.wrapper-icon').html().trim();
+            var type = 'svg';
+            var selected = {
+                'name':name,
+                'content': content,
+                'type' : type
+            }
+            if (this.selected.hasOwnProperty(name)) {
+                delete  this.selected[selected.name];
+                $target.removeClass('selected');
+                this.selectedLength--;
+            }
+            else {
+                this.selected[selected.name] = selected;
+                this.selectedLength++;
+                $target.toggleClass('selected');
+            }
+        },
+    
+    });
+    return SelectLabelView;
+});

Property changes on: src/js/JEditor/Commons/Svgs/Views/SelectLabelView.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/Commons/Svgs/Views/SelectSocialView.js
===================================================================
--- src/js/JEditor/Commons/Svgs/Views/SelectSocialView.js	(nonexistent)
+++ src/js/JEditor/Commons/Svgs/Views/SelectSocialView.js	(révision 14037)
@@ -0,0 +1,77 @@
+define([
+    "jquery",
+    "underscore",
+    "text!../Templates/selectSocial.html",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/DialogView",
+    "JEditor/Commons/Svgs/Models/SocialsSvgCollection",
+    "i18n!../nls/i18n",
+    //not in params
+    "jqueryPlugins/dropdown"
+], function($, _, selectSocial, Events, DialogView,SocialsSvgCollection, translate) {
+    var SelectSocialView = DialogView.extend({
+        className: 'socialSvgSelector',
+        events: {
+            'click .oneFile': 'onSelect',
+        },
+        currentList: [],
+        currentType : 'social',
+        constructor: function(options) {
+            var opts = _.extend({
+                title: translate('browseIconTitle'),
+                buttons: [
+                    {
+                        text: translate('choose'),
+                        class: 'okay',
+                        click: _.bind(this.onOk, this)
+                    },
+                    {
+                        text: translate('cancel'),
+                        class: 'cancel',
+                        click: _.bind(this.onCancel, this)
+                    }
+                ],
+                width: 720,
+                height: 600,
+                allowMultipleSelect: false
+            }, options);
+            return DialogView.call(this, opts);
+        },
+        initialize: function(options) {
+            this._super();
+            this._template = this.buildTemplate(selectSocial, translate);
+            this.socialsSvgCollection = SocialsSvgCollection.getInstance();
+            this.currentList = this.options.svgList;
+            this.render();
+        },
+        render: function() {
+            this.selected = {};
+            var data = (this.socialsSvgCollection === undefined ? [] : this.socialsSvgCollection.toJSON());
+            this._super();
+            this.undelegateEvents();
+            this.$el.html(this._template({content:data , type:'social', selected: this.selected}));
+            this.dom[this.cid].selections = this.$('.oneFile');
+            this.dom[this.cid].type = this.$('.onglet.mycollections');
+            this.delegateEvents();
+            return this;
+        },
+        onCancel: function() {
+            $('.box-icon').css("background-color", "transparent");
+            this.selected = {};
+            this.$el.dialog('close');
+        },
+        onOk: function() {
+            this.trigger(Events.ListSocialNetworks.SELECT_ICON, this.selected);
+            this.$el.dialog('close');
+        },
+        onSelect: function(e) {
+            var $target = $(e.currentTarget), name = $target.data('name');
+            this.dom[this.cid].selections.removeClass('selected');
+            this.selected = this.socialsSvgCollection.getSvgByName(name);
+            $target.toggleClass('selected');
+            this.trigger(Events.ListViewEvents.SELECT,this.selected, this.selected?1:0,this.svgCollection);
+        },
+    
+    });
+    return SelectSocialView;
+});
Index: src/js/JEditor/Commons/Svgs/nls/de-de/i18n.js
===================================================================
--- src/js/JEditor/Commons/Svgs/nls/de-de/i18n.js	(nonexistent)
+++ src/js/JEditor/Commons/Svgs/nls/de-de/i18n.js	(révision 14037)
@@ -0,0 +1 @@
+define({});
\ No newline at end of file

Property changes on: src/js/JEditor/Commons/Svgs/nls/de-de/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/Commons/Svgs/nls/en-au/i18n.js
===================================================================
--- src/js/JEditor/Commons/Svgs/nls/en-au/i18n.js	(nonexistent)
+++ src/js/JEditor/Commons/Svgs/nls/en-au/i18n.js	(révision 14037)
@@ -0,0 +1 @@
+define({});
\ No newline at end of file

Property changes on: src/js/JEditor/Commons/Svgs/nls/en-au/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/Commons/Svgs/nls/en-us/i18n.js
===================================================================
--- src/js/JEditor/Commons/Svgs/nls/en-us/i18n.js	(nonexistent)
+++ src/js/JEditor/Commons/Svgs/nls/en-us/i18n.js	(révision 14037)
@@ -0,0 +1 @@
+define({});
\ No newline at end of file

Property changes on: src/js/JEditor/Commons/Svgs/nls/en-us/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/Commons/Svgs/nls/es-es/i18n.js
===================================================================
--- src/js/JEditor/Commons/Svgs/nls/es-es/i18n.js	(nonexistent)
+++ src/js/JEditor/Commons/Svgs/nls/es-es/i18n.js	(révision 14037)
@@ -0,0 +1 @@
+define({});
\ No newline at end of file

Property changes on: src/js/JEditor/Commons/Svgs/nls/es-es/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/Commons/Svgs/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/Commons/Svgs/nls/fr-ca/i18n.js	(nonexistent)
+++ src/js/JEditor/Commons/Svgs/nls/fr-ca/i18n.js	(révision 14037)
@@ -0,0 +1,7 @@
+define({
+    "browseIconTitle":"Parcourir la base d'icônes",
+    "cancel": "Annuler",
+    "choose": "Choisir",
+    "okay": "Ok",
+    "browseLabelTitle":"Parcourir la base d'images",
+});
\ No newline at end of file
Index: src/js/JEditor/Commons/Svgs/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/Commons/Svgs/nls/fr-fr/i18n.js	(nonexistent)
+++ src/js/JEditor/Commons/Svgs/nls/fr-fr/i18n.js	(révision 14037)
@@ -0,0 +1,7 @@
+define({
+    "browseIconTitle":"Parcourir la base d'icônes",
+    "cancel": "Annuler",
+    "choose": "Choisir",
+    "okay": "Ok",
+    "browseLabelTitle":"Parcourir la base d'images",
+});
\ No newline at end of file

Property changes on: src/js/JEditor/Commons/Svgs/nls/fr-fr/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/Commons/Svgs/nls/i18n.js
===================================================================
--- src/js/JEditor/Commons/Svgs/nls/i18n.js	(nonexistent)
+++ src/js/JEditor/Commons/Svgs/nls/i18n.js	(révision 14037)
@@ -0,0 +1,9 @@
+define({ "root": 
+    {
+        "browseIconTitle":"Browse the icon database",
+        "cancel": "Cancel",
+        "choose": "Choose",
+        "okay": "Ok",
+        "browseLabelTitle":"Browse the image database",
+    }
+    , "fr-fr":true, "fr-ca":true })
\ No newline at end of file

Property changes on: src/js/JEditor/Commons/Svgs/nls/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/Commons/main.js
===================================================================
--- src/js/JEditor/Commons/main.js	(révision 14036)
+++ src/js/JEditor/Commons/main.js	(révision 14037)
@@ -22,9 +22,6 @@
     "JEditor/Commons/Ancestors/Views/SaveCancelPanel",
     "JEditor/Commons/Ancestors/Views/TabbedView",
     "JEditor/Commons/Ancestors/Views/View",
-    "JEditor/Commons/Basefile/Models/LabelsCollection",
-    "JEditor/Commons/Basefile/Models/SocialsCollection",
-    "JEditor/Commons/Basefile/Models/Svg",
     "JEditor/Commons/Events",
     "JEditor/Commons/Files/Models/File",
     "JEditor/Commons/Files/Models/FileCollection",
@@ -41,7 +38,6 @@
     "JEditor/Commons/Files/Views/ReadOnlyFileListManagerView",
     "JEditor/Commons/Files/Views/ReadOnlyFileListView",
     "JEditor/Commons/Files/Views/SelectFileView",
-    "JEditor/Commons/Files/Views/SocialSvgSelectorDialog",
     "JEditor/Commons/Languages/Models/ContentLanguageList",
     "JEditor/Commons/Languages/Models/Language",
     "JEditor/Commons/Languages/Models/LanguageCollection",
@@ -67,6 +63,14 @@
     "JEditor/Commons/Pages/Views/PageSelector",
     "JEditor/Commons/Pages/Views/PageSelectorDialog",
     "JEditor/Commons/Pages/Views/PageSelectorWrapper",
+    "JEditor/Commons/Svgs/Models/BaseSvgCollection",
+    "JEditor/Commons/Svgs/Models/IconsSvgCollection",
+    "JEditor/Commons/Svgs/Models/LabelsSvgCollection",
+    "JEditor/Commons/Svgs/Models/SocialsSvgCollection",
+    "JEditor/Commons/Svgs/Models/Svg",
+    "JEditor/Commons/Svgs/Views/SelectIconView",
+    "JEditor/Commons/Svgs/Views/SelectLabelView",
+    "JEditor/Commons/Svgs/Views/SelectSocialView",
     "JEditor/Commons/Utils/main",
     "JEditor/Commons/main"
 ])
\ No newline at end of file
Index: src/js/JEditor/FilePanel/Mixins/SvgLabelsHandlerMixin.js
===================================================================
--- src/js/JEditor/FilePanel/Mixins/SvgLabelsHandlerMixin.js	(nonexistent)
+++ src/js/JEditor/FilePanel/Mixins/SvgLabelsHandlerMixin.js	(révision 14037)
@@ -0,0 +1,65 @@
+define([
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Svgs/Views/SelectLabelView"
+], function(Events, SelectLabelView) {
+    return {
+        initializeIcons: function() {
+            this.selectIconDialog = new SelectLabelView();
+            this.listenTo(this.selectIconDialog, Events.ListViewEvents.SELECT_ICON, this.onSelectedIcon);
+        },
+
+        showIcons: function() {
+            this.selectIconDialog.getIcons();
+            this.selectIconDialog.open();
+        },
+
+        onSelectedIcon: function(files) {
+            var self = this;
+            _.each(files, function(file) {
+                if(file.type == 'svg') {
+                    var blob = new Blob([file.content], { type: 'image/svg+xml' });
+                    self.uploaderFichier(blob, file.name);
+                } else {
+                    self.telecharger(file);
+                }
+            });
+        },
+
+        uploaderFichier: function(blob, fileName) {
+            var that = this;
+            if (fileName && blob) {
+                var postData = new FormData();
+                postData.append('name', fileName);
+                postData.append('value', blob, fileName);
+                $.ajax({
+                    type: "POST", 
+                    context: that,
+                    url: __IDEO_UPLOAD_URL__ ? __IDEO_UPLOAD_URL__ : '/admin/resources',
+                    data: postData,
+                    contentType: false,
+                    processData: false,
+                    success: function(data) {
+                        that.handleImportLabelSuccess(data);
+                    }
+                });
+            }
+        },
+
+        telecharger: function(fichier) {
+            var self = this;
+            fetch(fichier.url, {
+                method: 'GET'
+            })
+            .then(function(response) {
+                if (!response.ok) throw new Error('Download error');
+                return response.blob();
+            })
+            .then(function(data) {
+                self.uploaderFichier(data, fichier.name);
+            })
+            .catch(function(error) {
+                console.error('Download error:', error);
+            });
+        }
+    };
+});
Index: src/js/JEditor/FilePanel/Views/FileListView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileListView.js	(révision 14036)
+++ src/js/JEditor/FilePanel/Views/FileListView.js	(révision 14037)
@@ -8,12 +8,13 @@
     "JEditor/Commons/Events",
     "JEditor/Commons/Ancestors/Views/ListView",
     "JEditor/Commons/Utils",
-    "JEditor/FilePanel/Views/SelectIconView",
+    "JEditor/Commons/Svgs/Views/SelectLabelView",
+    "JEditor/FilePanel/Mixins/SvgLabelsHandlerMixin",
     "i18n!../nls/i18n",
     //not in params
     "jqueryPlugins/uploader"
-], function($, _, fileList, groupFileList, emptyGroupFileList, emptyFileList, Events, ListView, Utils, SelectIconView, translate) {
-    var FileListView = ListView.extend({
+], function($, _, fileList, groupFileList, emptyGroupFileList, emptyFileList, Events, ListView, Utils, SelectLabelView,SvgLabelsHandlerMixin, translate) {
+    var FileListView = ListView.extend(_.extend({},SvgLabelsHandlerMixin, {
         selected: {},
         selectedLength: 0,
         attributes: {
@@ -44,9 +45,7 @@
             this.listenTo(this.collection, Events.BackboneEvents.ADD, this._rebuildList);
             this.listenTo(this.collection, Events.BackboneEvents.REMOVE, this._rebuildList);
             this.listenTo(this.collection, Events.BackboneEvents.CHANGE, this._rebuildList);
-            this.selectIconDialog = new SelectIconView();
-            this.listenTo( this.selectIconDialog,Events.ListViewEvents.SELECT_ICON,this.onSelectedIcon);
-
+            this.initializeIcons();
         },
         onRequest: function (e,r) {
             if (e.models) {
@@ -316,64 +315,13 @@
                 this.collection.fetch({remove: false});
             }
         },
-        onSelectedIcon:function(files){
-        var self = this;
-            _.each(files, function (file) {
-                if(file.type == 'svg'){
-                    var blob = new Blob([file.content ], { type: 'image/svg+xml' });
-                    self.uploaderFichier(blob,file.name);
-               }else{
-                self.telecharger(file);
-               }
-            });     
-          
-        },
-        // Fonction pour télécharger le fichier s'il s'agit d'un lien externe
-        telecharger: function (fichier) {
-            const self = this;
+
+        // depuis le mixin
+        handleImportLabelSuccess: function(data) {
+            this.resetFilter();
            
-            fetch(fichier.url, {
-                method: 'GET',
-            })
-            .then(function (response){
-                if (!response.ok) {
-                throw new Error('Erreur lors du téléchargement du fichier');
-                }
-                return response.blob();
-            })
-            .then(function(data) {
-                // Appeler la fonction d'upload avec le fichier téléchargé
-                self.uploaderFichier(data, fichier.name);
-            })
-            .catch(function (error) {
-                console.error('Erreur lors du téléchargement du fichier:', error);
-            });
-        },
-        // Fonction pour uploader les fichier à partir d'un blob d'image 
-        uploaderFichier: function (blob, fileName) {
-            var that = this;
-            if (fileName && blob) {
-                var postData = new FormData();
-                    postData.append('name', fileName);
-                    postData.append('value', blob, fileName);
-                    $.ajax({
-                        type: "POST",
-                        context: that,
-                        url: __IDEO_UPLOAD_URL__ ? __IDEO_UPLOAD_URL__ : '/admin/resources',
-                        data: postData,
-                        contentType: false,
-                        processData: false,
-                        success: function(data, xhr, statut) {
-                            that.resetFilter();
-                        }
-                    });
-            }
-          },
-        showIcons: function (){
-            this.selectIconDialog.getIcons();
-            this.selectIconDialog.open();
         }
-    });
+    }));
     var regexpImg = /^image/;
     FileListView.FILTER_IMAGES_ONLY = function(file) {
         return regexpImg.test(file.mimeType);
Index: src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 14036)
+++ src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 14037)
@@ -130,7 +130,6 @@
     "ImageAddToCollection" : " L’image à bien été ajoutée à la collection",
     "fontExiste": "Cette font existe déjà",
     "addFileExist": "Ajouter des images existantes",
-    "browseIconTitle":"Parcourir la base d'icônes",
     "showIcons" : "Importer depuis une base d'images",
     "mimeNotsupporte": "non pris en charge. Échec de la création d'une ressource",
     "NewsFiles" :"Entêtes articles",
Index: src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 14036)
+++ src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 14037)
@@ -134,7 +134,6 @@
     "ImageAddToCollection" : " L’image à bien été ajoutée à la collection",
     "fontExiste": "Cette font existe déjà",
     "addFileExist": "Ajouter des images existantes",
-    "browseIconTitle":"Parcourir la base d'icônes",
     "showIcons" : "Importer depuis une base d'images",
     "mimeNotsupporte": "non pris en charge. Échec de la création d'une ressource",
     "NewsFiles" :"Entêtes articles",
Index: src/js/JEditor/FilePanel/nls/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/i18n.js	(révision 14036)
+++ src/js/JEditor/FilePanel/nls/i18n.js	(révision 14037)
@@ -136,7 +136,6 @@
         "ImageAddToCollection" : " The image has been added to the collection",
         "fontExiste":"This font already exists",
         "addFileExist": "Add existing images",
-        "browseIconTitle":"Browse the icon database",
         "showIcons" : "Import from image database",
         "mimeNotsupporte":"not supported. Create resource failed",
         "NewsFiles" :"Article headers",
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js	(révision 14036)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js	(révision 14037)
@@ -2,7 +2,7 @@
     "JEditor/PagePanel/Contents/Blocks/Block/Views/BlockView",
     "text!../Templates/buttonBlock.html",
     "i18n!../nls/i18n",
-    "JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/SvgCollection",
+    "JEditor/Commons/Svgs/Models/IconsSvgCollection",
     "JEditor/Commons/Ancestors/Views/SaveCancelPanel",
     "./ButtonOptionView",
     "./ButtonStyleOptionView",
@@ -9,9 +9,10 @@
     "./ButtonCTAOptionView",
     "JEditor/PagePanel/Contents/Options/Views/AdvancedOptionView",
     "./../Models/CTACollection",
+    "JEditor/Commons/Events",
     "JEditor/PagePanel/Contents/Blocks/ButtonBlock/Utils/Common",
     "JEditor/App/Messages/Confirm"
-], function(BlockView, template, translate,SvgCollection, SaveCancelPanel, ButtonOptionView, ButtonStyleOptionView,ButtonCTAOptionView, AdvancedOptionView,CTACollection,Common,Confirm) {
+], function(BlockView, template, translate,IconsSvgCollection, SaveCancelPanel, ButtonOptionView, ButtonStyleOptionView,ButtonCTAOptionView, AdvancedOptionView,CTACollection,Events,Common,Confirm) {
     /**
      * Vue des blocs d'images
      * @class ImageBlockView
@@ -54,10 +55,15 @@
                             });
                             this.listenTo(this.model, 'change', this.onModelChange);
                             this.delegateEvents();         
-                            this.svgContent = '';   
+                            this.svgContent = '';  
+                            
+                            //svg 
+                            this.iconsSvgCollection = IconsSvgCollection.getInstance();
                             this.svgName = this.model.options.ButtonStyleOption.icon;
-                            if(this.svgName) this.fetchSvg(this.svgName);       
-                            
+                            if(this.svgName){
+                                this.svgContent   = this.iconsSvgCollection.getSvgByName(this.svgName).content;
+                            } 
+
                         },
                         removeIcon: function(){
                             this.$(".blk-button__icon").empty();
@@ -88,7 +94,6 @@
                             this.render();
                         },
                         render: function() {
-                            var iconContent ="";
 
                             this._super();
                             var textButton = this.model.options.ButtonOption.text?this.model.options.ButtonOption.text:translate('button');
@@ -98,34 +103,16 @@
                             var color = this.model.options.ButtonStyleOption.color;
                             var isDifferentText = this.model.options.ButtonOption.isDifferentText;
                             var textOnMobile = this.model.options.ButtonOption.textOnMobile;
-                            this.$(".blk-button__label").css('margin-top','');
-                            this.$(".blk-button__icon").empty();
-                            this.$('.content').append(this._contentTemplate({icon: iconContent,size: sizeButton, text: textButton, buttonAlignment:alignButton, textAlignment:alignText, color :color, isDifferentText: isDifferentText, textOnMobile: textOnMobile}));
+                            if(this.svgContent !== '')
+                                this.$(".blk-button__icon").empty();
+                            this.$('.content').append(this._contentTemplate({icon: this.svgContent,size: sizeButton, text: textButton, buttonAlignment:alignButton, textAlignment:alignText, color :color, isDifferentText: isDifferentText, textOnMobile: textOnMobile}));
+                            if(this.svgContent !== '')
+                                this.$(".blk-button__label").css('margin-top','-15px');
+                            
                         },
-                        fetchSvg: function (svgName) {
-                            var svgCollectionObj = new SvgCollection({"svgName":svgName});
-                            svgCollectionObj.fetchIcons(_.bind(function(error, svg) {
-                                if (error) {
-                                    console.error(error);
-                                } else {
-                                    this.svgContent = svg.content;
-                                    this.$(".blk-button__label").css('margin-top','-15px');
-                                    this.$(".blk-button__icon").empty();
-                                    this.$(".blk-button__icon").append(this.svgContent);
-                                }
-                            },this));
-                        },
+
                         renderOptions: function(model, options) {
-                            this.$(".blk-button__icon").empty();
-                            this.$(".blk-button__label").css('margin-top','');
-                            var svgName = this.model.options.ButtonStyleOption.icon;
-                            // fetch icon content
-                            if(svgName !== "" && svgName !== this.svgName && model.attributes.optionType === "ButtonStyleOption" ){
-                                this.fetchSvg(svgName);
-                            }
-                            this.$(".blk-button__label").css('margin-top','-15px');
-                            this.$(".blk-button__icon").empty();
-                            this.$(".blk-button__icon").append(this.svgContent);
+
                             var sizeButton = this.model.options.ButtonStyleOption.size;
                             var alignButton = this.model.options.ButtonStyleOption.buttonAlignment;
                             var alignText = this.model.options.ButtonStyleOption.textAlignment;
@@ -149,7 +136,12 @@
                             buttonStylesOptionView = new ButtonStyleOptionView({
                                 model: options.ButtonStyleOption
                             });
-
+                            this.listenTo(buttonStylesOptionView.selectIconDialog,Events.ButtonEvents.SELECT_ICON,function(){
+                                if(options.ButtonStyleOption.icon != "")
+                                    var svg = this.iconsSvgCollection.getSvgByName(options.ButtonStyleOption.icon);
+                                    this.svgContent = svg.content;
+                                    this.render();
+                            });
                             advancedView = new AdvancedOptionView({
                                 model: options.advancedCSS
                             });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js	(révision 14036)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js	(révision 14037)
@@ -4,12 +4,10 @@
     "JEditor/Commons/Ancestors/Views/SaveCancelPanel",
     "text!../Templates/buttonStyleOption.html",
     "JEditor/PagePanel/Contents/Options/Views/AbstractOptionView",
-    "JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/SelectIconView",
-    "JEditor/ParamsPanel/Models/Params",
-    "JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/SvgCollection",
-    "JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/Svg",
+    "JEditor/Commons/Svgs/Views/SelectIconView",
+    "JEditor/Commons/Svgs/Models/IconsSvgCollection",
     "i18n!../nls/i18n"
-], function($,Events,SaveCancelPanel, buttonStyleOption, AbstractOptionView,SelectIconView,Params, SvgCollection,Svg,translate) {
+], function($,Events,SaveCancelPanel, buttonStyleOption, AbstractOptionView,SelectIconView, IconsSvgCollection,translate) {
     var ButtonStyleOptionView = AbstractOptionView.extend(
             {
                 optionType: 'ButtonStyleOption',
@@ -100,16 +98,11 @@
                         },
 
                         getIconContent:function(svgName){
-                            var svgCollectionObj = new SvgCollection({"svgName":svgName});
-                            svgCollectionObj.fetchIcons(_.bind(function(error, svg) {
-                                if (error) {
-                                    console.error(error);
-                                } else {
-                                    this.selectedIconEl.html(svg.content);
-                                    this.selectedIconEl.show();
-                                    $('.btn-browse-icons').show(); 
-                                }
-                            },this));
+                            this.iconsSvgCollection =  IconsSvgCollection.getInstance();
+                            this.svgJSON = this.iconsSvgCollection.getSvgByName(svgName);
+                            this.selectedIconEl.html(this.svgJSON.content);
+                            this.selectedIconEl.show();
+                            $('.btn-browse-icons').show(); 
                 
                         },
 
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js	(révision 14036)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js	(révision 14037)
@@ -56,7 +56,6 @@
     "pastel-lead":"Pastel Lead",
     "vibrante-lead":"Vibrante Lead",
     "contour-lead":"Contour Lead",
-    "browseIconTitle":"Parcourir la base d'icônes",
     "choose":"Choisir",
     "advancedCSS": "Avancé",
     "DifferentTextOnMobile": "Texte différent sur mobile ?",
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js	(révision 14036)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js	(révision 14037)
@@ -58,7 +58,6 @@
         "pastel-lead":"Pastel Lead",
         "vibrante-lead":"Vibrant Lead",
         "contour-lead":"Contour Lead",
-        "browseIconTitle":"Browse the icon database",
         "choose":"Choose",
         "advancedCSS": "Advanced",
         "DifferentTextOnMobile": "Different text on mobile ?",
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardBlockView.js	(révision 14036)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardBlockView.js	(révision 14037)
@@ -5,9 +5,8 @@
         "JEditor/PagePanel/Contents/Blocks/Block/Views/BlockView",
         "text!../Templates/CardBlock.html",
         "i18n!../nls/i18n",
-        "JEditor/PagePanel/Contents/Blocks/CardBlock/Models/SvgCollection",
     ],
-    function($, _, BlockView, template, translate, SvgCollection) {
+    function($, _, BlockView, template, translate) {
         /**
          * Vue des blocs cartes
          * @extends BlockView;
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardListView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardListView.js	(révision 14036)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardListView.js	(révision 14037)
@@ -4,14 +4,14 @@
         "underscore",
         "JEditor/Commons/Ancestors/Views/View",
         "text!../Templates/CardList.html",
-        "JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/SelectIconView",
-        "JEditor/PagePanel/Contents/Blocks/CardBlock/Models/SvgCollection",
+        "JEditor/Commons/Svgs/Views/SelectIconView",
+        "JEditor/Commons/Svgs/Models/IconsSvgCollection",
         "JEditor/Commons/Events",
         "JEditor/Commons/Links/Views/LinkView",
         "collection!JEditor/Commons/Pages/Models/PageCollection",
         "i18n!../nls/i18n"
     ],
-    function ($, _, View, template, SelectIconView, SvgCollection,Events, LinkView, PageCollection, i18n) {
+    function ($, _, View, template, SelectIconView, IconsSvgCollection,Events, LinkView, PageCollection, i18n) {
         var CardListView = View.extend({
             tagName:'li',
             className:'form-element',
@@ -180,18 +180,13 @@
                 this.model.icon = event.name;
             },
             getIconContent:function(svgName){
-                var svgCollectionObj = new SvgCollection({"svgName":svgName});
-                svgCollectionObj.fetchIcons(_.bind(function(error, svg) {
-                    if (error) {
-                        console.error(error);
-                    } else {
-                        this.selectedIconEl.html(svg.content);
-                        this.selectedIconEl.show();
-                        this.btnBrowseIconsEl.show();
-                        this.switchIcon.css('background-color', '#34d399');
-                        this.switchIcon.find('span').css('float', 'right'); 
-                    }
-                },this));
+                this.iconsSvgCollection =  IconsSvgCollection.getInstance();
+                this.svgJSON = this.iconsSvgCollection.getSvgByName(svgName);
+                this.selectedIconEl.html(this.svgJSON.content);
+                this.selectedIconEl.show();
+                this.btnBrowseIconsEl.show();
+                this.switchIcon.css('background-color', '#34d399');
+                this.switchIcon.find('span').css('float', 'right'); 
     
             },
             _onClickSwitchIcon: function() {
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/main.js	(révision 14036)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/main.js	(révision 14037)
@@ -1,11 +1,10 @@
 // this file has been auto-generated by a grunt task, it will be overriden, do not modify it
-define(["./CardBlockView", "./CardOptionView", "./CardListView", "./CardStyleOptionView", "./SelectIconView"],function(CardBlockView, CardOptionView, CardListView, CardStyleOptionView, SelectIconView){
+define(["./CardBlockView", "./CardOptionView", "./CardListView", "./CardStyleOptionView"],function(CardBlockView, CardOptionView, CardListView, CardStyleOptionView){
     var comp={
         "CardBlockView":CardBlockView,
         "CardOptionView":CardOptionView,
         "CardListView":CardListView,
         "CardStyleOptionView": CardStyleOptionView,
-        "SelectIconView": SelectIconView
     };
     return comp;
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/PagePanel.js
===================================================================
--- src/js/JEditor/PagePanel/PagePanel.js	(révision 14036)
+++ src/js/JEditor/PagePanel/PagePanel.js	(révision 14037)
@@ -23,7 +23,27 @@
 		"i18n!./nls/i18n",
 		// not in params
 		"owlCarousel",
-		"jqueryPlugins/affix" ], function($, _, pagePanel, Events, PanelView, RightPanelView,  PageCollectionView, PageCollection, PageSupportCollection, PageListManagerView, AvailableView, AddPageView, LayoutCollection, PageView, LanguagesDropDown, ContentModelCollection, VersionsCollectionView, PageLpManagerView, FilterPageView,CTACollection,Common, translate) {
+		"jqueryPlugins/affix" ], function($, _, 
+			pagePanel, 
+			Events, 
+			PanelView, 
+			RightPanelView,  
+			PageCollectionView, 
+			PageCollection, 
+			PageSupportCollection, 
+			PageListManagerView, 
+			AvailableView, 
+			AddPageView, 
+			LayoutCollection, 
+			PageView, 
+			LanguagesDropDown, 
+			ContentModelCollection, 
+			VersionsCollectionView, 
+			PageLpManagerView, 
+			FilterPageView,
+			CTACollection,
+			Common, 
+			translate) {
 	 /**
 		 * not main Vue du Panneau de page, gère les vues de page (nom, etc),
 		 * langues, panneaux latéraux
Index: src/js/JEditor/ParamsPanel/ParamsPanel.js
===================================================================
--- src/js/JEditor/ParamsPanel/ParamsPanel.js	(révision 14036)
+++ src/js/JEditor/ParamsPanel/ParamsPanel.js	(révision 14037)
@@ -17,11 +17,11 @@
     "./Views/CustomShortcode",
     "./Views/ImageCompressionQualityView",
     "./Models/Params",
-    "JEditor/Commons/Basefile/Models/SocialsCollection",
+    "JEditor/Commons/Svgs/Models/SocialsSvgCollection",
     //hidden
     "jqueryui/datepicker"
 ],
-        function ($, _, PanelView, Events, ParamsPanelTemplate, translate, SocialNetworks,SEOView, MobileStoreView, PolitiqueConfidentialiteView,DonneeStructureeView, MarqueClientView, FontsGoogleView, IconsCollectionView,ThemeSiteView,CustomShortcode,ImageCompressionQualityView,Params,SocialsCollection) {
+        function ($, _, PanelView, Events, ParamsPanelTemplate, translate, SocialNetworks,SEOView, MobileStoreView, PolitiqueConfidentialiteView,DonneeStructureeView, MarqueClientView, FontsGoogleView, IconsCollectionView,ThemeSiteView,CustomShortcode,ImageCompressionQualityView,Params,SocialsSvgCollection) {
             var SocialPanel = PanelView.extend(
                     /**
                      * @lends JEditor.Panels.MessagePanel.prototype
@@ -57,7 +57,7 @@
                                     this._super();
                                     this._template = this.buildTemplate(ParamsPanelTemplate, translate);
                                     this.params = Params.getInstance();
-                                    this.svgCollection = SocialsCollection.getInstance();
+                                
                                     this.menuEntries = {
                                         MarqueClient:{
                                             object: new MarqueClientView({model:this.params}),
@@ -102,7 +102,7 @@
                                     };
                                      
                                     if(this.app.user.can('createshortcode')) {
-                                        this.menuEntries. customShortcode = 
+                                        this.menuEntries.customShortcode = 
                                         {
                                             object: new CustomShortcode({model:this.params, languages : this.languages}),
                                             icon:"icon-link",
@@ -159,15 +159,13 @@
                                     var loaded = 0;
                                     function onLoaded() {
                                         loaded++;
-                                        if (loaded === 2) {
+                                        if (loaded === 1) {
                                           this.loadingEnd();
                                         }
                                       }
                         
-                                    this.listenToOnce(this.svgCollection, Events.BackboneEvents.SYNC, onLoaded);
                                     this.listenToOnce(this.params, Events.BackboneEvents.SYNC, onLoaded);
                                     this.params.fetch();
-                                    this.svgCollection.fetch();
 
                                     this.loadCss(this.cssFile);
                                 },
Index: src/js/JEditor/ParamsPanel/Views/ListSocialNetworks.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/ListSocialNetworks.js	(révision 14036)
+++ src/js/JEditor/ParamsPanel/Views/ListSocialNetworks.js	(révision 14037)
@@ -8,9 +8,9 @@
     "JEditor/App/Messages/Confirm",
     "JEditor/App/Messages/ClipboardModule", 
     "../Models/SocialList",
-    "JEditor/Commons/Basefile/Models/SocialsCollection",
+    "JEditor/Commons/Svgs/Models/SocialsSvgCollection",
     'i18n!../nls/i18n'],
-    function ($, _, View, Events, listSocial, Confirm, ClipboardModule, SocialList,SocialsCollection, translate) {
+    function ($, _, View, Events, listSocial, Confirm, ClipboardModule, SocialList,SocialsSvgCollection, translate) {
     var ListSocialNetworks = View.extend({
         events: {
             //"blur .content": "onChangeTitle",
@@ -22,7 +22,7 @@
         initialize: function () {
             this._super();
             this.model = this.options.model;
-            this.svgCollection = SocialsCollection.getInstance();
+            this.socialsSvgCollection = SocialsSvgCollection.getInstance();
            // this._template = this.buildTemplate(template, translate);
             this._listTemplate = this.buildTemplate(listSocial, translate);
             this.selectSvgDialog = this.options.selectSvgDialog;
@@ -166,12 +166,10 @@
                     var svgData = '';
                     if (social.svg) {
                         var svgName = social.svg;
-                        svgData = this.svgCollection.find(function(item) {
-                            return item.attributes.name === svgName;
-                        });
+                        svgData = this.socialsSvgCollection.getSvgByName(svgName);
                     }
                     if (svgData) {
-                        item.icon = svgData.attributes.content;
+                        item.icon = svgData.content;
                     }
                     else {
                         item.icon = "<span class='customrs-img'></span>";
@@ -203,7 +201,7 @@
         },
         onSelectedSvg:function (params,id) {
             var ns=this.model.get("SocialNetworksUrl");
-            ns[id].svg = params.attributes.name;
+            ns[id].svg = params.name;
             this.model.set("SocialNetworksUrl",ns);
             this.model.save();
             this.trigger("updateOneSocialNetwork");
Index: src/js/JEditor/ParamsPanel/Views/SocialNetworks.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/SocialNetworks.js	(révision 14036)
+++ src/js/JEditor/ParamsPanel/Views/SocialNetworks.js	(révision 14037)
@@ -6,11 +6,12 @@
     'JEditor/ParamsPanel/Views/ListSocialNetworks',
     "JEditor/App/Messages/ClipboardModule",
     'text!../Templates/SocialNetwork.html',
-    "JEditor/Commons/Files/Views/SocialSvgSelectorDialog", 
+    // "JEditor/Commons/Files/Views/SocialSvgSelectorDialog", 
+    "JEditor/Commons/Svgs/Views/SelectSocialView", 
     'i18n!../nls/i18n',
     //not in params
     'jqueryui/sortable'],
-    function ($, _, View, Events,ListSocialNetworks, ClipboardModule, template, SocialSvgSelectorDialog, translate) {
+    function ($, _, View, Events,ListSocialNetworks, ClipboardModule, template,SelectSocialView, translate) {
     var SocialNetworks = View.extend({
         events: {
             "click .dropdown-menu":"onClickCascade",
@@ -128,7 +129,7 @@
         },
         renderListeSocialNetwork :function(data){
             
-            this.selectSvgDialog = new SocialSvgSelectorDialog();
+            this.selectSvgDialog = new SelectSocialView();
             this.ChildViews.ListSocialNetworks=new ListSocialNetworks({model:data, selectSvgDialog:this.selectSvgDialog});
             this.listenTo(this.ChildViews.ListSocialNetworks, 'deleteOneSocialNetwork',this.DeleteOne);
             this.listenTo(this.ChildViews.ListSocialNetworks, 'updateOneSocialNetwork',this.render);
Index: src/less/main.less
===================================================================
--- src/less/main.less	(révision 14036)
+++ src/less/main.less	(révision 14037)
@@ -2964,7 +2964,7 @@
 .card-panel-content {
   margin-top: 22px;
 }
-.page-search {
+.page-search, .svg-search {
   width: 59%;
   border: 1px solid #ccc;
   border-radius: 4px;
@@ -2976,6 +2976,9 @@
   background-color: #b2b2b2;
   color: #fff;
 }
+.svg-search{
+  margin-bottom: 10px;
+}
 .page-search::placeholder {
   color: #fff;
 }
