Revision: r13233
Date: 2024-10-17 13:25:42 +0300 (lkm 17 Okt 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
correction erreur dans le console apres upload success

## Files changed

## Full metadata
------------------------------------------------------------------------
r13233 | sraz<PERSON><PERSON><PERSON>oa | 2024-10-17 13:25:42 +0300 (lkm 17 Okt 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileManagerUIView.js

correction erreur dans le console apres upload success
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/FilePanel/Views/FileManagerUIView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileManagerUIView.js	(révision 13232)
+++ src/js/JEditor/FilePanel/Views/FileManagerUIView.js	(révision 13233)
@@ -241,7 +241,8 @@
                 },
                 onUploaded: function(e, data) {
                     this.dom[this.cid].filters.removeClass('active'); // reinitialisation des filtres
-                    for (var i = 0; i < data.filesData.length; i++) {
+                    // on a plus besoin ça car on a resetFilter() et on a deja fait un fetch 
+                    /* for (var i = 0; i < data.filesData.length; i++) {
                         var file = data.filesData[i].response;
                         if (file.includes("not supported. Create resource failed")) {
                             message = file.replace("not supported. Create resource failed", translate("mimeNotsupporte"));
@@ -250,7 +251,7 @@
                                 message: message
                             });
                         }else this.collection.create(file);
-                    }
+                     }*/
                     if (this.options.listView.sortBy)
                         this.options.listView.sortBy(this.sortBy, this.sortOrder === 'asc');
                     this.trigger(Events.ListManagerViewEvents.SORTLIST);
