Revision: r10400
Date: 2023-02-14 14:41:17 +0300 (tlt 14 Feb 2023) 
Author: jn.harison 

## Commit message
Wishlist IDEO3.2:Paramaters:add google fonts

## Files changed

## Full metadata
------------------------------------------------------------------------
r10400 | jn.harison | 2023-02-14 14:41:17 +0300 (tlt 14 Feb 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/dev/#librairies/vendor/CoreMoteurDeRendu/src/CoreMoteurDeRendu/Core/AmbianceEngineRenderer.php
   M /branches/ideo3_v2/dev/#librairies/vendor/CoreMoteurDeRendu/src/CoreMoteurDeRendu/Core/Resources/AmbianceResourceAbstract.php
   M /branches/ideo3_v2/dev/#librairies/vendor/CoreMoteurDeRendu/src/CoreMoteurDeRendu/Core/Resources/LinkResource.php
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Models/Params.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/ParamsPanel.js
   A /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Templates/FontsGoogle.html
   A /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/FontsGoogleView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/i18n.js

Wishlist IDEO3.2:Paramaters:add google fonts
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/ParamsPanel.js
===================================================================
--- src/js/JEditor/ParamsPanel/ParamsPanel.js	(révision 10399)
+++ src/js/JEditor/ParamsPanel/ParamsPanel.js	(révision 10400)
@@ -10,11 +10,12 @@
     "./Views/PolitiqueConfidentialiteView",
     "./Views/DonneeStructureeView",
     "./Views/MarqueClientView",
+    "./Views/FontsGoogleView",
     "./Models/Params",
     //hidden
     "jqueryui/datepicker"
 ],
-        function ($, _, PanelView, Events, ParamsPanelTemplate, translate, SocialNetworks,SEOView, MobileStoreView, PolitiqueConfidentialiteView,DonneeStructureeView, MarqueClientView, Params) {
+        function ($, _, PanelView, Events, ParamsPanelTemplate, translate, SocialNetworks,SEOView, MobileStoreView, PolitiqueConfidentialiteView,DonneeStructureeView, MarqueClientView, FontsGoogleView, Params) {
             var SocialPanel = PanelView.extend(
                     /**
                      * @lends JEditor.Panels.MessagePanel.prototype
@@ -56,6 +57,11 @@
                                             icon:"icon-html",
                                             title:translate("MarqueClient") 
                                         },
+                                        FontsGoogle:{
+                                            object: new FontsGoogleView({model:this.params}),
+                                            icon:"icon-html",
+                                            title:translate('fontsGoogle')
+                                        },
                                         socialNetworks: {
                                             object: new SocialNetworks({model: this.params}),
                                             icon: "icon-social",
Index: src/js/JEditor/ParamsPanel/Templates/FontsGoogle.html
===================================================================
--- src/js/JEditor/ParamsPanel/Templates/FontsGoogle.html	(nonexistent)
+++ src/js/JEditor/ParamsPanel/Templates/FontsGoogle.html	(révision 10400)
@@ -0,0 +1,34 @@
+<div class="main-content__wrapper ">
+    <!--
+        ******************************************
+        créer nouvelle childView marque client,
+        y déplacer ce contenu
+        ******************************************
+        -->
+        <div class="ideo-title">
+            <h1>
+                <%=__("fontsGoogle")%>
+                <span><%=__("set_fonts_google")%></span>
+            </h1>
+        </div>
+
+        <div id="content" class="inline-params thin-border  radius  shadow">
+            <span class="inline-params__name">
+                <svg width="70px" height="70px" viewBox="0 0 70 70" enable-background="new 0 0 70 70" xml:space="preserve">
+                <path fill="#34495E" d="M35,0C15.67,0,0,15.67,0,35s15.67,35,35,35s35-15.67,35-35S54.33,0,35,0z M35,67C17.327,67,3,52.673,3,35S17.327,3,35,3s32,14.327,32,32S52.673,67,35,67z M22.436,35.002l4.769-4.528c0.556-0.527,0.556-1.385,0-1.912c-0.556-0.533-1.464-0.533-2.02,0l-5.782,5.484c-0.556,0.527-0.556,1.385,0,1.912l5.788,5.501c0.561,0.527,1.47,0.527,2.025,0s0.556-1.385,0-1.918L22.436,35.002z M38.728,28.149c-0.7-0.341-1.563-0.082-1.927,0.582l-6.152,11.305c-0.365,0.67-0.087,1.489,0.613,1.829c0.694,0.347,1.563,0.083,1.921-0.582l6.158-11.304C39.699,29.314,39.428,28.49,38.728,28.149z M44.77,28.562c-0.557-0.527-1.465-0.527-2.02,0c-0.563,0.527-0.563,1.385,0,1.912l4.779,4.539l-4.769,4.528c-0.562,0.533-0.562,1.391,0,1.918c0.556,0.527,1.458,0.527,2.021,0l5.775-5.484c0.561-0.527,0.561-1.391,0-1.918L44.77,28.562z"></path>
+                </svg>
+                <%=__('fontsGoogle')%>
+            </span>
+            <label>
+                <span class="custom-input" style="margin-bottom: 20px;">
+                    <textarea id="FontsGoogle" data-autosave="true" name="FontsGoogle" rows="15" class="field-input neutral-input  bold">
+                    </textarea>
+                </span>
+            </label>
+        </div>
+        <!-- 
+        ******************
+        end new childView
+        ******************
+        -->
+    </div>
\ No newline at end of file

Property changes on: src/js/JEditor/ParamsPanel/Templates/FontsGoogle.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/ParamsPanel/Views/FontsGoogleView.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/FontsGoogleView.js	(nonexistent)
+++ src/js/JEditor/ParamsPanel/Views/FontsGoogleView.js	(révision 10400)
@@ -0,0 +1,27 @@
+define(['jquery',
+        "underscore", 
+        'JEditor/Commons/Ancestors/Views/View',
+        'text!../Templates/FontsGoogle.html',
+        'i18n!../nls/i18n'], function (
+            $,
+            _,
+            View, 
+            template,
+            translate) {
+            var FontsGoogle = View.extend({
+               
+                initialize: function () {
+                    this._super();
+                    this._template = this.buildTemplate(template,translate);
+                },
+               
+                render: function () {
+                    this.undelegateEvents();
+                    this.$el.html(this._template(this.model.toJSON()));
+                    this.delegateEvents();
+                    return this;
+                }
+            
+            });
+            return FontsGoogle;
+        });
\ No newline at end of file

Property changes on: src/js/JEditor/ParamsPanel/Views/FontsGoogleView.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/ParamsPanel/Models/Params.js
===================================================================
--- src/js/JEditor/ParamsPanel/Models/Params.js	(révision 10399)
+++ src/js/JEditor/ParamsPanel/Models/Params.js	(révision 10400)
@@ -22,6 +22,8 @@
         youtubeUrl:/^(https?\:\/\/)?(www\.)?youtube\.com\/(.*)/,
         instagramUrl:/^(https?\:\/\/)?(www\.)?instagram\.com\/(.*)/
     };
+    //verification for google fonts
+
     var Params = Model.extend({
         url: __IDEO_API_PATH__ + "/settings/",
         defaults: {
@@ -49,6 +51,7 @@
             JsonLdCustomData: null ,
             SocialNetworksUrl :null,
             MarqueClient: null,
+            FontsGoogle: null,
             Logo: null,
             LogoSmall: null,
             Favicon: null
@@ -98,6 +101,65 @@
                 return {field: "PVYoutubeId", message: translate("Invalid_PVYoutubeId")};
             if(attributes.RGPDLink && (!attributes.RGPDLink.match || !attributes.RGPDLink.match(RGPDLinkRegex)))
                 return {field: "RGPDLink", message: translate("Invalid_RGPDLink")};
+            //google fonts verification
+            if(attributes.FontsGoogle)
+            {
+                let countFontGoogleApi = 0;
+                let countFontGstatic = 0;
+                let countFontGoogleApiSwap = 0;
+                //case if we want the attributes to be switchable
+                //const fontRule1 = RegExp('<link (?:rel=["\']preconnect["\'].*href=["\']https://fonts.googleapis.com["\']|href=["\']https://fonts.googleapis.com["\'].*rel=["\']preconnect["\'])>', 'g');
+                const fontRule1 = RegExp('<link (rel=["\']preconnect["\'] href=["\']https:\/\/fonts.googleapis.com["\']|href=["\']https:\/\/fonts.googleapis.com["\'] rel=["\']preconnect["\'])>', 'g');
+                const fontRule2 = RegExp('<link (rel=["\']preconnect["\'] href=["\']https:\/\/fonts.gstatic.com["\']|href=["\']https:\/\/fonts.gstatic.com["\'] rel=["\']preconnect["\']) crossorigin>', 'g');
+                const fontRule3 = RegExp('<link (href=["\']([a-zA-Z0-9\-_=?\/.:&\@]*)\&display=swap["\'] rel=["\']stylesheet["\']|rel=["\']stylesheet["\'] href=["\']([a-zA-Z0-9\-_=?\/.:&\@]*)\&display=swap["\'])>', 'g');
+                while (fontRule1.exec(attributes.FontsGoogle) !== null) {
+                    countFontGoogleApi++;
+                }
+                if(countFontGoogleApi>1)countFontGoogleApi = 2;
+                while (fontRule2.exec(attributes.FontsGoogle) !== null) {
+                    countFontGstatic++;
+                }
+                if(countFontGstatic>1)countFontGstatic = 2;
+                switch (countFontGoogleApi) {
+                    case 2:
+                        return {field: "FontsGoogle", message: translate("Multiple_FontsGoogleApi")};
+                        break;
+                    
+                    case 0:
+                        return {field: "FontsGoogle", message: translate("Missing_FontsGoogleApi")};
+                        break;
+                    default:
+                        break;
+                }
+                switch (countFontGstatic) {
+                    case 2:
+                        return {field: "FontsGoogle", message: translate("Multiple_FontsGoogleStatic")};
+                        break;
+                    
+                    case 0:
+                        return {field: "FontsGoogle", message: translate("Missing_FontsGoogleStatic")};
+                        break;
+
+                    default:
+                        break;
+                }
+                while (fontRule3.exec(attributes.FontsGoogle) !== null) {
+                    countFontGoogleApiSwap++;
+                }
+                if(countFontGoogleApiSwap >1)countFontGoogleApiSwap = 2;
+                switch (countFontGoogleApiSwap) {
+                    case 2:
+                        return {field: "FontsGoogle", message: translate("Multiple_FontsGoogleApiSwap")};
+                        break;
+                    
+                    case 0:
+                        return {field: "FontsGoogle", message: translate("Missing_FontsGoogleApiSwap")};
+                        break;
+
+                    default:
+                        break;
+                }
+            }
         }
     });
     Events.extend({
Index: src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 10399)
+++ src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 10400)
@@ -63,6 +63,12 @@
         "DefaultMessageUploaderLogo": "Cliquez ici ou glissez-déposez une image",
         "copy":"Copié", 
         "confirmDelete": "Vous êtes sur le point de supprimer<br/>définitivement l'élément :</br><strong><% name %></strong>",
-
-
+        "fontsGoogle": "Google Fonts",
+        "set_fonts_google": "Ajouter l\'import de google fonts au format &lt;link&gt;<br/>Toutes les fonts doivent être regroupées dans un même &lt;link&gt; <br/>Les deux &lt;link rel=\"preconnect\"... \"\"&gt; fournit sur Google Fonts sont obligatoires",
+        "Multiple_FontsGoogleApi": "Plusieurs fonts google détéctés",
+        "Missing_FontsGoogleApi": "Script <strong>&lt;link rel=\"preconnect\" href=\"https\:\/\/fonts.googleapis.com\"\&gt;</strong>  manquant",
+        "Multiple_FontsGoogleStatic": "Multiple scripts <strong>&lt;link rel=\"preconnect\" href=\"https\:\/\/fonts.gstastic.com\" crossorigin\&gt;</strong> détectés",
+        "Missing_FontsGoogleStatic": "Script <strong>&lt;link rel=\"preconnect\" href=\"https\:\/\/fonts.gstatic.com\" crossorigin\&gt;</strong>  manquant",
+        "Multiple_FontsGoogleApiSwap": "Plusieurs scripts <strong>&lt;link href=\"https\:\/\/fonts.googleapis.com\/css2\?...\"\&gt;</strong> avec l\'option display swap détectés",
+        "Missing_FontsGoogleApiSwap": "Script  <strong>&lt;link href=\"https\:\/\/fonts.googleapis.com\/css2\?...\"\&gt;</strong> avec l\'option display swap manquant"
     });
Index: src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 10399)
+++ src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 10400)
@@ -66,5 +66,12 @@
         "DefaultMessageUploaderLogo": "Cliquez ici ou glissez-déposez une image",
         "copy":"Copié", 
         "confirmDelete": "Vous êtes sur le point de supprimer<br/>définitivement l'élément :</br><strong><% name %></strong>",
-
+        "fontsGoogle": "Google Fonts",
+        "set_fonts_google": "Ajouter l\'import de google fonts au format &lt;link&gt;<br/>Toutes les fonts doivent être regroupées dans un même &lt;link&gt; <br/>Les deux &lt;link rel=\"preconnect\"... \"\"&gt; fournit sur Google Fonts sont obligatoires",
+        "Multiple_FontsGoogleApi": "Plusieurs fonts google détéctés",
+        "Missing_FontsGoogleApi": "Script <strong>&lt;link rel=\"preconnect\" href=\"https\:\/\/fonts.googleapis.com\"\&gt;</strong>  manquant",
+        "Multiple_FontsGoogleStatic": "Multiple scripts <strong>&lt;link rel=\"preconnect\" href=\"https\:\/\/fonts.gstastic.com\" crossorigin\&gt;</strong> détectés",
+        "Missing_FontsGoogleStatic": "Script <strong>&lt;link rel=\"preconnect\" href=\"https\:\/\/fonts.gstatic.com\" crossorigin\&gt;</strong>  manquant",
+        "Multiple_FontsGoogleApiSwap": "Plusieurs scripts <strong>&lt;link href=\"https\:\/\/fonts.googleapis.com\/css2\?...\"\&gt;</strong> avec l\'option display swap détectés",
+        "Missing_FontsGoogleApiSwap": "Script  <strong>&lt;link href=\"https\:\/\/fonts.googleapis.com\/css2\?...\"\&gt;</strong> avec l\'option display swap manquant"
     });
Index: src/js/JEditor/ParamsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 10399)
+++ src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 10400)
@@ -68,6 +68,15 @@
         "DefaultMessageUploaderLogo": "Click here or drag and drop an image",
         "copy":"Copied", 
         "confirmDelete": "You are about to definitely <br/>suppress the element :</br><strong><% name %></strong>",
+        "fontsGoogle": "Google Fonts",
+        "set_fonts_google": "Add google fonts import in \<link\> format<br/>All fonts must be grouped in the same \<link\> <br/>The two \<link rel=\"preconnect\"... = \"\" \> rovided on Google Fonts are mandatory",
+        "Multiple_FontsGoogleApi": "Several google fonts detected",
+        "Missing_FontsGoogleApi": "Missing google font",
+        "Multiple_FontsGoogleStatic": "Several google static fonts detected",
+        "Missing_FontsGoogleStatic": "Missing google static font",
+        "Multiple_FontsGoogleApiSwap": "Several Font google API with display swap option detected",
+        "Missing_FontsGoogleApiSwap": "Font google API with missing display swap option"
+
     },
     "fr-fr": true,
     "fr-ca": true
