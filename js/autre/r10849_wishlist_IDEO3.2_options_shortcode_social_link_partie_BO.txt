Revision: r10849
Date: 2023-04-19 16:58:46 +0300 (lrb 19 Apr 2023) 
Author: sraz<PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2 : options shortcode [[social_link]] partie BO 

## Files changed

## Full metadata
------------------------------------------------------------------------
r10849 | srazanandralisoa | 2023-04-19 16:58:46 +0300 (lrb 19 Apr 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Templates/ListSocialNetwork.html
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/ListSocialNetworks.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/SocialNetworks.js
   M /branches/ideo3_v2/integration/src/less/imports/params_panel/module/inline-social.less

wishlist IDEO3.2 : options shortcode [[social_link]] partie BO 
------------------------------------------------------------------------

## Diff
Index: src/less/imports/params_panel/module/inline-social.less
===================================================================
--- src/less/imports/params_panel/module/inline-social.less	(révision 10848)
+++ src/less/imports/params_panel/module/inline-social.less	(révision 10849)
@@ -19,6 +19,11 @@
 			width: 100%;
 		}
 	}
+    .rightShortcode{
+        float: right;
+        margin-right: 20px;
+    }
+
 }
 
 	.inline-params__check {
Index: src/js/JEditor/ParamsPanel/Templates/ListSocialNetwork.html
===================================================================
--- src/js/JEditor/ParamsPanel/Templates/ListSocialNetwork.html	(révision 10848)
+++ src/js/JEditor/ParamsPanel/Templates/ListSocialNetwork.html	(révision 10849)
@@ -311,6 +311,10 @@
                         <input type="text" data-autosave="true" name="<%=social.type%>_<%=i %>" class="field-input neutral-input  bold" value="<%=social.url%>" />
                     </span>
                 </label>
+                <span class="rightShortcode">
+                    <span class="shortcode intfont">[[social_link|network=<%=social.network %>]]</span>
+                    <span class="shortcode intfont">[[social_link|id=<%=social.id %>]]</span>
+                </span>
                 <span class="rigth-delete icon-bin"> </span>
             </li>
        
Index: src/js/JEditor/ParamsPanel/Views/ListSocialNetworks.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/ListSocialNetworks.js	(révision 10848)
+++ src/js/JEditor/ParamsPanel/Views/ListSocialNetworks.js	(révision 10849)
@@ -4,14 +4,16 @@
     'JEditor/Commons/Ancestors/Views/View',
     'JEditor/Commons/Events', 
     'text!../Templates/ListSocialNetwork.html',
-    "JEditor/App/Messages/Confirm", 
+    "JEditor/App/Messages/Confirm",
+    "JEditor/App/Messages/ClipboardModule", 
     'i18n!../nls/i18n'],
-    function ($, _, View, Events, template, Confirm, translate) {
+    function ($, _, View, Events, template, Confirm, ClipboardModule, translate) {
     var ListSocialNetworks = View.extend({
         events: {
             //"blur .content": "onChangeTitle",
             "change .content.rs": "onChangeTitle",
-            "click .rigth-delete " :"DeleteOneSocialNetwork"
+            "click .rigth-delete " :"DeleteOneSocialNetwork",
+            'click .shortcode.intfont' : 'copyToClipboard',
         },
         initialize: function () {
             this.model=this.options.model;
@@ -139,10 +141,12 @@
             this.delegateEvents();
             return this;
         },
+        copyToClipboard : function (e){
+            ClipboardModule.copyToClipboard(e);
+        },
         render: function () {
             this.undelegateEvents();
             
-            
             this.$el.html(this._template({ liste: (this.data != null? this.data :0)}));
             
             this.delegateEvents();
Index: src/js/JEditor/ParamsPanel/Views/SocialNetworks.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/SocialNetworks.js	(révision 10848)
+++ src/js/JEditor/ParamsPanel/Views/SocialNetworks.js	(révision 10849)
@@ -4,11 +4,12 @@
     'JEditor/Commons/Ancestors/Views/View',
     'JEditor/Commons/Events', 
     'JEditor/ParamsPanel/Views/ListSocialNetworks',
+    "JEditor/App/Messages/ClipboardModule",
     'text!../Templates/SocialNetwork.html', 
     'i18n!../nls/i18n',
     //not in params
     'jqueryui/sortable'],
-    function ($, _, View, Events,ListSocialNetworks, template, translate) {
+    function ($, _, View, Events,ListSocialNetworks, ClipboardModule, template, translate) {
     var SocialNetworks = View.extend({
         events: {
             "click .dropdown-menu":"onClickCascade",
@@ -19,6 +20,23 @@
             this._super();
             this._template = this.buildTemplate(template, translate);
             this.ChildViews={};
+            this.networks = {
+                facebookUrl: 'facebook',
+                twitterUrl: 'twitter',
+                mybusinessUrl: 'googlemybusiness',
+                pinterestUrl: 'pinterest',
+                viadeoUrl: 'viadeo',
+                linkedinUrl: 'linkedin',
+                youtubeUrl: 'youtube',
+                instagramUrl: 'instagram',
+                skypeUrl : 'skype',
+                theforkUrl: 'thefork',
+                tiktokUrl: 'tiktok',
+                tripadvisorUrl: 'tripadvisor',
+                wazeUrl: 'waze',
+                whatsappUrl: 'whatsapp',
+                slideshareUrl: 'slideshare',
+            };
             
         },
         
@@ -30,13 +48,24 @@
         },
         onClickCascade : function(e){
             var target=e.target;
-            var Type=target.getAttribute("data-id");
+            var listeSN=this.model.get("SocialNetworksUrl");
+            var Type = target.getAttribute("data-id");
+            // Utilisation de Array.map() pour extraire les valeurs d'id dans un tableau
+            var listeId = listeSN.map(function(objet) {
+                return objet.id;
+            });
+            
+            // Si listeId est 0 on le return sinon on utilise de Math.max() pour trouver la plus grande valeur d'id
+            var maxId = (listeId.length === 0)? listeId :Math.max(...listeId);
+            
             var object={
+                id: maxId+1,
                 title:"",
                 url:"",
-                type:Type
+                type:Type,
+                network : this.networks[Type],           
             };
-            var listeSN=this.model.get("SocialNetworksUrl");
+            
             listeSN.push(object);
             this.model.set("SocialNetworksUrl",listeSN);
             this.model.save();
@@ -80,31 +109,7 @@
             this.$("#liste").append(this.ChildViews.ListSocialNetworks.render().$el);
         },
         copyToClipboard : function (e){
-            var copyText = $(e.currentTarget).text();
-            const clipboard = navigator.clipboard;
-            if (clipboard !== undefined && clipboard !== "undefined") {
-                navigator.clipboard.writeText(copyText).then(this.successfully($(e.currentTarget)));
-            } else {
-                if (document.execCommand) {
-                const el = document.createElement("input");
-                el.value = copyText;
-                document.body.append(el);
-
-                el.select();
-                el.setSelectionRange(0, value.length);
-
-                if (document.execCommand("copy")) {
-                    this.successfully();
-                }
-                el.remove();
-                }
-            }
-        },
-        successfully :function (el){
-            el.before('<div role="alert" aria-live="polite" style="left: 95%; top: auto;position: absolute;" class="toastcopy"><div class="jq-toast-single jq-icon-success" style="text-align: left; /*! display: none; */"><span class="icon icon-check-circle"></span><span>'+translate("copy")+'</span></div></div>')
-            window.setTimeout(function(){
-                el.parent().find('.toastcopy').remove();
-            }, 2000);
+            ClipboardModule.copyToClipboard(e);
         }
     });
     return SocialNetworks;
