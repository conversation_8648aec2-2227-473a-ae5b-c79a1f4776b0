Revision: r11140
Date: 2023-07-06 18:26:05 +0300 (lkm 06 Jol 2023) 
Author: mpartaux 

## Commit message
add FB video icon

## Files changed

## Full metadata
------------------------------------------------------------------------
r11140 | mpartaux | 2023-07-06 18:26:05 +0300 (lkm 06 Jol 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/assets/img/video-icons.png
   M /branches/ideo3_v2/integration/src/less/imports/panel_video_option.less

add FB video icon
------------------------------------------------------------------------

## Diff
Index: assets/img/video-icons.png
===================================================================
Impossible d'afficher : fichier considéré comme binaire.
svn:mime-type = application/octet-stream
Index: src/less/imports/panel_video_option.less
===================================================================
--- src/less/imports/panel_video_option.less	(révision 11139)
+++ src/less/imports/panel_video_option.less	(révision 11140)
@@ -33,7 +33,7 @@
             display:block;
             & .container{
                 background:url('../../assets/img/video-icons.png') no-repeat;
-                background-position:-240px 0;
+                background-position:-300px 0;
             }
         }
         & .youtube .container{
@@ -48,6 +48,9 @@
         & .error .container{
             background-position: -180px 0;
         }
+        & .facebook .container{
+            background-position: -240px 0;
+        }
         & .wrapper > * {
             float:left;
             margin-left:3%;
