Revision: r10333
Date: 2023-02-03 15:10:25 +0300 (zom 03 Feb 2023) 
Author: rrakotoarinelina 

## Commit message
Ajout class sur les items du menu coté front

## Files changed

## Full metadata
------------------------------------------------------------------------
r10333 | rrakotoarinelina | 2023-02-03 15:10:25 +0300 (zom 03 Feb 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Menus/Models/MenuItem.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NavigationPanel/Templates/menuEdit.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NavigationPanel/Templates/menuItemEdit.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NavigationPanel/Views/MenuEditView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NavigationPanel/Views/MenuItemEditView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NavigationPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NavigationPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NavigationPanel/nls/i18n.js

Ajout class sur les items du menu coté front
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Menus/Models/MenuItem.js
===================================================================
--- src/js/JEditor/Commons/Menus/Models/MenuItem.js	(révision 10332)
+++ src/js/JEditor/Commons/Menus/Models/MenuItem.js	(révision 10333)
@@ -294,6 +294,15 @@
         this.trigger(Events.BackboneEvents.CHANGE, this, {});
       }
     },
+    cssClass: {
+      get: function () {
+        return this.attributes ? this.attributes.cssClass : undefined;
+      },
+      set: function (cssClass) {
+        this.attributes.cssClass = cssClass;
+        this.trigger(Events.BackboneEvents.CHANGE, this, {});
+      }
+    },
     childLevelCount: {
       get: function () {
         var recurse = function (menuItem) {
Index: src/js/JEditor/NavigationPanel/Templates/menuEdit.html
===================================================================
--- src/js/JEditor/NavigationPanel/Templates/menuEdit.html	(révision 10332)
+++ src/js/JEditor/NavigationPanel/Templates/menuEdit.html	(révision 10333)
@@ -31,6 +31,7 @@
         <div>
             <input type="text" name="link-name" placeholder="<%=__("myLinkName")%>"/>
             <input type="url" name="url" placeholder="http://google.com"/>
+            <input type="text" name="css-class" placeholder="<%=__("cssClass")%>"/>
         </div>
         <button class="reset" ><span class="icon-delete"></span><%=__("cancel")%></button>
         <button class="submit" ><span class="icon-check"></span><%=__("save")%></button>
Index: src/js/JEditor/NavigationPanel/Templates/menuItemEdit.html
===================================================================
--- src/js/JEditor/NavigationPanel/Templates/menuItemEdit.html	(révision 10332)
+++ src/js/JEditor/NavigationPanel/Templates/menuItemEdit.html	(révision 10333)
@@ -4,19 +4,20 @@
     <input type="text" name="name" value="<%=menuItem.name %>" placeholder="<%=__("placeholderLink")%>">
     <% if(menuItem.type===MenuItem.LINK_TYPE&&menuItem.link.type===1){ %>
       <input type="text" name="href" value="<%=menuItem.link.href %>" placeholder="<%=__("placeholderHref")%>">
-    <% } %>
-    <% if(menuItem.type===MenuItem.LINK_TYPE&&menuItem.link.type===0){ %>
-      <input type="text" name="anchor" value="<%=menuItem.link.anchor %>" placeholder="<%=__("placeholderAnchor")%>">
-    <% } %>
-    <% if(menuItem.type===MenuItem.LINK_TYPE){ %>
-    <div class="target-link">
-        <span class="icon-link white"></span><span class="white"><%=__("openLinkIn")%></span><br>
-        <% var id = _.uniqueId('link_target'); %>
-        <input name="target" value="_self" <%= menuItem.link.target==='_self'?'checked':''%> type="radio" id="<%=id%>"/><label for="<%=id%>" class="white"><span class="icon-radio-inactive radio"></span><span class="icon-radio-active radio"></span><%=__("inSameWindow")%></label><br>
-        <% var id = _.uniqueId('link_target'); %>
-        <input name="target" value="_blank" <%= menuItem.link.target==='_blank'?'checked':''%>  type="radio" id="<%=id%>"/><label for="<%=id%>"><span class="icon-radio-inactive radio"></span><span class="icon-radio-active radio"></span><%=__("inNewWindow")%></label><br>
-    </div>
-    <% } %>
+      <% } %>
+      <% if(menuItem.type===MenuItem.LINK_TYPE&&menuItem.link.type===0){ %>
+        <input type="text" name="anchor" value="<%=menuItem.link.anchor %>" placeholder="<%=__("placeholderAnchor")%>">
+        <% } %>
+        <input type="text" name="css-class" value="<%=menuItem.cssClass %>" placeholder="<%=__("cssClass")%>">
+        <% if(menuItem.type===MenuItem.LINK_TYPE){ %>
+          <div class="target-link">
+            <span class="icon-link white"></span><span class="white"><%=__("openLinkIn")%></span><br>
+            <% var id = _.uniqueId('link_target'); %>
+            <input name="target" value="_self" <%= menuItem.link.target==='_self'?'checked':''%> type="radio" id="<%=id%>"/><label for="<%=id%>" class="white"><span class="icon-radio-inactive radio"></span><span class="icon-radio-active radio"></span><%=__("inSameWindow")%></label><br>
+            <% var id = _.uniqueId('link_target'); %>
+            <input name="target" value="_blank" <%= menuItem.link.target==='_blank'?'checked':''%>  type="radio" id="<%=id%>"/><label for="<%=id%>"><span class="icon-radio-inactive radio"></span><span class="icon-radio-active radio"></span><%=__("inNewWindow")%></label><br>
+          </div>
+          <% } %>
     <footer>
         <input type="reset" class="cancel" value="<%=__("cancel")%>">
         <input type="submit" class="save" value="<%=__("save")%>">
Index: src/js/JEditor/NavigationPanel/Views/MenuEditView.js
===================================================================
--- src/js/JEditor/NavigationPanel/Views/MenuEditView.js	(révision 10332)
+++ src/js/JEditor/NavigationPanel/Views/MenuEditView.js	(révision 10333)
@@ -102,6 +102,7 @@
       this._super();
       this._template = this.buildTemplate(menuEdit, translate);
       this.currentMenuItemView = null;
+
       this.resetMenuItemView()
         .listenTo(this.model, Events.BackboneEvents.SYNC, this.render)
         .listenTo(this.model, Events.MenuEvents.ADD, this.render)
@@ -133,6 +134,9 @@
     _onSubmit: function (event) {
       var url = this.dom[this.cid].urlInput.val();
       var name = this.dom[this.cid].nameInput.val();
+      var cssClass = this.dom[this.cid].cssClassInput.val();
+
+      
       var link = new Link({
         name: name,
         href: url,
@@ -141,7 +145,8 @@
       var menuItem = new MenuItem({
         name: name,
         type: MenuItem.LINK_TYPE,
-        link: link
+        link: link,
+        cssClass: cssClass
       }, {
         collection: this.model.itemCollection,
         parentMenu: this.model
@@ -244,6 +249,7 @@
         this.dom[this.cid].selectCount = this.$('.selected-count');
         this.dom[this.cid].urlInput = this.$('.add-link form input[name="url"]');
         this.dom[this.cid].nameInput = this.$('.add-link form input[name="link-name"]');
+        this.dom[this.cid].cssClassInput = this.$('.add-link form input[name="css-class"]');
         this.dom[this.cid].formMessage = this.$('form div.message');
         this.dom[this.cid].formInputs = this.$('form input[type="text"],form input[type="url"]');
         this.dom[this.cid].divAdd = this.$('div.add-link');
Index: src/js/JEditor/NavigationPanel/Views/MenuItemEditView.js
===================================================================
--- src/js/JEditor/NavigationPanel/Views/MenuItemEditView.js	(révision 10332)
+++ src/js/JEditor/NavigationPanel/Views/MenuItemEditView.js	(révision 10333)
@@ -15,6 +15,7 @@
       'change input[name="name"]': '_onNameChange',
       'change input[name="href"]': '_onHrefChange',
       'change input[name="anchor"]':'_onAnchorChange',
+      'change input[name="css-class"]':'_onCssClassChange',
       'click .save': 'save',
       'click .cancel': 'cancel'
     },
@@ -34,6 +35,11 @@
       this.model.name = name;
       this.model.link.name = name;
     },
+    _onCssClassChange: function (event) {
+      var $target = $(event.currentTarget);
+      var cssClass = $target.val();
+      this.model.cssClass = cssClass;
+    },
     _onTargetChange: function (event) {
       var $target = $(event.currentTarget);
       this.model.link.target = $target.val();
Index: src/js/JEditor/NavigationPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NavigationPanel/nls/fr-ca/i18n.js	(révision 10332)
+++ src/js/JEditor/NavigationPanel/nls/fr-ca/i18n.js	(révision 10333)
@@ -3,6 +3,7 @@
 	"none":"aucun",
 	"addLink":"Ajouter un lien",
 	"myLinkName":"Nom de mon lien",
+	"cssClass":"Classe",
 	"cancel":"Annuler",
 	"save":"Sauvegarder",
 	"addToMenu":"Ajouter au menu",
Index: src/js/JEditor/NavigationPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NavigationPanel/nls/fr-fr/i18n.js	(révision 10332)
+++ src/js/JEditor/NavigationPanel/nls/fr-fr/i18n.js	(révision 10333)
@@ -3,6 +3,7 @@
 	"none":"aucun",
 	"addLink":"Ajouter un lien",
 	"myLinkName":"Nom de mon lien",
+	"cssClass":"Classe",
 	"cancel":"Annuler",
 	"save":"Sauvegarder",
 	"addToMenu":"Ajouter au menu",
Index: src/js/JEditor/NavigationPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NavigationPanel/nls/i18n.js	(révision 10332)
+++ src/js/JEditor/NavigationPanel/nls/i18n.js	(révision 10333)
@@ -4,6 +4,7 @@
 		"none":"None",
 		"addLink":"Add a link",
 		"myLinkName":"My link name",
+		"cssClass":"Class",
 		"cancel":"Cancel",
 		"save":"Save",
 		"addToMenu":"Add to menu",
