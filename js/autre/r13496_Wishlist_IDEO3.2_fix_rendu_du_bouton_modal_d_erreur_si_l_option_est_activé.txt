Revision: r13496
Date: 2024-11-25 11:03:08 +0300 (lts 25 Nov 2024) 
Author: frahajanirina 

## Commit message
Wishlist:IDEO3.2:fix rendu du bouton & modal d'erreur si l'option est activé

## Files changed

## Full metadata
------------------------------------------------------------------------
r13496 | frahajanirina | 2024-11-25 11:03:08 +0300 (lts 25 Nov 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js

Wishlist:IDEO3.2:fix rendu du bouton & modal d'erreur si l'option est activé
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonOptionView.js	(révision 13495)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonOptionView.js	(révision 13496)
@@ -66,6 +66,7 @@
                         },
                         inputChange: function(event) {
                             this.model.textOnMobile = event.target.value;
+                            this.render();
                         }
                     });
 
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js	(révision 13495)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js	(révision 13496)
@@ -44,5 +44,6 @@
     "choose":"Choose",
     "advancedCSS": "Avancé",
     "DifferentTextOnMobile": "Texte différent sur mobile ?",
-    "errorWithoutTextOnMobile": "Vous n’avez pas indiqué le texte pour mobile. Vous devez indiquer le texte à afficher ou désactiver l’option."
+    "errorWithoutTextOnMobile": "Vous n’avez pas indiqué le texte pour mobile. Vous devez indiquer le texte à afficher ou désactiver l’option.",
+    "Invalid_text": "Texte invalide"
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js	(révision 13495)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js	(révision 13496)
@@ -44,6 +44,7 @@
     "choose":"Choisir",
     "advancedCSS": "Avancé",
     "DifferentTextOnMobile": "Texte différent sur mobile ?",
-    "errorWithoutTextOnMobile": "Vous n’avez pas indiqué le texte pour mobile. Vous devez indiquer le texte à afficher ou désactiver l’option."
+    "errorWithoutTextOnMobile": "Vous n’avez pas indiqué le texte pour mobile. Vous devez indiquer le texte à afficher ou désactiver l’option.",
+    "Invalid_text": "Texte invalide"
 
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js	(révision 13495)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js	(révision 13496)
@@ -45,7 +45,8 @@
         "choose":"Choose",
         "advancedCSS": "Advanced",
         "DifferentTextOnMobile": "Different text on mobile ?",
-        "errorWithoutTextOnMobile": "You have not specified the text for mobile. You must indicate the text to display or deactivate the option."
+        "errorWithoutTextOnMobile": "You have not specified the text for mobile. You must indicate the text to display or deactivate the option.",
+        "Invalid_text": "Invalid text"
     },
     "fr-fr": true,
     "fr-ca": true
