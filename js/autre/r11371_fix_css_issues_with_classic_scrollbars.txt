Revision: r11371
Date: 2023-10-06 13:54:15 +0300 (zom 06 Okt 2023) 
Author: mpartaux 

## Commit message
fix css issues with classic scrollbars

## Files changed

## Full metadata
------------------------------------------------------------------------
r11371 | mpartaux | 2023-10-06 13:54:15 +0300 (zom 06 Okt 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SeparatorBlock/Templates/separatorOptions.html
   M /branches/ideo3_v2/integration/src/less/imports/common/layout/layout.less
   M /branches/ideo3_v2/integration/src/less/imports/designPanel.less
   M /branches/ideo3_v2/integration/src/less/imports/editImageDialog.less
   M /branches/ideo3_v2/integration/src/less/imports/navigation.less
   M /branches/ideo3_v2/integration/src/less/imports/pageSelector.less
   M /branches/ideo3_v2/integration/src/less/imports/page_panel/module/block-options/field-input.less
   M /branches/ideo3_v2/integration/src/less/imports/panel.less
   M /branches/ideo3_v2/integration/src/less/imports/separatorpanel.less
   M /branches/ideo3_v2/integration/src/less/main.less

fix css issues with classic scrollbars
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/SeparatorBlock/Templates/separatorOptions.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SeparatorBlock/Templates/separatorOptions.html	(révision 11370)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SeparatorBlock/Templates/separatorOptions.html	(révision 11371)
@@ -16,13 +16,13 @@
                 var _id= _.uniqueId('radioSeparator');     
                 %>
                 <div class="border-style-radio">
-                    <label for="<%=_id %>" ><%= __("bordersDotted")%></label><input id="<%= _id%>" type="radio" class="small" data-icon-class="icon-dotted" value="dotted" name="radioBorderStyle" <%=option.borderStyle==="dotted"?' checked="checked" ':'' %> />
+                    <label for="<%=_id %>" ><%= __("bordersDotted")%></label><input id="<%= _id%>" type="radio" class="" data-icon-class="icon-dotted" value="dotted" name="radioBorderStyle" <%=option.borderStyle==="dotted"?' checked="checked" ':'' %> />
                                                                                                          <% _id= _.uniqueId('radioSeparator');      %>
-                                                                                                         <label for="<%=_id %>" ><%= __("bordersDashed")%></label><input id="<%= _id%>" type="radio" class="small" data-icon-class="icon-dashed" value="dashed" name="radioBorderStyle" <%=option.borderStyle==="dashed"?' checked="checked" ':'' %> />
+                                                                                                         <label for="<%=_id %>" ><%= __("bordersDashed")%></label><input id="<%= _id%>" type="radio" class="" data-icon-class="icon-dashed" value="dashed" name="radioBorderStyle" <%=option.borderStyle==="dashed"?' checked="checked" ':'' %> />
                                                                                                          <% _id= _.uniqueId('radioSeparator');      %>
-                                                                                                         <label for="<%=_id %>" ><%= __("bordersSolid")%></label><input id="<%= _id%>" type="radio" class="small" data-icon-class="icon-solid" value="solid" name="radioBorderStyle" <%=option.borderStyle==="solid"?' checked="checked" ':'' %> />
+                                                                                                         <label for="<%=_id %>" ><%= __("bordersSolid")%></label><input id="<%= _id%>" type="radio" class="" data-icon-class="icon-solid" value="solid" name="radioBorderStyle" <%=option.borderStyle==="solid"?' checked="checked" ':'' %> />
                 </div>
-                <input type="text" class="border-color-input"/>
+                <!-- <input type="text" class="border-color-input"/> -->
             </div>
         </div>
     </article>
Index: src/less/imports/common/layout/layout.less
===================================================================
--- src/less/imports/common/layout/layout.less	(révision 11370)
+++ src/less/imports/common/layout/layout.less	(révision 11371)
@@ -2,7 +2,7 @@
 	width: 100%;
 	min-height: 695px;
 	position: relative;
-	padding-bottom: 150px;
+	padding-bottom: 170px;
 	
 	font-family: @raleway;
 }
Index: src/less/imports/designPanel.less
===================================================================
--- src/less/imports/designPanel.less	(révision 11370)
+++ src/less/imports/designPanel.less	(révision 11371)
@@ -67,7 +67,7 @@
         outline: none;
     }
     .content{
-        min-height:695px;
+        min-height:850px;
         margin:20px 20px 20px 290px;
         .legend{
             margin-left:5px;
@@ -176,7 +176,7 @@
         color:@darkgrey;
 
         .contentAce {
-            height: 512px;
+            height: 662px;
         }
 
     }
Index: src/less/imports/editImageDialog.less
===================================================================
--- src/less/imports/editImageDialog.less	(révision 11370)
+++ src/less/imports/editImageDialog.less	(révision 11371)
@@ -113,7 +113,7 @@
                 margin: 10px 0;
             }
             height:338px;
-            overflow:hidden;
+            // overflow:hidden;
             position:relative;
             right: 0;
             .actions-wrapper{
Index: src/less/imports/navigation.less
===================================================================
--- src/less/imports/navigation.less	(révision 11370)
+++ src/less/imports/navigation.less	(révision 11371)
@@ -78,7 +78,7 @@
         }
     }
     .content{
-        min-height:925px;
+        min-height:888px;
         &.affix{
             position:relative;
             .header.navigation,
@@ -695,7 +695,7 @@
         padding-left: 35px;
         width:330px;
         .infos{
-            margin:10px 0;
+            margin:10px 0 20px;
             .save-button{
                 .border-radius();
                 display: inline-block;
@@ -860,7 +860,9 @@
     {
         width: 100%;
         min-height:100%;
-        padding: 30px 0;
+        position: relative;
+        overflow: hidden;
+        // padding: 30px 0;
 
         .ui-sortable-placeholder {
             min-height: 40px;
Index: src/less/imports/pageSelector.less
===================================================================
--- src/less/imports/pageSelector.less	(révision 11370)
+++ src/less/imports/pageSelector.less	(révision 11371)
@@ -3,6 +3,7 @@
     header{
         height:80px;
         position: relative;
+        padding-left: 27px;
         &>h1{
             position:absolute;
             font-size:24px;
Index: src/less/imports/page_panel/module/block-options/field-input.less
===================================================================
--- src/less/imports/page_panel/module/block-options/field-input.less	(révision 11370)
+++ src/less/imports/page_panel/module/block-options/field-input.less	(révision 11371)
@@ -5,6 +5,7 @@
 		outline: none !important;
 		background-color: #000 !important;
 		color: #999 !important;
+		box-sizing: border-box;
 	}
 
 	.receiver-list {
Index: src/less/imports/panel.less
===================================================================
--- src/less/imports/panel.less	(révision 11370)
+++ src/less/imports/panel.less	(révision 11371)
@@ -63,6 +63,43 @@
 
 
 .panel-container{
+
+        // Scrollbar Firefox
+        * {
+            scrollbar-color: #999999 #1a1a1a;
+            scrollbar-width: thin;
+        }
+    
+        // Scrollbar Webkit Chrome/Edge/Safari
+        ::-webkit-scrollbar {
+            width: 12px;
+            height: 12px;
+        }
+    
+        ::-webkit-scrollbar-track {
+            border-radius: 0;
+            background: #1a1a1a;
+            border: none;
+        }
+    
+        ::-webkit-scrollbar-thumb {
+            border-radius: 16px;
+            background: #999999;
+            background-clip: padding-box;
+            border: 3px solid #1a1a1a;
+            box-shadow: none;
+            min-height: 50px;
+    
+            &:hover {
+                background: #5b5b5b;
+                background-clip: border-box;
+                border: 3px solid #1a1a1a;
+            }
+        }
+    
+    .tabbed-view {
+        overflow:hidden;
+    }
     .tabbed-view,.available-items{
         position:absolute;
         top:20px;
@@ -69,8 +106,6 @@
         left:20px;
         right:5px;
         bottom:20px;
-        overflow:hidden;
-
     }
     background: @headerColor;
     color: @white;
@@ -184,14 +219,52 @@
 .panel-content-intro h2 .icon{height:25px; width:25px; float: left; margin-right:5px;}
 .fx-panel .panel-content-intro h2 .icon{.sprite(24,3);}
 .tabbed-view{
+
+    // Scrollbar Firefox
+    * {
+        scrollbar-color: #999999 #1a1a1a;
+        scrollbar-width: thin;
+    }
+
+    // Scrollbar Webkit Chrome/Edge/Safari
+    ::-webkit-scrollbar {
+        width: 12px;
+        height: 12px;
+    }
+
+    ::-webkit-scrollbar-track {
+        border-radius: 0;
+        background: #1a1a1a;
+        border: none;
+    }
+
+    ::-webkit-scrollbar-thumb {
+        border-radius: 16px;
+        background: #999999;
+        background-clip: padding-box;
+        border: 3px solid #1a1a1a;
+        box-shadow: none;
+        min-height: 50px;
+
+        &:hover {
+            background: #5b5b5b;
+            background-clip: border-box;
+            border: 3px solid #1a1a1a;
+        }
+    }
+
     ///contenu
     .panel-content{
         position:absolute;
         top:78px;
-        bottom:40px;
+        bottom:50px;
         width:100%;
         overflow:auto;
         overflow-x:hidden;
+        box-sizing: border-box;
+        padding-right: 3px;
+        scrollbar-gutter: stable;
+
         .panel-content-legend{
             color:@ui-grey;
             font-size:0.8em;
@@ -277,7 +350,8 @@
         font-weight: normal;
     }
     .panel-content{
-        margin-top:0;
+        width: 305px;
+        margin: auto;
         .panel-content-intro{
             color:#727272;
             font-size:0.9em;
Index: src/less/imports/separatorpanel.less
===================================================================
--- src/less/imports/separatorpanel.less	(révision 11370)
+++ src/less/imports/separatorpanel.less	(révision 11371)
@@ -7,14 +7,14 @@
 
     .separator-options > .border-style-radio
     {
-        width: 200px;
-        float: left;
-
+        width: 100%;
+        display: flex;
+        justify-content: space-between;
     }
 
         .separator-options > .border-style-radio > .effect-radio
         {
-            float:left;
+            // float:left;
             margin: 0px;
         }
 
Index: src/less/main.less
===================================================================
--- src/less/main.less	(révision 11370)
+++ src/less/main.less	(révision 11371)
@@ -792,10 +792,46 @@
   }
 
 }
-.scrollbar-classic{
-  overflow: auto;
+
+// Scrollbar Firefox
+* {
+	scrollbar-color: #999999 #ededed;
+	scrollbar-width: thin;
+}
+
+// Scrollbar Webkit Chrome/Edge/Safari
+::-webkit-scrollbar {
+	width: 12px;
+	height: 12px;
+}
+
+::-webkit-scrollbar-track {
+	border-radius: 0;
+	background: #ededed;
+	border: none;
+}
+
+::-webkit-scrollbar-thumb {
+	border-radius: 16px;
+	background: #999999;
+	background-clip: padding-box;
+	border: 3px solid #ededed;
+	box-shadow: none;
+	min-height: 50px;
+
+	&:hover {
+		background: #5b5b5b;
+		background-clip: border-box;
+		border: 3px solid #ededed;
+	}
+}
+
+.scrollbar-classic {
+  overflow-y: auto;
+  overflow-x: hidden;
   overscroll-behavior-y: contain;
- }
+  scrollbar-gutter: stable;
+}
 .pageList, .add-page-dialog {
   .mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
     background: rgba(0, 0, 0, 0.4);
@@ -2848,3 +2884,9 @@
 .ui-dialog .ui-dialog-content li{
   margin: 6px;
 }
+
+
+//ckeditor fix
+.cke_reset_all textarea {
+  white-space: break-spaces!important;
+}
\ No newline at end of file
