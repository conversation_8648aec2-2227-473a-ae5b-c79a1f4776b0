Revision: r13090
Date: 2024-09-23 10:28:55 +0300 (lts 23 Sep 2024) 
Author: traj<PERSON><PERSON><PERSON><PERSON> 

## Commit message
Wishlist : telecharger fichier en masse

## Files changed

## Full metadata
------------------------------------------------------------------------
r13090 | trajaonar<PERSON>lo | 2024-09-23 10:28:55 +0300 (lts 23 Sep 2024) | 1 ligne
Chemins modifiés :
   M /branches/env/apache-php-node.dockerfile
   M /branches/ideo3_v2/dev/#librairies/module/Gestioncontenu/config/module.config.php
   M /branches/ideo3_v2/dev/#librairies/vendor/CoreAccess/config/acl.config.php
   M /branches/ideo3_v2/dev/#librairies/vendor/CoreRessourceManager/autoload_classmap.php
   M /branches/ideo3_v2/dev/#librairies/vendor/CoreRessourceManager/config/acl.config.php
   M /branches/ideo3_v2/dev/#librairies/vendor/CoreRessourceManager/config/module.config.php
   A /branches/ideo3_v2/dev/#librairies/vendor/CoreRessourceManager/src/CoreRessourceManager/Controller/ResourceZipExportController.php
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/collectionManagerUIView.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileManagerUIView.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileManagerUIView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/i18n.js

Wishlist : telecharger fichier en masse
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/FilePanel/Templates/collectionManagerUIView.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/collectionManagerUIView.html	(révision 13089)
+++ src/js/JEditor/FilePanel/Templates/collectionManagerUIView.html	(révision 13090)
@@ -21,6 +21,12 @@
         <span class="infobulles"><%= __("addFileExist")%></span>
     </a>
     <%}%>
+    <% if(!add){ %>
+    <a href="#" class="file-action disabable" role="link" aria-disabled="true" data-action="downloadFilesCollection">
+        <span class="icon-download"></span>
+        <span class="infobulles"><%= __("downloadSelectedFiles")%></span>
+    </a>
+    <%}%>
     <!-- ACTIONS -->
     <% if(user.can("delete_file")){ %>
         <a href="#" class="file-action disabable" role="link" aria-disabled="true"  data-action="delete">
Index: src/js/JEditor/FilePanel/Templates/fileManagerUIView.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileManagerUIView.html	(révision 13089)
+++ src/js/JEditor/FilePanel/Templates/fileManagerUIView.html	(révision 13090)
@@ -38,7 +38,7 @@
 <div class="separation"></div>
 
 <!-- Actions PAR LOTS -->
-<div class="action-par-lots">
+<div class="action-par-lots" style="width:auto">
 
     <!-- SELECTION -->
     <div class="selection">
@@ -55,6 +55,11 @@
 
     </div>
 
+    <a href="#" class="file-action disabable" role="link" aria-disabled="true" data-action="downloadFiles">
+        <span class="icon-download"></span>
+        <span class="infobulles"><%= __("downloadSelectedFiles")%></span>
+    </a>
+
     <!-- ACTIONS -->
     <a href="#" class="file-action disabable" role="link" aria-disabled="true" data-action="addSelectionToNewCollection">
         <span class="icon-new-collection"></span>
Index: src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 13089)
+++ src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 13090)
@@ -46,6 +46,7 @@
     "fileSortnameAsc": "Fichiers de A à Z",
     "fileSortnameDesc": "Fichiers de Z à A",
     "createNewCollection": "Créer une<br />nouvelle collection",
+    "downloadSelectedFiles": "Télécharger les fichiers sélectionnés",
     "addToExistingCollection": "Ajouter à une<br />collection existante",
     "myFiles": "Mes fichiers",
     "filePanelDesc": "Parcourez vos fichiers et collections.<br />Ajoutez et triez des fichiers.<br />Editez vos images.",
Index: src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 13089)
+++ src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 13090)
@@ -48,6 +48,7 @@
     "fileSortnameAsc": "Fichiers de A à Z",
     "fileSortnameDesc": "Fichiers de Z à A",
     "createNewCollection": "Créer une<br />nouvelle collection",
+    "downloadSelectedFiles": "Télécharger les fichiers sélectionnés",
     "addToExistingCollection": "Ajouter à une<br />collection existante",
     "myFiles": "Mes fichiers",
     "filePanelDesc": "Parcourez vos fichiers et collections.<br />Ajoutez et triez des fichiers.<br />Editez vos images.",
Index: src/js/JEditor/FilePanel/nls/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/i18n.js	(révision 13089)
+++ src/js/JEditor/FilePanel/nls/i18n.js	(révision 13090)
@@ -48,6 +48,7 @@
         "fileSortnameAsc": "Sort by name asc",
         "fileSortnameDesc": "Sort by name desc",
         "createNewCollection": "Create a <br />new collection",
+        "downloadSelectedFiles": "Download selected files",
         "addToExistingCollection": "Add to an <br />existing collection",
         "myFiles": "My Files",
         "filePanelDesc": "Browse your files and collections.<br />Add and sort files<br />Edit your pictures",
Index: src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js	(révision 13089)
+++ src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js	(révision 13090)
@@ -18,6 +18,7 @@
             var CollectionManagerUIView = ListManagerView.extend({
                 events: {
                     'click .file-action[data-action="addFileExist"]': 'addFileExist',
+                    'click [data-action="downloadFilesCollection"]': 'downloadFilesCollection',
                 },
                 parentCollection: false,
                 initialize: function() {
@@ -60,6 +61,45 @@
                     }
                     return false;
                 },
+                downloadFilesCollection: function()
+                {
+                    let formData = new FormData();
+                    
+                    for (var cid in this.selected) 
+                    {
+                        if (!this.selected[cid])
+                            continue;
+                        var model = this.collection.get(cid);
+                        
+                        // les ids 
+                        formData.append('liste[]', model.attributes.id );
+                        formData.append('nom[]', model.attributes.name );
+                        
+                        let liste_fichiers_objet = model.attributes.files.models ;
+                        for(var i = 0 ; i<liste_fichiers_objet.length ; i++  )
+                        {
+                            // les fichiers
+                            formData.append('fichier_'+model.attributes.id+'[]', liste_fichiers_objet[i].attributes.fileUrl );
+                        }
+                    }
+
+                    fetch( __IDEO_API_PATH__+ '/zip-export/collection' , {
+                        method: "POST",
+                        body: formData,
+                    })
+                    .then(function (response)
+                    {
+                        if (!response.ok) 
+                        {
+                            throw new Error('Erreur lors de la creation du fichier .zip ');
+                        }
+                        else
+                        {
+                            window.location = __IDEO_RESSOURCE_IMAGE_PATH__+ 'collection.zip';
+                        }
+                    })
+                    return false;
+                },
                 _doDelete: function() {
                     for (var cid in this.selected) {
                         if (!this.selected[cid])
Index: src/js/JEditor/FilePanel/Views/FileManagerUIView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileManagerUIView.js	(révision 13089)
+++ src/js/JEditor/FilePanel/Views/FileManagerUIView.js	(révision 13090)
@@ -24,6 +24,7 @@
             var FileManagerUIView = ListManagerView.extend({
                 events: {
                     'click [data-action="addSelectionToNewCollection"]': 'addSelectionToNewCollection',
+                    'click [data-action="downloadFiles"]': 'downloadFiles',
                 },
                 sortBy: 'createdAt',
                 sortOrder: 'desc',
@@ -190,6 +191,42 @@
                     }
                     return false;
                 },
+                downloadFiles: function()
+                {
+                        // recuperer les fichiers 
+                        var fichiers = [];
+                        let tempFile;
+                        for (cid in this.selected) {
+                            if (this.selected[cid] === true) {
+                                tempFile = this.collection.get(cid) ;
+                                fichiers.push(tempFile.attributes.fileUrl);
+                            }
+                        }
+                        
+
+                        // envoie ajax pour creer .zip
+                        let formData = new FormData();
+                        for (var i = 0; i < fichiers.length; i++) 
+                        {
+                            formData.append('liste[]', fichiers[i]);
+                        }
+                        fetch( __IDEO_API_PATH__+ '/zip-export/fichier' , {
+                            method: "POST",
+                            body: formData,
+                        })
+                        .then(function (response)
+                        {
+                            if (!response.ok) 
+                            {
+                                throw new Error('Erreur lors de la creation du fichier .zip ');
+                            }
+                            else
+                            {
+                                window.location = __IDEO_RESSOURCE_IMAGE_PATH__+ 'fichiers.zip';
+                            }
+                        })
+                        return false;
+                },
                 addSelectionToExistingCollection: function(collection) {
                     if (this.selectedLength) {
                         var arrFiles = [];
@@ -220,7 +257,6 @@
                     ADD_COLLECTION: 'clickAddCollection',
                     ADD_TO_COLLECTION: 'clickAddToCollection',
                     FILE_CHECKCOLLECTION_ONDELETE: 'deleteFileCheckCollection',
-
                 },
             });
 
