Revision: r10431
Date: 2023-02-17 10:51:02 +0300 (zom 17 Feb 2023) 
Author: mpartaux 

## Commit message
updatee highlight class names

## Files changed

## Full metadata
------------------------------------------------------------------------
r10431 | mpartaux | 2023-02-17 10:51:02 +0300 (zom 17 Feb 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js

updatee highlight class names
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 10430)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 10431)
@@ -44,10 +44,10 @@
                                 __IDEO_CSS_PATH__ + 'ideo3-back.css'
                             ],
                             stylesSet: [
-                                { name: 'Highlight 1', element: 'span', attributes: { 'class': 'txt-hightlight-1' } },
-                                { name: 'Highlight 2', element: 'span', attributes: { 'class': 'txt-hightlight-2' } },
-                                { name: 'Highlight 3', element: 'span', attributes: { 'class': 'txt-hightlight-3' } },
-                                { name: 'Highlight 4', element: 'span', attributes: { 'class': 'txt-hightlight-4' } },
+                                { name: 'Highlight 1', element: 'span', attributes: { 'class': 'txt-highlight-1' } },
+                                { name: 'Highlight 2', element: 'span', attributes: { 'class': 'txt-highlight-2' } },
+                                { name: 'Highlight 3', element: 'span', attributes: { 'class': 'txt-highlight-3' } },
+                                { name: 'Highlight 4', element: 'span', attributes: { 'class': 'txt-highlight-4' } },
                                 { name: 'Underline 1', element: 'span', attributes: { 'class': 'txt-underline-1' } },
                                 { name: 'Color 1', element: 'span', attributes: { 'class': 'txt-color-1' } },
                                 { name: 'Color 2', element: 'span', attributes: { 'class': 'txt-color-2' } },
