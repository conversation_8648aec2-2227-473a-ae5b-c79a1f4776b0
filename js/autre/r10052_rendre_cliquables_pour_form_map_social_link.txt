Revision: r10052
Date: 2022-12-30 09:12:38 +0300 (zom 30 Des 2022) 
Author: srazanandralisoa 

## Commit message
rendre cliquables pour [[form_#]] [[map_#]] [[social_link]]

## Files changed

## Full metadata
------------------------------------------------------------------------
r10052 | srazanandralisoa | 2022-12-30 09:12:38 +0300 (zom 30 Des 2022) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormBuilderView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Views/MapOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Templates/SocialNetwork.html
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/SocialNetworks.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/form_block/contentOptions.less
   M /branches/ideo3_v2/integration/src/less/imports/panel_map_address.less
   M /branches/ideo3_v2/integration/src/less/imports/params_panel/module/inline-shortcode.less

rendre cliquables pour [[form_#]] [[map_#]] [[social_link]]
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormBuilderView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormBuilderView.js	(révision 10051)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormBuilderView.js	(révision 10052)
@@ -12,7 +12,8 @@
             "change input[type=\"radio\"][name=\"col-count\"]":"setColumnCount",
             "click .add-field a[data-type]":"onAddClick",
             "sortstop .form-column":"onSortStop",
-            "sortstart .form-column":"onSortStart"
+            "sortstart .form-column":"onSortStart",
+            "click .shortcode" : "copyToClipboard"
         },
         initialize: function() {
             View.prototype.initialize.call(this);
@@ -125,6 +126,33 @@
             });
             this.$(".add-field").after(this.templateList.el);
             this.templateList.render();
+        },
+        copyToClipboard : function (e){
+            var copyText = $(e.currentTarget).text();
+            const clipboard = navigator.clipboard;
+            if (clipboard !== undefined && clipboard !== "undefined") {
+                navigator.clipboard.writeText(copyText).then(this.successfully($(e.currentTarget)));
+            } else {
+                if (document.execCommand) {
+                const el = document.createElement("input");
+                el.value = copyText;
+                document.body.append(el);
+
+                el.select();
+                el.setSelectionRange(0, value.length);
+
+                if (document.execCommand("copy")) {
+                    this.successfully();
+                }
+                el.remove();
+                }
+            }
+        },
+        successfully :function (el){
+            el.before('<div role="alert" aria-live="polite" style="left: 75%; top: auto;position: absolute;" class="toastcopy"><div class="jq-toast-single jq-icon-success" style="text-align: left; /*! display: none; */"><span class="icon icon-check-circle"></span><span>'+i18n("copy")+'</span></div></div>')
+            window.setTimeout(function(){
+                el.parent().find('.toastcopy').remove();
+            }, 2000);
         }
     });
     return FormView;
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js	(révision 10051)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js	(révision 10052)
@@ -70,6 +70,7 @@
     "hotel" : "Hôtel & Hébergement",
     "secteurpublic" : "Secteur Public",
     "proximite" : "Commerces de proximité",
-    "mailInternaute" : "Envoyer un mail de récéption à l'internaute" 
+    "mailInternaute" : "Envoyer un mail de récéption à l'internaute",
+    "copy":"Copié", 
 
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js	(révision 10051)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js	(révision 10052)
@@ -70,6 +70,7 @@
     "hotel" : "Hôtel & Hébergement",
     "secteurpublic" : "Secteur Public",
     "proximite" : "Commerces de proximité",
-    "mailInternaute" : "Envoyer un mail de récéption à l'internaute" 
+    "mailInternaute" : "Envoyer un mail de récéption à l'internaute",
+    "copy":"Copié",
 
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js	(révision 10051)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js	(révision 10052)
@@ -75,7 +75,8 @@
         "hotel" : "Hotel / Lodging",
         "secteurpublic" : "Public sector",
         "proximite" : "Local shops",
-        "mailInternaute" : "Send notification to user" 
+        "mailInternaute" : "Send notification to user",
+        "copy":"Copied",
     },
     "fr-fr":true,
     "fr-ca": true
Index: src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Views/MapOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Views/MapOptionView.js	(révision 10051)
+++ src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Views/MapOptionView.js	(révision 10052)
@@ -13,7 +13,8 @@
         events: {
             'click .address-actions span.pointer': 'showPointerPanel',
             'click .address-suggestions > li': 'selectSuggestion',
-            'click .dashed-button.address-input': 'addAddress'
+            'click .dashed-button.address-input': 'addAddress',
+            'click .shortcode' : 'copyToClipboard'
         },
         className: 'panel-content address-panel address',
         initialize: function() {
@@ -66,6 +67,33 @@
                 advanced:{ autoScrollOnFocus: false }
             });
             return this;
+        },
+        copyToClipboard : function (e){
+            var copyText = $(e.currentTarget).text();
+            const clipboard = navigator.clipboard;
+            if (clipboard !== undefined && clipboard !== "undefined") {
+                navigator.clipboard.writeText(copyText).then(this.successfully($(e.currentTarget)));
+            } else {
+                if (document.execCommand) {
+                const el = document.createElement("input");
+                el.value = copyText;
+                document.body.append(el);
+
+                el.select();
+                el.setSelectionRange(0, value.length);
+
+                if (document.execCommand("copy")) {
+                    this.successfully();
+                }
+                el.remove();
+                }
+            }
+        },
+        successfully :function (el){
+            el.before('<div role="alert" aria-live="polite" style="left: 75%; top: auto;position: absolute;" class="toastcopy"><div class="jq-toast-single jq-icon-success" style="text-align: left; /*! display: none; */"><span class="icon icon-check-circle"></span><span>'+translate("copy")+'</span></div></div>')
+            window.setTimeout(function(){
+                el.parent().find('.toastcopy').remove();
+            }, 2000);
         }
     });
     return MapOptionView;
Index: src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/nls/fr-ca/i18n.js	(révision 10051)
+++ src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/nls/fr-ca/i18n.js	(révision 10052)
@@ -1 +1 @@
-define({"BLOCK_NAME":"Plan","myAddresses":"Mes adresses","address":"Addresses","mapStyle":"Styles du plan","selectAddress":"Sélectionnez et configurez les adresses qui apparaîtront sur votre plan ci-dessous.","addAddress":"Ajouter une adresse","websiteLabel":"Site Web :","phoneLabel":"Téléphone :","emailLabel":"email :","mapView":"Vue du plan","roadmap":"vue plan","satellite":"vue satellite","hybrid":"vue mixte","mapBackground":"Fond du plan","color":"couleur","bw":"Noir et Blanc","zoom":"Niveau de zoom","mapHeight":"Hauteur du plan","error":"Erreur","mapError":"Impossible de créer le plan, vérifiez votre connexion Internet","mapBlockOption":"Options du bloc de plan"});
\ No newline at end of file
+define({"BLOCK_NAME":"Plan","myAddresses":"Mes adresses","address":"Addresses","mapStyle":"Styles du plan","selectAddress":"Sélectionnez et configurez les adresses qui apparaîtront sur votre plan ci-dessous.","addAddress":"Ajouter une adresse","websiteLabel":"Site Web :","phoneLabel":"Téléphone :","emailLabel":"email :","mapView":"Vue du plan","roadmap":"vue plan","satellite":"vue satellite","hybrid":"vue mixte","mapBackground":"Fond du plan","color":"couleur","bw":"Noir et Blanc","zoom":"Niveau de zoom","mapHeight":"Hauteur du plan","error":"Erreur","mapError":"Impossible de créer le plan, vérifiez votre connexion Internet","mapBlockOption":"Options du bloc de plan","copy":"Copié"});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/nls/fr-fr/i18n.js	(révision 10051)
+++ src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/nls/fr-fr/i18n.js	(révision 10052)
@@ -1 +1 @@
-define({"BLOCK_NAME":"Plan","myAddresses":"Mes adresses","address":"Addresses","mapStyle":"Styles du plan","selectAddress":"Sélectionnez et configurez les adresses qui apparaîtront sur votre plan ci-dessous.","addAddress":"Ajouter une adresse","websiteLabel":"Site Web :","phoneLabel":"Téléphone :","emailLabel":"email :","mapView":"Vue du plan","roadmap":"vue plan","satellite":"vue satellite","hybrid":"vue mixte","mapBackground":"Fond du plan","color":"couleur","bw":"Noir et Blanc","zoom":"Niveau de zoom","mapHeight":"Hauteur du plan","error":"Erreur","mapError":"Impossible de créer le plan, vérifiez votre connexion Internet","mapBlockOption":"Options du bloc de plan"});
\ No newline at end of file
+define({"BLOCK_NAME":"Plan","myAddresses":"Mes adresses","address":"Addresses","mapStyle":"Styles du plan","selectAddress":"Sélectionnez et configurez les adresses qui apparaîtront sur votre plan ci-dessous.","addAddress":"Ajouter une adresse","websiteLabel":"Site Web :","phoneLabel":"Téléphone :","emailLabel":"email :","mapView":"Vue du plan","roadmap":"vue plan","satellite":"vue satellite","hybrid":"vue mixte","mapBackground":"Fond du plan","color":"couleur","bw":"Noir et Blanc","zoom":"Niveau de zoom","mapHeight":"Hauteur du plan","error":"Erreur","mapError":"Impossible de créer le plan, vérifiez votre connexion Internet","mapBlockOption":"Options du bloc de plan","copy":"Copié"});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/nls/i18n.js	(révision 10051)
+++ src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/nls/i18n.js	(révision 10052)
@@ -20,7 +20,8 @@
     "mapHeight": "Map height",
     "error": "Error ",
     "mapError": "Failed to create the map, check your Internet connection",
-    "mapBlockOption": "Map block options"
+    "mapBlockOption": "Map block options",
+    "copy":"Copied",
   },
   "fr-fr": true,
   "fr-ca": true
Index: src/js/JEditor/ParamsPanel/Templates/SocialNetwork.html
===================================================================
--- src/js/JEditor/ParamsPanel/Templates/SocialNetwork.html	(révision 10051)
+++ src/js/JEditor/ParamsPanel/Templates/SocialNetwork.html	(révision 10052)
@@ -155,7 +155,7 @@
             <%=__("copypastecodeSocialNetworks")%>
         </span>
         <div class="shortcode-wrapper thin-border radius shadow arrow_left">
-            <input type="text" class="custom-input  intfont" value="[[social_link]]" disabled/>
+            <span class="custom-input  intfont">[[social_link]]</span>
             <!--<a href="#" class="ideo-btn copy"><%=__("Copy")%></a>-->
         </div>
     </div>
Index: src/js/JEditor/ParamsPanel/Views/SocialNetworks.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/SocialNetworks.js	(révision 10051)
+++ src/js/JEditor/ParamsPanel/Views/SocialNetworks.js	(révision 10052)
@@ -12,6 +12,7 @@
     var SocialNetworks = View.extend({
         events: {
             "click .dropdown-menu":"onClickCascade",
+            'click .shortcode-wrapper .custom-input' : 'copyToClipboard',
         },
         currentList: null,
         initialize: function () {
@@ -76,6 +77,33 @@
             this.ChildViews.ListSocialNetworks=new ListSocialNetworks({model:data});
             this.listenTo(this.ChildViews.ListSocialNetworks, 'deleteOneSocialNetwork',this.DeleteOne);
             this.$("#liste").append(this.ChildViews.ListSocialNetworks.render().$el);
+        },
+        copyToClipboard : function (e){
+            var copyText = $(e.currentTarget).text();
+            const clipboard = navigator.clipboard;
+            if (clipboard !== undefined && clipboard !== "undefined") {
+                navigator.clipboard.writeText(copyText).then(this.successfully($(e.currentTarget)));
+            } else {
+                if (document.execCommand) {
+                const el = document.createElement("input");
+                el.value = copyText;
+                document.body.append(el);
+
+                el.select();
+                el.setSelectionRange(0, value.length);
+
+                if (document.execCommand("copy")) {
+                    this.successfully();
+                }
+                el.remove();
+                }
+            }
+        },
+        successfully :function (el){
+            el.before('<div role="alert" aria-live="polite" style="left: 95%; top: auto;position: absolute;" class="toastcopy"><div class="jq-toast-single jq-icon-success" style="text-align: left; /*! display: none; */"><span class="icon icon-check-circle"></span><span>'+translate("copy")+'</span></div></div>')
+            window.setTimeout(function(){
+                el.parent().find('.toastcopy').remove();
+            }, 2000);
         }
     });
     return SocialNetworks;
Index: src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 10051)
+++ src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 10052)
@@ -60,7 +60,8 @@
         "Add_logo_small": "Ajouter le logo \"small\"",
         "Add_favicon": "Ajouter le favicon",
         "set_marque_client": "Renseignez les informations de Marque du client tel que l'enseigne, le logo ...",
-        "DefaultMessageUploaderLogo": "Cliquez ici ou glissez-déposez une image"
+        "DefaultMessageUploaderLogo": "Cliquez ici ou glissez-déposez une image",
+        "copy":"Copié", 
 
 
     });
Index: src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 10051)
+++ src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 10052)
@@ -63,6 +63,7 @@
         "Add_logo_small": "Ajouter le logo \"small\"",
         "Add_favicon": "Ajouter le favicon",
         "set_marque_client": "Renseignez les informations de Marque du client tel que l'enseigne, le logo ...",
-        "DefaultMessageUploaderLogo": "Cliquez ici ou glissez-déposez une image"
+        "DefaultMessageUploaderLogo": "Cliquez ici ou glissez-déposez une image",
+        "copy":"Copié", 
 
     });
Index: src/js/JEditor/ParamsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 10051)
+++ src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 10052)
@@ -65,7 +65,8 @@
         "Add_logo_small": "Add logo \"small\"",
         "Add_favicon": "Add favicon",
         "set_marque_client": "Fill in the customer's brand information such as the brand name, the logo ...",
-        "DefaultMessageUploaderLogo": "Click here or drag and drop an image"
+        "DefaultMessageUploaderLogo": "Click here or drag and drop an image",
+        "copy":"Copied", 
     },
     "fr-fr": true,
     "fr-ca": true
Index: src/less/imports/form_block/contentOptions.less
===================================================================
--- src/less/imports/form_block/contentOptions.less	(révision 10051)
+++ src/less/imports/form_block/contentOptions.less	(révision 10052)
@@ -13,6 +13,7 @@
         &::selection {
             background-color: #36b1c0;
         }
+        cursor: pointer;
     }
     .form-name{
         margin: 0 0 20px;
Index: src/less/imports/panel_map_address.less
===================================================================
--- src/less/imports/panel_map_address.less	(révision 10051)
+++ src/less/imports/panel_map_address.less	(révision 10052)
@@ -13,6 +13,7 @@
       &::selection {
           background-color: #36b1c0;
       }
+      cursor: pointer;
   }
 }
 #item-config.panel-container{
Index: src/less/imports/params_panel/module/inline-shortcode.less
===================================================================
--- src/less/imports/params_panel/module/inline-shortcode.less	(révision 10051)
+++ src/less/imports/params_panel/module/inline-shortcode.less	(révision 10052)
@@ -19,7 +19,7 @@
 		.custom-input {
 			color: @main;
 			font-weight: 600;
-			cursor: text;
+			cursor: pointer;
 		}
 
 		.ideo-btn {
