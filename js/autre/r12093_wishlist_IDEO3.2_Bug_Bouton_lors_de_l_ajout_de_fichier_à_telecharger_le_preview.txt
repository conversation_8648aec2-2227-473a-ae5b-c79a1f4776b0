Revision: r12093
Date: 2024-03-11 14:47:49 +0300 (lts 11 Mar 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2 : <PERSON><PERSON>, lors de l'ajout de fichier à telecharger, le preview disparait 

## Files changed

## Full metadata
------------------------------------------------------------------------
r12093 | srazana<PERSON>lisoa | 2024-03-11 14:47:49 +0300 (lts 11 Mar 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Links/Models/Link.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Links/Views/LinkView.js

wishlist IDEO3.2 : B<PERSON>, lors de l'ajout de fichier à telecharger, le preview disparait 
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Links/Models/Link.js
===================================================================
--- src/js/JEditor/Commons/Links/Models/Link.js	(révision 12092)
+++ src/js/JEditor/Commons/Links/Models/Link.js	(révision 12093)
@@ -5,6 +5,7 @@
   "collection!JEditor/Commons/Pages/Models/PageCollection",
   "collection!JEditor/Commons/Pages/Models/PageSupportCollection",
   "collection!JEditor/Commons/Files/Models/FileDBCollection",
+  "JEditor/FilePanel/Models/FileCollection",
   "collection!JEditor/Commons/Files/Models/FileGroupCollection",
   "JEditor/Commons/Files/Models/File"
 ], function (
@@ -14,6 +15,7 @@
   PageCollection,
   PageSupportCollection,
   FileDBCollection,
+  FileCollection,
   FileGroupCollection,
   File
 ) {
@@ -54,7 +56,7 @@
         this._super();
         this.pageList = PageCollection.getInstance();
         this.pageSupportList = PageSupportCollection.getInstance();
-        this.fileList = FileDBCollection.getInstance();
+        this.file = null;
         this.fileGroupList = FileGroupCollection.getInstance();
         this.setDefaults();
       },
@@ -102,7 +104,13 @@
        * @returns {File}
        */
       getFile: function () {
-        return this.fileList.get(this.href);
+        if (this.file) return this.file ;
+        
+        this.fileCollection =  new FileCollection();
+        this.fileCollection.setId(this.href);
+        this.fileCollection.fetch({async:false});
+        this.file =  this.fileCollection.get(this.href);
+        return this.file;
       },
       /**
        *
Index: src/js/JEditor/Commons/Links/Views/LinkView.js
===================================================================
--- src/js/JEditor/Commons/Links/Views/LinkView.js	(révision 12092)
+++ src/js/JEditor/Commons/Links/Views/LinkView.js	(révision 12093)
@@ -114,6 +114,7 @@
       if (file) {
         this.currentFile = file;
         this.model.href = file.id;
+        this.model.file = file;
         //this.model.save();
         this.render();
       }
@@ -122,6 +123,7 @@
       if (file) {
         this.currentFile = file;
         this.model.href = this.currentFile.id;
+        this.model.file = file;
         //this.model.save();
         this.fileUploader.currentFile = file;
         this.render();
