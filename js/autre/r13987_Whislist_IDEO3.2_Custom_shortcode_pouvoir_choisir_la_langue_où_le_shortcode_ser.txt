Revision: r13987
Date: 2025-03-25 08:24:46 +0300 (tlt 25 Mar 2025) 
Author: r<PERSON><PERSON>arinelina 

## Commit message
Whislist IDEO3.2 : Custom shortcode : pouvoir choisir la langue où le shortcode sera appliqué - partie JS

## Files changed

## Full metadata
------------------------------------------------------------------------
r13987 | rrakotoarinelina | 2025-03-25 08:24:46 +0300 (tlt 25 Mar 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Templates/CustomShortcode.html
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/CustomShortcode.js

Whislist IDEO3.2 : Custom shortcode : pouvoir choisir la langue où le shortcode sera appliqué - partie JS
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Templates/CustomShortcode.html
===================================================================
--- src/js/JEditor/ParamsPanel/Templates/CustomShortcode.html	(révision 13986)
+++ src/js/JEditor/ParamsPanel/Templates/CustomShortcode.html	(révision 13987)
@@ -60,6 +60,35 @@
                 </span>
                 
             </label>
+
+            <br/>
+            <span class="inline-params__name">
+                <svg width="70px" height="70px" viewBox="0 0 70 70" enable-background="new 0 0 70 70" xml:space="preserve">
+                <path fill="#34495E" d="M35,0C15.67,0,0,15.67,0,35s15.67,35,35,35s35-15.67,35-35S54.33,0,35,0z M35,67C17.327,67,3,52.673,3,35S17.327,3,35,3s32,14.327,32,32S52.673,67,35,67z M22.436,35.002l4.769-4.528c0.556-0.527,0.556-1.385,0-1.912c-0.556-0.533-1.464-0.533-2.02,0l-5.782,5.484c-0.556,0.527-0.556,1.385,0,1.912l5.788,5.501c0.561,0.527,1.47,0.527,2.025,0s0.556-1.385,0-1.918L22.436,35.002z M38.728,28.149c-0.7-0.341-1.563-0.082-1.927,0.582l-6.152,11.305c-0.365,0.67-0.087,1.489,0.613,1.829c0.694,0.347,1.563,0.083,1.921-0.582l6.158-11.304C39.699,29.314,39.428,28.49,38.728,28.149z M44.77,28.562c-0.557-0.527-1.465-0.527-2.02,0c-0.563,0.527-0.563,1.385,0,1.912l4.779,4.539l-4.769,4.528c-0.562,0.533-0.562,1.391,0,1.918c0.556,0.527,1.458,0.527,2.021,0l5.775-5.484c0.561-0.527,0.561-1.391,0-1.918L44.77,28.562z"></path>
+                </svg>
+                <%=__('CustomShortcodeLang') %> 
+            </span>
+            <label> 
+                <span class="custom-input">
+                   <select id="CustomShortcodeLang_<%=shortcode.id%>" name="customShortcodeLang<%=shortcode.id%>" class="neutral-input bold customShortcodeLang">
+                    <option value="all" >
+                        <%=__('all') %> 
+                    </option>
+                    <%
+                        for (var j = 0; j < languages.length; j++) {
+                            var lang = languages[j];
+                        %>
+                            <option value="<%= lang.id %>" <%= (typeof shortcode !== 'undefined' && 'lang' in shortcode && shortcode.lang === lang.id ? 'selected' : '') %>>
+                                <%= lang.name %>
+                            </option>
+                        <%
+                        }
+                    %>
+                
+                   </select>
+                </span>
+                
+            </label>
            
             <label>
                 <span class="custom-input" style="margin-bottom: 20px;">
Index: src/js/JEditor/ParamsPanel/Views/CustomShortcode.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/CustomShortcode.js	(révision 13986)
+++ src/js/JEditor/ParamsPanel/Views/CustomShortcode.js	(révision 13987)
@@ -14,9 +14,10 @@
             translate) {
             var CustomShortcode = View.extend({
                 events: {
-                    "change .neutral-input.bold.customShortcodeName ": "onChangeShortcodeName",
-                    "change .neutral-input.bold.customShortcodePosition": "onChangeShortcodePosition",
-                    "change .neutral-input.bold.customShortcodeContent": "onChangeShortcodeContent",
+                    "change .customShortcodeName": function(e) { this.handleShortcodeChange(e, 'name'); },
+                    "change .customShortcodePosition": function(e) { this.handleShortcodeChange(e, 'position'); },
+                    "change .customShortcodeContent": function(e) { this.handleShortcodeChange(e, 'content'); },
+                    "change .customShortcodeLang": function(e) { this.handleShortcodeChange(e, 'lang'); },
                     "click #addShortcode": "onClickAddShortcode",
                     'click .rightShortcode .shortcode' : 'copyToClipboard',
                     "click .rigth-delete": "deleteOneShortcode"
@@ -25,21 +26,13 @@
                 initialize: function () {
                     this._super();
                     this.data = this.options.model.toJSON();
+                    this.languages = this.options.languages.toJSON();
                     this.data = this.data.CustomShortcode;
-                    if(typeof this.data !== "object"){
-                        if(this.data === "")
-                        {
-                            this.currentList = null;
-                        }
-                        else
-                        {
-                            this.currentList = JSON.parse(this.data);
-                        }
-                    }
-                    else{
+                    if(typeof this.data !== "object") {
+                        this.currentList = this.data === "" ? null : JSON.parse(this.data);
+                    } else {
                         this.currentList = this.data;
                     }
-                  
                     
                     this._template = this.buildTemplate(template,translate);
                 },
@@ -52,6 +45,7 @@
                     id: count,
                     name: "",
                     position: "headStart",
+                    lang: "all",
                     shortcode: "",
                     content:""
                 };
@@ -92,41 +86,27 @@
             copyToClipboard : function (e){
                 ClipboardModule.copyToClipboard(e);
             },
-               //modify name of shortcode
-               onChangeShortcodeName :function(event){
-                var $target = $(event.currentTarget);
-                var $targetName = $target.attr("id");
-                var $targetValue = $('#'+$targetName).val();
-                //replace all accent for the shortcode
-                var $targetShortcode = this._removeAccentsShortcode($targetValue);
-                //replace the space by _ for the shortcode
-                $targetShortcode = $targetShortcode.replace(/\s+/g, '_')
-                var id = $targetName.split('_');
-                this.updateProperty(id[1], "name", $targetValue);
-                this.updateProperty(id[1], "shortcode", $targetShortcode);
-                this.model.set("CustomShortcode", this.currentList);
-                this.model.save();
-                this.render();
-               },
-               onChangeShortcodePosition: function(event){
-                var $target = $(event.currentTarget);
-                var $targetName = $target.attr("id");
-                var $targetValue = $('#'+$targetName).val();
-                var id = $targetName.split('_');
-                this.updateProperty(id[1], "position", $targetValue);
-                this.model.set("CustomShortcode", this.currentList);
-                this.model.save();
-                this.render();
-               },
-               onChangeShortcodeContent: function(event){
-                var $target = $(event.currentTarget);
-                var $targetName = $target.attr("id");
-                var $targetValue = $('#'+$targetName).val();
-                var id = $targetName.split('_');
-                this.updateProperty(id[1], "content", $targetValue);
-                this.model.set("CustomShortcode", this.currentList);
-                this.model.save();
-               },
+               handleShortcodeChange: function(event, propertyName) {
+                    var $target = $(event.currentTarget);
+                    var $targetName = $target.attr("id"); 
+                    var $targetValue = $('#'+$targetName).val();
+                    var id = $targetName.split('_')[1];
+                    
+                    if(propertyName === 'name') {
+                        var $targetShortcode = this._removeAccentsShortcode($targetValue);
+                        $targetShortcode = $targetShortcode.replace(/\s+/g, '_');
+                        this.updateProperty(id, "shortcode", $targetShortcode);
+                    }
+                    
+                    this.updateProperty(id, propertyName, $targetValue);
+                    this.model.set("CustomShortcode", this.currentList);
+                    this.model.save();
+                    
+                    if(propertyName === 'name' || propertyName === 'position') {
+                        this.render();
+                    }
+                },
+
                updateProperty: function( id, propertyName, newValue)
                {
                     id = Number(id);
@@ -156,8 +136,14 @@
                     });
                },
                 render: function () {
+                    console.log("invalid string length");
+                    console.log(this.languages); 
+
+                    console.log(" invalid string length this.currentList"); 
+                    console.log(this.currentList); 
+                   
                     this.undelegateEvents();
-                    this.$el.html(this._template({ listeShortcode: (this.currentList != null? this.currentList :[])}));
+                    this.$el.html(this._template({ listeShortcode: (this.currentList != null? this.currentList :[]), languages: this.languages}));
                     this.delegateEvents();
                     return this;
                 }
