Revision: r11153
Date: 2023-07-11 11:08:14 +0300 (tlt 11 Jol 2023) 
Author: rrakotoarinelina 

## Commit message
Refacto Marque Client

## Files changed

## Full metadata
------------------------------------------------------------------------
r11153 | rrakotoarinelina | 2023-07-11 11:08:14 +0300 (tlt 11 Jol 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/MarqueClientView.js

Refacto Marque Client
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Views/MarqueClientView.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 11152)
+++ src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 11153)
@@ -48,170 +48,149 @@
             this.fileCollection = new FileCollection();
             this.translations = translate.translations;
          },
-
+        
         _onUpload: function(file) {
-            if (this.currentFileLogo) this._deleteLastFile('Logo');
-            this.currentFileLogo = new File(file.attributes);
-            this.model.set('Logo', this.currentFileLogo);
-            this.model.set('LogoSmall', this.currentFileLogoSmall);
-            this.model.set('Favicon', this.currentFileFavicon);
-            this.model.save();
-            this.$('.group-content-logo .uploader .view').addClass('done');
-            this.$('.menu-wrapper-logo .uploader .preview .imagepreview').css({opacity: 1});
-            this.$('.menu-wrapper-logo .uploader .preview .imagepreview .progressbar').css({width: 0});
-            this.$('.group-content-logo .rigth-delete-image').show();
-            if(this.model.attributes.Logo.ext === "svg"){
-                this.$('.menu-wrapper-logo .uploader .preview .imagepreview').css({backgroundSize:'100%'});
-                this.$('.menu-wrapper-logo .uploader .preview .imagepreview').css({backgroundRepeat:'no-repeat'});
-                this.$('.menu-wrapper-logo .uploader .preview .imagepreview').css({backgroundPosition:'center'});
-            }      
+            this.upload(file,'Logo');
         },
 
         _onUpload2: function(file) {
-            if (this.currentFileLogoSmall) this._deleteLastFile('LogoSmall');
-            this.currentFileLogoSmall = new File(file.attributes);
-            this.model.set('Logo', this.currentFileLogo);
-            this.model.set('LogoSmall', this.currentFileLogoSmall);
-            this.model.set('Favicon', this.currentFileFavicon);
-            this.model.save();
-            this.$('.group-content-logoSmall .uploader .view').addClass('done');
-            this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview').css({opacity: 1});
-            this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview .progressbar').css({width: 0});
-            this.$('.group-content-logo-small .rigth-delete-image').show();
-            if(this.model.attributes.LogoSmall.ext === "svg"){
-                this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview').css({backgroundSize:'100%'});
-                this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview').css({backgroundRepeat:'no-repeat'});
-                this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview').css({backgroundPosition:'center'});
-            }   
+            this.upload(file,'LogoSmall');   
         },
 
         _onUpload3: function(file) {
-            if (this.currentFileFavicon) this._deleteLastFile('Favicon');
-            this.currentFileFavicon = new File(file.attributes);
+            this.upload(file,'Favicon');
+        },
+
+        upload: function(file,fileType){
+
+            if (this["currentFile" + fileType]) this._deleteLastFile(fileType);
+            this["currentFile" + fileType] = new File(file.attributes);
             this.model.set('Logo', this.currentFileLogo);
             this.model.set('LogoSmall', this.currentFileLogoSmall);
             this.model.set('Favicon', this.currentFileFavicon);
             this.model.save();
-            this.$('.group-content-favicon .uploader .view').addClass('done');
-            this.$('.menu-wrapper-favicon .uploader .preview .imagepreview').css({opacity: 1});
-            this.$('.menu-wrapper-favicon .uploader .preview .imagepreview .progressbar').css({width: 0});
-            this.$('.group-content-favicon .rigth-delete-image').show();
-            if(this.model.attributes.Favicon.ext === "svg"){
-                this.$('.menu-wrapper-favicon .uploader .preview .imagepreview').css({backgroundSize:'100%'});
-                this.$('.menu-wrapper-favicon .uploader .preview .imagepreview').css({backgroundRepeat:'no-repeat'});
-                this.$('.menu-wrapper-favicon .uploader .preview .imagepreview').css({backgroundPosition:'center'});
+            var classSuffix = fileType.charAt(0).toLowerCase() + fileType.slice(1);
+            var deleteClassSuffix = classSuffix;
+            if (fileType === 'LogoSmall') {
+                deleteClassSuffix = 'logo-small';
+            }
+            this.$('.group-content-'+classSuffix+' .uploader .view').addClass('done');
+            this.$('.menu-wrapper-'+classSuffix+' .uploader .preview .imagepreview').css({opacity: 1});
+            this.$('.menu-wrapper-'+classSuffix+' .uploader .preview .imagepreview .progressbar').css({width: 0});
+            this.$('.group-content-'+deleteClassSuffix+' .rigth-delete-image').show();
+            if(this.model.attributes[fileType].ext === "svg"){
+                this.$('.menu-wrapper-'+classSuffix+' .uploader .preview .imagepreview').css({
+                    backgroundSize: '100%',
+                    backgroundRepeat: 'no-repeat',
+                    backgroundPosition: 'center'
+                });
             }   
+
         },
-        _onlyComputerLogo: function(e) {
-            e.preventDefault();
-            e.stopImmediatePropagation();
-            this.$('.menu-wrapper-logo .uploader .actions-wrapper').removeClass('visible');
-            this.$('.menu-wrapper-logo .uploader .actions-wrapper .actions .computer input:file.upload').trigger('click');
-        },
+
         _deleteLastFile: function (name) {
             var cid = this.model.get(name).id;
             this.fileCollection.setId(cid);
             this.fileCollection.fetch({async:false});
             var model =this.fileCollection.get(cid);
-            if (model!=undefined) {
-                model.destroy(); 
-            }
+            model.destroy(); 
         },
+
+        _onlyComputerLogo: function(e) {
+           this.onlyComputer(e,'logo');
+        },
+   
         
         _onlyComputerLogoSmall: function(e) {
-            e.preventDefault();
-            e.stopImmediatePropagation();
-            this.$('.menu-wrapper-logoSmall .uploader .actions-wrapper').removeClass('visible');
-            this.$('.menu-wrapper-logoSmall .uploader .actions-wrapper .actions .computer input:file.upload').trigger('click'); 
+            this.onlyComputer(e,'logoSmall');
         },
 
         _onlyComputerFavicon: function(e) {
+            this.onlyComputer(e,'favicon');
+        },
+
+        onlyComputer : function(e,fileType){
             e.preventDefault();
             e.stopImmediatePropagation();
-            this.$('.menu-wrapper-favicon .uploader .actions-wrapper').removeClass('visible');
-            this.$('.menu-wrapper-favicon .uploader .actions-wrapper .actions .computer input:file.upload').trigger('click');
+            this.$('.menu-wrapper-'+fileType+' .uploader .actions-wrapper').removeClass('visible');
+            this.$('.menu-wrapper-'+fileType+' .uploader .actions-wrapper .actions .computer input:file.upload').trigger('click');
         },
+
+
+        proccessAttribute : function(attributeType) {
+
+            var mwSuffix = attributeType.charAt(0).toLowerCase() + attributeType.slice(1);
+            var grpSuffix = mwSuffix;
+            if (attributeType === 'LogoSmall') {
+                grpSuffix = 'logo-small';
+            }
+                
+            this.$('.group-content-'+grpSuffix+' .uploader').addClass('done');
+            this.$('.menu-wrapper-'+mwSuffix+' .uploader .preview .imagepreview').css({opacity: 1});
+            this.$('.group-content-'+grpSuffix+' .rigth-delete-image').show();
+            if(this.model.attributes[attributeType].ext === "svg"){
+                
+                this.$('.menu-wrapper-'+mwSuffix+' .uploader .preview .imagepreview').css({
+                    backgroundSize: '100%',
+                    backgroundRepeat: 'no-repeat'
+                  });
+ 
+            }  
+
+        },
+
+        uploadFile : function(currentFile, uploadParams) {
+            return new FileUploaderView({
+              currentFile : currentFile,
+              collection: this.fileCollection,
+              uploadParams,
+            });
+          },
+
+        setCurrentFile : function(file, attr) {
+            if (attr && !Array.isArray(attr)) {
+              file = (attr.attributes) ? attr.attributes : attr;
+              file = new File(file);
+            } else {
+              file = null;
+            }
+            return file;
+        },
         
         render: function () {
             this.$el.html(this._template(this.model.toJSON()));
             var attrs = this.model.attributes;
-            if(attrs.Logo && (!Array.isArray(attrs.Logo)))
-            {
-                this.currentFileLogo = (attrs.Logo.attributes) ? attrs.Logo.attributes : attrs.Logo;
-                this.currentFileLogo = new File(this.currentFileLogo);
-            }
-            else
-            {
-                this.currentFileLogo = null;
-                console.log('or here');
-            }
-            if(attrs.LogoSmall && (!Array.isArray(attrs.LogoSmall)))
-            {
-                this.currentFileLogoSmall = (attrs.LogoSmall.attributes) ? attrs.LogoSmall.attributes : attrs.LogoSmall;
-                this.currentFileLogoSmall = new File(this.currentFileLogoSmall);
-            }
-            else
-            {
-                this.currentFileLogoSmall = null;
-            }
-            if(attrs.Favicon && (!Array.isArray(attrs.Favicon)))
-            {
-                this.currentFileFavicon =(attrs.Favicon.attributes) ? attrs.Favicon.attributes : attrs.Favicon
-                this.currentFileFavicon = new File(this.currentFileFavicon);
-            }
-            else
-            {
-                this.currentFileFavicon = null;
-            }
-              //Logo
-              this.fileUploader = new FileUploaderView({
-                currentFile: this.currentFileLogo,
-                collection: this.fileCollection,
-				uploadParams : {
-					customStockEvent : '_parsestock_image',
-					acceptedTypes : [ 'image' ],
-					acceptedExtensions : ['jpeg','jpg','gif','png', 'svg'],
-                    refusedExtensions:['bmp'],
-                    uploadUrl : __IDEO_UPLOADLOGO_URL__? __IDEO_UPLOADLOGO_URL__:'/admin/resource-logo',
-				},
 
-			});
+            const uploadParams = {
+                customStockEvent: '_parsestock_image',
+                acceptedTypes: ['image'],
+                acceptedExtensions: ['jpeg', 'jpg', 'png', 'svg'],
+                refusedExtensions: ['bmp'],
+                uploadUrl: __IDEO_UPLOADLOGO_URL__ ? __IDEO_UPLOADLOGO_URL__ : '/admin/resource-logo',
+            };
 
+
+            this.currentFileLogo = this.setCurrentFile(this.currentFileLogo, attrs.Logo);
+            this.currentFileLogoSmall = this.setCurrentFile(this.currentFileLogoSmall, attrs.LogoSmall);
+            this.currentFileFavicon = this.setCurrentFile(this.currentFileFavicon, attrs.Favicon);
+
+            //logo
+            this.fileUploader = this.uploadFile(this.currentFileLogo, uploadParams);
+            
             //Logo small
-            this.fileUploader2 = new FileUploaderView({
-                currentFile: this.currentFileLogoSmall,
-                collection: this.fileCollection,
-				uploadParams : {
-					customStockEvent : '_parsestock_image',
-					acceptedTypes : [ 'image' ],
-					acceptedExtensions : ['jpeg','jpg','gif','png', 'svg'],
-                    refusedExtensions:['bmp'],
-                    uploadUrl : __IDEO_UPLOADLOGO_URL__? __IDEO_UPLOADLOGO_URL__:'/admin/resource-logo',
-				}
-			});
+            this.fileUploader2 = this.uploadFile(this.currentFileLogoSmall, uploadParams);
 
-              //Favicon
-              this.fileUploader3 = new FileUploaderView({
-                currentFile: this.currentFileFavicon,
-                collection: this.fileCollection,
-				uploadParams : {
-					customStockEvent : '_parsestock_image',
-					acceptedTypes : [ 'image' ],
-					acceptedExtensions : ['jpeg','jpg','png'],
-                    refusedExtensions:['bmp'],
-                    uploadUrl : __IDEO_UPLOADLOGO_URL__? __IDEO_UPLOADLOGO_URL__:'/admin/resource-logo',
-				}
-			});
+            //Favicon
+            this.fileUploader3 = this.uploadFile(this.currentFileFavicon, {
+                ...uploadParams,
+                acceptedExtensions: ['jpeg', 'jpg', 'png']
+              });
 
+
             this.listenTo(this.fileUploader, Events.FileUploaderEvents.UPLOAD, this._onUpload);
             this.listenTo(this.fileUploader2, Events.FileUploaderEvents.UPLOAD, this._onUpload2);
             this.listenTo(this.fileUploader3, Events.FileUploaderEvents.UPLOAD, this._onUpload3);
-            // en cas echec d'upload on re-render les marqueClients
-            this.listenTo(this.fileUploader, Events.FileUploaderEvents.TOOBIG, this.render);
-            this.listenTo(this.fileUploader2, Events.FileUploaderEvents.TOOBIG, this.render);
-            this.listenTo(this.fileUploader3, Events.FileUploaderEvents.TOOBIG, this.render);
 
-
             this.$('.inline-logo .group-content-logo header').after(this.fileUploader.el);
             this.$('.inline-logo .group-content-logo-small header').after(this.fileUploader2.el);
             this.$('.inline-logo .group-content-favicon header').after(this.fileUploader3.el);
@@ -219,32 +198,15 @@
             this.fileUploader2.render();
             this.fileUploader3.render();
             if(attrs.Logo && (!Array.isArray(attrs.Logo))){
-                this.$('.group-content-logo .uploader').addClass('done');
-                this.$('.menu-wrapper-logo .uploader .preview .imagepreview').css({opacity: 1});
-                this.$('.group-content-logo .rigth-delete-image').show();   
-                if(this.model.attributes.Logo.ext === "svg"){
-                    this.$('.menu-wrapper-logo .uploader .preview .imagepreview').css({backgroundSize:'100%'});
-                    this.$('.menu-wrapper-logo .uploader .preview .imagepreview').css({backgroundRepeat:'no-repeat'});
-                }             
+                this.proccessAttribute('Logo');            
             }
             if(attrs.LogoSmall && (!Array.isArray(attrs.LogoSmall))){
-                this.$('.group-content-logo-small .uploader').addClass('done');
-                this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview').css({opacity: 1});
-                this.$('.group-content-logo-small .rigth-delete-image').show();
-                if(this.model.attributes.LogoSmall.ext === "svg"){
-                    this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview').css({backgroundSize:'100%'});
-                    this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview').css({backgroundRepeat:'no-repeat'});
-                }       
+                this.proccessAttribute('LogoSmall');      
             }
             if(attrs.Favicon && (!Array.isArray(attrs.Favicon))){
-                this.$('.group-content-favicon .uploader').addClass('done');
-                this.$('.menu-wrapper-favicon .uploader .preview .imagepreview').css({opacity: 1});
-                this.$('.group-content-favicon .rigth-delete-image').show();
-                if(this.model.attributes.Favicon.ext === "svg"){
-                    this.$('.menu-wrapper-favicon .uploader .preview .imagepreview').css({backgroundSize:'100%'});
-                    this.$('.menu-wrapper-favicon .uploader .preview .imagepreview').css({backgroundRepeat:'no-repeat'});
-                }       
+                this.proccessAttribute('Favicon');     
             }
+
             this.$('.menu-wrapper-logo .uploader .preview .imagepreview').after('<span style="font-size:9px;display:block;margin-top:35px;color: #666;text-align:center;">'+this.translations.DefaultMessageUploaderLogo+'</span>');
             this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview').after('<span style="font-size:9px;display:block;margin-top:35px;color: #666;text-align:center;">'+this.translations.DefaultMessageUploaderLogo+'</span>');
             this.$('.menu-wrapper-favicon .uploader .preview .imagepreview').after('<span style="font-size:9px;display:block;margin-top:35px;color: #666;text-align:center;">'+this.translations.DefaultMessageUploaderLogo+'</span>');
@@ -260,8 +222,8 @@
             var cid = this.model.get(name).id;
             this.fileCollection.setId(cid);
             this.fileCollection.fetch({async:false});
-            var model = this.fileCollection.get(cid);
-            if(!this.app.user.can("delete_file") && model){
+            var model =this.fileCollection.get(cid);
+            if(!this.app.user.can("delete_file")){
                 return false;
             }
                 if (!this.app.params.dontAskAgainFor['deleteFileItem']) {
@@ -271,7 +233,7 @@
                         type: 'delete',
                         onOk: _.bind(function() {
                             this.model.set(name, null);
-                            if(model!=undefined) model.destroy();
+                            model.destroy();
                             this.model.save();
                             this.render();
                         }, this),
