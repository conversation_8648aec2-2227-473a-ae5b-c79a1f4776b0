Revision: r10118
Date: 2023-01-09 19:40:10 +0300 (lts 09 Jan 2023) 
Author: anthony 

## Commit message
modif ckeditor phase 1 (existant)

## Files changed

## Full metadata
------------------------------------------------------------------------
r10118 | anthony | 2023-01-09 19:40:10 +0300 (lts 09 Jan 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js

modif ckeditor phase 1 (existant)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 10117)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 10118)
@@ -31,13 +31,13 @@
                                 {name: 'remove_formats', groups: ['format'], items: ["RemoveFormat"]},
                                 {name: 'kikou', groups: ['basicstyles','cleanup'], items: ['Bold', 'Italic', 'Underline','Strike']},
                                 {name:'links',items:['ideoLink', 'ideoUnlink']},
-                                {name: 'paragraph', groups: ['align', 'bidi'], items: ["JustifyLeft", "JustifyCenter", "JustifyRight", "JustifyBlock"]},
+                                {name: 'paragraph', groups: ['align', 'bidi'], items: ["JustifyLeft", "JustifyCenter", "JustifyRight"]},
                                 {name: 'list', groups: ['list'], items: ["BulletedList","-","Blockquote"]},
-                                {name: 'formats', groups: ['format'], items: ["Format", "Font","FontSize"]},
-                                {name: 'ideo', groups: ['ideocolors'], items: ["ideoTextColor"]},
+                                {name: 'formats', groups: ['format'], items: ["Format", "FontSize"]},
                                 {name: '_ideostyle', groups: ['ideostyle'], items: ['blockstyles']}
                             ],
-                            extraPlugins: 'ideocolorbutton,ideocolorpicker,ideolink,ideostyler,blockquote,removeformat',
+                            fontSize_sizes: 'tiny/0.75em;small/0.875em;large/1.25em;xlarge/1.5em;xxlarge/2em;xxxlarge/2.5em',
+                            extraPlugins: 'ideolink,ideostyler,blockquote,removeformat',
                             skin: 'ideo',
                             forcePasteAsPlainText: true,
                             pasteFromWordRemoveFontStyles: true,
