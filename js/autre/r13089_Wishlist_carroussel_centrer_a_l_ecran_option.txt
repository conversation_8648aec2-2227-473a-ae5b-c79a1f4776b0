Revision: r13089
Date: 2024-09-23 10:06:52 +0300 (lts 23 Sep 2024) 
Author: t<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
Wishlist : carroussel centrer a l'ecran option

## Files changed

## Full metadata
------------------------------------------------------------------------
r13089 | traj<PERSON><PERSON><PERSON><PERSON> | 2024-09-23 10:06:52 +0300 (lts 23 Sep 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/dev/#librairies/module/GestionContenuUtil/src/GestionContenuUtil/Core/BlockModuleHandlers/BlockCarrouselHandler.php
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js

Wishlist : carroussel centrer a l'ecran option
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselOption.html	(révision 13088)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselOption.html	(révision 13089)
@@ -50,8 +50,9 @@
         <div class="defilement">
           <p class="panel-legend"><%=__("autoPlay")%></p>
           <div class="option-content">
+            
             <% var _id=_.uniqueId('autoplay') %>
-            <input type="checkbox" class="blue-bg" name="Autoplay" id="<%=_id %>" <%=Autoplay?'checked="checked"':''%>>
+            <input type="checkbox" class="blue-bg Autoplay" name="Autoplay" id="<%=_id %>" <%=Autoplay?'checked="checked"':''%>>
             <label for="<%=_id %>">
               <span class="checkbox-wrapper">
                 <span class="icon-unchecked"></span>
@@ -59,8 +60,21 @@
               </span>
               <span class="text"><%=__("activeAutoPlay")%></span>
             </label>
+
+            <div id="CentreEcran-Block">
+              <% var _id=_.uniqueId('CentreEcran') %>
+              <input type="checkbox" class="blue-bg CentreEcran" name="CentreEcran" id="<%=_id %>" <%=CentreEcran?'checked="checked"':''%>>
+              <label for="<%=_id %>">
+                <span class="checkbox-wrapper">
+                  <span class="icon-unchecked"></span>
+                  <span class="icon-checked"></span>
+                </span>
+                <span class="text"><%=__("centreEcran")%></span>
+              </label>
+            </div>
+            
+          </div>
         </div>
-        </div>
         <div class="defilement-cran">
           <p class="panel-legend"><%=__("duration")%></p>
           <div class="option-content">
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js	(révision 13088)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js	(révision 13089)
@@ -50,6 +50,7 @@
     "ShowArrow2"            :   "Navigation avancée avec indicateurs *actif*",
     "autoPlay"              :  "Défilement automatique",
     "activeAutoPlay"        :  "Activer le défilement automatique",
+    "centreEcran"           :  "Centrer à l'écran l'élément “actif”",
     "duration"              :  "Glissez pour ajuster le delais",
     "emptyCollection"       :   "Votre collection d’images est vide.",
     "DescStyle1"           :  "Texte sous l'image", 
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js	(révision 13088)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js	(révision 13089)
@@ -54,6 +54,7 @@
        "ShowArrow2"            :   "Advanced navigation with *active* indicators",
        "autoPlay"              :  "Automatic scrolling",
        "activeAutoPlay"        :  "Activate automatic scrolling",
+       "centreEcran"           :  "Center on the screen the “active” element ",
        "duration"              :  "Slide to adjust delay",
        "emptyCollection"      :  "Your image collection is empty",
        "DescStyle1"           :  "Text under the image", 
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js	(révision 13088)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js	(révision 13089)
@@ -24,7 +24,8 @@
             events: {
                 'click .files-action'   :   'openFileGroupDialog',
                 'click input[type=radio]':'_onChangeRadio',
-                'change input[type="checkbox"].blue-bg': '_onChangeAutoplay',
+                'change .Autoplay': '_onChangeAutoplay',
+                'change .CentreEcran':'_onChangeCentreEcran',
                 'slidechange .slider': 'onSliderChange',
                 },
             className: 'carrousel-option-home panel-content',
@@ -95,7 +96,6 @@
                     range:"min"
                 });
                 this.dom[this.cid].slider = slider;
-               
                 this.scrollables({
                     advanced:{ autoScrollOnFocus: false }
                 });
@@ -106,7 +106,10 @@
                 this.model.Autoplay = !this.model.Autoplay;
                 this._showHide();       
             },
-             onSliderChange: function(e,ui) {
+            _onChangeCentreEcran: function(event){
+                this.model.CentreEcran = !this.model.CentreEcran;
+            },
+            onSliderChange: function(e,ui) {
                 var value = ui.value;
                 this.model.Duration=value;
                 return false;
@@ -114,10 +117,14 @@
             _showHide :function(){
                 this.dom[this.cid].autoplay.hide();
                 this.dom[this.cid].cran.hide();
-                if (this.model.Arrow == 2 ) {
+                $("#CentreEcran-Block").hide();
+                if (this.model.Arrow == 2 ) 
+                {
                     this.dom[this.cid].autoplay.show();
-                    if (this.model.Autoplay) {
+                    if (this.model.Autoplay) 
+                    {
                         this.dom[this.cid].cran.show();
+                        $("#CentreEcran-Block").show();
                     }
                 }
             },
