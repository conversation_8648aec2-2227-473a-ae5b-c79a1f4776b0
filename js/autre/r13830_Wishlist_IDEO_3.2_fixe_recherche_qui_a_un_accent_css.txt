Revision: r13830
Date: 2025-02-13 10:13:26 +0300 (lkm 13 Feb 2025) 
Author: frahajanirina 

## Commit message
Wishlist IDEO 3.2: fixe recherche qui a un accent + css

## Files changed

## Full metadata
------------------------------------------------------------------------
r13830 | frahajanirina | 2025-02-13 10:13:26 +0300 (lkm 13 Feb 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileList.html
   M /branches/ideo3_v2/integration/src/less/imports/filePanel.less

Wishlist IDEO 3.2: fixe recherche qui a un accent + css
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/FilePanel/Templates/fileList.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileList.html	(révision 13829)
+++ src/js/JEditor/FilePanel/Templates/fileList.html	(révision 13830)
@@ -42,7 +42,7 @@
             var file = content[i];
             %>
             <%if(!file.isLogo){%>
-            <div class="menu-wrapper file image <%=(selected[file.cid]===true)?'selected':''%> <%=(file.previewClass())%> img-display" data-cid="<%=file.cid %>"  <%if(file.isImg()){%>style="background-image: url(<%= file.thumb %>);<%if(file.isSvg()){%>background-size:100%;background-repeat:no-repeat;<%}%>"<%}%>>
+            <div class="menu-wrapper file image <%=(selected[file.cid]===true)?'selected':''%> <%=(file.previewClass())%> img-display" data-cid="<%=file.cid %>"  <%if(file.isImg()){%>style="background-image: url(<%= file.thumb %>);"<%}%>>
                 <span class="select"><span class="icon-check"></span></span>
                 <%if(!file.isImg()){%>
                 <span class="icon-file ext">
@@ -63,7 +63,7 @@
                 </ul>
                 <div class="fileName-container">
                     <span class="<%= file.isImg() ? 'img-name' : 'other-name' %>">
-                        <%= file.name %>
+                        <%= file.originalName %>
                     </span>               
                 </div>
             </div>
Index: src/less/imports/filePanel.less
===================================================================
--- src/less/imports/filePanel.less	(révision 13829)
+++ src/less/imports/filePanel.less	(révision 13830)
@@ -917,6 +917,11 @@
     color: #FFF;
     font-size: 12px;
     text-align: center;
+    background-size: contain;
+    background-repeat: no-repeat;
+    margin-bottom: 50px;
+    border-radius: 7px;
+    background-color: #f1f1f1;
 
     & .icon-file.ext {
         margin-top:20px;
@@ -970,11 +975,6 @@
 }
 #files .my-files {
     .img-display {
-        background-size: contain;
-        background-repeat: no-repeat;
-        margin-bottom: 50px;
-        border-radius: 7px;
-        background-color: #f1f1f1;
         .fileName-container {
             .img-name, .other-name {
                 display: block;
@@ -1031,6 +1031,7 @@
     position: absolute;
     top: 0; left: 0;
     background-color: rgba(0, 0, 0, 0.5);
+    border-radius: 7px;
 }
 
 .my-files .file ul {
@@ -1041,6 +1042,8 @@
     top: -12px; right: 0;
     list-style-type: none;
     min-height: 120px;
+    border-radius: 0 7px 7px 0;
+    overflow: hidden;
 
     & li {
         cursor: pointer;
@@ -1283,11 +1286,13 @@
 
     & .behind {
         display: block;
-        width: 119px; height: 110px;
         background-color: #999999;
 
         position: absolute;
         top: 0; left: 0;
+        width: 140px;
+        height: 140px;
+        border-radius: 13px;
     }
 
     & .sliders {
@@ -1300,6 +1305,7 @@
 
         position: absolute;
         bottom: 0; right: 0;
+        border-radius: 10px;
 
         &.xls, &.pdf, &.doc, &.ppt, &.unknown{
             background-image: -moz-linear-gradient( 90deg, rgba(0,0,0,0.14902) 0%, rgba(255,255,255,0.14902) 100%);
@@ -1355,46 +1361,8 @@
             }
         }
 
-        &:before {
-            content: "";
-            width: 0px; height: 0px;
+              
 
-            border-left: 20px solid transparent;
-            border-right: 20px solid transparent;
-            border-bottom: 20px solid #cccccc;
-
-            transform:rotate(45deg);
-            -ms-transform:rotate(45deg);
-            -webkit-transform:rotate(45deg);
-
-            position: absolute;
-            bottom: 10px; left: 0px;
-            z-index: 2;
-
-            -webkit-transition:	all 0.15s ease-in-out;
-            -moz-transition:	all 0.15s ease-in-out;
-            -o-transition:          all 0.15s ease-in-out;
-            -ms-transition:         all 0.15s ease-in-out;
-            transition:		all 0.15s ease-in-out;
-        }
-
-        &:after {
-            content: "";
-            width: 0px; height: 0px;
-
-            border-left: 20px solid transparent;
-            border-right: 20px solid transparent;
-            border-bottom: 20px solid #FFFFFF;
-
-            transform:rotate(-135deg);
-            -ms-transform:rotate(-135deg);
-            -webkit-transform:rotate(-135deg);
-
-            position: absolute;
-            bottom: -4px; left: -14px;
-            z-index: 2;
-        }      
-
     }/* end collection */
 
     & .title {
@@ -1424,6 +1392,7 @@
     -o-transition:          all 0.15s ease-in-out;
     -ms-transition:         all 0.15s ease-in-out;
     transition:		all 0.15s ease-in-out;
+    border-radius: 7px;
 
     & .icon-bin {
         float: right; margin: 15px;
