Revision: r10337
Date: 2023-02-06 11:44:45 +0300 (lts 06 Feb 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
suite r10336 render apres supression images logo, image undefined 

## Files changed

## Full metadata
------------------------------------------------------------------------
r10337 | srazanandralisoa | 2023-02-06 11:44:45 +0300 (lts 06 Feb 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/MarqueClientView.js

suite r10336 render apres supression images logo, image undefined 
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Views/MarqueClientView.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 10336)
+++ src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 10337)
@@ -131,7 +131,7 @@
             var attrs = this.model.attributes;
             if(attrs.Logo && (!Array.isArray(attrs.Logo)))
             {
-                this.currentFileLogo = attrs.Logo;
+                this.currentFileLogo = (attrs.Logo.attributes) ? attrs.Logo.attributes : attrs.Logo;
                 this.currentFileLogo = new File(this.currentFileLogo);
             }
             else
@@ -141,7 +141,7 @@
             }
             if(attrs.LogoSmall && (!Array.isArray(attrs.LogoSmall)))
             {
-                this.currentFileLogoSmall = attrs.LogoSmall;
+                this.currentFileLogoSmall = (attrs.LogoSmall.attributes) ? attrs.LogoSmall.attributes : attrs.LogoSmall;
                 this.currentFileLogoSmall = new File(this.currentFileLogoSmall);
             }
             else
@@ -150,7 +150,7 @@
             }
             if(attrs.Favicon && (!Array.isArray(attrs.Favicon)))
             {
-                this.currentFileFavicon = attrs.Favicon;
+                this.currentFileFavicon =(attrs.Favicon.attributes) ? attrs.Favicon.attributes : attrs.Favicon
                 this.currentFileFavicon = new File(this.currentFileFavicon);
             }
             else
