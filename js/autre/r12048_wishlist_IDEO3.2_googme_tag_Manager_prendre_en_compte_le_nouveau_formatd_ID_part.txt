Revision: r12048
Date: 2024-03-04 14:23:01 +0300 (lts 04 Mar 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: googme tag Manager prendre en compte le nouveau formatd'ID (partie JS)

## Files changed

## Full metadata
------------------------------------------------------------------------
r12048 | srazanandralisoa | 2024-03-04 14:23:01 +0300 (lts 04 Mar 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Models/Params.js

wishlist IDEO3.2: googme tag Manager prendre en compte le nouveau formatd'ID (partie JS)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Models/Params.js
===================================================================
--- src/js/JEditor/ParamsPanel/Models/Params.js	(révision 12047)
+++ src/js/JEditor/ParamsPanel/Models/Params.js	(révision 12048)
@@ -4,7 +4,7 @@
     "i18n!../nls/i18n"
 ], function (Model, Events, translate) {
     var GARegex=/^(G-\w{10}$)|((UA|YT|MO)-[0-9]{1,}-[0-9]{1,}$)/;
-    var GTMRegex=/^(GTM)-[a-zA-Z0-9]{1,}$/;
+    var GTMRegex=/^(G|GTM)-[a-zA-Z0-9]{1,}$/;
     var FBPixelRegex=/^[0-9]*$/;
     var FBDomainRegex=/^[^\s]{1,}$/;
     // var WTRegex=/^[^\s]{1,}$/;
