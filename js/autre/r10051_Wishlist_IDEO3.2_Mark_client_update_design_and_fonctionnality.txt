Revision: r10051
Date: 2022-12-23 16:52:33 +0300 (zom 23 Des 2022) 
Author: jn.harison 

## Commit message
Wishlist IDEO3.2 Mark client:update design and fonctionnality

## Files changed

## Full metadata
------------------------------------------------------------------------
r10051 | jn.harison | 2022-12-23 16:52:33 +0300 (zom 23 Des 2022) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/ParamsPanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Templates/MarqueClient.html
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/MarqueClientView.js
   M /branches/ideo3_v2/integration/src/less/imports/params_panel/module/inline-social.less

Wishlist IDEO3.2 Mark client:update design and fonctionnality
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/ParamsPanel.js
===================================================================
--- src/js/JEditor/ParamsPanel/ParamsPanel.js	(révision 10050)
+++ src/js/JEditor/ParamsPanel/ParamsPanel.js	(révision 10051)
@@ -51,6 +51,11 @@
                                     this._template = this.buildTemplate(ParamsPanelTemplate, translate);
                                     this.params = new Params();
                                     this.menuEntries = {
+                                        MarqueClient:{
+                                            object: new MarqueClientView({model:this.params}),
+                                            icon:"icon-html",
+                                            title:translate("MarqueClient") 
+                                        },
                                         socialNetworks: {
                                             object: new SocialNetworks({model: this.params}),
                                             icon: "icon-social",
@@ -70,13 +75,7 @@
                                             object: new PolitiqueConfidentialiteView({model:this.params}),
                                             icon:"icon-link",
                                             title:translate("PolitiqueConfidentialite")
-                                        },
-                                        MarqueClient:{
-                                            object: new MarqueClientView({model:this.params}),
-                                            icon:"icon-html",
-                                            title:translate("MarqueClient") 
                                         }
-                                       
                                     };
                                     if(this.app.user.can('view_jsonLd')) {
                                         this.menuEntries.donneeStructuree =   
Index: src/js/JEditor/ParamsPanel/Templates/MarqueClient.html
===================================================================
--- src/js/JEditor/ParamsPanel/Templates/MarqueClient.html	(révision 10050)
+++ src/js/JEditor/ParamsPanel/Templates/MarqueClient.html	(révision 10051)
@@ -1,4 +1,36 @@
-  
+<style type="text/css">
+    .inline-params__nameLogoFooter .shortcode {
+        font-family: 'Open Sans',sans-serif;
+        font-weight: 400;
+        font-size: 12px;
+        border: 1px solid #CCC;
+        color: #999;
+        -webkit-border-radius: 4px;
+        -moz-border-radius: 4px;
+        border-radius: 4px;
+        line-height: 36px;
+        padding: 15px;
+        margin-top: 1px;
+        margin-bottom: 15px;
+        cursor: pointer;
+    }
+
+    .shortcode {
+        font-family: 'Open Sans',sans-serif;
+        font-weight: 400;
+        font-size: 12px;
+        border: 1px solid #CCC;
+        color: #999;
+        -webkit-border-radius: 4px;
+        -moz-border-radius: 4px;
+        border-radius: 4px;
+        line-height: 36px;
+        padding: 15px;
+        margin-top: 1px;
+        margin-bottom: 15px;
+        cursor: pointer;
+    }
+</style>  
     <div class="main-content__wrapper ">
         <!--
             ******************************************
@@ -14,7 +46,7 @@
             </div>
         
             <div class="inline-params thin-border  radius  shadow">
-                <span class="inline-params__name">
+                <span class="inline-params__name mark">
                    Enseigne:
                 </span>
                 <label>
@@ -22,9 +54,10 @@
                         <input type="text" data-autosave="true" name="MarqueClient" class="field-input neutral-input  bold" value="<%=MarqueClient%>"/>
                     </span>
                 </label>
-                <label>
+                <label class="shortcode">
                     [[contact_enseigne]]
                 </label>
+               
             </div>
     
             <div class="my-files fileList">
@@ -39,7 +72,7 @@
                                     <header></header>
                                 </div>
                                 
-                                <span class="inline-params__nameLogoFooter">
+                                <span class="inline-params__nameLogoFooter shortcode">
                                     [[contact_logo]]
                                 </span>
                             </div>      
@@ -50,7 +83,7 @@
                                 <div class="menu-wrapper-logoSmall file image">
                                     <header></header>
                                 </div>
-                                <span class="inline-params__nameLogoFooter">
+                                <span class="inline-params__nameLogoFooter shortcode">
                                     [[contact_logo|format=small]]
                                 </span>
                             </div>
@@ -66,11 +99,6 @@
                         </div>
                        
                     </div>
-                    <div class="inline-params thin-border radius shadow">
-                       
-                       
-                    </div>
-                    
                 </div>
             </div>
             <!-- 
Index: src/js/JEditor/ParamsPanel/Views/MarqueClientView.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 10050)
+++ src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 10051)
@@ -29,6 +29,7 @@
             'click .menu-wrapper-logo ' : '_onlyComputerLogo',
             'click .menu-wrapper-logoSmall ' : '_onlyComputerLogoSmall',
             'click .menu-wrapper-favicon ' : '_onlyComputerFavicon',
+            'click .shortcode': 'copyToClipboard'
         },
 
         initialize: function () {
@@ -42,7 +43,7 @@
             this.fileCollection = FileDBCollection.getInstance();
             this.translations = translate.translations;
          },
-         
+
         _onUpload: function(file) {
             file.attributes.isLogo = "logo";
             this.currentFileLogo = new File(file.attributes);
@@ -50,7 +51,9 @@
             this.model.set('LogoSmall', this.currentFileLogoSmall);
             this.model.set('Favicon', this.currentFileFavicon);
             this.model.save();
-            this.$('.group-content .uploader .view').addClass('done');
+            this.$('.group-content-logo .uploader .view').addClass('done');
+            this.$('.menu-wrapper-logo .uploader .preview .imagepreview').css({opacity: 1});
+            this.$('.menu-wrapper-logo .uploader .preview .imagepreview .progressbar').css({width: 0});
         },
 
         _onUpload2: function(file) {
@@ -60,7 +63,9 @@
             this.model.set('LogoSmall', this.currentFileLogoSmall);
             this.model.set('Favicon', this.currentFileFavicon);
             this.model.save();
-            this.$('.group-content-logo-small .uploader .view').addClass('done');
+            this.$('.group-content-logoSmall .uploader .view').addClass('done');
+            this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview').css({opacity: 1});
+            this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview .progressbar').css({width: 0});
         },
 
         _onUpload3: function(file) {
@@ -71,6 +76,8 @@
             this.model.set('Favicon', this.currentFileFavicon);
             this.model.save();
             this.$('.group-content-favicon .uploader .view').addClass('done');
+            this.$('.menu-wrapper-favicon .uploader .preview .imagepreview').css({opacity: 1});
+            this.$('.menu-wrapper-favicon .uploader .preview .imagepreview .progressbar').css({width: 0});
         },
 
         _onlyComputerLogo: function(e) {
@@ -172,9 +179,18 @@
 			this.fileUploader.render();
             this.fileUploader2.render();
             this.fileUploader3.render();
-            if(this.model.attributes.Logo && (!Array.isArray(this.model.attributes.Logo))){this.$('.group-content-logo .uploader').addClass('done');}
-            if(this.model.attributes.LogoSmall && (!Array.isArray(this.model.attributes.LogoSmall))){this.$('.group-content-logo-small .uploader').addClass('done');}
-            if(this.model.attributes.Favicon && (!Array.isArray(this.model.attributes.Favicon))){this.$('.group-content-favicon .uploader').addClass('done');}
+            if(this.model.attributes.Logo && (!Array.isArray(this.model.attributes.Logo))){
+                this.$('.group-content-logo .uploader').addClass('done');
+                this.$('.menu-wrapper-logo .uploader .preview .imagepreview').css({opacity: 1});
+            }
+            if(this.model.attributes.LogoSmall && (!Array.isArray(this.model.attributes.LogoSmall))){
+                this.$('.group-content-logo-small .uploader').addClass('done');
+                this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview').css({opacity: 1});
+            }
+            if(this.model.attributes.Favicon && (!Array.isArray(this.model.attributes.Favicon))){
+                this.$('.group-content-favicon .uploader').addClass('done');
+                this.$('.menu-wrapper-favicon .uploader .preview .imagepreview').css({opacity: 1});
+            }
             this.$('.menu-wrapper-logo .uploader .preview .imagepreview').after('<span style="font-size:9px;display:block;margin-top:35px;color: #666;text-align:center;">'+this.translations.DefaultMessageUploaderLogo+'</span>');
             this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview').after('<span style="font-size:9px;display:block;margin-top:35px;color: #666;text-align:center;">'+this.translations.DefaultMessageUploaderLogo+'</span>');
             this.$('.menu-wrapper-favicon .uploader .preview .imagepreview').after('<span style="font-size:9px;display:block;margin-top:35px;color: #666;text-align:center;">'+this.translations.DefaultMessageUploaderLogo+'</span>');
@@ -181,6 +197,38 @@
             return this;
         },
 
+        copyToClipboard : function (e){
+            var copyText = $(e.currentTarget).text();
+            const clipboard = navigator.clipboard;
+            if (clipboard !== undefined && clipboard !== "undefined") {
+                navigator.clipboard.writeText(copyText.trim()).then(this.successfully($(e.currentTarget)));
+            } 
+            else 
+            {
+                if (document.execCommand) 
+                {
+                    const el = document.createElement("input");
+                    el.value = copyText;
+                    document.body.append(el);
+                    el.select();
+                    el.setSelectionRange(0, value.length);
+                    if (document.execCommand("copy")) 
+                    {
+                        this.successfully();
+                    }
+                    el.remove();
+                }
+            }
+        },
+
+        successfully :function (el){
+            el.before('<div role="alert" aria-live="polite" style="top: auto;position: absolute;" class="toastcopy"><div class="jq-toast-single jq-icon-success" style="text-align: left; /*! display: none; */"><span class="icon icon-check-circle"></span><span>'+translate("copy")+'</span></div></div>')
+            window.setTimeout(function(){
+            el.parent().find('.toastcopy').remove();
+            }, 2000);
+        }
+                
+
     });
 
     return MarqueClient;
Index: src/less/imports/params_panel/module/inline-social.less
===================================================================
--- src/less/imports/params_panel/module/inline-social.less	(révision 10050)
+++ src/less/imports/params_panel/module/inline-social.less	(révision 10051)
@@ -59,6 +59,10 @@
 		&.pinterest {
 			color: @pinterest-color;
 		}
+        &.mark {
+            margin-right: 10px;
+            padding-right: 10px;
+        }
 
 		svg, img {
 			.align-middle();
@@ -100,6 +104,7 @@
         -ms-transform: scale(1.3);
         -o-transform: scale(1.3);
         transform: scale(1.3);
+        cursor: pointer;
     }
 
     .menu-wrapper-logoSmall {
@@ -108,6 +113,7 @@
         -ms-transform: scale(1);
         -o-transform: scale(1);
         transform: scale(1);
+        cursor: pointer;
     }
 
     .menu-wrapper-favicon {
@@ -116,6 +122,7 @@
         -ms-transform: scale(1);
         -o-transform: scale(1);
         transform: scale(1);
+        cursor: pointer;
     }
     
     .inline-params__nameLogo{
