Revision: r11876
Date: 2024-02-15 12:07:39 +0300 (lkm 15 Feb 2024) 
Author: rrakotoarinelina 

## Commit message
balises métas OpenGraph - resize image et renforcement restriction 

## Files changed

## Full metadata
------------------------------------------------------------------------
r11876 | rrakotoarinelina | 2024-02-15 12:07:39 +0300 (lkm 15 Feb 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Views/FileUploaderView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Templates/MarqueClient.html
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/params_panel/module/inline-social.less

balises métas OpenGraph - resize image et renforcement restriction 
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Files/Views/FileUploaderView.js
===================================================================
--- src/js/JEditor/Commons/Files/Views/FileUploaderView.js	(révision 11875)
+++ src/js/JEditor/Commons/Files/Views/FileUploaderView.js	(révision 11876)
@@ -64,7 +64,7 @@
         },
         _onUpload: function(event, data) {
             var file = data.filesData[0];
-            if (file) {
+            if (file && data.error == "") {
                 this.currentFile = new File(file.response);
                 this.collection.add(this.currentFile);
                 this.trigger(Events.FileUploaderEvents.UPLOAD, this.currentFile, this.collection);
Index: src/js/JEditor/ParamsPanel/Templates/MarqueClient.html
===================================================================
--- src/js/JEditor/ParamsPanel/Templates/MarqueClient.html	(révision 11875)
+++ src/js/JEditor/ParamsPanel/Templates/MarqueClient.html	(révision 11876)
@@ -83,9 +83,11 @@
                                 <span class="inline-params__nameOpengraph">
                                     <%=__("Add_opengraph")%>
                                 </span>
+                                <br>
+                                <strong><em><%=__("Text_opengraph")%></em></strong>
                                 <!-- <span class="rigth-delete-image icon-bin" data-name="Opengraph" style="top: 32%;right: 17%;"> </span> -->
 
-                                <span class="rigth-delete-image icon-bin" data-name="Opengraph" style="top: 16%; right: 5%;"> </span>
+                                <span class="rigth-delete-image icon-bin" data-name="Opengraph" style="top: 26%; right: 5%;"> </span>
 
                                 <div class="menu-wrapper-opengraph file image">
                                     <header></header>
Index: src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 11875)
+++ src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 11876)
@@ -69,6 +69,7 @@
         "Add_logo_small": "Ajouter le logo \"small\"",
         "Add_favicon": "Ajouter le favicon",
         "Add_opengraph":"Ajouter image OpenGraph (L'image doit être de 1200x630 pixels)",
+        "Text_opengraph": "! Les balises OpenGraph ne sont générés dans les pages qu'après upload d'une image.",
         "set_marque_client": "Renseignez les informations de Marque du client tel que l'enseigne, le logo ...",
         "DefaultMessageUploaderLogo": "Cliquez ici ou glissez-déposez une image",
         "copy":"Copié", 
Index: src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 11875)
+++ src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 11876)
@@ -73,6 +73,7 @@
         "Add_logo_small": "Ajouter le logo \"small\"",
         "Add_favicon": "Ajouter le favicon",
         "Add_opengraph":"Ajouter image OpenGraph (L'image doit être de 1200x630 pixels)",
+        "Text_opengraph": "! Les balises OpenGraph ne sont générés dans les pages qu'après upload d'une image.",
         "set_marque_client": "Renseignez les informations de Marque du client tel que l'enseigne, le logo ...",
         "DefaultMessageUploaderLogo": "Cliquez ici ou glissez-déposez une image",
         "copy":"Copié", 
Index: src/js/JEditor/ParamsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 11875)
+++ src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 11876)
@@ -81,6 +81,7 @@
         "Add_logo_small": "Add logo \"small\"",
         "Add_favicon": "Add favicon",
         "Add_opengraph":"Add OpenGraph image (Image must be 1200x630 pixels)",
+        "Text_opengraph": "! OpenGraph tags are only generated in pages after an image has been uploaded.",
         "set_marque_client": "Fill in the customer's brand information such as the brand name, the logo ...",
         "DefaultMessageUploaderLogo": "Click here or drag and drop an image",
         "copy":"Copied", 
Index: src/less/imports/params_panel/module/inline-social.less
===================================================================
--- src/less/imports/params_panel/module/inline-social.less	(révision 11875)
+++ src/less/imports/params_panel/module/inline-social.less	(révision 11876)
@@ -257,6 +257,12 @@
 
     }
 
+    .group-content-opengraph strong{
+        position: absolute;
+        margin-left: 50px;
+        margin-bottom: 10px;
+        top: 7%;
+    }
 
     .group-content-opengraph .uploader{
         width : 500px;
@@ -263,6 +269,7 @@
         line-height: 10px;
         margin-left: 50px;
         margin-right: 50px;
+        margin-top: 50px;
         border: 1px solid #666;
         background-color: #ffffff;
         height: 270px;
