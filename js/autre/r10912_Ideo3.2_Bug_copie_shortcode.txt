Revision: r10912
Date: 2023-05-09 10:53:40 +0300 (tlt 09 Mey 2023) 
Author: nor<PERSON><PERSON><PERSON><PERSON> 

## Commit message
Ideo3.2 : Bug copie shortcode

## Files changed

## Full metadata
------------------------------------------------------------------------
r10912 | norajaonarivelo | 2023-05-09 10:53:40 +0300 (tlt 09 Mey 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/App/Messages/ClipboardModule.js

Ideo3.2 : Bug copie shortcode
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/App/Messages/ClipboardModule.js
===================================================================
--- src/js/JEditor/App/Messages/ClipboardModule.js	(révision 10911)
+++ src/js/JEditor/App/Messages/ClipboardModule.js	(révision 10912)
@@ -10,6 +10,7 @@
           var copyText = copyText.replace(edit, '').trim();
         }
             const clipboard = navigator.clipboard;
+            //it cannot be used from a content script running on http:-pages, only https:-pages.
             if (clipboard !== undefined && clipboard !== "undefined") {
                 navigator.clipboard.writeText(copyText.trim()).then(this.successfully($(e.currentTarget),e));
             } 
@@ -18,13 +19,13 @@
                 if (document.execCommand) 
                 {
                     const el = document.createElement("input");
-                    el.value = copyText;
+                    el.value = copyText.trim();
                     document.body.append(el);
                     el.select();
-                    el.setSelectionRange(0, value.length);
+                    el.setSelectionRange(0, copyText.length);
                     if (document.execCommand("copy")) 
                     {
-                        this.successfully();
+                        this.successfully($(e.currentTarget),e);
                     }
                     el.remove();
                 }
