Revision: r14167
Date: 2025-04-25 16:51:33 +0300 (zom 25 Apr 2025) 
Author: mpartaux 

## Commit message
test inversion des boutons

## Files changed

## Full metadata
------------------------------------------------------------------------
r14167 | mpartaux | 2025-04-25 16:51:33 +0300 (zom 25 Apr 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/NewsPanel.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/categorieList.html

test inversion des boutons
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Templates/NewsPanel.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/NewsPanel.html	(révision 14166)
+++ src/js/JEditor/NewsPanel/Templates/NewsPanel.html	(révision 14167)
@@ -29,15 +29,12 @@
             </div>
         </div>
         <!--liste des pages-->
-        <% if(canAddNews){ %>
-            <a class="dialog-view-trigger addArticle">
-                <span>+</span><%=__("newsAddArticle")%>
-            </a>
-            <a class="dialog-view-trigger uploadArticle">
-                <span class="icon-add-image"></span>
-                <%=__("uploadLink")%>
-            </a>
-        <% } %>
+        
+
+        <a class="dialog-view-trigger addCategory">
+            <span>+</span><%=__("newsAddACategory")%>
+        </a>
+
 	</aside>
     <div id="item-config" class="panel-container"></div>
   
Index: src/js/JEditor/NewsPanel/Templates/categorieList.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/categorieList.html	(révision 14166)
+++ src/js/JEditor/NewsPanel/Templates/categorieList.html	(révision 14167)
@@ -21,6 +21,13 @@
         </ul>
     </nav>
 </div>
-<a class="dialog-view-trigger addCategory">
-    <span>+</span><%=__("newsAddACategory")%>
-</a>
+
+<% if(canAddNews){ %>
+    <a class="dialog-view-trigger addArticle">
+        <span>+</span><%=__("newsAddArticle")%>
+    </a>
+    <a class="dialog-view-trigger uploadArticle">
+        <span class="icon-add-image"></span>
+        <%=__("uploadLink")%>
+    </a>
+<% } %>
