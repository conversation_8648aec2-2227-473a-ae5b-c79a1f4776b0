Revision: r13717
Date: 2025-01-20 11:16:23 +0300 (lts 20 Jan 2025) 
Author: frahajanirina 

## Commit message
Wishlist:IDEO3.2:fix panel configuration article

## Files changed

## Full metadata
------------------------------------------------------------------------
r13717 | frahajanirina | 2025-01-20 11:16:23 +0300 (lts 20 Jan 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/PublishConfigView.js

Wishlist:IDEO3.2:fix panel configuration article
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Views/PublishConfigView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/PublishConfigView.js	(révision 13716)
+++ src/js/JEditor/NewsPanel/Views/PublishConfigView.js	(révision 13717)
@@ -169,6 +169,11 @@
         save: function() {
             var d = new Date();
             this.model.publicationDate = (this._programmed === false) ? d.getDate() + '-' + (d.getMonth() + 1) + '-' + d.getFullYear() : null;
+            if (this._programmed) {
+                this.model.set('state', [1]);
+            } else {
+                this.model.set('state', [2]);
+            }
             this.model.save();
         },
         /**
