Revision: r10319
Date: 2023-02-03 09:44:54 +0300 (zom 03 Feb 2023) 
Author: jn.harison 

## Commit message
Debug mark client:show image as a whole if svg

## Files changed

## Full metadata
------------------------------------------------------------------------
r10319 | jn.harison | 2023-02-03 09:44:54 +0300 (zom 03 Feb 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Templates/MarqueClient.html
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/MarqueClientView.js
   M /branches/ideo3_v2/integration/src/less/imports/params_panel/main.less
   A /branches/ideo3_v2/integration/src/less/imports/params_panel/module/ideo-mark-client.less

Debug mark client:show image as a whole if svg
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Templates/MarqueClient.html
===================================================================
--- src/js/JEditor/ParamsPanel/Templates/MarqueClient.html	(révision 10318)
+++ src/js/JEditor/ParamsPanel/Templates/MarqueClient.html	(révision 10319)
@@ -1,54 +1,4 @@
-<style type="text/css">
-    .inline-params__nameLogoFooter .shortcode {
-        font-family: 'Open Sans',sans-serif;
-        font-weight: 400;
-        font-size: 12px;
-        border: 1px solid #CCC;
-        color: #999;
-        -webkit-border-radius: 4px;
-        -moz-border-radius: 4px;
-        border-radius: 4px;
-        line-height: 36px;
-        padding: 15px;
-        margin-top: 1px;
-        margin-bottom: 15px;
-        cursor: pointer;
-    }
-    .relative{
-        position: relative;
-    }
-    .rigth-delete-image {
-        background-color: #f4f4f4;
-        padding: 0 5px;
-        height: 23px;
-        border-radius: 20px;
-        width: 10px;
-        border: 5px solid #fff;
-        position: absolute;
-        margin-right: -10px;
-        display: none;
-        line-height: 20px;
-        text-align: center;
-        font-size: 12px;
-        cursor: pointer;
-        z-index: 111;
-    }
-    .shortcode {
-        font-family: 'Open Sans',sans-serif;
-        font-weight: 400;
-        font-size: 12px;
-        border: 1px solid #CCC;
-        color: #999;
-        -webkit-border-radius: 4px;
-        -moz-border-radius: 4px;
-        border-radius: 4px;
-        line-height: 36px;
-        padding: 15px;
-        margin-top: 1px;
-        margin-bottom: 15px;
-        cursor: pointer;
-    }
-</style>  
+
     <div class="main-content__wrapper ">
         <!--
             ******************************************
Index: src/less/imports/params_panel/main.less
===================================================================
--- src/less/imports/params_panel/main.less	(révision 10318)
+++ src/less/imports/params_panel/main.less	(révision 10319)
@@ -18,7 +18,7 @@
 @import 'module/ideo-btn';
 @import 'module/inline-social';
 @import 'module/inline-shortcode';
-
+@import 'module/ideo-mark-client.less';
 @import 'state/state';
 
 // oriented object small css helper
Index: src/less/imports/params_panel/module/ideo-mark-client.less
===================================================================
--- src/less/imports/params_panel/module/ideo-mark-client.less	(nonexistent)
+++ src/less/imports/params_panel/module/ideo-mark-client.less	(révision 10319)
@@ -0,0 +1,49 @@
+.inline-params__nameLogoFooter .shortcode {
+    font-family: 'Open Sans',sans-serif;
+    font-weight: 400;
+    font-size: 12px;
+    border: 1px solid #CCC;
+    color: #999;
+    -webkit-border-radius: 4px;
+    -moz-border-radius: 4px;
+    border-radius: 4px;
+    line-height: 36px;
+    padding: 15px;
+    margin-top: 1px;
+    margin-bottom: 15px;
+    cursor: pointer;
+}
+.relative{
+    position: relative;
+}
+.rigth-delete-image {
+    background-color: #f4f4f4;
+    padding: 0 5px;
+    height: 23px;
+    border-radius: 20px;
+    width: 10px;
+    border: 5px solid #fff;
+    position: absolute;
+    margin-right: -10px;
+    display: none;
+    line-height: 20px;
+    text-align: center;
+    font-size: 12px;
+    cursor: pointer;
+    z-index: 111;
+}
+.shortcode {
+    font-family: 'Open Sans',sans-serif;
+    font-weight: 400;
+    font-size: 12px;
+    border: 1px solid #CCC;
+    color: #999;
+    -webkit-border-radius: 4px;
+    -moz-border-radius: 4px;
+    border-radius: 4px;
+    line-height: 36px;
+    padding: 15px;
+    margin-top: 1px;
+    margin-bottom: 15px;
+    cursor: pointer;
+}
\ No newline at end of file
Index: src/js/JEditor/ParamsPanel/Views/MarqueClientView.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 10318)
+++ src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 10319)
@@ -195,17 +195,29 @@
             if(this.model.attributes.Logo && (!Array.isArray(this.model.attributes.Logo))){
                 this.$('.group-content-logo .uploader').addClass('done');
                 this.$('.menu-wrapper-logo .uploader .preview .imagepreview').css({opacity: 1});
-                this.$('.group-content-logo .rigth-delete-image').show();
+                this.$('.group-content-logo .rigth-delete-image').show();   
+                if(this.model.attributes.Logo.ext === "svg"){
+                    this.$('.menu-wrapper-logo .uploader .preview .imagepreview').css({backgroundSize:'100%'});
+                    this.$('.menu-wrapper-logo .uploader .preview .imagepreview').css({backgroundRepeat:'no-repeat'});
+                }             
             }
             if(this.model.attributes.LogoSmall && (!Array.isArray(this.model.attributes.LogoSmall))){
                 this.$('.group-content-logo-small .uploader').addClass('done');
                 this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview').css({opacity: 1});
                 this.$('.group-content-logo-small .rigth-delete-image').show();
+                if(this.model.attributes.LogoSmall.ext === "svg"){
+                    this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview').css({backgroundSize:'100%'});
+                    this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview').css({backgroundRepeat:'no-repeat'});
+                }       
             }
             if(this.model.attributes.Favicon && (!Array.isArray(this.model.attributes.Favicon))){
                 this.$('.group-content-favicon .uploader').addClass('done');
                 this.$('.menu-wrapper-favicon .uploader .preview .imagepreview').css({opacity: 1});
                 this.$('.group-content-favicon .rigth-delete-image').show();
+                if(this.model.attributes.Favicon.ext === "svg"){
+                    this.$('.menu-wrapper-favicon .uploader .preview .imagepreview').css({backgroundSize:'100%'});
+                    this.$('.menu-wrapper-favicon .uploader .preview .imagepreview').css({backgroundRepeat:'no-repeat'});
+                }       
             }
             this.$('.menu-wrapper-logo .uploader .preview .imagepreview').after('<span style="font-size:9px;display:block;margin-top:35px;color: #666;text-align:center;">'+this.translations.DefaultMessageUploaderLogo+'</span>');
             this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview').after('<span style="font-size:9px;display:block;margin-top:35px;color: #666;text-align:center;">'+this.translations.DefaultMessageUploaderLogo+'</span>');
