Revision: r10852
Date: 2023-04-24 08:34:54 +0300 (lts 24 Apr 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: options shortcode [[social_link]] (correction pour cacher le shortcode si network id null ) 

## Files changed

## Full metadata
------------------------------------------------------------------------
r10852 | srazanandralisoa | 2023-04-24 08:34:54 +0300 (lts 24 Apr 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Templates/ListSocialNetwork.html
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/ListSocialNetworks.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/SocialNetworks.js

wishlist IDEO3.2: options shortcode [[social_link]] (correction pour cacher le shortcode si network id null ) 
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Templates/ListSocialNetwork.html
===================================================================
--- src/js/JEditor/ParamsPanel/Templates/ListSocialNetwork.html	(révision 10851)
+++ src/js/JEditor/ParamsPanel/Templates/ListSocialNetwork.html	(révision 10852)
@@ -312,8 +312,8 @@
                     </span>
                 </label>
                 <span class="rightShortcode">
-                    <span class="shortcode intfont">[[social_link|network=<%=social.network %>]]</span>
-                    <span class="shortcode intfont">[[social_link|id=<%=social.id %>]]</span>
+                    <% if( typeof(social.network) !== 'undefined') {%> <span class="shortcode">[[social_link|network=<%=social.network %>]]</span><% } %>
+                    <% if( typeof(social.id) !== 'undefined') { %><span class="shortcode">[[social_link|id=<%=social.id %>]]</span><% } %>
                 </span>
                 <span class="rigth-delete icon-bin"> </span>
             </li>
Index: src/js/JEditor/ParamsPanel/Views/ListSocialNetworks.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/ListSocialNetworks.js	(révision 10851)
+++ src/js/JEditor/ParamsPanel/Views/ListSocialNetworks.js	(révision 10852)
@@ -13,7 +13,7 @@
             //"blur .content": "onChangeTitle",
             "change .content.rs": "onChangeTitle",
             "click .rigth-delete " :"DeleteOneSocialNetwork",
-            'click .shortcode.intfont' : 'copyToClipboard',
+            'click .rightShortcode .shortcode' : 'copyToClipboard',
         },
         initialize: function () {
             this.model=this.options.model;
Index: src/js/JEditor/ParamsPanel/Views/SocialNetworks.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/SocialNetworks.js	(révision 10851)
+++ src/js/JEditor/ParamsPanel/Views/SocialNetworks.js	(révision 10852)
@@ -48,15 +48,17 @@
         },
         onClickCascade : function(e){
             var target=e.target;
-            var listeSN=this.model.get("SocialNetworksUrl");
+            var listeSN = this.model.get("SocialNetworksUrl");
             var Type = target.getAttribute("data-id");
+            // Utilisation de Array.filter() pour filtrer les tableaux si les valeurs d'id est un nombre non NaN
+            var filteredArr = listeSN.filter(obj => Number.isInteger(obj.id) && !isNaN(obj.id));
             // Utilisation de Array.map() pour extraire les valeurs d'id dans un tableau
-            var listeId = listeSN.map(function(objet) {
-                return objet.id;
+            var listeId = filteredArr.map(function(objet) {
+                    return objet.id;
             });
             
             // Si listeId est 0 on le return sinon on utilise de Math.max() pour trouver la plus grande valeur d'id
-            var maxId = (listeId.length === 0)? listeId :Math.max(...listeId);
+            var maxId = (listeId.length === 0)? 0 : Math.max(...listeId);
             
             var object={
                 id: maxId+1,
