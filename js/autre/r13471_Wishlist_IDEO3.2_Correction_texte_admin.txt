Revision: r13471
Date: 2024-11-19 15:40:29 +0300 (tlt 19 Nov 2024) 
Author: frahajanirina 

## Commit message
Wishlist:IDEO3.2:Correction texte admin

## Files changed

## Full metadata
------------------------------------------------------------------------
r13471 | frahajanirina | 2024-11-19 15:40:29 +0300 (tlt 19 Nov 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Addresses/nls/en-us/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Addresses/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Addresses/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SeparatorBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SeparatorBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TemplateBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TemplateBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TemplateBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/i18n.js

Wishlist:IDEO3.2:Correction texte admin
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Addresses/nls/en-us/i18n.js
===================================================================
--- src/js/JEditor/Commons/Addresses/nls/en-us/i18n.js	(révision 13470)
+++ src/js/JEditor/Commons/Addresses/nls/en-us/i18n.js	(révision 13471)
@@ -1,5 +1,5 @@
 define({
-	"newAddress": "Nouvelle addresse",
+	"newAddress": "New address",
 	"error": "Erreur",
 	"mapError": "Impossible de créer le plan, vérifiez votre connexion Internet",
 	"linkPlan":"Map link",
Index: src/js/JEditor/Commons/Addresses/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/Commons/Addresses/nls/fr-ca/i18n.js	(révision 13470)
+++ src/js/JEditor/Commons/Addresses/nls/fr-ca/i18n.js	(révision 13471)
@@ -1 +1 @@
-define({"newAddress":"Nouvelle addresse","name":"Nom","address":"Adresse","phone":"Téléphone","email":"email","website":"Site Web","adjustPointer":"Ajuster la position du pointeur","latt":"lattitude","lng":"longitude","linkPlan":"Lien du plan", "planLink":"Privilégier lien GMB si disponible","labelIdGmb":"Préciser ID à utiliser si plusieurs GMB"});
\ No newline at end of file
+define({"newAddress":"Nouvelle adresse","name":"Nom","address":"Adresse","phone":"Téléphone","email":"email","website":"Site Web","adjustPointer":"Ajuster la position du pointeur","latt":"lattitude","lng":"longitude","linkPlan":"Lien du plan", "planLink":"Privilégier lien GMB si disponible","labelIdGmb":"Préciser ID à utiliser si plusieurs GMB"});
\ No newline at end of file
Index: src/js/JEditor/Commons/Addresses/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/Commons/Addresses/nls/fr-fr/i18n.js	(révision 13470)
+++ src/js/JEditor/Commons/Addresses/nls/fr-fr/i18n.js	(révision 13471)
@@ -1 +1 @@
-define({"newAddress":"Nouvelle addresse","name":"Nom","address":"Adresse","phone":"Téléphone","email":"email","website":"Site Web","adjustPointer":"Ajuster la position du pointeur","latt":"lattitude","lng":"longitude","linkPlan":"Lien du plan", "planLink":"Privilégier lien GMB si disponible","labelIdGmb":"Préciser ID à utiliser si plusieurs GMB"});
\ No newline at end of file
+define({"newAddress":"Nouvelle adresse","name":"Nom","address":"Adresse","phone":"Téléphone","email":"email","website":"Site Web","adjustPointer":"Ajuster la position du pointeur","latt":"lattitude","lng":"longitude","linkPlan":"Lien du plan", "planLink":"Privilégier lien GMB si disponible","labelIdGmb":"Préciser ID à utiliser si plusieurs GMB"});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/nls/fr-ca/i18n.js	(révision 13470)
+++ src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/nls/fr-ca/i18n.js	(révision 13471)
@@ -1 +1 @@
-define({"BLOCK_NAME":"Plan","myAddresses":"Mes adresses","address":"Addresses","mapStyle":"Styles du plan","selectAddress":"Sélectionnez et configurez les adresses qui apparaîtront sur votre plan ci-dessous.","addAddress":"Ajouter une adresse","websiteLabel":"Site Web :","phoneLabel":"Téléphone :","emailLabel":"email :","mapView":"Vue du plan","roadmap":"vue plan","satellite":"vue satellite","hybrid":"vue mixte","mapBackground":"Fond du plan","color":"couleur","bw":"Noir et Blanc","zoom":"Niveau de zoom","mapHeight":"Hauteur du plan","error":"Erreur","mapError":"Impossible de créer le plan, vérifiez votre connexion Internet","mapBlockOption":"Options du bloc de plan","copy":"Copié"});
\ No newline at end of file
+define({"BLOCK_NAME":"Plan","myAddresses":"Mes adresses","address":"Adresses","mapStyle":"Styles du plan","selectAddress":"Sélectionnez et configurez les adresses qui apparaîtront sur votre plan ci-dessous.","addAddress":"Ajouter une adresse","websiteLabel":"Site Web :","phoneLabel":"Téléphone :","emailLabel":"email :","mapView":"Vue du plan","roadmap":"vue plan","satellite":"vue satellite","hybrid":"vue mixte","mapBackground":"Fond du plan","color":"couleur","bw":"Noir et Blanc","zoom":"Niveau de zoom","mapHeight":"Hauteur du plan","error":"Erreur","mapError":"Impossible de créer le plan, vérifiez votre connexion Internet","mapBlockOption":"Options du bloc de plan","copy":"Copié"});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/nls/fr-fr/i18n.js	(révision 13470)
+++ src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/nls/fr-fr/i18n.js	(révision 13471)
@@ -1 +1 @@
-define({"BLOCK_NAME":"Plan","myAddresses":"Mes adresses","address":"Addresses","mapStyle":"Styles du plan","selectAddress":"Sélectionnez et configurez les adresses qui apparaîtront sur votre plan ci-dessous.","addAddress":"Ajouter une adresse","websiteLabel":"Site Web :","phoneLabel":"Téléphone :","emailLabel":"email :","mapView":"Vue du plan","roadmap":"vue plan","satellite":"vue satellite","hybrid":"vue mixte","mapBackground":"Fond du plan","color":"couleur","bw":"Noir et Blanc","zoom":"Niveau de zoom","mapHeight":"Hauteur du plan","error":"Erreur","mapError":"Impossible de créer le plan, vérifiez votre connexion Internet","mapBlockOption":"Options du bloc de plan","copy":"Copié"});
\ No newline at end of file
+define({"BLOCK_NAME":"Plan","myAddresses":"Mes adresses","address":"Adresses","mapStyle":"Styles du plan","selectAddress":"Sélectionnez et configurez les adresses qui apparaîtront sur votre plan ci-dessous.","addAddress":"Ajouter une adresse","websiteLabel":"Site Web :","phoneLabel":"Téléphone :","emailLabel":"email :","mapView":"Vue du plan","roadmap":"vue plan","satellite":"vue satellite","hybrid":"vue mixte","mapBackground":"Fond du plan","color":"couleur","bw":"Noir et Blanc","zoom":"Niveau de zoom","mapHeight":"Hauteur du plan","error":"Erreur","mapError":"Impossible de créer le plan, vérifiez votre connexion Internet","mapBlockOption":"Options du bloc de plan","copy":"Copié"});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/SeparatorBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SeparatorBlock/nls/fr-ca/i18n.js	(révision 13470)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SeparatorBlock/nls/fr-ca/i18n.js	(révision 13471)
@@ -2,7 +2,7 @@
    "BLOCK_NAME":"Séparation",
    "separator":"Séparation",
    "titleSeparator":"Style de la ligne de séparation",
-   "legendSeparator":"Personnalisez l'épaisseur de la ligne et choisissez son et style et sa couleur",
+   "legendSeparator":"Personnalisez l'épaisseur de la ligne et choisissez son style et sa couleur",
    "bordersDotted":"Pointillés",
    "bordersDashed":"Tirets",
    "bordersSolid":"Unie",
Index: src/js/JEditor/PagePanel/Contents/Blocks/SeparatorBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SeparatorBlock/nls/fr-fr/i18n.js	(révision 13470)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SeparatorBlock/nls/fr-fr/i18n.js	(révision 13471)
@@ -2,7 +2,7 @@
    "BLOCK_NAME":"Séparation",
    "separator":"Séparation",
    "titleSeparator":"Style de la ligne de séparation",
-   "legendSeparator":"Personnalisez l'épaisseur de la ligne et choisissez son et style et sa couleur",
+   "legendSeparator":"Personnalisez l'épaisseur de la ligne et choisissez son style et sa couleur",
    "bordersDotted":"Pointillés",
    "bordersDashed":"Tirets",
    "bordersSolid":"Unie",
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-ca/i18n.js	(révision 13470)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-ca/i18n.js	(révision 13471)
@@ -56,5 +56,6 @@
     "DescStyle3":  "Texte sur l'image",
     "DescStyle4":  "Textes sous l'image avec bordures",
     "DescStyle5":  "Textes sous l'image avec images arrondies",
-    "delayText" : "Glissez pour ajuster le délais"  
+    "delayText" : "Glissez pour ajuster le délais",
+    "emptySlideshow":"Votre diaporama est vide"  
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-fr/i18n.js	(révision 13470)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-fr/i18n.js	(révision 13471)
@@ -57,5 +57,6 @@
     "DescStyle4":  "Texte sous l'image, bordures",
     "DescStyle5":  "Texte sous l'image, images arrondies (idéal images carrés)",             
     "DescStyle6":  "Texte à côté de l'image",
-    "delayText" : "Glissez pour ajuster le délais"             
+    "delayText" : "Glissez pour ajuster le délais",
+    "emptySlideshow":"Votre diaporama est vide"             
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/nls/fr-ca/i18n.js	(révision 13470)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/nls/fr-ca/i18n.js	(révision 13471)
@@ -1,4 +1,5 @@
 define({
     "BLOCK_NAME": "Tableau",
-    "Title":"Titre"
+    "Title":"Titre",
+    "tableBlockOption": 'Option du tableau'
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/nls/fr-fr/i18n.js	(révision 13470)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/nls/fr-fr/i18n.js	(révision 13471)
@@ -1,4 +1,5 @@
 define({
     "BLOCK_NAME": "Tableau",
-    "Title":"Titre"
+    "Title":"Titre",
+    "tableBlockOption": 'Option du tableau'
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/nls/i18n.js	(révision 13470)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/nls/i18n.js	(révision 13471)
@@ -1,4 +1,5 @@
 define({"root": {
         "BLOCK_NAME": "Table",
-        "Title":"Title"
+        "Title":"Title",
+        "tableBlockOption": 'Table option'
     }, "fr-fr": true, "fr-ca": true});
Index: src/js/JEditor/PagePanel/Contents/Blocks/TemplateBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TemplateBlock/nls/fr-ca/i18n.js	(révision 13470)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TemplateBlock/nls/fr-ca/i18n.js	(révision 13471)
@@ -1 +1 @@
-define({"BLOCK_NAME":"Bouton","addButton":"Texte du bouton","buttonOptionLegend":"Renseignez le texte de votre boutton ici.","buttonTitle":"Texte du bouton","selectClickActionButton":"Action au clic sur le bouton","buttonActionLegend":"Sélectionez et configurez l'action souhaitée au clic sur le bouton","cancel":"Annuler","image":"Image","templateBlockOption":"Options du bouton"});
\ No newline at end of file
+define({"BLOCK_NAME":"Bouton","addButton":"Texte du bouton","buttonOptionLegend":"Renseignez le texte de votre boutton ici.","buttonTitle":"Texte du bouton","selectClickActionButton":"Action au clic sur le bouton","buttonActionLegend":"Sélectionnez et configurez l'action souhaitée au clic sur le bouton","cancel":"Annuler","image":"Image","templateBlockOption":"Options du bouton"});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/TemplateBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TemplateBlock/nls/fr-fr/i18n.js	(révision 13470)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TemplateBlock/nls/fr-fr/i18n.js	(révision 13471)
@@ -1,3 +1,3 @@
 define({
-  "BLOCK_NAME":"Bouton","addButton":"Texte du bouton","buttonOptionLegend":"Renseignez le texte de votre boutton ici.","buttonTitle":"Texte du bouton","selectClickActionButton":"Action au clic sur le bouton","buttonActionLegend":"Sélectionez et configurez l'action souhaitée au clic sur le bouton","cancel":"Annuler","image":"Image","templateBlockOption":"Options du bouton"
+  "BLOCK_NAME":"Bouton","addButton":"Texte du bouton","buttonOptionLegend":"Renseignez le texte de votre boutton ici.","buttonTitle":"Texte du bouton","selectClickActionButton":"Action au clic sur le bouton","buttonActionLegend":"Sélectionnez et configurez l'action souhaitée au clic sur le bouton","cancel":"Annuler","image":"Image","templateBlockOption":"Options du bouton"
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/TemplateBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TemplateBlock/nls/i18n.js	(révision 13470)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TemplateBlock/nls/i18n.js	(révision 13471)
@@ -1 +1 @@
-define({ "root": {"BLOCK_NAME":"Bouton","addButton":"Texte du bouton","buttonOptionLegend":"Renseignez le texte de votre boutton ici.","buttonTitle":"Texte du bouton","selectClickActionButton":"Action au clic sur le bouton","buttonActionLegend":"Sélectionez et configurez l'action souhaitée au clic sur le bouton","cancel":"Annuler","image":"Image","templateBlockOption":"Options du bouton"}, "fr-fr":true, "fr-ca":true })
\ No newline at end of file
+define({ "root": {"BLOCK_NAME":"Bouton","addButton":"Texte du bouton","buttonOptionLegend":"Renseignez le texte de votre boutton ici.","buttonTitle":"Texte du bouton","selectClickActionButton":"Action au clic sur le bouton","buttonActionLegend":"Sélectionnez et configurez l'action souhaitée au clic sur le bouton","cancel":"Annuler","image":"Image","templateBlockOption":"Options du bouton"}, "fr-fr":true, "fr-ca":true })
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Options/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/nls/fr-ca/i18n.js	(révision 13470)
+++ src/js/JEditor/PagePanel/Contents/Options/nls/fr-ca/i18n.js	(révision 13471)
@@ -75,7 +75,7 @@
     "styles" : "Styles",
     "BlockColor"  : "Couleurs du bloc",
     "BlockColorDesc"  : "Appliquer un ensemble de couleurs",
-    "shadeWorn" : "Ombres portée",
+    "shadeWorn" : "Ombres portées",
     "shadeWornDesc" :"Faire glisser pour ajuster l'intensité de l'ombre",
     "contentSize" : "Taille du contenu",
     "contentSizeDesc" : "Glisser pour ajuster la taille du contenu.<br>(Cette option n'a pas d'effet dans la zone d'administration)",
Index: src/js/JEditor/PagePanel/Contents/Options/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/nls/fr-fr/i18n.js	(révision 13470)
+++ src/js/JEditor/PagePanel/Contents/Options/nls/fr-fr/i18n.js	(révision 13471)
@@ -75,7 +75,7 @@
     "styles" : "Styles",
     "BlockColor"  : "Couleurs du bloc",
     "BlockColorDesc"  : "Appliquer un ensemble de couleurs",
-    "shadeWorn" : "Ombres portée",
+    "shadeWorn" : "Ombres portées",
     "shadeWornDesc" :"Faire glisser pour ajuster l'intensité de l'ombre",
     "contentSize" : "Taille du contenu",
     "contentSizeDesc" : "Glisser pour ajuster la taille du contenu.<br>(Cette option n'a pas d'effet dans la zone d'administration)",
Index: src/js/JEditor/ParamsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 13470)
+++ src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 13471)
@@ -90,7 +90,7 @@
         "copy":"Copied", 
         "confirmDelete": "You are about to definitely <br/>suppress the element :</br><strong><% name %></strong>",
         "fontsGoogle": "Google Fonts",
-        "set_fonts_google": "Add google fonts import in \<link\> format<br/>All fonts must be grouped in the same \<link\> <br/>The two \<link rel=\"preconnect\"... = \"\" \> rovided on Google Fonts are mandatory",
+        "set_fonts_google": "Add google fonts import in &lt;link&gt; format<br/>All fonts must be grouped in the same &lt;link&gt; <br/>The two &lt;link rel=\"preconnect\"... = \"\"&gt; rovided on Google Fonts are mandatory",
         "Multiple_FontsGoogleApi": "Several <strong>&lt;link rel=\"preconnect\" href=\"https\:\/\/fonts.googleapis.com\"\&gt;</strong> scripts detected",
         "Missing_FontsGoogleApi": "Missing <strong>&lt;link rel=\"preconnect\" href=\"https\:\/\/fonts.googleapis.com\"\&gt;</strong>",
         "Multiple_FontsGoogleStatic": "Several  <strong>&lt;link rel=\"preconnect\" href=\"https\:\/\/fonts.gstastic.com\" crossorigin\&gt;</strong> scripts detected",
@@ -108,7 +108,7 @@
         "Themedescription" :    "Choose whether to apply the default Dark Mode theme to the site",
         "ThemeLabel":   "Theme",
         "lightLabel":   "Light (default)",
-        "darkLabel" :   "Darl",
+        "darkLabel" :   "Dark",
         "addShortcode": "Add shortcode",
         "CustomShortcode": "Custom shortcode",
         "headStart": "Start of head",
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js	(révision 13470)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js	(révision 13471)
@@ -4,7 +4,7 @@
     "buttonOptionLegend": "Renseignez le texte de votre boutton ici.",
     "buttonTitle": "Texte du bouton",
     "selectClickActionButton": "Action au clic sur le bouton",
-    "buttonActionLegend": "Sélectionez et configurez l'action souhaitée au clic sur le bouton",
+    "buttonActionLegend": "Sélectionnez et configurez l'action souhaitée au clic sur le bouton",
     "cancel": "Annuler",
     "image": "Image",
     "buttonBlockOption": "Options du bouton",
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js	(révision 13470)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js	(révision 13471)
@@ -4,7 +4,7 @@
     "buttonOptionLegend": "Renseignez le texte de votre boutton ici.",
     "buttonTitle": "Texte du bouton",
     "selectClickActionButton": "Action au clic sur le bouton",
-    "buttonActionLegend": "Sélectionez et configurez l'action souhaitée au clic sur le bouton",
+    "buttonActionLegend": "Sélectionnez et configurez l'action souhaitée au clic sur le bouton",
     "cancel": "Annuler",
     "image": "Image",
     "buttonBlockOption": "Options du bouton",
