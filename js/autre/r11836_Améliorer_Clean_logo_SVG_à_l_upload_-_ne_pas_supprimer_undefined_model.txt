Revision: r11836
Date: 2024-02-02 11:38:35 +0300 (zom 02 Feb 2024) 
Author: rrakotoarinelina 

## Commit message
Améliorer Clean logo SVG à l'upload - ne pas supprimer undefined model

## Files changed

## Full metadata
------------------------------------------------------------------------
r11836 | rrakotoarinelina | 2024-02-02 11:38:35 +0300 (zom 02 Feb 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/MarqueClientView.js

Améliorer Clean logo SVG à l'upload - ne pas supprimer undefined model
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Views/MarqueClientView.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 11835)
+++ src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 11836)
@@ -96,7 +96,9 @@
             this.fileCollection.setId(cid);
             this.fileCollection.fetch({async:false});
             var model =this.fileCollection.get(cid);
-            model.destroy(); 
+            if (typeof model !== 'undefined') {
+                model.destroy();
+            }
         },
 
         _onlyComputerLogo: function(e) {
@@ -252,7 +254,9 @@
                         type: 'delete',
                         onOk: _.bind(function() {
                             this.model.set(name, null);
-                            model.destroy();
+                            if (typeof model !== 'undefined') {
+                                model.destroy();
+                            }
                             this.model.save();
                             this.render();
                         }, this),
