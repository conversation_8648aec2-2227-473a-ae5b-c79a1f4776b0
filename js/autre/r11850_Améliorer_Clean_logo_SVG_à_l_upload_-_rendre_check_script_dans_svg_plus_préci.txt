Revision: r11850
Date: 2024-02-06 12:02:42 +0300 (tlt 06 Feb 2024) 
Author: rrakotoarinelina 

## Commit message
Améliorer Clean logo SVG à l'upload - rendre  check script dans svg plus précis

## Files changed

## Full metadata
------------------------------------------------------------------------
r11850 | rrakotoarinelina | 2024-02-06 12:02:42 +0300 (tlt 06 Feb 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js

Améliorer Clean logo SVG à l'upload - rendre  check script dans svg plus précis
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js
===================================================================
--- src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 11849)
+++ src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 11850)
@@ -362,7 +362,7 @@
                             var uploadInfos = {name: file.name, value: file, size: file.size, preview: preview, uploaded: 0,code:e.target.result,type: file.type};
                             if (uploader._isImage(file)) {
                                 var decodedData = atob(uploadInfos.code.split(',')[1]);
-                                if (decodedData.includes('<script>')) {
+                                if (decodedData.includes('<script')) {
                                     message = uploader.options.lang.uploadFailCorrupted;
                                     uploader.errors.push(message);
                                     files.rejected++;
