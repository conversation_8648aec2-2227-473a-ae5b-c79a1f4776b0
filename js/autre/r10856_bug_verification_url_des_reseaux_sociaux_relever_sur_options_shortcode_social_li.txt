Revision: r10856
Date: 2023-04-27 11:35:11 +0300 (lkm 27 Apr 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
bug verification url des reseaux sociaux (relever sur options shortcode [[social_link]])

## Files changed

## Full metadata
------------------------------------------------------------------------
r10856 | srazanandralisoa | 2023-04-27 11:35:11 +0300 (lkm 27 Apr 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/ListSocialNetworks.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/SocialNetworks.js

bug verification url des reseaux sociaux (relever sur options shortcode [[social_link]])
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Views/ListSocialNetworks.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/ListSocialNetworks.js	(révision 10855)
+++ src/js/JEditor/ParamsPanel/Views/ListSocialNetworks.js	(révision 10856)
@@ -48,16 +48,22 @@
             }
             else {
                 if(field.includes("_")){
-                    var arraySpliteField=field.split('_');
-                    field =arraySpliteField[0];
-                    var keyField =arraySpliteField[1];
+                    var arraySpliteField = field.split('_');
+                    field = arraySpliteField[0];
+                    var keyField = arraySpliteField[1];
                     var listeSocialNetwork =this.model.get("SocialNetworksUrl");
-                    listeSocialNetwork[keyField].url=value;
-                    this.model.KeySocialNetworks=keyField;
+                    listeSocialNetwork[keyField].url = value;
+                    this.model.KeySocialNetworks = keyField;
+                    // validation de l'url du field
+                    var valid = this.model.validateSocialNetworks(listeSocialNetwork[keyField]);
+                    var container = this.$("input.field-input[name=\"" + field + "_"+keyField+"\"]").parents(".inline-params");
+                    var action = valid ? "removeClass" : "addClass";
+                    error = valid ? false : {field: field, message: translate("Invalid_" + field)};
+                    container[action]("disabled");
+
                     this.model.set("SocialNetworksUrl", listeSocialNetwork);
                 }
-                
-                this.model.set(field, value, {validate: true});
+                else this.model.set(field, value, {validate: true});
             }
             //error_positioning
             if (pos !== "after" && pos !== "before") {
@@ -106,6 +112,7 @@
 
            
         },
+        
         onChange: function (model) {
             var changes=model.changedAttributes();
             var keyModel=model.KeySocialNetworks;
Index: src/js/JEditor/ParamsPanel/Views/SocialNetworks.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/SocialNetworks.js	(révision 10855)
+++ src/js/JEditor/ParamsPanel/Views/SocialNetworks.js	(révision 10856)
@@ -41,7 +41,10 @@
         },
         
         DeleteOne :function(key){
-            this.model.get("SocialNetworksUrl").splice(key,1)
+            var data = this.model.get("SocialNetworksUrl").splice(key,1);
+            // remettre à vide l'autre attribut direct dans le model params ex this.model.facebookUrl
+            // on note que ce model n'est pas enregistré dans la BD mais on le garde juste pour la validation
+            this.model.set(data.type , "");
             console.log(this.model.get("SocialNetworksUrl"));
             this.model.save();
             this.render();
