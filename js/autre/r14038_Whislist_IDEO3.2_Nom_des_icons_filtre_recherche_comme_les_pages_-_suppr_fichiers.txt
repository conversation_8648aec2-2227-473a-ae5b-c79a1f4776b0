Revision: r14038
Date: 2025-04-02 11:18:34 +0300 (lrb 02 Apr 2025) 
Author: rrakotoarinelina 

## Commit message
Whislist IDEO3.2 : Nom des icons + filtre recherche (comme les pages) - suppr fichiers apres refacto

## Files changed

## Full metadata
------------------------------------------------------------------------
r14038 | rrakotoarinelina | 2025-04-02 11:18:34 +0300 (lrb 02 Apr 2025) | 1 ligne
Chemins modifiés :
   D /branches/ideo3_v2/integration/src/js/JEditor/Commons/Basefile
   D /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Templates/socialSvgSelector.html
   D /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Views/SocialSvgSelectorDialog.js
   D /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/selectIcon.html
   D /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/SelectIconView.js
   D /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/Svg.js
   D /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/SvgCollection.js
   D /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/selectIcon.html
   D /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/SelectIconView.js
   D /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/Svg.js
   D /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/SvgCollection.js
   D /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/SelectIconView.js

Whislist IDEO3.2 : Nom des icons + filtre recherche (comme les pages) - suppr fichiers apres refacto
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Basefile/Models/SocialsCollection.js
===================================================================
--- src/js/JEditor/Commons/Basefile/Models/SocialsCollection.js	(révision 14037)
+++ src/js/JEditor/Commons/Basefile/Models/SocialsCollection.js	(nonexistent)
@@ -1,24 +0,0 @@
-define([
-    "JEditor/Commons/Ancestors/Models/DBCollection",
-    "JEditor/Commons/Basefile/Models/Svg"
-],function(
-	DBCollection,
-	Svg
-){
-var SocialsCollection = DBCollection.extend(
-    /**
-     * @lends SocialsCollection.prototype
-     */
-    {
-        model: Svg,
-        url:  __IDEO_API_PATH__ + "/resources-svgcollection/social",
-        /**
-         * initialize l'objet
-         */
-        initialize: function() {
-            this._super();
-        }
-
-    });
-return SocialsCollection;
-});
\ No newline at end of file
Index: src/js/JEditor/Commons/Basefile/Models/LabelsCollection.js
===================================================================
--- src/js/JEditor/Commons/Basefile/Models/LabelsCollection.js	(révision 14037)
+++ src/js/JEditor/Commons/Basefile/Models/LabelsCollection.js	(nonexistent)
@@ -1,39 +0,0 @@
-define([
-    "JEditor/Commons/Ancestors/Models/Collection",
-    "JEditor/Commons/Basefile/Models/Svg"
-],function(
-	Collection,
-	Svg
-){
-var LabelsCollection = Collection.extend(
-    /**
-     * @lends LabelsCollection.prototype
-     */
-    {
-        model: Svg,
-        url:  __IDEO_API_PATH__ + "/resources-svgcollection/labels",
-        
-        /**
-         * initialize l'objet
-         */
-        initialize: function() {
-        },
-        parse: function(response) {
-            return response.data; // assuming the array of files is in the 'data' property of the response
-        },
-
-        fetchIcons: function(callback){
-            this.fetch({
-                success: function(collection, response, options) {
-                    callback(null, response);
-                },
-                error: function(collection, response, options) {
-                    callback(new Error('Failed to fetch SVG'));
-                }
-            });
-        },
-
-
-    });
-return LabelsCollection;
-});
\ No newline at end of file

Property changes on: src/js/JEditor/Commons/Basefile/Models/LabelsCollection.js
___________________________________________________________________
Deleted: svn:executable
## -1 +0,0 ##
-*
\ No newline at end of property
Index: src/js/JEditor/Commons/Basefile/Models/Svg.js
===================================================================
--- src/js/JEditor/Commons/Basefile/Models/Svg.js	(révision 14037)
+++ src/js/JEditor/Commons/Basefile/Models/Svg.js	(nonexistent)
@@ -1,15 +0,0 @@
-define([
-    "JEditor/Commons/Ancestors/Models/Model",
-
-], function (Model) {
-
-    var Svg = Model.extend({
-        
-        defaults: {
-            'name': '',
-            'content': '',
-        },
-    });
-
-    return Svg;
-});

Property changes on: src/js/JEditor/Commons/Basefile/Models/Svg.js
___________________________________________________________________
Deleted: svn:executable
## -1 +0,0 ##
-*
\ No newline at end of property
Index: src/js/JEditor/Commons/Files/Templates/socialSvgSelector.html
===================================================================
--- src/js/JEditor/Commons/Files/Templates/socialSvgSelector.html	(révision 14037)
+++ src/js/JEditor/Commons/Files/Templates/socialSvgSelector.html	(nonexistent)
@@ -1,14 +0,0 @@
-<div class="container-icon option-content baseFile">
-    <div class="grid-container-icon content">
-        <%for(i=0;i<content.length;i++){
-            file = content[i]; %>
-            <div  class="oneFile <%=selected === file.name ? 'selected-icon':''%>" data-name="<%= file.name%>" data-id="<%=i%>" >
-                <span class="select"><span class="icon-check"></span></span>
-                <span class="wrapper-icon">
-                    <%= file.content%>
-                </span>
-            </div>
-            <%}%>
-    </div>
-</div>
-
Index: src/js/JEditor/Commons/Files/Views/SocialSvgSelectorDialog.js
===================================================================
--- src/js/JEditor/Commons/Files/Views/SocialSvgSelectorDialog.js	(révision 14037)
+++ src/js/JEditor/Commons/Files/Views/SocialSvgSelectorDialog.js	(nonexistent)
@@ -1,79 +0,0 @@
-define([
-    "jquery",
-    "underscore",
-    "text!../Templates/socialSvgSelector.html",
-    "JEditor/Commons/Events",
-    "JEditor/Commons/Ancestors/Views/DialogView",
-    "JEditor/ParamsPanel/Models/Params",
-    "JEditor/Commons/Basefile/Models/SocialsCollection",
-    "JEditor/Commons/Basefile/Models/Svg",
-    "i18n!../nls/i18n",
-    //not in params
-    "jqueryPlugins/dropdown"
-], function($, _, socialSvgSelector, Events, DialogView,Params,SocialsCollection,Svg, translate) {
-    var SocialSvgSelectorDialog = DialogView.extend({
-        className: 'socialSvgSelector',
-        events: {
-            'click .oneFile': 'onSelect',
-        },
-        currentList: [],
-        currentType : 'social',
-        constructor: function(options) {
-            var opts = _.extend({
-                title: translate('browseIconTitle'),
-                buttons: [
-                    {
-                        text: translate('choose'),
-                        class: 'okay',
-                        click: _.bind(this.onOk, this)
-                    },
-                    {
-                        text: translate('cancel'),
-                        class: 'cancel',
-                        click: _.bind(this.onCancel, this)
-                    }
-                ],
-                width: 720,
-                height: 600,
-                allowMultipleSelect: false
-            }, options);
-            return DialogView.call(this, opts);
-        },
-        initialize: function(options) {
-            this._super();
-            this._template = this.buildTemplate(socialSvgSelector, translate);
-            this.svgCollection = SocialsCollection.getInstance();
-            this.currentList = this.options.svgList;
-            this.render();
-        },
-        render: function() {
-            this.selected = {};
-            var data = (this.svgCollection === undefined ? [] : this.svgCollection);
-            this._super();
-            this.undelegateEvents();
-            this.$el.html(this._template({content:data.toJSON() , type:'social', selected: this.selected}));
-            this.dom[this.cid].selections = this.$('.oneFile');
-            this.dom[this.cid].type = this.$('.onglet.mycollections');
-            this.delegateEvents();
-            return this;
-        },
-        onCancel: function() {
-            $('.box-icon').css("background-color", "transparent");
-            this.selected = {};
-            this.$el.dialog('close');
-        },
-        onOk: function() {
-            this.trigger(Events.ListSocialNetworks.SELECT_ICON, this.selected);
-            this.$el.dialog('close');
-        },
-        onSelect: function(e) {
-            var $target = $(e.currentTarget), name = $target.data('name');
-            this.dom[this.cid].selections.removeClass('selected');
-            this.selected = this.svgCollection.findWhere({name:name});
-            $target.toggleClass('selected');
-            this.trigger(Events.ListViewEvents.SELECT,this.selected, this.selected?1:0,this.svgCollection);
-        },
-    
-    });
-    return SocialSvgSelectorDialog;
-});
Index: src/js/JEditor/FilePanel/Templates/selectIcon.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/selectIcon.html	(révision 14037)
+++ src/js/JEditor/FilePanel/Templates/selectIcon.html	(nonexistent)
@@ -1,30 +0,0 @@
-<div class="container-icon option-content baseFile">
-    <header class="popup-bar"> 
-        <a href="javascript:;" class="onglet mycollections active" data-switchto="labels">
-            Labels
-        </a>
-        <!-- <a href="javascript:;" class="onglet mycollections " data-switchto="LinkeoStock">
-            LinkeoStock
-        </a> -->
-   </header>
-    <div class="grid-container-icon content">
-        <%for(i=0;i<content.length;i++){
-            file = content[i];
-            %>
-            <%if (type === 'labels'){%>
-                <div  class="oneFile <%=selected === file.name ? 'selected-icon':''%>" data-name="<%= file.name%>" >
-                    <span class="select"><span class="icon-check"></span></span>
-                    <span class="wrapper-icon">
-                        <%= file.content%>
-                    </span>
-                </div>
-            <%} else {%>
-                <div  class="oneFile <%=selected === file.name ? 'selected-icon':''%>" style="background-image: url(<%=file.url%>);" data-url="<%=file.url%>" >
-                    <span class="select"><span class="icon-check"></span></span>
-                </div>
-           <%}%>
-           
-            <%}%>
-    </div>
-</div>
-

Property changes on: src/js/JEditor/FilePanel/Templates/selectIcon.html
___________________________________________________________________
Deleted: svn:executable
## -1 +0,0 ##
-*
\ No newline at end of property
Index: src/js/JEditor/FilePanel/Views/SelectIconView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/SelectIconView.js	(révision 14037)
+++ src/js/JEditor/FilePanel/Views/SelectIconView.js	(nonexistent)
@@ -1,113 +0,0 @@
-define([
-    "jquery",
-    "underscore",
-    "text!../Templates/selectIcon.html",
-    "JEditor/Commons/Events",
-    "JEditor/Commons/Ancestors/Views/DialogView",
-    "JEditor/ParamsPanel/Models/Params",
-    "JEditor/Commons/Basefile/Models/LabelsCollection",
-    "JEditor/Commons/Basefile/Models/Svg",
-    "i18n!../nls/i18n",
-    //not in params
-    "jqueryPlugins/dropdown"
-], function($, _, selectIcon, Events, DialogView,Params,LabelsCollection,Svg, translate) {
-    var SelectIconView = DialogView.extend({
-        className: 'selectIcon',
-        events: {
-            'click .oneFile': 'onSelect',
-            'click a[data-switchto]': '_switchListCollection',
-        },
-        currentList: [],
-        currentType : 'labels',
-        constructor: function(options) {
-            if (!options)
-            var options = {};
-            options.width = 750;
-            options.title = translate('browseIconTitle')
-            options.height = 600;
-            options.allowMultipleSelect = true;
-            options.buttons = [
-                {
-                    class: 'okay',
-                    text: translate("okay"),
-                    click: _.bind(this.onOk, this)
-                },
-                {
-                    text: translate("cancel"),
-                    class: 'cancel',
-                    click: _.bind(this.onCancel, this)
-                }
-            ]
-            DialogView.call(this, options);
-            this.on('open close', this.onToggle);
-        },
-        initialize: function(options) {
-            this._super();
-            this._template = this.buildTemplate(selectIcon, translate);
-            this.currentList = this.options.svgList;
-            this.render();
-        },
-        getIcons: function(){
-            var svgCollectionObj = new LabelsCollection();
-            svgCollectionObj.fetchIcons(_.bind(function(error, svgs) {
-                if (error) {
-                    console.error(error);
-                } else {
-                    this.svgCollection = svgs;
-                    this.trigger('data:fetched');
-                }
-            },this));
-
-            this.listenTo(this, 'data:fetched', this.render);
-        },
-        _switchListCollection : function(e){
-            var $target = $(e.currentTarget);
-            this.dom[this.cid].type.removeClass('active');
-            $target.addClass('active');
-            this.currentType = $target.data('switchto');
-        },
-        render: function() {
-            this.selected = {};
-            this.selectedLength = 0;
-            var data = (this.svgCollection === undefined ? [] : this.svgCollection);
-            this._super();
-            this.undelegateEvents();
-            this.$el.html(this._template({content:data , type:'labels', selected: this.selected}));
-            this.dom[this.cid].type = this.$('.onglet.mycollections');
-            this.delegateEvents();
-            return this;
-        },
-        onCancel: function() {
-            $('.box-icon').css("background-color", "transparent");
-            this.selected = {};
-            this.$el.dialog('close');
-        },
-        onOk: function() {
-            this.trigger(Events.ListViewEvents.SELECT_ICON, this.selected);
-            this.$el.dialog('close');
-        },
-        onSelect: function(e) {
-            var $target = $(e.currentTarget);
-            var name = $target.data('name');
-            var content = $target.find('.wrapper-icon').html().trim();
-            var type = 'svg';
-            var selected = {
-                'name':name,
-                'content': content,
-                'type' : type
-            }
-            if (this.selected.hasOwnProperty(name)) {
-                delete  this.selected[selected.name];
-                $target.removeClass('selected');
-                this.selectedLength--;
-            }
-            else {
-                this.selected[selected.name] = selected;
-                this.selectedLength++;
-                $target.toggleClass('selected');
-            }
-        },
-    
-    });
-    return SelectIconView;
-});

Property changes on: src/js/JEditor/FilePanel/Views/SelectIconView.js
___________________________________________________________________
Deleted: svn:executable
## -1 +0,0 ##
-*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/Svg.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/Svg.js	(révision 14037)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/Svg.js	(nonexistent)
@@ -1,33 +0,0 @@
-define([
-    "JEditor/Commons/Ancestors/Models/Model",
-
-], function (Model) {
-
-    var Svg = Model.extend({
-          /**
-         * @constructs
-         */
-        // constructor: function(options) {
-            
-        //     console.log('attributes');
-        //     console.log(options);
-        //     this.collectionName = 'outline';
-        //     this.svgName = '';
-        //     // console.log(PageCollection.caller);
-        //     if (options && options.collectionName && options.svgName){
-        //         this.collectionName = options.collectionName;
-        //         this.svgName = options.svgName;
-        //     }
-        // },
-        defaults: {
-            'name': '',
-            'content': ''
-        },
-        // url: function() {
-        //     //  __IDEO_API_PATH__ + "/resources-svgcollection/" + this.name;
-        //      return __IDEO_API_PATH__ + "/resources-svgcollection/" + this.collectionName +"/"+ this.svgName
-        // },
-    });
-
-    return Svg;
-});
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/SvgCollection.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/SvgCollection.js	(révision 14037)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/SvgCollection.js	(nonexistent)
@@ -1,59 +0,0 @@
-define([
-	"JEditor/Commons/Events",
-	"JEditor/Commons/Ancestors/Models/Collection",
-    "JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/Svg",
-    "JEditor/ParamsPanel/Models/Params",
-],function(
-    Events,
-	Collection,
-	Svg,
-    Params
-){
-var /**
- * 
- * @class SvgCollection
- * @extends Collection
- * @property {MapPoint} model Le type de modèle à créer
- */
-SvgCollection = Collection.extend(
-        /**
-         * @lends MapPointCollection.prototype
-         */
-                {
-                    model: Svg,
-                    constructor: function(options) {
-                        if(options && options.svgName !== ''){
-                            this.svgName = options.svgName;
-                        }else{
-                            this.svgName = '';
-                        }
-                    },
-                    /**
-                     * initialize l'objet
-                     */
-                    initialize: function() {
-                    },
-                    url: function() {
-                        return __IDEO_API_PATH__ + "/resources-svgcollection/" + this.name + ( this.svgName != '' ?  "/" + this.svgName :'' );
-                    },
-                    parse: function(response) {
-                        return response.data; // assuming the array of files is in the 'data' property of the response
-                    },
-
-                    fetchIcons: function(callback){
-                        this.params = Params.getInstance();
-                        this.name =  this.params.attributes.IconsCollection || 'outline';
-                        this.fetch({
-                            success: function(collection, response, options) {
-                                callback(null, response);
-                            },
-                            error: function(collection, response, options) {
-                                callback(new Error('Failed to fetch SVG'));
-                            }
-                        });
-                    },
-
-
-                });
-return SvgCollection;
-});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/selectIcon.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/selectIcon.html	(révision 14037)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/selectIcon.html	(nonexistent)
@@ -1,15 +0,0 @@
-
-    <div class="container-icon option-content">
-        <div class="grid-container-icon">
-            <%for(i=0;i<content.length;i++){
-                svg = content[i];
-                %>
-                <div  class="box-icon <%=selected === svg.name ? 'selected-icon':''%>" data-name="<%= svg.name%>"  >
-                        <span class="wrapper-icon">
-                            <%= svg.content%>
-                        </span>
-                </div>
-                <%}%>
-        </div>
-    </div>
-
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/SelectIconView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/SelectIconView.js	(révision 14037)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/SelectIconView.js	(nonexistent)
@@ -1,91 +0,0 @@
-define([
-    "jquery",
-    "underscore",
-    "text!../Templates/selectIcon.html",
-    "JEditor/Commons/Events",
-    "JEditor/Commons/Ancestors/Views/DialogView",
-    "JEditor/ParamsPanel/Models/Params",
-    "JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/SvgCollection",
-    "JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView",
-    "i18n!../nls/i18n",
-    //not in params
-    "jqueryPlugins/dropdown"
-], function($, _, selectIcon, Events, DialogView,Params,SvgCollection,ButtonStyleOptionView, translate) {
-    var SelectIconView = DialogView.extend({
-        className: 'selectIcon',
-        events: {
-            'click .box-icon': 'onSelect',
-            // 'click [data-select]': '_onSelectClick',
-        },
-        currentList: [],
-        constructor: function(options) {
-            var opts = _.extend({
-                title: translate("browseIconTitle"),
-                buttons: [
-                    {
-                        text: translate("choose"),
-                        class: 'okay',
-                        click: _.bind(this.onOk, this)
-                    },
-                    {
-                        text: translate("cancel"),
-                        class: 'cancel',
-                        click: _.bind(this.onCancel, this)
-                    }
-                ],
-                width: 720,
-                height: 600
-            }, options);
-            return DialogView.call(this, opts);
-        },
-        initialize: function(options) {
-            this._super();
-            this._template = this.buildTemplate(selectIcon, translate);
-
-        },
-        getIcons: function(usedIcon){
-            var svgCollectionObj = new SvgCollection();
-            svgCollectionObj.fetchIcons(_.bind(function(error, svgs) {
-                if (error) {
-                    console.error(error);
-                } else {
-                    this.svgCollection = svgs;
-                    this.selected = (usedIcon !== '' ? usedIcon : '');
-                    currentList = this.svgCollection;
-                    this.trigger('data:fetched');
-                }
-            },this));
-
-            this.listenTo(this, 'data:fetched', this.render);
-        },
-        render: function() {
-           
-            var data = (this.svgCollection === undefined ? [] : this.svgCollection);
-            this._super();
-            this.undelegateEvents();
-             this.$el.html(this._template({content:data, selected: this.selected}));
-            this.delegateEvents();
-            return this;
-        },
-        onCancel: function() {
-            $('.box-icon').css("background-color", "transparent");
-            this.$el.dialog('close');
-        },
-        onOk: function() {
-            var selectedIcon = this.selected;
-            var svgObj = this.svgCollection.filter(function(item) {
-                return item.name === selectedIcon;
-             })[0];
-            this.trigger(Events.ButtonEvents.SELECT_ICON, svgObj );
-            this.$el.dialog('close');
-        },
-        onSelect: function(e){
-            $('.box-icon').css("background-color", "transparent");
-            var $target = $(e.currentTarget);
-            $target.css('background-color', '#41ffbe');
-            var name = $target.data('name');
-            this.selected = name;
-        }
-    });
-    return SelectIconView;
-});
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/Svg.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/Svg.js	(révision 14037)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/Svg.js	(nonexistent)
@@ -1,13 +0,0 @@
-define([
-    "JEditor/Commons/Ancestors/Models/Model",
-
-], function (Model) {
-    var Svg = Model.extend({
-        defaults: {
-            'name': '',
-            'content': ''
-        },
-    });
-
-    return Svg;
-});
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/SvgCollection.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/SvgCollection.js	(révision 14037)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/SvgCollection.js	(nonexistent)
@@ -1,59 +0,0 @@
-define([
-	"JEditor/Commons/Events",
-	"JEditor/Commons/Ancestors/Models/Collection",
-    "JEditor/PagePanel/Contents/Blocks/CardBlock/Models/Svg",
-    "JEditor/ParamsPanel/Models/Params",
-],function(
-    Events,
-	Collection,
-	Svg,
-    Params
-){
-var /**
- * 
- * @class SvgCollection
- * @extends Collection
- * @property {MapPoint} model Le type de modèle à créer
- */
-SvgCollection = Collection.extend(
-        /**
-         * @lends MapPointCollection.prototype
-         */
-                {
-                    model: Svg,
-                    constructor: function(options) {
-                        if(options && options.svgName !== ''){
-                            this.svgName = options.svgName;
-                        }else{
-                            this.svgName = '';
-                        }
-                    },
-                    /**
-                     * initialize l'objet
-                     */
-                    initialize: function() {
-                    },
-                    url: function() {
-                        return __IDEO_API_PATH__ + "/resources-svgcollection/" + this.name + ( this.svgName != '' ?  "/" + this.svgName :'' );
-                    },
-                    parse: function(response) {
-                        return response.data; // assuming the array of files is in the 'data' property of the response
-                    },
-
-                    fetchIcons: function(callback){
-                        this.params = Params.getInstance();
-                        this.name =  this.params.attributes.IconsCollection || 'outline';
-                        this.fetch({
-                            success: function(collection, response, options) {
-                                callback(null, response);
-                            },
-                            error: function(collection, response, options) {
-                                callback(new Error('Failed to fetch SVG'));
-                            }
-                        });
-                    },
-
-
-                });
-return SvgCollection;
-});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/SelectIconView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/SelectIconView.js	(révision 14037)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/SelectIconView.js	(nonexistent)
@@ -1,90 +0,0 @@
-define([
-    "jquery",
-    "underscore",
-    "text!../Templates/selectIcon.html",
-    "JEditor/Commons/Events",
-    "JEditor/Commons/Ancestors/Views/DialogView",
-    "JEditor/ParamsPanel/Models/Params",
-    "JEditor/PagePanel/Contents/Blocks/CardBlock/Models/SvgCollection",
-    "i18n!../nls/i18n",
-    //not in params
-    "jqueryPlugins/dropdown"
-], function($, _, selectIcon, Events, DialogView,SvgCollection, translate) {
-    var SelectIconView = DialogView.extend({
-        className: 'selectIcon',
-        events: {
-            'click .box-icon': 'onSelect',
-            // 'click [data-select]': '_onSelectClick',
-        },
-        currentList: [],
-        constructor: function(options) {
-            var opts = _.extend({
-                title: translate("browseIconTitle"),
-                buttons: [
-                    {
-                        text: translate("choose"),
-                        class: 'okay',
-                        click: _.bind(this.onOk, this)
-                    },
-                    {
-                        text: translate("cancel"),
-                        class: 'cancel',
-                        click: _.bind(this.onCancel, this)
-                    }
-                ],
-                width: 720,
-                height: 600
-            }, options);
-            return DialogView.call(this, opts);
-        },
-        initialize: function(options) {
-            this._super();
-            this._template = this.buildTemplate(selectIcon, translate);
-
-        },
-        getIcons: function(usedIcon){
-            var svgCollectionObj = new SvgCollection();
-            svgCollectionObj.fetchIcons(_.bind(function(error, svgs) {
-                if (error) {
-                    console.error(error);
-                } else {
-                    this.svgCollection = svgs;
-                    this.selected = (usedIcon !== '' ? usedIcon : '');
-                    currentList = this.svgCollection;
-                    this.trigger('data:fetched');
-                }
-            },this));
-
-            this.listenTo(this, 'data:fetched', this.render);
-        },
-        render: function() {
-           
-            var data = (this.svgCollection === undefined ? [] : this.svgCollection);
-            this._super();
-            this.undelegateEvents();
-             this.$el.html(this._template({content:data, selected: this.selected}));
-            this.delegateEvents();
-            return this;
-        },
-        onCancel: function() {
-            $('.box-icon').css("background-color", "transparent");
-            this.$el.dialog('close');
-        },
-        onOk: function() {
-            var selectedIcon = this.selected;
-            var svgObj = this.svgCollection.filter(function(item) {
-                return item.name === selectedIcon;
-             })[0];
-            this.trigger(Events.ButtonEvents.SELECT_ICON, svgObj );
-            this.$el.dialog('close');
-        },
-        onSelect: function(e){
-            $('.box-icon').css("background-color", "transparent");
-            var $target = $(e.currentTarget);
-            $target.css('background-color', '#41ffbe');
-            var name = $target.data('name');
-            this.selected = name;
-        }
-    });
-    return SelectIconView;
-});
