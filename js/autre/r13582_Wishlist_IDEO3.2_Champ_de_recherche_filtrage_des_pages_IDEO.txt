Revision: r13582
Date: 2024-12-06 09:12:22 +0300 (zom 06 Des 2024) 
Author: frahajanirina 

## Commit message
Wishlist:IDEO3.2:Champ de recherche/filtrage des pages IDEO

## Files changed

## Full metadata
------------------------------------------------------------------------
r13582 | frahajanirina | 2024-12-06 09:12:22 +0300 (zom 06 Des 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Ancestors/Views/ListView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Pages/Templates/pageSelectorWrapper.html
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Pages/Views/PageSelectorWrapper.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Pages/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Pages/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Pages/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/PagePanel.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Templates/emptySupportPageList.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Templates/filterPage.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Views/FilterPageView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Views/PageCollectionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/pageSelector.less
   M /branches/ideo3_v2/integration/src/less/main.less

Wishlist:IDEO3.2:Champ de recherche/filtrage des pages IDEO
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Ancestors/Views/ListView.js
===================================================================
--- src/js/JEditor/Commons/Ancestors/Views/ListView.js	(révision 13581)
+++ src/js/JEditor/Commons/Ancestors/Views/ListView.js	(révision 13582)
@@ -258,11 +258,14 @@
             if (this.groupedBy !== null) {
                 this.$el.empty();
                 var groups = this.getGroupedList(list);
-                if(groups.content){
+                if(groups.content || this.options.supportCollection){
                     var byLang = this.options.supportCollection.groupBy('lang');
                     if(byLang[this.lang]){
                      var groupLPs = byLang[this.lang];
                      if(groupLPs.length > 0 && localStorage.getItem( 'lpaccess') == "true") groups.lp = groupLPs;
+                     if ((localStorage.getItem( 'lpaccess') == "true") && this.filteredBy !== null) {
+                        groups.lp  = this.getFilteredList(groupLPs);
+                     }
                     }
                 }
                 this.groupOrder= ['content', 'lp','template'];
Index: src/js/JEditor/Commons/Pages/Templates/pageSelectorWrapper.html
===================================================================
--- src/js/JEditor/Commons/Pages/Templates/pageSelectorWrapper.html	(révision 13581)
+++ src/js/JEditor/Commons/Pages/Templates/pageSelectorWrapper.html	(révision 13582)
@@ -14,5 +14,10 @@
             <input type="checkbox" id="<%=_id3%>" value="showNews" name="box"/><label for="<%=_id3%>" class="label"><span class="checkbox"><span class="icon-checked"></span><span class="icon-unchecked"></span></span><%= __("OnlyShowNews") %></label>
         <%}%>
     </header>
-    
+    <br>
+    <form id="form-page-selector">
+        <span class="search-page-selector">
+            <input type="text" class="search-input" placeholder="<%= __('filter')%>" value="<%= searchValue %>"/>
+        </span>
+    </form>
 </div>
\ No newline at end of file
Index: src/js/JEditor/Commons/Pages/Views/PageSelectorWrapper.js
===================================================================
--- src/js/JEditor/Commons/Pages/Views/PageSelectorWrapper.js	(révision 13581)
+++ src/js/JEditor/Commons/Pages/Views/PageSelectorWrapper.js	(révision 13582)
@@ -14,6 +14,7 @@
     var PageSelectorDialog = BabblerView.extend({
         events: {
             'change input[type="checkbox"]': '_toggleActive',
+            'input #form-page-selector input': 'search'
         },
         selected: null,
         initialize: function() {
@@ -26,6 +27,7 @@
             this._pagesSupportByLang = this.supportpagecollection.groupBy('lang');
             this.role=this.app.user.role;
             this.childViews = {};
+            this.searchValue = null;
            if (this.filePanel) {
                panel = this.filePanel;
                this.currentLang = panel.currentLang;
@@ -101,7 +103,7 @@
         },
         render: function() {
             var currentPageSupportList = this._pagesSupportByLang[this.currentLang.id] ? this._pagesSupportByLang[this.currentLang.id] : [];
-            this.$el.html(this._template({currentLang: this.currentLang,user:this.app.user, storage: (currentPageSupportList.length>0)?true:false}));
+            this.$el.html(this._template({currentLang: this.currentLang,user:this.app.user, storage: (currentPageSupportList.length>0)?true:false, searchValue: this.searchValue}));
             this.$('header').append(this.childViews.langDropDown.el);
             this.childViews.langDropDown.render()
             this.$('.dropdown-toggle').dropdown();
@@ -109,6 +111,55 @@
             this.dom[this.cid].title = this.$('.dialog-title');
             this.childViews.pageSelector.render();
             return this._super();
+        },
+        search: function(event) {
+            var inputValue = event.target.value;
+            var showhiddenpage = this.$('input[value="showhiddenpage"]').is(':checked');
+            
+            // type content
+            if (!this.$('input[type="checkbox"]').is(':checked')) {
+                if (inputValue.length == 0) {
+                    this.childViews.pageSelector.filter(function(value){
+
+                        return (value.active==true && value.type=="content" );
+                    });
+                } else {
+                    this.pageSelectorActiveSearch(inputValue, 'content');
+                }
+            } else if (showhiddenpage) {
+                this.pageSelectorSearch(inputValue);
+            }
+
+            // type news
+            else if (this.$('input[value="showNews"]').is(':checked')) {
+                this.pageSelectorActiveSearch(inputValue, 'news');
+            }
+            // type LPsupport
+            else {
+                this.pageSelectorActiveSearch(inputValue, 'LPsupport');
+            }
+		},
+        pageSelectorActiveSearch: function(keyword, type) {
+            this.childViews.pageSelector.filter(function(value){
+                if (value.type === type && value.active === true) {
+                    var pageName = value.name.trim().toLowerCase();
+                    var regex = new RegExp(keyword.trim().toLowerCase(), 'g');
+                    
+                    return regex.test(pageName);
+                }    
+            });
+            this.childViews.pageSelector.render(keyword);
+        },
+        pageSelectorSearch: function(keyword) {
+            this.childViews.pageSelector.filter(function(value){
+                if (value.type === 'content') {
+                    var pageName = value.name.trim().toLowerCase();
+                    var regex = new RegExp(keyword.trim().toLowerCase(), 'g');
+                    
+                    return regex.test(pageName);
+                }   
+            });
+            this.childViews.pageSelector.render(keyword);
         }
     });
     return PageSelectorDialog;
Index: src/js/JEditor/Commons/Pages/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/Commons/Pages/nls/fr-ca/i18n.js	(révision 13581)
+++ src/js/JEditor/Commons/Pages/nls/fr-ca/i18n.js	(révision 13582)
@@ -1 +1 @@
-define({"pageName":"Nom de la page","setAsHome":"Définir comme page d'accueil","homelessLang":"Attention, vous n'avez pas encore défini de page d'accueil pour cette langue","batchActions":"Traitement par lots","selectAll":"Tout sélectionner","selectNone":"Tout déselectionner","enable":"Activer","disable":"Désactiver","lock":"Vérouiller","unlock":"Dévérouiller","chooseLayout":"Choisir la disposition","duplicate":"Dupliquer","delete":"Supprimer","prevent404":"Cette page est désactivé, si vous ne l'activez pas, votre lien ne sera pas fontionnel, voulez-vous l'activer?","pageEnabled":"La page a été activée avec succès","pageDisabled":"Lapage n'a pas été activée","pages":"Pages","disabledDisplayed":"Afficher les pages désactivées","preview":"Aperçu","addContent":"Ajouter du contenu","selectPage":"Sélectionnez une page","ok":"Ok","cancel":"Annuler"});
\ No newline at end of file
+define({"pageName":"Nom de la page","setAsHome":"Définir comme page d'accueil","homelessLang":"Attention, vous n'avez pas encore défini de page d'accueil pour cette langue","batchActions":"Traitement par lots","selectAll":"Tout sélectionner","selectNone":"Tout déselectionner","enable":"Activer","disable":"Désactiver","lock":"Vérouiller","unlock":"Dévérouiller","chooseLayout":"Choisir la disposition","duplicate":"Dupliquer","delete":"Supprimer","prevent404":"Cette page est désactivé, si vous ne l'activez pas, votre lien ne sera pas fontionnel, voulez-vous l'activer?","pageEnabled":"La page a été activée avec succès","pageDisabled":"Lapage n'a pas été activée","pages":"Pages","disabledDisplayed":"Afficher les pages désactivées","preview":"Aperçu","addContent":"Ajouter du contenu","selectPage":"Sélectionnez une page","ok":"Ok","cancel":"Annuler", "filter": "Filtre ..."});
\ No newline at end of file
Index: src/js/JEditor/Commons/Pages/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/Commons/Pages/nls/fr-fr/i18n.js	(révision 13581)
+++ src/js/JEditor/Commons/Pages/nls/fr-fr/i18n.js	(révision 13582)
@@ -1 +1 @@
-define({"pageName":"Nom de la page","setAsHome":"Définir comme page d'accueil","homelessLang":"Attention, vous n'avez pas encore défini de page d'accueil pour cette langue","batchActions":"Traitement par lots","selectAll":"Tout sélectionner","selectNone":"Tout déselectionner","enable":"Activer","disable":"Désactiver","lock":"Vérouiller","unlock":"Dévérouiller","chooseLayout":"Choisir la disposition","duplicate":"Dupliquer","delete":"Supprimer","prevent404":"Cette page est désactivé, si vous ne l'activez pas, votre lien ne sera pas fontionnel, voulez-vous l'activer?","pageEnabled":"La page a été activée avec succès","pageDisabled":"Lapage n'a pas été activée","pages":"Pages","disabledDisplayed":"Afficher les pages désactivées","OnlyShowLPSupport":"Afficher uniquement les pages supports", "OnlyShowNews":"Afficher uniquement les pages actualités","preview":"Aperçu","addContent":"Ajouter du contenu","selectPage":"Sélectionnez une page","ok":"Ok","cancel":"Annuler"});
\ No newline at end of file
+define({"pageName":"Nom de la page","setAsHome":"Définir comme page d'accueil","homelessLang":"Attention, vous n'avez pas encore défini de page d'accueil pour cette langue","batchActions":"Traitement par lots","selectAll":"Tout sélectionner","selectNone":"Tout déselectionner","enable":"Activer","disable":"Désactiver","lock":"Vérouiller","unlock":"Dévérouiller","chooseLayout":"Choisir la disposition","duplicate":"Dupliquer","delete":"Supprimer","prevent404":"Cette page est désactivé, si vous ne l'activez pas, votre lien ne sera pas fontionnel, voulez-vous l'activer?","pageEnabled":"La page a été activée avec succès","pageDisabled":"Lapage n'a pas été activée","pages":"Pages","disabledDisplayed":"Afficher les pages désactivées","OnlyShowLPSupport":"Afficher uniquement les pages supports", "OnlyShowNews":"Afficher uniquement les pages actualités","preview":"Aperçu","addContent":"Ajouter du contenu","selectPage":"Sélectionnez une page","ok":"Ok","cancel":"Annuler", "filter": "Filtre ..."});
\ No newline at end of file
Index: src/js/JEditor/Commons/Pages/nls/i18n.js
===================================================================
--- src/js/JEditor/Commons/Pages/nls/i18n.js	(révision 13581)
+++ src/js/JEditor/Commons/Pages/nls/i18n.js	(révision 13582)
@@ -25,7 +25,8 @@
         "addContent":"Add content",
         "selectPage":"Select a page",
         "ok":"Ok",
-        "cancel":"Cancel"
+        "cancel":"Cancel",
+        "filter": "Filter ..."
     },
     "fr-fr":true, 
     "fr-ca":true 
Index: src/js/JEditor/PagePanel/PagePanel.js
===================================================================
--- src/js/JEditor/PagePanel/PagePanel.js	(révision 13581)
+++ src/js/JEditor/PagePanel/PagePanel.js	(révision 13582)
@@ -17,10 +17,11 @@
 		"collection!JEditor/PagePanel/ContentModel/ContentModelCollection",
         "JEditor/PagePanel/Contents/Zones/Versions/Views/VersionsCollectionView",
 		"JEditor/PagePanel/Views/PageLpManagerView",
+		"JEditor/PagePanel/Views/FilterPageView",
 		"i18n!./nls/i18n",
 		// not in params
 		"owlCarousel",
-		"jqueryPlugins/affix" ], function($, _, pagePanel, Events, PanelView, RightPanelView,  PageCollectionView, PageCollection, PageSupportCollection, PageListManagerView, AvailableView, AddPageView, LayoutCollection, PageView, LanguagesDropDown, ContentModelCollection, VersionsCollectionView, PageLpManagerView, translate) {
+		"jqueryPlugins/affix" ], function($, _, pagePanel, Events, PanelView, RightPanelView,  PageCollectionView, PageCollection, PageSupportCollection, PageListManagerView, AvailableView, AddPageView, LayoutCollection, PageView, LanguagesDropDown, ContentModelCollection, VersionsCollectionView, PageLpManagerView, FilterPageView, translate) {
 	 /**
 		 * not main Vue du Panneau de page, gère les vues de page (nom, etc),
 		 * langues, panneaux latéraux
@@ -35,6 +36,7 @@
 		 * @property {AddPageView} addpageView Dialogue d'ajout de page
 		 * @property {Commons.Pages.Views.PageView} pageView Vue de la page(au
 		 *           centre)
+		 * @property {FilterPageView} filterPage filtrer les pages
 		 */
 
 	var PagePanel = PanelView.extend(
@@ -45,7 +47,8 @@
 		events : {
 			'click #available-blocks-trigger' : 'showAvailableBlocks',
                         'click #show-zone-version': 'showZoneVersionsPanel',
-			'click .addPage,.empty-content-lang':'onAddPageClick'
+			'click .addPage,.empty-content-lang':'onAddPageClick',
+			'input #form-search-page input': 'searchPage'
 		},
 		cssFile: __IDEO_INTEGRATION_PATH__ + "/build/css/page_panel.css",
 
@@ -154,6 +157,7 @@
 			this.childViews.availableBlocksView = new AvailableView({
 				pagePanel : this
 			});
+			this.childViews.filterPage = new FilterPageView();
 
 			this.loadingEnd();
 		},
@@ -283,6 +287,7 @@
 				
 			
 			this.$('.options').append(this.childViews.langDropDown.render().el);
+			this.$('.options').append(this.childViews.filterPage.render().el);
 			this.$('aside').affix({
 				offset : {
 					top : this.checkAffixTop
@@ -292,6 +297,10 @@
 			this.dom.window.on('resize.pageDelegate', _.bind(this._scrollbar, this));
 			this.dom.window.scroll();
 		},
+		searchPage: function(event) {
+			var inputValue = event.target.value;
+			this.childViews.pageList.pageSearch(inputValue);
+		},
 		/**
 		 * crée le rendu du panneau de droite
                  * @param {object} content child view to render in the right panel
Index: src/js/JEditor/PagePanel/Templates/emptySupportPageList.html
===================================================================
Index: src/js/JEditor/PagePanel/Templates/filterPage.html
===================================================================
--- src/js/JEditor/PagePanel/Templates/filterPage.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Templates/filterPage.html	(révision 13582)
@@ -0,0 +1,6 @@
+<form id="form-search-page">
+    <span class="search-page-selector">
+        <input type="text" class="page-search" placeholder="<%= __('filter')%>" value="<%= value %>" />
+    </span>
+</form>
+<br>
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Views/FilterPageView.js
===================================================================
--- src/js/JEditor/PagePanel/Views/FilterPageView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Views/FilterPageView.js	(révision 13582)
@@ -0,0 +1,24 @@
+define([
+	"jquery",
+	"text!../Templates/filterPage.html",
+	"JEditor/Commons/Ancestors/Views/View",
+    "i18n!../nls/i18n"
+],
+function($, filterPage, View, translate){
+    var FilterPage = View.extend({
+        attributes: {
+            class: "filter-page"
+        },
+        initialize: function() {
+            this._super();
+            this.value = null;
+            this._template = this.buildTemplate(filterPage, translate);
+        },
+        render: function() {
+            this.$el.html(this._template({value: this.value}));
+
+            return this;
+        }
+    });
+    return FilterPage;
+});
Index: src/js/JEditor/PagePanel/Views/PageCollectionView.js
===================================================================
--- src/js/JEditor/PagePanel/Views/PageCollectionView.js	(révision 13581)
+++ src/js/JEditor/PagePanel/Views/PageCollectionView.js	(révision 13582)
@@ -6,6 +6,7 @@
   "JEditor/Commons/Events",
   "JEditor/Commons/Ancestors/Views/ListView",
   "JEditor/Commons/Utils",
+  "text!../Templates/emptySupportPageList.html",
   "i18n!../nls/i18n",
   //not in params
   "jqueryPlugins/dropdown"
@@ -15,6 +16,7 @@
   Events,
   ListView,
   Utils,
+  emptySupportPageList,
   translate) {
 
   var PageCollectionView = ListView.extend({
@@ -37,6 +39,7 @@
       this._super();
       this._template = this.buildTemplate(pageList, translate);
       this._groupTemplate = this.buildTemplate(pageList, translate);
+      this._emptyGroupTemplate = this.buildTemplate(emptySupportPageList, translate);
       this._current = null;
       this.groupBy('type');
       if(!this.app.user.can('access_layout'))
@@ -255,12 +258,44 @@
             break;
       }
     },
-    render: function() {
+    render: function(search) {
       this._super();
       this.hideNews();
+      if (search) {
+        // tout les pages typés
+        var listPage = ['home', 'contact', 'legal', 'plan', 'avis', 'newsletter'];
+        
+        // cacher les page typés s'il y a de mot clé  
+        listPage.forEach(function(list){
+          this.$('.' + list).hide();
+        });
+
+        var regex = new RegExp(search, 'g');
+
+        var anotherTypedPage = this.addTemplateParams(this.collection);
+
+        listPage.forEach(function(list) {
+            var page = anotherTypedPage[list];
+            if (page && page.name) {
+                var pageName = page.name.trim().toLowerCase();
+                if (regex.test(pageName)) {
+                  this.$('.' + list).show();
+                }
+            }
+        });
+      }
       this.scrollables();
       return this;
     },
+    pageSearch: function(keyword) {
+      this.filteredBy = function (item) {
+        var pageName = item.name.trim().toLowerCase();
+        var regex = new RegExp(keyword.trim().toLowerCase(), 'g');
+
+        return regex.test(pageName);
+      };
+      this.render(keyword);
+    },
     
     hideNews: function() {
       this.$('.wrapper.news').html('');
Index: src/js/JEditor/PagePanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/nls/fr-ca/i18n.js	(révision 13581)
+++ src/js/JEditor/PagePanel/nls/fr-ca/i18n.js	(révision 13582)
@@ -128,5 +128,6 @@
     "pagelp": "Pages supports",
     "showLpActions":"Afficher les pages supports",
     "landingPage" : "Landing page",
-    "urlText" : "Ouvrir la page 🡥"
+    "urlText" : "Ouvrir la page 🡥",
+    "filter": "Filtre ..."
 });
Index: src/js/JEditor/PagePanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/nls/fr-fr/i18n.js	(révision 13581)
+++ src/js/JEditor/PagePanel/nls/fr-fr/i18n.js	(révision 13582)
@@ -134,5 +134,6 @@
     "pagelp": "Pages supports",
     "showLpActions":"Afficher les pages supports",
     "landingPage" : "Landing page",
-    "urlText" : "Ouvrir la page 🡥"
+    "urlText" : "Ouvrir la page 🡥",
+    "filter": "Filtre ..."
 });
Index: src/js/JEditor/PagePanel/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/nls/i18n.js	(révision 13581)
+++ src/js/JEditor/PagePanel/nls/i18n.js	(révision 13582)
@@ -132,7 +132,8 @@
         "pagelp": " Support page",
         "showLpActions":"Show supports pages",
         "landingPage" : "Landing page",
-        "urlText" : "Open the webpage 🡥"
+        "urlText" : "Open the webpage 🡥",
+        "filter": "Filter ..."
     },
     "fr-fr": true, "fr-ca":true
 });
Index: src/less/imports/pageSelector.less
===================================================================
--- src/less/imports/pageSelector.less	(révision 13581)
+++ src/less/imports/pageSelector.less	(révision 13582)
@@ -171,4 +171,18 @@
     input[type="checkbox"]:checked+label>span.checkbox>span.icon-checked{
         color:@pageColor;
     }
+    .search-input {
+        width: 89%;
+        border: 1px solid #ccc;
+        border-radius: 4px;
+        background-image: url("data:image/svg+xml,%3Csvg width='16' height='16' viewBox='0 0 512 512' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' fill='%2334d399' class='icon-search01'%3E%3Cpath class='color1' d='M483.3,451.3l-97.7-97.7c29.6-35.7,47.4-81.5,47.4-131.4c0-113.6-92.4-206-206-206c-113.6,0-206,92.4-206,206 c0,113.6,92.4,206,206,206c45,0,86.7-14.5,120.6-39.1l98.9,98.9c5.1,5.1,11.7,7.6,18.4,7.6c6.7,0,13.3-2.5,18.4-7.6 C493.5,478,493.5,461.5,483.3,451.3z M73,222.3c0-84.9,69.1-154,154-154c84.9,0,154,69.1,154,154c0,84.9-69.1,154-154,154 C142.2,376.4,73,307.2,73,222.3z'/%3E%3C/svg%3E");
+        background-position: 10px 13px; 
+        background-repeat: no-repeat;
+        padding: 12px 20px 7px 30px;
+        margin-left: 25px;
+        color: #34d399;
+    }
+    .search-input::placeholder {
+        color: #34d399;
+    }
 }
\ No newline at end of file
Index: src/less/main.less
===================================================================
--- src/less/main.less	(révision 13581)
+++ src/less/main.less	(révision 13582)
@@ -2964,4 +2964,19 @@
 .card-panel-content {
   margin-top: 22px;
 }
+.page-search {
+  width: 59%;
+  border: 1px solid #ccc;
+  border-radius: 4px;
+  background-image: url("data:image/svg+xml,%3Csvg width='16' height='16' viewBox='0 0 512 512' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' fill='%23FFFFFF' class='icon-search01'%3E%3Cpath class='color1' d='M483.3,451.3l-97.7-97.7c29.6-35.7,47.4-81.5,47.4-131.4c0-113.6-92.4-206-206-206c-113.6,0-206,92.4-206,206 c0,113.6,92.4,206,206,206c45,0,86.7-14.5,120.6-39.1l98.9,98.9c5.1,5.1,11.7,7.6,18.4,7.6c6.7,0,13.3-2.5,18.4-7.6 C493.5,478,493.5,461.5,483.3,451.3z M73,222.3c0-84.9,69.1-154,154-154c84.9,0,154,69.1,154,154c0,84.9-69.1,154-154,154 C142.2,376.4,73,307.2,73,222.3z'/%3E%3C/svg%3E");
+  background-position: 10px 6px; 
+  background-repeat: no-repeat;
+  padding: 5px 20px 7px 30px;
+  margin-left: 35px;
+  background-color: #b2b2b2;
+  color: #fff;
+}
+.page-search::placeholder {
+  color: #fff;
+}
 
