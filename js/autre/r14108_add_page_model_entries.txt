Revision: r14108
Date: 2025-04-16 18:38:45 +0300 (lrb 16 Apr 2025) 
Author: mpartaux 

## Commit message
add page model entries

## Files changed

## Full metadata
------------------------------------------------------------------------
r14108 | mpartaux | 2025-04-16 18:38:45 +0300 (lrb 16 Apr 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/nls/i18n.js

add page model entries
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/nls/fr-ca/i18n.js	(révision 14107)
+++ src/js/JEditor/PagePanel/nls/fr-ca/i18n.js	(révision 14108)
@@ -108,6 +108,8 @@
 	"landingPage01SupportDesc":"Modèle LP Support",
     "landingPageSEA01Name":"Landing page SEA 01",
     "landingPageSEA01Desc":"Template Lp SEA",
+    "landingPageHome01Name":"Home Page 01",
+    "landingPageHome01Desc":"Modèle de page d'accueil classique",
     "successImportAction" : "Import effectuée avec succès",
     "failedImportAction": "Le modèle de page n'a pas pu être importé",
     "previousVersionSuccesful":"La zone a été restaurée avec succès et <br/>la page a été sauvegardée",
Index: src/js/JEditor/PagePanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/nls/fr-fr/i18n.js	(révision 14107)
+++ src/js/JEditor/PagePanel/nls/fr-fr/i18n.js	(révision 14108)
@@ -114,6 +114,8 @@
 	"landingPage01SupportDesc":"Modèle LP Support",
     "landingPageSEA01Name":"Landing page SEA 01",
     "landingPageSEA01Desc":"Template Lp SEA",
+    "landingPageHome01Name":"Home Page 01",
+    "landingPageHome01Desc":"Modèle de page d'accueil classique",
     "successImportAction" : "Import effectuée avec succès",
     "failedImportAction": "Le modèle de page n'a pas pu être importé",
     "previousVersionSuccesful":"La zone a été restaurée avec succès et <br/>la page a été sauvegardée",
Index: src/js/JEditor/PagePanel/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/nls/i18n.js	(révision 14107)
+++ src/js/JEditor/PagePanel/nls/i18n.js	(révision 14108)
@@ -114,6 +114,8 @@
 		"landingPage01SupportDesc":"LP Support",
         "landingPageSEA01Name":"Landing page SEA 01",
 		"landingPageSEA01Desc":"Lp SEA template",
+        "landingPageHome01Name":"Home Page 01",
+        "landingPageHome01Desc":"Classic landing page",
         "previousVersionSuccesful":"The content has been successfully restored and the page has been saved",
         "deleteContact": "Please set a different contact page <br\/> before you can delete it",
         "deleteLegale": "Please set a different Legal page <br\/> before you can delete it",
