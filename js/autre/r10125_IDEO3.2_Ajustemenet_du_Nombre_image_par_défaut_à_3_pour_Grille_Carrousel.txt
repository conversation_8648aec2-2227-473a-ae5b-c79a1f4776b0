Revision: r10125
Date: 2023-01-10 14:22:32 +0300 (tlt 10 Jan 2023) 
Author: norajaonarivelo 

## Commit message
IDEO3.2 Ajustemenet du Nombre image par défaut à 3 pour Grille&Carrousel

## Files changed

## Full metadata
------------------------------------------------------------------------
r10125 | norajaonarivelo | 2023-01-10 14:22:32 +0300 (tlt 10 Jan 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/CarrouselStyleOption.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Models/GridStyleOption.js

IDEO3.2 Ajustemenet du Nombre image par défaut à 3 pour Grille&Carrousel
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/CarrouselStyleOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/CarrouselStyleOption.js	(révision 10124)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/CarrouselStyleOption.js	(révision 10125)
@@ -17,7 +17,7 @@
                             theme: 'minimal', 
                             priority: 80,
                             FormatImage :   'landscape',
-                            NombreImage :   1,
+                            NombreImage :   3,
                             StyleAffichage  :1
                         },
                         validate: function(attributes, options) {
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Models/GridStyleOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Models/GridStyleOption.js	(révision 10124)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Models/GridStyleOption.js	(révision 10125)
@@ -21,7 +21,7 @@
                             priority: 80, 
                             grilleStyle :   0,
                             grilleFormat    :   'landscape',
-                            grilleNbreImage :   1,
+                            grilleNbreImage :   3,
                             grilleStyleAff  :   1,
                         },
                         
