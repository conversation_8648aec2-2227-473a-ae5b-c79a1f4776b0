Revision: r12425
Date: 2024-06-18 12:01:43 +0300 (tlt 18 Jon 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
changement su paramettre de route par le contentID

## Files changed

## Full metadata
------------------------------------------------------------------------
r12425 | s<PERSON><PERSON><PERSON><PERSON><PERSON> | 2024-06-18 12:01:43 +0300 (tlt 18 Jon 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/App/Router_save.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/App/routes/news.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/App/routes_build.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/App/routes_define.json

changement su paramettre de route par le contentID
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/App/routes/news.js
===================================================================
--- src/js/JEditor/App/routes/news.js	(révision 12424)
+++ src/js/JEditor/App/routes/news.js	(révision 12425)
@@ -1,5 +1,5 @@
 define(["JEditor/Commons/Events"], function (Events) {
-    return function (lang, type, objectID, zoneID) {
+    return function (lang, type, objectID, contentID) {
         require(["JEditor/NewsPanel/NewsPanel"], _.bind(function (NewsPanel) {
             if (!(this.app.currentPanel instanceof NewsPanel))
                 this.app.currentPanel = NewsPanel.getInstance();
Index: src/js/JEditor/App/Router_save.js
===================================================================
--- src/js/JEditor/App/Router_save.js	(révision 12424)
+++ src/js/JEditor/App/Router_save.js	(révision 12425)
@@ -14,7 +14,7 @@
             "social": "social",
             "storelocator": "storelocator",
              "*notFound": "notFound",
-            "news(/:lang)(/:type)(/:objectID)(/:zoneID)": "news",
+            "news(/:lang)(/:type)(/:objectID)(/:contentID)": "news",
         },
         constructor: function (attrs, options) {
             if (arguments.callee.caller !== Router.getInstance)
Index: src/js/JEditor/App/routes_build.js
===================================================================
--- src/js/JEditor/App/routes_build.js	(révision 12424)
+++ src/js/JEditor/App/routes_build.js	(révision 12425)
@@ -18,5 +18,5 @@
  "icom":"icom",
  "restaurant":"restaurant",
  "feedget":"feedget",
- "news":"news(/:lang)(/:type)(/:objectID)(/:zoneID)"
+ "news":"news(/:lang)(/:type)(/:objectID)(/:contentID)"
 });
Index: src/js/JEditor/App/routes_define.json
===================================================================
--- src/js/JEditor/App/routes_define.json	(révision 12424)
+++ src/js/JEditor/App/routes_define.json	(révision 12425)
@@ -18,5 +18,5 @@
   "icom":"icom",
   "restaurant":"restaurant",
   "feedget":"feedget",
-  "news": "news(/:lang)(/:type)(/:objectID)(/:zoneID)"
+  "news": "news(/:lang)(/:type)(/:objectID)(/:contentID)"
 }
