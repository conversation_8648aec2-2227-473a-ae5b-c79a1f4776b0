Revision: r13819
Date: 2025-02-12 10:04:56 +0300 (lrb 12 Feb 2025) 
Author: frahajanirina 

## Commit message
Wishilst IDEO 3.2: ajout de compteur pour les resultats et une phrase s'il n'y en a pas

## Files changed

## Full metadata
------------------------------------------------------------------------
r13819 | frahajanirina | 2025-02-12 10:04:56 +0300 (lrb 12 Feb 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Models/FileCollection.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileList.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileListView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileManagerUIView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/filePanel.less

Wishilst IDEO 3.2: ajout de compteur pour les resultats et une phrase s'il n'y en a pas
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/FilePanel/Models/FileCollection.js
===================================================================
--- src/js/JEditor/FilePanel/Models/FileCollection.js	(révision 13818)
+++ src/js/JEditor/FilePanel/Models/FileCollection.js	(révision 13819)
@@ -12,6 +12,7 @@
         countFile : 0,
         model:File,
         searchQuery: null,
+        emptyFile: false,
         // cette attribut pour verifier si on esat dans un detail de'un fichier
         detail:false,
         initialize: function (detail) {
@@ -30,8 +31,12 @@
                 this.hasmore=false;
                 
             this.countFile=0;
-            if(resp[0]){
+            if(resp[0] && resp[0].countFile){
                 this.countFile = resp[0].countFile;
+                this.emptyFile = false;
+            } else {
+                this.countFile = resp[0];
+                this.emptyFile = resp[1];
             }
             if(typeof resp === 'object' && resp !== null && !Array.isArray(resp)){
                 if(resp.countFile){
@@ -102,6 +107,9 @@
         },
         setName: function (name) {
             this.options.name = name;
+        },
+        setEmptyFile: function (emptyFile) {
+            this.emptyFile = emptyFile;
         }
     });
     return FileCollection;
Index: src/js/JEditor/FilePanel/Views/FileListView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileListView.js	(révision 13818)
+++ src/js/JEditor/FilePanel/Views/FileListView.js	(révision 13819)
@@ -238,6 +238,13 @@
                 this.collection.setName(searchValue);
                 this.collection.searchQuery = searchValue;
                 this.collection.setOffset(0);
+                this.listenToOnce(this.collection, 'sync', function() {
+                    if (this.collection.emptyFile) {
+                        $('.fileList .filtered-list').empty().append('<p class="no-result">'+translate('noResult')+'</p>');
+                    } else {
+                        $('.filtered-list .search-result').removeClass('hidden');
+                    }
+                });
 
                 this.collection.fetch({ remove: true });
             }
@@ -247,6 +254,7 @@
             this.collection.setName(null);
             this.collection.searchQuery = null;
             this.collection.setOffset(0);
+            this.collection.setEmptyFile(false);
 
             this.collection.fetch({ remove: true });
         },
@@ -265,6 +273,7 @@
         this.collection.setOffset(0);
         this.collection.setName(null);
         this.collection.searchQuery = null;
+        this.collection.setEmptyFile(false);
         this.collection.fetch({remove: true});
         },
         onlyImagesNews : function() {
@@ -272,6 +281,7 @@
             this.collection.setOffset(0);
             this.collection.setName(null);
             this.collection.searchQuery = null;
+            this.collection.setEmptyFile(false);
             this.collection.fetch({remove: true});
         },
         resetFilter :function(){
@@ -279,6 +289,7 @@
             this.collection.setOffset(0);
             this.collection.setName(null);
             this.collection.searchQuery = null;
+            this.collection.setEmptyFile(false);
             this.collection.fetch({remove: true});
         },
         noImage: function() {
@@ -287,6 +298,7 @@
             this.collection.setOffset(0);
             this.collection.setName(null);
             this.collection.searchQuery = null;
+            this.collection.setEmptyFile(false);
             this.collection.fetch({remove: true});
         },
         loadMore: function(){
Index: src/js/JEditor/FilePanel/Views/FileManagerUIView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileManagerUIView.js	(révision 13818)
+++ src/js/JEditor/FilePanel/Views/FileManagerUIView.js	(révision 13819)
@@ -45,12 +45,17 @@
                     this.on("UPDATECOUNT",this.updateCount);
                 },
                 updateCount: function() {
-                    var fileLength=this.collection.length;
-                    if(fileLength >0){
+                    var fileLength=this.collection.length, isEmpty = this.collection.emptyFile;
+                    if (isEmpty) {
                         this.$('.countFile').show();
-                        this.$('.countFile span').html("<strong>"+fileLength+"</strong><span> "+translate("FileOn")+" "+this.collection.countFile+" </span>");
-                    }else{
-                        this.$('.countFile').hide();
+                        this.$('.countFile span').html("<strong>0</strong><span> "+translate("FileOn")+" "+this.collection.countFile+" </span>");
+                    } else {
+                        if(fileLength >0){
+                            this.$('.countFile').show();
+                            this.$('.countFile span').html("<strong>"+fileLength+"</strong><span> "+translate("FileOn")+" "+this.collection.countFile+" </span>");
+                        }else{
+                            this.$('.countFile').hide();
+                        }   
                     }
                 },
                 _onSortByClick: function(event) {
Index: src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 13818)
+++ src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 13819)
@@ -135,5 +135,6 @@
     "mimeNotsupporte": "non pris en charge. Échec de la création d'une ressource",
     "NewsFiles" :"Entêtes articles",
     "searchFile" : "Rechercher une image, un fichier ...",
-    "noResult": "Aucun résultat trouvé."
+    "noResult": "Aucun résultat trouvé.",
+    "result": "résultats trouvés."
 });
\ No newline at end of file
Index: src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 13818)
+++ src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 13819)
@@ -139,5 +139,6 @@
     "mimeNotsupporte": "non pris en charge. Échec de la création d'une ressource",
     "NewsFiles" :"Entêtes articles",
     "searchFile" : "Rechercher une image, un fichier ...",
-    "noResult": "Aucun résultat trouvé."
+    "noResult": "Aucun résultat trouvé.",
+    "result": "résultats trouvés."
 });
\ No newline at end of file
Index: src/js/JEditor/FilePanel/nls/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/i18n.js	(révision 13818)
+++ src/js/JEditor/FilePanel/nls/i18n.js	(révision 13819)
@@ -141,7 +141,8 @@
         "mimeNotsupporte":"not supported. Create resource failed",
         "NewsFiles" :"Article headers",
         "searchFile" : "Search for an image, file ...",
-        "noResult": "No results found."
+        "noResult": "No results found.",
+        "result": "results found."
     },
     "fr-fr": true, "fr-ca":true
 })
\ No newline at end of file
Index: src/js/JEditor/FilePanel/Templates/fileList.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileList.html	(révision 13818)
+++ src/js/JEditor/FilePanel/Templates/fileList.html	(révision 13819)
@@ -36,7 +36,8 @@
     </span>
 </div>
 <div class="my-files fileList">
-    <div class="content scroll-container">
+    <div class="content scroll-container filtered-list">
+            <p class="search-result hidden"><%=content.length%> <%= __("result")%></p>
             <% for(var i=0; i< content.length; i++){
             var file = content[i];
             %>
Index: src/less/imports/filePanel.less
===================================================================
--- src/less/imports/filePanel.less	(révision 13818)
+++ src/less/imports/filePanel.less	(révision 13819)
@@ -994,6 +994,16 @@
                 right: 0;
             }
         }
+    }
+    .filtered-list {
+        text-align: center;
+        color: #666;
+        .no-result {
+            margin-top: 100px;
+        }
+        .search-result {
+            margin-top: 20px;
+        }
     }   
 }
 .container-search {
