Revision: r11135
Date: 2023-07-06 14:07:55 +0300 (lkm 06 Jol 2023) 
Author: norajaonarive<PERSON> 

## Commit message
Wishlist IDEO3.2: Ajout image à une collection depuis la vue détails

## Files changed

## Full metadata
------------------------------------------------------------------------
r11135 | norajaonarivelo | 2023-07-06 14:07:55 +0300 (lkm 06 Jol 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/FilePanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileDetailManager.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileDetailManagerView.js
   M /branches/ideo3_v2/integration/src/less/imports/filePanel.less

Wishlist IDEO3.2: Ajout image à une collection depuis la vue détails
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/FilePanel/Templates/fileDetailManager.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileDetailManager.html	(révision 11134)
+++ src/js/JEditor/FilePanel/Templates/fileDetailManager.html	(révision 11135)
@@ -5,27 +5,31 @@
     </a>
 
     <div class="right-actions">
+        <a href="#" class="file-action" data-action="addSelectionToNewCollection">
+            <span class="icon-new-collection"></span>
+            <span class="infobulles"><%= __("createNewCollection")%></span>
+        </a>
+        <a href="#" class="file-action" data-action="selectCollection">
+            <span class="icon-gallery"></span>
+            <span class="infobulles"><%= __("addToExistingCollection")%></span>
+        </a>
         <% if(isImg){%>
         <a href="#" data-action="focus" data-id="<%=id%>">
             <span class="icon-interest-point"></span>
-            <%= __("editFocusAction")%>
             <span class="infobulles"><%= __("editFocus")%></span>
         </a>
         <a href="#" data-action="crop" data-id="<%=id%>">
             <span class="icon-crop"></span>
-            <%= __("editImageAction")%>
             <span class="infobulles"><%= __("editImage")%></span>
         </a>
         <%}%>
         <a href="<%=fileUrl%>" download="<%=name%>">
             <span class="icon-download"></span>
-            <%= __("Download")%>
             <span class="infobulles"><%= __("getFile")%></span>
         </a>
         <% if(user.can("delete_file")){ %>
         <a href="#" data-delete="<%=cid%>">
             <span class="icon-bin"></span>
-            <%= __("delete")%>
             <span class="infobulles"><%= __("deleteFile")%></span>
         </a>
         <% } %>
Index: src/js/JEditor/FilePanel/Views/FileDetailManagerView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileDetailManagerView.js	(révision 11134)
+++ src/js/JEditor/FilePanel/Views/FileDetailManagerView.js	(révision 11135)
@@ -7,6 +7,7 @@
 	"JEditor/Commons/Ancestors/Views/BabblerView",
     "JEditor/FilePanel/Views/ImageEditView",
 	"JEditor/FilePanel/Views/FocusEditView",
+    "JEditor/FilePanel/Views/SelectCollectionView",
 	"JEditor/Commons/Utils"
 ,
 	"i18n!../nls/i18n"],function(	$,
@@ -17,6 +18,7 @@
 	BabblerView,
     ImageEditView,
 	FocusEditView,
+    SelectCollectionView,
 	Utils
 ,
 	translate){
@@ -30,16 +32,36 @@
         'uploadercomplete a[data-replace]': 'uploadercomplete',
         'click a[data-replace]': 'showUploader',
         'click a[data-action="focus"]': 'editFocusPoint',
+        'click a[data-action="addSelectionToNewCollection"]' :'addSelectionToNewCollection'
     },
     MyFile:null,
     initialize: function() {
         this._super();
         this._template = this.buildTemplate(fileDetailManager,translate);
+
+        this.selectCollectionView = new SelectCollectionView({collection: this.options.childViews.fileGroupList});
+        this.listenTo(this.selectCollectionView, Events.ListManagerViewEvents.ADD_TO_COLLECTION, this.addSelectionToExistingCollection);    
     },
     goBack: function() {
         this.trigger(Events.FileDetailManagerViewEvents.BACK, this.referrer);
         return false;
     },
+    addSelectionToNewCollection : function(){
+        if (this.FileDetailView.model) {
+            var arrFiles = [];
+                arrFiles.push(this.FileDetailView.model);
+            this.trigger(Events.ListManagerViewEvents.ADD_COLLECTION, arrFiles);
+        }
+        return false;
+    },
+    addSelectionToExistingCollection: function(collection) {
+        if (this.FileDetailView.model) {
+            var arrFiles = [];
+                arrFiles.push(this.FileDetailView.model);
+            this.trigger(Events.ListManagerViewEvents.ADD_TO_COLLECTION, collection, arrFiles);
+        }
+        return false;
+    },
     render: function() {
         this.undelegateEvents();
         this._super();
@@ -56,6 +78,9 @@
             if(!file && this.MyFile)
                 file = this.MyFile;
         }
+        this.dom[this.cid].selectCollection = this.$('[data-action="selectCollection"]');
+        this.selectCollectionView.attach(this.dom[this.cid].selectCollection);
+
         this.ImageEditView = new ImageEditView({model: file});
         this.ImageEditView.attach(this.$('[data-action="crop"]'));
         this.listenToOnce(this.ImageEditView, Events.ImageEditorEvents.SAVE, this._onImageEdited);
Index: src/less/imports/filePanel.less
===================================================================
--- src/less/imports/filePanel.less	(révision 11134)
+++ src/less/imports/filePanel.less	(révision 11135)
@@ -2140,7 +2140,7 @@
 
         display:block;
         position: relative;
-        width: 115px; height: 30px;
+        width: 40px; height: 30px;
         float: left;
         margin: 20px 5px;
 
@@ -2160,11 +2160,13 @@
 
         & .icon-asterix { float: left; margin: 7px 0 0 10px }
         & .icon-hexagon { float: left; margin: 7px 0 0 10px }
-        & .icon-interest-point { float: left; margin: 7px 0 0 6px }
+        & .icon-interest-point { float: left; margin: 7px 0 0 13px }
         & .icon-crop { float: left; margin: 7px 0 0 10px }
         & .icon-download { float: left; margin: 6px 0 0 10px }
         & .icon-refresh { float: left; margin: 6px 0 0 10px }
         & .icon-bin { float: left; margin: 6px 0 0 10px }
+        & .icon-new-collection { float: left; margin: 6px 0 0 10px }
+        & .icon-gallery { float: left; margin: 6px 0 0 10px }
         & .message-wrapper{
             display:none;
         }
@@ -2172,14 +2174,14 @@
     /* Infobulles Mes filtres */
     & .right-actions a .infobulles{
         position: absolute;
-        left: -20%; top: 40px;
+        left: -135%; top: 40px;
 
         display: block;
         display: none;
         min-width: 130px;
-        height: 20px;
-        padding: 0 5px;
-        line-height: 20px;
+        height: 28px;
+        padding: 5px;
+        line-height: 14px;
         text-align: center;
         color: #FFF;
         background-color: #f27230;
Index: src/js/JEditor/FilePanel/FilePanel.js
===================================================================
--- src/js/JEditor/FilePanel/FilePanel.js	(révision 11134)
+++ src/js/JEditor/FilePanel/FilePanel.js	(révision 11135)
@@ -109,7 +109,7 @@
                             this.childViews.fileManagerUIView = new FileManagerUIView({collection: this.fileList, listView: this.childViews.fileListView, fileGroupList: this.fileGroupList});
                             this.childViews.collectionListView = new CollectionListView({collection: this.fileGroupList});
                             this.childViews.collectionManagerUIView = new CollectionManagerUIView({collection: this.fileGroupList, listView: this.childViews.collectionListView, parentCollection: false});
-                            this.childViews.fileDetailManagerView = new FileDetailManagerView({childViews: {fileList: this.childViews.fileListView, fileGroupList: this.childViews.collectionListView}});
+                            this.childViews.fileDetailManagerView = new FileDetailManagerView({childViews: {fileList: this.childViews.fileListView, fileGroupList: this.fileGroupList}});
                             this.listenTo(this.childViews.collectionListView, Events.ListViewEvents.DETAIL_COLLECTION, this.goToCollection);
                             this.listenTo(this.childViews.collectionListView, Events.ListManagerViewEvents.ADD_COLLECTION, this.addCollection);
                             this.listenTo(this.childViews.collectionListView, Events.ListViewEvents.FILE_CLEARVIEW, this._deleteOneDetailView);
@@ -122,6 +122,8 @@
                             this.listenTo(this.childViews.fileManagerUIView, Events.ListViewEvents.FILE_CHECKCOLLECTION_ONDELETE, this._checkCollectionOnFileDelete);
                             this.listenTo(this.childViews.fileDetailManagerView, Events.FileDetailManagerViewEvents.BACK, this.goBack);
                             this.listenTo(this.childViews.fileDetailManagerView, Events.FileDetailManagerViewEvents.EDIT, this.onEdit);
+                            this.listenTo(this.childViews.fileDetailManagerView, Events.ListManagerViewEvents.ADD_COLLECTION, this.addCollection);
+                            this.listenTo(this.childViews.fileDetailManagerView, Events.ListManagerViewEvents.ADD_TO_COLLECTION, this.addSelectionToCollection);
                             this.listenTo(this.childViews.fileDetailManagerView, Events.ListViewEvents.FILE_CLEARVIEW, this._deleteOneDetailView);
                             this.listenTo(this.childViews.collectionManagerUIView, Events.ListViewEvents.FILE_CLEARVIEW, this._deleteOneDetailView);
                             this.listenTo(this.childViews.collectionManagerUIView, Events.ListViewEvents.DETAIL_COLLECTION, this.goToCollection);
