Revision: r13892
Date: 2025-02-20 14:55:25 +0300 (lkm 20 Feb 2025) 
Author: rrakotoarinelina 

## Commit message
Whislist IDEO3.2 : Definir des cta - partie JS - CTA tab accessible seulement par root  + correction css

## Files changed

## Full metadata
------------------------------------------------------------------------
r13892 | rrakotoarinelina | 2025-02-20 14:55:25 +0300 (lkm 20 Feb 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/assets/ACLs/admin.json
   M /branches/ideo3_v2/integration/assets/ACLs/lpadmin.json
   M /branches/ideo3_v2/integration/assets/ACLs/root.json
   M /branches/ideo3_v2/integration/assets/ACLs/superadmin.json
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js
   M /branches/ideo3_v2/integration/src/less/imports/button_block/styleOptions.less

Whislist IDEO3.2 : Definir des cta - partie JS - CTA tab accessible seulement par root  + correction css
------------------------------------------------------------------------

## Diff
Index: assets/ACLs/admin.json
===================================================================
--- assets/ACLs/admin.json	(révision 13891)
+++ assets/ACLs/admin.json	(révision 13892)
@@ -218,5 +218,9 @@
   "access_image_compression": {
     "value": false,
     "comparison": null
- }
+ },
+ "access_cta_tab": {
+   "value": false,
+   "comparison": null
 }
+}
Index: assets/ACLs/lpadmin.json
===================================================================
--- assets/ACLs/lpadmin.json	(révision 13891)
+++ assets/ACLs/lpadmin.json	(révision 13892)
@@ -218,6 +218,10 @@
      "access_image_compression": {
         "value": false,
         "comparison": null
+     },
+     "access_cta_tab": {
+        "value": false,
+        "comparison": null
      }
    }
    
\ No newline at end of file
Index: assets/ACLs/root.json
===================================================================
--- assets/ACLs/root.json	(révision 13891)
+++ assets/ACLs/root.json	(révision 13892)
@@ -218,5 +218,9 @@
      "access_image_compression": {
         "value": true,
         "comparison": null
+     },
+     "access_cta_tab": {
+        "value": true,
+        "comparison": null
      }
 }
Index: assets/ACLs/superadmin.json
===================================================================
--- assets/ACLs/superadmin.json	(révision 13891)
+++ assets/ACLs/superadmin.json	(révision 13892)
@@ -218,5 +218,9 @@
   "access_image_compression": {
    "value": false,
    "comparison": null
+},
+"access_cta_tab": {
+   "value": false,
+   "comparison": null
 }
 }
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js	(révision 13891)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js	(révision 13892)
@@ -205,7 +205,9 @@
                             });
                             buttonOptionsView.addPane(translate("ButtonOption"), contentOptions);
                             buttonOptionsView.addPane(translate("ButtonStyleOption"), buttonStylesOptionView);
-                            buttonOptionsView.addPane(translate("CTATitle"), this.buttonCTAOptionView);
+                            if(this.app.user.can('access_cta_tab')) {
+                                buttonOptionsView.addPane(translate("CTATitle"), this.buttonCTAOptionView);
+                            }
                             buttonOptionsView.addPane(translate("advancedCSS"), advancedView);
                             buttonOptionsView.setTitle(translate("buttonBlockOption"));
                             
Index: src/less/imports/button_block/styleOptions.less
===================================================================
--- src/less/imports/button_block/styleOptions.less	(révision 13891)
+++ src/less/imports/button_block/styleOptions.less	(révision 13892)
@@ -355,3 +355,8 @@
         }
     }
 }
+
+
+.simple-view.panel-content.view {
+    padding-right: 16px;
+}
\ No newline at end of file
