Revision: r11175
Date: 2023-08-01 10:01:38 +0300 (tlt 01 Aog 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2 : supprimer les scrollbar JS(liste des pages)

## Files changed

## Full metadata
------------------------------------------------------------------------
r11175 | srazanandralisoa | 2023-08-01 10:01:38 +0300 (tlt 01 Aog 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Views/PageCollectionView.js
   M /branches/ideo3_v2/integration/src/less/main.less

wishlist IDEO3.2 : supprimer les scrollbar JS(liste des pages)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Views/PageCollectionView.js
===================================================================
--- src/js/JEditor/PagePanel/Views/PageCollectionView.js	(révision 11174)
+++ src/js/JEditor/PagePanel/Views/PageCollectionView.js	(révision 11175)
@@ -19,7 +19,7 @@
 
   var PageCollectionView = ListView.extend({
     attributes: {
-      class: 'pageList scroll-container'
+      class: 'pageList scroll-container scrollbar-classic'
     },
     events: {
       'click ul li a[data-cid]': '_onPageClick',
@@ -257,7 +257,7 @@
     },
     render: function() {
       this._super();
-      this.scrollables();
+      // this.scrollables();
       return this;
     },
     show: function(animate) {
Index: src/less/main.less
===================================================================
--- src/less/main.less	(révision 11174)
+++ src/less/main.less	(révision 11175)
@@ -792,6 +792,9 @@
   }
 
 }
+.scrollbar-classic{
+  overflow: auto;
+ }
 .pageList, .add-page-dialog {
   .mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
     background: rgba(0, 0, 0, 0.4);
