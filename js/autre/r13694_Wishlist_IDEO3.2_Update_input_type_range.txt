Revision: r13694
Date: 2025-01-14 13:36:32 +0300 (tlt 14 Jan 2025) 
Author: frahajanirina 

## Commit message
Wishlist:IDEO3.2:Update <input type="range">

## Files changed

## Full metadata
------------------------------------------------------------------------
r13694 | frahajanirina | 2025-01-14 13:36:32 +0300 (tlt 14 Jan 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardStyleOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardStyleOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Templates/slideshowOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Views/SlideshowOptionView.js

Wishlist:IDEO3.2:Update <input type="range">
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardStyleOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardStyleOption.html	(révision 13693)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardStyleOption.html	(révision 13694)
@@ -108,10 +108,15 @@
         </header>
         <div class="option-content">
             <div class="slider-container">
-                <span class="icon-less"></span>
-                <div class="slider"></div>
-                <span class="icon-more"></span>
-            </div>
+                <input type="range" id="numbPictures" name="numbPictures" list="numbPictures-list" min="1" max="5" value="3">
+                <datalist id="numbPictures-list">
+                  <option value="" label="1"></option>
+                  <option value="split2" label="2"></option>
+                  <option value="split3" label="3"></option>
+                  <option value="split4" label="4"></option>
+                  <option value="split5" label="5"></option>
+                </datalist>
+              </div>
         </div>
     </article>
 </div>
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardStyleOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardStyleOptionView.js	(révision 13693)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardStyleOptionView.js	(révision 13694)
@@ -20,7 +20,7 @@
             className: "gallery-template-option galleryStyle panel-content",
             events: {
                 'click .stylleDeCard div .effect-radio'   : '_onChangeStylleImage',
-                'slidechange .card-nbreImage .slider'   :   '_onSliderChangeNbreCard',
+                'change .card-nbreImage #numbPictures'   :   '_onChangeNbreCard',
                 'change input[type="radio"].select-box': '_onStyleAffichageChange',
                 'click input[type=radio].card-arrow':'_onChangeRadio',
                 'change input[name="Autoplay"]': '_onChangeAutoplay',
@@ -64,13 +64,7 @@
                     advanced:{ autoScrollOnFocus: false }
                 });
                 this.delegateEvents();
-                this.$('.card-nbreImage .slider').slider({
-                    range: "min",
-                    value: this.model.cardNbreCard,
-                    min: 1,
-                    max: 5,
-                    step: 1
-                });
+                $('.card-nbreImage #numbPictures').val(this.model.cardNbreCard);
 
                 return this;
             },
@@ -96,11 +90,8 @@
             /**
             * Slider change
             */
-            _onSliderChangeNbreCard: function(event, ui){
-                var value = ui.value;
-                this.model.cardNbreCard=value;
-
-                return false;
+            _onChangeNbreCard: function(event){
+                this.model.cardNbreCard = parseInt($(event.currentTarget).val());
             },
             _onStyleAffichageChange :function(event){
                 var $target = $(event.currentTarget);
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Templates/slideshowOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Templates/slideshowOption.html	(révision 13693)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Templates/slideshowOption.html	(révision 13694)
@@ -59,7 +59,15 @@
                   <h3 class="option-name"><%= __("delayText")%></h3>
               </header>
               <div class="option-content">
-                  <div class="slider-container"><span class="icon-less"></span><div class="slider"></div><span class="icon-more"></span></div>
+                <div class="slider-container">
+                  <input type="range" id="carouselDelay" name="carouselDelay" list="carouselDelay-list" min="1" max="4" value="1">
+                  <datalist id="carouselDelay-list">
+                    <option value="3000" label="3 sec"></option>
+                    <option value="5000" label="5 sec"></option>
+                    <option value="7000" label="7 sec"></option>
+                    <option value="10000" label="10 sec"></option>
+                  </datalist>
+                </div>
               </div>
           </article>
       </div>
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Views/SlideshowOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Views/SlideshowOptionView.js	(révision 13693)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Views/SlideshowOptionView.js	(révision 13694)
@@ -21,7 +21,7 @@
         var SlideshowOptionView = AbstractOptionView.extend({
             optionType: 'slideshow',
             events: {
-                'slidechange .slider'   :   'onSliderChange',
+                'change .slideshow-delay #carouselDelay': '_onDelayChange',
                 'click .files-action'   :   'openFileGroupDialog',
                 'click input[type=radio]':'_onChangeRadio',
                 'change input[type="checkbox"].blue-bg': '_onChangeArrow'
@@ -79,17 +79,7 @@
                 this.$el.empty();
                 this.$el.html(this._template(this.model));
 
-                // slide 
-                var slider = this.$('.slideshow-delay .slider');
-                slider.slider({
-                                min: 1,
-                                max: 4,
-                                step: 1,
-                                value:this.model.Delay,
-                                range:"min"
-                            });
-                this.dom[this.cid].slider = slider;
-                // slide 
+                $('.slideshow-delay #carouselDelay').val(this.model.Delay);
 
                 this.scrollables({
                     advanced:{ autoScrollOnFocus: false }
@@ -106,10 +96,8 @@
                     this.model.Info= event.currentTarget.value;
                 }
             },
-            onSliderChange: function(event,ui){
-                var value = ui.value;
-                this.model.Delay=value;
-                return false;
+            _onDelayChange: function(event){
+                this.model.Delay = parseInt($(event.currentTarget).val());
              },
         });
         return SlideshowOptionView;
