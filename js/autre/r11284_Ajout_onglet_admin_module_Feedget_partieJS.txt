Revision: r11284
Date: 2023-09-15 08:43:10 +0300 (zom 15 Sep 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
Ajout onglet admin module Feedget(partieJS)

## Files changed

## Full metadata
------------------------------------------------------------------------
r11284 | srazanandralisoa | 2023-09-15 08:43:10 +0300 (zom 15 Sep 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/Gruntfile.js
   M /branches/ideo3_v2/integration/assets/ACLs/admin.json
   M /branches/ideo3_v2/integration/assets/ACLs/lpadmin.json
   M /branches/ideo3_v2/integration/assets/ACLs/root.json
   M /branches/ideo3_v2/integration/assets/ACLs/superadmin.json
   M /branches/ideo3_v2/integration/src/js/JEditor/App/Router.js
   M /branches/ideo3_v2/integration/src/js/JEditor/App/Router_save.js
   M /branches/ideo3_v2/integration/src/js/JEditor/App/Templates/application.html
   M /branches/ideo3_v2/integration/src/js/JEditor/App/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/App/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/App/nls/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/App/routes/feedget.js
   M /branches/ideo3_v2/integration/src/js/JEditor/App/routes/main.js
   M /branches/ideo3_v2/integration/src/js/JEditor/App/routes_build.js
   M /branches/ideo3_v2/integration/src/js/JEditor/App/routes_define.json
   M /branches/ideo3_v2/integration/src/js/JEditor/DashBoard/Templates/DashBoardTpl.html
   M /branches/ideo3_v2/integration/src/js/JEditor/DashBoard/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/DashBoard/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/DashBoard/nls/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/FeedgetPanel
   A /branches/ideo3_v2/integration/src/js/JEditor/FeedgetPanel/FeedgetPanel.js
   A /branches/ideo3_v2/integration/src/js/JEditor/FeedgetPanel/Models
   A /branches/ideo3_v2/integration/src/js/JEditor/FeedgetPanel/Models/Feedget.js
   A /branches/ideo3_v2/integration/src/js/JEditor/FeedgetPanel/Templates
   A /branches/ideo3_v2/integration/src/js/JEditor/FeedgetPanel/Templates/FeedgetPanel.html
   A /branches/ideo3_v2/integration/src/js/JEditor/FeedgetPanel/Views
   A /branches/ideo3_v2/integration/src/js/JEditor/FeedgetPanel/nls
   A /branches/ideo3_v2/integration/src/js/JEditor/FeedgetPanel/nls/en-au
   A /branches/ideo3_v2/integration/src/js/JEditor/FeedgetPanel/nls/en-au/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/FeedgetPanel/nls/en-us
   A /branches/ideo3_v2/integration/src/js/JEditor/FeedgetPanel/nls/en-us/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/FeedgetPanel/nls/fr-ca
   A /branches/ideo3_v2/integration/src/js/JEditor/FeedgetPanel/nls/fr-ca/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/FeedgetPanel/nls/fr-fr
   A /branches/ideo3_v2/integration/src/js/JEditor/FeedgetPanel/nls/fr-fr/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/FeedgetPanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/dashboard.less
   A /branches/ideo3_v2/integration/src/less/imports/feedget_panel
   A /branches/ideo3_v2/integration/src/less/imports/feedget_panel/import
   A /branches/ideo3_v2/integration/src/less/imports/feedget_panel/import/vars.less
   A /branches/ideo3_v2/integration/src/less/imports/feedget_panel/main.less
   M /branches/ideo3_v2/integration/src/less/imports/variables.less
   M /branches/ideo3_v2/integration/src/less/main.less

Ajout onglet admin module Feedget(partieJS)
------------------------------------------------------------------------

## Diff
Index: Gruntfile.js
===================================================================
--- Gruntfile.js	(révision 11283)
+++ Gruntfile.js	(révision 11284)
@@ -21,7 +21,8 @@
                     'build/css/newsletter_panel.css': 'src/less/imports/newsletter_panel/main.less',
                     'build/css/logs_panel.css': 'src/less/imports/logs_panel/main.less',
                     'build/css/design_panel.css': 'src/less/imports/design_panel/main.less',
-                    'build/css/evaluation_panel.css': 'src/less/imports/evaluation_panel/main.less'
+                    'build/css/evaluation_panel.css': 'src/less/imports/evaluation_panel/main.less',
+                    'build/css/feedget_panel.css': 'src/less/imports/feedget_panel/main.less'
                 },
                 options: {
                     strictImports: true,
@@ -49,7 +50,8 @@
                     'build/css/newsletter_panel.css': 'src/less/imports/newsletter_panel/main.less',
                     'build/css/logs_panel.css': 'src/less/imports/logs_panel/main.less',
                     'build/css/design_panel.css': 'src/less/imports/design_panel/main.less',
-                    'build/css/evaluation_panel.css': 'src/less/imports/evaluation_panel/main.less'
+                    'build/css/evaluation_panel.css': 'src/less/imports/evaluation_panel/main.less',
+                    'build/css/feedget_panel.css': 'src/less/imports/feedget_panel/main.less',
                 },
                 options: {
                     strictImports: true,
Index: assets/ACLs/admin.json
===================================================================
--- assets/ACLs/admin.json	(révision 11283)
+++ assets/ACLs/admin.json	(révision 11284)
@@ -194,5 +194,9 @@
  "upload_video": {
     "value": false,
     "comparison": null
+ },
+ "access_panel_feedget": {
+    "value": true,
+    "comparison": "==="
  }
 }
Index: assets/ACLs/lpadmin.json
===================================================================
--- assets/ACLs/lpadmin.json	(révision 11283)
+++ assets/ACLs/lpadmin.json	(révision 11284)
@@ -194,6 +194,10 @@
     "upload_video": {
         "value": false,
         "comparison": null
+     },
+     "access_panel_feedget": {
+        "value": true,
+        "comparison": "==="
      }
    }
    
\ No newline at end of file
Index: assets/ACLs/root.json
===================================================================
--- assets/ACLs/root.json	(révision 11283)
+++ assets/ACLs/root.json	(révision 11284)
@@ -194,5 +194,9 @@
     "upload_video": {
         "value": true,
         "comparison": null
+     },
+     "access_panel_feedget": {
+        "value": true,
+        "comparison": "==="
      }
 }
Index: assets/ACLs/superadmin.json
===================================================================
--- assets/ACLs/superadmin.json	(révision 11283)
+++ assets/ACLs/superadmin.json	(révision 11284)
@@ -194,5 +194,9 @@
  "upload_video": {
     "value": true,
     "comparison": null
+ },
+ "access_panel_feedget": {
+    "value": true,
+    "comparison": "==="
  }
 }
Index: src/js/JEditor/App/Router.js
===================================================================
--- src/js/JEditor/App/Router.js	(révision 11283)
+++ src/js/JEditor/App/Router.js	(révision 11284)
@@ -25,6 +25,7 @@
         "evaluation": __IDEO_EVALUATION__,
         "mobileapp": __IDEO_MOBILEAPP__,
         "quote": __IDEO_QUOTE__,
+        "feedget": __IDEO_FEEDGET__,
         "icom": __IDEO_ICOM3__,
         "restaurant": __IDEO_RESTAURANT__
       };
Index: src/js/JEditor/App/Router_save.js
===================================================================
--- src/js/JEditor/App/Router_save.js	(révision 11283)
+++ src/js/JEditor/App/Router_save.js	(révision 11284)
@@ -34,8 +34,9 @@
                 "evaluation": __IDEO_EVALUATION__,
                 "mobileapp": __IDEO_MOBILEAPP__,
                 "quote": __IDEO_QUOTE__,
+                "feedget": __IDEO_FEEDGET__,
                 "icom": __IDEO_ICOM3__,
-                "restaurant": __IDEO_RESTAURANT__
+                "restaurant": __IDEO_RESTAURANT__,
             };
             if (!this.user.can("access_panel_" + name, route_params[name])) {
                 this.forbidden();
Index: src/js/JEditor/App/Templates/application.html
===================================================================
--- src/js/JEditor/App/Templates/application.html	(révision 11283)
+++ src/js/JEditor/App/Templates/application.html	(révision 11284)
@@ -46,6 +46,9 @@
              <% if(user.can('access_panel_quote',__IDEO_QUOTE__)){ %>
             <li><a href="#" class="quote panel-link" data-target="quote"><span class="icon icon-form-dropdown_list"></span><span class="text"><%= __("Quote")%></span></a></li>
              <% } %>
+             <% if(user.can('access_panel_feedget',__IDEO_FEEDGET__)){ %>
+                <li><a href="#" class="feedget panel-link" data-target="feedget"><span class="icon icon-feedget-logo"></span><span class="text"><%= __("Feedget")%></span></a></li>
+             <% } %>
              <% if(user.can('access_panel_newsletter',__IDEO_NEWSLETTER__)){ %>
             <li><a href="#" class="newsletter panel-link" data-target="newsletter"><span class="icon icon-newsletter-icon"></span><span class="text"><%= __("Newsletter")%></span></a></li>
              <% } %>
Index: src/js/JEditor/App/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/App/nls/fr-ca/i18n.js	(révision 11283)
+++ src/js/JEditor/App/nls/fr-ca/i18n.js	(révision 11284)
@@ -13,5 +13,6 @@
     "logout": "Déconnexion",
     "MobileApp" :"Appli Mobile",
     "Quote" :"Quote",
-    "disconnect": "vous avez été déconnecté"
+    "disconnect": "vous avez été déconnecté",
+    "Feedget":"Feedget"
 });
\ No newline at end of file
Index: src/js/JEditor/App/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/App/nls/fr-fr/i18n.js	(révision 11283)
+++ src/js/JEditor/App/nls/fr-fr/i18n.js	(révision 11284)
@@ -13,5 +13,6 @@
     "logout": "Déconnexion",
     "MobileApp" :"Appli Mobile",
     "Quote" :"Quote",
-    "disconnect": "vous avez été déconnecté"
+    "disconnect": "vous avez été déconnecté",
+    "Feedget":"Feedget"
 });
\ No newline at end of file
Index: src/js/JEditor/App/nls/i18n.js
===================================================================
--- src/js/JEditor/App/nls/i18n.js	(révision 11283)
+++ src/js/JEditor/App/nls/i18n.js	(révision 11284)
@@ -22,4 +22,5 @@
         "Deliver" :"Deliver",
         "logs":"logs",
         "disconnect":"You have been disconnected",
+        "Feedget":"Feedget"
     }, "fr-fr":true, "fr-ca":true });
Index: src/js/JEditor/App/routes/feedget.js
===================================================================
--- src/js/JEditor/App/routes/feedget.js	(nonexistent)
+++ src/js/JEditor/App/routes/feedget.js	(révision 11284)
@@ -0,0 +1,8 @@
+define([], function () {
+    return function () {
+        require(["JEditor/FeedgetPanel/FeedgetPanel"], _.bind(function (FeedgetPanel) {
+            if (!this.app.currentPanel || !(this.app.currentPanel instanceof FeedgetPanel))
+                this.app.currentPanel = FeedgetPanel.getInstance();
+        }, this));
+    };
+});
\ No newline at end of file
Index: src/js/JEditor/App/routes/main.js
===================================================================
--- src/js/JEditor/App/routes/main.js	(révision 11283)
+++ src/js/JEditor/App/routes/main.js	(révision 11284)
@@ -16,7 +16,8 @@
     "./mobileapp",
     "./quote",
     "./icom",
-    "./restaurant"
+    "./restaurant",
+    "./feedget"
 ],
         function (
                 social,
@@ -36,7 +37,8 @@
                 mobileapp,
                 quote,
                 icom,
-                restaurant
+                restaurant,
+                feedget
                 )
         {
             return {
@@ -57,6 +59,7 @@
                 mobileapp:mobileapp,
                 quote:quote,
                 icom:icom,
-                restaurant:restaurant
+                restaurant:restaurant,
+                feedget:feedget
             };
         });
Index: src/js/JEditor/App/routes_build.js
===================================================================
--- src/js/JEditor/App/routes_build.js	(révision 11283)
+++ src/js/JEditor/App/routes_build.js	(révision 11284)
@@ -16,5 +16,6 @@
  "mobileapp":"mobileapp",
  "quote":"quote",
  "icom":"icom",
- "restaurant":"restaurant"
+ "restaurant":"restaurant",
+ "feedget":"feedget"
 });
Index: src/js/JEditor/App/routes_define.json
===================================================================
--- src/js/JEditor/App/routes_define.json	(révision 11283)
+++ src/js/JEditor/App/routes_define.json	(révision 11284)
@@ -16,5 +16,6 @@
   "mobileapp":"mobileapp",
   "quote":"quote",
   "icom":"icom",
-  "restaurant":"restaurant"
+  "restaurant":"restaurant",
+  "feedget":"feedget"
 }
Index: src/js/JEditor/DashBoard/Templates/DashBoardTpl.html
===================================================================
--- src/js/JEditor/DashBoard/Templates/DashBoardTpl.html	(révision 11283)
+++ src/js/JEditor/DashBoard/Templates/DashBoardTpl.html	(révision 11284)
@@ -174,6 +174,17 @@
                   </a>
               </li><!-- 
                <% } %>
+               <% if(user.can('access_panel_feedget',__IDEO_FEEDGET__)){ %>
+              --><li>
+                    <a href="#feedget" class="feedget panel-link" data-target="feedget">
+                        <span class="icon icon-feedget-logo"></span>
+                        <span class="title"><%=__('Feedget')%></span>
+                        <span class="text">
+                        <%=__("feedget_explained")%>
+                        </span>
+                    </a>
+                </li><!-- 
+                <% } %>
                   <% if(user.can('access_panel_newsletter',__IDEO_NEWSLETTER__)){ %>
              --><li>
                  <a href="#newsletter" class="newsletter panel-link" data-target="newsletter">
Index: src/js/JEditor/DashBoard/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/DashBoard/nls/fr-ca/i18n.js	(révision 11283)
+++ src/js/JEditor/DashBoard/nls/fr-ca/i18n.js	(révision 11284)
@@ -33,5 +33,7 @@
     "getting_starded":"Prise en main",
     "customerInfos_1":"N'hésitez pas à contacter le Service Clients.",
     "customerInfos_2":"Joignable du lundi au vendredi de 8h30 à 18h00",
-    "customerInfos_3":"Joignable du lundi au vendredi de 9h00 à 17h00"
+    "customerInfos_3":"Joignable du lundi au vendredi de 9h00 à 17h00",
+    "Feedget": "Feedget",
+    "feedget_explained":"Affichez sur votre site vos posts Facebook / Instagram et vos avis GMB"
 });
\ No newline at end of file
Index: src/js/JEditor/DashBoard/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/DashBoard/nls/fr-fr/i18n.js	(révision 11283)
+++ src/js/JEditor/DashBoard/nls/fr-fr/i18n.js	(révision 11284)
@@ -33,5 +33,7 @@
     "getting_starded":"Prise en main",
     "customerInfos_1":"N'hésitez pas à contacter le Service Clients.",
     "customerInfos_2":"Joignable du lundi au vendredi de 8h30 à 18h00",
-    "customerInfos_3":"Joignable du lundi au vendredi de 9h00 à 17h00"
+    "customerInfos_3":"Joignable du lundi au vendredi de 9h00 à 17h00",
+    "Feedget": "Feedget",
+    "feedget_explained":"Affichez sur votre site vos posts Facebook / Instagram et vos avis GMB"
 });
\ No newline at end of file
Index: src/js/JEditor/DashBoard/nls/i18n.js
===================================================================
--- src/js/JEditor/DashBoard/nls/i18n.js	(révision 11283)
+++ src/js/JEditor/DashBoard/nls/i18n.js	(révision 11284)
@@ -41,7 +41,9 @@
       "getting_starded":"Getting Started",
       "customerInfos_1":"Do not hesitate to contact our Customer Service.",
       "customerInfos_2":"Available Monday to Friday from 8:30 am to 6:00 pm",
-      "customerInfos_3":"Available Monday to Friday from 9am til 5pm"
+      "customerInfos_3":"Available Monday to Friday from 9am til 5pm",
+      "Feedget": "Feedget",
+      "feedget_explained":"Display your Facebook / Instagram posts and GMB reviews on your website"
    },
    "fr-fr":true,
    "fr-ca":true
Index: src/js/JEditor/FeedgetPanel/FeedgetPanel.js
===================================================================
--- src/js/JEditor/FeedgetPanel/FeedgetPanel.js	(nonexistent)
+++ src/js/JEditor/FeedgetPanel/FeedgetPanel.js	(révision 11284)
@@ -0,0 +1,62 @@
+define([
+    "JEditor/Commons/Ancestors/Views/PanelView",
+    "text!./Templates/FeedgetPanel.html",
+    "i18n!./nls/i18n"
+],
+        function (PanelView, FeedgetPanelTemplate, translate) {
+            var FeedgetPanel = PanelView.extend(
+                    /**
+                     * @lends JEditor.Panels.FeedgetPanel.prototype
+                     */
+                            {
+                                cssFile: __IDEO_INTEGRATION_PATH__ + "/build/css/feedget_panel.css",
+
+                                constructor: function (options) {
+                                    //mon code
+                                    PanelView.call(this, {panelName: 'feedgetPanel'});
+                                },
+                                loadCss: function (url) {
+                                    var title    = 'panel-style';
+                                    var cssPanel = $('link[title="'+title+'"]');
+
+                                    // generate and append link tag with the related css file
+                                    if (cssPanel.length == 0) {
+                                        var link = document.createElement("link");
+                                        link.title = title;
+                                        link.type = "text/css";
+                                        link.rel = "stylesheet";
+                                        link.href = url;
+                                        document.getElementsByTagName("head")[0].appendChild(link);
+                                    } else {
+                                        cssPanel.attr('href', url);
+                                    }
+                                },
+                                /**
+                                 * initialise l'objet
+                                 */
+                                initialize: function () {
+                                    this._super();
+                                    this.loadCss(this.cssFile);
+                                    this._template = this.buildTemplate(FeedgetPanelTemplate, translate);
+                                },
+                                load: function () {
+                                    //utiliser si erreur
+                                    // this.loadingError();
+                                    //toujours utiliser si cuccès
+                                    this.loadingEnd();
+                                },
+                                /**
+                                 * @return JEditor.Panels.FeedgetPanel
+                                 */
+                                render: function () {
+                                    var lang = this.app.uiLanguage.id.replace('_', '-');
+                                    var paramsTokens = (__IDEO_FEEDGET_TOKEN__!=''&& __IDEO_FEEDGET_REFRESH_TOKEN__!='')? "?token=" + __IDEO_FEEDGET_TOKEN__ + "&refresh_token=" + __IDEO_FEEDGET_REFRESH_TOKEN__ : '';
+                                    var url = "https://feedget.by-linkeo.com/" + lang + paramsTokens;
+                                    this.$el.html(this._template( {url:url} ));
+                                    //open in a new tab / window
+                                    window.open(url);
+                                    return this;
+                                }
+                            });
+                    return FeedgetPanel;
+                });

Property changes on: src/js/JEditor/FeedgetPanel/FeedgetPanel.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/FeedgetPanel/Models/Feedget.js
===================================================================
--- src/js/JEditor/FeedgetPanel/Models/Feedget.js	(nonexistent)
+++ src/js/JEditor/FeedgetPanel/Models/Feedget.js	(révision 11284)
@@ -0,0 +1,19 @@
+define([
+    "JEditor/Commons/Ancestors/Models/Model",
+    "JEditor/Commons/Events",
+    "i18n!../nls/i18n"
+], function (Model, Events, translate) {
+    var Feedget = Model.extend({
+        defaults: {
+        },
+        initialize: function () {
+            console.log("init");
+            this._super();
+        },
+        validate: function (attributes, options) {
+            
+        }
+    });
+    
+    return Feedget;
+});
\ No newline at end of file

Property changes on: src/js/JEditor/FeedgetPanel/Models/Feedget.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/FeedgetPanel/Templates/FeedgetPanel.html
===================================================================
--- src/js/JEditor/FeedgetPanel/Templates/FeedgetPanel.html	(nonexistent)
+++ src/js/JEditor/FeedgetPanel/Templates/FeedgetPanel.html	(révision 11284)
@@ -0,0 +1,28 @@
+<div class="header">
+    <div id="sub-header">
+        <div class="sub-header__action">
+            <div class="sub-header__action-content">
+            </div>
+        </div>
+        <div class="sub-header__infos">
+            <h1 class="sub-header__info-title"><%= __('feedgetTitle') %></h1>
+            <p class="sub-header__info-txt"><%= __('feedgetDesc') %>.</p>
+            <div class="sub-header__info-icon  help">
+                <svg width="32px" height="32px" viewBox="0 0 32 32" enable-background="new 0 0 32 32" xml:space="preserve"><path fill="#FFFFFF" d="M27.678,7.111l-9.82-5.669c-1.021-0.589-2.692-0.589-3.716,0L4.321,7.111C3.3,7.701,2.463,9.15,2.463,10.331v11.34c0,1.18,0.836,2.629,1.857,3.219l9.821,5.669c1.023,0.589,2.695,0.589,3.716,0l9.82-5.669c1.023-0.59,1.859-2.039,1.859-3.219v-11.34C29.537,9.15,28.701,7.701,27.678,7.111z"/></svg>
+                <svg width="32px" height="32px" viewBox="0 0 32 32" enable-background="new 0 0 32 32" xml:space="preserve"><path fill="#FFFFFF" d="M15.563,21.303c0-0.693,0.146-1.283,0.437-1.77c0.292-0.485,0.667-0.917,1.124-1.289c0.459-0.376,0.965-0.714,1.519-1.02c0.557-0.308,1.108-0.625,1.666-0.959c0.666-0.416,1.262-0.846,1.789-1.289c0.526-0.443,0.983-0.943,1.373-1.498c0.389-0.555,0.688-1.172,0.896-1.852c0.209-0.68,0.311-1.463,0.311-2.352c0-1.442-0.258-2.685-0.77-3.725c-0.515-1.04-1.181-1.894-1.998-2.559c-0.818-0.666-1.74-1.151-2.769-1.457c-1.024-0.305-2.063-0.457-3.121-0.457c-0.943,0-1.859,0.118-2.746,0.354c-0.888,0.235-1.714,0.582-2.476,1.04C10.035,2.928,9.356,3.49,8.759,4.156C8.162,4.823,7.685,5.585,7.324,6.445l3.62,2.498c0.222-0.361,0.484-0.743,0.791-1.145c0.304-0.403,0.665-0.763,1.082-1.082c0.415-0.318,0.874-0.583,1.373-0.791c0.5-0.208,1.054-0.312,1.665-0.312c0.5,0,0.993,0.076,1.476,0.229c0.485,0.152,0.91,0.381,1.271,0.687c0.359,0.306,0.65,0.68,0.873,1.124c0.224,0.444,0.334,0.957,0.334,1.539c0,0.777-0.203,1.465-0.604,2.06c-0.399,0.598-0.896,1.118-1.478,1.563c-0.582,0.443-1.18,0.824-1.79,1.143c-0.611,0.319-1.124,0.604-1.539,0.853c-1.388,0.808-2.305,1.748-2.747,2.83c-0.445,1.084-0.667,2.305-0.667,3.664h4.579V21.303z M15.687,30.998v-5.744h-4.619v5.744H15.687z"/></svg>
+            </div>
+        </div>
+    </div>
+</div>
+
+<div class="page-container">
+    <section class="main-content">
+        <div class="new-tab-message">
+            <div class="top-empty-bar"></div>
+            <svg x="0px" y="0px" width="50px" height="50px" viewBox="0 0 50 50" enable-background="new 0 0 50 50" xml:space="preserve"><path fill="#CCCCCC" d="M7,18h32v10h3V4L4.015,4.011L4,40h26v-3H7V18z M42,37v-6h-3v6h-6v3h6v6h3v-6h6v-3H42z"/></svg>
+            <p><%= __('redirectText') %>.</p>
+            <a href="<%= url %>" class="ideo-btn" target="_blank"><%=__("clickHere")%></a>
+        </div>
+    </section>
+
+</div>
\ No newline at end of file

Property changes on: src/js/JEditor/FeedgetPanel/Templates/FeedgetPanel.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/FeedgetPanel/nls/en-au/i18n.js
===================================================================
--- src/js/JEditor/FeedgetPanel/nls/en-au/i18n.js	(nonexistent)
+++ src/js/JEditor/FeedgetPanel/nls/en-au/i18n.js	(révision 11284)
@@ -0,0 +1,2 @@
+define({
+});

Property changes on: src/js/JEditor/FeedgetPanel/nls/en-au/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/FeedgetPanel/nls/en-us/i18n.js
===================================================================
--- src/js/JEditor/FeedgetPanel/nls/en-us/i18n.js	(nonexistent)
+++ src/js/JEditor/FeedgetPanel/nls/en-us/i18n.js	(révision 11284)
@@ -0,0 +1,2 @@
+define({
+});

Property changes on: src/js/JEditor/FeedgetPanel/nls/en-us/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/FeedgetPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/FeedgetPanel/nls/fr-ca/i18n.js	(nonexistent)
+++ src/js/JEditor/FeedgetPanel/nls/fr-ca/i18n.js	(révision 11284)
@@ -0,0 +1,6 @@
+define({
+	"feedgetTitle" : "Feedget",
+	"feedgetDesc": "Affichez sur votre site vos posts Facebook / Instagram et vos avis GMB",
+	"clickHere": "Gérer mon module Feedget",
+	"redirectText": "L'espace d'administration de votre module Feedget a dû s'ouvrir dans un nouvel onglet ou dans une nouvelle fenêtre.<br>Si ce n'est pas le cas cliquez sur le lien ci-dessous"
+});
\ No newline at end of file

Property changes on: src/js/JEditor/FeedgetPanel/nls/fr-ca/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/FeedgetPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/FeedgetPanel/nls/fr-fr/i18n.js	(nonexistent)
+++ src/js/JEditor/FeedgetPanel/nls/fr-fr/i18n.js	(révision 11284)
@@ -0,0 +1,6 @@
+define({
+	"feedgetTitle" : "Feedget",
+	"feedgetDesc": "Affichez sur votre site vos posts Facebook / Instagram et vos avis GMB",
+	"clickHere": "Gérer mon module Feedget",
+	"redirectText": "L'espace d'administration de votre module Feedget a dû s'ouvrir dans un nouvel onglet ou dans une nouvelle fenêtre.<br>Si ce n'est pas le cas cliquez sur le lien ci-dessous"
+});
\ No newline at end of file

Property changes on: src/js/JEditor/FeedgetPanel/nls/fr-fr/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/FeedgetPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/FeedgetPanel/nls/i18n.js	(nonexistent)
+++ src/js/JEditor/FeedgetPanel/nls/i18n.js	(révision 11284)
@@ -0,0 +1,11 @@
+define({
+    "root": {
+    	"feedgetTitle" : "Feedget",
+    	"feedgetDesc" : "Display your Facebook / Instagram posts and GMB reviews on your website",
+    	"clickHere": "Manage my Feedget add-on",
+   		"redirectText": "Your add-on back end should open in a new tab or in a new window.<br>If it does not open click the link below",
+
+    },
+    "fr-fr": true,
+    "fr-ca": true
+})
\ No newline at end of file

Property changes on: src/js/JEditor/FeedgetPanel/nls/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/less/imports/dashboard.less
===================================================================
--- src/less/imports/dashboard.less	(révision 11283)
+++ src/less/imports/dashboard.less	(révision 11284)
@@ -147,6 +147,7 @@
                         &.icon-toque-icon		   {color:@restaurantColor;}
                         &.icon-newsletter-icon	   {color:@newsletterColor;}
 						&.icon-eval-star 		   {color:@evaluationColor;}
+						&.icon-feedget-logo  		{color:@feedgetColorDark;}
 
 
 						display: block;
Index: src/less/imports/feedget_panel/import/vars.less
===================================================================
--- src/less/imports/feedget_panel/import/vars.less	(nonexistent)
+++ src/less/imports/feedget_panel/import/vars.less	(révision 11284)
@@ -0,0 +1,18 @@
+@main			: #2677d9;
+
+@greyXL			: #f4f4f4;
+@greyL 			: #e1e1e1;
+@grey			: #C7C7C7;
+@greyM 			: #999999;
+@greyD			: #666666;
+@greyXD			: #383838;
+@black			: #1a1a1a;
+
+@fb-color		: #36609F;
+@twitter-color	: #2CAAE1;
+@google-color	: #DC5442;
+@pinterest-color: #CB2128;
+
+
+@raleway 		: 'raleway';
+@opensans 		: 'open sans';
\ No newline at end of file
Index: src/less/imports/feedget_panel/main.less
===================================================================
--- src/less/imports/feedget_panel/main.less	(nonexistent)
+++ src/less/imports/feedget_panel/main.less	(révision 11284)
@@ -0,0 +1,18 @@
+// basic less tools
+@import 'import/vars';
+@import '../common/import/mixins';
+@import '../common/module/ideo-btn';
+@import '../common/module/new-tab-message';
+
+// commons modules
+@import '../common/module/sub-header';
+.sub-header__action {
+	float: left;
+	display: table;
+	width: 261px;
+	height: 160px;
+	position: relative;
+	z-index: 1;
+
+	background-color: darken(@main, 10%);
+}
\ No newline at end of file

Property changes on: src/less/imports/feedget_panel/main.less
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/less/imports/variables.less
===================================================================
--- src/less/imports/variables.less	(révision 11283)
+++ src/less/imports/variables.less	(révision 11284)
@@ -26,6 +26,8 @@
 @restaurantColor:	   	#f84f2e;
 @newsletterColor:	   	#eb2a53;
 @evaluationColor:       #c2922c;
+@feedgetColorLight:     #2677d9;
+@feedgetColorDark:      #004599;
 
 @contrastTextColor: 	rgba(0,0,0,0.85);
 
Index: src/less/main.less
===================================================================
--- src/less/main.less	(révision 11283)
+++ src/less/main.less	(révision 11284)
@@ -1825,6 +1825,14 @@
       &.active span.text {
         color: #fff;
       }
+      &.feedget {
+        &:hover {
+          background-color: @feedgetColorLight;
+        }
+        &.active {
+          background-color: @feedgetColorDark;
+        }
+      }
 
     }
     span.icon {
@@ -2839,4 +2847,4 @@
 }
 .ui-dialog .ui-dialog-content li{
   margin: 6px;
-}
\ No newline at end of file
+}
