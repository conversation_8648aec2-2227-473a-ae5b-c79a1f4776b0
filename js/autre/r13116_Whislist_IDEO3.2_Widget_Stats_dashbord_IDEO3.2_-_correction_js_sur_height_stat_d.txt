Revision: r13116
Date: 2024-09-27 12:42:57 +0300 (zom 27 Sep 2024) 
Author: rrakotoarinelina 

## Commit message
Whislist IDEO3.2 : Widget Stats dashbord IDEO3.2 - correction js sur height stat dashboard

## Files changed

## Full metadata
------------------------------------------------------------------------
r13116 | rrakotoarinelina | 2024-09-27 12:42:57 +0300 (zom 27 Sep 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/StatsPanel/StatsPanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/StatsPanel/Templates/statsPanelTpl.html

Whislist IDEO3.2 : Widget Stats dashbord IDEO3.2 - correction js sur height stat dashboard
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/StatsPanel/StatsPanel.js
===================================================================
--- src/js/JEditor/StatsPanel/StatsPanel.js	(révision 13115)
+++ src/js/JEditor/StatsPanel/StatsPanel.js	(révision 13116)
@@ -77,7 +77,7 @@
                             // postMessage reçu à l'init des stats
                             if (message.data.iframe) {
                                 // set iframe height
-                                $('iframe').height(message.data.iframe+160);
+                                $('iframe#statspanel').height(message.data.iframe+160);
                                 // simule le retour naturel en haut de page
                                 $('html, body').stop().scrollTop(0);
                             }
Index: src/js/JEditor/StatsPanel/Templates/statsPanelTpl.html
===================================================================
--- src/js/JEditor/StatsPanel/Templates/statsPanelTpl.html	(révision 13115)
+++ src/js/JEditor/StatsPanel/Templates/statsPanelTpl.html	(révision 13116)
@@ -1,4 +1,4 @@
-<iframe width="100%"
+<iframe id="statspanel" width="100%"
 	src="<%= url %>" 
 	style="border:none;
 	background: none repeat scroll 0% 0% transparent;min-height:550px;">
