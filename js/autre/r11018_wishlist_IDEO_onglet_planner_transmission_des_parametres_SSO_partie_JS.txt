Revision: r11018
Date: 2023-06-13 08:01:57 +0300 (tlt 13 Jon 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO: onglet planner transmission des parametres SSO (partie JS)

## Files changed

## Full metadata
------------------------------------------------------------------------
r11018 | srazanandralisoa | 2023-06-13 08:01:57 +0300 (tlt 13 Jon 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ClickrdvPanel/ClickrdvPanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/QuotePanel/QuotePanel.js

wishlist IDEO: onglet planner transmission des parametres SSO (partie JS)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ClickrdvPanel/ClickrdvPanel.js
===================================================================
--- src/js/JEditor/ClickrdvPanel/ClickrdvPanel.js	(révision 11017)
+++ src/js/JEditor/ClickrdvPanel/ClickrdvPanel.js	(révision 11018)
@@ -50,7 +50,8 @@
                                  */
                                 render: function () {
                                     var lang = (this.app.uiLanguage.id.substr(0, 2) == "fr") ? "fr" : "en";
-                                    var url = "https://eplanner.linkeo.com/admin/?key=" + __IDEO_DLC_KEY__ + "&c=" + __IDEO_DB_NAME_SITE__ + "&v=" + __IDEO_DLC_IV__ + "&lang=" + lang;
+                                    var paramsTokens = (__IDEO_PLANNER_TOKEN__!='' && __IDEO_PLANNER_REFRESH_TOKEN__!='')? "&token=" + __IDEO_PLANNER_TOKEN__ +"&refresh_token="+__IDEO_PLANNER_REFRESH_TOKEN__ :'';
+                                    var url = "https://eplanner.linkeo.com/admin/?key=" + __IDEO_DLC_KEY__ + "&c=" + __IDEO_DB_NAME_SITE__ + "&v=" + __IDEO_DLC_IV__ + "&lang=" + lang + paramsTokens;
                                     this.$el.html(this._template( {url:url} ));
                                     //open in a new tab / window
                                     window.open(url);
Index: src/js/JEditor/QuotePanel/QuotePanel.js
===================================================================
--- src/js/JEditor/QuotePanel/QuotePanel.js	(révision 11017)
+++ src/js/JEditor/QuotePanel/QuotePanel.js	(révision 11018)
@@ -50,7 +50,8 @@
                                  */
                                 render: function () {
                                     var lang = this.app.uiLanguage.id.replace('_', '-');
-                                    var url = "https://quote.linkeo.ovh/" + lang + "?token=" + __IDEO_TOKEN__ + "&refresh_token=" + __IDEO_REFRESH_TOKEN__;
+                                    var paramsTokens = (__IDEO_QUOTE_TOKEN__!=''&& __IDEO_QUOTE_REFRESH_TOKEN__!='')? "?token=" + __IDEO_QUOTE_TOKEN__ + "&refresh_token=" + __IDEO_QUOTE_REFRESH_TOKEN__ : '';
+                                    var url = "https://quote.linkeo.ovh/" + lang + paramsTokens;
                                     this.$el.html(this._template( {url:url} ));
                                     //open in a new tab / window
                                     window.open(url);
