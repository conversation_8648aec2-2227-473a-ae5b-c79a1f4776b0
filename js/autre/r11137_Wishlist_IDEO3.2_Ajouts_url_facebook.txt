Revision: r11137
Date: 2023-07-06 14:51:53 +0300 (lkm 06 Jol 2023) 
Author: norajaonarivelo 

## Commit message
Wishlist IDEO3.2 :Ajouts url facebook

## Files changed

## Full metadata
------------------------------------------------------------------------
r11137 | norajaonarivelo | 2023-07-06 14:51:53 +0300 (lkm 06 Jol 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/dev/#librairies/module/GestionContenuUtil/src/GestionContenuUtil/Core/BlockModuleHandlers/BlockExternalVideoHandler.php
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/VideoBlock/Views/VideoInfoOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/VideoBlock/Views/VideoStyleOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/VideoBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/VideoBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/VideoBlock/nls/i18n.js

Wishlist IDEO3.2 :Ajouts url facebook
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/VideoBlock/Views/VideoStyleOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/VideoBlock/Views/VideoStyleOptionView.js	(révision 11136)
+++ src/js/JEditor/PagePanel/Contents/Blocks/VideoBlock/Views/VideoStyleOptionView.js	(révision 11137)
@@ -26,6 +26,9 @@
                     dailymotion: {
                         options: [],
                     },
+                    facebook: {
+                        options :[],
+                    },
                     default: {
                         options: [],
                     },
Index: src/js/JEditor/PagePanel/Contents/Blocks/VideoBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/VideoBlock/nls/fr-ca/i18n.js	(révision 11136)
+++ src/js/JEditor/PagePanel/Contents/Blocks/VideoBlock/nls/fr-ca/i18n.js	(révision 11137)
@@ -2,12 +2,13 @@
    "BLOCK_NAME":"Vidéo",
    "videoOptionIntro":"Vidéo à diffuser",
    "videoOptionIntroDetails":"Entrez ci-dessous le lien de la vidéo que vous souhaitez afficher.",
-   "videoAllowed":"Les vidéos acceptées sont celles des plateformes <strong>Youtube</strong>, <strong>Dailymotion</strong> et <strong>Vimeo</strong>.",
+   "videoAllowed":"Les vidéos acceptées sont celles des plateformes <strong>Youtube</strong>, <strong>Dailymotion</strong> et <strong>Vimeo</strong> et <strong>Facebook</strong>.",
    "autoplay":"Lecture automatique",
    "videoKO":"Nous n'avons pas réussi à intégrer votre vidéo.",
    "vimeoOK":"Votre vidéo Vimeo a été intégrée avec succès !",
    "youtubeOK":"Votre vidéo Youtube a été intégrée avec succès !",
    "dailymotionOK":"Votre vidéo Dailymotion a été intégrée avec succès !",
+   "facebookOK": "Votre vidéo Facebook a été intégrée avec succès !",
    "videoInfo":"Vidéo",
    "videoStyle":"Affichage",
    "videoBlockOption":"Éditer la vidéo",
Index: src/js/JEditor/PagePanel/Contents/Blocks/VideoBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/VideoBlock/nls/fr-fr/i18n.js	(révision 11136)
+++ src/js/JEditor/PagePanel/Contents/Blocks/VideoBlock/nls/fr-fr/i18n.js	(révision 11137)
@@ -2,12 +2,13 @@
    "BLOCK_NAME":"Vidéo",
    "videoOptionIntro":"Vidéo à diffuser",
    "videoOptionIntroDetails":"Entrez ci-dessous le lien de la vidéo que vous souhaitez afficher.",
-   "videoAllowed":"Les vidéos acceptées sont celles des plateformes <strong>Youtube</strong>, <strong>Dailymotion</strong> et <strong>Vimeo</strong>.",
+   "videoAllowed":"Les vidéos acceptées sont celles des plateformes <strong>Youtube</strong>, <strong>Dailymotion</strong> et <strong>Vimeo</strong> et <strong>Facebook</strong>.",
    "autoplay":"Lecture automatique",
    "videoKO":"Nous n'avons pas réussi à intégrer votre vidéo.",
    "vimeoOK":"Votre vidéo Vimeo a été intégrée avec succès !",
    "youtubeOK":"Votre vidéo Youtube a été intégrée avec succès !",
    "dailymotionOK":"Votre vidéo Dailymotion a été intégrée avec succès !",
+   "facebookOK": "Votre vidéo Facebook a été intégrée avec succès !",
    "videoInfo":"Vidéo",
    "videoStyle":"Affichage",
    "videoBlockOption":"Éditer la vidéo",
Index: src/js/JEditor/PagePanel/Contents/Blocks/VideoBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/VideoBlock/nls/i18n.js	(révision 11136)
+++ src/js/JEditor/PagePanel/Contents/Blocks/VideoBlock/nls/i18n.js	(révision 11137)
@@ -3,12 +3,13 @@
       "BLOCK_NAME":"Video",
       "videoOptionIntro":"Video to broadcast",
       "videoOptionIntroDetails":"Enter below the link to the video you want to view",
-      "videoAllowed":" Videos accepted are those platforms <strong> Youtube </ strong> <strong> Dailymotion </ strong> and <strong> Vimeo </ strong>. ",
+      "videoAllowed":" Videos accepted are those platforms <strong> Youtube </ strong> <strong> Dailymotion </ strong> and <strong> Vimeo </ strong> and <strong> Facebook </ strong>. ",
       "autoplay":"AutoPlay",
       "videoKO":"We have not been able to integrate your video",
       "vimeoOK":"Your video Vimeo has been integrated successfully!",
       "youtubeOK":"Your Youtube video has been successfully integrated!",
       "dailymotionOK":"Your video Dailymotion has been integrated successfully!",
+      "facebookOK": "Your video Facebook has been integrated successfully!",
       "videoInfo":"Video",
       "videoStyle":"Display",
       "videoBlockOption":"Video Editing",
Index: src/js/JEditor/PagePanel/Contents/Blocks/VideoBlock/Views/VideoInfoOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/VideoBlock/Views/VideoInfoOptionView.js	(révision 11136)
+++ src/js/JEditor/PagePanel/Contents/Blocks/VideoBlock/Views/VideoInfoOptionView.js	(révision 11137)
@@ -28,6 +28,10 @@
                         pattern: /^(?:https?:\/\/|\/\/)?(?:www\.)?(?:dai\.ly\/|dailymotion\.com\/(?:embed\/video\/|video\/|hub\/))((\w|-)+)(?:\S+)?$/,
                         replace: 'https://www.dailymotion.com/embed/video/$1',
                     },
+                    facebook :{
+                        pattern: /^(?:https?:\/\/|\/\/)?(?:www\.)?facebook\.com\/(.*)\/videos\/(\d+)\/$/,
+                        replace: 'https://www.facebook.com/plugins/video.php?height=314&href=https%3A%2F%2Fwww.facebook.com%2F$1%2Fvideos%2F$2%2F&show_text=false&width=560&t=0'
+                    }
                 },
                 initialize: function() {
                     this._super();
@@ -42,7 +46,7 @@
                     var isValid = this._isValid(url);
                     var domProvider = this.dom[this.cid].domProvider;//plutot que parcour du dom
                     var domLabel = this.dom[this.cid].label;//plutot que parcour du dom
-                    domProvider.removeClass('youtube dailymotion vimeo error');//domProvider.removeClass('youtube').removeClass('dailymotion').removeClass('vimeo').removeClass('error');
+                    domProvider.removeClass('youtube dailymotion vimeo facebook error');//domProvider.removeClass('youtube').removeClass('dailymotion').removeClass('vimeo').removeClass('error');
                     domLabel.empty();
                     if (url) {
                         if (isValid) {
@@ -60,7 +64,7 @@
                             var service = this.services[serviceName];
                             if (url.match(service.pattern)) {
                                 this.model.url = url;
-                                this.model.movieUrl = url.replace(service.pattern, service.replace);
+                                this.model.movieUrl = (url.replace(service.pattern, service.replace)).replace(/\/$/, '');
                                 this.model.provider = serviceName;
                                 console.log('Video ' + this.model.provider + ' detected');
                                 return serviceName;
