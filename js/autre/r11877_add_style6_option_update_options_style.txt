Revision: r11877
Date: 2024-02-15 13:29:34 +0300 (lkm 15 Feb 2024) 
Author: mpartaux 

## Commit message
add style6 option + update options style

## Files changed

## Full metadata
------------------------------------------------------------------------
r11877 | mpartaux | 2024-02-15 13:29:34 +0300 (lkm 15 Feb 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselStyle.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridStyleOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Templates/slideshowStyle.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/gallery_style.less
   M /branches/ideo3_v2/integration/src/less/imports/panel.less

add style6 option + update options style
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselStyle.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselStyle.html	(révision 11876)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselStyle.html	(révision 11877)
@@ -38,7 +38,7 @@
                 <input type="radio" class="select-box" name="AffichageCarrouselStyle" value="1" id="<%=_id %>" <%=(StyleAffichage==1)?'checked':''%>>
                    <label for="<%=_id %>">
                     <div class="wrapper">
-                        <div class="vertical-wrap">
+                        <div class="horizontal-wrap">
                             <span class="icon-wrapper">
                                 <span class="icon-collection-style1"></span>
                             </span>
@@ -52,7 +52,7 @@
                 <input type="radio" class="select-box" name="AffichageCarrouselStyle" value="2" id="<%=_id %>" <%=(StyleAffichage==2)?'checked':''%>>
                    <label for="<%=_id %>">
                     <div class="wrapper">
-                        <div class="vertical-wrap">
+                        <div class="horizontal-wrap">
                             <span class="icon-wrapper">
                                 <span class="icon-collection-style2"></span>
                             </span>
@@ -66,7 +66,7 @@
                 <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="3" id="<%=_id %>" <%=(StyleAffichage==3)?'checked':''%>>
                    <label for="<%=_id %>">
                     <div class="wrapper">
-                        <div class="vertical-wrap">
+                        <div class="horizontal-wrap">
                             <span class="icon-wrapper">
                                 <span class="icon-collection-style3"></span>
                             </span>
@@ -80,7 +80,7 @@
                 <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="4" id="<%=_id %>" <%=(StyleAffichage==4)?'checked':''%>>
                    <label for="<%=_id %>">
                     <div class="wrapper">
-                        <div class="vertical-wrap">
+                        <div class="horizontal-wrap">
                             <span class="icon-wrapper">
                                 <span class="icon-collection-style4"></span>
                             </span>
@@ -94,7 +94,7 @@
                 <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="5" id="<%=_id %>" <%=(StyleAffichage==5)?'checked':''%>>
                    <label for="<%=_id %>">
                     <div class="wrapper">
-                        <div class="vertical-wrap">
+                        <div class="horizontal-wrap">
                             <span class="icon-wrapper">
                                 <span class="icon-collection-style5"></span>
                             </span>
@@ -103,6 +103,20 @@
                         </div>
                     </div>
                 </label>
+
+                <%  var _id=_.uniqueId('carrouselStyleAffichage');%>
+                <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="6" id="<%=_id %>" <%=(StyleAffichage==6)?'checked':''%>>
+                   <label for="<%=_id %>">
+                    <div class="wrapper">
+                        <div class="horizontal-wrap">
+                            <span class="icon-wrapper">
+                                <span class="icon-collection-style6"></span>
+                            </span>
+                            <span class="name"><%= __("Style")%> 6</span>
+                            <span class="desc"><%= __("DescStyle6")%></span>
+                        </div>
+                    </div>
+                </label>
                 
         </div>
     </article>
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js	(révision 11876)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js	(révision 11877)
@@ -48,9 +48,10 @@
     "arrowImage"            :   "Afficher des flèches de navigation",
     "ShowArrow"             :   "Afficher les boutons de navigation",
     "emptyCollection"       :   "Votre collection d’images est vide.",
-    "DescStyle1"           :  "Textes sous l'image", 
+    "DescStyle1"           :  "Texte sous l'image", 
     "DescStyle2"           :  "Texte encadré sur l'image",
-    "DescStyle3"           :  "Texte sur l'image",
-    "DescStyle4"           :  "Textes sous l'image avec bordures",
-    "DescStyle5"           :  "Textes sous l'image avec images arrondies"             
+    "DescStyle3"           :  "Texte sur l'image, animé au survol",
+    "DescStyle4"           :  "Texte sous l'image, bordures",
+    "DescStyle5"           :  "Texte sous l'image, images arrondies (idéal images carrés)",             
+    "DescStyle6"           :  "Texte à côté de l'image"             
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js	(révision 11876)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js	(révision 11877)
@@ -42,7 +42,7 @@
        "ButtonReadMore"        :   "Add a button 'Visit the page'",
        "carrouselHeight"       :   "Number of images",
        "carrouselHeightDesc"   :   "Drag to adjust the number of images displayed",
-       "carrouselStyleAffichage"   :    "Style of the images",
+       "carrouselStyleAffichage"   :    "Image style",
        "carrouselStyleAffichageDesc"   :   "Apply a style to the images",
        "ImageFormat"           :   "Image format",
        "ImageFormatDesc"       :   "Apply an image format to the carousel",
@@ -54,9 +54,10 @@
        "emptyCollection"      :  "Your image collection is empty",
        "DescStyle1"           :  "Text under the image", 
        "DescStyle2"           :  "Text framed on the image",
-       "DescStyle3"           :  "Text on the image",
-       "DescStyle4"           :  "Text under the image with borders",
-       "DescStyle5"           :  "Text under the image with rounded pictures"      
+       "DescStyle3"           :  "Text on the image, animated on hover",
+       "DescStyle4"           :  "Text under the image, borders",
+       "DescStyle5"           :  "Text under the image, rounded pictures (ideal for square images)",      
+       "DescStyle6"           :  "Text next to the the image"      
     },
     "fr-fr":true,
     "fr-ca": true
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html	(révision 11876)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html	(révision 11877)
@@ -54,7 +54,7 @@
                 <input type="radio" class="select-box" name="AffichageCarrouselStyle" value="1" id="<%=_id %>" <%=(galerieStyleAff==1)?'checked':''%>>
                    <label for="<%=_id %>">
                     <div class="wrapper">
-                        <div class="vertical-wrap">
+                        <div class="horizontal-wrap">
                             <span class="icon-wrapper">
                                 <span class="icon-collection-style1"></span>
                             </span>
@@ -68,7 +68,7 @@
                 <input type="radio" class="select-box" name="AffichageCarrouselStyle" value="2" id="<%=_id %>" <%=(galerieStyleAff==2)?'checked':''%>>
                    <label for="<%=_id %>">
                     <div class="wrapper">
-                        <div class="vertical-wrap">
+                        <div class="horizontal-wrap">
                             <span class="icon-wrapper">
                                 <span class="icon-collection-style2"></span>
                             </span>
@@ -82,7 +82,7 @@
                 <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="3" id="<%=_id %>" <%=(galerieStyleAff==3)?'checked':''%>>
                    <label for="<%=_id %>">
                     <div class="wrapper">
-                        <div class="vertical-wrap">
+                        <div class="horizontal-wrap">
                             <span class="icon-wrapper">
                                 <span class="icon-collection-style3"></span>
                             </span>
@@ -96,7 +96,7 @@
                 <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="4" id="<%=_id %>" <%=(galerieStyleAff==4)?'checked':''%>>
                    <label for="<%=_id %>">
                     <div class="wrapper">
-                        <div class="vertical-wrap">
+                        <div class="horizontal-wrap">
                             <span class="icon-wrapper">
                                 <span class="icon-collection-style4"></span>
                             </span>
@@ -110,7 +110,7 @@
                 <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="5"  id="<%=_id %>" <%=(galerieStyleAff==5)?'checked':''%>>
                    <label for="<%=_id %>">
                     <div class="wrapper">
-                        <div class="vertical-wrap">
+                        <div class="horizontal-wrap">
                             <span class="icon-wrapper">
                                 <span class="icon-collection-style5"></span>
                             </span>
@@ -119,6 +119,20 @@
                         </div>
                     </div>
                 </label>
+
+                <%  var _id=_.uniqueId('galerieStyleAffichage');%>
+                <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="6"  id="<%=_id %>" <%=(galerieStyleAff==6)?'checked':''%>>
+                   <label for="<%=_id %>">
+                    <div class="wrapper">
+                        <div class="horizontal-wrap">
+                            <span class="icon-wrapper">
+                                <span class="icon-collection-style6"></span>
+                            </span>
+                            <span class="name"><%= __("Style")%> 6</span>
+                            <span class="desc"><%= __("DescStyle6")%></span>
+                        </div>
+                    </div>
+                </label>
                 
         </div>
     </article>
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js	(révision 11876)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js	(révision 11877)
@@ -46,10 +46,11 @@
    'portrait'              :   "Portrait",
    'square'                :   "Carré",
    "editMyGrid"            :   "Modifier ma galerie ",
-   "DescStyle1"           :  "Textes sous l'image", 
+   "DescStyle1"           :  "Texte sous l'image", 
    "DescStyle2"           :  "Texte encadré sur l'image",
-   "DescStyle3"           :  "Texte sur l'image",
+   "DescStyle3"           :  "Texte sur l'image, animé au survol",
    "addGalerieField": "ajouter une collection", 
-   "DescStyle4"           :  "Textes sous l'image avec bordures",
-   "DescStyle5"           :  "Textes sous l'image avec images arrondies"
+   "DescStyle4"           :  "Texte sous l'image, bordures",
+   "DescStyle5"           :  "Texte sous l'image, images arrondies (idéal images carrés)",
+   "DescStyle6"           :  "Texte à côté de l'image"
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/i18n.js	(révision 11876)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/i18n.js	(révision 11877)
@@ -57,10 +57,11 @@
       "editMyGrid"            :   "Edit my Gallery",
       "DescStyle1"           :  "Text under the image", 
       "DescStyle2"           :  "Text framed on the image",
-      "DescStyle3"           :  "Text on the image",
+      "DescStyle3"           :  "Text on the image, animated on hover",
       "addGalerieField": "add collection",  
-      "DescStyle4"           : "Text under the image with borders",
-      "DescStyle5"           :  "Text under the image with rounded pictures" 
+      "DescStyle4"           : "Text under the image, borders",
+      "DescStyle5"           :  "Text under the image, rounded pictures (ideal for square images)",
+      "DescStyle6"           :  "Text next to the the image"
    },
    "fr-fr":true,
    "fr-ca":true
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridStyleOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridStyleOption.html	(révision 11876)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridStyleOption.html	(révision 11877)
@@ -56,7 +56,7 @@
                 <input type="radio" class="select-box" name="AffichageCarrouselStyle" value="1" id="<%=_id %>" <%=(grilleStyleAff==1)?'checked':''%>>
                    <label for="<%=_id %>">
                     <div class="wrapper">
-                        <div class="vertical-wrap">
+                        <div class="horizontal-wrap">
                             <span class="icon-wrapper">
                                 <span class="icon-collection-style1"></span>
                             </span>
@@ -70,7 +70,7 @@
                 <input type="radio" class="select-box" name="AffichageCarrouselStyle" value="2" id="<%=_id %>" <%=(grilleStyleAff==2)?'checked':''%>>
                    <label for="<%=_id %>">
                     <div class="wrapper">
-                        <div class="vertical-wrap">
+                        <div class="horizontal-wrap">
                             <span class="icon-wrapper">
                                 <span class="icon-collection-style2"></span>
                             </span>
@@ -84,7 +84,7 @@
                 <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="3" id="<%=_id %>" <%=(grilleStyleAff==3)?'checked':''%>>
                    <label for="<%=_id %>">
                     <div class="wrapper">
-                        <div class="vertical-wrap">
+                        <div class="horizontal-wrap">
                             <span class="icon-wrapper">
                                 <span class="icon-collection-style3"></span>
                             </span>
@@ -98,7 +98,7 @@
                 <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="4" id="<%=_id %>" <%=(grilleStyleAff==4)?'checked':''%>>
                    <label for="<%=_id %>">
                     <div class="wrapper">
-                        <div class="vertical-wrap">
+                        <div class="horizontal-wrap">
                             <span class="icon-wrapper">
                                 <span class="icon-collection-style4"></span>
                             </span>
@@ -112,7 +112,7 @@
                 <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="5" id="<%=_id %>" <%=(grilleStyleAff==5)?'checked':''%>>
                    <label for="<%=_id %>">
                     <div class="wrapper">
-                        <div class="vertical-wrap">
+                        <div class="horizontal-wrap">
                             <span class="icon-wrapper">
                                 <span class="icon-collection-style5"></span>
                             </span>
@@ -121,6 +121,20 @@
                         </div>
                     </div>
                 </label>
+
+                <%  var _id=_.uniqueId('gridStyleAffichage');%>
+                <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="6" id="<%=_id %>" <%=(grilleStyleAff==6)?'checked':''%>>
+                   <label for="<%=_id %>">
+                    <div class="wrapper">
+                        <div class="horizontal-wrap">
+                            <span class="icon-wrapper">
+                                <span class="icon-collection-style6"></span>
+                            </span>
+                            <span class="name"><%= __("Style")%> 6</span>
+                            <span class="desc"><%= __("DescStyle6")%></span>
+                        </div>
+                    </div>
+                </label>
                 
         </div>
     </article>
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-fr/i18n.js	(révision 11876)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-fr/i18n.js	(révision 11877)
@@ -46,9 +46,10 @@
         'portrait'              :   "Portrait",
         'square'                :   "Carré",
         "editMyGrid"            :   "Modifier ma grille ",
-        "DescStyle1"           :  "Textes sous l'image", 
+        "DescStyle1"           :  "Texte sous l'image", 
         "DescStyle2"           :  "Texte encadré sur l'image",
-        "DescStyle3"           :  "Texte sur l'image",
-        "DescStyle4"           :  "Textes sous l'image avec bordures",
-        "DescStyle5"           :  "Textes sous l'image avec images arrondies"      
+        "DescStyle3"           :  "Texte sur l'image, animé au survol",
+        "DescStyle4"           :  "Texte sous l'image, bordures",
+        "DescStyle5"           :  "Texte sous l'image, images arrondies (idéal images carrés)",
+        "DescStyle6"           :  "Texte à côté de l'image"      
     });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/i18n.js	(révision 11876)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/i18n.js	(révision 11877)
@@ -57,7 +57,8 @@
         "editMyGrid"            :   "Edit my Grid",
         "DescStyle1"           :  "Text under the image", 
         "DescStyle2"           :  "Text framed on the image",
-        "DescStyle3"           :  "Text on the image",
-        "DescStyle4"           :  "Text under the image with borders",
-        "DescStyle5"           :  "Text under the image with rounded pictures"    
+        "DescStyle3"           :  "Text on the image, animated on hover",
+        "DescStyle4"           :  "Text under the image, borders",
+        "DescStyle5"           :  "Text under the image, rounded pictures (ideal for square images)",
+        "DescStyle6"           :  "Text next to the the image"    
 }, "fr-fr":true, "fr-ca":true })
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Templates/slideshowStyle.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Templates/slideshowStyle.html	(révision 11876)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Templates/slideshowStyle.html	(révision 11877)
@@ -26,7 +26,7 @@
                 <input type="radio" class="select-box" name="AffichageSlideshowStyle" value="1" id="<%=_id %>" <%=(StyleAffichage==1)?'checked':''%>>
                    <label for="<%=_id %>">
                     <div class="wrapper">
-                        <div class="vertical-wrap">
+                        <div class="horizontal-wrap">
                             <span class="icon-wrapper">
                                 <span class="icon-collection-style1"></span>
                             </span>
@@ -40,7 +40,7 @@
                 <input type="radio" class="select-box" name="AffichageSlideshowStyle" value="2" id="<%=_id %>" <%=(StyleAffichage==2)?'checked':''%>>
                    <label for="<%=_id %>">
                     <div class="wrapper">
-                        <div class="vertical-wrap">
+                        <div class="horizontal-wrap">
                             <span class="icon-wrapper">
                                 <span class="icon-collection-style2"></span>
                             </span>
@@ -54,7 +54,7 @@
                 <input type="radio" class="select-box"  name="AffichageSlideshowStyle" value="3" id="<%=_id %>" <%=(StyleAffichage==3)?'checked':''%>>
                    <label for="<%=_id %>">
                     <div class="wrapper">
-                        <div class="vertical-wrap">
+                        <div class="horizontal-wrap">
                             <span class="icon-wrapper">
                                 <span class="icon-collection-style3"></span>
                             </span>
@@ -68,7 +68,7 @@
                 <input type="radio" class="select-box"  name="AffichageSlideshowStyle" value="4" id="<%=_id %>" <%=(StyleAffichage==4)?'checked':''%>>
                    <label for="<%=_id %>">
                     <div class="wrapper">
-                        <div class="vertical-wrap">
+                        <div class="horizontal-wrap">
                             <span class="icon-wrapper">
                                 <span class="icon-collection-style4"></span>
                             </span>
@@ -82,7 +82,7 @@
                 <input type="radio" class="select-box"  name="AffichageSlideshowStyle" value="5" id="<%=_id %>" <%=(StyleAffichage==5)?'checked':''%>>
                    <label for="<%=_id %>">
                     <div class="wrapper">
-                        <div class="vertical-wrap">
+                        <div class="horizontal-wrap">
                             <span class="icon-wrapper">
                                 <span class="icon-collection-style5"></span>
                             </span>
@@ -91,6 +91,20 @@
                         </div>
                     </div>
                 </label>
+
+                <%  var _id=_.uniqueId('slideshowStyleAffichage');%>
+                <input type="radio" class="select-box"  name="AffichageSlideshowStyle" value="6" id="<%=_id %>" <%=(StyleAffichage==6)?'checked':''%>>
+                   <label for="<%=_id %>">
+                    <div class="wrapper">
+                        <div class="horizontal-wrap">
+                            <span class="icon-wrapper">
+                                <span class="icon-collection-style6"></span>
+                            </span>
+                            <span class="name"><%= __("Style")%> 6</span>
+                            <span class="desc"><%= __("DescStyle6")%></span>
+                        </div>
+                    </div>
+                </label>
                 
         </div>
     </article>
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-fr/i18n.js	(révision 11876)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-fr/i18n.js	(révision 11877)
@@ -51,9 +51,10 @@
     "arrowImage":   "Afficher des flèches de navigation",
     "ShowArrow":   "Afficher les boutons de navigation",
     "emptyCollection":   "Votre collection d’images est vide.",
-    "DescStyle1":  "Textes sous l'image", 
+    "DescStyle1":  "Texte sous l'image", 
     "DescStyle2":  "Texte encadré sur l'image",
-    "DescStyle3":  "Texte sur l'image",
-    "DescStyle4":  "Textes sous l'image avec bordures",
-    "DescStyle5":  "Textes sous l'image avec images arrondies"  
+    "DescStyle3":  "Texte sur l'image, animé au survol",
+    "DescStyle4":  "Texte sous l'image, bordures",
+    "DescStyle5":  "Texte sous l'image, images arrondies (idéal images carrés)",             
+    "DescStyle6":  "Texte à côté de l'image"             
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/i18n.js	(révision 11876)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/i18n.js	(révision 11877)
@@ -54,10 +54,11 @@
         "emptyCollection"      :  "Your image collection is empty",
         "DescStyle1"           :  "Text under the image", 
         "DescStyle2"           :  "Text framed on the image",
-        "DescStyle3"           :  "Text on the image",
-        "DescStyle4"           :  "Text under the image with borders",
-        "DescStyle5"           :  "Text under the image with rounded pictures"  
-    },
+        "DescStyle3"           :  "Text on the image, animated on hover",
+        "DescStyle4"           :  "Text under the image, borders",
+        "DescStyle5"           :  "Text under the image, rounded pictures (ideal for square images)",      
+        "DescStyle6"           :  "Text next to the the image"      
+     },
      "fr-fr":true, 
      "fr-ca":true 
     })
\ No newline at end of file
Index: src/less/imports/gallery_style.less
===================================================================
--- src/less/imports/gallery_style.less	(révision 11876)
+++ src/less/imports/gallery_style.less	(révision 11877)
@@ -5,15 +5,21 @@
     input[type="radio"].select-box{
         display:none;
         &+label{
-            height:130px;
-            width:49%;
+            height: auto;
+            width: 100%;
+            box-sizing: border-box;
             display:inline-block;
             background-color:#2F2F2F;
             margin: 0;
+            border-radius: 4px;
+            font-variant-numeric: lining-nums;
             &>.wrapper{
                 display:table;
                 margin:auto;
                 width: 87%;
+                span.icon-wrapper{
+                    font-size: 2em;
+                }
                 &>.vertical-wrap{
                     display:table-cell;
                     height:130px;
@@ -30,8 +36,26 @@
                         display:block;
                     }
                 }
-                span.icon-wrapper{
-                    font-size: 2em;
+                &>.horizontal-wrap{
+                    display: flex;
+                    align-items: center;
+                    gap: 1em;
+                    height: 4em;
+                    &>.icon-wrapper{
+                        flex-shrink: 0;
+                    }
+                    &>.name{
+                        color:#ffffff;
+                        font-size: 1.2em;
+                        display:block;
+                        font-weight:600;
+                        flex-shrink: 0;
+                    }
+                    &>.desc{
+                        color:#bcbcbc;
+                        display:block;
+                        font-style: italic;
+                    }
                 }
             }
         }
@@ -40,8 +64,21 @@
                 background-color:@pageColor;
                 &>.wrapper>.vertical-wrap>.desc{
                     color:#0b2428;
-                    font-weight: 500;
+                    font-weight: 600;
                 }
+                &>.wrapper>.horizontal-wrap {
+                    &>.name{
+                        color:#0b2428;
+                        font-weight: 700;
+                    }
+                    &>.desc{
+                        color:#0b2428;
+                        font-weight: 600;
+                    }
+                    &>.icon-wrapper{
+                        color:#0b2428;
+                    }
+                }
             }
         }
     }
Index: src/less/imports/panel.less
===================================================================
--- src/less/imports/panel.less	(révision 11876)
+++ src/less/imports/panel.less	(révision 11877)
@@ -513,10 +513,11 @@
     }
 }
 .panel-option{
+    margin-bottom: 1em;
     header{
-        border-bottom: 1px solid #313131;
+        // border-bottom: 1px solid #313131;
         border-top: 1px solid #313131;
-        padding: 0.5em 0;
+        padding: 1em 0 0;
         margin-bottom: 1em;
         h3.option-name{
             &>span[class^="icon-"]{
