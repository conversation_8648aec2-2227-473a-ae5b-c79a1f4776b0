Revision: r13133
Date: 2024-10-01 15:50:30 +0300 (tlt 01 Okt 2024) 
Author: frahajanirina 

## Commit message
fixe linkSelectorView & text

## Files changed

## Full metadata
------------------------------------------------------------------------
r13133 | frahajanirina | 2024-10-01 15:50:30 +0300 (tlt 01 Okt 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardListView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/fr-fr/i18n.js

fixe linkSelectorView & text
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardListView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardListView.js	(révision 13132)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardListView.js	(révision 13133)
@@ -37,10 +37,6 @@
                 this.listenTo(this.selectIconDialog, Events.ButtonEvents.SELECT_ICON, this.onSelectedIcon);
                 this._existeIcon = (this.model.icon !== '') ? true : false;
                 //this._existeBtn = (this.model.buttonText !== '') ? true : false;
-                this._linkSelectorView = new LinkView({
-                    model: this.model.link,
-                    pageCollection: PageCollection.getInstance()
-                });
                 this.listenTo(this.model,"change:buttonAlignment",this.toggleTextAlignment);
             },  
             render:function () {
@@ -78,9 +74,6 @@
                 styleCheckedRadio.call(this, 'size');
                 styleCheckedRadio.call(this, 'buttonAlignment');
                 styleCheckedRadio.call(this, 'textAlignment');
-
-                this.actionBtn = this.$('.action-btn');
-                this.actionBtn.append(this._linkSelectorView.render({model:this.model.link}).el);
                 this.selectedIconEl = this.$('.selected-icon-wrapper');
                 this.btnBrowseIconsEl = this.$('.btn-browse-icons');
                 this.templateCardOption = this.$('.card-template-option');
@@ -240,6 +233,13 @@
              */
             _onChangeShowParams : function (){
                 if (this.model.existeBtn) {
+                    this._linkSelectorView = new LinkView({
+                        model: this.model.link,
+                        pageCollection: PageCollection.getInstance()
+                    });
+                    this.actionBtn = this.$('.action-btn');
+                    this.actionBtn.append(this._linkSelectorView.render({model:this.model.link}).el);
+                    
                     this.switchBtn.css('background-color', '#34d399');
                     this.switchBtn.find('span').css('float', 'right');
                     this.templateCardOption.show();
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/fr-ca/i18n.js	(révision 13132)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/fr-ca/i18n.js	(révision 13133)
@@ -1,7 +1,7 @@
 define(
     {
         "BLOCK_NAME": "Carte",
-        "cardBlockOption": "Options du carte",
+        "cardBlockOption": "Options de la carte",
         "block": "Bloc",
         "move": "Déplacer",
         "edit": "Éditer",
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/fr-fr/i18n.js	(révision 13132)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/fr-fr/i18n.js	(révision 13133)
@@ -1,7 +1,7 @@
 define(
     {
         "BLOCK_NAME": "Carte",
-        "cardBlockOption": "Options du carte",
+        "cardBlockOption": "Options de la carte",
         "block": "Bloc",
         "move": "Déplacer",
         "edit": "Éditer",
