Revision: r11428
Date: 2023-10-16 12:04:21 +0300 (lts 16 Okt 2023) 
Author: mpartaux 

## Commit message
add location question form

## Files changed

## Full metadata
------------------------------------------------------------------------
r11428 | mpartaux | 2023-10-16 12:04:21 +0300 (lts 16 Okt 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/FormTemplate.js

add location question form
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/FormTemplate.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/FormTemplate.js	(révision 11427)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/FormTemplate.js	(révision 11428)
@@ -104,6 +104,14 @@
                     "mandatory": false,
                     "underline": false
                 }, {
+                    "type": "text",
+                    "label": "Lieu de l'intervention",
+                    "description": "Saisissez le lieu de l'intervention",
+                    "placeholder": "Paris 13",
+                    "options": [],
+                    "mandatory": false,
+                    "underline": false
+                }, {
                     "type": "separator",
                     "label": "",
                     "description": "",
@@ -798,6 +806,14 @@
                     "mandatory": false,
                     "underline": false
                 }, {
+                    "type": "text",
+                    "label": "Lieu de l'intervention",
+                    "description": "Saisissez le lieu de l'intervention",
+                    "placeholder": "Québec",
+                    "options": [],
+                    "mandatory": false,
+                    "underline": false
+                }, {
                     "type": "separator",
                     "label": "",
                     "description": "",
@@ -1492,6 +1508,14 @@
                     "mandatory": false,
                     "underline": false
                 }, {
+                    "type": "text",
+                    "label": "Place of operation",
+                    "description": "Please enter the place of operation",
+                    "placeholder": "Quebec city",
+                    "options": [],
+                    "mandatory": false,
+                    "underline": false
+                }, {
                     "type": "separator",
                     "label": "",
                     "description": "",
@@ -2186,6 +2210,14 @@
                     "mandatory": false,
                     "underline": false
                 }, {
+                    "type": "text",
+                    "label": "Place of operation",
+                    "description": "Please enter the place of operation",
+                    "placeholder": "Paris 13",
+                    "options": [],
+                    "mandatory": false,
+                    "underline": false
+                }, {
                     "type": "separator",
                     "label": "",
                     "description": "",
@@ -2880,6 +2912,14 @@
                     "mandatory": false,
                     "underline": false
                 }, {
+                    "type": "text",
+                    "label": "Place of operation",
+                    "description": "Please enter the place of operation",
+                    "placeholder": "Ringwood",
+                    "options": [],
+                    "mandatory": false,
+                    "underline": false
+                }, {
                     "type": "separator",
                     "label": "",
                     "description": "",
@@ -4268,6 +4308,14 @@
                     "mandatory": false,
                     "underline": false
                 }, {
+                    "type": "text",
+                    "label": "Place of operation",
+                    "description": "Please enter the place of operation",
+                    "placeholder": "Wellington, Fl",
+                    "options": [],
+                    "mandatory": false,
+                    "underline": false
+                }, {
                     "type": "separator",
                     "label": "",
                     "description": "",
@@ -4962,6 +5010,14 @@
                     "mandatory": false,
                     "underline": false
                 }, {
+                    "type": "text",
+                    "label": "Lugar de operación",
+                    "description": "Por favor, introduzca el lugar de operación",
+                    "placeholder": "Paris 13",
+                    "options": [],
+                    "mandatory": false,
+                    "underline": false
+                }, {
                     "type": "separator",
                     "label": "",
                     "description": "",
@@ -5656,6 +5712,14 @@
                     "mandatory": false,
                     "underline": false
                 }, {
+                    "type": "text",
+                    "label": "Lugar de operación",
+                    "description": "Por favor, introduzca el lugar de operación",
+                    "placeholder": "Quebec City",
+                    "options": [],
+                    "mandatory": false,
+                    "underline": false
+                }, {
                     "type": "separator",
                     "label": "",
                     "description": "",
@@ -6350,6 +6414,14 @@
                     "mandatory": false,
                     "underline": false
                 }, {
+                    "type": "text",
+                    "label": "Lugar de operación",
+                    "description": "Por favor, introduzca el lugar de operación",
+                    "placeholder": "Wellington, Fl",
+                    "options": [],
+                    "mandatory": false,
+                    "underline": false
+                }, {
                     "type": "separator",
                     "label": "",
                     "description": "",
