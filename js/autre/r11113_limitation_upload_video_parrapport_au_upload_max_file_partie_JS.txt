Revision: r11113
Date: 2023-07-04 14:55:20 +0300 (tlt 04 Jol 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
limitation upload video parrapport au upload_max_file partie JS 

## Files changed

## Full metadata
------------------------------------------------------------------------
r11113 | srazanandralisoa | 2023-07-04 14:55:20 +0300 (tlt 04 Jol 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js

limitation upload video parrapport au upload_max_file partie JS 
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js
===================================================================
--- src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 11112)
+++ src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 11113)
@@ -63,8 +63,8 @@
                 maxFiles: 5,
                 maxSize: 20000000,
                 alertmsg: false,
-                maxSizeVideo: 15000000,
-                maxSizeFile : 5000000,
+                maxSizeVideo: __FILE_MAX_UPLOAD__,
+                maxSizeFile : (__FILE_MAX_UPLOAD__ < 5000000 )? __FILE_MAX_UPLOAD__: 5000000,
                 defaultPreview: undefined,
                 showMenu: true,
                 menuContainer: this.element,
@@ -246,6 +246,10 @@
             }
             this.currentUploadSize = totalSize;
         },
+        bitsToMegabits: function(bits) {
+            var megabits = bits / 1000000;
+            return megabits;
+        },
         _computeAcceptable: function() {
             var accept = '', refuse = '', extensions = '', refusedExt = '';
             var options = this.options;
@@ -326,7 +330,7 @@
                 }
                 else if (uploader._isVideo(files[i]) && files[i].size > this.options.maxSizeVideo) {
                         message = this.options.lang.uploadFailTooBig;
-                        this.errors.push(message.replace('%name%', files[i].name).replace('%filesize%', filesize).replace('%limite%', "15"));
+                        this.errors.push(message.replace('%name%', files[i].name).replace('%filesize%', filesize).replace('%limite%', this.bitsToMegabits(this.options.maxSizeVideo)));
                         files.rejected++;
                         if (files.length === 1)
                             this._onComplete();
@@ -333,7 +337,7 @@
                 } 
                 else if (!uploader._isVideo(files[i]) && files[i].size > this.options.maxSizeFile) {
                     message = this.options.lang.uploadFailTooBig;
-                    this.errors.push(message.replace('%name%', files[i].name).replace('%filesize%', filesize).replace('%limite%', "5"));
+                    this.errors.push(message.replace('%name%', files[i].name).replace('%filesize%', filesize).replace('%limite%', this.bitsToMegabits(this.options.maxSizeFile)));
                     files.rejected++;
                     if (files.length === 1)
                         this._onComplete();
@@ -495,8 +499,8 @@
             // amelioration message d'erreur pour l'upload image
             if (error !== 'abort') {
                 if(jqXHR.responseText == "Something went wrong : Check that the file isn't corrupted The uploaded file exceeds the upload_max_filesize directive in php.ini[]"){
-                    var filesize = Math.ceil(currentUpload.size / 1000000);
-                    var err = this.options.lang.uploadFailTooBig.replace('%name%', currentUpload.name).replace('%filesize%', filesize).replace('%limite%', "15");
+                    var filesize = Math.ceil(currentUpload.size  / (1024 * 1024));
+                    var err = this.options.lang.uploadFailTooBig.replace('%name%', currentUpload.name).replace('%filesize%', filesize).replace('%limite%',  this.bitsToMegabits(this.options.maxSizeVideo));
                     this.$message.find('p').text(err);
                 }else{
                     var err = this.options.lang.uploadFailServerError.replace('%name%', currentUpload.name).replace('%error%', error);
