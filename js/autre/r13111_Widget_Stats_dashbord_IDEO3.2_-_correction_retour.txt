Revision: r13111
Date: 2024-09-26 18:05:12 +0300 (lkm 26 Sep 2024) 
Author: rrakotoarinelina 

## Commit message
Widget Stats dashbord IDEO3.2 - correction retour 

## Files changed

## Full metadata
------------------------------------------------------------------------
r13111 | rrakotoarinelina | 2024-09-26 18:05:12 +0300 (lkm 26 Sep 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/DashBoard/DashBoard.js
   M /branches/ideo3_v2/integration/src/less/imports/dashboard.less

Widget Stats dashbord IDEO3.2 - correction retour 
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/DashBoard/DashBoard.js
===================================================================
--- src/js/JEditor/DashBoard/DashBoard.js	(révision 13110)
+++ src/js/JEditor/DashBoard/DashBoard.js	(révision 13111)
@@ -129,7 +129,7 @@
                             if (codeboutonsNonFr != null) {
                                 url = 'https://stats.linkeo.ovh/index.php?id=' + key + '&ideo3=1&action=dashboard&lang=' + lang;
                             } else {
-                                url = 'https://stats-gui-back-office.linkeo.ovh/?codebouton=' + codebouton + '&vhid=' + vhostId;
+                                url = 'https://stats-gui-back-office.linkeo.ovh/dashboard?codebouton=' + codebouton + '&vhid=' + vhostId;
                             }
 
                             //code
Index: src/less/imports/dashboard.less
===================================================================
--- src/less/imports/dashboard.less	(révision 13110)
+++ src/less/imports/dashboard.less	(révision 13111)
@@ -92,7 +92,7 @@
 	.stats
 	{
 		background-color: @mainColor;
-		height: 500px;
+		// height: 500px;
 	}
 	.nav-container
 	{
