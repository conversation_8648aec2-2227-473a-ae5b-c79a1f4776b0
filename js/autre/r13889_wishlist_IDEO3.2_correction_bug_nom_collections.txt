Revision: r13889
Date: 2025-02-20 11:42:13 +0300 (lkm 20 Feb 2025) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: correction bug nom collections

## Files changed

## Full metadata
------------------------------------------------------------------------
r13889 | s<PERSON><PERSON><PERSON><PERSON><PERSON> | 2025-02-20 11:42:13 +0300 (lkm 20 Feb 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/selectCollection.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/CollectionListView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/SelectCollectionView.js

wishlist IDEO3.2: correction bug nom collections
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/FilePanel/Templates/selectCollection.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/selectCollection.html	(révision 13888)
+++ src/js/JEditor/FilePanel/Templates/selectCollection.html	(révision 13889)
@@ -35,7 +35,7 @@
 
                     <span class="title">
                         <span class="icon-gallery"></span>
-                        <%= collection.name%>
+                        <%= collection.name[lang]%>
                     </span>
                 </div>
 
Index: src/js/JEditor/FilePanel/Views/CollectionListView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/CollectionListView.js	(révision 13888)
+++ src/js/JEditor/FilePanel/Views/CollectionListView.js	(révision 13889)
@@ -68,7 +68,7 @@
         }
         if (!this.app.params.dontAskAgainFor['deleteCollectionItem']) {
             this.confirm({
-                message: translate('confirmDelete', {'name': model.name}),
+                message: translate('confirmDelete', {'name': model.name[this.lang]}),
                 title: translate("deleteAction"),
                 type: 'delete',
                 onOk: _.bind(function() {
Index: src/js/JEditor/FilePanel/Views/SelectCollectionView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/SelectCollectionView.js	(révision 13888)
+++ src/js/JEditor/FilePanel/Views/SelectCollectionView.js	(révision 13889)
@@ -1,21 +1,21 @@
-define([
-	"jquery",
-	"underscore",
-	"text!../Templates/selectCollection.html",
-	"JEditor/Commons/Events",
-	"JEditor/FilePanel/Views/SelectCollectionView",
-	"JEditor/Commons/Ancestors/Views/DialogView",
-	"JEditor/Commons/Utils"
-,
-	"i18n!../nls/i18n"],function(	$,
-	_,
-	selectCollection,
-	Events,
-	SelectCollectionView,
-	DialogView,
-	Utils
-,
-	translate){
+define([
+	"jquery",
+	"underscore",
+	"text!../Templates/selectCollection.html",
+	"JEditor/Commons/Events",
+	"JEditor/FilePanel/Views/SelectCollectionView",
+	"JEditor/Commons/Ancestors/Views/DialogView",
+	"JEditor/Commons/Utils"
+,
+	"i18n!../nls/i18n"],function(	$,
+	_,
+	selectCollection,
+	Events,
+	SelectCollectionView,
+	DialogView,
+	Utils
+,
+	translate){
 var SelectCollectionView = DialogView.extend({
     className: 'selectCollection',
     events: {
@@ -44,6 +44,8 @@
     },
     initialize: function() {
         this._super();
+        if (!this.lang)
+        this.lang = this.app.params.defaultcontentlang;
         this._template = this.buildTemplate(selectCollection,translate);
         this.render();
     },
@@ -51,7 +53,7 @@
         this._super();
         this.selected = false;
         this.undelegateEvents();
-        this.$el.html(this._template({content:this.collection.models}));
+        this.$el.html(this._template({content:this.collection.models, lang:this.lang}));
         this.dom[this.cid].selections = this.$('.oneCollection');
         this.delegateEvents();
         return this;
@@ -72,6 +74,6 @@
         var cid = $target.data('cid');
         this.selected = cid;
     },
-});
-return SelectCollectionView;
+});
+return SelectCollectionView;
 });
\ No newline at end of file
