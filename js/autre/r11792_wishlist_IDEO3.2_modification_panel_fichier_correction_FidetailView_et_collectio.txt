Revision: r11792
Date: 2024-01-22 12:06:45 +0300 (lts 22 Jan 2024) 
Author: sraz<PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: modification panel fichier, correction FidetailView et collectionFiledetailView

## Files changed

## Full metadata
------------------------------------------------------------------------
r11792 | srazanandralisoa | 2024-01-22 12:06:45 +0300 (lts 22 Jan 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/FilePanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Models/FileCollection.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileDetailManager.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/CollectionDetailView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileDetailManagerView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/LinkView.js

wishlist IDEO3.2: modification panel fichier, correction FidetailView et collectionFiledetailView
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/FilePanel/FilePanel.js
===================================================================
--- src/js/JEditor/FilePanel/FilePanel.js	(révision 11791)
+++ src/js/JEditor/FilePanel/FilePanel.js	(révision 11792)
@@ -315,6 +315,12 @@
                                     for (var i = 0; i < files.length; i++) {
                                         if (files[i].cid == cid) {
                                             this.app.router.navigate('files/collections/' + collection.id + '/' + files[i].id, {trigger: false});
+                                            if (!files[i].title)
+                                            files[i].title = {};
+                                            if (!files[i].desc)
+                                                files[i].desc = {};
+                                            if (!files[i].link)
+                                                files[i].link = {};
                                             break;
                                         }
                                     }
@@ -325,7 +331,7 @@
                                 var self=this;
                                 var file = this.fileList.get(cid);
                                 var id= (file)?file.id:cid;
-                                this.fileListDetail = new FileCollection();
+                                this.fileListDetail = new FileCollection(true);
                                 this.fileListDetail.setId(id);
                                 this.fileListDetail.fetch({
                                     success :function(){
@@ -350,12 +356,15 @@
                                 this._byCID[cid].previous = orderedList[(orderedList.lastIndexOf(cid) + length - 1) % length];
                                 this._byCID[cid].next = orderedList[(orderedList.lastIndexOf(cid) + 1) % length];
                             }else{
-                                var keyArray=Object.keys(this.fileListDetail._byId);
-                                var idFile=(this.fileListDetail._byId[cid].id).toString();
+                                //var keyArray=Object.keys(this.fileListDetail.models);
+                                var keyArray = this.fileListDetail.models.map(function(monModele) {
+                                    return monModele.get('id');
+                                  });
+                                var idFile=(this.fileListDetail._byId[cid].id);
                                 var nextIndex = keyArray.indexOf(idFile) +1;
                                 var prevIndex = keyArray.indexOf(idFile) -1;
-                                var previousId=keyArray[prevIndex];
-                                var nextId=keyArray[nextIndex];
+                                var previousId = keyArray[prevIndex];
+                                var nextId = keyArray[nextIndex];
                                 if(!previousId)
                                     previousId=false;
                                 if(_.contains(nextId,"c"))
@@ -542,6 +551,21 @@
                                 this._orderedFilesInCollectionsCID[collection.cid] = [];
                             this._orderedFilesInCollectionsCID[collection.cid].push(model.cid);
                         },
+                        _updateFilesInCollectionDetailsView: function (collection) {
+                           // this._initDetailsView();
+                            var currentCollection = collection.cid;
+                            // if (!this._orderedFilesInCollectionsCID[currentCollection])
+                                this._orderedFilesInCollectionsCID[currentCollection] = [];
+                            collection.files.each(function (m, i, c) {
+                                delete this._byCID[m.cid];
+                                this._byCID[m.cid] = new FileDetailView({model: m, cid: currentCollection, fileList: this.fileList, languages: this.languages});
+                                this.listenTo(this._byCID[m.cid], Events.FileDetailManagerViewEvents.NAV_PREVNEXT, this.goToFile);
+                                this._byCID[m.cid].hide(false);
+                                this.$el.append(this._byCID[m.cid].el);
+                    
+                                this._orderedFilesInCollectionsCID[currentCollection].push(m.cid);
+                            }, this);
+                        },
                         _createOneCollectionDetailsView: function (model) {
                             //console.log('_createOneCollectionDetailsView');
                             /**
@@ -552,6 +576,8 @@
                             this.$el.append(this._byCID[model.cid].el);
                             this.listenTo(this._byCID[model.cid], Events.ListViewEvents.GOTO_COLLECTIONLIST, this.switchToCollections);
                             this.listenTo(this._byCID[model.cid], Events.ListViewEvents.DETAIL_FILE, this.goToFile);
+                            this.listenTo(this.childViews.collectionManagerUIView, Events.ListViewEvents.UPDATE_COLLECTION, this._updateFilesInCollectionDetailsView);
+                            this.listenTo(this._byCID[model.cid], Events.ListViewEvents.UPDATE_COLLECTION, this._updateFilesInCollectionDetailsView);
                             this.listenTo(this._byCID[model.cid], Events.ListViewEvents.UPLOADER_COMPLETE, this._createFilesInCollectionDetailsView);
                             this.listenTo(this._byCID[model.cid], Events.ListViewEvents.FILE_CLEARVIEW, this._deleteOneDetailView);
                             //this.listenTo(this._byCID[model.cid], Events.ListManagerViewEvents.UPDATE_LIST_REMOVE_TRUE, this.updateListeRemoveTrue);
Index: src/js/JEditor/FilePanel/Models/FileCollection.js
===================================================================
--- src/js/JEditor/FilePanel/Models/FileCollection.js	(révision 11791)
+++ src/js/JEditor/FilePanel/Models/FileCollection.js	(révision 11792)
@@ -10,13 +10,18 @@
         hasmore:true,
         countFile : 0,
         model:File,
-        initialize: function () {
+        // cette attribut pour verifier si on esat dans un detail de'un fichier
+        detail:false,
+        initialize: function (detail) {
             Collection.prototype.initialize.apply(this, arguments);
-            this.setOffset(0);
+           if(!detail) this.setOffset(0);
             this.on(Events.BackboneEvents.SYNC, this.onSync);
         },
         onSync: function (coll,resp) {
-            this.options.offset = this.length;
+            // si on est dans le detail on ne reset pas l'offset
+            if (!this.detail) {
+                this.options.offset = this.length;
+            }
             if(resp.length === this.options.rowcount)
                 this.hasmore=true;
             else
@@ -39,9 +44,13 @@
             });
             if(args.id){
                 url +=this.getId();
+                if (this.getOrderBy()) {
+                    url += "?orderBy=" +this.getOrderBy();
+                }
+                this.detail=true;
                 this.setId(null);
             }else{
-             
+                this.detail=false;
                 var i=0;
                 url+=_.reduce(args,function(memo,value,index){
                         var ret= memo+((i>0?"&":"?")+encodeURIComponent(index)+"="+encodeURIComponent(value));
Index: src/js/JEditor/FilePanel/Templates/fileDetailManager.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileDetailManager.html	(révision 11791)
+++ src/js/JEditor/FilePanel/Templates/fileDetailManager.html	(révision 11792)
@@ -28,7 +28,7 @@
             <span class="infobulles"><%= __("getFile")%></span>
         </a>
         <% if(user.can("delete_file")){ %>
-        <a href="#" data-delete="<%=cid%>">
+        <a href="#" data-delete="<%=id%>">
             <span class="icon-bin"></span>
             <span class="infobulles"><%= __("deleteFile")%></span>
         </a>
Index: src/js/JEditor/FilePanel/Views/CollectionDetailView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/CollectionDetailView.js	(révision 11791)
+++ src/js/JEditor/FilePanel/Views/CollectionDetailView.js	(révision 11792)
@@ -85,6 +85,7 @@
                         this.model.files.remove(model);
                         this.model.save();
                         this.selectNone();
+                        this.trigger(Events.ListViewEvents.UPDATE_COLLECTION, this.model);
                         this.trigger(Events.ListViewEvents.FILE_CLEARVIEW, model, this.model);
                     }, this),
                     options: {dialogClass: 'delete no-close', dontAskAgain: true, subject: 'deleteFileItem'}
@@ -181,6 +182,7 @@
                     that.collection.add(selected, { at: index });
 
                     that.model.save();
+                    that.trigger(Events.ListViewEvents.UPDATE_COLLECTION, that.model);
                 });
                 this.listenTo(selectFileView, Events.DialogEvents.CLOSE, function() {
                     this.stopListening(selectFileView);
@@ -189,15 +191,12 @@
                 selectFileView.imagesOnly();
                 selectFileView.open();
             return false;
-        },
-        addFileExist: function(model) {
-            this.collection.add(model);
-            this.model.save();
-        }       
+        },  
     });
     Events.extend({
         ListViewEvents: {
-            GOTO_COLLECTIONLIST: 'clickCollection'
+            GOTO_COLLECTIONLIST: 'clickCollection',
+            UPDATE_COLLECTION: 'updateCollection'
         },
         
     });
Index: src/js/JEditor/FilePanel/Views/FileDetailManagerView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileDetailManagerView.js	(révision 11791)
+++ src/js/JEditor/FilePanel/Views/FileDetailManagerView.js	(révision 11792)
@@ -126,7 +126,7 @@
                 type: 'delete',
                 onOk: _.bind(function() {
                     if (this.referrer) { // suppression du fichier de la collection
-                        var currentCollection = this.options.childViews.fileGroupList.collection.get(this.referrer);
+                        var currentCollection = this.options.childViews.fileGroupList.get(this.referrer);
                         currentCollection.files.remove(this.FileDetailView.model);
                         currentCollection.save();
                         this.trigger(Events.ListViewEvents.FILE_CLEARVIEW, this.FileDetailView.model, currentCollection);
@@ -135,6 +135,8 @@
                         var model = this.options.childViews.fileList.collection.get(cid);
                         this.trigger(Events.ListViewEvents.FILE_CLEARVIEW, model);
                         model.destroy();
+                        this.options.childViews.fileList.collection.setOffset(0);
+                        this.options.childViews.fileList.collection.fetch();
                     }
                     this.goBack();
                 }, this),
Index: src/js/JEditor/FilePanel/Views/LinkView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/LinkView.js	(révision 11791)
+++ src/js/JEditor/FilePanel/Views/LinkView.js	(révision 11792)
@@ -29,13 +29,13 @@
         this.listenTo(this.pageSelector, Events.ListViewEvents.SELECT, this._onPageSelect)
         this._template = this.buildTemplate(LinkSelector, translate);
         this.currentFile = this.model.getFile();
-        this.fileUploader = new FileUploaderView({
-          collection: FileDBCollection.getInstance(),
-          currentFile: this.currentFile
-        });
-        this.fileUploader.setType(FileUploaderView.UploadType.FILE);
-        this.listenTo(this.fileUploader, Events.FileUploaderEvents.UPLOAD, this._onUpload);
-        this.listenTo(this.fileUploader, Events.ListViewEvents.CHOOSE_FILE, this._useExistingFile);
+        // this.fileUploader = new FileUploaderView({
+        //   collection: FileDBCollection.getInstance(),
+        //   currentFile: this.currentFile
+        // });
+        // this.fileUploader.setType(FileUploaderView.UploadType.FILE);
+        // this.listenTo(this.fileUploader, Events.FileUploaderEvents.UPLOAD, this._onUpload);
+        // this.listenTo(this.fileUploader, Events.ListViewEvents.CHOOSE_FILE, this._useExistingFile);
         // initialise le target à vide puisqu'on auras pas besoin
         this.model.target = '';
 
@@ -92,9 +92,9 @@
           this.$el.radioPanel();
           this.dom[this.cid].pageSelectorTrigger = this.$('.dialog-view-trigger.page-selector');
           this.pageSelector.attach(this.dom[this.cid].pageSelectorTrigger);
-          this.$('.wrapper-uploader')
-            .append(this.fileUploader.el);
-          this.fileUploader.render();
+          // this.$('.wrapper-uploader')
+          //   .append(this.fileUploader.el);
+          // this.fileUploader.render();
         }
         this.delegateEvents();
         return this;
Index: src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js	(révision 11791)
+++ src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js	(révision 11792)
@@ -68,6 +68,7 @@
                         if (!this.parentCollection) {
                             for (var i = 0; i < model.files.models.length; i++)
                                 this.trigger(Events.ListViewEvents.FILE_CLEARVIEW, model.files.models[i], model, true);
+                            this.trigger(Events.ListViewEvents.UPDATE_COLLECTION, model);
                             model.destroy();
                         }
                         else {
@@ -74,6 +75,7 @@
                             this.parentCollection.files.remove(model);
                             
                             this.trigger(Events.ListViewEvents.FILE_CLEARVIEW, model, this.parentCollection);
+                            this.trigger(Events.ListViewEvents.UPDATE_COLLECTION, this.parentCollection);
                         }
                     }
                     if (this.parentCollection)
@@ -166,7 +168,7 @@
                         this.parentCollection.name = this.translate("newGallery") + Utils.dateFormat(this.translate('dateFormat'));
                     }
                     this.parentCollection.save();
-        
+                    this.trigger(Events.ListViewEvents.UPDATE_COLLECTION, this.parentCollection);
                 },
             });
             Object.defineProperties(CollectionManagerUIView.prototype, {
