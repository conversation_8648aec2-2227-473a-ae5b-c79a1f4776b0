Revision: r13783
Date: 2025-02-04 12:27:09 +0300 (tlt 04 Feb 2025) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2 : ajout de RS custom (correction regex de validation)

## Files changed

## Full metadata
------------------------------------------------------------------------
r13783 | srazanandralisoa | 2025-02-04 12:27:09 +0300 (tlt 04 Feb 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Models/Params.js

wishlist IDEO3.2 : ajout de RS custom (correction regex de validation)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Models/Params.js
===================================================================
--- src/js/JEditor/ParamsPanel/Models/Params.js	(révision 13782)
+++ src/js/JEditor/ParamsPanel/Models/Params.js	(révision 13783)
@@ -48,7 +48,7 @@
         borneoUrl:/^(https?\:\/\/)?(www\.)?borneoapp\.com\/(.*)/,
         boncoinUrl:/^(https?\:\/\/)?(www\.)?leboncoin\.fr\/(.*)/,
         pagesjauneUrl:/^(https?\:\/\/)?(www\.)?pagesjaunes\.fr\/(.*)/,
-        customrsUrl : /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/
+        customrsUrl : /^(https?:\/\/)?(www\.)?([\da-z\.-]+)\.([a-z\.]{2,6})((^\/)?.*)/
     };  
     //verification for google fonts
 
