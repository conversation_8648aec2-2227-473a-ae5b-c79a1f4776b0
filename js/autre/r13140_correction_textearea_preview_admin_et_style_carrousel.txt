Revision: r13140
Date: 2024-10-03 11:45:51 +0300 (lkm 03 Okt 2024) 
Author: frahajanirina 

## Commit message
correction textearea, preview admin et style carrousel

## Files changed

## Full metadata
------------------------------------------------------------------------
r13140 | frahajanirina | 2024-10-03 11:45:51 +0300 (lkm 03 Okt 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/CardStyleOption.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardBlock.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardList.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardStyleOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardBlockView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardStyleOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/en-au/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/en-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/gallery_style.less
   M /branches/ideo3_v2/integration/src/less/main.less

correction textearea, preview admin et style carrousel
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/CardStyleOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/CardStyleOption.js	(révision 13139)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/CardStyleOption.js	(révision 13140)
@@ -23,10 +23,11 @@
                             Arrow       :   0,
                             Autoplay: false,
                             Duration: 5000,
+                            centerOnScreen: false
                         }
                     }
             );
-            CardStyleOption.SetAttributes(['cardStyle', 'cardNbreCard','cardStyleAff', 'Arrow', 'Autoplay', 'Duration']);
+            CardStyleOption.SetAttributes(['cardStyle', 'cardNbreCard','cardStyleAff', 'Arrow', 'Autoplay', 'Duration', 'centerOnScreen']);
             
             return CardStyleOption;
         });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardBlock.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardBlock.html	(révision 13139)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardBlock.html	(révision 13140)
@@ -1,31 +1,12 @@
-<div class="blk-card">
-    <div class="blk-card__head">
-        <% if (card.icon) { %>
-            <span class="blk-card__icon"><%= card.icon %></span>
-        <% } %>
-        <p class="blk-card__title"><%= card.title ? __("cardOption")+' '+'"'+card.title+'"' : __("cardOption") %></p>
-    </div>
-    <div class="blk-card__content">
-        <% if (card.description) { %>
-            <p class="blk-card__description"><%= card.description %></p>
-        <% } %>
-        <ul class="blk-card__list">
-            <% _.each(card.options, function(option) { %>
-                <% if (option != '') { %>
-                    <li><%= option %></li>
-                <% } %>
-            <% }); %>
-        </ul>
-    </div>
-    <% if (card.existeBtn) { %>
-        <div class="blk-card__action">
-            <div class="blk-button block-button <%= card.color %> <%= card.size %> <%= card.buttonAlignment %> <%= card.textAlignment %>">
-                <a href="/" class="button blk-button__link">
-                    <span class="txt blk-button__label">
-                        <span><%= card.buttonText ||  __("button") %></span>
-                    </span>
-                </a>
+<div id="<%= uid %>">
+    <% if(empty){%>
+        <div class="empty-gallery"><span class="icon-card-style1"></span></div>
+    <% } else{ %>
+        <div class="exist-gallery">
+            <div>
+                <span class="icon-card-style1"></span>
+                <span class="count">&nbsp;<%=CountCard%></span>
             </div>
         </div>
-    <% } %>
-</div>
+    <% }%>
+</div>
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardList.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardList.html	(révision 13139)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardList.html	(révision 13140)
@@ -12,10 +12,10 @@
         <label><%=__("title")%> :</label>
         <input class="field-input" name="label" value="<%= title %>" type="text" placeholder="<%=__('title')%>"><br/>
         <label><%=__("description")%> :</label>
-        <textarea class="field-input" name="description" placeholder="<%=__('description')%>"><%= description %></textarea><br/>
+        <textarea class="field-input card-text-area" name="description" placeholder="<%=__('description')%>"><%= description %></textarea><br/>
         <div class="options">
             <label><%=__("cardFieldOptionsMessage")%> :</label>
-            <textarea name="options" class="options-list field-input" placeholder="<%=__('options')%>"><%= options.join("\n") %></textarea>
+            <textarea name="options" class="options-list field-input card-text-area" placeholder="<%=__('options')%>"><%= options.join("\n") %></textarea>
         </div><br/>
         <div class="option-content">
             <span class="switch card-icon" style="cursor: pointer;">
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardStyleOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardStyleOption.html	(révision 13139)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardStyleOption.html	(révision 13140)
@@ -14,7 +14,7 @@
                             <span class="bottom"></span>
                         </span>
                         <span class="container">
-                            <span class="icon icon-photocss-column"></span>
+                            <span class="icon icon-gallery-arrow-outer"></span>
                             <span class="switch-container">
                                 <span class="radio">
                                     <span></span>
@@ -76,6 +76,17 @@
             </span>
             <span class="text"><%=__("activeAutoPlay")%></span>
           </label>
+          <div id="centre-ecran-card-block">
+            <% var _id=_.uniqueId('CentreEcran') %>
+            <input type="checkbox" class="blue-bg centreEcran" name="CentreEcran" id="<%=_id %>" <%=centerOnScreen?'checked="checked"':''%>>
+            <label for="<%=_id %>">
+              <span class="checkbox-wrapper">
+                <span class="icon-unchecked"></span>
+                <span class="icon-checked"></span>
+              </span>
+              <span class="text"><%=__("centreEcran")%></span>
+            </label>
+          </div>
         </div>
     </div>
     <div class="defilement-cran <%= (cardStyle === 1) ? 'hidden' : '' %>">
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardBlockView.js	(révision 13139)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardBlockView.js	(révision 13140)
@@ -18,7 +18,7 @@
                 class: "block cardblock buttonblock"
             },
             initialize: function() {
-                BlockView.prototype.initialize.apply(this, arguments);
+                this._super();
                 this.template = this.buildTemplate(template, translate);
                 // Écoute de l'événement
                 this.listenTo(this.model.options.cardOption, 'add:card', this.render);
@@ -27,32 +27,25 @@
                 this.listenTo(this.model.options.cardOption, "cards:reordered", this.render); 
                 this.listenTo(this.model.options.cardOption, "change", this.render);   
             },
-            _onLoad: function() {
-                this.render();
-            },
             render: function() {
                 this._super();
-                var cards = this.model.options.cardOption.cards;
-                this.$('.content').empty();
 
-                _.each(cards, function(card) {
-                    // Crée un élément pour la carte
-                    var cardElement = $(this.template({ card: card }));
+                return this;
+            },
+            renderOptions: function(model, options) {
+                var uid, opts, styleOpts,  htmlContent, cards;
+                uid = _.uniqueId('card');
+                opts = this.model.options.cardOption;
+                styleOpts = this.model.options.CardStyleOption;
+                cards = this.model.options.cardOption.cards;
+                if (cards.length != 0) {
+                    CountCard = cards.length;
+                    htmlContent = this.template({uid: uid, empty: false, CountCard: CountCard});
+                } else {
+                    htmlContent = this.template({uid: uid,  empty: true});
+                }
+                this.$('.content').html(htmlContent);
 
-                    // Vérifie si l'icône n'est pas vide
-                    if (card.icon && card.icon !== "") {
-                        var svgCollectionObj = new SvgCollection({ "svgName": card.icon });
-                        svgCollectionObj.fetchIcons(_.bind(function(error, svg) {
-                            if (error) {
-                                console.error(error);
-                            } else {
-                                cardElement.find(".blk-card__icon").html(svg.content);
-                            }
-                        }, this));
-                    }
-                    this.$('.content').append(cardElement);
-                }, this);
-
                 return this;
             }
         });
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardStyleOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardStyleOptionView.js	(révision 13139)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardStyleOptionView.js	(révision 13140)
@@ -23,8 +23,9 @@
                 'slidechange .card-nbreImage .slider'   :   '_onSliderChangeNbreCard',
                 'change input[type="radio"].select-box': '_onStyleAffichageChange',
                 'click input[type=radio].card-arrow':'_onChangeRadio',
-                'change input[type="checkbox"].blue-bg': '_onChangeAutoplay',
+                'change input[name="Autoplay"]': '_onChangeAutoplay',
                 'slidechange .defilement-cran .slider': 'onSliderChangeDefilement',
+                'change input[name="CentreEcran"]':'_onChangeCentreEcran'
             },
             initialize: function() {
                 this._super();
@@ -38,7 +39,8 @@
                     cardStyleAff:this.model.cardStyleAff,
                     Arrow:this.model.Arrow,
                     Autoplay:this.model.Autoplay,
-                    Duration:this.model.Duration
+                    Duration:this.model.Duration,
+                    centerOnScreen:this.model.centerOnScreen
                 };
                 this.$el.html(this.template(templateVars));
                 this.dom[this.cid].autoplay = this.$('.defilement');
@@ -116,15 +118,19 @@
             _showHide :function(){
                 this.dom[this.cid].autoplay.addClass('hidden');
                 this.dom[this.cid].cran.addClass('hidden');
+                $('#centre-ecran-card-block').addClass('hidden');
                 if (this.model.Arrow == 2 ) {
                     this.dom[this.cid].autoplay.removeClass('hidden');
                     if (this.model.Autoplay) {
                         this.dom[this.cid].cran.removeClass('hidden');
+                        $('#centre-ecran-card-block').removeClass('hidden');
                     }
                 }
             },
             _onChangeAutoplay: function(){
                 this.model.Autoplay = !this.model.Autoplay;
+                $('.centreEcran').prop('checked', false);
+                this.model.centerOnScreen = false;
                 this._showHide();       
             },
             onSliderChangeDefilement: function(event, ui) {
@@ -132,6 +138,9 @@
                 this.model.Duration=value;
                 
                 return false;
+            },
+            _onChangeCentreEcran: function(){
+                this.model.centerOnScreen = !this.model.centerOnScreen;
             }, 
         });
         return CardStyleOptionView;
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/en-au/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/en-au/i18n.js	(révision 13139)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/en-au/i18n.js	(révision 13140)
@@ -19,8 +19,8 @@
         "browseIconsLegend":"Browse icons",
         "showButtonLegend": "Show a button",
         "addButton": "Button text",
-        "styleDeGrille" : "Grid Style",
-        "cardStyleLegend" : "Apply a grid style",
+        "styleDeGrille" : "Display Style",
+        "cardStyleLegend" : "Apply a style",
         "masonryLegend"         :   "Carousel",
         "gridLegend"            :   "Grid",
         "cardNombreCard"       :   "Number of cards",
@@ -58,6 +58,7 @@
         "justifyAlignButton": "Justified",
         "alignText": "Text alignment",
         "alignTextLegend": "Select the alignment of your text in the button.",
-        "button": "Button"
+        "button": "Button",
+        "centreEcran"           :  "Center on the screen the “active” element "
     }
 );
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/en-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/en-ca/i18n.js	(révision 13139)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/en-ca/i18n.js	(révision 13140)
@@ -19,8 +19,8 @@
         "browseIconsLegend":"Browse icons",
         "showButtonLegend": "Show a button",
         "addButton": "Button text",
-        "styleDeGrille" : "Grid Style",
-        "cardStyleLegend" : "Apply a grid style",
+        "styleDeGrille" : "Display Style",
+        "cardStyleLegend" : "Apply a style",
         "masonryLegend"         :   "Carousel",
         "gridLegend"            :   "Grid",
         "cardNombreCard"       :   "Number of cards",
@@ -58,6 +58,7 @@
         "justifyAlignButton": "Justified",
         "alignText": "Text alignment",
         "alignTextLegend": "Select the alignment of your text in the button.",
-        "button": "Button"
+        "button": "Button",
+        "centreEcran"           :  "Center on the screen the “active” element "
     }
 );
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/fr-ca/i18n.js	(révision 13139)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/fr-ca/i18n.js	(révision 13140)
@@ -19,8 +19,8 @@
         "browseIconsLegend":"Parcourir les icônes",
         "showButtonLegend": "Afficher un bouton",
         "addButton": "Texte du bouton",
-        "styleDeGrille" : "Style de grille",
-        "cardStyleLegend" : "Appliquez un style de grille",
+        "styleDeGrille" : "Style d’affichage",
+        "cardStyleLegend" : "Appliquez un style",
         "masonryLegend"         :   "Carousel",
         "gridLegend"            :   "Grille",
         "cardNombreCard"       :   "Nombre de cartes",
@@ -62,6 +62,7 @@
         "leftAlignText": "Aligné à gauche",
         "centerAlignText": "Centré",
         "rightAlignText": "Aligné à droite",
-        "button": "Bouton"
+        "button": "Bouton",
+        "centreEcran"           :  "Centrer à l'écran l'élément “actif”"
     }
 );
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/fr-fr/i18n.js	(révision 13139)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/fr-fr/i18n.js	(révision 13140)
@@ -19,8 +19,8 @@
         "browseIconsLegend":"Parcourir les icônes",
         "showButtonLegend": "Afficher un bouton",
         "addButton": "Texte du bouton",
-        "styleDeGrille" : "Style de grille",
-        "cardStyleLegend" : "Appliquez un style de grille",
+        "styleDeGrille" : "Style d’affichage",
+        "cardStyleLegend" : "Appliquez un style",
         "masonryLegend"         :   "Carousel",
         "gridLegend"            :   "Grille",
         "cardNombreCard"       :   "Nombre de cartes",
@@ -62,6 +62,7 @@
         "leftAlignText": "Aligné à gauche",
         "centerAlignText": "Centré",
         "rightAlignText": "Aligné à droite",
-        "button": "Bouton"
+        "button": "Bouton",
+        "centreEcran"           :  "Centrer à l'écran l'élément “actif”"
     }
 );
\ No newline at end of file
Index: src/less/imports/gallery_style.less
===================================================================
--- src/less/imports/gallery_style.less	(révision 13139)
+++ src/less/imports/gallery_style.less	(révision 13140)
@@ -90,7 +90,7 @@
     border: 1px solid #ffffff;
     .border-radius(4px);
 
-    .icon-gallery, .icon-transition-horizontal, .icon-gallery-arrow-outer, .icon-photogrid-block {
+    .icon-gallery, .icon-transition-horizontal, .icon-gallery-arrow-outer, .icon-photogrid-block, .icon-card-style1 {
         top: 50%;
         left: 50%;
         .translate(-50%, -50%);
Index: src/less/main.less
===================================================================
--- src/less/main.less	(révision 13139)
+++ src/less/main.less	(révision 13140)
@@ -2927,4 +2927,7 @@
   pointer-events: none;
   opacity: .5;
 }
+.card-text-area {
+  min-height: 130px;
+}
 
