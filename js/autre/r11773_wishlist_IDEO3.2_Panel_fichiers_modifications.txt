Revision: r11773
Date: 2023-12-26 08:37:27 +0300 (tlt 26 Des 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2 : Panel fichiers modifications

## Files changed

## Full metadata
------------------------------------------------------------------------
r11773 | srazana<PERSON>lisoa | 2023-12-26 08:37:27 +0300 (tlt 26 Des 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/FilePanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/collectionDetail.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/collectionManagerUIView.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileDetailManager.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileManagerUIView.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/CollectionDetailView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileManagerUIView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/filePanel.less
   M /branches/ideo3_v2/integration/src/less/main.less

wishlist IDEO3.2 : Panel fichiers modifications
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/FilePanel/FilePanel.js
===================================================================
--- src/js/JEditor/FilePanel/FilePanel.js	(révision 11772)
+++ src/js/JEditor/FilePanel/FilePanel.js	(révision 11773)
@@ -221,6 +221,7 @@
                             this._hideAllViews();
                             this.childViews.collectionManagerUIView.listView = this._byCID[cid];
                             this.childViews.collectionManagerUIView.parentCollection = this.fileGroupList.get(cid);
+                            this.childViews.collectionManagerUIView.render();
                             this.childViews.collectionManagerUIView.show();
                             this._byCID[cid].selectNone();
                             this._byCID[cid].render();
Index: src/js/JEditor/FilePanel/Templates/collectionDetail.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/collectionDetail.html	(révision 11772)
+++ src/js/JEditor/FilePanel/Templates/collectionDetail.html	(révision 11773)
@@ -37,6 +37,9 @@
                     <li class="action goToFile" data-cid="<%=file.cid %>" >
                         <span class="icon-add"></span>
                     </li>
+                    <li class="action replaceFile" data-func="replaceFile" data-cid="<%=file.cid %>" >
+                        <span class="icon-refresh"></span>
+                    </li>
                 </ul>
                 <%if(!file.isImg()){%>
                 <div class="icon-file ext">
Index: src/js/JEditor/FilePanel/Templates/collectionManagerUIView.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/collectionManagerUIView.html	(révision 11772)
+++ src/js/JEditor/FilePanel/Templates/collectionManagerUIView.html	(révision 11773)
@@ -15,10 +15,15 @@
         </ul>
 
     </div>
-
+    <% if(add){ %>
+    <a href="#"  class="file-action" data-action="addFileExist">
+        <span class="icon-add-image"></span>
+        <span class="infobulles"><%= __("addFileExist")%></span>
+    </a>
+    <%}%>
     <!-- ACTIONS -->
     <% if(user.can("delete_file")){ %>
-        <a href="#" class="file-action" data-action="delete">
+        <a href="#" class="file-action disabable" role="link" aria-disabled="true"  data-action="delete">
             <span class="icon-bin"></span>
             <span class="infobulles"><%= __("deleteSelection")%></span>
         </a>
Index: src/js/JEditor/FilePanel/Templates/fileDetailManager.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileDetailManager.html	(révision 11772)
+++ src/js/JEditor/FilePanel/Templates/fileDetailManager.html	(révision 11773)
@@ -5,11 +5,11 @@
     </a>
 
     <div class="right-actions">
-        <a href="#" class="file-action" data-action="addSelectionToNewCollection">
+        <a href="#" class="file-action disabable" data-action="addSelectionToNewCollection">
             <span class="icon-new-collection"></span>
             <span class="infobulles"><%= __("createNewCollection")%></span>
         </a>
-        <a href="#" class="file-action" data-action="selectCollection">
+        <a href="#" class="file-action disabable" data-action="selectCollection">
             <span class="icon-gallery"></span>
             <span class="infobulles"><%= __("addToExistingCollection")%></span>
         </a>
Index: src/js/JEditor/FilePanel/Templates/fileManagerUIView.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileManagerUIView.html	(révision 11772)
+++ src/js/JEditor/FilePanel/Templates/fileManagerUIView.html	(révision 11773)
@@ -56,18 +56,18 @@
     </div>
 
     <!-- ACTIONS -->
-    <a href="#" class="file-action" data-action="addSelectionToNewCollection">
+    <a href="#" class="file-action disabable" role="link" aria-disabled="true" data-action="addSelectionToNewCollection">
         <span class="icon-new-collection"></span>
         <span class="infobulles"><%= __("createNewCollection")%></span>
     </a>
 <% if(fileGroupList.length){%>
-    <a href="#" class="file-action" data-action="selectCollection">
+    <a href="#" class="file-action disabable" role="link" aria-disabled="true" data-action="selectCollection">
         <span class="icon-gallery"></span>
         <span class="infobulles"><%= __("addToExistingCollection")%></span>
     </a>
 <%}%>
 <% if(user.can("delete_file")){%>
-    <a href="#" class="file-action" data-action="delete">
+    <a href="#" class="file-action disabable" role="link" aria-disabled="true" data-action="delete">
         <span class="icon-bin"></span>
         <span class="infobulles"><%= __("deleteSelection")%></span>
     </a>
Index: src/js/JEditor/FilePanel/Views/CollectionDetailView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/CollectionDetailView.js	(révision 11772)
+++ src/js/JEditor/FilePanel/Views/CollectionDetailView.js	(révision 11773)
@@ -9,6 +9,8 @@
     "JEditor/Commons/Utils",
     "JEditor/Commons/Files/Models/File",
     "JEditor/App/Messages/ClipboardModule", 
+    "JEditor/FilePanel/Models/FileCollection",
+    "JEditor/Commons/Files/Views/FileSelectorDialog",
     "i18n!../nls/i18n",
     //not in params
     "jqueryPlugins/uploader"
@@ -22,6 +24,8 @@
         Utils,
         File,
         ClipboardModule,
+        FileCollection,
+        FileSelectorDialog,
         translate) {
     var CollectionDetailView = ListView.extend({
         selected: {},
@@ -39,12 +43,15 @@
             'uploadercomplete .group-content': 'uploadercomplete',
             'click [data-action="showuploader"]': 'showUploader',
             'sortstop .group-content': 'onSortStop',
-            'click .shortcode-collection.shortcode' : 'copyToClipboard'
+            'click .shortcode-collection.shortcode' : 'copyToClipboard',
+            'click .file .action.replaceFile[data-cid]': 'replaceFile'   
         },
         initialize: function() {
             this._super();
             this._template = this.buildTemplate(collectionDetail, translate);
             this._emptyTemplate = this.buildTemplate(emptyCollection, translate);
+            this.fileCollection =  new FileCollection();
+            this.fileCollection.fetch();
         },
         toggleSelected: function(e) {
             var $target = $(e.currentTarget);
@@ -156,6 +163,37 @@
         copyToClipboard : function (e){
             ClipboardModule.copyToClipboard(e);
         },
+        resetFilter: function(){
+            this.fileCollection.resetFilter();
+          //  this.fileCollection.fetch();
+        },
+        replaceFile: function(e) {
+            var $target = $(e.currentTarget);
+            var cid = $target.data('cid');
+            var index = this.collection.indexOf( this.collection.get(cid));
+
+                var selectFileView, that = this;
+                this.resetFilter();
+                selectFileView = new FileSelectorDialog({collection: this.fileCollection});
+                
+                this.listenTo(selectFileView, Events.ListViewEvents.CHOOSE_FILE, function(selected) {
+                    that.collection.remove(this.collection.get(cid));
+                    that.collection.add(selected, { at: index });
+
+                    that.model.save();
+                });
+                this.listenTo(selectFileView, Events.DialogEvents.CLOSE, function() {
+                    this.stopListening(selectFileView);
+                    selectFileView.remove();
+                });
+                selectFileView.imagesOnly();
+                selectFileView.open();
+            return false;
+        },
+        addFileExist: function(model) {
+            this.collection.add(model);
+            this.model.save();
+        }       
     });
     Events.extend({
         ListViewEvents: {
Index: src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js	(révision 11772)
+++ src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js	(révision 11773)
@@ -7,14 +7,17 @@
     "JEditor/Commons/Ancestors/Views/ListManagerView",
     "JEditor/Commons/Utils",
     "JEditor/Commons/Files/Models/FileGroup",
+    "JEditor/FilePanel/Models/FileCollection",
+    "JEditor/Commons/Files/Views/FileSelectorDialog",
     "i18n!../nls/i18n",
     //not in params
     "jqueryPlugins/dropdown"
 ],
         function($, _, collectionManagerUIView, Events, CollectionManagerUIView,
-                ListManagerView, Utils, FileGroup, translate) {
+                ListManagerView, Utils, FileGroup, FileCollection, FileSelectorDialog, translate) {
             var CollectionManagerUIView = ListManagerView.extend({
                 events: {
+                    'click .file-action[data-action="addFileExist"]': 'addFileExist',
                 },
                 parentCollection: false,
                 initialize: function() {
@@ -24,13 +27,21 @@
                         throw('il manque une listView en paramètre');
                     }
                     this.listenTo(this.listView, Events.ListViewEvents.SELECT, this.onSelect);
+                    this.fileCollection =  new FileCollection();
+                    this.fileCollection.fetch();
                 },
                 render: function() {
                     this._super();
                     this.undelegateEvents();
-                    this.$el.html(this._template( {user: this.app.user}));
+                    this.$el.html(this._template( 
+                        {
+                            user: this.app.user,
+                            add: (this.parentCollection)? true : false
+                        }
+                    ));
                     this.dom[this.cid].selectionDropdown = this.$('.selection-dropdown-toggle').dropdown();
                     this.dom[this.cid].selection = this.$('.selection');
+                    this.dom[this.cid].fileAction = this.$('.file-action.disabable');
                     this.dom[this.cid].selectedLength = this.$('.icon-check');
                     this.delegateEvents();
                     return this;
@@ -41,9 +52,11 @@
                     if (length) {
                         this.dom[this.cid].selectedLength.text(length);
                         this.dom[this.cid].selection.addClass('active');
+                        this.dom[this.cid].fileAction.attr('aria-disabled','false');
                     } else if( this.dom[this.cid].selectedLength ) {
                         this.dom[this.cid].selectedLength.text('');
                         this.dom[this.cid].selection.removeClass('active');
+                        this.dom[this.cid].fileAction.attr('aria-disabled','true');
                     }
                     return false;
                 },
@@ -117,7 +130,44 @@
                         }
                         model.save();
                     }
-                }
+                },
+                resetFilter: function(){
+                    this.fileCollection.resetFilter();
+                  //  this.fileCollection.fetch();
+                },
+                addFileExist: function (){
+                    var selectFileView, that = this;
+                    this.resetFilter();
+                    selectFileView = new FileSelectorDialog({collection: this.fileCollection, allowMultipleSelect: true});
+                    
+                    this.listenTo(selectFileView, Events.ListViewEvents.CHOOSE_FILE, this._useExistingFile);
+                    this.listenTo(selectFileView, Events.DialogEvents.CLOSE, function() {
+                        this.stopListening(selectFileView);
+                        selectFileView.remove();
+                    });
+                    selectFileView.imagesOnly();
+                    selectFileView.open();
+            return false;
+                },
+                _useExistingFile: function(selected) {
+                    var file, id;
+                    if (!selected)
+                        return;
+                    for (id in selected) {
+                        file = selected[id];
+                        if (!file)
+                            continue;
+                        this.parentCollection.files.add(file);
+                        this.selected[file.id] = false;
+                    }
+                    if (!this.parentCollection.name) {
+                        var date = new Date();
+                        var dateString = date;
+                        this.parentCollection.name = this.translate("newGallery") + Utils.dateFormat(this.translate('dateFormat'));
+                    }
+                    this.parentCollection.save();
+        
+                },
             });
             Object.defineProperties(CollectionManagerUIView.prototype, {
                 typeList: {
@@ -129,6 +179,11 @@
                     }
                 }
             });
-            
+            Events.extend({
+                ListViewEvents: {
+                    ADD_TO_COLLECTION : 'addCollection',
+                },
+                
+            });
             return CollectionManagerUIView;
         });
\ No newline at end of file
Index: src/js/JEditor/FilePanel/Views/FileManagerUIView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileManagerUIView.js	(révision 11772)
+++ src/js/JEditor/FilePanel/Views/FileManagerUIView.js	(révision 11773)
@@ -83,6 +83,7 @@
                     this.dom[this.cid].sortDropdownParent = this.dom[this.cid].sortDropdown.parent();
                     this.dom[this.cid].sortDropdownItems = this.dom[this.cid].sortDropdownParent.find('.dropdown-menu a');
                     this.dom[this.cid].selection = this.$('.selection');
+                    this.dom[this.cid].fileAction = this.$('.file-action.disabable');
                     this.dom[this.cid].selectedLength = this.$('.icon-check');
                     this.dom[this.cid].labelSort = this.$('.sort-dropdown-toggle .text');
                     this.dom[this.cid].selectCollection = this.$('[data-action="selectCollection"]');
@@ -127,9 +128,11 @@
                     if (length) {
                         this.dom[this.cid].selectedLength.text(length);
                         this.dom[this.cid].selection.addClass('active');
+                        this.dom[this.cid].fileAction.attr('aria-disabled','false');
                     } else {
                         this.dom[this.cid].selectedLength.text('');
                         this.dom[this.cid].selection.removeClass('active');
+                        this.dom[this.cid].fileAction.attr('aria-disabled','true')
                     }
                     return false;
                 },
Index: src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 11772)
+++ src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 11773)
@@ -128,4 +128,5 @@
     "FileOn"    :   "fichier(s) sur",
     "ImageAddToCollection" : " L’image à bien été ajoutée à la collection",
     "fontExiste": "Cette font existe déjà",
+    "addFileExist": "Ajouter des images existantes"
 });
\ No newline at end of file
Index: src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 11772)
+++ src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 11773)
@@ -132,4 +132,5 @@
     "FileOn"    :   "fichier(s) sur",
     "ImageAddToCollection" : " L’image à bien été ajoutée à la collection",
     "fontExiste": "Cette font existe déjà",
+    "addFileExist": "Ajouter des images existantes"
 });
\ No newline at end of file
Index: src/js/JEditor/FilePanel/nls/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/i18n.js	(révision 11772)
+++ src/js/JEditor/FilePanel/nls/i18n.js	(révision 11773)
@@ -134,6 +134,7 @@
         "FileOn"    :   "file(s) on",
         "ImageAddToCollection" : " The image has been added to the collection",
         "fontExiste":"This font already exists",
+        "addFileExist": "Add existing images",
     },
     "fr-fr": true, "fr-ca":true
 })
\ No newline at end of file
Index: src/less/imports/filePanel.less
===================================================================
--- src/less/imports/filePanel.less	(révision 11772)
+++ src/less/imports/filePanel.less	(révision 11773)
@@ -926,6 +926,10 @@
     }
 
 }
+.my-files.collectionDetail .file ul li:nth-child(1) {
+    font-size: 14px;
+    margin-top: 0px;
+}
 /* CountFile*/
 .top-bar .countFile {
     padding: 6px 10px;
@@ -1440,7 +1444,7 @@
     top: -10.5px; left: -10.5px;
 }
 
-#files .select {
+#files .my-files .select {
     cursor: pointer;
     opacity: 0;
     position: absolute;
Index: src/less/main.less
===================================================================
--- src/less/main.less	(révision 11772)
+++ src/less/main.less	(révision 11773)
@@ -2901,4 +2901,8 @@
     }
   }
 }
+a[aria-disabled="true"] {
+  pointer-events: none;
+  opacity: .5;
+}
 
