Revision: r13377
Date: 2024-10-24 14:21:02 +0300 (lkm 24 Okt 2024) 
Author: frahajanirina 

## Commit message
Wishlist IDEO3.2: Ajout RS + modifier dropdown liste RS

## Files changed

## Full metadata
------------------------------------------------------------------------
r13377 | frahajanirina | 2024-10-24 14:21:02 +0300 (lkm 24 Okt 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Models/Params.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Templates/ListSocialNetwork.html
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Templates/SocialNetwork.html
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/SocialNetworks.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/components.less

Wishlist IDEO3.2: Ajout RS + modifier dropdown liste RS
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Models/Params.js
===================================================================
--- src/js/JEditor/ParamsPanel/Models/Params.js	(révision 13376)
+++ src/js/JEditor/ParamsPanel/Models/Params.js	(révision 13377)
@@ -44,7 +44,10 @@
         wazeUrl:/^(https?\:\/\/)?(www\.)?waze\.com\/(.*)/,
         whatsappUrl:/^(https?\:\/\/)?(www\.)?(api\.whatsapp\.com|wa\.me)\/(.*)/,
         slideshareUrl:/^(https?\:\/\/)?(www\.)?((fr|de|es|pt)\.)?slideshare\.net\/(.*)/,
-        quotes:/^(https?:\/\/)?(www\.)?(?!.*['"]).+\/(.*)$/
+        quotes:/^(https?:\/\/)?(www\.)?(?!.*['"]).+\/(.*)$/,
+        borneoUrl:/^(https?\:\/\/)?(www\.)?borneoapp\.com\/(.*)/,
+        boncoinUrl:/^(https?\:\/\/)?(www\.)?leboncoin\.fr\/(.*)/,
+        pagesjauneUrl:/^(https?\:\/\/)?(www\.)?pagesjaunes\.fr\/(.*)/
     };  
     //verification for google fonts
 
@@ -106,7 +109,7 @@
         },
         initialize: function () {
             this._super();
-            this.on("change:facebookUrl change:twitterUrl change:xUrl change:pinterestUrl change:mybusinessUrl change:youtubeUrl change:linkedinUrl change:viadeoUrl change:instagramUrl change:skypeUrl change:theforkUrl change:tiktokUrl change:tripadvisorUrl change:wazeUrl change:whatsappUrl change:slideshareUrl", this.onSocialNetworkUrlChange);
+            this.on("change:facebookUrl change:twitterUrl change:xUrl change:pinterestUrl change:mybusinessUrl change:youtubeUrl change:linkedinUrl change:viadeoUrl change:instagramUrl change:skypeUrl change:theforkUrl change:tiktokUrl change:tripadvisorUrl change:wazeUrl change:whatsappUrl change:slideshareUrl change:borneoUrl change:boncoinUrl change:pagesjauneUrl", this.onSocialNetworkUrlChange);
             this.on("change:MarqueClient", this.onMarqueClientChange);
         },
         onSocialNetworkUrlChange: function (model) {
@@ -113,7 +116,8 @@
             var changed = this.changedAttributes();
             var that = this;
             _.each(changed, function (value, key) {
-                if ((key === "facebookUrl" || key === "twitterUrl" || key === "xUrl" || key === "pinterestUrl" || key === "mybusinessUrl" || key==="instagramUrl"||key==="youtubeUrl"||key==="linkedinUrl"||key==="viadeoUrl" || key==="skypeUrl" || key==="theforkUrl" || key ==="tiktokUrl" || key ==="tripadvisorUrl" || key==="wazeUrl" || key==="whatsappUrl" || key==="slideshareUrl")) {
+                if ((key === "facebookUrl" || key === "twitterUrl" || key === "xUrl" || key === "pinterestUrl" || key === "mybusinessUrl" || key==="instagramUrl"||key==="youtubeUrl"||key==="linkedinUrl"||key==="viadeoUrl" || 
+                    key==="skypeUrl" || key==="theforkUrl" || key ==="tiktokUrl" || key ==="tripadvisorUrl" || key==="wazeUrl" || key==="whatsappUrl" || key==="slideshareUrl" || key==="borneoUrl" || key==="boncoinUrl" || key==="pagesjauneUrl")) {
                     that.trigger(Events.SettingsEvents.SOCIAL_NETWORK__CHANGE, key, value, model);
                 }
             });
@@ -140,7 +144,9 @@
             
        
 
-            var socialNetworks = ["facebookUrl", "mybusinessUrl", "twitterUrl", "xUrl", "pinterestUrl", "youtubeUrl", "linkedinUrl", "viadeoUrl", "instagramUrl","skypeUrl","theforkUrl","tiktokUrl","tripadvisorUrl","wazeUrl","whatsappUrl","slideshareUrl"];
+            var socialNetworks = ["facebookUrl", "mybusinessUrl", "twitterUrl", "xUrl", "pinterestUrl", "youtubeUrl", "linkedinUrl", "viadeoUrl", "instagramUrl","skypeUrl","theforkUrl","tiktokUrl","tripadvisorUrl","wazeUrl","whatsappUrl","slideshareUrl", 
+                "borneoUrl", "boncoinUrl", "pagesjauneUrl"
+            ];
             //en gros c'est ça dans une boucle:
             for (var i = 0; i < socialNetworks.length; i++) {             
                 if (attributes[socialNetworks[i]] && (!attributes[socialNetworks[i]].match || !attributes[socialNetworks[i]].match(regExes[socialNetworks[i]]) || !attributes[socialNetworks[i]].match(quotesRegex)))
Index: src/js/JEditor/ParamsPanel/Templates/ListSocialNetwork.html
===================================================================
--- src/js/JEditor/ParamsPanel/Templates/ListSocialNetwork.html	(révision 13376)
+++ src/js/JEditor/ParamsPanel/Templates/ListSocialNetwork.html	(révision 13377)
@@ -445,6 +445,46 @@
                         <input class="content rs" type="text" name="<%=social.type%>_<%=i %>" value='<%-slideshareUrl%>' data-titleId="<%=social.type%>_<%=i %>" data-reseaux="slideshare" />
                     </span>
                 <% } %>
+                <% if( social.type =="borneoUrl") { %>
+                    <span class="inline-params__name  borneo">
+                        <svg width="20px" height="20px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+                            <path fill="#FF6B00" fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.58172 0 0 3.58172 0 8V42C0 46.4183 3.58172 50 8 50H42C46.4183 50 50 46.4183 50 42V8C50 3.58172 46.4183 0 42 0H8ZM25.8493 9.35071C26.4571 9.64483 26.451 10.4668 26.0302 10.9984C23.9052 13.6828 22.4695 18.8002 22.4695 24.6717C22.4695 30.5432 23.9052 35.6605 26.0302 38.3449C26.451 38.8765 26.4571 39.6984 25.8493 39.9926C25.3746 40.2223 24.8819 40.3433 24.3765 40.3433C20.5281 40.3433 17.4083 33.3269 17.4083 24.6717C17.4083 16.0164 20.5281 9 24.3765 9C24.8819 9 25.3746 9.12096 25.8493 9.35071ZM40 25.1163C40 28.2878 38.2529 30.8588 36.0976 30.8588C33.9424 30.8588 33.1785 28.2878 33.1785 25.1163C33.1785 21.9448 33.9424 19.3737 36.0976 19.3737C38.2529 19.3737 40 21.9448 40 25.1163ZM33.017 11.6307C33.7259 11.925 33.72 12.8697 33.2569 13.4866C31.4459 15.8991 30.2444 20.1396 30.2444 24.968C30.2444 29.7964 31.4459 34.037 33.2569 36.4495C33.72 37.0663 33.7259 38.011 33.017 38.3053C32.6066 38.4757 32.1823 38.5649 31.7481 38.5649C28.163 38.5649 25.2566 32.4774 25.2566 24.968C25.2566 17.4586 28.163 11.3711 31.7481 11.3711C32.1823 11.3711 32.6066 11.4604 33.017 11.6307ZM15.5925 36.659C16.2867 37.1724 17.0673 36.3645 16.7309 35.5648C15.4878 32.609 14.7677 29.1009 14.7677 25.3385C14.7677 21.3082 15.594 17.5697 17.0043 14.4894C17.3663 13.6987 16.6098 12.8604 15.8989 13.3537C12.3547 15.8134 10 20.1619 10 25.1161C10 29.9261 12.2196 34.1652 15.5925 36.659Z"/>
+                        </svg>
+                            
+                        <% 
+                            var borneoUrl= (social.title ==="")?__("Borneo"):social.title;
+                        %>
+                        <input class="content rs" type="text" name="<%=social.type%>_<%=i %>" value='<%-borneoUrl%>' data-titleId="<%=social.type%>_<%=i %>" data-reseaux="borneo" />
+                    </span>
+                <% } %>
+                <% if( social.type =="boncoinUrl") { %>
+                    <span class="inline-params__name  leboncoin">
+                        <svg width="20px" height="20px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+                            <path d="M0 8C0 3.58172 3.58172 0 8 0H42C46.4183 0 50 3.58172 50 8V42C50 46.4183 46.4183 50 42 50H8C3.58172 50 0 46.4183 0 42V8Z" fill="#FDFDFD"/>
+                            <path d="M20 24C20 21.7909 21.7909 20 24 20H50L20 50V24Z" fill="#FE6D14"/>
+                            <path d="M50 46C50 48.2091 48.2091 50 46 50L20 50L50 20L50 46Z" fill="#954009"/>
+                        </svg>
+                            
+                        <% 
+                            var boncoinUrl= (social.title ==="")?__("Boncoin"):social.title;
+                        %>
+                        <input class="content rs" type="text" name="<%=social.type%>_<%=i %>" value='<%-boncoinUrl%>' data-titleId="<%=social.type%>_<%=i %>" data-reseaux="leboncoin" />
+                    </span>
+                <% } %>
+                <% if( social.type =="pagesjauneUrl") { %>
+                    <span class="inline-params__name  pagesjaunes">
+                        <svg width="20px" height="20px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+                            <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.58172 0 0 3.58172 0 8V42C0 46.4183 3.58172 50 8 50H42C46.4183 50 50 46.4183 50 42V8C50 3.58172 46.4183 0 42 0H8Z" fill="#FCEB23"/>
+                            <path d="M10.375 19.7917C10.375 20.9167 11.2917 21.8333 12.4584 21.8333H12.5001C13.6251 21.8333 14.5417 20.9167 14.5417 19.75V17.4583C14.5417 16.3333 13.5834 15.375 12.4584 15.375C11.3334 15.375 10.375 16.2917 10.375 17.4583V19.7917Z" fill="black"/>
+                            <path d="M19.8334 39.4167C21.625 40.0417 23.5417 40.375 25.4584 40.375C27.125 40.375 28.7501 40.125 30.3334 39.5833C36.6251 37.4167 40.25 31.3333 40.25 22.8333V17.4583C40.25 16.2917 39.3334 15.375 38.1667 15.375C37 15.375 36.0834 16.2917 36.0834 17.4583V22.8333C36.0834 29.5 33.5834 34.0417 29 35.625C24.0834 37.2917 17.7917 35.0833 14.0417 30.3333C14.0001 30.3333 14 30.2917 14 30.2917C13.2917 29.375 11.9584 29.25 11.0834 29.9583C10.1667 30.7083 10.0001 32 10.7084 32.9167C13.0834 35.9167 16.25 38.1667 19.8334 39.4167Z" fill="black"/>
+                        </svg>
+                            
+                        <% 
+                            var pagesjauneUrl= (social.title ==="")?__("Pagesjaune"):social.title;
+                        %>
+                        <input class="content rs" type="text" name="<%=social.type%>_<%=i %>" value='<%-pagesjauneUrl%>' data-titleId="<%=social.type%>_<%=i %>" data-reseaux="pagesjaunes" />
+                    </span>
+                <% } %>
                 <label>
                     <span class="custom-input">
                         <input type="text" data-autosave="true" name="<%=social.type%>_<%=i %>" class="field-input neutral-input  bold" value="<%=social.url%>" />
Index: src/js/JEditor/ParamsPanel/Templates/SocialNetwork.html
===================================================================
--- src/js/JEditor/ParamsPanel/Templates/SocialNetwork.html	(révision 13376)
+++ src/js/JEditor/ParamsPanel/Templates/SocialNetwork.html	(révision 13377)
@@ -17,7 +17,7 @@
           <a class="button" data-toggle="dropdown">
             <span class="txt"><span><%=__("addPage")%></span></span>
           </a>
-          <ul class="dropdown-menu shadow thin-border radius">
+          <ul class="dropdown-menu shadow thin-border radius social-network">
             <li>
                 <a  data-id="airbnbUrl" class="airbnb">
                     <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#FF5A5F" d="M0 8a8 8 0 0 1 8-8h34a8 8 0 0 1 8 8v34a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z"/><path fill="#fff" d="M40.167 32c-.167-.4-.334-.833-.5-1.2-.267-.6-.534-1.167-.767-1.7l-.033-.033C36.567 24.067 34.1 19 31.5 14l-.1-.2c-.267-.5-.533-1.033-.8-1.567-.333-.6-.667-1.233-1.2-1.833-1.067-1.333-2.6-2.067-4.233-2.067-1.667 0-3.167.734-4.267 2-.5.6-.867 1.234-1.2 1.834-.267.533-.533 1.066-.8 1.566l-.1.2C16.233 18.933 13.733 24 11.433 29l-.033.067c-.233.533-.5 1.1-.767 1.7-.166.366-.333.766-.5 1.2-.433 1.233-.566 2.4-.4 3.6a7.084 7.084 0 0 0 4.334 5.533 6.846 6.846 0 0 0 2.7.533 8.807 8.807 0 0 0 4.2-1.2c1.366-.766 2.666-1.866 4.133-3.466 1.467 1.6 2.8 2.7 4.133 3.466a8.807 8.807 0 0 0 4.2 1.2c.934 0 1.867-.166 2.7-.533 2.334-.933 3.967-3.067 4.334-5.533.266-1.167.133-2.334-.3-3.567Zm-15.034 1.733c-1.8-2.266-2.966-4.4-3.366-6.2-.167-.766-.2-1.433-.1-2.033.066-.533.266-1 .533-1.4.633-.9 1.7-1.467 2.933-1.467 1.234 0 2.334.534 2.934 1.467.266.4.466.867.533 1.4.1.6.067 1.3-.1 2.033-.4 1.767-1.567 3.9-3.367 6.2Zm13.3 1.567a4.953 4.953 0 0 1-3.033 3.9 5.05 5.05 0 0 1-2.533.333c-.834-.1-1.667-.366-2.534-.866-1.2-.667-2.4-1.7-3.8-3.234 2.2-2.7 3.534-5.166 4.034-7.366a8.355 8.355 0 0 0 .166-2.834 5.408 5.408 0 0 0-.9-2.266c-1.033-1.5-2.766-2.367-4.7-2.367-1.933 0-3.666.9-4.7 2.367a5.41 5.41 0 0 0-.9 2.266 6.92 6.92 0 0 0 .167 2.834c.5 2.2 1.867 4.7 4.033 7.4-1.366 1.533-2.6 2.566-3.8 3.233-.866.5-1.7.767-2.533.867a5.33 5.33 0 0 1-2.533-.334 4.954 4.954 0 0 1-3.034-3.9 5.519 5.519 0 0 1 .3-2.6c.1-.333.267-.666.434-1.066.233-.534.5-1.1.766-1.667l.034-.067A364.37 364.37 0 0 1 20.7 14.967l.1-.2c.267-.5.533-1.034.8-1.534.267-.533.567-1.033.933-1.466.7-.8 1.634-1.234 2.667-1.234s1.967.434 2.667 1.234c.366.433.666.933.933 1.466.267.5.533 1.034.8 1.534l.1.2c2.533 4.966 5 10.033 7.3 15V30c.267.533.5 1.133.767 1.667.166.4.333.733.433 1.066.267.867.367 1.7.233 2.567Z"/></svg>
@@ -25,12 +25,6 @@
                 </a>
             </li>
             <li>
-                <a  data-id="bookingUrl" class="booking">
-                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#0C3B7C" d="M0 8a8 8 0 0 1 8-8h34a8 8 0 0 1 8 8v34a8 8 0 0 1-8 8H0V8Z"/><path fill="#fff" d="m22.724 32.907-4.027-.004v-4.815c0-1.029.399-1.564 1.28-1.687h2.747c1.96 0 3.227 1.236 3.227 3.235 0 2.054-1.236 3.27-3.227 3.271ZM18.697 19.92v-1.267c0-1.11.47-1.637 1.498-1.705h2.062c1.766 0 2.825 1.057 2.825 2.828 0 1.348-.726 2.922-2.761 2.922h-3.624V19.92Zm9.17 4.806-.729-.41.636-.543c.74-.636 1.98-2.066 1.98-4.532 0-3.779-2.93-6.216-7.465-6.216h-5.175v-.002h-.59a2.526 2.526 0 0 0-2.434 2.495v21.44h8.301c5.04 0 8.293-2.744 8.293-6.994 0-2.288-1.05-4.244-2.818-5.238Z"/><path fill="#00BAFC" d="M32.846 33.995a2.955 2.955 0 0 1 2.948-2.963 2.963 2.963 0 0 1 0 5.926 2.956 2.956 0 0 1-2.948-2.963Z"/></svg>
-                    Booking.com
-                </a>
-            </li>
-            <li>
                 <a  data-id="appleUrl" class="apple">
                     <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#000" d="M0 8a8 8 0 0 1 8-8h34a8 8 0 0 1 8 8v34a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z"/><path fill="#fff" d="M30.65 8.275c-2.075 3.025-4.75 3.55-5.575 3.55-.275 0-.425-.025-.425-.025s-.15-3.5 1.8-5.95c2.7-3.35 5.775-3.125 5.775-3.125s.2 2.925-1.575 5.55Z M40.75 30.375s-.1.25-.25.675c-.325.8-.925 2.2-1.625 3.7-.05.075-.1.15-.15.25-1.475 2.6-3.775 5.325-5.7 5.7-1.509.302-2.487-.154-3.572-.659-.982-.457-2.05-.954-3.678-.966-1.356-.013-2.246.423-3.137.86-.884.432-1.77.865-3.113.865-2.375 0-4.9-2.6-6.85-6.05a26.728 26.728 0 0 1-1.75-3.725c-.04-.06-.064-.137-.085-.204l-.015-.046a1.7 1.7 0 0 0-.05-.125 1.805 1.805 0 0 1-.05-.125 4.863 4.863 0 0 1-.2-.55.19.19 0 0 1-.05-.1c-.525-1.55-.9-3.1-1.05-4.55 0-.066-.007-.125-.014-.18-.005-.05-.011-.098-.011-.145-.15-1.45-.1-2.75.05-3.95.25-1.775.85-4.2 2.45-6.25 2.025-2.625 4.75-3.375 6.7-3.375 2.072 0 3.127.49 4.08.931.782.363 1.495.694 2.645.694.875 0 1.64-.311 2.523-.671 1.211-.494 2.647-1.079 4.902-1.079 4.525 0 6.925 3.725 6.925 3.725l-.3.225c-.75.575-2.625 2.275-3.425 4.875a6.261 6.261 0 0 0-.175.65v.025A7.864 7.864 0 0 0 35.6 23c.1 1.55.5 2.8 1.075 3.8a8.459 8.459 0 0 0 3.05 3.075c.45.275.8.425.95.475.025.025.05.025.05.025h.025Z"/></svg>
                     Apple Store
@@ -37,15 +31,18 @@
                 </a>
             </li>
             <li>
-                <a  data-id="googleplayUrl" class="googleplay">
-                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#EA4335" d="M24.625 24 5.375 44.125c.625 2.125 2.625 3.75 5 3.75 1 0 1.875-.25 2.625-.75L34.75 34.75 24.625 24Z"/><path fill="#FBBC04" d="m44.125 20.5-9.375-5.375-10.5 9.25L34.875 34.75l9.375-5.25C45.875 28.625 47 26.875 47 25c-.125-1.875-1.25-3.625-2.875-4.5Z"/><path fill="#4285F4" d="M5.375 5.875c-.125.375-.125.875-.125 1.375v35.625c0 .5 0 .875.125 1.375l20-19.625-20-18.75Z"/><path fill="#34A853" d="m24.75 25 10-9.875-21.625-12.25c-.75-.5-1.75-.75-2.75-.75-2.375 0-4.5 1.625-5 3.75L24.75 25Z"/></svg>
-                    Google Play
+                <a  data-id="bookingUrl" class="booking">
+                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#0C3B7C" d="M0 8a8 8 0 0 1 8-8h34a8 8 0 0 1 8 8v34a8 8 0 0 1-8 8H0V8Z"/><path fill="#fff" d="m22.724 32.907-4.027-.004v-4.815c0-1.029.399-1.564 1.28-1.687h2.747c1.96 0 3.227 1.236 3.227 3.235 0 2.054-1.236 3.27-3.227 3.271ZM18.697 19.92v-1.267c0-1.11.47-1.637 1.498-1.705h2.062c1.766 0 2.825 1.057 2.825 2.828 0 1.348-.726 2.922-2.761 2.922h-3.624V19.92Zm9.17 4.806-.729-.41.636-.543c.74-.636 1.98-2.066 1.98-4.532 0-3.779-2.93-6.216-7.465-6.216h-5.175v-.002h-.59a2.526 2.526 0 0 0-2.434 2.495v21.44h8.301c5.04 0 8.293-2.744 8.293-6.994 0-2.288-1.05-4.244-2.818-5.238Z"/><path fill="#00BAFC" d="M32.846 33.995a2.955 2.955 0 0 1 2.948-2.963 2.963 2.963 0 0 1 0 5.926 2.956 2.956 0 0 1-2.948-2.963Z"/></svg>
+                    Booking.com
                 </a>
             </li>
             <li>
-                <a  data-id="vimeoUrl" class="vimeo">
-                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#19B1E3" d="M0 8a8 8 0 0 1 8-8h34a8 8 0 0 1 8 8v34a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z"/><path fill="#fff" d="M42.38 16.857c.222-4.422-1.437-6.743-4.974-6.854-4.754-.11-7.96 2.543-9.618 8.07.884-.442 1.769-.552 2.543-.552 1.768 0 2.542.994 2.321 2.984-.11 1.216-.884 2.875-2.321 5.196-1.437 2.211-2.543 3.427-3.206 3.427-.884 0-1.769-1.769-2.543-5.196-.22-1.105-.774-3.758-1.437-7.848-.663-3.87-2.21-5.749-4.975-5.417-1.216.11-2.874 1.216-5.085 3.095-.553.442-1.437 1.216-2.542 2.21-1.106.996-1.99 1.77-2.543 2.212l1.658 2.1c1.548-1.105 2.432-1.658 2.653-1.658 1.216 0 2.322 1.88 3.317 5.527.332 1.106.774 2.875 1.437 5.086.663 2.21 1.105 3.98 1.437 5.085 1.437 3.648 3.095 5.527 5.085 5.527 3.206 0 7.186-2.984 11.829-9.065 4.532-6.08 6.854-10.612 6.965-13.929Z"/></svg>
-                    Vimeo
+                <a  data-id="borneoUrl" class="borneo">
+                    <svg width="20px" height="20px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+                        <path fill="#FF6B00" fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.58172 0 0 3.58172 0 8V42C0 46.4183 3.58172 50 8 50H42C46.4183 50 50 46.4183 50 42V8C50 3.58172 46.4183 0 42 0H8ZM25.8493 9.35071C26.4571 9.64483 26.451 10.4668 26.0302 10.9984C23.9052 13.6828 22.4695 18.8002 22.4695 24.6717C22.4695 30.5432 23.9052 35.6605 26.0302 38.3449C26.451 38.8765 26.4571 39.6984 25.8493 39.9926C25.3746 40.2223 24.8819 40.3433 24.3765 40.3433C20.5281 40.3433 17.4083 33.3269 17.4083 24.6717C17.4083 16.0164 20.5281 9 24.3765 9C24.8819 9 25.3746 9.12096 25.8493 9.35071ZM40 25.1163C40 28.2878 38.2529 30.8588 36.0976 30.8588C33.9424 30.8588 33.1785 28.2878 33.1785 25.1163C33.1785 21.9448 33.9424 19.3737 36.0976 19.3737C38.2529 19.3737 40 21.9448 40 25.1163ZM33.017 11.6307C33.7259 11.925 33.72 12.8697 33.2569 13.4866C31.4459 15.8991 30.2444 20.1396 30.2444 24.968C30.2444 29.7964 31.4459 34.037 33.2569 36.4495C33.72 37.0663 33.7259 38.011 33.017 38.3053C32.6066 38.4757 32.1823 38.5649 31.7481 38.5649C28.163 38.5649 25.2566 32.4774 25.2566 24.968C25.2566 17.4586 28.163 11.3711 31.7481 11.3711C32.1823 11.3711 32.6066 11.4604 33.017 11.6307ZM15.5925 36.659C16.2867 37.1724 17.0673 36.3645 16.7309 35.5648C15.4878 32.609 14.7677 29.1009 14.7677 25.3385C14.7677 21.3082 15.594 17.5697 17.0043 14.4894C17.3663 13.6987 16.6098 12.8604 15.8989 13.3537C12.3547 15.8134 10 20.1619 10 25.1161C10 29.9261 12.2196 34.1652 15.5925 36.659Z"/>
+                    </svg>
+                        
+                    <%=__("Borneo")%>
                 </a>
             </li>
             <li>
@@ -55,58 +52,33 @@
                 </a>
             </li>
             <li>
-                <a  data-id="snapchatUrl" class="snapchat">
-                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#FFFC00" d="M0 8a8 8 0 0 1 8-8h34a8 8 0 0 1 8 8v34a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z"/><path fill="#fff" d="M25.227 42.186a5.38 5.38 0 0 1-.279-.008c-.06.006-.12.008-.183.008-2.18 0-3.637-1.03-4.923-1.94-.922-.652-1.792-1.267-2.81-1.436a9.08 9.08 0 0 0-1.48-.125c-.865 0-1.55.134-2.05.232-.308.06-.573.111-.778.111-.215 0-.475-.047-.584-.419-.086-.292-.148-.576-.208-.85-.149-.682-.257-1.099-.513-1.138-2.734-.422-4.35-1.044-4.671-1.796a.698.698 0 0 1-.057-.237.439.439 0 0 1 .367-.458c2.171-.358 4.102-1.505 5.739-3.412 1.267-1.477 1.89-2.887 1.957-3.043a.27.27 0 0 1 .01-.02c.315-.64.378-1.194.187-1.644-.353-.831-1.52-1.201-2.291-1.446a8.265 8.265 0 0 1-.518-.176c-.685-.27-1.81-.841-1.66-1.63.109-.574.87-.975 1.484-.975.17 0 .321.03.449.09.694.325 1.319.49 1.856.49.668 0 .99-.254 1.068-.326a110.69 110.69 0 0 0-.065-1.095c-.157-2.494-.353-5.598.441-7.379 2.377-5.328 7.417-5.743 8.905-5.743a379.787 379.787 0 0 0 .736-.007c1.492 0 6.543.415 8.921 5.747.794 1.781.598 4.889.441 7.386l-.007.12c-.022.338-.042.66-.059.97.074.068.369.301.97.325.512-.02 1.1-.184 1.748-.487.2-.095.422-.114.573-.114.228 0 .46.044.652.125l.01.004c.55.195.91.585.919.995.006.382-.277.953-1.674 1.504a8.428 8.428 0 0 1-.518.176c-.773.245-1.938.615-2.29 1.445-.192.45-.13 1.004.186 1.644l.01.021c.098.228 2.45 5.59 7.696 6.454a.44.44 0 0 1 .367.458.705.705 0 0 1-.058.24c-.32.748-1.935 1.369-4.67 1.791-.256.04-.365.454-.512 1.134a12.5 12.5 0 0 1-.209.844c-.08.272-.256.405-.54.405h-.044a4.38 4.38 0 0 1-.778-.099 10.24 10.24 0 0 0-2.05-.217 9.09 9.09 0 0 0-1.48.125c-1.017.17-1.887.783-2.807 1.434-1.289.911-2.747 1.942-4.926 1.942Z"/><path fill="#020202" d="M25.356 8.253c1.405 0 6.24.378 8.52 5.487.75 1.682.558 4.73.404 7.179-.025.388-.048.765-.068 1.124l-.008.155.103.115c.042.046.432.454 1.3.487l.014.001h.014c.574-.022 1.22-.2 1.922-.529a.939.939 0 0 1 .386-.072c.166 0 .342.031.487.093l.022.008c.368.13.628.372.632.592.002.125-.09.573-1.395 1.088a8 8 0 0 1-.49.165c-.848.27-2.13.676-2.562 1.693-.243.573-.176 1.249.198 2.01.154.358 2.576 5.815 8.028 6.714a.24.24 0 0 1-.023.087c-.092.218-.68.97-4.334 1.534-.572.087-.712.728-.874 1.474a12.66 12.66 0 0 1-.2.814c-.026.084-.03.089-.12.089h-.043c-.162 0-.408-.034-.694-.09a10.72 10.72 0 0 0-2.135-.225c-.506 0-1.028.044-1.551.131-1.119.186-2.028.83-2.991 1.51-1.294.914-2.63 1.86-4.67 1.86-.09 0-.177-.003-.264-.007l-.023-.001-.023.002c-.05.004-.1.006-.152.006-2.04 0-3.377-.945-4.67-1.86-.963-.68-1.873-1.324-2.991-1.51a9.48 9.48 0 0 0-1.551-.131c-.907 0-1.617.139-2.135.24-.285.056-.532.103-.694.103-.132 0-.135-.007-.163-.103-.082-.278-.142-.554-.2-.82-.163-.746-.303-1.391-.875-1.479-3.654-.564-4.241-1.317-4.334-1.534a.266.266 0 0 1-.023-.09c5.452-.897 7.874-6.354 8.029-6.715.374-.76.44-1.435.197-2.009-.432-1.016-1.714-1.423-2.562-1.692a8.187 8.187 0 0 1-.49-.166c-1.102-.435-1.44-.874-1.39-1.14.058-.306.59-.617 1.053-.617.104 0 .195.017.263.048.752.353 1.44.532 2.042.532.947 0 1.37-.441 1.415-.491l.102-.115-.008-.154c-.02-.36-.043-.736-.068-1.124-.153-2.448-.346-5.494.404-7.177 2.27-5.09 7.083-5.483 8.504-5.483l.652-.006h.083Zm0-.878h-.093l-.645.007c-.832 0-2.497.118-4.286.907a9.55 9.55 0 0 0-2.734 1.813c-.943.896-1.712 2-2.284 3.284-.838 1.878-.639 5.043-.48 7.585l.002.004.051.834a1.467 1.467 0 0 1-.616.116c-.473 0-1.034-.15-1.67-.448a1.493 1.493 0 0 0-.635-.132c-.379 0-.777.112-1.123.315-.435.256-.717.617-.793 1.017-.05.265-.048.788.534 1.32.32.292.79.561 1.396.8.159.063.348.123.548.187.694.22 1.745.554 2.019 1.199.138.326.079.757-.178 1.277l-.019.042c-.064.15-.661 1.503-1.887 2.93a10.61 10.61 0 0 1-2.278 2.016 8.377 8.377 0 0 1-3.198 1.249.878.878 0 0 0-.735.916c.008.129.038.258.09.382l.002.003c.179.418.594.774 1.27 1.088.824.383 2.057.705 3.665.958.081.154.166.542.224.808.061.283.125.573.216.882.098.334.352.733 1.005.733.248 0 .532-.055.862-.12.483-.094 1.143-.223 1.967-.223.457 0 .93.04 1.407.12.918.152 1.71.712 2.627 1.36 1.34.947 2.859 2.02 5.178 2.02.064 0 .127-.002.19-.006.076.004.171.006.272.006 2.32 0 3.837-1.073 5.177-2.02l.002-.002c.917-.647 1.708-1.206 2.626-1.359.477-.079.95-.12 1.407-.12.787 0 1.41.101 1.967.21.363.071.645.106.862.106h.043c.478 0 .829-.262.963-.721.089-.302.152-.586.215-.873.054-.25.142-.649.223-.805 1.609-.252 2.842-.574 3.666-.957.674-.312 1.089-.668 1.269-1.085.054-.125.085-.254.092-.386a.878.878 0 0 0-.734-.916c-5.012-.826-7.27-5.976-7.363-6.194a.61.61 0 0 0-.02-.043c-.256-.52-.316-.95-.176-1.277.273-.645 1.323-.978 2.018-1.199.2-.063.39-.123.548-.186.684-.27 1.173-.563 1.495-.896.385-.396.46-.776.456-1.025-.012-.602-.472-1.137-1.204-1.398a2.152 2.152 0 0 0-.816-.157 1.82 1.82 0 0 0-.758.155c-.587.275-1.112.425-1.563.446a1.414 1.414 0 0 1-.517-.114l.045-.727.006-.108c.16-2.544.36-5.711-.478-7.591-.574-1.288-1.346-2.395-2.293-3.293a9.572 9.572 0 0 0-2.744-1.813 10.914 10.914 0 0 0-4.285-.9Z"/></svg>
-                    Snapchat
+                <a  data-id="deliverooUrl" class="deliveroo">
+                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#35BEB2" d="M0 8a8 8 0 0 1 8-8h34a8 8 0 0 1 8 8v34a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z"/><path fill="#fff" d="M22.037 25.173a.044.044 0 0 0 .027-.018c.006-.01.009-.021.006-.033l-2.422-11.28a.103.103 0 0 1 .014-.076.1.1 0 0 1 .063-.044l7.413-1.548c.13-.027.21.024.238.155l2.386 11.066a.014.014 0 0 0 .014.012c.003 0 .007 0 .01-.003a.015.015 0 0 0 .006-.008.348.348 0 0 0 .014-.07c.467-4.382.932-8.763 1.393-13.145.008-.068.02-.113.038-.134a.122.122 0 0 1 .111-.046l7.608.8a.107.107 0 0 1 .09.075.106.106 0 0 1 .003.041c-.678 6.413-1.358 12.833-2.036 19.26a3.97 3.97 0 0 1-.326 1.21 1295.113 1295.113 0 0 1-4.187 9.25.125.125 0 0 1-.137.07l-19.371-4.056a.08.08 0 0 1-.062-.062l-1.928-9.01a.1.1 0 0 1 .014-.075.099.099 0 0 1 .062-.043l10.959-2.288Zm3.435 3.69c.241-1.398-2.155-2.012-2.497-.597-.265 1.098.32 1.678 1.409 1.637.586-.023.994-.498 1.088-1.04Zm5.455.893a1.123 1.123 0 0 0-.663-1.438l-.252-.093a1.114 1.114 0 0 0-1.434.665l-.088.24a1.123 1.123 0 0 0 .662 1.437l.253.093a1.115 1.115 0 0 0 1.434-.664l.088-.24Z"/></svg>
+                    Deliveroo
                 </a>
             </li>
             <li>
-                <a  data-id="yelpUrl" class="yelp">
-                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#D32323" d="M42 0H8a8 8 0 0 0-8 8v34a8 8 0 0 0 8 8h34a8 8 0 0 0 8-8V8a8 8 0 0 0-8-8Z"/><path fill="#fff" d="M24.25 7.25c-.125-.458-.5-.792-1-.958-1.583-.417-7.75 1.333-8.875 2.539-.375.375-.5.879-.375 1.292.167.375 7.792 12.542 7.792 12.542 1.125 1.833 2.041 1.583 2.333 1.458.293-.083 1.208-.375 1.125-2.539-.167-2.541-.917-13.792-1-14.334Zm-3.292 22.833c.625-.166 1.042-.75 1.084-1.458.041-.75-.334-1.417-.959-1.667l-1.747-.708c-5.957-2.5-6.208-2.583-6.5-2.583-.458-.042-.879.208-1.166.666-.586.959-.834 4.042-.625 6.084.083.666.208 1.25.375 1.583.25.458.625.75 1.083.75.293 0 .458-.042 6-1.833l2.455-.834Zm3.125 2.292c-.666-.293-1.416-.125-1.833.417l-1.208 1.458c-4.167 5-4.334 5.208-4.459 5.5a1.237 1.237 0 0 0-.083.542c.012.283.131.55.333.75.959 1.166 5.584 2.916 7.084 2.666a1.275 1.275 0 0 0 1.041-.837c.084-.293.125-.5.125-6.375v-2.663c.084-.625-.333-1.208-1-1.458Zm14.5 1.172c-.25-.167-.416-.25-5.957-2.084 0 0-2.417-.833-2.458-.833-.586-.25-1.25-.042-1.709.542-.459.584-.541 1.333-.166 1.916l.958 1.625c3.292 5.417 3.542 5.792 3.75 6 .375.293.833.334 1.333.125 1.417-.586 4.459-4.5 4.667-6.041.082-.505-.043-.964-.418-1.255v.005Zm-8.916-6.167c.879-.167 8.041-1.875 8.625-2.292.375-.25.586-.708.541-1.25v-.041c-.166-1.584-2.875-5.664-4.208-6.334-.458-.208-.958-.208-1.333.042-.25.167-.417.417-3.792 5.083l-1.542 2.119c-.416.5-.416 1.209 0 1.834.417.667.834.998 1.709.834v.005Z"/></svg>
-                    Yelp
+                <a  data-id="facebookUrl" class="facebook">
+                    <svg width="20px" height="20px" viewBox="0 0 50 50" enable-background="new 0 0 50 50" xml:space="preserve">
+                        <path fill="#36609F" d="M42.188,0H7.813C3.499,0,0,3.5,0,7.813v34.375C0,46.502,3.499,50,7.813,50h34.375C46.502,50,50,46.502,50,42.188V7.813C50,3.5,46.502,0,42.188,0z M30.355,25.009h-3.503c0,5.601,0,12.491,0,12.491h-5.191c0,0,0-6.824,0-12.491h-2.469v-4.413h2.469v-2.856c0-2.041,0.971-5.239,5.238-5.239l3.846,0.015V16.8c0,0-2.338,0-2.791,0c-0.454,0-1.102,0.229-1.102,1.202v2.594h3.958L30.355,25.009z"
+                        />
+                    </svg>
+                    <%=__("Facebook")%>
                 </a>
             </li>
             <li>
-                <a  data-id="houzzUrl" class="houzz">
-                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#4DBC15" d="M50 25c0 13.807-11.193 25-25 25S0 38.807 0 25 11.193 0 25 0s25 11.193 25 25Z"/><path fill="#fff" d="M19.336 12.5h-5.469v25h8.399v-7.715h5.566V37.5h8.3V23.242L19.337 18.36V12.5Z"/></svg>
-                    Houzz
+                <a  data-id="googleplayUrl" class="googleplay">
+                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#EA4335" d="M24.625 24 5.375 44.125c.625 2.125 2.625 3.75 5 3.75 1 0 1.875-.25 2.625-.75L34.75 34.75 24.625 24Z"/><path fill="#FBBC04" d="m44.125 20.5-9.375-5.375-10.5 9.25L34.875 34.75l9.375-5.25C45.875 28.625 47 26.875 47 25c-.125-1.875-1.25-3.625-2.875-4.5Z"/><path fill="#4285F4" d="M5.375 5.875c-.125.375-.125.875-.125 1.375v35.625c0 .5 0 .875.125 1.375l20-19.625-20-18.75Z"/><path fill="#34A853" d="m24.75 25 10-9.875-21.625-12.25c-.75-.5-1.75-.75-2.75-.75-2.375 0-4.5 1.625-5 3.75L24.75 25Z"/></svg>
+                    Google Play
                 </a>
             </li>
             <li>
-                <a  data-id="ubereatsUrl" class="ubereats">
-                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#06C167" d="M0 8a8 8 0 0 1 8-8h34a8 8 0 0 1 8 8v34a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z"/><path fill="#000" d="M12.56 22.165c1.626 0 2.883-1.249 2.883-3.097v-7.193h1.76V23.53H15.46v-1.082c-.788.815-1.878 1.282-3.101 1.282-2.514 0-4.442-1.815-4.442-4.562v-7.292h1.76v7.192c0 1.881 1.24 3.097 2.883 3.097Zm5.95 1.365h1.677v-1.066a4.28 4.28 0 0 0 3.05 1.266c2.515 0 4.493-1.981 4.493-4.429 0-2.464-1.977-4.445-4.492-4.445a4.242 4.242 0 0 0-3.034 1.265v-4.245H18.51V23.53Zm4.61-1.282a2.936 2.936 0 0 1-2.95-2.947 2.94 2.94 0 0 1 4.076-2.722 2.932 2.932 0 0 1 1.808 2.722 2.944 2.944 0 0 1-2.933 2.947Zm9.672-7.376c-2.497 0-4.392 2.015-4.392 4.412 0 2.53 1.979 4.43 4.543 4.43 1.56 0 2.833-.684 3.688-1.816L35.407 21c-.637.85-1.475 1.249-2.463 1.249-1.443 0-2.599-1.033-2.834-2.414h6.957v-.55c0-2.53-1.81-4.412-4.275-4.412Zm-2.648 3.63c.302-1.299 1.358-2.164 2.615-2.164 1.258 0 2.313.865 2.599 2.164h-5.214Zm12.17-1.965v-1.565h-.586c-.939 0-1.626.433-2.045 1.116v-1.05h-1.677v8.492H39.7v-4.828c0-1.315.804-2.165 1.91-2.165h.704ZM7.917 26.25h8.14v1.992h-5.941v2.85H15.9v1.932h-5.784v2.89h5.942v1.992H7.917V26.25ZM38.18 38.125c2.497 0 3.903-1.196 3.903-2.85 0-1.175-.832-2.052-2.575-2.43l-1.842-.379c-1.07-.199-1.407-.398-1.407-.797 0-.518.516-.837 1.467-.837 1.03 0 1.782.28 2 1.235h2.16c-.12-1.792-1.407-2.988-4.022-2.988-2.258 0-3.843.937-3.843 2.75 0 1.255.872 2.072 2.754 2.47l2.06.478c.812.16 1.03.379 1.03.718 0 .538-.614.877-1.605.877-1.248 0-1.96-.279-2.238-1.236h-2.18c.318 1.793 1.645 2.989 4.339 2.989Zm-6.545-2.172h1.624v1.952H30.92c-1.466 0-2.278-.916-2.278-2.072v-4.582H27v-1.953h1.644v-2.45h2.18v2.45h2.436v1.953h-2.437v4.025c0 .458.317.677.813.677Z M23.92 29.299h2.16v8.607h-2.16v-.777a4.197 4.197 0 0 1-2.734.996c-2.555 0-4.555-2.012-4.555-4.523 0-2.51 2-4.523 4.555-4.523a4.197 4.197 0 0 1 2.734.997v-.777Zm.04 4.303a2.635 2.635 0 0 0-.753-1.866 2.57 2.57 0 0 0-1.842-.764 2.559 2.559 0 0 0-1.838.768 2.622 2.622 0 0 0-.757 1.862 2.649 2.649 0 0 0 .757 1.862 2.584 2.584 0 0 0 1.838.768c1.446 0 2.595-1.156 2.595-2.63Z"/></svg>
-                    Uber Eats
-                </a>
-            </li>
-            <li>
-                <a  data-id="opentableUrl" class="opentable">
-                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#DD3743" d="M50 25c0 13.807-11.193 25-25 25S0 38.807 0 25 11.193 0 25 0s25 11.193 25 25Z"/><path fill="#fff" d="M13.544 22.716c-1.405 0-2.544 1.152-2.544 2.572 0 1.422 1.14 2.573 2.544 2.573 1.405 0 2.545-1.152 2.545-2.573 0-1.42-1.14-2.572-2.545-2.572Z M18.695 25.288C18.695 19.607 23.252 15 28.872 15c5.622 0 10.178 4.607 10.178 10.288 0 5.683-4.556 10.29-10.178 10.29-5.62 0-10.177-4.607-10.177-10.29Zm7.633 0c0 1.421 1.139 2.573 2.544 2.573s2.545-1.152 2.545-2.573c0-1.42-1.14-2.572-2.545-2.572s-2.544 1.152-2.544 2.572Z"/></svg>
-                    OpenTable
-                </a>
-            </li>
-            <li>
-                <a  data-id="deliverooUrl" class="deliveroo">
-                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#35BEB2" d="M0 8a8 8 0 0 1 8-8h34a8 8 0 0 1 8 8v34a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z"/><path fill="#fff" d="M22.037 25.173a.044.044 0 0 0 .027-.018c.006-.01.009-.021.006-.033l-2.422-11.28a.103.103 0 0 1 .014-.076.1.1 0 0 1 .063-.044l7.413-1.548c.13-.027.21.024.238.155l2.386 11.066a.014.014 0 0 0 .014.012c.003 0 .007 0 .01-.003a.015.015 0 0 0 .006-.008.348.348 0 0 0 .014-.07c.467-4.382.932-8.763 1.393-13.145.008-.068.02-.113.038-.134a.122.122 0 0 1 .111-.046l7.608.8a.107.107 0 0 1 .09.075.106.106 0 0 1 .003.041c-.678 6.413-1.358 12.833-2.036 19.26a3.97 3.97 0 0 1-.326 1.21 1295.113 1295.113 0 0 1-4.187 9.25.125.125 0 0 1-.137.07l-19.371-4.056a.08.08 0 0 1-.062-.062l-1.928-9.01a.1.1 0 0 1 .014-.075.099.099 0 0 1 .062-.043l10.959-2.288Zm3.435 3.69c.241-1.398-2.155-2.012-2.497-.597-.265 1.098.32 1.678 1.409 1.637.586-.023.994-.498 1.088-1.04Zm5.455.893a1.123 1.123 0 0 0-.663-1.438l-.252-.093a1.114 1.114 0 0 0-1.434.665l-.088.24a1.123 1.123 0 0 0 .662 1.437l.253.093a1.115 1.115 0 0 0 1.434-.664l.088-.24Z"/></svg>
-                    Deliveroo
-                </a>
-            </li>
-            <li>
                 <a  data-id="genericUrl" class="generic">
                     <svg width="20px" height="20px" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="#000000" class="icon-social"><path class="color1" d="M400.4,341.5c-20.2,0-38.7,7.1-53.3,18.9l-151.6-89.9c0.9-4.9,1.4-9.8,1.4-14.9c0-5-0.5-9.9-1.4-14.7l151.4-89.3 c14.6,11.8,33.2,19,53.5,19c47,0,85.2-38.2,85.2-85.2S447.4,0,400.4,0s-85.2,38.2-85.2,85.2c0,5,0.5,9.9,1.4,14.7l-151.4,89.3 c-14.6-11.8-33.2-19-53.5-19c-47,0-85.2,38.2-85.2,85.2s38.2,85.2,85.2,85.2c20.2,0,38.7-7.1,53.3-18.9l151.6,89.9 c-0.9,4.9-1.4,9.8-1.4,14.9c0,47,38.2,85.2,85.2,85.2s85.2-38.2,85.2-85.2S447.4,341.5,400.4,341.5z M400.4,59.8 c14,0,25.4,11.4,25.4,25.4s-11.4,25.4-25.4,25.4c-14,0-25.4-11.4-25.4-25.4S386.4,59.8,400.4,59.8z M111.6,281 c-14,0-25.4-11.4-25.4-25.4c0-14,11.4-25.4,25.4-25.4c14,0,25.4,11.4,25.4,25.4C137,269.6,125.6,281,111.6,281z M400.4,452.2 c-14,0-25.4-11.4-25.4-25.4c0-14,11.4-25.4,25.4-25.4c14,0,25.4,11.4,25.4,25.4C425.8,440.8,414.4,452.2,400.4,452.2z"/></svg>
                     Generic
                 </a>
             </li>
-            
             <li>
-                <a  data-id="facebookUrl" class="facebook">
-                    <svg width="20px" height="20px" viewBox="0 0 50 50" enable-background="new 0 0 50 50" xml:space="preserve">
-                        <path fill="#36609F" d="M42.188,0H7.813C3.499,0,0,3.5,0,7.813v34.375C0,46.502,3.499,50,7.813,50h34.375C46.502,50,50,46.502,50,42.188V7.813C50,3.5,46.502,0,42.188,0z M30.355,25.009h-3.503c0,5.601,0,12.491,0,12.491h-5.191c0,0,0-6.824,0-12.491h-2.469v-4.413h2.469v-2.856c0-2.041,0.971-5.239,5.238-5.239l3.846,0.015V16.8c0,0-2.338,0-2.791,0c-0.454,0-1.102,0.229-1.102,1.202v2.594h3.958L30.355,25.009z"
-                        />
-                    </svg>
-                    <%=__("Facebook")%>
-                </a>
-            </li>
-            <li>
                 <a  data-id="mybusinessUrl" class="mybusiness">
                     <svg width="20px" height="20px" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="#4e8df7" class="icon-mybusiness1"><path class="color1" d="M154.8 202.6c0.3 24.6 20.4 44.5 45.1 44.5 24.9 0 45.1-20.2 45.1-45.1V65.8h-74.6L154.8 202.6zM87.8 247.1c21.2 0 39-14.6 43.8-34.3l1.4-12 15.4-134.9H99.9c-11.4 0-21.5 8.1-23.9 19.3l-0.3 1.1 -31.8 113c-0.9 3.3-1.2 6.8-0.6 10C46.8 231.2 65.5 247.1 87.8 247.1zM312 247.1c24.6 0 44.7-19.9 45.1-44.4L341.5 65.8h-74.6V202C266.9 226.8 287.1 247.1 312 247.1zM424.1 247.1c22.8 0 42.1-17.1 44.8-39.7 0.3-2.3 0.1-4.7-0.5-6.9L436 85.4l-0.1-0.3c-2.4-11.2-12.5-19.3-23.9-19.3h-48.5l15.4 135C382.3 230.2 398.7 247.1 424.1 247.1zM424.1 269c-24.5 0-44.1-11.3-55.8-30.8C356.4 256.7 335.6 269 312 269c-23.4 0-44.1-12.1-56-30.4 -12 18.3-32.6 30.4-56 30.4 -23.4 0-44.1-12.1-56-30.4 -12 18.3-32.6 30.4-56.1 30.4 -11.8 0-23.4-3.1-33.5-9v170.7c0 8.6 7 15.5 15.5 15.5h361.3c8.6 0 15.5-7 15.5-15.5V265.1C439.6 267.6 432 269 424.1 269zM421.5 380.4c-2.9 10.2-8.6 19.9-16.4 27.1 -7.5 6.8-16.7 11.6-26.6 13.8 -10.9 2.5-22.4 2.5-33.2-0.3 -33.1-8.9-53.6-42-46.9-75.6 1.1-5.7 3-11.2 5.7-16.3 7.4-14.6 20-25.9 35.3-31.7 0.5-0.2 1-0.4 1.5-0.5 14.2-4.9 29.6-4.7 43.8 0.4 7.8 2.9 14.9 7.3 20.9 13 -2 2.2-4.2 4.3-6.3 6.4l-11.9 11.9c-4-3.8-8.8-6.6-14-8.2 -13.7-4.1-28.5-0.3-38.5 10 -4.2 4.3-7.4 9.6-9.4 15.3 -2.8 8.2-2.8 17 0 25.2l-0.1 0v0.1c0.2 0.6 0.4 1.2 0.6 1.7 2.9 7.3 7.8 13.6 14.2 18.2 4.3 3.1 9.3 5.2 14.5 6.3 5.1 1 10.4 1 15.5 0.1 5.1-0.8 10-2.7 14.3-5.6 0 0 0 0.2 0 0.2 6.8-4.6 11.4-11.7 13-19.8H362c0-8.6 0-17.9 0-26.4h61.1c0.5 3.1 0.8 5.5 1 8.2C424.8 362.7 424 371.7 421.5 380.4z"/></svg>
                     <%=__("Mybusiness")%>
@@ -113,9 +85,9 @@
                 </a>
             </li>
             <li>
-                <a  data-id="xUrl" class="x">
-                    <svg width="20" height="20" viewBox="0 0 20 20" fill="#000" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M3.1252 0H16.8752C18.6008 0 20 1.4 20 3.1252V16.8752C20 18.6008 18.6008 20 16.8752 20H3.1252C1.3988 20 0 18.6008 0 16.8752V3.1252C0 1.4 1.3988 0 3.1252 0ZM16.5459 3L11.3335 8.92867L17 17H12.8309L9.01369 11.5636L4.235 17H3L8.46604 10.7834L3 3H7.16908L10.7829 8.14671L15.3109 3H16.5459ZM9.08675 10.0762L9.64142 10.8518L13.4131 16.1334H15.3103L10.687 9.66391L10.1347 8.88837L6.57759 3.91102H4.68037L9.08675 10.0762Z"/></svg>
-                    <%=__("Twitter")%>
+                <a  data-id="houzzUrl" class="houzz">
+                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#4DBC15" d="M50 25c0 13.807-11.193 25-25 25S0 38.807 0 25 11.193 0 25 0s25 11.193 25 25Z"/><path fill="#fff" d="M19.336 12.5h-5.469v25h8.399v-7.715h5.566V37.5h8.3V23.242L19.337 18.36V12.5Z"/></svg>
+                    Houzz
                 </a>
             </li>
             <li>
@@ -134,58 +106,6 @@
                 </a>
             </li>
             <li>
-                <a  data-id="pinterestUrl" class="pinterest">
-                    <svg width="20px" height="20px" viewBox="0 0 30 30" enable-background="new 0 0 30 30" xml:space="preserve"><path fill="#CB2128" d="M25.313,0H4.688C2.098,0,0,2.1,0,4.688v20.625C0,27.901,2.098,30,4.688,30h20.625C27.901,30,30,27.901,30,25.313V4.688C30,2.1,27.901,0,25.313,0z M16.998,19.449c-1.2,0-2.33-0.671-2.716-1.433c0,0-0.646,2.646-0.782,3.159c-0.482,1.805-1.899,3.611-2.008,3.758c-0.077,0.104-0.247,0.07-0.265-0.064c-0.031-0.231-0.392-2.51,0.033-4.367c0.214-0.933,1.431-6.26,1.431-6.26s-0.356-0.732-0.356-1.816c0-1.704,0.957-2.974,2.146-2.974c1.011,0,1.498,0.784,1.498,1.724c0,1.052-0.646,2.622-0.98,4.077c-0.279,1.218,0.591,2.212,1.755,2.212c2.107,0,3.526-2.796,3.526-6.106c0-2.52-1.642-4.403-4.628-4.403c-3.373,0-5.475,2.6-5.475,5.502c0,1.002,0.286,1.707,0.734,2.254c0.206,0.251,0.234,0.352,0.16,0.64c-0.054,0.212-0.175,0.72-0.227,0.923c-0.073,0.291-0.302,0.393-0.556,0.285c-1.554-0.654-2.277-2.411-2.277-4.387c0-3.265,2.664-7.178,7.949-7.178c4.246,0,7.04,3.174,7.04,6.582C22.999,16.083,20.573,19.449,16.998,19.449z"/></svg>
-                    <%=__("Pinterest")%>
-                </a>
-            </li>
-            <li>
-                <a  data-id="youtubeUrl" class="youtube">
-                    <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="20px" height="20px" viewBox="0 0 30 30" enable-background="new 0 0 30 30" xml:space="preserve">
-                        <path fill="#EC2626" d="M12.594,21.504c0,0.273,0,0.488,0.013,0.637c0.013,0.149-0.013,0.244-0.063,0.286
-                                c-0.043,0.167-0.177,0.286-0.393,0.363c-0.21,0.077-0.369-0.029-0.483-0.334c-0.025-0.041-0.025-0.143-0.013-0.297
-                                c0.006-0.161,0.013-0.369,0.013-0.632l-0.032-3.846H10.12l0.038,3.786c0,0.298-0.014,0.548-0.02,0.751
-                                c-0.011,0.202-0.005,0.351,0.02,0.452c0.019,0.167,0.039,0.346,0.063,0.536c0.019,0.19,0.12,0.345,0.312,0.458
-                                c0.177,0.102,0.38,0.155,0.616,0.155c0.24,0,0.469-0.042,0.697-0.113c0.222-0.071,0.432-0.173,0.622-0.298s0.33-0.268,0.418-0.411
-                                l-0.032,0.757l1.257,0.029v-6.103h-1.517V21.504z M18.312,17.574c-0.152-0.083-0.336-0.125-0.533-0.125
-                                c-0.361,0-0.774,0.161-1.25,0.477V15.55h-1.518v8.175l1.258-0.029l0.095-0.513c0.406,0.321,0.774,0.519,1.098,0.59
-                                c0.324,0.071,0.597,0.042,0.825-0.083c0.222-0.132,0.399-0.34,0.526-0.638c0.121-0.291,0.185-0.631,0.185-1.012v-3.168
-                                C18.997,18.265,18.769,17.83,18.312,17.574z M17.848,22.302c0,0.125-0.069,0.232-0.209,0.327c-0.139,0.09-0.291,0.138-0.463,0.138
-                                c-0.178,0-0.336-0.048-0.477-0.138c-0.133-0.095-0.203-0.202-0.203-0.327v-3.584c0-0.125,0.07-0.232,0.203-0.334
-                                c0.141-0.095,0.299-0.148,0.477-0.148c0.172,0,0.324,0.054,0.463,0.148c0.14,0.102,0.209,0.209,0.209,0.334V22.302z
-                                 M14.745,10.924c0.178,0,0.336-0.06,0.476-0.184c0.133-0.119,0.203-0.275,0.203-0.447V6.941c0-0.19-0.07-0.347-0.203-0.472
-                                c-0.14-0.131-0.298-0.196-0.47-0.196c-0.177,0-0.336,0.065-0.469,0.196c-0.141,0.125-0.21,0.281-0.21,0.472v3.352
-                                c0,0.166,0.07,0.31,0.203,0.44C14.402,10.858,14.562,10.924,14.745,10.924z M25.313,0H4.688C2.098,0,0,2.1,0,4.688v20.625
-                                C0,27.901,2.098,30,4.688,30h20.625C27.901,30,30,27.901,30,25.313V4.688C30,2.1,27.901,0,25.313,0z M17.715,5.488h1.416v4.911
-                                c0,0.125,0.05,0.233,0.145,0.328c0.108,0.09,0.235,0.137,0.4,0.137c0.152,0,0.285-0.047,0.394-0.137
-                                c0.114-0.095,0.171-0.203,0.171-0.328V5.488h1.314v6.305h-1.676l0.025-0.512c-0.152,0.208-0.298,0.369-0.444,0.482
-                                c-0.171,0.101-0.361,0.154-0.577,0.154c-0.248,0-0.457-0.054-0.622-0.154c-0.146-0.096-0.272-0.233-0.38-0.417
-                                c-0.039-0.083-0.07-0.173-0.083-0.257c-0.013-0.088-0.025-0.172-0.052-0.256c-0.019-0.101-0.031-0.244-0.031-0.422V5.488z
-                                 M13.533,5.548c0.299-0.209,0.672-0.311,1.136-0.311c0.406,0,0.744,0.065,1.023,0.186c0.272,0.101,0.501,0.285,0.672,0.541
-                                c0.133,0.185,0.234,0.399,0.299,0.637c0.063,0.257,0.095,0.603,0.095,1.037v1.619c0,0.589-0.032,1.012-0.095,1.268
-                                c-0.025,0.237-0.134,0.5-0.33,0.793c-0.197,0.256-0.412,0.422-0.641,0.506c-0.279,0.125-0.591,0.184-0.94,0.184
-                                c-0.36,0-0.69-0.047-0.989-0.154c-0.273-0.083-0.482-0.232-0.609-0.447c-0.133-0.166-0.242-0.397-0.33-0.696
-                                c-0.063-0.278-0.103-0.696-0.103-1.268V7.774c0-0.567,0.057-1.036,0.166-1.405C13.02,5.988,13.236,5.714,13.533,5.548z
-                                 M9.085,2.981l1.003,3.107l1.008-3.107h1.758l-1.897,4.156v4.839H9.345V7.138L7.321,2.981H9.085z M26.016,23.028
-                                c0,0.423-0.096,0.81-0.286,1.167s-0.45,0.667-0.78,0.935c-0.33,0.273-0.711,0.482-1.148,0.637
-                                c-0.432,0.149-0.901,0.232-1.402,0.232H7.613c-0.488,0-0.952-0.083-1.391-0.232c-0.43-0.154-0.812-0.363-1.141-0.637
-                                c-0.33-0.268-0.59-0.577-0.787-0.935c-0.189-0.357-0.285-0.744-0.285-1.167v-6.782c0-0.404,0.096-0.785,0.285-1.154
-                                c0.197-0.363,0.457-0.679,0.787-0.941c0.329-0.262,0.711-0.476,1.141-0.637c0.439-0.154,0.902-0.238,1.398-0.238h14.778
-                                c0.488,0,0.958,0.084,1.396,0.238c0.438,0.161,0.825,0.375,1.155,0.637c0.33,0.263,0.59,0.578,0.78,0.941
-                                c0.196,0.369,0.286,0.75,0.286,1.154V23.028z M22.64,21.641v0.69c0,0.167-0.063,0.298-0.185,0.388
-                                c-0.127,0.095-0.279,0.137-0.457,0.137h-0.234c-0.172,0-0.317-0.042-0.432-0.137c-0.114-0.09-0.178-0.221-0.178-0.388v-1.464
-                                h2.621v-0.852c0-0.322-0.006-0.625-0.013-0.917c-0.013-0.298-0.038-0.524-0.083-0.69c-0.044-0.28-0.178-0.5-0.406-0.673
-                                c-0.222-0.167-0.488-0.286-0.787-0.346c-0.304-0.065-0.608-0.077-0.926-0.029c-0.311,0.041-0.584,0.137-0.818,0.291
-                                c-0.292,0.185-0.502,0.435-0.641,0.757c-0.127,0.321-0.197,0.762-0.197,1.333v1.899c0,0.804,0.229,1.376,0.68,1.715
-                                c0.418,0.315,0.855,0.477,1.313,0.477h0.141c0.514-0.023,0.971-0.232,1.363-0.613c0.299-0.28,0.444-0.649,0.444-1.096
-                                c-0.013-0.148-0.032-0.31-0.069-0.482H22.64z M21.192,19.074c0-0.166,0.063-0.303,0.196-0.41c0.134-0.107,0.286-0.167,0.477-0.167
-                                h0.07c0.196,0,0.361,0.054,0.501,0.155c0.133,0.106,0.203,0.244,0.203,0.422l-0.032,0.757H21.16L21.192,19.074z M10.417,15.55
-                                H5.526v1.048h1.566v7.187h1.511v-7.156h1.814V15.55z" />
-                    </svg>
-                    <%=__("Youtube")%>
-                </a>
-            </li>
-            <li>
                 <a  data-id="linkedinUrl" class="linkedin">
                     <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="20px" height="20px" viewBox="0 0 30 30" enable-background="new 0 0 30 30" xml:space="preserve">
                         <path fill="#0065A1" d="M25.313,0H4.688C2.098,0,0,2.1,0,4.688v20.625C0,27.901,2.098,30,4.688,30h20.625
@@ -198,27 +118,46 @@
                 </a>
             </li>
             <li>
-                <a  data-id="viadeoUrl" class="viadeo">
-                    <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="20px" height="20px" viewBox="0 0 30 30" enable-background="new 0 0 30 30" xml:space="preserve">
-
-                        <path fill="#f07355" d="M13.79,23.154c0.008,0,0.015-0.001,0.023-0.001c-0.015,0-0.03,0-0.045,0.001H13.79z M25.313,0H4.688
-                                C2.098,0,0,2.1,0,4.688v20.625C0,27.901,2.098,30,4.688,30h20.625C27.901,30,30,27.901,30,25.313V4.688C30,2.1,27.901,0,25.313,0z
-                                 M19.4,22.635C17.947,24.212,16.08,25,13.797,25c-2.292,0-4.165-0.788-5.617-2.365C6.727,21.058,6,19.214,6,17.105
-                                c0-2.091,0.688-3.903,2.063-5.439c1.513-1.692,3.424-2.538,5.734-2.538c0.972,0,1.872,0.151,2.703,0.451
-                                c-0.271,0.525-0.514,1.169-0.58,1.86c-0.66-0.273-1.37-0.411-2.13-0.411c-1.598,0-2.971,0.597-4.119,1.79
-                                s-1.722,2.636-1.722,4.329c0,1.091,0.262,2.108,0.785,3.052s1.23,1.672,2.122,2.186c0.591,0.341,1.221,0.565,1.889,0.681
-                                c5.712-2.209,5.038-12.988,4.997-13.576C17.516,8.833,16.518,6.073,14.677,3c0,0,2.612,1.724,3.063,6.463c0,0,0,0.011,0.002,0.026
-                                c0.033,0.097,0.049,0.149,0.049,0.149c3.353,8.906-3.939,13.5-3.959,13.513c1.048-0.007,2.013-0.26,2.893-0.767
-                                c0.891-0.514,1.598-1.242,2.121-2.186s0.785-1.961,0.785-3.052c0-0.886-0.159-1.702-0.475-2.451
-                                c0.525-0.109,1.158-0.323,1.826-0.731c0.396,0.966,0.598,2.012,0.598,3.141C21.58,19.214,20.854,21.058,19.4,22.635z
-                                 M24.611,9.738c0,0-0.591,1.597-1.821,2.325c-0.928,0.55-2.334,0.723-3.805-0.711c0,0,4.446-2.426,4.612-4.479
-                                c0,0-1.365,3.056-5.101,3.902c0,0-1.363-2.1,0.367-3.831c0,0,0.605-0.665,2.336-1.103c0,0,1.73-0.367,2.665-2.344
-                                C23.865,3.499,25.803,6.192,24.611,9.738z" />
+                <a  data-id="boncoinUrl" class="leboncoin">
+                    <svg width="20px" height="20px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+                        <path d="M0 8C0 3.58172 3.58172 0 8 0H42C46.4183 0 50 3.58172 50 8V42C50 46.4183 46.4183 50 42 50H8C3.58172 50 0 46.4183 0 42V8Z" fill="#FDFDFD"/>
+                        <path d="M20 24C20 21.7909 21.7909 20 24 20H50L20 50V24Z" fill="#FE6D14"/>
+                        <path d="M50 46C50 48.2091 48.2091 50 46 50L20 50L50 20L50 46Z" fill="#954009"/>
                     </svg>
-                    <%=__("viadeo")%>
+                        
+                    <%=__("Boncoin")%>
                 </a>
             </li>
             <li>
+                <a  data-id="opentableUrl" class="opentable">
+                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#DD3743" d="M50 25c0 13.807-11.193 25-25 25S0 38.807 0 25 11.193 0 25 0s25 11.193 25 25Z"/><path fill="#fff" d="M13.544 22.716c-1.405 0-2.544 1.152-2.544 2.572 0 1.422 1.14 2.573 2.544 2.573 1.405 0 2.545-1.152 2.545-2.573 0-1.42-1.14-2.572-2.545-2.572Z M18.695 25.288C18.695 19.607 23.252 15 28.872 15c5.622 0 10.178 4.607 10.178 10.288 0 5.683-4.556 10.29-10.178 10.29-5.62 0-10.177-4.607-10.177-10.29Zm7.633 0c0 1.421 1.139 2.573 2.544 2.573s2.545-1.152 2.545-2.573c0-1.42-1.14-2.572-2.545-2.572s-2.544 1.152-2.544 2.572Z"/></svg>
+                    OpenTable
+                </a>
+            </li>
+            <li>
+                <a  data-id="pinterestUrl" class="pinterest">
+                    <svg width="20px" height="20px" viewBox="0 0 30 30" enable-background="new 0 0 30 30" xml:space="preserve"><path fill="#CB2128" d="M25.313,0H4.688C2.098,0,0,2.1,0,4.688v20.625C0,27.901,2.098,30,4.688,30h20.625C27.901,30,30,27.901,30,25.313V4.688C30,2.1,27.901,0,25.313,0z M16.998,19.449c-1.2,0-2.33-0.671-2.716-1.433c0,0-0.646,2.646-0.782,3.159c-0.482,1.805-1.899,3.611-2.008,3.758c-0.077,0.104-0.247,0.07-0.265-0.064c-0.031-0.231-0.392-2.51,0.033-4.367c0.214-0.933,1.431-6.26,1.431-6.26s-0.356-0.732-0.356-1.816c0-1.704,0.957-2.974,2.146-2.974c1.011,0,1.498,0.784,1.498,1.724c0,1.052-0.646,2.622-0.98,4.077c-0.279,1.218,0.591,2.212,1.755,2.212c2.107,0,3.526-2.796,3.526-6.106c0-2.52-1.642-4.403-4.628-4.403c-3.373,0-5.475,2.6-5.475,5.502c0,1.002,0.286,1.707,0.734,2.254c0.206,0.251,0.234,0.352,0.16,0.64c-0.054,0.212-0.175,0.72-0.227,0.923c-0.073,0.291-0.302,0.393-0.556,0.285c-1.554-0.654-2.277-2.411-2.277-4.387c0-3.265,2.664-7.178,7.949-7.178c4.246,0,7.04,3.174,7.04,6.582C22.999,16.083,20.573,19.449,16.998,19.449z"/></svg>
+                    <%=__("Pinterest")%>
+                </a>
+            </li>
+            <li>
+                <a  data-id="pagesjauneUrl" class="pagesjaunes">
+                    <svg width="20px" height="20px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+                        <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.58172 0 0 3.58172 0 8V42C0 46.4183 3.58172 50 8 50H42C46.4183 50 50 46.4183 50 42V8C50 3.58172 46.4183 0 42 0H8Z" fill="#FCEB23"/>
+                        <path d="M10.375 19.7917C10.375 20.9167 11.2917 21.8333 12.4584 21.8333H12.5001C13.6251 21.8333 14.5417 20.9167 14.5417 19.75V17.4583C14.5417 16.3333 13.5834 15.375 12.4584 15.375C11.3334 15.375 10.375 16.2917 10.375 17.4583V19.7917Z" fill="black"/>
+                        <path d="M19.8334 39.4167C21.625 40.0417 23.5417 40.375 25.4584 40.375C27.125 40.375 28.7501 40.125 30.3334 39.5833C36.6251 37.4167 40.25 31.3333 40.25 22.8333V17.4583C40.25 16.2917 39.3334 15.375 38.1667 15.375C37 15.375 36.0834 16.2917 36.0834 17.4583V22.8333C36.0834 29.5 33.5834 34.0417 29 35.625C24.0834 37.2917 17.7917 35.0833 14.0417 30.3333C14.0001 30.3333 14 30.2917 14 30.2917C13.2917 29.375 11.9584 29.25 11.0834 29.9583C10.1667 30.7083 10.0001 32 10.7084 32.9167C13.0834 35.9167 16.25 38.1667 19.8334 39.4167Z" fill="black"/>
+                    </svg>
+                        
+                    <%=__("Pagesjaune")%>
+                </a>
+            </li>
+            <li>
+                <a  data-id="snapchatUrl" class="snapchat">
+                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#FFFC00" d="M0 8a8 8 0 0 1 8-8h34a8 8 0 0 1 8 8v34a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z"/><path fill="#fff" d="M25.227 42.186a5.38 5.38 0 0 1-.279-.008c-.06.006-.12.008-.183.008-2.18 0-3.637-1.03-4.923-1.94-.922-.652-1.792-1.267-2.81-1.436a9.08 9.08 0 0 0-1.48-.125c-.865 0-1.55.134-2.05.232-.308.06-.573.111-.778.111-.215 0-.475-.047-.584-.419-.086-.292-.148-.576-.208-.85-.149-.682-.257-1.099-.513-1.138-2.734-.422-4.35-1.044-4.671-1.796a.698.698 0 0 1-.057-.237.439.439 0 0 1 .367-.458c2.171-.358 4.102-1.505 5.739-3.412 1.267-1.477 1.89-2.887 1.957-3.043a.27.27 0 0 1 .01-.02c.315-.64.378-1.194.187-1.644-.353-.831-1.52-1.201-2.291-1.446a8.265 8.265 0 0 1-.518-.176c-.685-.27-1.81-.841-1.66-1.63.109-.574.87-.975 1.484-.975.17 0 .321.03.449.09.694.325 1.319.49 1.856.49.668 0 .99-.254 1.068-.326a110.69 110.69 0 0 0-.065-1.095c-.157-2.494-.353-5.598.441-7.379 2.377-5.328 7.417-5.743 8.905-5.743a379.787 379.787 0 0 0 .736-.007c1.492 0 6.543.415 8.921 5.747.794 1.781.598 4.889.441 7.386l-.007.12c-.022.338-.042.66-.059.97.074.068.369.301.97.325.512-.02 1.1-.184 1.748-.487.2-.095.422-.114.573-.114.228 0 .46.044.652.125l.01.004c.55.195.91.585.919.995.006.382-.277.953-1.674 1.504a8.428 8.428 0 0 1-.518.176c-.773.245-1.938.615-2.29 1.445-.192.45-.13 1.004.186 1.644l.01.021c.098.228 2.45 5.59 7.696 6.454a.44.44 0 0 1 .367.458.705.705 0 0 1-.058.24c-.32.748-1.935 1.369-4.67 1.791-.256.04-.365.454-.512 1.134a12.5 12.5 0 0 1-.209.844c-.08.272-.256.405-.54.405h-.044a4.38 4.38 0 0 1-.778-.099 10.24 10.24 0 0 0-2.05-.217 9.09 9.09 0 0 0-1.48.125c-1.017.17-1.887.783-2.807 1.434-1.289.911-2.747 1.942-4.926 1.942Z"/><path fill="#020202" d="M25.356 8.253c1.405 0 6.24.378 8.52 5.487.75 1.682.558 4.73.404 7.179-.025.388-.048.765-.068 1.124l-.008.155.103.115c.042.046.432.454 1.3.487l.014.001h.014c.574-.022 1.22-.2 1.922-.529a.939.939 0 0 1 .386-.072c.166 0 .342.031.487.093l.022.008c.368.13.628.372.632.592.002.125-.09.573-1.395 1.088a8 8 0 0 1-.49.165c-.848.27-2.13.676-2.562 1.693-.243.573-.176 1.249.198 2.01.154.358 2.576 5.815 8.028 6.714a.24.24 0 0 1-.023.087c-.092.218-.68.97-4.334 1.534-.572.087-.712.728-.874 1.474a12.66 12.66 0 0 1-.2.814c-.026.084-.03.089-.12.089h-.043c-.162 0-.408-.034-.694-.09a10.72 10.72 0 0 0-2.135-.225c-.506 0-1.028.044-1.551.131-1.119.186-2.028.83-2.991 1.51-1.294.914-2.63 1.86-4.67 1.86-.09 0-.177-.003-.264-.007l-.023-.001-.023.002c-.05.004-.1.006-.152.006-2.04 0-3.377-.945-4.67-1.86-.963-.68-1.873-1.324-2.991-1.51a9.48 9.48 0 0 0-1.551-.131c-.907 0-1.617.139-2.135.24-.285.056-.532.103-.694.103-.132 0-.135-.007-.163-.103-.082-.278-.142-.554-.2-.82-.163-.746-.303-1.391-.875-1.479-3.654-.564-4.241-1.317-4.334-1.534a.266.266 0 0 1-.023-.09c5.452-.897 7.874-6.354 8.029-6.715.374-.76.44-1.435.197-2.009-.432-1.016-1.714-1.423-2.562-1.692a8.187 8.187 0 0 1-.49-.166c-1.102-.435-1.44-.874-1.39-1.14.058-.306.59-.617 1.053-.617.104 0 .195.017.263.048.752.353 1.44.532 2.042.532.947 0 1.37-.441 1.415-.491l.102-.115-.008-.154c-.02-.36-.043-.736-.068-1.124-.153-2.448-.346-5.494.404-7.177 2.27-5.09 7.083-5.483 8.504-5.483l.652-.006h.083Zm0-.878h-.093l-.645.007c-.832 0-2.497.118-4.286.907a9.55 9.55 0 0 0-2.734 1.813c-.943.896-1.712 2-2.284 3.284-.838 1.878-.639 5.043-.48 7.585l.002.004.051.834a1.467 1.467 0 0 1-.616.116c-.473 0-1.034-.15-1.67-.448a1.493 1.493 0 0 0-.635-.132c-.379 0-.777.112-1.123.315-.435.256-.717.617-.793 1.017-.05.265-.048.788.534 1.32.32.292.79.561 1.396.8.159.063.348.123.548.187.694.22 1.745.554 2.019 1.199.138.326.079.757-.178 1.277l-.019.042c-.064.15-.661 1.503-1.887 2.93a10.61 10.61 0 0 1-2.278 2.016 8.377 8.377 0 0 1-3.198 1.249.878.878 0 0 0-.735.916c.008.129.038.258.09.382l.002.003c.179.418.594.774 1.27 1.088.824.383 2.057.705 3.665.958.081.154.166.542.224.808.061.283.125.573.216.882.098.334.352.733 1.005.733.248 0 .532-.055.862-.12.483-.094 1.143-.223 1.967-.223.457 0 .93.04 1.407.12.918.152 1.71.712 2.627 1.36 1.34.947 2.859 2.02 5.178 2.02.064 0 .127-.002.19-.006.076.004.171.006.272.006 2.32 0 3.837-1.073 5.177-2.02l.002-.002c.917-.647 1.708-1.206 2.626-1.359.477-.079.95-.12 1.407-.12.787 0 1.41.101 1.967.21.363.071.645.106.862.106h.043c.478 0 .829-.262.963-.721.089-.302.152-.586.215-.873.054-.25.142-.649.223-.805 1.609-.252 2.842-.574 3.666-.957.674-.312 1.089-.668 1.269-1.085.054-.125.085-.254.092-.386a.878.878 0 0 0-.734-.916c-5.012-.826-7.27-5.976-7.363-6.194a.61.61 0 0 0-.02-.043c-.256-.52-.316-.95-.176-1.277.273-.645 1.323-.978 2.018-1.199.2-.063.39-.123.548-.186.684-.27 1.173-.563 1.495-.896.385-.396.46-.776.456-1.025-.012-.602-.472-1.137-1.204-1.398a2.152 2.152 0 0 0-.816-.157 1.82 1.82 0 0 0-.758.155c-.587.275-1.112.425-1.563.446a1.414 1.414 0 0 1-.517-.114l.045-.727.006-.108c.16-2.544.36-5.711-.478-7.591-.574-1.288-1.346-2.395-2.293-3.293a9.572 9.572 0 0 0-2.744-1.813 10.914 10.914 0 0 0-4.285-.9Z"/></svg>
+                    Snapchat
+                </a>
+            </li>
+            <li>
                 <a  data-id="skypeUrl" class="skype">
                     <svg width="20px" height="20px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                         <g clip-path="url(#clip0_23_95)">
@@ -235,6 +174,17 @@
                 </a>
             </li>
             <li>
+                <a  data-id="slideshareUrl" class="slideshare">
+                    <svg width="20px" height="20px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+                        <path d="M10.9746 5.00104H39.2425C40.1547 5.00608 41.0282 5.37071 41.6734 6.01582C42.3185 6.66092 42.683 7.53442 42.6882 8.44672V24.4094C42.9215 24.2546 43.1521 24.095 43.3799 23.9305C44.5949 23.0756 45.4649 24.2755 44.73 25.3554C42.4447 27.9415 39.5385 29.9031 36.2853 31.0551C36.8081 32.7207 37.0809 34.4543 37.0953 36.1998C37.0953 40.8196 34.5905 45.1994 29.8358 45.1994C29.261 45.236 28.6853 45.1481 28.1475 44.9419C27.6098 44.7357 27.1229 44.4161 26.72 44.0047C26.3171 43.5932 26.0077 43.0997 25.813 42.5579C25.6181 42.0159 25.5423 41.4385 25.591 40.8647V34.22C26.1832 34.3426 26.786 34.4079 27.3908 34.415C28.6964 34.4499 29.9798 34.0713 31.0572 33.3331C32.1347 32.595 32.9514 31.5353 33.3906 30.3053C34.1555 28.1753 32.7156 27.6203 31.6506 29.2552C30.0007 31.8351 27.8108 31.8351 25.501 30.3353V30.0652C25.501 27.8103 26.9179 27.8197 28.3332 27.8291C28.4242 27.8297 28.5152 27.8303 28.6058 27.8303C32.979 28.3092 37.3708 27.4196 41.1882 25.3177V8.45307C41.1848 7.93639 40.9781 7.4418 40.6127 7.07642C40.2474 6.71103 39.7528 6.5043 39.2361 6.50095H10.9441C10.6825 6.49372 10.422 6.53855 10.178 6.63337C9.93407 6.72819 9.71148 6.87081 9.5234 7.05284C9.3353 7.23487 9.18547 7.45264 9.0827 7.69337C8.98016 7.93357 8.92655 8.19179 8.92497 8.45293V26.2848H7.42505V8.44699C7.42742 7.98543 7.52201 7.529 7.70321 7.1045C7.88443 6.67999 8.14863 6.29597 8.48032 5.97499C8.812 5.65401 9.20447 5.40253 9.63468 5.23533C10.0615 5.06945 10.5169 4.98984 10.9746 5.00104Z" fill="#026C97"/>
+                        <path d="M30.9452 26.0006C31.5678 26.0226 32.1885 25.9216 32.772 25.7037C33.3556 25.4857 33.8903 25.1548 34.3458 24.7298C34.8014 24.3049 35.1688 23.7944 35.4268 23.2275C35.6849 22.6605 35.8286 22.0484 35.85 21.4257C35.7838 20.183 35.2332 19.0158 34.3162 18.1745C33.3992 17.3333 32.189 16.885 30.9452 16.926C29.6891 16.8855 28.468 17.344 27.5487 18.2013C26.6297 19.0585 26.0874 20.2448 26.0405 21.5008C26.1065 22.7435 26.6572 23.9108 27.5741 24.752C28.4911 25.5932 29.7015 26.0416 30.9452 26.0006Z" fill="#026C97"/>
+                        <path fill-rule="evenodd" clip-rule="evenodd" d="M24.4651 21.3657C24.4497 21.9954 24.3092 22.6156 24.0521 23.1907C23.7949 23.7656 23.426 24.2837 22.9669 24.7149C22.5078 25.1462 21.9676 25.4818 21.3776 25.7025C20.7877 25.9232 20.1597 26.0245 19.5304 26.0005C18.9079 26.0225 18.2871 25.9215 17.7036 25.7036C17.1201 25.4856 16.5853 25.1547 16.1297 24.7297C15.6742 24.3048 15.3069 23.7943 15.0488 23.2274C14.7907 22.6604 14.6469 22.0483 14.6257 21.4257C14.6917 20.1829 15.2423 19.0157 16.1593 18.1744C17.0763 17.3332 18.2866 16.8849 19.5304 16.9259C20.7694 16.8763 21.9789 17.3128 22.9008 18.1422C23.8226 18.9716 24.384 20.1282 24.4651 21.3657ZM21.0303 27.8303C15.9007 28.2858 10.7761 26.9037 6.5711 23.9305C5.35617 23.0756 4.51621 24.2755 5.31117 25.3405C7.58693 27.9304 10.4894 29.893 13.7407 31.0402C10.9059 40.6996 16.0956 45.1994 20.1903 45.1844C20.7667 45.2278 21.3455 45.1454 21.887 44.943C22.4284 44.7407 22.9193 44.4231 23.3258 44.0122C23.7324 43.6014 24.0449 43.1071 24.2416 42.5637C24.4383 42.0202 24.5146 41.4404 24.4651 40.8647C24.4651 37.9848 24.4651 33.2 24.4651 33.2C25.4173 33.4838 26.3985 33.66 27.39 33.725C28.6617 33.8257 29.9329 33.5235 31.0233 32.8613C32.1135 32.1989 32.9677 31.2102 33.4646 30.0352C34.2896 28.0553 32.8797 27.4854 31.7847 29.0003C29.6698 31.9101 26.955 31.3101 24.1502 28.6253C23.3851 27.9204 22.9652 27.7254 21.0003 27.8303H21.0303Z" fill="#D8711C"/>                        
+                    </svg>
+                        
+                    <%=__("Slideshare")%>
+                </a>
+            </li>
+            <li>
                 <a  data-id="theforkUrl" class="thefork">
                     <svg width="20px" height="20px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                         <g clip-path="url(#clip0_23_37)">
@@ -288,6 +238,39 @@
                 </a>
             </li>
             <li>
+                <a  data-id="ubereatsUrl" class="ubereats">
+                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#06C167" d="M0 8a8 8 0 0 1 8-8h34a8 8 0 0 1 8 8v34a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z"/><path fill="#000" d="M12.56 22.165c1.626 0 2.883-1.249 2.883-3.097v-7.193h1.76V23.53H15.46v-1.082c-.788.815-1.878 1.282-3.101 1.282-2.514 0-4.442-1.815-4.442-4.562v-7.292h1.76v7.192c0 1.881 1.24 3.097 2.883 3.097Zm5.95 1.365h1.677v-1.066a4.28 4.28 0 0 0 3.05 1.266c2.515 0 4.493-1.981 4.493-4.429 0-2.464-1.977-4.445-4.492-4.445a4.242 4.242 0 0 0-3.034 1.265v-4.245H18.51V23.53Zm4.61-1.282a2.936 2.936 0 0 1-2.95-2.947 2.94 2.94 0 0 1 4.076-2.722 2.932 2.932 0 0 1 1.808 2.722 2.944 2.944 0 0 1-2.933 2.947Zm9.672-7.376c-2.497 0-4.392 2.015-4.392 4.412 0 2.53 1.979 4.43 4.543 4.43 1.56 0 2.833-.684 3.688-1.816L35.407 21c-.637.85-1.475 1.249-2.463 1.249-1.443 0-2.599-1.033-2.834-2.414h6.957v-.55c0-2.53-1.81-4.412-4.275-4.412Zm-2.648 3.63c.302-1.299 1.358-2.164 2.615-2.164 1.258 0 2.313.865 2.599 2.164h-5.214Zm12.17-1.965v-1.565h-.586c-.939 0-1.626.433-2.045 1.116v-1.05h-1.677v8.492H39.7v-4.828c0-1.315.804-2.165 1.91-2.165h.704ZM7.917 26.25h8.14v1.992h-5.941v2.85H15.9v1.932h-5.784v2.89h5.942v1.992H7.917V26.25ZM38.18 38.125c2.497 0 3.903-1.196 3.903-2.85 0-1.175-.832-2.052-2.575-2.43l-1.842-.379c-1.07-.199-1.407-.398-1.407-.797 0-.518.516-.837 1.467-.837 1.03 0 1.782.28 2 1.235h2.16c-.12-1.792-1.407-2.988-4.022-2.988-2.258 0-3.843.937-3.843 2.75 0 1.255.872 2.072 2.754 2.47l2.06.478c.812.16 1.03.379 1.03.718 0 .538-.614.877-1.605.877-1.248 0-1.96-.279-2.238-1.236h-2.18c.318 1.793 1.645 2.989 4.339 2.989Zm-6.545-2.172h1.624v1.952H30.92c-1.466 0-2.278-.916-2.278-2.072v-4.582H27v-1.953h1.644v-2.45h2.18v2.45h2.436v1.953h-2.437v4.025c0 .458.317.677.813.677Z M23.92 29.299h2.16v8.607h-2.16v-.777a4.197 4.197 0 0 1-2.734.996c-2.555 0-4.555-2.012-4.555-4.523 0-2.51 2-4.523 4.555-4.523a4.197 4.197 0 0 1 2.734.997v-.777Zm.04 4.303a2.635 2.635 0 0 0-.753-1.866 2.57 2.57 0 0 0-1.842-.764 2.559 2.559 0 0 0-1.838.768 2.622 2.622 0 0 0-.757 1.862 2.649 2.649 0 0 0 .757 1.862 2.584 2.584 0 0 0 1.838.768c1.446 0 2.595-1.156 2.595-2.63Z"/></svg>
+                    Uber Eats
+                </a>
+            </li>
+            <li>
+                <a  data-id="vimeoUrl" class="vimeo">
+                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#19B1E3" d="M0 8a8 8 0 0 1 8-8h34a8 8 0 0 1 8 8v34a8 8 0 0 1-8 8H8a8 8 0 0 1-8-8V8Z"/><path fill="#fff" d="M42.38 16.857c.222-4.422-1.437-6.743-4.974-6.854-4.754-.11-7.96 2.543-9.618 8.07.884-.442 1.769-.552 2.543-.552 1.768 0 2.542.994 2.321 2.984-.11 1.216-.884 2.875-2.321 5.196-1.437 2.211-2.543 3.427-3.206 3.427-.884 0-1.769-1.769-2.543-5.196-.22-1.105-.774-3.758-1.437-7.848-.663-3.87-2.21-5.749-4.975-5.417-1.216.11-2.874 1.216-5.085 3.095-.553.442-1.437 1.216-2.542 2.21-1.106.996-1.99 1.77-2.543 2.212l1.658 2.1c1.548-1.105 2.432-1.658 2.653-1.658 1.216 0 2.322 1.88 3.317 5.527.332 1.106.774 2.875 1.437 5.086.663 2.21 1.105 3.98 1.437 5.085 1.437 3.648 3.095 5.527 5.085 5.527 3.206 0 7.186-2.984 11.829-9.065 4.532-6.08 6.854-10.612 6.965-13.929Z"/></svg>
+                    Vimeo
+                </a>
+            </li>
+            <li>
+                <a  data-id="viadeoUrl" class="viadeo">
+                    <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="20px" height="20px" viewBox="0 0 30 30" enable-background="new 0 0 30 30" xml:space="preserve">
+
+                        <path fill="#f07355" d="M13.79,23.154c0.008,0,0.015-0.001,0.023-0.001c-0.015,0-0.03,0-0.045,0.001H13.79z M25.313,0H4.688
+                                C2.098,0,0,2.1,0,4.688v20.625C0,27.901,2.098,30,4.688,30h20.625C27.901,30,30,27.901,30,25.313V4.688C30,2.1,27.901,0,25.313,0z
+                                 M19.4,22.635C17.947,24.212,16.08,25,13.797,25c-2.292,0-4.165-0.788-5.617-2.365C6.727,21.058,6,19.214,6,17.105
+                                c0-2.091,0.688-3.903,2.063-5.439c1.513-1.692,3.424-2.538,5.734-2.538c0.972,0,1.872,0.151,2.703,0.451
+                                c-0.271,0.525-0.514,1.169-0.58,1.86c-0.66-0.273-1.37-0.411-2.13-0.411c-1.598,0-2.971,0.597-4.119,1.79
+                                s-1.722,2.636-1.722,4.329c0,1.091,0.262,2.108,0.785,3.052s1.23,1.672,2.122,2.186c0.591,0.341,1.221,0.565,1.889,0.681
+                                c5.712-2.209,5.038-12.988,4.997-13.576C17.516,8.833,16.518,6.073,14.677,3c0,0,2.612,1.724,3.063,6.463c0,0,0,0.011,0.002,0.026
+                                c0.033,0.097,0.049,0.149,0.049,0.149c3.353,8.906-3.939,13.5-3.959,13.513c1.048-0.007,2.013-0.26,2.893-0.767
+                                c0.891-0.514,1.598-1.242,2.121-2.186s0.785-1.961,0.785-3.052c0-0.886-0.159-1.702-0.475-2.451
+                                c0.525-0.109,1.158-0.323,1.826-0.731c0.396,0.966,0.598,2.012,0.598,3.141C21.58,19.214,20.854,21.058,19.4,22.635z
+                                 M24.611,9.738c0,0-0.591,1.597-1.821,2.325c-0.928,0.55-2.334,0.723-3.805-0.711c0,0,4.446-2.426,4.612-4.479
+                                c0,0-1.365,3.056-5.101,3.902c0,0-1.363-2.1,0.367-3.831c0,0,0.605-0.665,2.336-1.103c0,0,1.73-0.367,2.665-2.344
+                                C23.865,3.499,25.803,6.192,24.611,9.738z" />
+                    </svg>
+                    <%=__("viadeo")%>
+                </a>
+            </li>
+            <li>
                 <a  data-id="wazeUrl" class="waze">
                     <svg width="20px" height="20px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                         <g clip-path="url(#clip0_23_39)">
@@ -324,14 +307,61 @@
                 </a>
             </li>
             <li>
-                <a  data-id="slideshareUrl" class="slideshare">
-                    <svg width="20px" height="20px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
-                        <path d="M10.9746 5.00104H39.2425C40.1547 5.00608 41.0282 5.37071 41.6734 6.01582C42.3185 6.66092 42.683 7.53442 42.6882 8.44672V24.4094C42.9215 24.2546 43.1521 24.095 43.3799 23.9305C44.5949 23.0756 45.4649 24.2755 44.73 25.3554C42.4447 27.9415 39.5385 29.9031 36.2853 31.0551C36.8081 32.7207 37.0809 34.4543 37.0953 36.1998C37.0953 40.8196 34.5905 45.1994 29.8358 45.1994C29.261 45.236 28.6853 45.1481 28.1475 44.9419C27.6098 44.7357 27.1229 44.4161 26.72 44.0047C26.3171 43.5932 26.0077 43.0997 25.813 42.5579C25.6181 42.0159 25.5423 41.4385 25.591 40.8647V34.22C26.1832 34.3426 26.786 34.4079 27.3908 34.415C28.6964 34.4499 29.9798 34.0713 31.0572 33.3331C32.1347 32.595 32.9514 31.5353 33.3906 30.3053C34.1555 28.1753 32.7156 27.6203 31.6506 29.2552C30.0007 31.8351 27.8108 31.8351 25.501 30.3353V30.0652C25.501 27.8103 26.9179 27.8197 28.3332 27.8291C28.4242 27.8297 28.5152 27.8303 28.6058 27.8303C32.979 28.3092 37.3708 27.4196 41.1882 25.3177V8.45307C41.1848 7.93639 40.9781 7.4418 40.6127 7.07642C40.2474 6.71103 39.7528 6.5043 39.2361 6.50095H10.9441C10.6825 6.49372 10.422 6.53855 10.178 6.63337C9.93407 6.72819 9.71148 6.87081 9.5234 7.05284C9.3353 7.23487 9.18547 7.45264 9.0827 7.69337C8.98016 7.93357 8.92655 8.19179 8.92497 8.45293V26.2848H7.42505V8.44699C7.42742 7.98543 7.52201 7.529 7.70321 7.1045C7.88443 6.67999 8.14863 6.29597 8.48032 5.97499C8.812 5.65401 9.20447 5.40253 9.63468 5.23533C10.0615 5.06945 10.5169 4.98984 10.9746 5.00104Z" fill="#026C97"/>
-                        <path d="M30.9452 26.0006C31.5678 26.0226 32.1885 25.9216 32.772 25.7037C33.3556 25.4857 33.8903 25.1548 34.3458 24.7298C34.8014 24.3049 35.1688 23.7944 35.4268 23.2275C35.6849 22.6605 35.8286 22.0484 35.85 21.4257C35.7838 20.183 35.2332 19.0158 34.3162 18.1745C33.3992 17.3333 32.189 16.885 30.9452 16.926C29.6891 16.8855 28.468 17.344 27.5487 18.2013C26.6297 19.0585 26.0874 20.2448 26.0405 21.5008C26.1065 22.7435 26.6572 23.9108 27.5741 24.752C28.4911 25.5932 29.7015 26.0416 30.9452 26.0006Z" fill="#026C97"/>
-                        <path fill-rule="evenodd" clip-rule="evenodd" d="M24.4651 21.3657C24.4497 21.9954 24.3092 22.6156 24.0521 23.1907C23.7949 23.7656 23.426 24.2837 22.9669 24.7149C22.5078 25.1462 21.9676 25.4818 21.3776 25.7025C20.7877 25.9232 20.1597 26.0245 19.5304 26.0005C18.9079 26.0225 18.2871 25.9215 17.7036 25.7036C17.1201 25.4856 16.5853 25.1547 16.1297 24.7297C15.6742 24.3048 15.3069 23.7943 15.0488 23.2274C14.7907 22.6604 14.6469 22.0483 14.6257 21.4257C14.6917 20.1829 15.2423 19.0157 16.1593 18.1744C17.0763 17.3332 18.2866 16.8849 19.5304 16.9259C20.7694 16.8763 21.9789 17.3128 22.9008 18.1422C23.8226 18.9716 24.384 20.1282 24.4651 21.3657ZM21.0303 27.8303C15.9007 28.2858 10.7761 26.9037 6.5711 23.9305C5.35617 23.0756 4.51621 24.2755 5.31117 25.3405C7.58693 27.9304 10.4894 29.893 13.7407 31.0402C10.9059 40.6996 16.0956 45.1994 20.1903 45.1844C20.7667 45.2278 21.3455 45.1454 21.887 44.943C22.4284 44.7407 22.9193 44.4231 23.3258 44.0122C23.7324 43.6014 24.0449 43.1071 24.2416 42.5637C24.4383 42.0202 24.5146 41.4404 24.4651 40.8647C24.4651 37.9848 24.4651 33.2 24.4651 33.2C25.4173 33.4838 26.3985 33.66 27.39 33.725C28.6617 33.8257 29.9329 33.5235 31.0233 32.8613C32.1135 32.1989 32.9677 31.2102 33.4646 30.0352C34.2896 28.0553 32.8797 27.4854 31.7847 29.0003C29.6698 31.9101 26.955 31.3101 24.1502 28.6253C23.3851 27.9204 22.9652 27.7254 21.0003 27.8303H21.0303Z" fill="#D8711C"/>                        
+                <a  data-id="xUrl" class="x">
+                    <svg width="20" height="20" viewBox="0 0 20 20" fill="#000" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M3.1252 0H16.8752C18.6008 0 20 1.4 20 3.1252V16.8752C20 18.6008 18.6008 20 16.8752 20H3.1252C1.3988 20 0 18.6008 0 16.8752V3.1252C0 1.4 1.3988 0 3.1252 0ZM16.5459 3L11.3335 8.92867L17 17H12.8309L9.01369 11.5636L4.235 17H3L8.46604 10.7834L3 3H7.16908L10.7829 8.14671L15.3109 3H16.5459ZM9.08675 10.0762L9.64142 10.8518L13.4131 16.1334H15.3103L10.687 9.66391L10.1347 8.88837L6.57759 3.91102H4.68037L9.08675 10.0762Z"/></svg>
+                    <%=__("Twitter")%>
+                </a>
+            </li>
+            <li>
+                <a  data-id="yelpUrl" class="yelp">
+                    <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="none" viewBox="0 0 50 50"><path fill="#D32323" d="M42 0H8a8 8 0 0 0-8 8v34a8 8 0 0 0 8 8h34a8 8 0 0 0 8-8V8a8 8 0 0 0-8-8Z"/><path fill="#fff" d="M24.25 7.25c-.125-.458-.5-.792-1-.958-1.583-.417-7.75 1.333-8.875 2.539-.375.375-.5.879-.375 1.292.167.375 7.792 12.542 7.792 12.542 1.125 1.833 2.041 1.583 2.333 1.458.293-.083 1.208-.375 1.125-2.539-.167-2.541-.917-13.792-1-14.334Zm-3.292 22.833c.625-.166 1.042-.75 1.084-1.458.041-.75-.334-1.417-.959-1.667l-1.747-.708c-5.957-2.5-6.208-2.583-6.5-2.583-.458-.042-.879.208-1.166.666-.586.959-.834 4.042-.625 6.084.083.666.208 1.25.375 1.583.25.458.625.75 1.083.75.293 0 .458-.042 6-1.833l2.455-.834Zm3.125 2.292c-.666-.293-1.416-.125-1.833.417l-1.208 1.458c-4.167 5-4.334 5.208-4.459 5.5a1.237 1.237 0 0 0-.083.542c.012.283.131.55.333.75.959 1.166 5.584 2.916 7.084 2.666a1.275 1.275 0 0 0 1.041-.837c.084-.293.125-.5.125-6.375v-2.663c.084-.625-.333-1.208-1-1.458Zm14.5 1.172c-.25-.167-.416-.25-5.957-2.084 0 0-2.417-.833-2.458-.833-.586-.25-1.25-.042-1.709.542-.459.584-.541 1.333-.166 1.916l.958 1.625c3.292 5.417 3.542 5.792 3.75 6 .375.293.833.334 1.333.125 1.417-.586 4.459-4.5 4.667-6.041.082-.505-.043-.964-.418-1.255v.005Zm-8.916-6.167c.879-.167 8.041-1.875 8.625-2.292.375-.25.586-.708.541-1.25v-.041c-.166-1.584-2.875-5.664-4.208-6.334-.458-.208-.958-.208-1.333.042-.25.167-.417.417-3.792 5.083l-1.542 2.119c-.416.5-.416 1.209 0 1.834.417.667.834.998 1.709.834v.005Z"/></svg>
+                    Yelp
+                </a>
+            </li>
+            <li>
+                <a  data-id="youtubeUrl" class="youtube">
+                    <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="20px" height="20px" viewBox="0 0 30 30" enable-background="new 0 0 30 30" xml:space="preserve">
+                        <path fill="#EC2626" d="M12.594,21.504c0,0.273,0,0.488,0.013,0.637c0.013,0.149-0.013,0.244-0.063,0.286
+                                c-0.043,0.167-0.177,0.286-0.393,0.363c-0.21,0.077-0.369-0.029-0.483-0.334c-0.025-0.041-0.025-0.143-0.013-0.297
+                                c0.006-0.161,0.013-0.369,0.013-0.632l-0.032-3.846H10.12l0.038,3.786c0,0.298-0.014,0.548-0.02,0.751
+                                c-0.011,0.202-0.005,0.351,0.02,0.452c0.019,0.167,0.039,0.346,0.063,0.536c0.019,0.19,0.12,0.345,0.312,0.458
+                                c0.177,0.102,0.38,0.155,0.616,0.155c0.24,0,0.469-0.042,0.697-0.113c0.222-0.071,0.432-0.173,0.622-0.298s0.33-0.268,0.418-0.411
+                                l-0.032,0.757l1.257,0.029v-6.103h-1.517V21.504z M18.312,17.574c-0.152-0.083-0.336-0.125-0.533-0.125
+                                c-0.361,0-0.774,0.161-1.25,0.477V15.55h-1.518v8.175l1.258-0.029l0.095-0.513c0.406,0.321,0.774,0.519,1.098,0.59
+                                c0.324,0.071,0.597,0.042,0.825-0.083c0.222-0.132,0.399-0.34,0.526-0.638c0.121-0.291,0.185-0.631,0.185-1.012v-3.168
+                                C18.997,18.265,18.769,17.83,18.312,17.574z M17.848,22.302c0,0.125-0.069,0.232-0.209,0.327c-0.139,0.09-0.291,0.138-0.463,0.138
+                                c-0.178,0-0.336-0.048-0.477-0.138c-0.133-0.095-0.203-0.202-0.203-0.327v-3.584c0-0.125,0.07-0.232,0.203-0.334
+                                c0.141-0.095,0.299-0.148,0.477-0.148c0.172,0,0.324,0.054,0.463,0.148c0.14,0.102,0.209,0.209,0.209,0.334V22.302z
+                                 M14.745,10.924c0.178,0,0.336-0.06,0.476-0.184c0.133-0.119,0.203-0.275,0.203-0.447V6.941c0-0.19-0.07-0.347-0.203-0.472
+                                c-0.14-0.131-0.298-0.196-0.47-0.196c-0.177,0-0.336,0.065-0.469,0.196c-0.141,0.125-0.21,0.281-0.21,0.472v3.352
+                                c0,0.166,0.07,0.31,0.203,0.44C14.402,10.858,14.562,10.924,14.745,10.924z M25.313,0H4.688C2.098,0,0,2.1,0,4.688v20.625
+                                C0,27.901,2.098,30,4.688,30h20.625C27.901,30,30,27.901,30,25.313V4.688C30,2.1,27.901,0,25.313,0z M17.715,5.488h1.416v4.911
+                                c0,0.125,0.05,0.233,0.145,0.328c0.108,0.09,0.235,0.137,0.4,0.137c0.152,0,0.285-0.047,0.394-0.137
+                                c0.114-0.095,0.171-0.203,0.171-0.328V5.488h1.314v6.305h-1.676l0.025-0.512c-0.152,0.208-0.298,0.369-0.444,0.482
+                                c-0.171,0.101-0.361,0.154-0.577,0.154c-0.248,0-0.457-0.054-0.622-0.154c-0.146-0.096-0.272-0.233-0.38-0.417
+                                c-0.039-0.083-0.07-0.173-0.083-0.257c-0.013-0.088-0.025-0.172-0.052-0.256c-0.019-0.101-0.031-0.244-0.031-0.422V5.488z
+                                 M13.533,5.548c0.299-0.209,0.672-0.311,1.136-0.311c0.406,0,0.744,0.065,1.023,0.186c0.272,0.101,0.501,0.285,0.672,0.541
+                                c0.133,0.185,0.234,0.399,0.299,0.637c0.063,0.257,0.095,0.603,0.095,1.037v1.619c0,0.589-0.032,1.012-0.095,1.268
+                                c-0.025,0.237-0.134,0.5-0.33,0.793c-0.197,0.256-0.412,0.422-0.641,0.506c-0.279,0.125-0.591,0.184-0.94,0.184
+                                c-0.36,0-0.69-0.047-0.989-0.154c-0.273-0.083-0.482-0.232-0.609-0.447c-0.133-0.166-0.242-0.397-0.33-0.696
+                                c-0.063-0.278-0.103-0.696-0.103-1.268V7.774c0-0.567,0.057-1.036,0.166-1.405C13.02,5.988,13.236,5.714,13.533,5.548z
+                                 M9.085,2.981l1.003,3.107l1.008-3.107h1.758l-1.897,4.156v4.839H9.345V7.138L7.321,2.981H9.085z M26.016,23.028
+                                c0,0.423-0.096,0.81-0.286,1.167s-0.45,0.667-0.78,0.935c-0.33,0.273-0.711,0.482-1.148,0.637
+                                c-0.432,0.149-0.901,0.232-1.402,0.232H7.613c-0.488,0-0.952-0.083-1.391-0.232c-0.43-0.154-0.812-0.363-1.141-0.637
+                                c-0.33-0.268-0.59-0.577-0.787-0.935c-0.189-0.357-0.285-0.744-0.285-1.167v-6.782c0-0.404,0.096-0.785,0.285-1.154
+                                c0.197-0.363,0.457-0.679,0.787-0.941c0.329-0.262,0.711-0.476,1.141-0.637c0.439-0.154,0.902-0.238,1.398-0.238h14.778
+                                c0.488,0,0.958,0.084,1.396,0.238c0.438,0.161,0.825,0.375,1.155,0.637c0.33,0.263,0.59,0.578,0.78,0.941
+                                c0.196,0.369,0.286,0.75,0.286,1.154V23.028z M22.64,21.641v0.69c0,0.167-0.063,0.298-0.185,0.388
+                                c-0.127,0.095-0.279,0.137-0.457,0.137h-0.234c-0.172,0-0.317-0.042-0.432-0.137c-0.114-0.09-0.178-0.221-0.178-0.388v-1.464
+                                h2.621v-0.852c0-0.322-0.006-0.625-0.013-0.917c-0.013-0.298-0.038-0.524-0.083-0.69c-0.044-0.28-0.178-0.5-0.406-0.673
+                                c-0.222-0.167-0.488-0.286-0.787-0.346c-0.304-0.065-0.608-0.077-0.926-0.029c-0.311,0.041-0.584,0.137-0.818,0.291
+                                c-0.292,0.185-0.502,0.435-0.641,0.757c-0.127,0.321-0.197,0.762-0.197,1.333v1.899c0,0.804,0.229,1.376,0.68,1.715
+                                c0.418,0.315,0.855,0.477,1.313,0.477h0.141c0.514-0.023,0.971-0.232,1.363-0.613c0.299-0.28,0.444-0.649,0.444-1.096
+                                c-0.013-0.148-0.032-0.31-0.069-0.482H22.64z M21.192,19.074c0-0.166,0.063-0.303,0.196-0.41c0.134-0.107,0.286-0.167,0.477-0.167
+                                h0.07c0.196,0,0.361,0.054,0.501,0.155c0.133,0.106,0.203,0.244,0.203,0.422l-0.032,0.757H21.16L21.192,19.074z M10.417,15.55
+                                H5.526v1.048h1.566v7.187h1.511v-7.156h1.814V15.55z" />
                     </svg>
-                        
-                    <%=__("Slideshare")%>
+                    <%=__("Youtube")%>
                 </a>
             </li>
         </ul>
Index: src/js/JEditor/ParamsPanel/Views/SocialNetworks.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/SocialNetworks.js	(révision 13376)
+++ src/js/JEditor/ParamsPanel/Views/SocialNetworks.js	(révision 13377)
@@ -50,6 +50,9 @@
                 wazeUrl: 'waze',
                 whatsappUrl: 'whatsapp',
                 slideshareUrl: 'slideshare',
+                borneoUrl: 'borneo',
+                pagesjauneUrl: 'pagesjaunes',
+                boncoinUrl: 'leboncoin',
             };
             
         },
Index: src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 13376)
+++ src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 13377)
@@ -30,6 +30,9 @@
         "Invalid_linkedinUrl":"L'url renseignée ne semble pas valide",
         "Invalid_viadeoUrl":"L'url renseignée ne semble pas valide",
         "Invalid_instagramUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_borneoUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_boncoinUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_pagesjauneUrl":"L'url renseignée ne semble pas valide",
         "Invalid_GAKey":"Le code renseigné ne semble pas valide",
         "Invalid_GTMKey":"Le code renseigné ne semble pas valide",
         "Invalid_WTKey":"Le code renseigné ne semble pas valide",
Index: src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 13376)
+++ src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 13377)
@@ -30,6 +30,9 @@
         "Invalid_linkedinUrl":"L'url renseignée ne semble pas valide",
         "Invalid_viadeoUrl":"L'url renseignée ne semble pas valide",
         "Invalid_instagramUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_borneoUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_boncoinUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_pagesjauneUrl":"L'url renseignée ne semble pas valide",
         "Invalid_GAKey":"Le code renseigné ne semble pas valide",
         "Invalid_GTMKey":"Le code renseigné ne semble pas valide",
         "Invalid_WTKey":"Le code renseigné ne semble pas valide",
Index: src/js/JEditor/ParamsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 13376)
+++ src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 13377)
@@ -18,6 +18,9 @@
         "Invalid_linkedinUrl":"This is not a valid url",
         "Invalid_viadeoUrl":"This is not a valid url",
         "Invalid_instagramUrl":"This is not a valid url",
+        "Invalid_borneoUrl":"This is not a valid url",
+        "Invalid_boncoinUrl":"This is not a valid url",
+        "Invalid_pagesjauneUrl":"This is not a valid url",
         "Invalid_GAKey":"This is not a valid code",
         "Invalid_GTMKey":"This is not a valid code",
         "Invalid_WTKey":"This is not a valid code",
@@ -38,7 +41,7 @@
         "Twitter":"X(Twitter)",
         "Pinterest":"Pinterest",
         "Google":"Google +",
-        "Mybusiness":"Google My Business",
+        "Mybusiness":"Google Business Profil",
         "copypastecodeSocialNetworks":"Copy and paste the following code to an HTML block where you want to put your links to the social networks",
         "Copy":"Copy",
         "Settings":"Settings",
@@ -121,6 +124,9 @@
         "qualityCompression" : "Compression quality",
         "lowQuality" : "Normal (default)",
         "highQuality" : "High",
+        "Borneo": "Borneo",
+        "Pagesjaune" : "Pagesjaunes",
+        "Boncoin": "Leboncoin"
     },
     "fr-fr": true,
     "fr-ca": true
Index: src/less/imports/components.less
===================================================================
--- src/less/imports/components.less	(révision 13376)
+++ src/less/imports/components.less	(révision 13377)
@@ -190,10 +190,11 @@
     left: auto;
 }
 .dropdown-menu > li {
-    border-top: 1px solid #EEE;
+    border-bottom: 1px solid #EEE;
+    margin-top: 6px;
 
-    &:first-child {
-        border-top: none;
+    &:last-child {
+        border-bottom: none;
     }
 }
 .dropdown-menu  li  a {
@@ -248,4 +249,9 @@
   top: auto;
   bottom: 100%;
   margin-bottom: 1px;
+}
+.open > .social-network {
+    display: grid;
+    grid-template-columns: repeat(4, 1fr);
+    padding: 15px;
 }
\ No newline at end of file
