Revision: r13513
Date: 2024-11-26 10:04:33 +0300 (tlt 26 Nov 2024) 
Author: frahajanirina 

## Commit message
Wishlist:IDEO3.2:afficher un message sur le bouton dans l’admin & fix rendu du bouton

## Files changed

## Full metadata
------------------------------------------------------------------------
r13513 | frahajanirina | 2024-11-26 10:04:33 +0300 (tlt 26 Nov 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonBlock.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/button_block/button_block.less

Wishlist:IDEO3.2:afficher un message sur le bouton dans l’admin & fix rendu du bouton
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js	(révision 13512)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js	(révision 13513)
@@ -46,7 +46,8 @@
         "advancedCSS": "Advanced",
         "DifferentTextOnMobile": "Different text on mobile ?",
         "errorWithoutTextOnMobile": "You have not specified the text for mobile. You must indicate the text to display or deactivate the option.",
-        "Invalid_text": "Invalid text"
+        "Invalid_text": "Invalid text",
+        "btnTextOnMobileWarning": "Alternative text on mobile is missing"
     },
     "fr-fr": true,
     "fr-ca": true
Index: src/less/imports/button_block/button_block.less
===================================================================
--- src/less/imports/button_block/button_block.less	(révision 13512)
+++ src/less/imports/button_block/button_block.less	(révision 13513)
@@ -52,8 +52,22 @@
         &.text-right .button{
             text-align:right;
         }
-        &.text-center .button{
-            text-align: center;
+        &.text-center {
+            .button{
+                text-align: center;
+            }
+            .txt-warning {
+                position: absolute;
+                color: #d42525;
+                left: 50%;
+                transform: translateX(-50%);
+                bottom: 10px;
+                background-color: #f5f5f5;
+                padding: 3px 8px;
+                border-radius: 14px;
+                font-size: 11px;
+                font-family: sans-serif;
+            }
         }
     }
 }
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonBlock.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonBlock.html	(révision 13512)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonBlock.html	(révision 13513)
@@ -5,4 +5,7 @@
     </span>
     <span class="txt blk-button__label"><span><%=text%></span></span>
   </a>
+  <span class="txt-warning">
+    <span class="icon-warning"></span> <%= __("btnTextOnMobileWarning")%>
+  </span>
 </div>
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js	(révision 13512)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js	(révision 13513)
@@ -24,7 +24,33 @@
                         initialize: function() {
                             this._super();
                             this._contentTemplate = this.buildTemplate(template, translate);
+                            this.buttonOption = this.model.options.ButtonOption;
+                            this.listenTo(this.buttonOption, "radio:change", this.onChangeRadio); 
+                            this.listenTo(this.buttonOption, "input:change", this.onInputChange);
+                            this.delegateEvents();                   
                         },
+                        onChangeRadio: function() {
+                            var isDifferentText, textOnMobile, txtWarning;
+
+                            isDifferentText = this.model.options.ButtonOption.isDifferentText;
+                            textOnMobile = this.model.options.ButtonOption.textOnMobile;
+                            txtWarning = $('.txt-warning');
+
+                            if (isDifferentText && (textOnMobile == null || textOnMobile == '')) {
+                                txtWarning.show();
+                                
+                            } else {
+                                txtWarning.hide();
+                            }
+                        },
+                        onInputChange: function(length){
+                            var txtWarning = $('.txt-warning');
+                            if (length === 0) {
+                                txtWarning.show();
+                            } else {
+                                txtWarning.hide();
+                            }
+                        },
                         _onLoad: function() {
                             this.render();
                         },
@@ -83,6 +109,13 @@
                             this.$(".block-button").attr("class", "block-button " + sizeButton + " " + alignButton + " " + alignText + " " + color);
                             var textButton = this.model.options.ButtonOption.text?this.model.options.ButtonOption.text:translate('button');
                             this.$(".txt span").text(textButton);
+                            var isDifferentText = this.model.options.ButtonOption.isDifferentText;
+                            var textOnMobile = this.model.options.ButtonOption.textOnMobile;
+                            if (isDifferentText && (textOnMobile == null || textOnMobile == '')) {
+                                $('.txt-warning').show();
+                            } else {
+                                $('.txt-warning').hide();
+                            }
                             return this;
                         },
                         edit: function() {
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonOptionView.js	(révision 13512)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonOptionView.js	(révision 13513)
@@ -22,7 +22,8 @@
                         className: "panel-content button-panel ",
                         events: {
                             'click .text-different-radio': '_onClickAddDifferentText',
-                            'change .text-different-input': 'inputChange'
+                            'change .text-different-input': 'inputChange',
+                            'input .text-different-input': 'onInputText'
                         },
                         /**
                          * initialise l'objet
@@ -61,6 +62,7 @@
                         },
                         _onClickAddDifferentText: function(){
                             this.model.isDifferentText = !this.model.isDifferentText;
+                            this.model.trigger("radio:change");
 
                             this.render();
                         },
@@ -67,6 +69,11 @@
                         inputChange: function(event) {
                             this.model.textOnMobile = event.target.value;
                             this.render();
+                        },
+                        onInputText: function(event) {
+                            var inputValue = event.target.value;
+                            var inputLength = inputValue.length;
+                            this.model.trigger('input:change', inputLength);
                         }
                     });
 
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js	(révision 13512)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js	(révision 13513)
@@ -45,5 +45,6 @@
     "advancedCSS": "Avancé",
     "DifferentTextOnMobile": "Texte différent sur mobile ?",
     "errorWithoutTextOnMobile": "Vous n’avez pas indiqué le texte pour mobile. Vous devez indiquer le texte à afficher ou désactiver l’option.",
-    "Invalid_text": "Texte invalide"
+    "Invalid_text": "Texte invalide",
+    "btnTextOnMobileWarning": "Manque text altérnatif mobile"
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js	(révision 13512)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js	(révision 13513)
@@ -45,6 +45,7 @@
     "advancedCSS": "Avancé",
     "DifferentTextOnMobile": "Texte différent sur mobile ?",
     "errorWithoutTextOnMobile": "Vous n’avez pas indiqué le texte pour mobile. Vous devez indiquer le texte à afficher ou désactiver l’option.",
-    "Invalid_text": "Texte invalide"
+    "Invalid_text": "Texte invalide",
+    "btnTextOnMobileWarning": "Manque text altérnatif mobile"
 
 });
