Revision: r10420
Date: 2023-02-16 14:39:08 +0300 (lkm 16 Feb 2023) 
Author: norajaonarivelo 

## Commit message
Wishlist Ideo3.2: Ajout reseaux sociaux Correction validation Url

## Files changed

## Full metadata
------------------------------------------------------------------------
r10420 | norajaonarivelo | 2023-02-16 14:39:08 +0300 (lkm 16 Feb 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/dev/#librairies/vendor/CoreConfigurationService/config/module.config.php
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Models/Params.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js

Wishlist Ideo3.2: Ajout reseaux sociaux Correction validation Url
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Models/Params.js
===================================================================
--- src/js/JEditor/ParamsPanel/Models/Params.js	(révision 10419)
+++ src/js/JEditor/ParamsPanel/Models/Params.js	(révision 10420)
@@ -21,13 +21,13 @@
         linkedinUrl:/^(https?\:\/\/)?(www\.)?linkedin\.com\/(.*)/,
         youtubeUrl:/^(https?\:\/\/)?(www\.)?youtube\.com\/(.*)/,
         instagramUrl:/^(https?\:\/\/)?(www\.)?instagram\.com\/(.*)/,
-        skypeUrl :/^(https?\:\/\/)?(www\.)?skype\.com\/(.*)|^(https?\:\/\/)?(www\.)?web.skype\.com\/(.*)|^(skype)\:(.*)\?(call|chat)/,
-        theforkUrl:/^(https?\:\/\/)?(www\.)?thefork\.(com|com\.au|fr)\/(.*)/,
+        skypeUrl :/^(https?\:\/\/)?(www\.)?skype\.com\/(.*)|^(https?\:\/\/)?(www\.)?web.skype\.com\/(.*)|^(skype)\:(.*)\?(call|chat)|^(https?\:\/\/)?(www\.)?join\.skype\.com\/invite\/(.*)/,
+        theforkUrl:/^(https?\:\/\/)?(www\.)?(.*)?(thefork|lafourchette)\.(com|com\.au|fr)\/(.*)/,
         tiktokUrl:/^(https?\:\/\/)?(www\.)?tiktok\.com\/(.*)/,
         tripadvisorUrl:/^(https?\:\/\/)?(www\.)?tripadvisor\.(com|com\.au|fr|ca)\/(.*)/,
         wazeUrl:/^(https?\:\/\/)?(www\.)?waze\.com\/(.*)/,
         whatsappUrl:/^(https?\:\/\/)?(www\.)?(api\.whatsapp\.com|wa\.me)\/(.*)/,
-        slideshareUrl:/^(https?\:\/\/)?(www\.)?slideshare\.net\/(.*)/,
+        slideshareUrl:/^(https?\:\/\/)?(www\.)?((fr|de|es|pt)\.)?slideshare\.net\/(.*)/,
     };  
     //verification for google fonts
 
Index: src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 10419)
+++ src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 10420)
@@ -1,5 +1,12 @@
 define({
         "socialNetworks": "Réseaux sociaux",
+        "Invalid_slideshareUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_whatsappUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_wazeUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_tripadvisorUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_tiktokUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_skypeUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_theforkUrl":"L'url renseignée ne semble pas valide",
         "Invalid_facebookUrl":"L'url renseignée ne semble pas valide",
         "Invalid_twitterUrl":"L'url renseignée ne semble pas valide",
         "Invalid_googleUrl":"L'url renseignée ne semble pas valide",
Index: src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 10419)
+++ src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 10420)
@@ -1,5 +1,12 @@
 define({
         "socialNetworks": "Réseaux sociaux",
+        "Invalid_slideshareUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_whatsappUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_wazeUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_tripadvisorUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_tiktokUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_skypeUrl":"L'url renseignée ne semble pas valide",
+        "Invalid_theforkUrl":"L'url renseignée ne semble pas valide",
         "Invalid_facebookUrl":"L'url renseignée ne semble pas valide",
         "Invalid_twitterUrl":"L'url renseignée ne semble pas valide",
         "Invalid_googleUrl":"L'url renseignée ne semble pas valide",
