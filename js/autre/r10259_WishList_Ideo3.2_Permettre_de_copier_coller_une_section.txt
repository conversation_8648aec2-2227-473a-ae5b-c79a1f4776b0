Revision: r10259
Date: 2023-01-30 11:33:37 +0300 (lts 30 Jan 2023) 
Author: norajaonarivelo 

## Commit message
WishList Ideo3.2:Permettre de copier/coller une section

## Files changed

## Full metadata
------------------------------------------------------------------------
r10259 | norajaonarivelo | 2023-01-30 11:33:37 +0300 (lts 30 Jan 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/assets/ACLs/admin.json
   M /branches/ideo3_v2/integration/assets/ACLs/lpadmin.json
   M /branches/ideo3_v2/integration/assets/ACLs/root.json
   M /branches/ideo3_v2/integration/assets/ACLs/superadmin.json
   M /branches/ideo3_v2/integration/src/js/JEditor/App/App.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Sections/Templates/section.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Sections/Views/SectionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Zones/Templates/zone.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Zones/Views/ContentZoneView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Zones/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Zones/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/edit.less

WishList Ideo3.2:Permettre de copier/coller une section
------------------------------------------------------------------------

## Diff
Index: assets/ACLs/admin.json
===================================================================
--- assets/ACLs/admin.json	(révision 10258)
+++ assets/ACLs/admin.json	(révision 10259)
@@ -1,4 +1,8 @@
 {
+"access_copy_paste_section":{
+    "value": false,
+    "comparison": null
+},
  "access_panel_params": {
   "value": false,
   "comparison": null
Index: assets/ACLs/lpadmin.json
===================================================================
--- assets/ACLs/lpadmin.json	(révision 10258)
+++ assets/ACLs/lpadmin.json	(révision 10259)
@@ -1,4 +1,8 @@
 {
+    "access_copy_paste_section":{
+        "value": false,
+        "comparison": null
+    },
     "access_panel_params": {
      "value": false,
      "comparison": null
Index: assets/ACLs/root.json
===================================================================
--- assets/ACLs/root.json	(révision 10258)
+++ assets/ACLs/root.json	(révision 10259)
@@ -1,4 +1,8 @@
 {
+    "access_copy_paste_section":{
+        "value": true,
+        "comparison": null
+    },
     "access_panel_params": {
         "value": true,
         "comparison": null
Index: assets/ACLs/superadmin.json
===================================================================
--- assets/ACLs/superadmin.json	(révision 10258)
+++ assets/ACLs/superadmin.json	(révision 10259)
@@ -1,4 +1,8 @@
 {
+    "access_copy_paste_section":{
+        "value": true,
+        "comparison": null
+    },
  "access_panel_params": {
   "value": true,
   "comparison": null
Index: src/js/JEditor/App/App.js
===================================================================
--- src/js/JEditor/App/App.js	(révision 10258)
+++ src/js/JEditor/App/App.js	(révision 10259)
@@ -48,7 +48,8 @@
         tabindex: 0
       },
       events: {
-        'click .panel-link': '_onPanelLinkClick'
+        'click .panel-link': '_onPanelLinkClick',
+        'click a.logout'  : '_onClickLogout',
       },
       checkSessionTimeOut: function() {
         var milliTime, time, now, exp;
@@ -209,6 +210,9 @@
         this.navigateTo(targetUrl);
         return false;
       },
+      _onClickLogout:function(){
+        sessionStorage.removeItem('modelSectionClone');
+      },
       navigateTo: function(panelRoute) {
         this.params.lastUrl = panelRoute;
         this.params.save();
Index: src/js/JEditor/PagePanel/Contents/Sections/Templates/section.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Sections/Templates/section.html	(révision 10258)
+++ src/js/JEditor/PagePanel/Contents/Sections/Templates/section.html	(révision 10259)
@@ -16,7 +16,22 @@
 		<% } else {%>
 			<span class="menu-item move"><span class="icon icon-move"></span></span>
 			<span class="menu-item add-column"><span class="icon icon-add-column"></span><span class="text"><%= __("addColumn")%></span></span>
-			<span class="menu-item duplicate"><span class="icon icon-dupplicate-section"></span><span class="text"><%= __("duplicateSection")%></span></span>
+		<% if(canCopyPasteSection) { %>
+			<span class="menu-item menu-group">
+				<span class="menu-item copy">
+					<span class="icon icon-dupplicate-section"></span>
+					<span class="text">Copier</span>
+				</span>
+				<span class="menu-item paste-before" data-paste="before" style="display:none">
+					<span class="icon icon-add"></span>
+					<span class="text">Coller avant</span>
+				</span>
+				<span class="menu-item paste-after " data-paste="after" style="display:none">
+					<span class="icon icon-add"></span>
+					<span class="text">Coller après</span>
+				</span>
+			</span>
+		<% } %>
 			<span class="menu-item style"><span class="icon icon-style"></span><span class="text"><%= __("style")%></span></span>
 			<% if(canLockSection){ %>
 				<% if(locked){ %>
Index: src/js/JEditor/PagePanel/Contents/Sections/Views/SectionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Sections/Views/SectionView.js	(révision 10258)
+++ src/js/JEditor/PagePanel/Contents/Sections/Views/SectionView.js	(révision 10259)
@@ -50,7 +50,8 @@
                         class: "SectionView",
                         events: {
                             "click .add-column": "addColumn",
-                            "click .duplicate": "duplicate",
+                            "click .copy": "copySection",
+                            "click .paste-after,.paste-before": "pasteSection",
                             "click .menu-item.delete": "confirmDelete",
                             "click .menu-item.unlock": "toggleLockSection",
                             "click .menu-item.lock": "toggleLockSection",
@@ -105,10 +106,31 @@
                          * Dupplique la section et la rajoute à la collection à laquelle appartient celle d'origine
                          */
                         duplicate: function(e) {
-                            var clone = this.model.clone();
+                            var clone = this.model.clone(); 
                             //delete clone.id;
-                            this.model.parent.addChild(clone);
+                            this.model.parent.addChild(clone,0);
                         },
+                        copySection: function(){
+                            var clone =this.model.clone();
+                            sessionStorage.setItem("modelSectionClone",JSON.stringify(clone));
+                            this.$(".paste-after,.paste-before").show();
+                            console.log("Section copié");
+                        },
+                        pasteSection: function(e){
+                            var dataPatse=e.currentTarget;
+                            var ValuePaste=dataPatse.getAttribute('data-paste');
+                            var myModel=this.model;
+                            var parentModel=myModel.parent;
+                            var position=parentModel._index[myModel.cid];
+                            if(ValuePaste ==='after')
+                                position++;
+                            var clone = JSON.parse(sessionStorage.getItem("modelSectionClone"));
+                            if(clone !== null){
+                                var section = new Section(clone);
+                                this.model.parent.addChild(section,position);
+                            }
+                           
+                        },
                         /**
                          * ajoute une colonne à la section
                          */
@@ -174,17 +196,20 @@
                          */
                         render: function() {
                             var isPopup = this.model.isPopup();
+                            var cloneSection = JSON.parse(sessionStorage.getItem("modelSectionClone"));
                             this.undelegateEvents();
-                            
                             this.$el.html(this._template({
                                 locked: this.model.locked != 'unlocked' ? true : false,
                                 roleLocked: SectionsUtils.getSectionRoleLocked(this.app.user.role, this.model.locked),
                                 canLockSection:this.app.user.can("lock_section"), 
+                                canCopyPasteSection:this.app.user.can("access_copy_paste_section"),
                                 isPopup:isPopup,
                                 isCanDefinePopup: this.options.parentView.canDefinePopup
                                // isCanDefinePopup :false
                             }));
-
+                            if(cloneSection)
+                                this.$(".paste-after,.paste-before").show();
+                            
                             this.childContainer = this.$('.content');
                             this.childContainer.attr("data-child-count", this.childViews.length);
                             this.dom[this.cid].contentElement = this.$('.content-placeholder');
Index: src/js/JEditor/PagePanel/Contents/Zones/Templates/zone.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Zones/Templates/zone.html	(révision 10258)
+++ src/js/JEditor/PagePanel/Contents/Zones/Templates/zone.html	(révision 10259)
@@ -1,4 +1,4 @@
-<div class="nothing-to-display <%= empty?'active empty':'' %>">
+<div class="nothing-to-display <%= empty?'active empty':'' %>" style="z-index: 1;">
     <div class="wrapper">
         <div class="icon">
             <span class="icon-file"></span>
@@ -9,5 +9,8 @@
         <span class="how-to">
             <%= __("howToAddContent") %>
         </span>
+        <% if(canPaste)    {%>
+            <button type="button" class="btn btn-default add-content" id="PasteSection"><span class="icon icon-add"></span><span class="label"><%= __("pasteTheSection")%></span></button>
+        <% }%>
     </div>
 </div>
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Zones/Views/ContentZoneView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Zones/Views/ContentZoneView.js	(révision 10258)
+++ src/js/JEditor/PagePanel/Contents/Zones/Views/ContentZoneView.js	(révision 10259)
@@ -20,6 +20,9 @@
 
 
         },
+        events: {
+            "click button#PasteSection": "pasteSection",
+        },
         options: {childSelector: ".editable-section",
             sortableItemsClass: "editable-section",
             singleViewType: SectionView,
@@ -30,7 +33,11 @@
             helper: "clone"
         },
         render: function() {
-            this.$el.html(this._template({empty: this.model.children.length === 0}));
+            var canPaste = (JSON.parse(sessionStorage.getItem("modelSectionClone"))!==null)?true:false;
+            this.$el.html(this._template({
+                empty: this.model.children.length === 0,
+                canPaste:canPaste
+            }));
             this.dom[this.cid].emptyZone = this.$('.emptyzone');
 //            this.childContainer = this.$('.sections');
             this.childContainer = this.$el;
@@ -65,6 +72,13 @@
                 });
             }
             return !isPopup;
+        },
+        pasteSection: function(){
+            var clone = JSON.parse(sessionStorage.getItem("modelSectionClone"));
+            if(clone !== null){
+                var section = new Section(clone);
+                this.model.addChild(section);
+            }
         }
     });
     return ContentZoneView;
Index: src/js/JEditor/PagePanel/Contents/Zones/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Zones/nls/fr-fr/i18n.js	(révision 10258)
+++ src/js/JEditor/PagePanel/Contents/Zones/nls/fr-fr/i18n.js	(révision 10259)
@@ -28,5 +28,6 @@
   "tabmobile": "Onglets mobile",
   "confirmUncustomize":"Vous vous apprêtez à réinitialiser cette zone de la page. Tout le contenu sera perdu‏. Êtes-vous sûr de vouloir réinitialiser cette zone ?",
   "uncustomize":"Êtes-vous sûr ?",
-  "versions": "Versions"
+  "versions": "Versions",
+  "pasteTheSection" : "Coller la section"
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Zones/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Zones/nls/i18n.js	(révision 10258)
+++ src/js/JEditor/PagePanel/Contents/Zones/nls/i18n.js	(révision 10259)
@@ -29,7 +29,8 @@
     "tabmobile": "Mobile tabs",
     "confirmUncustomize":"You are about to reset this zone. All content will be lost. Are you sure you want to reset this zone?",
     "uncustomize":"Are-you sure ?",
-    "versions": "Versions"
+    "versions": "Versions",
+    "pasteTheSection" : "paste the section"
   },
   "fr-fr": true, "fr-ca":true
 });
\ No newline at end of file
Index: src/less/imports/edit.less
===================================================================
--- src/less/imports/edit.less	(révision 10258)
+++ src/less/imports/edit.less	(révision 10259)
@@ -111,6 +111,45 @@
 
     }
 }
+#PasteSection {
+    background: #36b1c0;
+  color: #fff;
+  border-color: #36b1c0;
+    width: 78%;
+    height: 50px;
+    justify-content: center;
+    margin: 7px 12% !important;
+}
+#PasteSection:hover{
+  background-color: #3dc9da;
+}
+#PasteSection > .icon-add {
+    display: inline-block;
+  margin:  0px !important;;
+  width: 29px !important;
+  height: 25px !important;;
+  padding: 10px 0px 10px 0px !important;;
+  background:transparent !important;
+  font-size: 14px !important;
+}
+#page-edit #content-editor .editable-section .edition-menu span.menu-item.menu-group {
+    padding: 0;
+    height: 39px;
+    border: 1px solid #999999;
+    border-radius: 6px;
+    overflow: hidden;
+    margin: 2px;
+}
+
+#page-edit #content-editor .editable-section .edition-menu span.menu-item.menu-group span.menu-item {
+    height: 20px;
+    line-height: 20px;
+    padding: 10px 5px;
+}
+
+#page-edit #content-editor .editable-section .edition-menu span.menu-item.menu-group span.menu-item>.icon {
+    line-height: 20px;
+}
 #page-edit #content-editor{
     margin: auto;
     max-width:1200px;
