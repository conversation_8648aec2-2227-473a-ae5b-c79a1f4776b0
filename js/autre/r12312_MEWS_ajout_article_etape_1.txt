Revision: r12312
Date: 2024-05-24 12:21:47 +0300 (zom 24 Mey 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
MEWS: ajout article etape 1

## Files changed

## Full metadata
------------------------------------------------------------------------
r12312 | sraz<PERSON><PERSON><PERSON>oa | 2024-05-24 12:21:47 +0300 (zom 24 Mey 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/Article.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/ArticleEditorView.js
   M /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js

MEWS: ajout article etape 1
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Models/Article.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Article.js	(révision 12311)
+++ src/js/JEditor/NewsPanel/Models/Article.js	(révision 12312)
@@ -5,8 +5,10 @@
     "./Categorie",
     "./CategorieCollection",
     "JEditor/Commons/Pages/Models/Page",
-    "JEditor/Commons/Pages/Models/PageCollection"
-], function(_, Events, Model, Categorie, CategorieCollection, Page, PageCollection) {
+    "JEditor/Commons/Pages/Models/PageCollection",
+    "JEditor/FilePanel/Models/FileCollection"
+
+], function(_, Events, Model, Categorie, CategorieCollection, Page, PageCollection, FileCollection) {
     var Article = Model.extend(
             /**
              * @lends Article
@@ -17,11 +19,11 @@
                             category: [],
                             state: [0],
                             title: "",
-                            lang:"",
-                            ressource: "",
+                            lang:null,
+                            ressource: null,
                             introduction: "",                
-                            publicationDate:"", 
-                            programmingDate: "",
+                            publicationDate:null, 
+                            programmingDate: null,
                             metaTitle: "",
                             metaOpengraph: "",
                             page:null,
@@ -31,30 +33,39 @@
                             this._super();
                             this.file = null;
                             this.getFile();
-                            if (this.category ){
-                                var categories = CategorieCollection.getInstance();
-                                if (Array.isArray(this.category)) {
-                                    this.category = this.category.map(function(categoryId) {
-                                        if (!(categoryId instanceof Categorie)) {
-                                            return categories.findWhere({id: categoryId});
-                                        } else {
-                                            return categoryId;
-                                        }
-                                    });
-                                }
-                                else if (!(this.category instanceof Categorie)) {
-                                    this.category = categories.findWhere({id: categoryId});
-            
-                                }
+                            this.transformCategory();
+                            this.transformPage();
+                    
+                            this.lastState = this.toJSON();
+                            this.on(Events.BackboneEvents.SYNC, this._onSync);
+                        },
+                    
+                        transformCategory: function(category) {
+                            if (!category) category = this.get('category');
+                            var categories = CategorieCollection.getInstance();
+                    
+                            if (Array.isArray(category)) {
+                                return category.map(function(categoryId) {
+                                    if (!(categoryId instanceof Categorie)) {
+                                        return categories.findWhere({id: categoryId});
+                                    } else {
+                                        return categoryId;
+                                    }
+                                });
+                            } else if (!(category instanceof Categorie)) {
+                                return categories.findWhere({id: category});
                             }
-                            if (!(this.page instanceof Page)) {
+                            return category;
+                        },
+                    
+                        transformPage: function(page) {
+                            if (!page) page = this.get('page');
+                            if (page && !(page instanceof Page)) {
                                 var pageCollection = PageCollection.getInstance();
                                 this.page = pageCollection.findWhere({id: this.page});
                             }
-                            
-                            this.lastState = this.toJSON();
-                            this.on(Events.BackboneEvents.SYNC, this._onSync);
-                        },
+                            return page;
+                        },                    
                         _onSync: function() {
                             this.lastState = this.toJSON();
                         },
@@ -77,7 +88,9 @@
                             return this.file;
                         },
                         parse : function(data) {
-                            ret = (data.data)? data.data : data
+                            ret = (data.data)? data.data : data;
+                            ret.category = this.transformCategory(ret.category);
+                            ret.page = this.transformPage(ret.page);
                             return ret;
                         },
                         toJSON: function() {
Index: src/js/JEditor/NewsPanel/Views/ArticleEditorView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticleEditorView.js	(révision 12311)
+++ src/js/JEditor/NewsPanel/Views/ArticleEditorView.js	(révision 12312)
@@ -67,6 +67,8 @@
         zonesFetched: false,
         selectedZoneVersion: null,
         versionsCollection: new VersionsCollection(),
+        page : null,
+        page : null,
         initialize: function () {
             this._super();
             this.currentZoneID = this.options.zoneID || null;
@@ -74,7 +76,7 @@
             if (this.model) {
                 this.zoneToolbox = new ZoneToolBox();
             }
-            else this.model = new Article()
+            else this.model = new Article();
             this.pagePreview = new PagePreview();
             this.articleDetail = new AddArticleView({
                 model: this.model,
@@ -139,9 +141,10 @@
             this.trigger(Events.LoadEvents.LOAD_START, this);
             try {
                 
-                if (this.currentZoneID) {
-                    this.versionsCollection.zoneId = this.currentZoneID;
-                    
+                if (this.model.page) {
+                   this.page = this.model.page
+                   this.versionsCollection.zoneId = parseInt(this.page.main_zone);
+            
                     this.listenToOnce(this.versionsCollection, Events.BackboneEvents.SYNC, this.onAllZoneLoad);
                     this.listenToOnce(this.versionsCollection, Events.BackboneEvents.ERROR, function (zoneCollection, xhr, options) {
                         this.trigger(Events.LoadEvents.LOAD_ERROR, this, xhr, options);
Index: src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 12311)
+++ src/js/JEditor/NewsPanel/Views/ArticlesCollectionView.js	(révision 12312)
@@ -88,7 +88,7 @@
        
         json = {
           cid : list.id,
-          image : (list.image)?list.image.url:'',
+          image : (list.file)?list.file.fileUrl:'',
           title : list.title,
           author : 'srazanandralisoa',
           categorie : categories,
