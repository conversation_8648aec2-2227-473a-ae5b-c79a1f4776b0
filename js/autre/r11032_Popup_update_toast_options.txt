Revision: r11032
Date: 2023-06-14 09:16:58 +0300 (lrb 14 Jon 2023) 
Author: rrakotoarinelina 

## Commit message
Popup : update toast options

## Files changed

## Full metadata
------------------------------------------------------------------------
r11032 | rrakotoarinelina | 2023-06-14 09:16:58 +0300 (lrb 14 Jon 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/dev/#librairies/vendor/CoreMoteurDeRendu/src/CoreMoteurDeRendu/Core/AmbianceEngineRenderer.php
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/Models/PopupStyle.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/Templates/popupStyle.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/Views/PopupStyleView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/nls/i18n.js

Popup : update toast options
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Options/Models/PopupStyle.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/Models/PopupStyle.js	(révision 11031)
+++ src/js/JEditor/PagePanel/Contents/Options/Models/PopupStyle.js	(révision 11032)
@@ -16,7 +16,7 @@
                  * @lends PopupStyle.prototype
                  */
                         {
-                            defaults: {optionType: 'popupStyle', size:'medium', display:'toast',alignment:'bottom-right', priority: 60},
+                            defaults: {optionType: 'popupStyle', size:'medium', display:'toast',color:'surface',alignment:'bottom-right', priority: 60},
                             initialize: function() {
                                 this._super();
                             },
@@ -27,7 +27,7 @@
                             },
                             translate: translate
                         });
-                PopupStyle.SetAttributes(['size','display','alignment']);
+                PopupStyle.SetAttributes(['size','display','color','alignment']);
                 PopupStyle.tanslate = translate;
                 return PopupStyle;
             });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Options/Templates/popupStyle.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/Templates/popupStyle.html	(révision 11031)
+++ src/js/JEditor/PagePanel/Contents/Options/Templates/popupStyle.html	(révision 11032)
@@ -109,70 +109,136 @@
             </div>
             </div>
         </div>
-        
-        <!--  style pour toast  -->
-        <div class="alignment-container">
-        <header>
+
+                
+        <!--  couleur pour toast  -->
+        <div class="gallery-template-option color-container">
+       
+            <header>
             <h3 class="option-name">
-                <span class="icon-button-size icon-midsize"></span>
-                <%=__("alignmentPopup")%>
+                <span class="icon-contrast icon-midsize"></span>
+                <%=__("colorPopup")%>
             </h3>
             <span class="panel-content-legend">
-                <%=__("alignmentPopupLegend")%>
+            <%=__("colorPopupLegend")%>
             </span>
-        </header>
-        <div class="option-content">
-            <div class="controlPanel-selector">
-                <div class="option radio">
-                        <div class="button-size-radio">
-                            <label for="bottom-right" class="inline-block-label">
-                                <input id="bottom-right" type="radio" class="field-input" value="top-right" name="alignment" <%=alignement==="top-right"? ' checked="checked" ':'' %> />
-                                
-                                <div class="inline-block-label__top">
-                                    <span class="icon-align-top_right"></span>
-                                </div>
-                                <div class="inline-block-label__bottom">
-                                    <span class="icon-radio-inactive"></span>
-                                    <span class="icon-radio-active"></span>
-                                </div>
-                            </label>
-                            <label for="top-right" class="inline-block-label">
-                                <input id="top-right" type="radio" class="field-input" value="bottom-right" name="alignment"  <%=alignement==="bottom-right"? ' checked="checked" ':'' %>/>
-                                <div class="inline-block-label__top">
-                                    <span class="icon-align-bottom_right"></span>
-                                </div>
-                                <div class="inline-block-label__bottom">
-                                    <span class="icon-radio-inactive"></span>
-                                    <span class="icon-radio-active"></span>
-                                </div>
-                            </label>
-                            <label for="bottom-left" class="inline-block-label">
-                                <input id="bottom-left" type="radio" class="field-input" value="top-left" name="alignment" <%=alignement==="top-left"? ' checked="checked" ':'' %>/>
-                                <div class="inline-block-label__top">
-                                    <span class="icon-align-top_left"></span>
-                                </div>
-                                <div class="inline-block-label__bottom">
-                                    <span class="icon-radio-inactive"></span>
-                                    <span class="icon-radio-active"></span>
-                                </div>
-                            </label>
+            </header>
+            <div class="category-content radio-transformed " style="">
+                <div>
+                    <span class="effect-radio color-choice normal <%=(color==='surface')?'active':''%>" id="surface" data-value="surface" data-helper="">
+                    <span class="helper">
+                    <span class="help">Normal</span>
+                    <span class="bottom"></span>
+                    </span>
+                    <span class="normal">
+                    <span class="icon-form-long_text">
+                    </span>
+                    <span class="switch-container">
+                    <span class="radio"><span>
+                    </span>
+                    </span>
+                    </span>
+                </div>
+                <div>
+                    <span class="effect-radio color-choice accent <%=(color==='accent')?'active':''%>" id="accent" data-value="accent" data-helper="">
+                    <span class="helper">
+                    <span class="help">Accent</span>
+                    <span class="bottom"></span>
+                    </span>
+                    <span class="accent">
+                    <span class="icon-form-long_text">
+                    </span>
+                    <span class="switch-container">
+                    <span class="radio"><span>
+                    </span>
+                    </span>
+                    </span>
+                </div>
+                <div>
+                    <span class="effect-radio color-choice infos <%=(color==='notice')?'active':''%>" id="notice" data-value="notice" data-helper="">
+                    <span class="helper">
+                    <span class="help">Infos</span>
+                    <span class="bottom"></span>
+                    </span>
+                    <span class="infos">
+                    <span class="icon-form-long_text">
+                    </span>
+                    <span class="switch-container">
+                    <span class="radio"><span>
+                    </span>
+                    </span>
+                    </span>
+                </div>
 
-                            <label for="top-left" class="inline-block-label">
-                                <input id="top-left" type="radio" class="field-input" value="bottom-left" name="alignment" <%=alignement==="bottom-left"? ' checked="checked" ':'' %> />
-                                <div class="inline-block-label__top">
-                                    <span class="icon-align-bottom_left"></span>
-                                </div>
-                                <div class="inline-block-label__bottom">
-                                    <span class="icon-radio-inactive"></span>
-                                    <span class="icon-radio-active"></span>
-                                </div>
-                            </label>
-                        </div>
+
+
+            </div>
+        </div>
+
+
+        <!--  style pour toast  -->
+        <div class="alignment-container">
+            <header>
+                <h3 class="option-name">
+                    <span class="icon-button-size icon-midsize"></span>
+                    <%=__("alignmentPopup")%>
+                </h3>
+                <span class="panel-content-legend">
+                    <%=__("alignmentPopupLegend")%>
+                </span>
+            </header>
+            <div class="option-content">
+                <div class="controlPanel-selector">
+                    <div class="option radio">
+                            <div class="button-size-radio">
+                                <label for="bottom-right" class="inline-block-label">
+                                    <input id="bottom-right" type="radio" class="field-input" value="top-right" name="alignment" <%=alignement==="top-right"? ' checked="checked" ':'' %> />
+                                    
+                                    <div class="inline-block-label__top">
+                                        <span class="icon-align-top_right"></span>
+                                    </div>
+                                    <div class="inline-block-label__bottom">
+                                        <span class="icon-radio-inactive"></span>
+                                        <span class="icon-radio-active"></span>
+                                    </div>
+                                </label>
+                                <label for="top-right" class="inline-block-label">
+                                    <input id="top-right" type="radio" class="field-input" value="bottom-right" name="alignment"  <%=alignement==="bottom-right"? ' checked="checked" ':'' %>/>
+                                    <div class="inline-block-label__top">
+                                        <span class="icon-align-bottom_right"></span>
+                                    </div>
+                                    <div class="inline-block-label__bottom">
+                                        <span class="icon-radio-inactive"></span>
+                                        <span class="icon-radio-active"></span>
+                                    </div>
+                                </label>
+                                <label for="bottom-left" class="inline-block-label">
+                                    <input id="bottom-left" type="radio" class="field-input" value="top-left" name="alignment" <%=alignement==="top-left"? ' checked="checked" ':'' %>/>
+                                    <div class="inline-block-label__top">
+                                        <span class="icon-align-top_left"></span>
+                                    </div>
+                                    <div class="inline-block-label__bottom">
+                                        <span class="icon-radio-inactive"></span>
+                                        <span class="icon-radio-active"></span>
+                                    </div>
+                                </label>
+
+                                <label for="top-left" class="inline-block-label">
+                                    <input id="top-left" type="radio" class="field-input" value="bottom-left" name="alignment" <%=alignement==="bottom-left"? ' checked="checked" ':'' %> />
+                                    <div class="inline-block-label__top">
+                                        <span class="icon-align-bottom_left"></span>
+                                    </div>
+                                    <div class="inline-block-label__bottom">
+                                        <span class="icon-radio-inactive"></span>
+                                        <span class="icon-radio-active"></span>
+                                    </div>
+                                </label>
+                            </div>
+                    </div>
                 </div>
             </div>
+
         </div>
-
-    </div>
     </article>
 </div>
 
Index: src/js/JEditor/PagePanel/Contents/Options/Views/PopupStyleView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/Views/PopupStyleView.js	(révision 11031)
+++ src/js/JEditor/PagePanel/Contents/Options/Views/PopupStyleView.js	(révision 11032)
@@ -13,6 +13,7 @@
                         events: {
                             "change input[type=radio][name=size]": "sizeChange",
                             'click .display-choice': "displayChange",
+                            'click .color-choice': "colorChange",
                             "change input[type=radio][name=alignment]": "alignmentChange",
                         },
                         /**
@@ -27,14 +28,9 @@
                          */
                         render: function() {
        
-                            var templateVars = {size:this.model.size,display:this.model.display,alignement:this.model.alignment};
+                            var templateVars = {size:this.model.size,display:this.model.display,color:this.model.color,alignement:this.model.alignment};
                             this.$el.html(this.template(templateVars));
-                            if (this.model.display =="modal") {
-                                this.$(".alignment-container").css("display", "none");
-                                this.model.alignement="bottom-right";
-                            }else{
-                                this.$(".alignment-container").css("display", "block");
-                            }
+                            this.toggleColorAlignment(this.model.display);
                             this.scrollables();
                             return this;
 
@@ -55,15 +51,32 @@
                             var $target = $(event.currentTarget);
                             $target.addClass("active");
                             var value = $target.attr("data-value");
-                            if (value =="modal") {
+                            this.toggleColorAlignment(value);
+                            this.model.display=value;
+                        },
+                        /**
+                         * action changer de coleur toast
+                         * @param {*} event 
+                         */
+                        colorChange:function(event){
+                            this.$(".color-choice").removeClass("active");
+                            var $target = $(event.currentTarget);
+                            $target.addClass("active");
+                            var value = $target.attr("data-value");
+                            this.model.color=value;
+                        },  
+
+                        toggleColorAlignment:function (display){
+                            if (display ==="modal") {
                                 this.$(".alignment-container").css("display", "none");
+                                this.$(".color-container").css("display", "none");
                                 this.model.alignement="bottom-right";
+                                this.model.color="surface";
                             }else{
                                 this.$(".alignment-container").css("display", "block");
+                                this.$(".color-container").css("display", "block");
                             }
-
-                             this.model.display=value;
-                        },  
+                        },
             }
     );
     PopupStyleOptionView.translate = translate;
Index: src/js/JEditor/PagePanel/Contents/Options/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/nls/fr-ca/i18n.js	(révision 11031)
+++ src/js/JEditor/PagePanel/Contents/Options/nls/fr-ca/i18n.js	(révision 11032)
@@ -65,6 +65,8 @@
     "popupStyle": "Style",
     "sizePopup": "Taille de la popup",
     "sizePopupLegend": "Sélectionnez la taille de la popup ici",
+    "colorPopup": "Couleurs",
+    "colorPopupLegend": "Appliquer un ensemble de couleurs",
     "displayPopup": "Style",
     "displayPopupLegend": "Sélectionnez le style d'affichage du popup",
     "alignmentPopup": "Alignement",
Index: src/js/JEditor/PagePanel/Contents/Options/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/nls/fr-fr/i18n.js	(révision 11031)
+++ src/js/JEditor/PagePanel/Contents/Options/nls/fr-fr/i18n.js	(révision 11032)
@@ -65,6 +65,8 @@
     "popupStyle": "Style",
     "sizePopup": "Taille de la popup",
     "sizePopupLegend": "Sélectionnez la taille de la popup ici",
+    "colorPopup": "Couleurs",
+    "colorPopupLegend": "Appliquer un ensemble de couleurs",
     "displayPopup": "Style",
     "displayPopupLegend": "Sélectionnez le style d'affichage du popup",
     "alignmentPopup": "Alignement",
Index: src/js/JEditor/PagePanel/Contents/Options/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/nls/i18n.js	(révision 11031)
+++ src/js/JEditor/PagePanel/Contents/Options/nls/i18n.js	(révision 11032)
@@ -67,6 +67,8 @@
     "popupStyle": "Style",
     "sizePopup": "Popup size",
     "sizePopupLegend": "Select popup size here",
+    "colorPopup": "Colors",
+    "colorPopupLegend": "Apply a set of colors",
     "alignmentPopup": "Alignment",
     "alignmentPopupLegend": "Select the alignment of the popup in the window",
     "displayPopup": "Style",
