Revision: r13098
Date: 2024-09-25 10:48:16 +0300 (lrb 25 Sep 2024) 
Author: traj<PERSON><PERSON><PERSON><PERSON> 

## Commit message
Whishlist : change let to var (telechargement en masse)

## Files changed

## Full metadata
------------------------------------------------------------------------
r13098 | trajaonarivelo | 2024-09-25 10:48:16 +0300 (lrb 25 Sep 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileManagerUIView.js

Whishlist : change let to var (telechargement en masse)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js	(révision 13097)
+++ src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js	(révision 13098)
@@ -63,7 +63,7 @@
                 },
                 downloadFilesCollection: function()
                 {
-                    let formData = new FormData();
+                    var formData = new FormData();
                     
                     for (var cid in this.selected) 
                     {
@@ -75,7 +75,7 @@
                         formData.append('liste[]', model.attributes.id );
                         formData.append('nom[]', model.attributes.name );
                         
-                        let liste_fichiers_objet = model.attributes.files.models ;
+                        var liste_fichiers_objet = model.attributes.files.models ;
                         for(var i = 0 ; i<liste_fichiers_objet.length ; i++  )
                         {
                             // les fichiers
Index: src/js/JEditor/FilePanel/Views/FileManagerUIView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileManagerUIView.js	(révision 13097)
+++ src/js/JEditor/FilePanel/Views/FileManagerUIView.js	(révision 13098)
@@ -195,7 +195,7 @@
                 {
                         // recuperer les fichiers 
                         var fichiers = [];
-                        let tempFile;
+                        var tempFile;
                         for (cid in this.selected) {
                             if (this.selected[cid] === true) {
                                 tempFile = this.collection.get(cid) ;
@@ -205,7 +205,7 @@
                         
 
                         // envoie ajax pour creer .zip
-                        let formData = new FormData();
+                        var formData = new FormData();
                         for (var i = 0; i < fichiers.length; i++) 
                         {
                             formData.append('liste[]', fichiers[i]);
