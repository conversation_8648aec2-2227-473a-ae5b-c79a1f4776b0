Revision: r10362
Date: 2023-02-08 16:54:11 +0300 (lrb 08 Feb 2023) 
Author: mpartaux 

## Commit message
add new text style

## Files changed

## Full metadata
------------------------------------------------------------------------
r10362 | mpartaux | 2023-02-08 16:54:11 +0300 (lrb 08 Feb 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js

add new text style
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 10361)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 10362)
@@ -48,7 +48,8 @@
                                 { name: 'Highlight 2', element: 'span', attributes: { 'class': 'txt-hightlight-2' } },
                                 { name: 'Highlight 3', element: 'span', attributes: { 'class': 'txt-hightlight-3' } },
                                 { name: 'Highlight 4', element: 'span', attributes: { 'class': 'txt-hightlight-4' } },
-                                { name: 'Underline 1', element: 'span', attributes: { 'class': 'txt-underline-1' } }
+                                { name: 'Underline 1', element: 'span', attributes: { 'class': 'txt-underline-1' } },
+                                { name: 'Color 1', element: 'span', attributes: { 'class': 'txt-color-1' } }
                             ],
                             forcePasteAsPlainText: true,
                             pasteFromWordRemoveFontStyles: true,
