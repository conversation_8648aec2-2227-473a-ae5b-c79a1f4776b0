Revision: r11536
Date: 2023-11-08 14:51:14 +0300 (lrb 08 Nov 2023) 
Author: rrakotoarinelina 

## Commit message
Sélection d'une icon pour les boutons (partie js) - correction erreur grunt

## Files changed

## Full metadata
------------------------------------------------------------------------
r11536 | rrakotoarinelina | 2023-11-08 14:51:14 +0300 (lrb 08 Nov 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/SelectIconView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Models/Params.js

Sélection d'une icon pour les boutons (partie js) - correction erreur grunt
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/SelectIconView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/SelectIconView.js	(révision 11535)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/SelectIconView.js	(révision 11536)
@@ -79,7 +79,10 @@
             this.$el.dialog('close');
         },
         onOk: function() {
-            const svgObj = this.svgCollection.find(item => item.name === this.selected);
+            var selectedIcon = this.selected;
+            var svgObj = this.svgCollection.filter(function(item) {
+                return item.name === selectedIcon;
+             })[0];
             this.trigger(Events.ButtonEvents.SELECT_ICON, svgObj );
             this.$el.dialog('close');
         },
@@ -87,8 +90,8 @@
             $('.box-icon').css("background-color", "transparent");
             var $target = $(e.currentTarget);
             $target.css('background-color', '#41ffbe');
-            var id = $target.data('name');
-            this.selected = id;
+            var name = $target.data('name');
+            this.selected = name;
         }
     });
     return SelectIconView;
Index: src/js/JEditor/ParamsPanel/Models/Params.js
===================================================================
--- src/js/JEditor/ParamsPanel/Models/Params.js	(révision 11535)
+++ src/js/JEditor/ParamsPanel/Models/Params.js	(révision 11536)
@@ -130,9 +130,9 @@
             //google fonts verification
             if(attributes.FontsGoogle)
             {
-                let countFontGoogleApi = 0;
-                let countFontGstatic = 0;
-                let countFontGoogleApiSwap = 0;
+                var countFontGoogleApi = 0;
+                var countFontGstatic = 0;
+                var countFontGoogleApiSwap = 0;
                 //case if we want the attributes to be switchable
                 //const fontRule1 = RegExp('<link (?:rel=["\']preconnect["\'].*href=["\']https://fonts.googleapis.com["\']|href=["\']https://fonts.googleapis.com["\'].*rel=["\']preconnect["\'])>', 'g');
                 const fontRule1 = RegExp('<link (rel=["\']preconnect["\'] href=["\']https:\/\/fonts.googleapis.com["\']|href=["\']https:\/\/fonts.googleapis.com["\'] rel=["\']preconnect["\'])>', 'g');
