Revision: r13202
Date: 2024-10-15 14:02:23 +0300 (tlt 15 Okt 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: ajouter l'upload de gif dans marqueclient

## Files changed

## Full metadata
------------------------------------------------------------------------
r13202 | srazanandralisoa | 2024-10-15 14:02:23 +0300 (tlt 15 Okt 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/MarqueClientView.js

wishlist IDEO3.2: ajouter l'upload de gif dans marqueclient
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Views/MarqueClientView.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 13201)
+++ src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 13202)
@@ -179,7 +179,7 @@
             const uploadParams = {
                 customStockEvent: '_parsestock_image',
                 acceptedTypes: ['image'],
-                acceptedExtensions: ['jpeg', 'jpg', 'png', 'svg'],
+                acceptedExtensions: ['jpeg', 'jpg', 'gif', 'png', 'svg'],
                 refusedExtensions: ['bmp'],
                 uploadUrl: __IDEO_UPLOADLOGO_URL__ ? __IDEO_UPLOADLOGO_URL__ : '/admin/resource-logo',
             };
