Revision: r13091
Date: 2024-09-23 10:34:17 +0300 (lts 23 Sep 2024) 
Author: traj<PERSON><PERSON><PERSON><PERSON> 

## Commit message
Wishlist : boutton option telecharger fichier

## Files changed

## Full metadata
------------------------------------------------------------------------
r13091 | traj<PERSON><PERSON><PERSON><PERSON> | 2024-09-23 10:34:17 +0300 (lts 23 Sep 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/dev/#librairies/module/GestionContenuUtil/src/GestionContenuUtil/Core/BlockModuleHandlers/BlockBoutonHandler.php
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Links/Models/Link.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Links/Templates/LinkSelector.html
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Links/Views/LinkView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Links/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Links/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Links/nls/i18n.js

Wishlist : boutton option telecharger fichier
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Links/Models/Link.js
===================================================================
--- src/js/JEditor/Commons/Links/Models/Link.js	(révision 13090)
+++ src/js/JEditor/Commons/Links/Models/Link.js	(révision 13091)
@@ -47,7 +47,8 @@
         target: '_self',
         name: null,
         href: '#',
-        anchor:""
+        anchor:"",
+        downloadOrNewTab: true
       },
       constructor: function (attrs, options) {
         if (options && options.context)
@@ -195,7 +196,7 @@
         return ret;
       }
     });
-  Link.SetAttributes(['type', 'href', 'target', 'showTitle', 'showDesc', 'lang', 'name', "anchor"])
+  Link.SetAttributes(['type', 'href', 'target', 'showTitle', 'showDesc', 'lang', 'name', "anchor" ,"downloadOrNewTab"])
 
   return Link;
 });
Index: src/js/JEditor/Commons/Links/Templates/LinkSelector.html
===================================================================
--- src/js/JEditor/Commons/Links/Templates/LinkSelector.html	(révision 13090)
+++ src/js/JEditor/Commons/Links/Templates/LinkSelector.html	(révision 13091)
@@ -56,6 +56,16 @@
     </div>
     <div class="action-options radio-group link-options wrapper-uploader">
     </div>
+    <div class="action-options radio-group link-options">
+        <div class="option radio">
+            <% var _id=_.uniqueId('downloadOrNewTab'); %>
+            <input type="radio" name="downloadOrNewTab" value="true"  id="<%=_id %>" <%=model.downloadOrNewTab=== true ?' checked="checked" ':'' %> /><label for="<%=_id%>"><span class="radio"><span class="icon-radio-active"></span><span class="icon-radio-inactive"></span></span><%= __("downloadTheFile") %></label>
+        </div>
+        <div class="option radio">
+            <% var _id=_.uniqueId('downloadOrNewTab'); %>
+            <input type="radio" name="downloadOrNewTab" value="false" id="<%=_id %>" <%=model.downloadOrNewTab=== false ?' checked="checked" ':'' %> /><label for="<%=_id%>"><span class="radio"><span class="icon-radio-active"></span><span class="icon-radio-inactive"></span></span><%= __("openInNewWindow") %></label>
+        </div>
+    </div>
 </div>
 <div class="panel-radio <%=model.type===TYPES.NONE?'selected':''%>" data-value="<%=TYPES.NONE %>" >
     <div class="panel-radio-title">
Index: src/js/JEditor/Commons/Links/Views/LinkView.js
===================================================================
--- src/js/JEditor/Commons/Links/Views/LinkView.js	(révision 13090)
+++ src/js/JEditor/Commons/Links/Views/LinkView.js	(révision 13091)
@@ -18,6 +18,7 @@
       'radiopanelchange': '_onRadioChange',
       'change [data-action="openInLightBox"]': '_toggleLightBox',
       'change input[type="radio"][data-target]': '_onTargetSelect',
+      'change input[name="downloadOrNewTab"]': '_onDownloadOrNewTabSelect',
       'keyup input[type="text"][name="href"]': '_onHrefChange',
       'change input[data-litebox]': '_onLightBoxOptionChange',
     },
@@ -60,6 +61,10 @@
       this.model.target = value;
       //this.model.save();
     },
+    _onDownloadOrNewTabSelect: function (event) {
+      var value = $(event.currentTarget).val();     
+      this.model.downloadOrNewTab = JSON.parse(value);
+    },
     _toggleLightBox: function (e) {
       var $target = $(e.currentTarget);
       if ($target.is(':checked'))
Index: src/js/JEditor/Commons/Links/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/Commons/Links/nls/fr-ca/i18n.js	(révision 13090)
+++ src/js/JEditor/Commons/Links/nls/fr-ca/i18n.js	(révision 13091)
@@ -1 +1 @@
-define({"imageZoom":"Zoomer sur l'image","imageZoomEnabled":"Le zoom sur l'image est activé","imageZoomTitle":"Afficher le titre de l’image sur le zoom","imageZoomDesc":"Afficher la description de l’image sur le zoom","openLink":"Ouvrir un lien","targetSelf":"Ouvrir le lien dans la même fenêtre","targetBlank":"Ouvrir le lien dans une autre fenêtre","relLitebox":"Ouvrir le lien dans une boîte de dialogue","sendToPage":"Envoyer vers une autre page du site","selectPage":"Sélectionnez une page","downloadFile":"Télécharger un fichier","doNothing":"Ne rien faire"});
\ No newline at end of file
+define({"imageZoom":"Zoomer sur l'image","imageZoomEnabled":"Le zoom sur l'image est activé","imageZoomTitle":"Afficher le titre de l’image sur le zoom","imageZoomDesc":"Afficher la description de l’image sur le zoom","openLink":"Ouvrir un lien","targetSelf":"Ouvrir le lien dans la même fenêtre","targetBlank":"Ouvrir le lien dans une autre fenêtre","relLitebox":"Ouvrir le lien dans une boîte de dialogue","sendToPage":"Envoyer vers une autre page du site","selectPage":"Sélectionnez une page","downloadFile":"Télécharger un fichier","downloadTheFile":"Téléchargement du fichier","openInNewWindow":"Ouvrir le fichier dans une autre fenêtre","doNothing":"Ne rien faire"});
\ No newline at end of file
Index: src/js/JEditor/Commons/Links/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/Commons/Links/nls/fr-fr/i18n.js	(révision 13090)
+++ src/js/JEditor/Commons/Links/nls/fr-fr/i18n.js	(révision 13091)
@@ -1 +1 @@
-define({"imageZoom":"Zoomer sur l'image","imageZoomEnabled":"Le zoom sur l'image est activé","imageZoomTitle":"Afficher le titre de l’image sur le zoom","imageZoomDesc":"Afficher la description de l’image sur le zoom","openLink":"Ouvrir un lien","targetSelf":"Ouvrir le lien dans la même fenêtre","targetBlank":"Ouvrir le lien dans une autre fenêtre","relLitebox":"Ouvrir le lien dans une boîte de dialogue","sendToPage":"Envoyer vers une autre page du site","selectPage":"Sélectionnez une page","downloadFile":"Télécharger un fichier","doNothing":"Ne rien faire"});
\ No newline at end of file
+define({"imageZoom":"Zoomer sur l'image","imageZoomEnabled":"Le zoom sur l'image est activé","imageZoomTitle":"Afficher le titre de l’image sur le zoom","imageZoomDesc":"Afficher la description de l’image sur le zoom","openLink":"Ouvrir un lien","targetSelf":"Ouvrir le lien dans la même fenêtre","targetBlank":"Ouvrir le lien dans une autre fenêtre","relLitebox":"Ouvrir le lien dans une boîte de dialogue","sendToPage":"Envoyer vers une autre page du site","selectPage":"Sélectionnez une page","downloadFile":"Télécharger un fichier","downloadTheFile":"Téléchargement du fichier","openInNewWindow":"Ouvrir le fichier dans une autre fenêtre","doNothing":"Ne rien faire"});
\ No newline at end of file
Index: src/js/JEditor/Commons/Links/nls/i18n.js
===================================================================
--- src/js/JEditor/Commons/Links/nls/i18n.js	(révision 13090)
+++ src/js/JEditor/Commons/Links/nls/i18n.js	(révision 13091)
@@ -1 +1 @@
-define({ "root": {"imageZoom":"Zoom on","imageZoomEnabled":"Zoom enabled","imageZoomTitle":"Show picture title on zoom","imageZoomDesc":"Show picture description on zoom","openLink":"Open a link","targetSelf":"Open the link in the same window","targetBlank":"Open the link in antoher window","relLitebox":"Open the link in another dialog box","sendToPage":"Send to antoher page of the site","selectPage":"Select a page","downloadFile":"Download a file","doNothing":"Do nothing"}, "fr-fr":true, "fr-ca":true })
\ No newline at end of file
+define({ "root": {"imageZoom":"Zoom on","imageZoomEnabled":"Zoom enabled","imageZoomTitle":"Show picture title on zoom","imageZoomDesc":"Show picture description on zoom","openLink":"Open a link","targetSelf":"Open the link in the same window","targetBlank":"Open the link in antoher window","relLitebox":"Open the link in another dialog box","sendToPage":"Send to antoher page of the site","selectPage":"Select a page","downloadFile":"Download a file","downloadTheFile":"Download the file","openInNewWindow":"Open the file in another window","doNothing":"Do nothing"}, "fr-fr":true, "fr-ca":true })
\ No newline at end of file
