Revision: r10128
Date: 2023-01-10 20:12:40 +0300 (tlt 10 Jan 2023) 
Author: anthony 

## Commit message
nouvelles fonctionnalités ckeditor (avec code source pour superadmin)

## Files changed

## Full metadata
------------------------------------------------------------------------
r10128 | anthony | 2023-01-10 20:12:40 +0300 (tlt 10 Jan 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/assets/ACLs/admin.json
   M /branches/ideo3_v2/integration/assets/ACLs/lpadmin.json
   M /branches/ideo3_v2/integration/assets/ACLs/root.json
   M /branches/ideo3_v2/integration/assets/ACLs/superadmin.json
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/de-de/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/en-au/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/en-us/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/es-es/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/i18n.js

nouvelles fonctionnalités ckeditor (avec code source pour superadmin)
------------------------------------------------------------------------

## Diff
Index: assets/ACLs/admin.json
===================================================================
--- assets/ACLs/admin.json	(révision 10127)
+++ assets/ACLs/admin.json	(révision 10128)
@@ -175,6 +175,10 @@
     "value": false,
     "comparison": null
  },
+ "edit_source": {
+    "value": false,
+    "comparison": null
+ },
  "homeless_msg":{
     "value": false,
     "comparison": null
Index: assets/ACLs/lpadmin.json
===================================================================
--- assets/ACLs/lpadmin.json	(révision 10127)
+++ assets/ACLs/lpadmin.json	(révision 10128)
@@ -175,6 +175,10 @@
        "value": false,
        "comparison": null
     },
+    "edit_source": {
+        "value": false,
+        "comparison": null
+     },
     "homeless_msg":{
        "value": false,
        "comparison": null
Index: assets/ACLs/root.json
===================================================================
--- assets/ACLs/root.json	(révision 10127)
+++ assets/ACLs/root.json	(révision 10128)
@@ -175,6 +175,10 @@
         "value": true,
         "comparison": null
     },
+    "edit_source": {
+        "value": true,
+        "comparison": null
+     },
     "homeless_msg":{
         "value": true,
         "comparison": null
Index: assets/ACLs/superadmin.json
===================================================================
--- assets/ACLs/superadmin.json	(révision 10127)
+++ assets/ACLs/superadmin.json	(révision 10128)
@@ -175,6 +175,10 @@
     "value": false,
     "comparison": null
  },
+ "edit_source": {
+    "value": true,
+    "comparison": null
+ },
  "homeless_msg":{
     "value": true,
     "comparison": null
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/de-de/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/de-de/i18n.js	(révision 10127)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/de-de/i18n.js	(révision 10128)
@@ -1,8 +1 @@
-define({
-	"videoDisplayStyle_intro": "Sélectionnez le mode d'affichage des contrôleurs de votre lecteur vidéo",
-	"playerIntro": "Couleurs du lecteur vidéo",
-	"playerIntroDetails": "Personnalisez les couleurs de votre lecteur vidéo",
-	"playerDark": "foncé",
-	"playerLight": "clair",
-	"editingZone": "Vous éditez la zone :"
-});
\ No newline at end of file
+define({});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/en-au/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/en-au/i18n.js	(révision 10127)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/en-au/i18n.js	(révision 10128)
@@ -1,8 +1 @@
-define({
-	"videoDisplayStyle_intro": "Select the display mode for controllers in your video player",
-	"playerIntro": "Colors of the video player",
-	"playerIntroDetails": "Customize the colors of your video player",
-	"playerDark": "dark",
-	"playerLight": "clear",
-	"editingZone": "Editing area:"
-});
\ No newline at end of file
+define({});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/en-us/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/en-us/i18n.js	(révision 10127)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/en-us/i18n.js	(révision 10128)
@@ -1,8 +1 @@
-define({
-	"videoDisplayStyle_intro": "Sélectionnez le mode d'affichage des contrôleurs de votre lecteur vidéo",
-	"playerIntro": "Couleurs du lecteur vidéo",
-	"playerIntroDetails": "Personnalisez les couleurs de votre lecteur vidéo",
-	"playerDark": "foncé",
-	"playerLight": "clair",
-	"editingZone": "Vous éditez la zone :"
-});
\ No newline at end of file
+define({});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/es-es/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/es-es/i18n.js	(révision 10127)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/es-es/i18n.js	(révision 10128)
@@ -1,8 +1 @@
-define({
-	"videoDisplayStyle_intro": "Sélectionnez le mode d'affichage des contrôleurs de votre lecteur vidéo",
-	"playerIntro": "Couleurs du lecteur vidéo",
-	"playerIntroDetails": "Personnalisez les couleurs de votre lecteur vidéo",
-	"playerDark": "foncé",
-	"playerLight": "clair",
-	"editingZone": "Vous éditez la zone :"
-});
\ No newline at end of file
+define({});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/fr-ca/i18n.js	(révision 10127)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/fr-ca/i18n.js	(révision 10128)
@@ -1 +1 @@
-define({"BLOCK_NAME":"Texte","block":"Bloc","move":"Déplacer","edit":"Éditer","delete":"Supprimer","textBlockOption":"Options du bloc de texte"});
\ No newline at end of file
+define({"BLOCK_NAME":"Texte","block":"Bloc","move":"Déplacer","edit":"Éditer","delete":"Supprimer","textBlockOption":"Options du bloc de texte","defaultAccordionTitle":"Un titre"});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/fr-fr/i18n.js	(révision 10127)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/fr-fr/i18n.js	(révision 10128)
@@ -1 +1 @@
-define({"BLOCK_NAME":"Texte","block":"Bloc","move":"Déplacer","edit":"Éditer","delete":"Supprimer","textBlockOption":"Options du bloc de texte"});
\ No newline at end of file
+define({"BLOCK_NAME":"Texte","block":"Bloc","move":"Déplacer","edit":"Éditer","delete":"Supprimer","textBlockOption":"Options du bloc de texte","defaultAccordionTitle":"Un titre"});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/i18n.js	(révision 10127)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/i18n.js	(révision 10128)
@@ -1 +1 @@
-define({ "root": {"BLOCK_NAME":"Text","block":"Block","move":"Move","edit":"Edit","delete":"Delete","textBlockOption":"Text block options"}, "fr-fr":true, "fr-ca":true })
\ No newline at end of file
+define({ "root": {"BLOCK_NAME":"Text","block":"Block","move":"Move","edit":"Edit","delete":"Delete","textBlockOption":"Text block options","defaultAccordionTitle":"A title"}, "fr-fr":true, "fr-ca":true })
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 10127)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 10128)
@@ -25,24 +25,36 @@
                         tagname: "div",
                         attributes: {"class": "block", tabindex: 1},
                         events: _.extend({}, BlockView.prototype.events, {'click .action.delete': "confirmDelete", 'keydown .content': '_onKeyPress', 'dblclick .content': 'avoidPropagation'}),
+                        defaultToolbar: [
+                            {name: 'remove_formats', groups: ['format'], items: ["RemoveFormat"]},
+                            {name: 'styles', groups: ['styles'], items: ["Styles"] },
+                            {name: 'kikou', groups: ['basicstyles','cleanup'], items: ['Bold', 'Italic', 'Underline','Strike']},
+                            {name: 'links', groups: ['links'], items:['ideoLink', 'ideoUnlink', 'Anchor']},
+                            {name: 'paragraph', groups: ['align', 'bidi'], items: ["JustifyLeft", "JustifyCenter", "JustifyRight"]},
+                            {name: 'list', groups: ['list'], items: ["NumberedList","BulletedList","-","Blockquote","Abbr","pre"]},
+                            {name: 'formats', groups: ['format'], items: ["Format", "FontSize"]},
+                            {name: '_ideostyle', groups: ['ideostyle'], items: ['blockstyles']}
+                        ],
                         CKconfig: {
                             dialog_backgroundCoverColor: "#ffffff",
-                            toolbar: [
-                                {name: 'remove_formats', groups: ['format'], items: ["RemoveFormat"]},
-                                {name: 'kikou', groups: ['basicstyles','cleanup'], items: ['Bold', 'Italic', 'Underline','Strike']},
-                                {name:'links',items:['ideoLink', 'ideoUnlink']},
-                                {name: 'paragraph', groups: ['align', 'bidi'], items: ["JustifyLeft", "JustifyCenter", "JustifyRight"]},
-                                {name: 'list', groups: ['list'], items: ["BulletedList","-","Blockquote"]},
-                                {name: 'formats', groups: ['format'], items: ["Format", "FontSize"]},
-                                {name: '_ideostyle', groups: ['ideostyle'], items: ['blockstyles']}
+                            extraPlugins: 'abbr,pre,ideolink,ideostyler,sourcedialog',
+                            skin: 'ideo',
+                            fontSize_sizes: 'tiny/0.75em;small/0.875em;large/1.25em;xlarge/1.5em;xxlarge/2em;xxxlarge/2.5em',
+                            stylesSet: [
+                                // Block-level styles
+                                { name: 'Blue Title', element: 'h2', styles: { 'color': 'Blue' } },
+                                { name: 'Red Title' , element: 'h3', styles: { 'color': 'Red' } },
+                            
+                                // Inline styles
+                                { name: 'CSS Style', element: 'span', attributes: { 'class': 'my_style' } },
+                                { name: 'Marker: Yellow', element: 'span', styles: { 'background-color': 'Yellow' } }
                             ],
-                            fontSize_sizes: 'tiny/0.75em;small/0.875em;large/1.25em;xlarge/1.5em;xxlarge/2em;xxxlarge/2.5em',
-                            extraPlugins: 'ideolink,ideostyler,blockquote,removeformat',
-                            skin: 'ideo',
                             forcePasteAsPlainText: true,
                             pasteFromWordRemoveFontStyles: true,
                             language:window.userLocale.split('-')[0],
-                            format_tags: 'p;h2;h3;h4;h5;h6;pre;address;div',
+                            preDefaultTitle: translate("defaultAccordionTitle"),
+                            allowedContent: true,
+                            format_tags: 'p;h2;h3;h4;h5;h6',
                             entities:false,
                             title:false
                         },
@@ -58,6 +70,14 @@
                             this._template = this.buildTemplate(block, translate);
                             this.class = "TextBlockView";
                             this.listenTo(this.model, Events.BackboneEvents.CHANGE + ':content', this.render);
+                            //edition code source uniquement pour superadmin et root
+                            if(this.app.user.can('edit_source')) {
+                                var rootToolbar = this.defaultToolbar.slice();
+                                rootToolbar.push({name: 'source', groups: ['source'], items: ["Sourcedialog"]});
+                                this.CKconfig.toolbar = rootToolbar;
+                            } else {
+                                this.CKconfig.toolbar = this.defaultToolbar;
+                            }
                         },
                         saveText: function(dependency) {
                             if (this.constructor.editor)
