Revision: r11803
Date: 2024-01-24 10:06:52 +0300 (lrb 24 Jan 2024) 
Author: sraz<PERSON><PERSON><PERSON><PERSON> 

## Commit message
correction: panel fichier modification (erreur ajout image dans un collection avec le nom vide) 

## Files changed

## Full metadata
------------------------------------------------------------------------
r11803 | srazanandralisoa | 2024-01-24 10:06:52 +0300 (lrb 24 Jan 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js

correction: panel fichier modification (erreur ajout image dans un collection avec le nom vide) 
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js	(révision 11802)
+++ src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js	(révision 11803)
@@ -165,7 +165,7 @@
                     if (!this.parentCollection.name) {
                         var date = new Date();
                         var dateString = date;
-                        this.parentCollection.name = this.translate("newGallery") + Utils.dateFormat(this.translate('dateFormat'));
+                        this.parentCollection.name = translate("newGallery") + Utils.dateFormat(translate('dateFormat'));
                     }
                     this.parentCollection.save();
                     this.trigger(Events.ListViewEvents.UPDATE_COLLECTION, this.parentCollection);
