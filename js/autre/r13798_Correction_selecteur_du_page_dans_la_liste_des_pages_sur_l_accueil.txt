Revision: r13798
Date: 2025-02-06 11:43:05 +0300 (lkm 06 Feb 2025) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
Correction selecteur du page  dans la liste des pages sur l'accueil 

## Files changed

## Full metadata
------------------------------------------------------------------------
r13798 | srazanandralisoa | 2025-02-06 11:43:05 +0300 (lkm 06 Feb 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/PagePanel.js

Correction selecteur du page  dans la liste des pages sur l'accueil 
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/PagePanel.js
===================================================================
--- src/js/JEditor/PagePanel/PagePanel.js	(révision 13797)
+++ src/js/JEditor/PagePanel/PagePanel.js	(révision 13798)
@@ -49,10 +49,10 @@
                         'click #show-zone-version': 'showZoneVersionsPanel',
 			'click .addPage,.empty-content-lang':'onAddPageClick',
 			'input #form-search-page input': 'searchPage',
-			'click .icon-bin': '_onRemoveClick',
-			'click .icon-edit': '_onEditClick',
-			'click  .switch': '_onSwitchClick',
-			'click th[data-column]': 'onSort'
+			'click #all-data-table td .icon-bin': '_onRemoveClick',
+			'click #all-data-table td .icon-edit': '_onEditClick',
+			'click #all-data-table td .switch': '_onSwitchClick',
+			'click #all-data-table th[data-column]': 'onSort'
 		},
 		cssFile: __IDEO_INTEGRATION_PATH__ + "/build/css/page_panel.css",
 
