Revision: r11560
Date: 2023-11-10 10:04:53 +0300 (zom 10 Nov 2023) 
Author: rrakotoarinelina 

## Commit message
Sélection d'une icon pour les boutons (partie js) - correction retour

## Files changed

## Full metadata
------------------------------------------------------------------------
r11560 | rrakotoarinelina | 2023-11-10 10:04:53 +0300 (zom 10 Nov 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonStyleOption.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/SvgCollection.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonBlock.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonStyleOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/selectIcon.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/SelectIconView.js
   M /branches/ideo3_v2/integration/src/less/imports/button_block/button_block.less
   M /branches/ideo3_v2/integration/src/less/imports/button_block/styleOptions.less
   M /branches/ideo3_v2/integration/src/less/main.less

Sélection d'une icon pour les boutons (partie js) - correction retour
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonStyleOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonStyleOption.js	(révision 11559)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonStyleOption.js	(révision 11560)
@@ -22,7 +22,7 @@
              * @lends ImageOptionss
              */
                     {
-                        defaults: {optionType: 'ButtonStyleOption', priority: 80, size:'medium', buttonAlignment:'', textAlignment:'text-center', color:'pastel'},
+                        defaults: {optionType: 'ButtonStyleOption', priority: 80,icon:'', size:'medium', buttonAlignment:'', textAlignment:'text-center', color:'pastel'},
                         initialize: function() {
                             this._super();
                         },
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/SvgCollection.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/SvgCollection.js	(révision 11559)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/SvgCollection.js	(révision 11560)
@@ -1,11 +1,13 @@
 define([
 	"JEditor/Commons/Events",
 	"JEditor/Commons/Ancestors/Models/Collection",
-    "JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/Svg"
+    "JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/Svg",
+    "JEditor/ParamsPanel/Models/Params",
 ],function(
     Events,
 	Collection,
-	Svg
+	Svg,
+    Params
 ){
 var /**
  * 
@@ -20,8 +22,11 @@
                 {
                     model: Svg,
                     constructor: function(options) {
-                        this.name = options.name || 'outline';
-                        this.svgName = options.svgName || '';
+                        if(options && options.svgName !== ''){
+                            this.svgName = options.svgName;
+                        }else{
+                            this.svgName = '';
+                        }
                     },
                     /**
                      * initialize l'objet
@@ -33,8 +38,29 @@
                     },
                     parse: function(response) {
                         return response.data; // assuming the array of files is in the 'data' property of the response
-                    }
+                    },
 
+                    fetchIcons: function(callback){
+                        var self = this;
+                        this.params = new Params();
+                        this.params.fetch().done((function(data) {
+                            self.name = data.IconsCollection || 'outline';
+                            self.fetch({
+                                success: function(collection, response, options) {
+                                    callback(null, response);
+                                },
+                                error: function(collection, response, options) {
+                                  callback(new Error('Failed to fetch SVG'));
+                                }
+                              });
+            
+                        })).fail(function(error) {
+                            console.log(error);
+                        });
+            
+                    },
+
+
                 });
 return SvgCollection;
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonBlock.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonBlock.html	(révision 11559)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonBlock.html	(révision 11560)
@@ -1,5 +1,8 @@
-<div class="block-button <%=size%> <%=buttonAlignment%> <%=textAlignment%> <%=color%>">
+<div class="block-button blk-button <%=size%> <%=buttonAlignment%> <%=textAlignment%> <%=color%>">
   <a href="#" class="button">
-    <span class="txt"><span><%=text%></span></span>
+    <span class="ico blk-button__icon  aria-hidden="true">
+      <%=icon%>
+    </span>
+    <span class="txt blk-button__label"><span><%=text%></span></span>
   </a>
 </div>
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonStyleOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonStyleOption.html	(révision 11559)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonStyleOption.html	(révision 11560)
@@ -1,17 +1,3 @@
- <style>
-    /* button icon  */
-    /* .selected-icon {
-  display: flex;
-  justify-content: center;
-  align-items: center;
-  font-size: 50px;
-}
-
-.selected-icon svg {
-  width: 100px;
-  height: 100px;
-} */
-</style> 
  <div class="panel-option-container animated button-icon">
     <article class="panel-option">
         <header>
@@ -25,7 +11,7 @@
         </header>
         <div class="option-content">
             <% var _id=_.uniqueId('iconButton') %>
-                <input type="checkbox" class="blue-bg show-browse-icon-button" name="show-browse-icon-button" id="<%=_id %>" <%= icon !== null && icon !== "" ? 'checked="checked"' : '' %> >
+                <input type="checkbox" class="blue-bg show-browse-icon-button" name="show-browse-icon-button" id="<%=_id %>" <%= icon !== null && icon !== "" && icon !== undefined ? 'checked="checked"' : '' %> >
                 <label for="<%=_id %>">
                     <span class="checkbox-wrapper">
                         <span class="icon-unchecked"></span>
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/selectIcon.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/selectIcon.html	(révision 11559)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/selectIcon.html	(révision 11560)
@@ -1,10 +1,10 @@
 
-    <div class="option-content">
+    <div class="container-icon option-content">
         <div class="grid-container-icon">
             <%for(i=0;i<content.length;i++){
                 svg = content[i];
                 %>
-                <div  class="box-icon <%=selected == svg.name ? 'selected-icon':''%>" data-name="<%= svg.name%>"  >
+                <div  class="box-icon <%=selected === svg.name ? 'selected-icon':''%>" data-name="<%= svg.name%>"  >
                         <span class="wrapper-icon">
                             <%= svg.content%>
                         </span>
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js	(révision 11559)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js	(révision 11560)
@@ -1,8 +1,9 @@
 define([
     "JEditor/PagePanel/Contents/Blocks/Block/Views/BlockView",
     "text!../Templates/buttonBlock.html",
-    "i18n!../nls/i18n"
-], function(BlockView, template, translate) {
+    "i18n!../nls/i18n",
+    "JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/SvgCollection",
+], function(BlockView, template, translate,SvgCollection) {
     /**
      * Vue des blocs d'images
      * @class ImageBlockView
@@ -24,6 +25,9 @@
                             this.render();
                         },
                         render: function() {
+                            var svgName = this.model.options.ButtonStyleOption.icon;
+                            var iconContent ="";
+
                             this._super();
                             var textButton = this.model.options.ButtonOption.text?this.model.options.ButtonOption.text:translate('button');
                             var sizeButton = this.model.options.ButtonStyleOption.size;
@@ -30,10 +34,44 @@
                             var alignButton = this.model.options.ButtonStyleOption.buttonAlignment;
                             var alignText = this.model.options.ButtonStyleOption.textAlignment;
                             var color = this.model.options.ButtonStyleOption.color;
-                            this.$('.content').append(this._contentTemplate({size: sizeButton, text: textButton, buttonAlignment:alignButton, textAlignment:alignText, color :color}));
-                            return this;
+
+                            this.$(".blk-button__label").css('margin-top','');
+                            this.$(".blk-button__icon").empty();
+                            if(svgName !== ""  ){
+                                var svgCollectionObj = new SvgCollection({"svgName":svgName});
+                                svgCollectionObj.fetchIcons(_.bind(function(error, svg) {
+                                    if (error) {
+                                        console.error(error);
+                                    } else {
+                                        iconContent = svg.content;
+                                        this.$(".blk-button__label").css('margin-top','-15px');
+                                        this.$('.content').append(this._contentTemplate({icon: iconContent,size: sizeButton, text: textButton, buttonAlignment:alignButton, textAlignment:alignText, color :color}));
+                                         return this;
+                                    }
+                                },this));
+                            }else{
+                                this.$('.content').append(this._contentTemplate({icon: iconContent,size: sizeButton, text: textButton, buttonAlignment:alignButton, textAlignment:alignText, color :color}));
+                                return this;
+                            }
+                            
                         },
                         renderOptions: function(model, options) {
+                            this.$(".blk-button__icon").empty();
+                            this.$(".blk-button__label").css('margin-top','');
+                            var svgName = this.model.options.ButtonStyleOption.icon;
+                            // fetch icon content
+                            if(svgName !== ""  ){
+                                var svgCollectionObj = new SvgCollection({"svgName":svgName});
+                                svgCollectionObj.fetchIcons(_.bind(function(error, svg) {
+                                    if (error) {
+                                        console.error(error);
+                                    } else {
+                                        this.$(".blk-button__label").css('margin-top','-15px');
+                                        this.$(".blk-button__icon").empty();
+                                        this.$(".blk-button__icon").append(svg.content);
+                                    }
+                                },this));
+                            }
                             var sizeButton = this.model.options.ButtonStyleOption.size;
                             var alignButton = this.model.options.ButtonStyleOption.buttonAlignment;
                             var alignText = this.model.options.ButtonStyleOption.textAlignment;
@@ -41,7 +79,6 @@
                             this.$(".block-button").attr("class", "block-button " + sizeButton + " " + alignButton + " " + alignText + " " + color);
                             var textButton = this.model.options.ButtonOption.text?this.model.options.ButtonOption.text:translate('button');
                             this.$(".txt span").text(textButton);
-                            this.$(".block-button").attr("class", "block-button " + sizeButton + " " + alignButton + " " + alignText + " " + color);
                             return this;
                         }
                     });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js	(révision 11559)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js	(révision 11560)
@@ -86,7 +86,8 @@
                          *  
                          */
                         _onClickBrowseIcons:function(event){
-                            this.selectIconDialog.fetchIcons();
+                            var usedIcon = (this.model.icon !== '' ? this.model.icon: '');
+                            this.selectIconDialog.getIcons(usedIcon);
                             this.selectIconDialog.open();
                         },
 
@@ -97,27 +98,17 @@
                         },
 
                         getIconContent:function(svgName){
-                            this.params = new Params();
-                            //fetch le nom du repertoire collection icon utilisé
-                            this.value = this.params.fetch();
-                            this.value.done(_.bind(function(data) {
-                                var nomRepertoire  = data.IconsCollection;
-                                var svgIconObj = new SvgCollection({"name":nomRepertoire,"svgName":svgName});
-                                svgIconObj = svgIconObj.fetch();
-                                // extraction icones
-                                svgIconObj.done(_.bind(function(data) {
-                                    this.selectedIconEl.html(data.content);
+                            var svgCollectionObj = new SvgCollection({"svgName":svgName});
+                            svgCollectionObj.fetchIcons(_.bind(function(error, svg) {
+                                if (error) {
+                                    console.error(error);
+                                } else {
+                                    this.selectedIconEl.html(svg.content);
                                     this.selectedIconEl.show();
                                     $('.btn-browse-icons').show(); 
-                                },this)).fail(function(error) {
-                                    console.log(error);
-                                });
+                                }
+                            },this));
                 
-                            },this)).fail(function(error) {
-                                // Handle any errors that occurred during the fetch() method
-                                console.log(error);
-                            });
-                
                         },
 
                         /**
@@ -131,7 +122,7 @@
                             //cacher selected-icon au chargement
                             this.selectedIconEl.hide();
                             $('.btn-browse-icons').hide(); 
-                            if(this.model.icon != ""){
+                            if(this.model.icon !== ""){
                                 this.getIconContent(this.model.icon);
                             }  
                             return this;
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/SelectIconView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/SelectIconView.js	(révision 11559)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/SelectIconView.js	(révision 11560)
@@ -43,30 +43,23 @@
             this._template = this.buildTemplate(selectIcon, translate);
 
         },
-        fetchIcons: function(){
-            $('.selectIcon').addClass("loading");
-            this.params = new Params();
-            this.value = this.params.fetch();
-            this.value.done(_.bind(function(data) {
-                var svgCollectionObj = new SvgCollection({"name":data.IconsCollection});
-                svgCollectionObj = svgCollectionObj.fetch();
-                //extraction des icones
-                svgCollectionObj.done(_.bind(function(response) {
-                    this.svgCollection = response;
+        getIcons: function(usedIcon){
+            var svgCollectionObj = new SvgCollection();
+            svgCollectionObj.fetchIcons(_.bind(function(error, svgs) {
+                if (error) {
+                    console.error(error);
+                } else {
+                    this.svgCollection = svgs;
+                    this.selected = (usedIcon !== '' ? usedIcon : '');
                     currentList = this.svgCollection;
                     this.trigger('data:fetched');
-                    $('.selectIcon').removeClass("loading");
-                },this)).fail(function(error) {
-                    console.log(error);
-                });
+                }
+            },this));
 
-            },this)).fail(function(error) {
-                console.log(error);
-            });
-
             this.listenTo(this, 'data:fetched', this.render);
         },
         render: function() {
+           
             var data = (this.svgCollection === undefined ? [] : this.svgCollection);
             this._super();
             this.undelegateEvents();
Index: src/less/imports/button_block/button_block.less
===================================================================
--- src/less/imports/button_block/button_block.less	(révision 11559)
+++ src/less/imports/button_block/button_block.less	(révision 11560)
@@ -20,6 +20,7 @@
             display: inline-block;
             vertical-align: middle;
             line-height: 1;
+            
         }
         &.full-width .button {
             display: block;
@@ -56,3 +57,5 @@
         }
     }
 }
+
+
Index: src/less/imports/button_block/styleOptions.less
===================================================================
--- src/less/imports/button_block/styleOptions.less	(révision 11559)
+++ src/less/imports/button_block/styleOptions.less	(révision 11560)
@@ -122,4 +122,45 @@
 }
 .color-radio.active .radio, .color-radio.active:hover .radio, .color-switcher.active .radio, .color-switcher.active:hover .radio {
     background-position: -1px -24px;
-}
\ No newline at end of file
+}
+
+
+/* button icon  */
+.selected-icon-wrapper {
+    display: flex;
+    justify-content: center;
+    align-items: center;
+    font-size: 50px;
+  }
+  
+  .selected-icon-wrapper svg {
+    width: 100px;
+    height: 100px;
+  }
+  .container-icon.option-content{
+    padding: 20px;
+  }
+  .container-icon.option-content .grid-container-icon {
+    display: grid;
+    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
+    grid-gap: 5px;
+  }
+  
+  .container-icon.option-content .box-icon {
+    padding: 5px;
+    text-align: center;
+    border: 1px solid;
+    border-color: #f3eaea;
+    font-size: 50px;
+  }
+  .container-icon.option-content .box-icon svg {
+    width:  50px;
+    height:  50px;
+  }
+  .container-icon.option-content .selected-icon{
+      background-color: #41ffbe;
+  }
+ //rendu button backoffice
+  .force-horizontal-align {
+    margin-top: -15px;
+  }
\ No newline at end of file
Index: src/less/main.less
===================================================================
--- src/less/main.less	(révision 11559)
+++ src/less/main.less	(révision 11560)
@@ -23,7 +23,6 @@
 @import "./imports/imageEditor.less";
 @import "./imports/panel_gallery_animations.less";
 @import "./imports/gallery_style.less";
-@import "./imports/blockButton.less";
 @import "./imports/htmlEditor.less";
 @import "./imports/navigation.less";
 @import "./imports/editImageDialog.less";
