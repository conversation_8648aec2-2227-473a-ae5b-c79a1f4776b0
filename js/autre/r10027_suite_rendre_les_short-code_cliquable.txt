Revision: r10027
Date: 2022-12-21 11:41:12 +0300 (lrb 21 Des 2022) 
Author: sraz<PERSON><PERSON><PERSON><PERSON> 

## Commit message
suite rendre les short-code cliquable 

## Files changed

## Full metadata
------------------------------------------------------------------------
r10027 | srazana<PERSON>lisoa | 2022-12-21 11:41:12 +0300 (lrb 21 Des 2022) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/less/imports/filePanel.less
   M /branches/ideo3_v2/integration/src/less/imports/navigation.less

suite rendre les short-code cliquable 
------------------------------------------------------------------------

## Diff
Index: src/less/imports/filePanel.less
===================================================================
--- src/less/imports/filePanel.less	(révision 10026)
+++ src/less/imports/filePanel.less	(révision 10027)
@@ -476,6 +476,7 @@
     line-height: 36px;
     margin-left: 30px;
     padding: 0 15px;
+    cursor: pointer;
 }
 .shortcode::-moz-selection {
     background-color: #f27330;
Index: src/less/imports/navigation.less
===================================================================
--- src/less/imports/navigation.less	(révision 10026)
+++ src/less/imports/navigation.less	(révision 10027)
@@ -757,6 +757,7 @@
     .aff-menu .shortcode{
         font-family: 'Open Sans', sans-serif;
         font-weight: 400;
+        cursor: pointer;
         font-size: 12px;
         text-align: center;
         color: #777;
