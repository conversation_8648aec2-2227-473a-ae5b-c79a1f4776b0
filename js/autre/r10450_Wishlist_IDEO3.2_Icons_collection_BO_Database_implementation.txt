Revision: r10450
Date: 2023-02-20 11:09:23 +0300 (lts 20 Feb 2023) 
Author: jn.harison 

## Commit message
Wishlist IDEO3.2:Icons collection BO&Database implementation

## Files changed

## Full metadata
------------------------------------------------------------------------
r10450 | jn.harison | 2023-02-20 11:09:23 +0300 (lts 20 Feb 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/dev/#librairies/vendor/CoreConfigurationService/config/module.config.php
   M /branches/ideo3_v2/dev/#librairies/vendor/CoreConfigurationService/src/CoreConfigurationService/Controller/IndexController.php
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Models/Params.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/ParamsPanel.js
   A /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Templates/IconsCollection.html
   A /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/IconsCollectionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/i18n.js

Wishlist IDEO3.2:Icons collection BO&Database implementation
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Models/Params.js
===================================================================
--- src/js/JEditor/ParamsPanel/Models/Params.js	(révision 10449)
+++ src/js/JEditor/ParamsPanel/Models/Params.js	(révision 10450)
@@ -61,7 +61,8 @@
             FontsGoogle: null,
             Logo: null,
             LogoSmall: null,
-            Favicon: null
+            Favicon: null,
+            IconsCollection: "outline"
         },
         initialize: function () {
             this._super();
Index: src/js/JEditor/ParamsPanel/ParamsPanel.js
===================================================================
--- src/js/JEditor/ParamsPanel/ParamsPanel.js	(révision 10449)
+++ src/js/JEditor/ParamsPanel/ParamsPanel.js	(révision 10450)
@@ -11,11 +11,12 @@
     "./Views/DonneeStructureeView",
     "./Views/MarqueClientView",
     "./Views/FontsGoogleView",
+    "./Views/IconsCollectionView",
     "./Models/Params",
     //hidden
     "jqueryui/datepicker"
 ],
-        function ($, _, PanelView, Events, ParamsPanelTemplate, translate, SocialNetworks,SEOView, MobileStoreView, PolitiqueConfidentialiteView,DonneeStructureeView, MarqueClientView, FontsGoogleView, Params) {
+        function ($, _, PanelView, Events, ParamsPanelTemplate, translate, SocialNetworks,SEOView, MobileStoreView, PolitiqueConfidentialiteView,DonneeStructureeView, MarqueClientView, FontsGoogleView, IconsCollectionView, Params) {
             var SocialPanel = PanelView.extend(
                     /**
                      * @lends JEditor.Panels.MessagePanel.prototype
@@ -57,6 +58,11 @@
                                             icon:"icon-html",
                                             title:translate("MarqueClient") 
                                         },
+                                        IconsCollection:{
+                                            object: new IconsCollectionView({model:this.params}),
+                                            icon:"icon-html",
+                                            title:translate("IconsCollectionView")
+                                        },
                                         FontsGoogle:{
                                             object: new FontsGoogleView({model:this.params}),
                                             icon:"icon-html",
Index: src/js/JEditor/ParamsPanel/Templates/IconsCollection.html
===================================================================
--- src/js/JEditor/ParamsPanel/Templates/IconsCollection.html	(nonexistent)
+++ src/js/JEditor/ParamsPanel/Templates/IconsCollection.html	(révision 10450)
@@ -0,0 +1,37 @@
+<div class="main-content__wrapper ">
+    <!--
+        ******************************************
+        créer nouvelle childView marque client,
+        y déplacer ce contenu
+        ******************************************
+        -->
+        <div class="ideo-title">
+            <h1>
+                <%=__("iconsCollection")%>
+                <span><%=__("set_icons_collection")%></span>
+            </h1>
+        </div>
+
+        <div id="content" class="inline-params thin-border  radius  shadow">
+            <span class="inline-params__name">
+                <svg width="70px" height="70px" viewBox="0 0 70 70" enable-background="new 0 0 70 70" xml:space="preserve">
+                <path fill="#34495E" d="M35,0C15.67,0,0,15.67,0,35s15.67,35,35,35s35-15.67,35-35S54.33,0,35,0z M35,67C17.327,67,3,52.673,3,35S17.327,3,35,3s32,14.327,32,32S52.673,67,35,67z M22.436,35.002l4.769-4.528c0.556-0.527,0.556-1.385,0-1.912c-0.556-0.533-1.464-0.533-2.02,0l-5.782,5.484c-0.556,0.527-0.556,1.385,0,1.912l5.788,5.501c0.561,0.527,1.47,0.527,2.025,0s0.556-1.385,0-1.918L22.436,35.002z M38.728,28.149c-0.7-0.341-1.563-0.082-1.927,0.582l-6.152,11.305c-0.365,0.67-0.087,1.489,0.613,1.829c0.694,0.347,1.563,0.083,1.921-0.582l6.158-11.304C39.699,29.314,39.428,28.49,38.728,28.149z M44.77,28.562c-0.557-0.527-1.465-0.527-2.02,0c-0.563,0.527-0.563,1.385,0,1.912l4.779,4.539l-4.769,4.528c-0.562,0.533-0.562,1.391,0,1.918c0.556,0.527,1.458,0.527,2.021,0l5.775-5.484c0.561-0.527,0.561-1.391,0-1.918L44.77,28.562z"></path>
+                </svg>
+                <%=__("collection")%>
+            </span>
+            <label>
+                <span class="custom-input">
+                    <select id="typeCollection" name="IconsCollection" class="neutral-input" data-autosave="true">
+                        <option value="outline" <%=(IconsCollection==='outline' || IconsCollection ==null) ? 'selected':'' %> ><%=__("outline") %></option>
+                        <option value="solid" <%=(IconsCollection==='solid') ? 'selected':'' %> ><%=__("solid") %></option>
+                        <option value="duotone" <%=(IconsCollection==='duotone') ? 'selected':'' %> ><%=__("duotone") %></option>
+                    </select>
+                </span>
+            </label>
+        </div>
+        <!-- 
+        ******************
+        end new childView
+        ******************
+        -->
+    </div>
\ No newline at end of file

Property changes on: src/js/JEditor/ParamsPanel/Templates/IconsCollection.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/ParamsPanel/Views/IconsCollectionView.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/IconsCollectionView.js	(nonexistent)
+++ src/js/JEditor/ParamsPanel/Views/IconsCollectionView.js	(révision 10450)
@@ -0,0 +1,34 @@
+define(['jquery',
+        "underscore", 
+        'JEditor/Commons/Ancestors/Views/View',
+        'text!../Templates/IconsCollection.html',
+        'i18n!../nls/i18n'], function (
+            $,
+            _,
+            View, 
+            template,
+            translate) {
+            var IconsCollection = View.extend({
+                events: {
+                    'change #typeCollection': 'onTypeCollectionChange'
+                },
+                initialize: function () {
+                    this._super();
+                    this._template = this.buildTemplate(template,translate);
+                },
+               
+                onTypeCollectionChange: function (model) {
+                    var value = this.$('#typeCollection').val();
+                    this.model.attributes['IconsCollection'] = value;
+                    this.model.save();  
+                },
+                render: function () {
+                    this.undelegateEvents();
+                    this.$el.html(this._template(this.model.toJSON()));
+                    this.delegateEvents();
+                    return this;
+                }
+            
+            });
+            return IconsCollection;
+        });
\ No newline at end of file

Property changes on: src/js/JEditor/ParamsPanel/Views/IconsCollectionView.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 10449)
+++ src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 10450)
@@ -77,5 +77,12 @@
         "Multiple_FontsGoogleStatic": "Plusieurs scripts <strong>&lt;link rel=\"preconnect\" href=\"https\:\/\/fonts.gstastic.com\" crossorigin\&gt;</strong> détectés",
         "Missing_FontsGoogleStatic": "Script <strong>&lt;link rel=\"preconnect\" href=\"https\:\/\/fonts.gstatic.com\" crossorigin\&gt;</strong>  manquant",
         "Multiple_FontsGoogleApiSwap": "Plusieurs scripts <strong>&lt;link href=\"https\:\/\/fonts.googleapis.com\/css2\?...\"\&gt;</strong> avec l\'option display swap détectés",
-        "Missing_FontsGoogleApiSwap": "Script  <strong>&lt;link href=\"https\:\/\/fonts.googleapis.com\/css2\?...\"\&gt;</strong> avec l\'option display swap manquant"
+        "Missing_FontsGoogleApiSwap": "Script  <strong>&lt;link href=\"https\:\/\/fonts.googleapis.com\/css2\?...\"\&gt;</strong> avec l\'option display swap manquant",
+        "iconsCollection": "Collection d\'icons",
+        "IconsCollectionView": "Collection d\'icons",
+        "set_icons_collection": "Choisissez la collection d\'icons a utiliser sur le site",
+        "collection": "Collection",
+        "outline": "Outline(défaut)",
+        "solid": "Solid",
+        "duotone": "Duotone"
     });
Index: src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 10449)
+++ src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 10450)
@@ -81,5 +81,12 @@
         "Multiple_FontsGoogleStatic": "Plusieurs scripts <strong>&lt;link rel=\"preconnect\" href=\"https\:\/\/fonts.gstastic.com\" crossorigin\&gt;</strong> détectés",
         "Missing_FontsGoogleStatic": "Script <strong>&lt;link rel=\"preconnect\" href=\"https\:\/\/fonts.gstatic.com\" crossorigin\&gt;</strong>  manquant",
         "Multiple_FontsGoogleApiSwap": "Plusieurs scripts <strong>&lt;link href=\"https\:\/\/fonts.googleapis.com\/css2\?...\"\&gt;</strong> avec l\'option display swap détectés",
-        "Missing_FontsGoogleApiSwap": "Script  <strong>&lt;link href=\"https\:\/\/fonts.googleapis.com\/css2\?...\"\&gt;</strong> avec l\'option display swap manquant"
+        "Missing_FontsGoogleApiSwap": "Script  <strong>&lt;link href=\"https\:\/\/fonts.googleapis.com\/css2\?...\"\&gt;</strong> avec l\'option display swap manquant",
+        "iconsCollection": "Collection d\'icons",
+        "IconsCollectionView": "Collection d\'icons",
+        "set_icons_collection": "Choisissez la collection d\'icons a utiliser sur le site",
+        "collection": "Collection",
+        "outline": "Outline(défaut)",
+        "solid": "Solid",
+        "duotone": "Duotone"
     });
Index: src/js/JEditor/ParamsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 10449)
+++ src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 10450)
@@ -89,8 +89,14 @@
         "Multiple_FontsGoogleStatic": "Several  <strong>&lt;link rel=\"preconnect\" href=\"https\:\/\/fonts.gstastic.com\" crossorigin\&gt;</strong> scripts detected",
         "Missing_FontsGoogleStatic": "Missing <strong>&lt;link rel=\"preconnect\" href=\"https\:\/\/fonts.gstatic.com\" crossorigin\&gt;</strong>",
         "Multiple_FontsGoogleApiSwap": "Several <strong>&lt;link href=\"https\:\/\/fonts.googleapis.com\/css2\?...\"\&gt;</strong> with display swap option detected",
-        "Missing_FontsGoogleApiSwap": "Missing <strong>&lt;link href=\"https\:\/\/fonts.googleapis.com\/css2\?...\"\&gt;</strong> with display swap missing"
-    
+        "Missing_FontsGoogleApiSwap": "Missing <strong>&lt;link href=\"https\:\/\/fonts.googleapis.com\/css2\?...\"\&gt;</strong> with display swap missing",
+        "iconsCollection": "Icons collection",
+        "IconsCollectionView": "Icons",
+        "set_icons_collection": "Choose the collection of icons to use on the site",
+        "collection": "Collection",
+        "outline": "Outline(default)",
+        "solid": "Solid",
+        "duotone": "Duotone"
     },
     "fr-fr": true,
     "fr-ca": true
