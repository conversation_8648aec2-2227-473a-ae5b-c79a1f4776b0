Revision: r13477
Date: 2024-11-20 08:41:49 +0300 (lrb 20 Nov 2024) 
Author: frahajanirina 

## Commit message
Wishlist:IDEO3.2:fix l'iframe et bouton stats

## Files changed

## Full metadata
------------------------------------------------------------------------
r13477 | frahajanirina | 2024-11-20 08:41:49 +0300 (lrb 20 Nov 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/DashBoard/Templates/DashBoardTpl.html
   M /branches/ideo3_v2/integration/src/less/imports/dashboard.less

Wishlist:IDEO3.2:fix l'iframe et bouton stats
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/DashBoard/Templates/DashBoardTpl.html
===================================================================
--- src/js/JEditor/DashBoard/Templates/DashBoardTpl.html	(révision 13476)
+++ src/js/JEditor/DashBoard/Templates/DashBoardTpl.html	(révision 13477)
@@ -241,7 +241,7 @@
 <div class="stats">
     <div class="row">
         <% if(!env){ %>
-        <iframe width="1024" height="495" src="<%=url%>" scrolling="no" style="border:none;" class="stats-dashboard"></iframe>
+        <iframe width="1024" height="450" src="<%=url%>" scrolling="no" style="border:none;" class="stats-dashboard"></iframe>
         <div class="gotostats">
             <button class="btn-stats <%= lang == 'fr' ? 'btn-stats-fr' : 'btn-stats-en' %>">
                 <svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="22" height="22" viewBox="0 0 32 32">
Index: src/less/imports/dashboard.less
===================================================================
--- src/less/imports/dashboard.less	(révision 13476)
+++ src/less/imports/dashboard.less	(révision 13477)
@@ -267,8 +267,8 @@
 		color: #fff;
 	}
 	.btn-stats {
-		margin: 24px auto;
-		display: flex;
+		margin: 0px auto 3rem;
+		display: inline-flex;
 		justify-content: space-between;
 		border-radius: 5px;
 		padding: 15px 13px;
