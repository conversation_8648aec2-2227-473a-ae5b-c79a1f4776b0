Revision: r11725
Date: 2023-12-12 11:42:57 +0300 (tlt 12 Des 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: bug retouche image

## Files changed

## Full metadata
------------------------------------------------------------------------
r11725 | srazana<PERSON>lisoa | 2023-12-12 11:42:57 +0300 (tlt 12 Des 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileDetailManagerView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileListView.js

wishlist IDEO3.2: bug retouche image
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/FilePanel/Views/FileDetailManagerView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileDetailManagerView.js	(révision 11724)
+++ src/js/JEditor/FilePanel/Views/FileDetailManagerView.js	(révision 11725)
@@ -156,19 +156,23 @@
         return false;
     },
     _onImageEdited: function(newFile, oldFile) {
+        var self = this;
         console.log('creating new file');
         console.dir(newFile);
         if (this.referrer) {
-            var currentCollection = this.options.childViews.fileGroupList.collection.get(this.referrer);
+            var currentCollection = this.options.childViews.fileGroupList.get(this.referrer);
             currentCollection.files.remove(oldFile);
             currentCollection.files.add(newFile);
+            this.options.childViews.fileList.collection.resetFilter();
+            this.options.childViews.fileList.collection.fetch();
             currentCollection.save();
             console.log('in collection');
             console.dir(currentCollection);
         } else {
             console.log('hors collection');
-            this.options.childViews.fileList.collection.create(newFile);
-            console.dir(this.options.childViews.fileList.collection);
+            this.options.childViews.fileList.collection.add(newFile, { at: 0 });
+            this.options.childViews.fileList.collection.resetFilter();
+            this.options.childViews.fileList.collection.fetch();
         }
         this.trigger(Events.FileDetailManagerViewEvents.EDIT, newFile.cid, this.referrer, oldFile);
     },
Index: src/js/JEditor/FilePanel/Views/FileListView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileListView.js	(révision 11724)
+++ src/js/JEditor/FilePanel/Views/FileListView.js	(révision 11725)
@@ -37,6 +37,9 @@
             this._emptyTemplate = this.buildTemplate(emptyFileList, translate);
             this.listenTo(this.collection, Events.BackboneEvents.REQUEST, this.onRequest);
             this.listenTo(this.collection, Events.BackboneEvents.SYNC, this.onSync);
+            this.listenTo(this.collection, Events.BackboneEvents.ADD, this._rebuildList);
+            this.listenTo(this.collection, Events.BackboneEvents.REMOVE, this._rebuildList);
+            this.listenTo(this.collection, Events.BackboneEvents.CHANGE, this._rebuildList);
         },
         onRequest: function (e,r) {
             if (e.models) {
