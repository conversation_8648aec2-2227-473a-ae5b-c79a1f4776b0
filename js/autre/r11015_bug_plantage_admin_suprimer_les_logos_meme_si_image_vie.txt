Revision: r11015
Date: 2023-06-12 14:34:44 +0300 (lts 12 Jon 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
bug plantage admin (suprimer les logos meme si image vie)

## Files changed

## Full metadata
------------------------------------------------------------------------
r11015 | srazanandralisoa | 2023-06-12 14:34:44 +0300 (lts 12 Jon 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/MarqueClientView.js

bug plantage admin (suprimer les logos meme si image vie)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Views/MarqueClientView.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 11014)
+++ src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 11015)
@@ -113,7 +113,9 @@
             this.fileCollection.setId(cid);
             this.fileCollection.fetch({async:false});
             var model =this.fileCollection.get(cid);
-            model.destroy(); 
+            if (model!=undefined) {
+                model.destroy(); 
+            }
         },
         
         _onlyComputerLogoSmall: function(e) {
@@ -258,8 +260,8 @@
             var cid = this.model.get(name).id;
             this.fileCollection.setId(cid);
             this.fileCollection.fetch({async:false});
-            var model =this.fileCollection.get(cid);
-            if(!this.app.user.can("delete_file")){
+            var model = this.fileCollection.get(cid);
+            if(!this.app.user.can("delete_file") && model){
                 return false;
             }
                 if (!this.app.params.dontAskAgainFor['deleteFileItem']) {
@@ -269,7 +271,7 @@
                         type: 'delete',
                         onOk: _.bind(function() {
                             this.model.set(name, null);
-                            model.destroy();
+                            if(model!=undefined) model.destroy();
                             this.model.save();
                             this.render();
                         }, this),
