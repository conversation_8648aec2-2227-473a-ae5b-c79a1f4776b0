Revision: r11933
Date: 2024-02-20 09:07:17 +0300 (tlt 20 Feb 2024) 
Author: rrakotoarinelina 

## Commit message
balises métas OpenGraph - crop image, accepter png et jpeg seulement 

## Files changed

## Full metadata
------------------------------------------------------------------------
r11933 | rrakotoarinelina | 2024-02-20 09:07:17 +0300 (tlt 20 Feb 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Views/FileUploaderView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/i18n.js

balises métas OpenGraph - crop image, accepter png et jpeg seulement 
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Files/Views/FileUploaderView.js
===================================================================
--- src/js/JEditor/Commons/Files/Views/FileUploaderView.js	(révision 11932)
+++ src/js/JEditor/Commons/Files/Views/FileUploaderView.js	(révision 11933)
@@ -46,10 +46,14 @@
             finally {
                 var defaultPreview;
                 if (this.currentFile) {
-                    if (this.currentFile.isImg())
-                        defaultPreview = '<div class="preview"><div class="imagepreview shown" style="background-image: url(' + this.currentFile.thumb + '); background-size: cover; display: block; background-position: initial initial; background-repeat: initial initial;"></div></div>';
-                    else
+                   
+                    if (this.currentFile.isImg()){
+                        var toDisplay = this.options.uploadParams.isOpengraph ? this.currentFile.fileUrl : this.currentFile.thumb;
+                        defaultPreview = '<div class="preview"><div class="imagepreview shown" style="background-image: url(' + toDisplay + '); background-size: cover; display: block; background-position: initial initial; background-repeat: initial initial;"></div></div>';
+                    }
+                    else{
                         defaultPreview = '<div class="preview"><div class="filepreview shown" ><p>' + this.currentFile.name + '</p><div class="file ' + this.currentFile.ext + '">.' + this.currentFile.ext + '</div></div></div>';
+                    }
                 }
                 else
                     defaultPreview = '<div class="preview"><div class="imagepreview"></div></div>';
Index: src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js
===================================================================
--- src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 11932)
+++ src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 11933)
@@ -104,7 +104,8 @@
                     uploadSuccess: 'Le transfert des fichiers s\'est déroulé avec succès',
                     uploadFailServerError: 'Impossible de transférer le fichier %name% à cause de l\'erreur: "%error%"',
                     uploadFaviconCorrupted: 'Le format SVG n\'est pas autorisé pour les favicons',
-                    uploadOpenGraphFail: 'Echec du chargement. L\'image doit être de 1200x630 pixels',
+                    uploadOpenGraphFail: 'Echec du chargement. L\'image doit être de 1200x630 pixels au minimum',
+                    uploadOpenGraphForbiddenType: 'Ce format d\'image n\'est pas autorisé pour l\'opengraph',
                 }
             };
             this.options.lang = $.extend({}, settings.lang, this.options.lang ? this.options.lang : {});
@@ -378,60 +379,145 @@
                                  //check pour open graph
                                 if(uploader.options.isOpengraph)
                                 { 
-                                    //prendre les dimensions de l'image
-                                    var imageUrl = uploadInfos.code; 
-                                    var img = new Image();
-                                    img.src = imageUrl;
 
-                                    img.onload = function() {
-                                        
-                                        if (img.width >= 1200 && img.height >= 630) {
+                                    var acceptedType = ['image/jpeg','image/png'];
 
-                                             //---------------------code répété-------------------------------
-                                            realPreview.show();
-                                            realPreview.addClass('shown');
-                                            $frag.append(preview);
-                                            uploader.previews[file.name] = preview;
-                                
-                                            uploadInfos.index = uploader.dataArray.push(uploadInfos) - 1;
-                                            preview.data('uploadId', uploadInfos.index);
-                                            uploader._trigger('previewcreated', null, {uploader: uploader, previewElement: preview, previews: uploader.previews});
-                                            uploader.element.prepend($frag);
-                                            if (uploader.options.maxFiles !== -1) {
-                                                while ((uploader.uploaded.length + uploader.uploading) > uploader.options.maxFiles) {
-                                                    var data = uploader.uploaded.shift();
-                                                    data.preview.remove();
+                                    if (acceptedType.includes(uploadInfos.type)) {
+                                            
+                                        //prendre les dimensions de l'image
+                                        var imageUrl = uploadInfos.code; 
+                                        var img = new Image();
+                                        img.src = imageUrl;
+
+                                        img.onload = function() {
+                                            
+                                            if (img.width >= 1200 && img.height >= 630) {
+
+
+                                                //-------------------crop image -----------------
+
+                                                var desiredWidth = 1200;
+                                                var desiredHeight = 630;
+
+                                                var canvas = document.createElement('canvas');
+                                                var ctx = canvas.getContext('2d');
+
+                                                // Calculer le centre de l'image 
+                                                var centerX = img.width /  2;
+                                                var centerY = img.height /  2;
+
+                                                // Calcul nouvelle dimension
+                                                var newWidth = Math.min(desiredWidth, img.width);
+                                                var newHeight = Math.min(desiredHeight, img.height);
+
+                                                // Calculer les coordonées du crop
+                                                var cropX = centerX - newWidth /  2;
+                                                var cropY = centerY - newHeight /  2;
+
+
+                                                //assigner au canvas les nouveaus size
+                                                canvas.width = newWidth;
+                                                canvas.height = newHeight;
+
+                                                //extraire depuis cropx et cropy sur l'image original et dessiner sur le canvas
+                                                ctx.drawImage(img, cropX, cropY, newWidth, newHeight,  0,  0, newWidth, newHeight);
+
+                                                // Convert le canvas en dataurl
+                                                var dataURL = canvas.toDataURL(file.type);
+
+                                                //conversion d'une URL de données en base64 en un tableau d'octets
+                                                var arr = dataURL.split(','),
+                                                baseString = atob(arr[arr.length - 1]), 
+                                                n = baseString.length, 
+                                                u8arr = new Uint8Array(n);
+                                                while(n--){
+                                                    u8arr[n] = baseString.charCodeAt(n);
                                                 }
-                                            }
-                                             //------------------------------------------------------
-                                            uploader._upload.call(uploader);
 
-                                        }else{
-                                            message = uploader.options.lang.uploadOpenGraphFail;
-                                            uploader.errors.push(message);
-                                            files.rejected++;
-                                            if (files.length === 1)
-                                                uploader._onComplete();
-                                        
-                                            //---------------------code répété-------------------------------
-                                            realPreview.show();
-                                            realPreview.addClass('shown');
-                                            $frag.append(preview);
-                                            uploader.previews[file.name] = preview;
-                                
-                                            uploadInfos.index = uploader.dataArray.push(uploadInfos) - 1;
-                                            preview.data('uploadId', uploadInfos.index);
-                                            uploader._trigger('previewcreated', null, {uploader: uploader, previewElement: preview, previews: uploader.previews});
-                                            uploader.element.prepend($frag);
-                                            if (uploader.options.maxFiles !== -1) {
-                                                while ((uploader.uploaded.length + uploader.uploading) > uploader.options.maxFiles) {
-                                                    var data = uploader.uploaded.shift();
-                                                    data.preview.remove();
+                                                // creation d'un objet File à partir d'un tableau d'octets
+                                                var croppedFile = new File([u8arr], file.name, {type:file.type});
+
+                                                // Remplacer les infos par image cropped
+                                                uploadInfos.value = croppedFile;
+                                                uploadInfos.name = croppedFile.name;
+                                                uploadInfos.size =  croppedFile.size;
+                                                uploadInfos.code = e.target.result;
+                                                uploadInfos.type = croppedFile.type;
+                                            //-------------------fin crop image -----------------
+
+                                                //---------------------code répété-------------------------------
+                                                realPreview.show();
+                                                realPreview.addClass('shown');
+                                                $frag.append(preview);
+                                                uploader.previews[file.name] = preview;
+                                    
+                                                uploadInfos.index = uploader.dataArray.push(uploadInfos) - 1;
+                                                preview.data('uploadId', uploadInfos.index);
+                                                uploader._trigger('previewcreated', null, {uploader: uploader, previewElement: preview, previews: uploader.previews});
+                                                uploader.element.prepend($frag);
+                                                if (uploader.options.maxFiles !== -1) {
+                                                    while ((uploader.uploaded.length + uploader.uploading) > uploader.options.maxFiles) {
+                                                        var data = uploader.uploaded.shift();
+                                                        data.preview.remove();
+                                                    }
                                                 }
+                                                //------------------------------------------------------
+                                                uploader._upload.call(uploader);
+
+                                            }else{
+                                                message = uploader.options.lang.uploadOpenGraphFail;
+                                                uploader.errors.push(message);
+                                                files.rejected++;
+                                                if (files.length === 1)
+                                                    uploader._onComplete();
+                                            
+                                                //---------------------code répété-------------------------------
+                                                realPreview.show();
+                                                realPreview.addClass('shown');
+                                                $frag.append(preview);
+                                                uploader.previews[file.name] = preview;
+                                    
+                                                uploadInfos.index = uploader.dataArray.push(uploadInfos) - 1;
+                                                preview.data('uploadId', uploadInfos.index);
+                                                uploader._trigger('previewcreated', null, {uploader: uploader, previewElement: preview, previews: uploader.previews});
+                                                uploader.element.prepend($frag);
+                                                if (uploader.options.maxFiles !== -1) {
+                                                    while ((uploader.uploaded.length + uploader.uploading) > uploader.options.maxFiles) {
+                                                        var data = uploader.uploaded.shift();
+                                                        data.preview.remove();
+                                                    }
+                                                }
+                                                //------------------------------------------------------
+                                                
                                             }
-                                             //------------------------------------------------------
-                                            
                                         }
+
+                                    }else{ // si le format d'image pour opengraph n'est ni png ni jpeg
+
+                                        message = uploader.options.lang.uploadOpenGraphForbiddenType;
+                                        uploader.errors.push(message);
+                                        files.rejected++;
+                                        if (files.length === 1)
+                                            uploader._onComplete();
+                                    
+                                        //---------------------code répété-------------------------------
+                                        realPreview.show();
+                                        realPreview.addClass('shown');
+                                        $frag.append(preview);
+                                        uploader.previews[file.name] = preview;
+                            
+                                        uploadInfos.index = uploader.dataArray.push(uploadInfos) - 1;
+                                        preview.data('uploadId', uploadInfos.index);
+                                        uploader._trigger('previewcreated', null, {uploader: uploader, previewElement: preview, previews: uploader.previews});
+                                        uploader.element.prepend($frag);
+                                        if (uploader.options.maxFiles !== -1) {
+                                            while ((uploader.uploaded.length + uploader.uploading) > uploader.options.maxFiles) {
+                                                var data = uploader.uploaded.shift();
+                                                data.preview.remove();
+                                            }
+                                        }
+                                        //------------------------------------------------------
+
                                     }
                                 }else{
 
@@ -589,7 +675,7 @@
             try {
                 obj = data;
                 if (obj.fileUrl)
-                    currentUpload.preview.children('.imagepreview').css({background: 'url(' + obj.fileUrl + ')', backgroundSize: 'cover'});
+                    currentUpload.preview.children('.imagepreview').css({background: 'url(' + obj.fileUrl + ') center center', backgroundSize: 'cover'});
                 this.previews[currentUpload.name].find('.progressbar>.bar').stop().animate({width: '100%'});
                 this.previews[currentUpload.name].find('.action.abort').off('click.abort').remove();
                 this.dataArray[currentUpload.index].uploaded = 100;
Index: src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 11932)
+++ src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 11933)
@@ -68,7 +68,7 @@
         "Add_logo": "Ajouter le logo",
         "Add_logo_small": "Ajouter le logo \"small\"",
         "Add_favicon": "Ajouter le favicon",
-        "Add_opengraph":"Ajouter image OpenGraph (L'image doit être de 1200x630 pixels)",
+        "Add_opengraph":"Ajouter image OpenGraph (L'image doit être de 1200x630 pixels au minimum)",
         "Text_opengraph": "! Les balises OpenGraph ne sont générés dans les pages qu'après upload d'une image.",
         "set_marque_client": "Renseignez les informations de Marque du client tel que l'enseigne, le logo ...",
         "DefaultMessageUploaderLogo": "Cliquez ici ou glissez-déposez une image",
Index: src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 11932)
+++ src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 11933)
@@ -72,7 +72,7 @@
         "Add_logo": "Ajouter le logo",
         "Add_logo_small": "Ajouter le logo \"small\"",
         "Add_favicon": "Ajouter le favicon",
-        "Add_opengraph":"Ajouter image OpenGraph (L'image doit être de 1200x630 pixels)",
+        "Add_opengraph":"Ajouter image OpenGraph (L'image doit être de 1200x630 pixels au minimum)",
         "Text_opengraph": "! Les balises OpenGraph ne sont générés dans les pages qu'après upload d'une image.",
         "set_marque_client": "Renseignez les informations de Marque du client tel que l'enseigne, le logo ...",
         "DefaultMessageUploaderLogo": "Cliquez ici ou glissez-déposez une image",
Index: src/js/JEditor/ParamsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 11932)
+++ src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 11933)
@@ -80,7 +80,7 @@
         "Add_logo": "Add logo",
         "Add_logo_small": "Add logo \"small\"",
         "Add_favicon": "Add favicon",
-        "Add_opengraph":"Add OpenGraph image (Image must be 1200x630 pixels)",
+        "Add_opengraph":"Add OpenGraph image (Image must be at least 1200x630 pixels)",
         "Text_opengraph": "! OpenGraph tags are only generated in pages after an image has been uploaded.",
         "set_marque_client": "Fill in the customer's brand information such as the brand name, the logo ...",
         "DefaultMessageUploaderLogo": "Click here or drag and drop an image",
