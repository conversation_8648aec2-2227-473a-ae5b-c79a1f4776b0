Revision: r13521
Date: 2024-11-26 11:25:52 +0300 (tlt 26 Nov 2024) 
Author: frahajanirina 

## Commit message
Wishlist:IDEO3.2:fix texte warning sur plusieurs bouton dans l'admin

## Files changed

## Full metadata
------------------------------------------------------------------------
r13521 | frahajanirina | 2024-11-26 11:25:52 +0300 (tlt 26 Nov 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js

Wishlist:IDEO3.2:fix texte warning sur plusieurs bouton dans l'admin
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js	(révision 13520)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js	(révision 13521)
@@ -34,7 +34,7 @@
 
                             isDifferentText = this.model.options.ButtonOption.isDifferentText;
                             textOnMobile = this.model.options.ButtonOption.textOnMobile;
-                            txtWarning = $('.txt-warning');
+                            txtWarning = this.$('.txt-warning');
 
                             if (isDifferentText && (textOnMobile == null || textOnMobile == '')) {
                                 txtWarning.show();
@@ -44,7 +44,7 @@
                             }
                         },
                         onInputChange: function(length){
-                            var txtWarning = $('.txt-warning');
+                            var txtWarning = this.$('.txt-warning');
                             if (length === 0) {
                                 txtWarning.show();
                             } else {
@@ -112,9 +112,9 @@
                             var isDifferentText = this.model.options.ButtonOption.isDifferentText;
                             var textOnMobile = this.model.options.ButtonOption.textOnMobile;
                             if (isDifferentText && (textOnMobile == null || textOnMobile == '')) {
-                                $('.txt-warning').show();
+                                this.$('.txt-warning').show();
                             } else {
-                                $('.txt-warning').hide();
+                                this.$('.txt-warning').hide();
                             }
                             return this;
                         },
