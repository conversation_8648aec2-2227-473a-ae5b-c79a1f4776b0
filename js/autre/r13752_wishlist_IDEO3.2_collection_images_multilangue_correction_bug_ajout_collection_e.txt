Revision: r13752
Date: 2025-01-28 15:13:20 +0300 (tlt 28 Jan 2025) 
Author: sraz<PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: collection images multilangue correction bug ajout collection en modal

## Files changed

## Full metadata
------------------------------------------------------------------------
r13752 | srazanandralisoa | 2025-01-28 15:13:20 +0300 (tlt 28 Jan 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/CarrouselOptionTypeLien.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieFieldView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js

wishlist IDEO3.2: collection images multilangue correction bug ajout collection en modal
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/CarrouselOptionTypeLien.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/CarrouselOptionTypeLien.html	(révision 13751)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/CarrouselOptionTypeLien.html	(révision 13752)
@@ -1,5 +1,5 @@
 <p class="panel-legend"><%=__("selectTypeLink")%></p>
-<% var _id= "link_type1" %>
+<% var _id=_.uniqueId('link_type1') %>
 <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkType" value="1"  <%=(TypeLien==1)?"checked":""%> />
 <label  class="block-label" for="<%= _id %>" >
     <div class="radio-wrapper">
@@ -9,7 +9,7 @@
     <div class="block-label-radio"><%= __("LinkImage")%></div>
 
 </label>
-<% var _id= "link_type2" %>
+<% var _id=_.uniqueId('link_type2') %>
 <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkType" value="2"  <%=(TypeLien==2)?"checked":""%>/>
 <label  class="block-label <%=(LinkText)?'carrouselTypeLinkDisable':''%>" for="<%= _id %>" >
     <div class="radio-wrapper">
@@ -19,7 +19,7 @@
     <div class="block-label-radio"><%= __("LinkText")%></div>
 
 </label>
-<% var _id= "link_type3" %>
+<% var  _id=_.uniqueId('link_type3') %>
 <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkType" value="3"  <%=(TypeLien==3)?"checked":""%> />
 <label  class="block-label <%=(BouttonMoreInfo)?'carrouselTypeLinkDisable':''%>" for="<%= _id %>" >
     <div class="radio-wrapper">
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieFieldView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieFieldView.js	(révision 13751)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieFieldView.js	(révision 13752)
@@ -11,8 +11,9 @@
     "JEditor/Commons/Files/Models/FileGroup",
     "JEditor/Commons/Files/Views/CollectionSelectorDialog",
     "JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/CollectionView",
+    "collection!JEditor/Commons/Files/Models/FileGroupCollection",
     "i18n!../nls/i18n"
-],function (_,View, Events, FieldTemplate, GalerieFieldView, FileGroup, CollectionSelectorDialog, CollectionView, translate) {
+],function (_,View, Events, FieldTemplate, GalerieFieldView, FileGroup, CollectionSelectorDialog, CollectionView,FileGroupCollection, translate) {
     
     var GalerieFieldView=View.extend({
         events: {
@@ -40,8 +41,10 @@
             viewAttributes = {title: translate("editMyGrid"), model: fileGroup};
             fileGroupDialog = new CollectionSelectorDialog(viewAttributes);
             this.listenTo(fileGroupDialog, Events.ChoiceEvents.SELECT, function(view, selected) {
-                // 
+                var groupCollection = FileGroupCollection.getInstance();
+                groupCollection.add( selected );
                 this.model.addField( selected );
+               
             });
             this.listenTo(fileGroupDialog, Events.DialogEvents.CLOSE, function() {
                 this.stopListening(fileGroupDialog);
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-ca/i18n.js	(révision 13751)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-ca/i18n.js	(révision 13752)
@@ -53,6 +53,6 @@
    "DescStyle3"           :  "Texte sur l'image" ,
    "addGalerieField": "ajouter une collection", 
    "DescStyle4"           :  "Textes sous l'image avec bordures",
-   "nameNotFound"         :  '<span class="icon-warning" style="color: #d42525;"></span>colletion sans nom',
+   "nameNotFound"         :  '<span class="icon-warning" style="color: #d42525;"></span>collection sans nom',
    "DescStyle5"           :  "Textes sous l'image avec images arrondies"
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js	(révision 13751)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js	(révision 13752)
@@ -54,6 +54,6 @@
    "addGalerieField": "ajouter une collection", 
    "DescStyle4"           :  "Texte sous l'image, bordures",
    "DescStyle5"           :  "Texte sous l'image, images arrondies (idéal images carrés)",
-   "nameNotFound"         :  '<span class="icon-warning" style="color: #d42525;"></span>colletion sans nom',
+   "nameNotFound"         :  '<span class="icon-warning" style="color: #d42525;"></span>collection sans nom',
    "DescStyle6"           :  "Texte à côté de l'image"
 });
\ No newline at end of file
