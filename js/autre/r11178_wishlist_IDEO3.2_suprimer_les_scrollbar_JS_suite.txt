Revision: r11178
Date: 2023-08-02 09:48:55 +0300 (lrb 02 Aog 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: suprimer les scrollbar JS (suite)

## Files changed

## Full metadata
------------------------------------------------------------------------
r11178 | srazanandralisoa | 2023-08-02 09:48:55 +0300 (lrb 02 Aog 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/less/main.less

wishlist IDEO3.2: suprimer les scrollbar JS (suite)
------------------------------------------------------------------------

## Diff
Index: src/less/main.less
===================================================================
--- src/less/main.less	(révision 11177)
+++ src/less/main.less	(révision 11178)
@@ -794,6 +794,7 @@
 }
 .scrollbar-classic{
   overflow: auto;
+  overscroll-behavior-y: contain;
  }
 .pageList, .add-page-dialog {
   .mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
