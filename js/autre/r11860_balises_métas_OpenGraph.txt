Revision: r11860
Date: 2024-02-07 14:02:57 +0300 (lrb 07 Feb 2024) 
Author: rrakotoarinelina 

## Commit message
balises métas OpenGraph

## Files changed

## Full metadata
------------------------------------------------------------------------
r11860 | rrakotoarinelina | 2024-02-07 14:02:57 +0300 (lrb 07 Feb 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Models/Params.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Templates/MarqueClient.html
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/MarqueClientView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/params_panel/module/inline-social.less

balises métas OpenGraph
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js
===================================================================
--- src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 11859)
+++ src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 11860)
@@ -74,6 +74,7 @@
                 uploadUrl: __IDEO_UPLOAD_URL__ ? __IDEO_UPLOAD_URL__ : '/admin/resources',
                 customStockEvent: '_parsestock_all',
                 isFavicon:false,
+                isOpengraph:false,
                 onUploadStart: function() {
                 },
                 onUploadFail: function() {
@@ -102,7 +103,8 @@
                     uploadFailErrorsOccured: 'Des erreurs ont eu lieu pendant le transfert de fichier',
                     uploadSuccess: 'Le transfert des fichiers s\'est déroulé avec succès',
                     uploadFailServerError: 'Impossible de transférer le fichier %name% à cause de l\'erreur: "%error%"',
-                    uploadFaviconCorrupted: 'Le format SVG n\'est sont pas autorisés pour les favicons'
+                    uploadFaviconCorrupted: 'Le format SVG n\'est sont pas autorisés pour les favicons',
+                    uploadOpenGraphFail: 'Echec du chargement. L\'image doit être de 1200x630 pixels',
                 }
             };
             this.options.lang = $.extend({}, settings.lang, this.options.lang ? this.options.lang : {});
@@ -384,7 +386,25 @@
                                     }
                                 }
 
+                                   //check pour opengrah
+                                if(uploader.options.isOpengraph){
+                                    //prendre les dimensions de l'image
+                                    var imageUrl = uploadInfos.code; 
+                                    var img = new Image();
+                                    img.src = imageUrl;
+                                    img.onload = function() {
+                                        if (img.width != 1200 || img.height != 630) {
+                                            message = uploader.options.lang.uploadOpenGraphFail;
+                                            uploader.errors.push(message);
+                                            files.rejected++;
+                                            if (files.length === 1)
+                                                uploader._onComplete();
+                                            
+                                        }
+                                    }
+                                }
 
+
                             }
                             else {
                                 realPreview.addClass('filepreview');
Index: src/js/JEditor/ParamsPanel/Models/Params.js
===================================================================
--- src/js/JEditor/ParamsPanel/Models/Params.js	(révision 11859)
+++ src/js/JEditor/ParamsPanel/Models/Params.js	(révision 11860)
@@ -70,6 +70,7 @@
             Logo: null,
             LogoSmall: null,
             Favicon: null,
+            Opengraph:null,
             IconsCollection: "outline"
         },
         constructor: function () {
Index: src/js/JEditor/ParamsPanel/Templates/MarqueClient.html
===================================================================
--- src/js/JEditor/ParamsPanel/Templates/MarqueClient.html	(révision 11859)
+++ src/js/JEditor/ParamsPanel/Templates/MarqueClient.html	(révision 11860)
@@ -72,6 +72,30 @@
                     </div>
                 </div>
             </div>
+
+            <!--  ajout de open graph  -->
+
+            <div class="my-files fileList">
+                <div class="content scroll-container">
+                    <div class="inline-params thin-border radius shadow">
+                        <div class="inline-logo">
+                            <div class="group-content-opengraph relative">
+                                <span class="inline-params__nameOpengraph">
+                                    <%=__("Add_opengraph")%>
+                                </span>
+                                <!-- <span class="rigth-delete-image icon-bin" data-name="Opengraph" style="top: 32%;right: 17%;"> </span> -->
+
+                                <span class="rigth-delete-image icon-bin" data-name="Opengraph" style="top: 16%; right: 5%;"> </span>
+
+                                <div class="menu-wrapper-opengraph file image">
+                                    <header></header>
+                                </div>
+                            </div>    
+                        </div>
+                    </div>
+                </div>
+            </div>
+
             <!-- 
             ******************
             end new childView
Index: src/js/JEditor/ParamsPanel/Views/MarqueClientView.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 11859)
+++ src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 11860)
@@ -28,6 +28,7 @@
         currentFile: null,
         currentFile2: null,
         currentFileFavicon: null,
+        currentFileOpengraph: null,
         tagName: "div",
         className: "panel-content image-panel image",
         events: {
@@ -35,6 +36,7 @@
             'click .menu-wrapper-logo ' : '_onlyComputerLogo',
             'click .menu-wrapper-logoSmall ' : '_onlyComputerLogoSmall',
             'click .menu-wrapper-favicon ' : '_onlyComputerFavicon',
+            'click .menu-wrapper-opengraph ' : '_onlyComputerOpengraph',
             'click .rigth-delete-image[data-name]': 'deleteOne',
             'click .shortcode': 'copyToClipboard'
         },
@@ -46,6 +48,7 @@
             this.currentFileLogo = null;
             this.currentFileLogoSmall = null;
             this.currentFileFavicon = null;
+            this.currentFileOpengraph = null;
             this._template = this.buildTemplate(template,translate);
             this.fileCollection = new FileCollection();
             this.translations = translate.translations;
@@ -62,6 +65,9 @@
         _onUpload3: function(file) {
             this.upload(file,'Favicon');
         },
+        _onUpload4: function(file) {
+            this.upload(file,'Opengraph');
+        },
 
         upload: function(file,fileType){
             
@@ -71,6 +77,7 @@
             this.model.set('Logo', this.currentFileLogo);
             this.model.set('LogoSmall', this.currentFileLogoSmall);
             this.model.set('Favicon', this.currentFileFavicon);
+            this.model.set('Opengraph', this.currentFileOpengraph);
             this.model.save();
             var classSuffix = fileType.charAt(0).toLowerCase() + fileType.slice(1);
             var deleteClassSuffix = classSuffix;
@@ -113,6 +120,9 @@
         _onlyComputerFavicon: function(e) {
             this.onlyComputer(e,'favicon');
         },
+        _onlyComputerOpengraph: function(e) {
+            this.onlyComputer(e,'opengraph');
+        },
 
         onlyComputer : function(e,fileType){
             e.preventDefault();
@@ -178,6 +188,7 @@
             this.currentFileLogo = this.setCurrentFile(this.currentFileLogo, attrs.Logo);
             this.currentFileLogoSmall = this.setCurrentFile(this.currentFileLogoSmall, attrs.LogoSmall);
             this.currentFileFavicon = this.setCurrentFile(this.currentFileFavicon, attrs.Favicon);
+            this.currentFileOpengraph = this.setCurrentFile(this.currentFileOpengraph, attrs.Opengraph);
 
             //logo
             this.fileUploader = this.uploadFile(this.currentFileLogo, uploadParams);
@@ -192,11 +203,18 @@
                 isFavicon : true
               });
 
+            //Opengraph
+            this.fileUploader4 = this.uploadFile(this.currentFileOpengraph, {
+                ...uploadParams,
+                acceptedExtensions: ['jpeg', 'jpg', 'png'],
+                isOpengraph: true
+            });
 
 
             this.listenTo(this.fileUploader, Events.FileUploaderEvents.UPLOAD, this._onUpload);
             this.listenTo(this.fileUploader2, Events.FileUploaderEvents.UPLOAD, this._onUpload2);
             this.listenTo(this.fileUploader3, Events.FileUploaderEvents.UPLOAD, this._onUpload3);
+            this.listenTo(this.fileUploader4, Events.FileUploaderEvents.UPLOAD, this._onUpload4);
 
             // en cas echec d'upload on re-render les marqueClients
             // this.listenTo(this.fileUploader, Events.FileUploaderEvents.TOOBIG, this.render);
@@ -203,13 +221,16 @@
             this.listenTo(this.fileUploader, Events.FileUploaderEvents.TOOBIG, this.onError);
             this.listenTo(this.fileUploader2, Events.FileUploaderEvents.TOOBIG, this.onError);
             this.listenTo(this.fileUploader3, Events.FileUploaderEvents.TOOBIG, this.onError);
+            this.listenTo(this.fileUploader4, Events.FileUploaderEvents.TOOBIG, this.onError);
 
             this.$('.inline-logo .group-content-logo header').after(this.fileUploader.el);
             this.$('.inline-logo .group-content-logo-small header').after(this.fileUploader2.el);
             this.$('.inline-logo .group-content-favicon header').after(this.fileUploader3.el);
+            this.$('.inline-logo .group-content-opengraph header').after(this.fileUploader4.el);
 			this.fileUploader.render();
             this.fileUploader2.render();
             this.fileUploader3.render();
+            this.fileUploader4.render();
             if(attrs.Logo && (!Array.isArray(attrs.Logo))){
                 this.proccessAttribute('Logo');            
             }
@@ -219,10 +240,14 @@
             if(attrs.Favicon && (!Array.isArray(attrs.Favicon))){
                 this.proccessAttribute('Favicon');     
             }
+             if(attrs.Opengraph && (!Array.isArray(attrs.Opengraph))){
+                this.proccessAttribute('Opengraph');     
+            }
 
             this.$('.menu-wrapper-logo .uploader .preview .imagepreview').after('<span style="font-size:9px;display:block;margin-top:35px;color: #666;text-align:center;">'+this.translations.DefaultMessageUploaderLogo+'</span>');
             this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview').after('<span style="font-size:9px;display:block;margin-top:35px;color: #666;text-align:center;">'+this.translations.DefaultMessageUploaderLogo+'</span>');
             this.$('.menu-wrapper-favicon .uploader .preview .imagepreview').after('<span style="font-size:9px;display:block;margin-top:35px;color: #666;text-align:center;">'+this.translations.DefaultMessageUploaderLogo+'</span>');
+            this.$('.menu-wrapper-opengraph .uploader .preview .imagepreview').after('<span style="font-size:15px;display:block;margin-top:110px;color: #666;text-align:center;">'+this.translations.DefaultMessageUploaderLogo+'</span>');
             return this;
         },
         onError: function(data){
Index: src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 11859)
+++ src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 11860)
@@ -68,6 +68,7 @@
         "Add_logo": "Ajouter le logo",
         "Add_logo_small": "Ajouter le logo \"small\"",
         "Add_favicon": "Ajouter le favicon",
+        "Add_opengraph":"Ajouter image OpenGraph (L'image doit être de 1200x630 pixels)",
         "set_marque_client": "Renseignez les informations de Marque du client tel que l'enseigne, le logo ...",
         "DefaultMessageUploaderLogo": "Cliquez ici ou glissez-déposez une image",
         "copy":"Copié", 
Index: src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 11859)
+++ src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 11860)
@@ -72,6 +72,7 @@
         "Add_logo": "Ajouter le logo",
         "Add_logo_small": "Ajouter le logo \"small\"",
         "Add_favicon": "Ajouter le favicon",
+        "Add_opengraph":"Ajouter image OpenGraph (L'image doit être de 1200x630 pixels)",
         "set_marque_client": "Renseignez les informations de Marque du client tel que l'enseigne, le logo ...",
         "DefaultMessageUploaderLogo": "Cliquez ici ou glissez-déposez une image",
         "copy":"Copié", 
Index: src/js/JEditor/ParamsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 11859)
+++ src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 11860)
@@ -80,6 +80,7 @@
         "Add_logo": "Add logo",
         "Add_logo_small": "Add logo \"small\"",
         "Add_favicon": "Add favicon",
+        "Add_opengraph":"Add OpenGraph image (Image must be 1200x630 pixels)",
         "set_marque_client": "Fill in the customer's brand information such as the brand name, the logo ...",
         "DefaultMessageUploaderLogo": "Click here or drag and drop an image",
         "copy":"Copied", 
Index: src/less/imports/params_panel/module/inline-social.less
===================================================================
--- src/less/imports/params_panel/module/inline-social.less	(révision 11859)
+++ src/less/imports/params_panel/module/inline-social.less	(révision 11860)
@@ -230,4 +230,63 @@
         -ms-transform: scale(1.3);
         -o-transform: scale(1.3);
         transform: scale(1.3);
-    }
\ No newline at end of file
+    }
+
+
+    
+    .menu-wrapper-opengraph {
+        width : 500px;
+        -webkit-transform: scale(1);
+        -moz-transform: scale(1);
+        -ms-transform: scale(1);
+        -o-transform: scale(1);
+        transform: scale(1);
+        cursor: pointer;
+    }
+    
+
+    .inline-params__nameOpengraph{
+        margin-left: 55px;
+        font-size: 1.2em;
+    }
+
+
+    .group-content-opengraph {
+        margin-bottom: 50px;
+        margin-right: 50px;
+
+    }
+
+
+    .group-content-opengraph .uploader{
+        width : 500px;
+        line-height: 10px;
+        margin-left: 50px;
+        margin-right: 50px;
+        border: 1px solid #666;
+        background-color: #ffffff;
+        height: 270px;
+        content: 'cliquez ici ou glissez-déposez une image';
+    }
+ 
+
+    .group-content-opengraph .uploader .preview:after{
+        top:35%;
+        width: 80px;
+        left: 55%;
+        transform: scale(2); 
+    }
+
+    .group-content-opengraph .uploader .preview{
+        width: 380px;
+        margin-left: 60px;
+        height: 200px;
+    }
+
+
+    .my-files .group-content-opengraph .preview .imagepreview{
+        width: 380px;
+        height: 200px;
+    }
+
+
