Revision: r13726
Date: 2025-01-20 14:50:25 +0300 (lts 20 Jan 2025) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
Wishlist IDEO3.2: option changer le texte des boutons carousel et grille de photo(partie js)

## Files changed

## Full metadata
------------------------------------------------------------------------
r13726 | srazanandralisoa | 2025-01-20 14:50:25 +0300 (lts 20 Jan 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/CarrouselOption.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/CarrouselOptionTypeLien.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/OptionTypeLienView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieOption.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieOptionTypeLien.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieOptionTypeLienView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Models/GridOption.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridOptionTypeLien.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridOptionTypeLienView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/i18n.js

Wishlist IDEO3.2: option changer le texte des boutons carousel et grille de photo(partie js)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/CarrouselOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/CarrouselOption.js	(révision 13725)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/CarrouselOption.js	(révision 13726)
@@ -2,11 +2,13 @@
     "JEditor/Commons/Events",
     "JEditor/PagePanel/Contents/Options/Models/AbstractOption",
     "collection!JEditor/Commons/Files/Models/FileGroupCollection",
-    "JEditor/Commons/Files/Models/FileGroup"
+    "JEditor/Commons/Files/Models/FileGroup",
+    "i18n!../nls/i18n",
 ], function(Events,
         AbstractOption,
         FileGroupCollection,
-        FileGroup
+        FileGroup,
+        translate
         ) {
     var fileGroupCollection = FileGroupCollection.getInstance();
     /**
@@ -29,6 +31,7 @@
                             Info        :   0,
                             Action      :   0,
                             TypeLien    :   1,
+                            ButtonText : translate('ButtonText'),
                             Autoplay : false,
                             Duration: 5000,
                             CentreEcran: false 
@@ -63,6 +66,7 @@
                             var typelien =this.getTypeLien();
                             var autoplay =this.getAutoplay();
                             var duration =this.getDuration();
+                            var buttonText = this.getButtonText();
                             return {
                                 fileGroup: fileGroup?fileGroup.id:null,
                                 Arrow: arrow,
@@ -72,6 +76,7 @@
                                 Duration : duration,
                                 Autoplay : autoplay,
                                 optionType: 'carrousel',
+                                ButtonText : buttonText,
                                 CentreEcran: this.CentreEcran
                             }
                         },
@@ -80,7 +85,7 @@
                         }
                     }
             );
-            CarrouselOption.SetAttributes(['fileGroup', 'Arrow','Info','Action','TypeLien', 'Autoplay', 'Duration' , 'CentreEcran' ]);
+            CarrouselOption.SetAttributes(['fileGroup', 'Arrow','Info','Action','TypeLien', 'Autoplay', 'Duration' , 'CentreEcran' , 'ButtonText' ]);
 
             return CarrouselOption;
         });
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/CarrouselOptionTypeLien.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/CarrouselOptionTypeLien.html	(révision 13725)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/CarrouselOptionTypeLien.html	(révision 13726)
@@ -28,4 +28,8 @@
     </div>
     <div class="block-label-radio"><%= __("ButtonReadMore")%></div>
 
-</label>
\ No newline at end of file
+</label>
+<div class="button-text">
+    <p class="panel-content-legend"><%= __("buttonTextLegend")%></p>
+    <input class="buttonText-input" name="ButtonText" value="<%=ButtonText%>" />
+</div>
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/OptionTypeLienView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/OptionTypeLienView.js	(révision 13725)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/OptionTypeLienView.js	(révision 13726)
@@ -8,6 +8,8 @@
 ], function($, CarrouselOptionTypeLien, Events, View, translate) {
     var OptionTypeLienView = View.extend({
         events: {
+            'change input[name="LinkType"]': '_onLinkTypeChnage',
+            'change input[name="ButtonText"]': '_onButtonTextChnage',
         },
         initialize: function() {
             this._super();
@@ -19,6 +21,21 @@
 
            
         },
+        _onButtonTextChnage : function (event){
+            var $target = $(event.currentTarget);
+            this.options.ButtonText = $target.val();
+        },
+        hideOrShowButton: function (){
+            if(this.options.TypeLien == 3){
+                this.$('.button-text').show();
+             }
+             else this.$('.button-text').hide();
+        },
+        _onLinkTypeChnage: function(event){
+            var $target = $(event.currentTarget);
+            this.options.TypeLien = $target.val();
+            this.hideOrShowButton();
+        },
         render: function() {
             this.BouttonMoreInfo=false;
             this.LinkText=false;
@@ -43,8 +60,8 @@
                 
             this.undelegateEvents();
             this.$el.empty();
-            this.$el.html(this._template({TypeLien: TypeLien,LinkText:this.LinkText,BouttonMoreInfo:this.BouttonMoreInfo}));
-
+            this.$el.html(this._template({TypeLien: TypeLien,LinkText:this.LinkText,ButtonText:this.options.ButtonText,BouttonMoreInfo:this.BouttonMoreInfo}));
+            this.hideOrShowButton();
             this.delegateEvents();
             return this;
         },
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js	(révision 13725)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js	(révision 13726)
@@ -35,7 +35,9 @@
     "selectTypeLink"        :   "Selectionnez le type de lien souhaitée",
     "LinkImage"             :   "Ajouter le lien sur l'image",
     "LinkText"              :   "Ajouter le lien sur le texte",
-    "ButtonReadMore"        :   "Ajouter un bouton 'Consulter la page'",
+    "ButtonReadMore"        :   "Ajouter le lien sur un bouton",
+    "ButtonText"            :   "Consulter la page",
+    "buttonTextLegend"      :   "Texte du bouton",
     "carrouselHeight"       :   "Nombre d'image",
     "carrouselHeightDesc"   :   "Glissez pour ajuster le nombre d'images affichées",
     "carrouselStyleAffichage"   :   "Style des images",
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieOption.js	(révision 13725)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieOption.js	(révision 13726)
@@ -2,9 +2,11 @@
     "JEditor/Commons/Events",
     "JEditor/PagePanel/Contents/Options/Models/AbstractOption",
     "JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/Galerie",
+    "i18n!../nls/i18n",
 ], function(Events,
         AbstractOption,
-        Galerie
+        Galerie,
+        translate
         ) {
     /**
      * @class GalerieOption
@@ -30,6 +32,7 @@
                             galerieInfo:0, 
                             galerieAction: 0,
                             galerieTypeLink :1,
+                            ButtonText : translate('ButtonText'),
                         },
                         initialize: function() {
                             this._super();
@@ -39,6 +42,6 @@
                         },
                     }
             );
-            GalerieOption.SetAttributes([ 'galerie', 'galerieInfo', 'galerieAction','galerieTypeLink']);
+            GalerieOption.SetAttributes([ 'galerie', 'galerieInfo', 'galerieAction','galerieTypeLink','ButtonText']);
             return GalerieOption;
         });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieOptionTypeLien.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieOptionTypeLien.html	(révision 13725)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieOptionTypeLien.html	(révision 13726)
@@ -28,4 +28,8 @@
     </div>
     <div class="block-label-radio"><%= __("ButtonReadMore")%></div>
 
-</label>
\ No newline at end of file
+</label>
+<div class="button-text">
+    <p class="panel-content-legend"><%= __("buttonTextLegend")%></p>
+    <input class="buttonText-input" name="ButtonText" value="<%=ButtonText%>" />
+</div>
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieOptionTypeLienView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieOptionTypeLienView.js	(révision 13725)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieOptionTypeLienView.js	(révision 13726)
@@ -12,6 +12,8 @@
      */
     var galerieOptionTypeLienView = View.extend({
         events: {
+            'change input[name="LinkType"]': '_onLinkTypeChnage',
+            'change input[name="ButtonText"]': '_onButtonTextChnage',
         },
         initialize: function() {
             this._super();
@@ -23,6 +25,21 @@
 
            
         },
+        _onButtonTextChnage : function (event){
+            var $target = $(event.currentTarget);
+            this.options.ButtonText = $target.val();
+        },
+        hideOrShowButton: function (){
+            if(this.options.galerieTypeLink == 3){
+                this.$('.button-text').show();
+                }
+                else this.$('.button-text').hide();
+        },
+        _onLinkTypeChnage: function(event){
+            var $target = $(event.currentTarget);
+            this.options.galerieTypeLink = $target.val();
+            this.hideOrShowButton();
+        },
         render: function() {
             this.BouttonMoreInfo=false;
             this.LinkText=false;
@@ -45,8 +62,8 @@
                 
             this.undelegateEvents();
             this.$el.empty();
-            this.$el.html(this._template({TypeLien: TypeLien,LinkText:this.LinkText,BouttonMoreInfo:this.BouttonMoreInfo}));
-
+            this.$el.html(this._template({TypeLien: TypeLien,LinkText:this.LinkText,ButtonText:this.options.ButtonText,BouttonMoreInfo:this.BouttonMoreInfo}));
+            this.hideOrShowButton();
             this.delegateEvents();
             return this;
         },
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-ca/i18n.js	(révision 13725)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-ca/i18n.js	(révision 13726)
@@ -41,7 +41,9 @@
    "selectTypeLink"        :   "Selectionnez le type de lien souhaitée",
    "LinkImage"             :   "Ajouter le lien sur l'image",
    "LinkText"              :   "Ajouter le lien sur le texte",
-   "ButtonReadMore"        :   "Ajouter un bouton 'en savoir plus'",
+   "ButtonReadMore"        :   "Ajouter le lien sur un bouton",
+   "ButtonText"            :   "en savoir plus",
+   "buttonTextLegend"      :   "Texte du bouton",
    'landscape'             :   "Paysage",
    'portrait'              :   "Portrait",
    'square'                :   "Carré",
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js	(révision 13725)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js	(révision 13726)
@@ -41,7 +41,9 @@
    "selectTypeLink"        :   "Selectionnez le type de lien souhaitée",
    "LinkImage"             :   "Ajouter le lien sur l'image",
    "LinkText"              :   "Ajouter le lien sur le texte",
-   "ButtonReadMore"        :   "Ajouter un bouton 'en savoir plus'",
+   "ButtonReadMore"        :   "Ajouter le lien sur un bouton",
+   "ButtonText"            :   "en savoir plus",
+   "buttonTextLegend"      :   "Texte du bouton",
    'landscape'             :   "Paysage",
    'portrait'              :   "Portrait",
    'square'                :   "Carré",
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/i18n.js	(révision 13725)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/i18n.js	(révision 13726)
@@ -46,7 +46,9 @@
       "selectTypeLink"        :   "Select the type of link you want",
       "LinkImage"             :   "Add link to image",
       "LinkText"              :   "Add link to text",
-      "ButtonReadMore"        :   "Add a button 'read more'",
+      "ButtonReadMore"        :   "Add link to button",
+      "ButtonText"            :   "Read More",
+      "buttonTextLegend"      :   "Button text",
       "galerieOption"            :       "Grid",
       "galerieBlockOption"       :       "Grid options",
       "galerieStyleOption"       :       "Style",
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Models/GridOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Models/GridOption.js	(révision 13725)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Models/GridOption.js	(révision 13726)
@@ -4,11 +4,13 @@
     "collection!JEditor/Commons/Files/Models/FileGroupCollection",
     //"collection!./GalleryDesignCollection",
     //"./GalleryDesign",
-    "JEditor/Commons/Files/Models/FileGroup"
+    "JEditor/Commons/Files/Models/FileGroup",
+    "i18n!../nls/i18n",
 ], function(Events,
         AbstractOption,
         FileGroupCollection,
-        FileGroup
+        FileGroup,
+        translate
         ) {
     var fileGroupCollection = FileGroupCollection.getInstance()
     var allowedCaptionType = ["caption-visible","caption-over","caption-hidden"];
@@ -32,7 +34,8 @@
                             grilleInfo:0, 
                             grilleAction: 0,
                             grilleTypeLink :1,
-                            fileGroupId: null
+                            fileGroupId: null,
+                            ButtonText : translate('ButtonText'),
                         },
                         initialize: function() {
                             this._super();
@@ -65,6 +68,6 @@
                         // }
                     }
             );
-            GridOption.SetAttributes(['fileGroup', 'grilleInfo', 'grilleAction','grilleTypeLink','fileGroupId']);
+            GridOption.SetAttributes(['fileGroup', 'grilleInfo', 'grilleAction','grilleTypeLink','fileGroupId','ButtonText']);
             return GridOption;
         });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridOptionTypeLien.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridOptionTypeLien.html	(révision 13725)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridOptionTypeLien.html	(révision 13726)
@@ -28,4 +28,8 @@
     </div>
     <div class="block-label-radio"><%= __("ButtonReadMore")%></div>
 
-</label>
\ No newline at end of file
+</label>
+<div class="button-text">
+    <p class="panel-content-legend"><%= __("buttonTextLegend")%></p>
+    <input class="buttonText-input" name="ButtonText" value="<%=ButtonText%>" />
+</div>
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridOptionTypeLienView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridOptionTypeLienView.js	(révision 13725)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridOptionTypeLienView.js	(révision 13726)
@@ -8,6 +8,8 @@
 ], function($, gridOptionTypeLien, Events, View, translate) {
     var gridOptionTypeLienView = View.extend({
         events: {
+            'change input[name="LinkType"]': '_onLinkTypeChnage',
+            'change input[name="ButtonText"]': '_onButtonTextChnage',
         },
         initialize: function() {
             this._super();
@@ -19,6 +21,21 @@
 
            
         },
+        _onButtonTextChnage : function (event){
+            var $target = $(event.currentTarget);
+            this.options.ButtonText = $target.val();
+        },
+        hideOrShowButton: function (){
+            if(this.options.grilleTypeLink == 3){
+                this.$('.button-text').show();
+                }
+                else this.$('.button-text').hide();
+        },
+        _onLinkTypeChnage: function(event){
+            var $target = $(event.currentTarget);
+            this.options.grilleTypeLink = $target.val();
+            this.hideOrShowButton();
+        },
         render: function() {
             this.BouttonMoreInfo=false;
             this.LinkText=false;
@@ -41,8 +58,8 @@
                 
             this.undelegateEvents();
             this.$el.empty();
-            this.$el.html(this._template({TypeLien: TypeLien,LinkText:this.LinkText,BouttonMoreInfo:this.BouttonMoreInfo}));
-
+            this.$el.html(this._template({TypeLien: TypeLien,LinkText:this.LinkText,ButtonText:this.options.ButtonText,BouttonMoreInfo:this.BouttonMoreInfo}));
+            this.hideOrShowButton();
             this.delegateEvents();
             return this;
         },
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-ca/i18n.js	(révision 13725)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-ca/i18n.js	(révision 13726)
@@ -41,7 +41,9 @@
         "selectTypeLink"        :   "Selectionnez le type de lien souhaitée",
         "LinkImage"             :   "Ajouter le lien sur l'image",
         "LinkText"              :   "Ajouter le lien sur le texte",
-        "ButtonReadMore"        :   "Ajouter un bouton 'Consulter la page'",
+        "ButtonReadMore"        :   "Ajouter le lien sur un bouton",
+        "ButtonText"            :   "Consulter la page",
+        "buttonTextLegend"      :   "Texte du bouton",
         'landscape'             :   "Paysage",
         'portrait'              :   "Portrait",
         'square'                :   "Carré",
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-fr/i18n.js	(révision 13725)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-fr/i18n.js	(révision 13726)
@@ -41,7 +41,9 @@
         "selectTypeLink"        :   "Selectionnez le type de lien souhaitée",
         "LinkImage"             :   "Ajouter le lien sur l'image",
         "LinkText"              :   "Ajouter le lien sur le texte",
-        "ButtonReadMore"        :   "Ajouter un bouton 'Consulter la page'",
+        "ButtonReadMore"        :   "Ajouter le lien sur un bouton",
+        "ButtonText"            :   "Consulter la page",
+        "buttonTextLegend"      :   "Texte du bouton",
         'landscape'             :   "Paysage",
         'portrait'              :   "Portrait",
         'square'                :   "Carré",
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/i18n.js	(révision 13725)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/i18n.js	(révision 13726)
@@ -46,7 +46,9 @@
         "selectTypeLink"        :   "Select the type of link you want",
         "LinkImage"             :   "Add link to image",
         "LinkText"              :   "Add link to text",
-        "ButtonReadMore"        :   "Add a button 'Visit the page'",
+        "ButtonReadMore"        :   "Add link to button",
+        "ButtonText"            :   "Visit the page",
+        "buttonTextLegend"      :   "Button text",
         "gridOption"            :       "Grid",
         "gridBlockOption"       :       "Grid options",
         "gridStyleOption"       :       "Style",
