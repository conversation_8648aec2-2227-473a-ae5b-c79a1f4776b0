Revision: r13096
Date: 2024-09-23 11:48:07 +0300 (lts 23 Sep 2024) 
Author: traj<PERSON><PERSON><PERSON><PERSON> 

## Commit message
Whislist : model carroussel 

## Files changed

## Full metadata
------------------------------------------------------------------------
r13096 | trajao<PERSON><PERSON>lo | 2024-09-23 11:48:07 +0300 (lts 23 Sep 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/CarrouselOption.js

Whislist : model carroussel 
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/CarrouselOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/CarrouselOption.js	(révision 13095)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/CarrouselOption.js	(révision 13096)
@@ -31,6 +31,7 @@
                             TypeLien    :   1,
                             Autoplay : false,
                             Duration: 5000,
+                            CentreEcran: false 
                         },
                         initialize: function() {
                             this._super();
@@ -70,7 +71,8 @@
                                 Action: action,
                                 Duration : duration,
                                 Autoplay : autoplay,
-                                optionType: 'carrousel'
+                                optionType: 'carrousel',
+                                CentreEcran: this.CentreEcran
                             }
                         },
                         JSONClone:function(){
@@ -78,7 +80,7 @@
                         }
                     }
             );
-            CarrouselOption.SetAttributes(['fileGroup', 'Arrow','Info','Action','TypeLien', 'Autoplay', 'Duration']);
+            CarrouselOption.SetAttributes(['fileGroup', 'Arrow','Info','Action','TypeLien', 'Autoplay', 'Duration' , 'CentreEcran' ]);
 
             return CarrouselOption;
         });
