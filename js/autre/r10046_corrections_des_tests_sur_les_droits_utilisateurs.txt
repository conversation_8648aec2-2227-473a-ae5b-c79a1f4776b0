Revision: r10046
Date: 2022-12-22 11:57:22 +0300 (lkm 22 Des 2022) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
corrections des tests sur les droits utilisateurs

## Files changed

## Full metadata
------------------------------------------------------------------------
r10046 | srazana<PERSON>lisoa | 2022-12-22 11:57:22 +0300 (lkm 22 Des 2022) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/assets/ACLs/admin.json
   M /branches/ideo3_v2/integration/assets/ACLs/root.json
   M /branches/ideo3_v2/integration/assets/ACLs/superadmin.json
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Ancestors/Views/ListView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Pages/Templates/pageSelectorWrapper.html
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Pages/Views/PageSelectorWrapper.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/collectionList.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/collectionManagerUIView.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileDetailManager.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileManagerUIView.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/CollectionListView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileDetailManagerView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileListView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileManagerUIView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/PagePanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Templates/pageList.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Templates/pageView.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Views/PageCollectionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Views/PageView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/ParamsPanel.js

corrections des tests sur les droits utilisateurs
------------------------------------------------------------------------

## Diff
Index: assets/ACLs/admin.json
===================================================================
--- assets/ACLs/admin.json	(révision 10045)
+++ assets/ACLs/admin.json	(révision 10046)
@@ -151,8 +151,32 @@
      "value":false,
      "comparison":null
  },
- "access_lp_page":{
+ "access_lpsupport_page":{
     "value":false,
     "comparison":null
+ },
+ "delete_file": {
+    "value": false,
+    "comparison": null
+ },
+ "view_jsonLd": {
+    "value": false,
+    "comparison": null
+ },
+ "config_page":{
+    "value": false,
+    "comparison": null
+ },
+ "export_page":{
+    "value": false,
+    "comparison": null
+ },
+ "export_contenu_page":{
+    "value": false,
+    "comparison": null
+ },
+ "homeless_msg":{
+    "value": false,
+    "comparison": null
  }
 }
Index: assets/ACLs/root.json
===================================================================
--- assets/ACLs/root.json	(révision 10045)
+++ assets/ACLs/root.json	(révision 10046)
@@ -151,8 +151,32 @@
         "value":true,
         "comparison":null
     },
-    "access_lp_page":{
+    "access_lpsupport_page":{
         "value":true,
         "comparison":null
-     }
+    },
+     "delete_file": {
+        "value": true,
+        "comparison": null
+    },
+     "view_jsonLd": {
+        "value": true,
+        "comparison": null
+    }, 
+    "config_page":{
+        "value": true,
+        "comparison": null
+    },
+    "export_page":{
+        "value": true,
+        "comparison": null
+    },
+    "export_contenu_page":{
+        "value": true,
+        "comparison": null
+    },
+    "homeless_msg":{
+        "value": true,
+        "comparison": null
+    }
 }
Index: assets/ACLs/superadmin.json
===================================================================
--- assets/ACLs/superadmin.json	(révision 10045)
+++ assets/ACLs/superadmin.json	(révision 10046)
@@ -151,8 +151,32 @@
      "value":true,
      "comparison":null
  },
- "access_lp_page":{
+ "access_lpsupport_page":{
     "value":true,
     "comparison":null
+ },
+ "delete_file": {
+    "value": true,
+    "comparison": null
+ },
+ "view_jsonLd": {
+    "value": false,
+    "comparison": null
+ },
+ "config_page":{
+    "value": true,
+    "comparison": null
+ },
+ "export_page":{
+    "value": true,
+    "comparison": null
+ },
+ "export_contenu_page":{
+    "value": false,
+    "comparison": null
+ },
+ "homeless_msg":{
+    "value": true,
+    "comparison": null
  }
 }
Index: src/js/JEditor/Commons/Ancestors/Views/ListView.js
===================================================================
--- src/js/JEditor/Commons/Ancestors/Views/ListView.js	(révision 10045)
+++ src/js/JEditor/Commons/Ancestors/Views/ListView.js	(révision 10046)
@@ -271,7 +271,7 @@
             } else {
                 list = this.sortList(list);
                 if (list.length > 0) {
-                    var params = _.extend({}, {content: list, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected,role:this.app.user.role}, this.addTemplateParams(this.collection, list));
+                    var params = _.extend({}, {content: list, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected, user: this.app.user}, this.addTemplateParams(this.collection, list));
                     this.$el.html(this._template(params));
                 }
                 else {
Index: src/js/JEditor/Commons/Pages/Templates/pageSelectorWrapper.html
===================================================================
--- src/js/JEditor/Commons/Pages/Templates/pageSelectorWrapper.html	(révision 10045)
+++ src/js/JEditor/Commons/Pages/Templates/pageSelectorWrapper.html	(révision 10046)
@@ -6,7 +6,7 @@
         <% var _id=_.uniqueId('display_disabled') %>
         <% var _id2=_.uniqueId('display_disabled') %>
         <input type="checkbox" id="<%=_id%>" value="showhiddenpage" name="box"/><label for="<%=_id%>" class="label"><span class="checkbox"><span class="icon-checked"></span><span class="icon-unchecked"></span></span><%= __("disabledDisplayed") %></label>
-        <% if((role =="root" || role =="superadmin") && storage ) {%>
+        <% if(user.can("access_lpsupport_page") && storage ) {%>
             <input type="checkbox" id="<%=_id2%>" value="showlp" name="box"/><label for="<%=_id2%>" class="label"><span class="checkbox"><span class="icon-checked"></span><span class="icon-unchecked"></span></span><%= __("OnlyShowLPSupport") %></label>
          <%}%>
     </header>
Index: src/js/JEditor/Commons/Pages/Views/PageSelectorWrapper.js
===================================================================
--- src/js/JEditor/Commons/Pages/Views/PageSelectorWrapper.js	(révision 10045)
+++ src/js/JEditor/Commons/Pages/Views/PageSelectorWrapper.js	(révision 10046)
@@ -69,7 +69,7 @@
                     this.childViews.pageSelector.filter({type:"LPsupport"});
                 }
             }else{
-                if(this.role=="root" || this.role=="superadmin"){
+                if(this.app.user.can("access_lpsupport_page")){
                     this.childViews.pageSelector.filter(function(value){
                         return (value.active==true && value.type=="content");
                     });
@@ -93,7 +93,7 @@
         },
         render: function() {
             var currentPageSupportList = this._pagesSupportByLang[this.currentLang.id] ? this._pagesSupportByLang[this.currentLang.id] : [];
-            this.$el.html(this._template({currentLang: this.currentLang,role:this.role, storage: (currentPageSupportList.length>0)?true:false}));
+            this.$el.html(this._template({currentLang: this.currentLang,user:this.app.user, storage: (currentPageSupportList.length>0)?true:false}));
             this.$('header').append(this.childViews.langDropDown.el);
             this.childViews.langDropDown.render()
             this.$('.dropdown-toggle').dropdown();
Index: src/js/JEditor/FilePanel/Templates/collectionList.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/collectionList.html	(révision 10045)
+++ src/js/JEditor/FilePanel/Templates/collectionList.html	(révision 10046)
@@ -1,7 +1,7 @@
 <div class="my-files collectionList">
     <% 
     var disableDelete="";
-    if(role ==="admin"){
+    if(!user.can("delete_file")){
         disableDelete='style="visibility:hidden"';
     }%>
     <% for(var i=0; i< content.length; i++){
Index: src/js/JEditor/FilePanel/Templates/collectionManagerUIView.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/collectionManagerUIView.html	(révision 10045)
+++ src/js/JEditor/FilePanel/Templates/collectionManagerUIView.html	(révision 10046)
@@ -17,7 +17,7 @@
     </div>
 
     <!-- ACTIONS -->
-    <% if(role !== "admin"){ %>
+    <% if(user.can("delete_file")){ %>
         <a href="#" class="file-action" data-action="delete">
             <span class="icon-bin"></span>
             <span class="infobulles"><%= __("deleteSelection")%></span>
Index: src/js/JEditor/FilePanel/Templates/fileDetailManager.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileDetailManager.html	(révision 10045)
+++ src/js/JEditor/FilePanel/Templates/fileDetailManager.html	(révision 10046)
@@ -22,7 +22,7 @@
             <%= __("Download")%>
             <span class="infobulles"><%= __("getFile")%></span>
         </a>
-        <% if(role !== "admin"){ %>
+        <% if(user.can("delete_file")){ %>
         <a href="#" data-delete="<%=cid%>">
             <span class="icon-bin"></span>
             <%= __("delete")%>
Index: src/js/JEditor/FilePanel/Templates/fileManagerUIView.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileManagerUIView.html	(révision 10045)
+++ src/js/JEditor/FilePanel/Templates/fileManagerUIView.html	(révision 10046)
@@ -63,7 +63,7 @@
         <span class="infobulles"><%= __("addToExistingCollection")%></span>
     </a>
 <%}%>
-<% if (role !== "admin"){%>
+<% if(user.can("delete_file")){%>
     <a href="#" class="file-action" data-action="delete">
         <span class="icon-bin"></span>
         <span class="infobulles"><%= __("deleteSelection")%></span>
Index: src/js/JEditor/FilePanel/Views/CollectionListView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/CollectionListView.js	(révision 10045)
+++ src/js/JEditor/FilePanel/Views/CollectionListView.js	(révision 10046)
@@ -60,7 +60,7 @@
         var $target = $(e.currentTarget);
         var cid = $target.data('cid');
         var model = this.collection.get(cid);
-        if(this.app.user.role === "admin") {
+        if(!this.app.user.can("delete_file")) {
             return false;
         }
         if (!this.app.params.dontAskAgainFor['deleteCollectionItem']) {
Index: src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js	(révision 10045)
+++ src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js	(révision 10046)
@@ -28,7 +28,7 @@
                 render: function() {
                     this._super();
                     this.undelegateEvents();
-                    this.$el.html(this._template({role:this.app.user.role}));
+                    this.$el.html(this._template( {user: this.app.user}));
                     this.dom[this.cid].selectionDropdown = this.$('.selection-dropdown-toggle').dropdown();
                     this.dom[this.cid].selection = this.$('.selection');
                     this.dom[this.cid].selectedLength = this.$('.icon-check');
@@ -69,7 +69,7 @@
                 },
                 deleteSelected: function() {
                     if (this.selectedLength) {
-                        if(this.app.user.role === "admin") {
+                        if(!this.app.user.can("delete_file")){
                             return false;
                         }
                         var shouldAsk = this.parentCollection ? !this.app.params.dontAskAgainFor['deleteFileItem'] : !this.app.params.dontAskAgainFor['deleteCollectionItem'];
Index: src/js/JEditor/FilePanel/Views/FileDetailManagerView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileDetailManagerView.js	(révision 10045)
+++ src/js/JEditor/FilePanel/Views/FileDetailManagerView.js	(révision 10046)
@@ -45,7 +45,7 @@
         var params = {};
         if (this.FileDetailView) {
             var isImg = this.FileDetailView.model.isImg();
-            params = _.extend(params, this.FileDetailView.model.toJSON(), {cid: this.FileDetailView.model.cid, isImg: isImg,role:this.app.user.role});
+            params = _.extend(params, this.FileDetailView.model.toJSON(), {cid: this.FileDetailView.model.cid, isImg: isImg, user: this.app.user});
         }
         this.$el.html(this._template(params));
         if (this.referrer)
@@ -89,10 +89,6 @@
         e.stopImmediatePropagation();
         var $target = $(e.currentTarget);
         var cid = $target.data('delete');
-        var role=this.app.user;
-        if(role.role === "root") {
-            return false;
-        }
             
         if (!this.app.params.dontAskAgainFor['deleteFileItem']) {
             this.confirm({
Index: src/js/JEditor/FilePanel/Views/FileListView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileListView.js	(révision 10045)
+++ src/js/JEditor/FilePanel/Views/FileListView.js	(révision 10046)
@@ -102,7 +102,7 @@
             var $target = $(e.currentTarget);
             var cid = $target.data('cid');
             var model = this.collection.get(cid);
-            if(this.app.user.role === "admin") {
+            if(!this.app.user.can("delete_file")) {
                 return false;
             }
             
@@ -130,7 +130,7 @@
         },
         render: function() {
             this._super();
-            if(this.app.user.role === "admin") {
+            if(!this.app.user.can("delete_file")){
                 this.$("li.action.delete").replaceWith('<li class="" style="visibility: hidden;"></li>');
             }
             this.dom[this.cid].uploadZone = this.$('.group-content');
Index: src/js/JEditor/FilePanel/Views/FileManagerUIView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileManagerUIView.js	(révision 10045)
+++ src/js/JEditor/FilePanel/Views/FileManagerUIView.js	(révision 10046)
@@ -66,7 +66,7 @@
                 render: function() {
                     this._super();
                     this.undelegateEvents();
-                    this.$el.html(this._template({fileGroupList: this.options.fileGroupList,role:this.app.user.role}));
+                    this.$el.html(this._template({fileGroupList: this.options.fileGroupList, user: this.app.user}));
                     this.dom[this.cid].selectionDropdown = this.$('.selection-dropdown-toggle').dropdown();
                     this.dom[this.cid].sortDropdown = this.$('.sort-dropdown-toggle');
                     this.dom[this.cid].sortDropdown.dropdown();
@@ -112,7 +112,7 @@
                     return false;
                 },
                 deleteSelected: function() {
-                    if(this.app.user.role === "admin") {
+                    if(!this.app.user.can("delete_file")) {
                         return false;
                     }
                     if (this.selectedLength) {
Index: src/js/JEditor/PagePanel/PagePanel.js
===================================================================
--- src/js/JEditor/PagePanel/PagePanel.js	(révision 10045)
+++ src/js/JEditor/PagePanel/PagePanel.js	(révision 10046)
@@ -276,7 +276,7 @@
 			this.$('aside .options').after(this.childViews.pageList.render().el);
 			if(this.app.user.can('access_batch_action'))
 				this.$('aside .options').after(this.childViews.pageListManager.render().el);
-			if(this.app.user.can('access_lp_page') ){
+			if(this.app.user.can('access_lpsupport_page') ){
 				if(currentListSupport.length > 0) this.$('aside .batch').after(this.childViews.pageLpManager.render().el);
 				else this.$('aside .lp.view').remove();
 			}
Index: src/js/JEditor/PagePanel/Templates/pageList.html
===================================================================
--- src/js/JEditor/PagePanel/Templates/pageList.html	(révision 10045)
+++ src/js/JEditor/PagePanel/Templates/pageList.html	(révision 10046)
@@ -1,11 +1,11 @@
 <div class="wrapper <%=groupName%>">
-    <% if(!home && groupName==='content'&& role!=='admin'){ %>
+    <% if(!home && groupName==='content'&& user.can('homeless_msg')){ %>
     <div class="warning">
         <span class="icon-warning"></span><span><%= __("homelessLang")%></span>
     </div>
     <% } %>
     <nav>
-        <h2> <% if(lpactive && role !=="admin" || groupName !=='lp'){ %> <%=__('page'+groupName)%> <% } %></h2>
+        <h2> <% if(lpactive && user.can("access_lpsupport_page") || groupName !=='lp'){ %> <%=__('page'+groupName)%> <% } %></h2>
         <ul>
             <% if(home&&groupName==="content"){ %>
             <li class="home <%= home==current?'edit':''%><%=home.locked? 'locked':'' %>">
@@ -120,7 +120,7 @@
             </li>
             <%
             }
-            if(page.home ===7 && groupName==="lp" && (role ==="root" || role ==="superadmin")){%>
+            if(page.home ===7 && groupName==="lp" && user.can("access_lpsupport_page")){%>
                 <li class=" ">
                     <a data-cid="<%= page.cid %>" href="#">
                         <span class="icon icon-interest-point"></span>
Index: src/js/JEditor/PagePanel/Templates/pageView.html
===================================================================
--- src/js/JEditor/PagePanel/Templates/pageView.html	(révision 10045)
+++ src/js/JEditor/PagePanel/Templates/pageView.html	(révision 10046)
@@ -3,7 +3,7 @@
 
         <div class="config-preview">
             <div class="btn-group">
-              <% if(role !== 'admin' ) {%>
+            <% if(user.can('config_page')) {%>
             <div class="btn-group config">
                 <a class="btn dropdown-toggle page-action" href="#">
                     <span class="icon icon-params"></span><span class="label"><%=__('config')%></span>
@@ -60,7 +60,7 @@
                         </ul>
                     </li>
                     <% } %>
-                    <% if(page.accueil !== 7) { %>
+                    <% if(page.accueil !== 7 && user.can('export_page')) { %>
                     <li>
                         <a href="#" data-action="none"><%= __("exportImportPage") %><span class="caret-right"></span></a>
                         <ul>
@@ -67,7 +67,7 @@
                             <li>
                                 <a href="<%=__IDEO_API_PATH__%>/page-export/<%=page.id%>"><%= __("export") %></a> 
                             </li>
-                            <% if( role === "root" ) { %>
+                            <% if( user.can('export_contenu_page') ) { %>
                             <li>
                                 <a href="<%=__IDEO_API_PATH__%>/page-export-contenu?idpage=<%=page.id%>&idzone=<%=currentZoneID%>"><%= __("exportContenu") %></a> 
                             </li>
Index: src/js/JEditor/PagePanel/Views/PageCollectionView.js
===================================================================
--- src/js/JEditor/PagePanel/Views/PageCollectionView.js	(révision 10045)
+++ src/js/JEditor/PagePanel/Views/PageCollectionView.js	(révision 10046)
@@ -114,7 +114,7 @@
       });
       return {
         canDelete:this.app.user.can('delete_page'),
-        role:this.app.user.role,
+        user:this.app.user,
         home: home,
         contact: contact,
         legal:legal,
Index: src/js/JEditor/PagePanel/Views/PageView.js
===================================================================
--- src/js/JEditor/PagePanel/Views/PageView.js	(révision 10045)
+++ src/js/JEditor/PagePanel/Views/PageView.js	(révision 10046)
@@ -49,7 +49,6 @@
                         selectedZoneVersion: null,
                         initialize: function () {
                             this._super();
-                            this.role = this.app.user.role ;
                             this.currentZoneID = this.options.zoneID || null;
                             this._template = this.buildTemplate(pageView, translate);
                             if (this.model) {
@@ -277,7 +276,6 @@
                                 languages: ContentLanguageList.getInstance(),
                                 canChangeLayout: this.app.user.can("set_page_layout"),
                                 currentZoneID:this.currentZoneID,
-                                role:this.role
                             }));
 
                             if (this.pagePreview) {
Index: src/js/JEditor/ParamsPanel/ParamsPanel.js
===================================================================
--- src/js/JEditor/ParamsPanel/ParamsPanel.js	(révision 10045)
+++ src/js/JEditor/ParamsPanel/ParamsPanel.js	(révision 10046)
@@ -78,8 +78,7 @@
                                         }
                                        
                                     };
-                                    var role = this.app.user.role ;
-                                    if(role=="root") {
+                                    if(this.app.user.can('view_jsonLd')) {
                                         this.menuEntries.donneeStructuree =   
                                         {
                                             object: new DonneeStructureeView({model:this.params}),
