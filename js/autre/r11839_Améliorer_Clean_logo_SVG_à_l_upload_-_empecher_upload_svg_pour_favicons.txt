Revision: r11839
Date: 2024-02-02 15:57:19 +0300 (zom 02 Feb 2024) 
Author: rrakotoarinelina 

## Commit message
Améliorer Clean logo SVG à l'upload - empecher upload svg pour favicons

## Files changed

## Full metadata
------------------------------------------------------------------------
r11839 | rrakotoarinelina | 2024-02-02 15:57:19 +0300 (zom 02 Feb 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/MarqueClientView.js

Améliorer Clean logo SVG à l'upload - empecher upload svg pour favicons
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js
===================================================================
--- src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 11838)
+++ src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 11839)
@@ -73,6 +73,7 @@
                 hideMenuOn: null,
                 uploadUrl: __IDEO_UPLOAD_URL__ ? __IDEO_UPLOAD_URL__ : '/admin/resources',
                 customStockEvent: '_parsestock_all',
+                isFavicon:false,
                 onUploadStart: function() {
                 },
                 onUploadFail: function() {
@@ -100,7 +101,8 @@
                     uploadFailCorrupted: 'Echec du chargement. Le fichier semble corrompu',
                     uploadFailErrorsOccured: 'Des erreurs ont eu lieu pendant le transfert de fichier',
                     uploadSuccess: 'Le transfert des fichiers s\'est déroulé avec succès',
-                    uploadFailServerError: 'Impossible de transférer le fichier %name% à cause de l\'erreur: "%error%"'
+                    uploadFailServerError: 'Impossible de transférer le fichier %name% à cause de l\'erreur: "%error%"',
+                    uploadFaviconCorrupted: 'Le format SVG n\'est sont pas autorisés pour les favicons'
                 }
             };
             this.options.lang = $.extend({}, settings.lang, this.options.lang ? this.options.lang : {});
@@ -357,7 +359,7 @@
                                             </div>\n\
                                             <div class="action abort"></div></div>');
                             var realPreview = preview.children('div:not(.abort)');
-                            var uploadInfos = {name: file.name, value: file, size: file.size, preview: preview, uploaded: 0,code:e.target.result};
+                            var uploadInfos = {name: file.name, value: file, size: file.size, preview: preview, uploaded: 0,code:e.target.result,type: file.type};
                             if (uploader._isImage(file)) {
                                 var decodedData = atob(uploadInfos.code.split(',')[1]);
                                 if (decodedData.includes('<script>')) {
@@ -371,6 +373,18 @@
                                     realPreview.addClass('imagepreview');
                                     realPreview.css({background: 'url(' + this.result + ') center center', backgroundSize: 'cover'});
                                 }
+
+                                //checker si favicon et empecher upload svg
+                                if(uploadInfos.type === 'image/svg+xml' && uploader.options.isFavicon){
+                                    message = uploader.options.lang.uploadFaviconCorrupted;
+                                    uploader.errors.push(message);
+                                    files.rejected++;
+                                    if (files.length === 1){
+                                        uploader._onComplete();
+                                    }
+                                }
+
+
                             }
                             else {
                                 realPreview.addClass('filepreview');
Index: src/js/JEditor/ParamsPanel/Views/MarqueClientView.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 11838)
+++ src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 11839)
@@ -188,7 +188,8 @@
             //Favicon
             this.fileUploader3 = this.uploadFile(this.currentFileFavicon, {
                 ...uploadParams,
-                acceptedExtensions: ['jpeg', 'jpg', 'png']
+                acceptedExtensions: ['jpeg', 'jpg', 'png'],
+                isFavicon : true
               });
 
 
