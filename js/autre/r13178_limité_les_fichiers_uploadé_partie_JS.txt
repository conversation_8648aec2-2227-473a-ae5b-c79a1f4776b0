Revision: r13178
Date: 2024-10-09 10:04:44 +0300 (lrb 09 Okt 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
limité les fichiers uploadé (partie JS)

## Files changed

## Full metadata
------------------------------------------------------------------------
r13178 | sraz<PERSON><PERSON><PERSON>oa | 2024-10-09 10:04:44 +0300 (lrb 09 Okt 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js

limité les fichiers uploadé (partie JS)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js
===================================================================
--- src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 13177)
+++ src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 13178)
@@ -57,7 +57,7 @@
             $.event.props.push('dataTransfer');
             var settings = {
                 acceptedTypes: ['image', 'audio', 'video','font'],
-                acceptedExtensions: ['.doc', '.pdf', '.xls', '.zip', '.odp', '.odt', '.ods', '.json','ttf','otf','woff','woff2','eot'],
+                acceptedExtensions: ['.png', '.jpeg', '.jpg', '.gif', '.svg', '.webp', '.mp4', '.pdf', '.doc', '.dot', '.docx', '.ppt', '.pot', '.json', '.ttf', '.otf', '.woff', '.woff2', '.eot'],
                 refusedExtensions: ['.php', '.pl', '.rb', '.exe'],
                 refusedTypes: ['exploit'],
                 maxFiles: 5,
@@ -326,7 +326,7 @@
                 var filesize = Math.ceil(files[i].size  / (1024 * 1024));
                 if (this.options.maxFiles !== -1 && (i >= this.options.maxFiles))
                     break; //Yeah, this is a breakup reason
-                if (!(this.acceptedTypes.test(files[i].type.toLowerCase()) || (this.acceptedExt && this.acceptedExt.test(files[i].name.toLowerCase()))) ||
+                if (!(this.acceptedTypes.test(files[i].type.toLowerCase()) && (this.acceptedExt && this.acceptedExt.test(files[i].name.toLowerCase()))) ||
                         (this.refusedExt.test(files[i].name.toLowerCase()) || this.refusedTypes.test(files[i].type.toLowerCase()))) {
                     message = this.options.lang.importFailBadType ? this.options.lang.importFailBadType : this.options.lang.error;
                     this.errors.push(message.replace('%name%', files[i].name));
