Revision: r10377
Date: 2023-02-10 09:57:54 +0300 (zom 10 Feb 2023) 
Author: norajaonarivelo 

## Commit message
WishList Ideo3.2 :Fix bug First Commit Liste Evoluion File

## Files changed

## Full metadata
------------------------------------------------------------------------
r10377 | norajaonarivelo | 2023-02-10 09:57:54 +0300 (zom 10 Feb 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Views/ReadOnlyFileListManagerView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileListView.js
   M /branches/ideo3_v2/integration/src/less/imports/filePanel.less

WishList Ideo3.2 :Fix bug First Commit Liste Evoluion File
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Files/Views/ReadOnlyFileListManagerView.js
===================================================================
--- src/js/JEditor/Commons/Files/Views/ReadOnlyFileListManagerView.js	(révision 10376)
+++ src/js/JEditor/Commons/Files/Views/ReadOnlyFileListManagerView.js	(révision 10377)
@@ -11,8 +11,8 @@
                 },
                 defaultOptions: {
                     showFilters: true,
-                    sortBy: '',
-                    sortOrder: ''
+                    sortBy: 'createdAt',
+                    sortOrder: 'desc'
                 },
                 initialize: function() {
                     this._super();
@@ -27,7 +27,7 @@
                     this.options.sortBy = data.sortby;
                     this.options.sortOrder = data.sortorder;
                     if (this.listView.sortBy)
-                        this.listView.sortBy(data.sortby, data.sortorder === 'asc');
+                        this.listView.sortBy(data.sortby, data.sortorder === 'desc');
                     this.dom[this.cid].sortDropdownItems.removeClass('active');
                     this.dom[this.cid].sortDropdownParent.removeClass('open');
                     $target.addClass('active');
Index: src/js/JEditor/FilePanel/Views/FileListView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileListView.js	(révision 10376)
+++ src/js/JEditor/FilePanel/Views/FileListView.js	(révision 10377)
@@ -81,7 +81,7 @@
                 this.render();
             }
             else {
-                this.sortedBy = property;
+                this.sortedBy = (property==="createdAt")?"id":property;
                 this.sortAsc = asc;
                 this.currentList = this.sortList(this.currentList);
             }
Index: src/less/imports/filePanel.less
===================================================================
--- src/less/imports/filePanel.less	(révision 10376)
+++ src/less/imports/filePanel.less	(révision 10377)
@@ -772,7 +772,33 @@
 #files .file:hover .select ,#files .file.selected .select {
     opacity: 1;
 }
-
+.menu-wrapper.add-file {
+    width: 91px;
+    height: 91px;
+    padding: 14px;
+    border: 1px solid #ddd;
+    color: #989898;
+    line-height: 14px;
+    font-size: 12px;
+    text-align: center;
+    cursor: pointer;
+    -webkit-transition: all .3s ease-in-out;
+    -moz-transition: all .3s ease-in-out;
+    -o-transition: all .3s ease-in-out;
+    -ms-transition: all .3s ease-in-out;
+    transition: all .3s ease-in-out;
+  }
+  .menu-wrapper.add-file> .icon > .icon-add {
+    position: absolute;
+    font-size: 14px;
+    top: 6px;
+    left: 0;
+    color: #FFFFFF;
+    right: 0;
+  }
+  .menu-wrapper.add-file> .icon > .icon-hexagon {
+    font-size: 26px;
+  }
 #files .my-files .file {
     width: 120px; height: 120px;
     position: relative;
