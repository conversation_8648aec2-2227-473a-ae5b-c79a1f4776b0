Revision: r10678
Date: 2023-03-20 09:55:43 +0300 (lts 20 Mar 2023) 
Author: norajaonarivelo 

## Commit message
Correction sur retour Wishilis3.2 Javascript set get theme inline dans le head

## Files changed

## Full metadata
------------------------------------------------------------------------
r10678 | norajaonarivelo | 2023-03-20 09:55:43 +0300 (lts 20 Mar 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/dev/#librairies/vendor/CoreMoteurDeRendu/src/CoreMoteurDeRendu/Core/AmbianceEngineRenderer.php
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/ParamsPanel.js

Correction sur retour Wishilis3.2 Javascript set get theme inline dans le head
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/ParamsPanel.js
===================================================================
--- src/js/JEditor/ParamsPanel/ParamsPanel.js	(révision 10677)
+++ src/js/JEditor/ParamsPanel/ParamsPanel.js	(révision 10678)
@@ -59,6 +59,11 @@
                                             icon:"icon-html",
                                             title:translate("MarqueClient") 
                                         },
+                                        themeSite:{
+                                            object: new ThemeSiteView({model:this.params}),
+                                            icon:'',
+                                            title:translate("ThemeTitle")
+                                        },
                                         IconsCollection:{
                                             object: new IconsCollectionView({model:this.params}),
                                             icon:"icon-html",
@@ -89,11 +94,7 @@
                                             icon:"icon-link",
                                             title:translate("PolitiqueConfidentialite")
                                         },
-                                        themeSite:{
-                                            object: new ThemeSiteView({model:this.params}),
-                                            icon:'',
-                                            title:translate("ThemeTitle")
-                                        }
+                                       
                                     };
                                     if(this.app.user.can('view_jsonLd')) {
                                         this.menuEntries.donneeStructuree =   
