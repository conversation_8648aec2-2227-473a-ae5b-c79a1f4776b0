Revision: r11040
Date: 2023-06-15 11:40:35 +0300 (lkm 15 Jon 2023) 
Author: mpartaux 

## Commit message
fix CSS issues

## Files changed

## Full metadata
------------------------------------------------------------------------
r11040 | mpartaux | 2023-06-15 11:40:35 +0300 (lkm 15 Jon 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/less/imports/page_panel/module/block-options/advanced-option.less

fix CSS issues
------------------------------------------------------------------------

## Diff
Index: src/less/imports/page_panel/module/block-options/advanced-option.less
===================================================================
--- src/less/imports/page_panel/module/block-options/advanced-option.less	(révision 11039)
+++ src/less/imports/page_panel/module/block-options/advanced-option.less	(révision 11040)
@@ -107,17 +107,26 @@
 	color: var(--lead-text1);
 } 
 
-.normal{
-	background: var(--surface1);
-	color: var(--fg-color);
-}
+.color-container {
+	.normal{
+		background: var(--surface1);
+		color: var(--fg-color);
+	}
 
-.accent{
-	background: var(--accent-surface1);
-	color: var(--accent-text1);
-}
+	.accent{
+		background: var(--accent-surface1);
+		color: var(--accent-text1);
+	}
 
-.infos{
-	background: var(--notice);
-	color: var(--notice-contrast);
+	.infos{
+		background: var(--notice);
+		color: var(--notice-contrast);
+	}
+
+	.color-choice {
+		border-radius: 4px;
+		.helper {
+			color: white;
+		}
+	}
 }
\ No newline at end of file
