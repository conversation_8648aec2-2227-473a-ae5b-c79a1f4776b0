Revision: r13747
Date: 2025-01-27 12:30:43 +0300 (lts 27 Jan 2025) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: adapter titre collections images selon les langues du site(partie JS)

## Files changed

## Full metadata
------------------------------------------------------------------------
r13747 | srazanandralisoa | 2025-01-27 12:30:43 +0300 (lts 27 Jan 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Ancestors/Views/ListView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Models/FileGroup.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Templates/imageGroup.html
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Templates/imageGroupCollection.html
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Views/FileGroupListView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Views/ImageGroupView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/FilePanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/collectionDetail.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/collectionList.html
   A /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/collectionTitle.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/emptyCollection.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/CollectionDetailView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/CollectionListView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/CollectionTitleView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieField.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/collection.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/CollectionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalleryBlock/Templates/imageGroupCollection.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalleryBlock/Views/GalleryOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalleryBlock/Views/ImageGroupView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/imageGroupCollection.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Templates/imageGroupCollection.html
   M /branches/ideo3_v2/integration/src/less/imports/filePanel.less

wishlist IDEO3.2: adapter titre collections images selon les langues du site(partie JS)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Ancestors/Views/ListView.js
===================================================================
--- src/js/JEditor/Commons/Ancestors/Views/ListView.js	(révision 13746)
+++ src/js/JEditor/Commons/Ancestors/Views/ListView.js	(révision 13747)
@@ -274,11 +274,11 @@
             } else {
                 list = this.sortList(list);
                 if (list.length > 0) {
-                    var params = _.extend({}, {content: list, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected, user: this.app.user}, this.addTemplateParams(this.collection, list));
+                    var params = _.extend({}, {content: list, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected, user: this.app.user, lang : this.lang}, this.addTemplateParams(this.collection, list));
                     this.$el.html(this._template(params));
                 }
                 else {
-                    var params = _.extend({}, {content: list, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected}, this.addTemplateParams(this.collection, list));
+                    var params = _.extend({}, {content: list, filters: this.filteredBy, sortAsc: this.sortAsc, sortedBy: this.sortedBy, selected: this.selected, lang : this.lang}, this.addTemplateParams(this.collection, list));
                     this.$el.html(this._emptyTemplate(params));
                 }
 
Index: src/js/JEditor/Commons/Files/Models/FileGroup.js
===================================================================
--- src/js/JEditor/Commons/Files/Models/FileGroup.js	(révision 13746)
+++ src/js/JEditor/Commons/Files/Models/FileGroup.js	(révision 13747)
@@ -3,11 +3,13 @@
     "JEditor/Commons/Files/Models/FileGroup",
     "JEditor/Commons/Ancestors/Models/Model",
     "JEditor/Commons/Files/Models/FileCollection",
+    "JEditor/Commons/Languages/Models/ContentLanguageList",
     "collection!JEditor/Commons/Files/Models/FileDBCollection"
 ], function(Events,
         FileGroup,
         Model,
         FileCollection,
+        ContentLanguageList,
         FileDBCollection
         ) {
     var FileGroup = Model.extend({
@@ -26,7 +28,10 @@
             Model.call(this, attrs, options);
         },
         initialize: function() {
+            if (!this.name)
+                this.name = {};
             this.fileList = FileDBCollection.getInstance();
+            this.languages = ContentLanguageList.getInstance();
             this.listenTo(this.files, 'add remove', this._updateLength);
             this.listenTo(this.files, Events.BackboneEvents.ADD, this._onAdd);
             this.listenTo(this.files, Events.BackboneEvents.REMOVE, this._onRemove);
@@ -71,6 +76,11 @@
                 }
             }
         },
+        setNameforAllLang: function (name){
+            this.languages.each(function(lang) {
+               this.name[lang.id] = name
+            },this);
+        },
         /**
          * 
          * @returns {undefined}
Index: src/js/JEditor/Commons/Files/Templates/imageGroup.html
===================================================================
--- src/js/JEditor/Commons/Files/Templates/imageGroup.html	(révision 13746)
+++ src/js/JEditor/Commons/Files/Templates/imageGroup.html	(révision 13747)
@@ -1,7 +1,7 @@
 <header>
     <div class="title">
         <div>
-            <span class="icon-gallery"></span><input type="text" class="gallery-name" placeholder="<%=__("newCollectionName")%>" value="<%=Utils.stripHTML(group.name)%>" name="name"/><span class="icon-edit"></span>
+            <span class="icon-gallery"></span><input type="text" class="gallery-name" placeholder="<%=__("newCollectionName")%>" value="<%=Utils.stripHTML(group.name[lang])%>" name="name"/><span class="icon-edit"></span>
         </div>
     </div><% if(group.files.length>0){%>
 
Index: src/js/JEditor/Commons/Files/Templates/imageGroupCollection.html
===================================================================
--- src/js/JEditor/Commons/Files/Templates/imageGroupCollection.html	(révision 13746)
+++ src/js/JEditor/Commons/Files/Templates/imageGroupCollection.html	(révision 13747)
@@ -10,7 +10,7 @@
             
             <div class="image" style="background-image: url(<%= fileGroup.files.at(0)?fileGroup.files.at(0).thumb:'#'%>)">
                 <div class="overlay">
-                    <span class="groupName"><%=fileGroup.name%></span>
+                    <span class="groupName"><%=fileGroup.name[lang]%></span>
                     <span class="fileCount"><span class="count"><%=fileGroup.length%></span><span class="icon-image"></span></span>
                 </div>
                 <div class="corner"></div>
Index: src/js/JEditor/Commons/Files/Views/FileGroupListView.js
===================================================================
--- src/js/JEditor/Commons/Files/Views/FileGroupListView.js	(révision 13746)
+++ src/js/JEditor/Commons/Files/Views/FileGroupListView.js	(révision 13747)
@@ -12,6 +12,8 @@
         },
         initialize: function() {
             this._super();
+            if (!this.lang)
+            this.lang = this.app.params.defaultcontentlang;
             this._template = this.buildTemplate(imageGroupCollection, translate);
             this._emptyTemplate = this.buildTemplate(imageGroupCollection, translate);
         },
Index: src/js/JEditor/Commons/Files/Views/ImageGroupView.js
===================================================================
--- src/js/JEditor/Commons/Files/Views/ImageGroupView.js	(révision 13746)
+++ src/js/JEditor/Commons/Files/Views/ImageGroupView.js	(révision 13747)
@@ -39,6 +39,8 @@
             this.listenTo(this.model.files, Events.BackboneEvents.REMOVE, this.render);
             this.selectFileView = new FileSelectorDialog({collection: this.fileCollection, allowMultipleSelect: true});
             this.listenTo(this.selectFileView, Events.ListViewEvents.CHOOSE_FILE, this._useExistingFile);
+            if (!this.lang)
+            this.lang = this.app.params.defaultcontentlang;
         },
         crop: function(fileId) {
             var file = this.model.files.get(fileId);
@@ -106,8 +108,8 @@
             var $target = $(e.currentTarget);
             this._timeout = window.setTimeout(_.bind(function() {
                 var value = $target.val();
-                if (value && value !== this.model.name) {
-                    this.model.name = value;
+                if (value && value !== this.model.name[this.lang]) {
+                    this.model.name[this.lang] = value;
                     console.log(this.count);
                     this.model.save();
                 }
@@ -150,7 +152,7 @@
                 if (this.selected[selected])
                     selectedCount++;
             }
-            this.$el.html(this._template({group: this.model, selected: this.selected, selectedCount: selectedCount, Utils: Utils}));
+            this.$el.html(this._template({group: this.model, selected: this.selected, selectedCount: selectedCount, Utils: Utils, lang :this.lang}));
             this.dom[this.cid].uploader = this.$('.uploader');
             this.dom[this.cid].uploader.uploader({showMenu: false, maxFiles: -1, menuContainer: this.$('.content'), lang: this.translate.translations, customStockEvent: '_parsestock_image'});
             this.dom[this.cid].selectCount = this.$('.batch > .dropdown > a .selected .count');
@@ -168,12 +170,12 @@
                 toAdd.push(file);
             }
             this.model.files.add(toAdd, {at: 0});
-            if (!this.model.name) {
-                var date = new Date();
-                var dateString = date;
-                this.model.name = this.translate("newCollectionName") + Utils.dateFormat(this.translate('dateFormat'))
+            if (Object.keys(this.model.name).length === 0 ||!this.model.name || this.model.name.length === 0) {
+                var name = translate("newCollectionName") + ' ' + Utils.dateFormat(translate('dateFormat'));
+                this.model.setNameforAllLang(name);
             }
             this.model.save();
+            this.render();
         },
         resetFilter: function(){
             this.fileCollection.resetFilter();
@@ -202,13 +204,12 @@
                 this.model.files.add(file, {at: 0});
                 this.selected[file.id] = false;
             }
-            if (!this.model.name) {
-                var date = new Date();
-                var dateString = date;
-                this.model.name = this.translate("newCollectionName") + Utils.dateFormat(this.translate('dateFormat'));
+            if (Object.keys(this.model.name).length === 0 ||!this.model.name || this.model.name.length === 0) {
+                var name = translate("newCollectionName") + ' ' + Utils.dateFormat(translate('dateFormat'));
+                this.model.setNameforAllLang(name);
             }
             this.model.save();
-
+            this.render();
         },
         _onBack: function() {
             this.trigger(Events.ChoiceEvents.BACK);
Index: src/js/JEditor/FilePanel/FilePanel.js
===================================================================
--- src/js/JEditor/FilePanel/FilePanel.js	(révision 13746)
+++ src/js/JEditor/FilePanel/FilePanel.js	(révision 13747)
@@ -571,7 +571,7 @@
                             /**
                              * création de la vues détaillée de la collection
                              */
-                            this._byCID[model.cid] = new CollectionDetailView({model: model, collection: model.files});
+                            this._byCID[model.cid] = new CollectionDetailView({model: model, collection: model.files, languages: this.languages});
                             this._byCID[model.cid].hide(false);
                             this.$el.append(this._byCID[model.cid].el);
                             this.listenTo(this._byCID[model.cid], Events.ListViewEvents.GOTO_COLLECTIONLIST, this.switchToCollections);
Index: src/js/JEditor/FilePanel/Templates/collectionDetail.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/collectionDetail.html	(révision 13746)
+++ src/js/JEditor/FilePanel/Templates/collectionDetail.html	(révision 13747)
@@ -1,14 +1,5 @@
 <div class="my-files collectionDetail">
-    <div class="title-collection-wrapper">
-        <div class="title-collection">
-            <label><span class="icon-gallery"></span></label>
-            <input class="collectionName" value="<%=_.escape(name)%>"></input>
-            <span class="icon-edit"></span>
-        </div>
-        <div class="shortcode-collection shortcode">
-            [[collection_<%= id %>]]
-        </div>
-    </div>
+    <div class="collectionDetail__header"></div>
     <!-- Collection return -->
     <div class="menu-wrapper collection-return">
 
Index: src/js/JEditor/FilePanel/Templates/collectionList.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/collectionList.html	(révision 13746)
+++ src/js/JEditor/FilePanel/Templates/collectionList.html	(révision 13747)
@@ -42,7 +42,7 @@
 
             <span class="title">
                 <span class="icon-gallery"></span>
-                <%=collection.name%>
+                <%=collection.name[lang]%>
             </span>
 
         </div>
Index: src/js/JEditor/FilePanel/Templates/collectionTitle.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/collectionTitle.html	(nonexistent)
+++ src/js/JEditor/FilePanel/Templates/collectionTitle.html	(révision 13747)
@@ -0,0 +1,11 @@
+<div class="title-collection">
+    <label><span class="icon-gallery"></span></label>
+    <input class="collectionName" value="<%=_.escape(name)%>"></input>
+    <span class="icon-edit"></span>
+</div>
+<div class="collectionName-lang">
+    
+</div>
+<div class="shortcode-collection shortcode">
+    [[collection_<%= id %>]]
+</div>
\ No newline at end of file
Index: src/js/JEditor/FilePanel/Templates/emptyCollection.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/emptyCollection.html	(révision 13746)
+++ src/js/JEditor/FilePanel/Templates/emptyCollection.html	(révision 13747)
@@ -1,16 +1,5 @@
 <div id="collection-empty">
-
-    <div class="title-collection-wrapper">
-            <div class="title-collection">
-                <label><span class="icon-gallery"></span></label>
-                <input class="collectionName" value="<%=_.escape(name)%>"></input>
-                <span class="icon-edit"></span>
-            </div>
-            <div class="shortcode-collection shortcode">
-                [[collection_<%= id %>]]
-            </div>
-        </div>
-
+    <div class="collectionDetail__header"></div>
     <div class="content scroll-container">
         <div class="group-content">
 
Index: src/js/JEditor/FilePanel/Views/CollectionDetailView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/CollectionDetailView.js	(révision 13746)
+++ src/js/JEditor/FilePanel/Views/CollectionDetailView.js	(révision 13747)
@@ -11,6 +11,7 @@
     "JEditor/App/Messages/ClipboardModule", 
     "JEditor/FilePanel/Models/FileCollection",
     "JEditor/Commons/Files/Views/FileSelectorDialog",
+    "JEditor/FilePanel/Views/CollectionTitleView",
     "i18n!../nls/i18n",
     //not in params
     "jqueryPlugins/uploader"
@@ -26,6 +27,7 @@
         ClipboardModule,
         FileCollection,
         FileSelectorDialog,
+        CollectionTitleView,
         translate) {
     var CollectionDetailView = ListView.extend({
         selected: {},
@@ -39,11 +41,9 @@
             'click .back-to-file': 'backToList',
             'click .file .action.goToFile[data-cid]': 'goToFile',
             'click .file .action.delete[data-cid]': 'deleteOne',
-            'change input.collectionName': 'setName',
             'uploadercomplete .group-content': 'uploadercomplete',
             'click [data-action="showuploader"]': 'showUploader',
             'sortstop .group-content': 'onSortStop',
-            'click .shortcode-collection.shortcode' : 'copyToClipboard',
             'click .file .action.replaceFile[data-cid]': 'replaceFile'   
         },
         initialize: function() {
@@ -52,6 +52,10 @@
             this._emptyTemplate = this.buildTemplate(emptyCollection, translate);
             this.fileCollection =  new FileCollection();
             this.fileCollection.fetch();
+            this.childViews.collectionTitle = new CollectionTitleView({
+                model: this.model,
+                languages : this.options.languages
+            });
         },
         toggleSelected: function(e) {
             var $target = $(e.currentTarget);
@@ -108,22 +112,10 @@
             this.dom[this.cid].showUploader = this.$('input.upload.button');
 
             this.dom[this.cid].uploadZone.sortable({items: '.menu-wrapper.file'});
-
+            this.$('.collectionDetail__header').append(this.childViews.collectionTitle.render().el);
             this.delegateEvents();
             return this;
         },
-        setName: function(e) {
-            e.stopImmediatePropagation();
-            var $target = $(e.currentTarget);
-            var newName = $target.val();
-            console.dir(newName);
-            var currentCollection = this.model;
-            if (currentCollection) {
-                currentCollection.name = newName;
-                currentCollection.save();
-            }
-            return false;
-        },
         uploadercomplete: function(e, data) {
             for (var i = 0; i < data.filesData.length; i++) {
                 var file = new File(data.filesData[i].response);
@@ -161,9 +153,6 @@
             this.currentList = this.collection.toArray();
             this.model.save();
         },
-        copyToClipboard : function (e){
-            ClipboardModule.copyToClipboard(e);
-        },
         resetFilter: function(){
             this.fileCollection.resetFilter();
           //  this.fileCollection.fetch();
Index: src/js/JEditor/FilePanel/Views/CollectionListView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/CollectionListView.js	(révision 13746)
+++ src/js/JEditor/FilePanel/Views/CollectionListView.js	(révision 13747)
@@ -36,11 +36,14 @@
         'click .collection-not': 'addCollection',
     },
     initialize: function() {
+        if (!this.lang)
+        this.lang = this.app.params.defaultcontentlang;
         this._super();
         this._template = this.buildTemplate(collectionList,translate);
         this._groupTemplate = this.buildTemplate(groupCollectionList,translate);
         this._emptyGroupTemplate = this.buildTemplate(emptyGroupCollectionList,translate);
         this._emptyTemplate = this.buildTemplate(emptyCollectionList,translate);
+        
     },
     toggleSelected: function(e) {
         var $target = $(e.currentTarget);
Index: src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js	(révision 13746)
+++ src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js	(révision 13747)
@@ -148,7 +148,8 @@
                         var collection = this.collection;
                     var date = new Date();
                     var name = translate("newGallery") + ' ' + Utils.dateFormat(translate('dateFormat'));
-                    var newFileGroup = new FileGroup({name: name});
+                    var newFileGroup = new FileGroup();
+                    newFileGroup.setNameforAllLang(name);
                     this.listenToOnce(collection, Events.BackboneEvents.SYNC, this.collectionAdded);
                     if (arrFiles && arrFiles.length) {
                         for (var i = 0; i < arrFiles.length; i++) {
@@ -205,7 +206,8 @@
                     if (!this.parentCollection.name) {
                         var date = new Date();
                         var dateString = date;
-                        this.parentCollection.name = translate("newGallery") + Utils.dateFormat(translate('dateFormat'));
+                        var name = translate("newGallery") + Utils.dateFormat(translate('dateFormat'));
+                        this.parentCollection.setNameforAllLang(name);
                     }
                     this.parentCollection.save();
                     this.trigger(Events.ListViewEvents.UPDATE_COLLECTION, this.parentCollection);
Index: src/js/JEditor/FilePanel/Views/CollectionTitleView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/CollectionTitleView.js	(nonexistent)
+++ src/js/JEditor/FilePanel/Views/CollectionTitleView.js	(révision 13747)
@@ -0,0 +1,89 @@
+define( [
+    "jquery",
+    "underscore",
+    "text!../Templates/collectionTitle.html",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/View",
+    "JEditor/App/Messages/ClipboardModule",
+    "JEditor/Commons/Languages/Views/LanguagesDropDown", 
+    "i18n!../nls/i18n"
+    //not in params
+],
+        function($,
+                _,
+                collectionTitle,
+                Events,
+                View,
+                ClipboardModule,
+                LanguageDropDown,
+                translate
+                ) {
+            var CollectionTitleView = View.extend({
+                /**
+                 * Initialise l'objet
+                 */
+                events: {
+                    'change input.collectionName': 'setName',
+                    'click .shortcode-collection' : 'copyToClipboard'
+                },
+                currentLang: null,
+                attributes: {
+                    class: 'title-collection-wrapper'
+                },
+                initialize: function() {
+                    this._super();
+                    this._template = this.buildTemplate(collectionTitle, translate);
+                    if (!this.lang)
+                        this.lang = this.app.params.defaultcontentlang;
+
+                    this.currentLang = this.options.languages.get(this.lang);
+
+                    this.langDropDown = new LanguageDropDown({
+                        collection: this.options.languages,
+                        _default: this.currentLang,
+                        defaultLabel: 'language'
+                    });
+                    this.listenTo(this.langDropDown, "selected:choice", this._onLangSelect);
+                },
+                setName: function(e) {
+                  e.stopImmediatePropagation();
+                    var $target = $(e.currentTarget);
+                    var newName = $target.val();
+                    console.dir(newName);
+                    var currentCollection = this.model;
+                    if (currentCollection) {
+                        currentCollection.name[this.currentLang.id] = newName;
+                        currentCollection.save();
+                    }
+                    return false;
+                },
+                render: function() {
+                    this._super();
+                    var params = {
+                        lang: this.currentLang, 
+                        languages: this.options.languages.models, 
+                        name: this.model.name[this.currentLang.id], 
+                        id: this.model.id
+                    };
+                    
+                    params = _.extend(this.model.toJSON(), params, {user: this.app.user});
+                    this.$el.html(this._template(params));
+                    this.$('.collectionName-lang').append(this.langDropDown.render().el);
+                    this.delegateEvents(); // Assure que les événements sont attachés après le rendu
+                    return this;
+                },
+                
+                _onLangSelect: function(view, lang){
+                    if (lang && lang !== this.lang) {
+                        // this.lang = lang;
+                        this.currentLang = lang;
+                        this.render();
+                    }
+                    return false;
+                },
+                copyToClipboard : function (e){
+                    ClipboardModule.copyToClipboard(e);
+                },
+            });
+            return CollectionTitleView;
+        });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js	(révision 13746)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js	(révision 13747)
@@ -66,7 +66,8 @@
                     var options = { year: 'numeric', month: 'numeric', day: 'numeric',hour: 'numeric', minute: 'numeric', second: 'numeric' };
                     var dateString = date.toLocaleDateString('fr-FR', options);
                     
-                    fileGroup.name = this.translate("newGallery")+dateString ;//+ Utils.dateFormat(this.translate('dateFormat'));
+                    var name = this.translate("newGallery")+dateString ;//+ Utils.dateFormat(this.translate('dateFormat'));
+                    fileGroup.setNameforAllLang(name);
                     fileGroup.save();
                     this.listenTo(this.model.getFileGroup(), 'change:length', this.render);
                 }
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieField.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieField.js	(révision 13746)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieField.js	(révision 13747)
@@ -5,7 +5,7 @@
  * @property {string} name nom de la collection 
  * @property {integer} length nombre des image dans la collection
  */
-define(["JEditor/Commons/Ancestors/Models/Model"], function(Model) {
+define(["JEditor/Commons/Ancestors/Models/Model","JEditor/Commons/Files/Models/FileGroupCollection"], function(Model,FileGroupCollection) {
     var GalerieField = Model.extend({
         defaults: function() {
             var ret = {
@@ -15,6 +15,11 @@
             };
             return ret;
         },
+        initialize: function() {
+            this._super();
+            this.fileGroupList = FileGroupCollection.getInstance();
+            this.fileGroup = this.fileGroupList.get(this.id);
+        },
         constructor: function(attrs, options) 
         {
             Model.call(this, attrs, options);
@@ -24,6 +29,13 @@
             this.galerie.removeField(null,null,this);
             return this;
         },
+        getFileGroup : function (){
+            if (this.fileGroup) {
+                return this.fileGroup;
+            }
+            this.fileGroup = this.fileGroupList.get(this.id);
+            return this.fileGroup;
+        }
     }).setAttributes(["name","length"]);
     return GalerieField;
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/collection.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/collection.html	(révision 13746)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/collection.html	(révision 13747)
@@ -1,7 +1,7 @@
 
     <div class = "show-part" id="<%= field.id %>" >
         <span class="icon-grip"></span>
-        <span class="field-name"> <%= field.name %>  </span> 
+        <span class="field-name"> <%= name %>  </span> 
         <span class="collection-nb"> <%= field.length %>  images</span>
         <span class="delete">&nbsp;x</span>
     </div> 
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html	(révision 13746)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html	(révision 13747)
@@ -1,8 +1,8 @@
 <div class="panel-option-container animated  mr15">
     <article class="panel-option">
         <header>
-            <h3 class="option-name"></span> <%=__("galerieStyleContent")%></h3>
-            <p class="panel-content-legend"><%= __("galerieStyleLegend")%></p>
+            <h3 class="option-name"></span> <%=__("styleDegalerie")%></h3>
+            <p class="panel-content-legend"><%= __("styleDegalerieLegend")%></p>
         </header>
         <div>
             <div class="category-content radio-transformed stylleDegalerie">
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/CollectionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/CollectionView.js	(révision 13746)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/CollectionView.js	(révision 13747)
@@ -12,6 +12,8 @@
         initialize:function () {
             View.prototype.initialize.call(this);
             this.template=this.buildTemplate(template,i18n);
+            if (!this.lang)
+            this.lang = this.app.params.defaultcontentlang;
         },
         /**
          * on appel la fonction removeField du model galerie pour effacer une collection 
@@ -22,7 +24,9 @@
         },
         render:function () {
             View.prototype.render.call(this);
-            this.$el.html(this.template({field:this.model}));
+            var fileGroup = this.model.getFileGroup();
+            var name = (fileGroup.name[this.lang])? fileGroup.name[this.lang] : i18n('nameNotFound');
+            this.$el.html(this.template({field:fileGroup, name:  name}));
         }
     });
     return CollectionView;
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/i18n.js	(révision 13746)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/i18n.js	(révision 13747)
@@ -62,6 +62,7 @@
       "DescStyle3"           :  "Text on the image, animated on hover",
       "addGalerieField": "add collection",  
       "DescStyle4"           : "Text under the image, borders",
+      "nameNotFound"         :  '<span class="icon-warning" style="color: #d42525;"></span>Mameless collection',
       "DescStyle5"           :  "Text under the image, rounded pictures (ideal for square images)",
       "DescStyle6"           :  "Text next to the the image"
    },
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalleryBlock/Templates/imageGroupCollection.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalleryBlock/Templates/imageGroupCollection.html	(révision 13746)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalleryBlock/Templates/imageGroupCollection.html	(révision 13747)
@@ -10,7 +10,7 @@
             
             <div class="image" style="background-image: url(<%= fileGroup.files.at(0)?fileGroup.files.at(0).thumb:'#'%>)">
                 <div class="overlay">
-                    <span class="groupName"><%=fileGroup.name%></span>
+                    <span class="groupName"><%=fileGroup.name[lang]%></span>
                     <span class="fileCount"><span class="count"><%=fileGroup.length%></span><span class="icon-image"></span></span>
                 </div>
                 <div class="corner"></div>
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalleryBlock/Views/GalleryOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalleryBlock/Views/GalleryOptionView.js	(révision 13746)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalleryBlock/Views/GalleryOptionView.js	(révision 13747)
@@ -66,7 +66,8 @@
                         this.stopListening(oldFilegroup, 'change:length');
                     if (fileGroup && !fileGroup.length && !fileGroup.name) {
                         var date = new Date();
-                        fileGroup.name = this.translate("newGallery") + Utils.dateFormat(this.translate('dateFormat'));
+                        var name = this.translate("newGallery") + Utils.dateFormat(this.translate('dateFormat'));
+                        fileGroup.setNameforAllLang(name);
                         fileGroup.save();
                         this.listenTo(this.model.getFileGroup(), 'change:length', this.render);
                     }
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalleryBlock/Views/ImageGroupView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalleryBlock/Views/ImageGroupView.js	(révision 13746)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalleryBlock/Views/ImageGroupView.js	(révision 13747)
@@ -38,6 +38,8 @@
             this.listenTo(this.model.files, Events.BackboneEvents.REMOVE, this.render);
             this.selectFileView = new FileSelectorDialog({collection: this.fileCollection, allowMultipleSelect: true});
             this.listenTo(this.selectFileView, Events.ListViewEvents.CHOOSE_FILE, this._useExistingFile);
+            if (!this.lang)
+            this.lang = this.app.params.defaultcontentlang;
         },
         crop: function(fileId) {
             var file = this.model.files.get(fileId);
@@ -171,7 +173,8 @@
             if (!this.model.name) {
                 var date = new Date();
                 var dateString = date;
-                this.model.name = this.translate("newGallery") + Utils.dateFormat(this.translate('dateFormat'))
+                var name = this.translate("newGallery") + Utils.dateFormat(this.translate('dateFormat'))
+                this.model.setNameforAllLang(name);
             }
             this.model.save();
         },
@@ -205,7 +208,8 @@
             if (!this.model.name) {
                 var date = new Date();
                 var dateString = date;
-                this.model.name = this.translate("newGallery") + Utils.dateFormat(this.translate('dateFormat'));
+                var name = this.translate("newGallery") + Utils.dateFormat(this.translate('dateFormat'));
+                this.model.setNameforAllLang(name);
             }
             this.model.save();
 
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/imageGroupCollection.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/imageGroupCollection.html	(révision 13746)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/imageGroupCollection.html	(révision 13747)
@@ -10,7 +10,7 @@
             
             <div class="image" style="background-image: url(<%= fileGroup.files.at(0)?fileGroup.files.at(0).thumb:'#'%>)">
                 <div class="overlay">
-                    <span class="groupName"><%=fileGroup.name%></span>
+                    <span class="groupName"><%=fileGroup.name[lang]%></span>
                     <span class="fileCount"><span class="count"><%=fileGroup.length%></span><span class="icon-image"></span></span>
                 </div>
                 <div class="corner"></div>
Index: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopOptionView.js	(révision 13746)
+++ src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopOptionView.js	(révision 13747)
@@ -57,7 +57,8 @@
                     var options = { year: 'numeric', month: 'numeric', day: 'numeric',hour: 'numeric', minute: 'numeric', second: 'numeric' };
                     var dateString = date.toLocaleDateString('fr-FR', options);
                     
-                    fileGroup.name = this.translate("newGallery")+dateString ;//+ Utils.dateFormat(this.translate('dateFormat'));
+                    var name = this.translate("newGallery")+dateString ;//+ Utils.dateFormat(this.translate('dateFormat'));
+                    fileGroup.setNameforAllLang(name);
                     fileGroup.save();
                     this.listenTo(this.model.getFileGroup(), 'change:length', this.render);
                 }
Index: src/js/JEditor/Templates/imageGroupCollection.html
===================================================================
--- src/js/JEditor/Templates/imageGroupCollection.html	(révision 13746)
+++ src/js/JEditor/Templates/imageGroupCollection.html	(révision 13747)
@@ -10,7 +10,7 @@
             
             <div class="image" style="background-image: url(<%= fileGroup.files.at(0)?fileGroup.files.at(0).fileUrl:'#'%>)">
                 <div class="overlay">
-                    <span class="groupName"><%=fileGroup.name%></span>
+                    <span class="groupName"><%=fileGroup.name[lang]%></span>
                     <span class="fileCount"><span class="count"><%=fileGroup.length%></span><span class="icon-image"></span></span>
                 </div>
                 <div class="corner"></div>
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-ca/i18n.js	(révision 13746)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-ca/i18n.js	(révision 13747)
@@ -53,5 +53,6 @@
    "DescStyle3"           :  "Texte sur l'image" ,
    "addGalerieField": "ajouter une collection", 
    "DescStyle4"           :  "Textes sous l'image avec bordures",
+   "nameNotFound"         :  '<span class="icon-warning" style="color: #d42525;"></span>colletion sans nom',
    "DescStyle5"           :  "Textes sous l'image avec images arrondies"
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js	(révision 13746)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js	(révision 13747)
@@ -54,5 +54,6 @@
    "addGalerieField": "ajouter une collection", 
    "DescStyle4"           :  "Texte sous l'image, bordures",
    "DescStyle5"           :  "Texte sous l'image, images arrondies (idéal images carrés)",
+   "nameNotFound"         :  '<span class="icon-warning" style="color: #d42525;"></span>colletion sans nom',
    "DescStyle6"           :  "Texte à côté de l'image"
 });
\ No newline at end of file
Index: src/less/imports/filePanel.less
===================================================================
--- src/less/imports/filePanel.less	(révision 13746)
+++ src/less/imports/filePanel.less	(révision 13747)
@@ -533,6 +533,50 @@
     margin: 10px 0 20px 12px;
     .clearfix();
 }
+.collectionName-lang {
+    float: left;
+    font-family: 'Open Sans', sans-serif;
+    margin-left: 20px;
+    .btn-group.lang .dropdown-menu {
+        position: absolute;
+        top: 43px; left: 0px;
+        width: 200px;
+        list-style:none;
+        padding: 0px;
+        overflow:hidden;
+        background-color: #E5E5E5;
+        .border-radius(4px);
+        & li a {
+            display: block;
+            line-height: 30px;
+            margin: 0;
+            padding: 0 0 0 10px;
+            color:#666;
+        }
+        & li a:hover, & li a.active {
+            background-color: #F5F5F5;
+            color: @fileColor;
+        }
+    }
+    .btn-group.lang .btn.dropdown-toggle {
+        width: 185px;
+        height: 40px;
+        background-color: #EEEEEE;
+        color: #666666;
+        line-height: 40px;
+        text-align: left;
+        .border-radius(4px);
+        margin-bottom: 20px;
+        & .caret {
+            vertical-align: middle;
+            margin-top: 0;
+            border-top-color: @fileColor;
+            position: absolute;
+            right: 20px;
+            top: 20px;
+        }
+    }
+}
 .shortcode-collection {
     float: left;
     font-family: 'Open Sans', sans-serif;
@@ -542,7 +586,7 @@
     color: #999;
     .border-radius(4px);
     line-height: 36px;
-    margin-left: 30px;
+    margin-left: 20px;
     padding: 0 15px;
     cursor: pointer;
 }
