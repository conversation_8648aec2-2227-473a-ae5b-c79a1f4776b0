Revision: r14172
Date: 2025-04-25 17:08:38 +0300 (zom 25 Apr 2025) 
Author: mpartaux 

## Commit message
test inversion des boutons

## Files changed

## Full metadata
------------------------------------------------------------------------
r14172 | mpartaux | 2025-04-25 17:08:38 +0300 (zom 25 Apr 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/NewsPanel.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/categorieList.html

test inversion des boutons
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Templates/NewsPanel.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/NewsPanel.html	(révision 14171)
+++ src/js/JEditor/NewsPanel/Templates/NewsPanel.html	(révision 14172)
@@ -29,12 +29,11 @@
             </div>
         </div>
         <!--liste des pages-->
-        
-
-        <a class="dialog-view-trigger addCategory">
-            <span>+</span><%=__("newsAddACategory")%>
-        </a>
-
+        <% if(canAddNews){ %>
+            <a class="dialog-view-trigger addArticle">
+                <span>+</span><%=__("newsAddArticle")%>
+            </a>
+        <% } %>
 	</aside>
     <div id="item-config" class="panel-container"></div>
   
Index: src/js/JEditor/NewsPanel/Templates/categorieList.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/categorieList.html	(révision 14171)
+++ src/js/JEditor/NewsPanel/Templates/categorieList.html	(révision 14172)
@@ -22,12 +22,11 @@
     </nav>
 </div>
 
-<% if(canAddNews){ %>
-    <a class="dialog-view-trigger addArticle">
-        <span>+</span><%=__("newsAddArticle")%>
-    </a>
-    <a class="dialog-view-trigger uploadArticle">
-        <span class="icon-add-image"></span>
-        <%=__("uploadLink")%>
-    </a>
-<% } %>
+<a class="dialog-view-trigger addCategory">
+    <span>+</span><%=__("newsAddACategory")%>
+</a>
+
+<a class="dialog-view-trigger uploadArticle">
+    <span class="icon-add-image"></span>
+    <%=__("uploadLink")%>
+</a>
