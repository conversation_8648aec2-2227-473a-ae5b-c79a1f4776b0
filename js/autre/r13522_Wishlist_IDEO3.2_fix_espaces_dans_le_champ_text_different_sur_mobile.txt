Revision: r13522
Date: 2024-11-26 14:43:36 +0300 (tlt 26 Nov 2024) 
Author: frahajanirina 

## Commit message
Wishlist:IDEO3.2:fix espaces dans le champ text different sur mobile

## Files changed

## Full metadata
------------------------------------------------------------------------
r13522 | frahajanirina | 2024-11-26 14:43:36 +0300 (tlt 26 Nov 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonOptionView.js

Wishlist:IDEO3.2:fix espaces dans le champ text different sur mobile
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonOptionView.js	(révision 13521)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonOptionView.js	(révision 13522)
@@ -67,11 +67,11 @@
                             this.render();
                         },
                         inputChange: function(event) {
-                            this.model.textOnMobile = event.target.value;
+                            this.model.textOnMobile = event.target.value.trim();
                             this.render();
                         },
                         onInputText: function(event) {
-                            var inputValue = event.target.value;
+                            var inputValue = event.target.value.trim();
                             var inputLength = inputValue.length;
                             this.model.trigger('input:change', inputLength);
                         }
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js	(révision 13521)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js	(révision 13522)
@@ -138,7 +138,7 @@
 
                             this.listenTo(buttonOptionsView, "save", function() {
                                 var isDifferentTextOnMobile = options.ButtonOption.isDifferentText;
-                                var textOnMobile = $('.text-different-input').val();
+                                var textOnMobile = $('.text-different-input').val().trim();
                                 if (isDifferentTextOnMobile && textOnMobile == '') {
                                     this.error({
                                         message: translate("errorWithoutTextOnMobile"),
