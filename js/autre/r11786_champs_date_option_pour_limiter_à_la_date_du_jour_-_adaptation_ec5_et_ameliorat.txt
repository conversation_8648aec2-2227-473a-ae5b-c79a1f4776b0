Revision: r11786
Date: 2024-01-12 16:00:42 +0300 (zom 12 Jan 2024) 
Author: rrakotoarinelina 

## Commit message
champs date : option pour limiter à la date du jour - adaptation ec5 et amelioration

## Files changed

## Full metadata
------------------------------------------------------------------------
r11786 | rrakotoarinelina | 2024-01-12 16:00:42 +0300 (zom 12 Jan 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/Field.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FieldBuilderView.js

champs date : option pour limiter à la date du jour - adaptation ec5 et amelioration
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/Field.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/Field.js	(révision 11785)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/Field.js	(révision 11786)
@@ -20,7 +20,9 @@
             else if(this.type === "url"){
                 this.placeholder ="https://www.exemple.com";
             } else if (this.type === "date" || this.type === "datetime-local") {
-                this.options = ['previousDate_true'];
+                if (this.options.indexOf('previousDate_true') === -1 && this.options.indexOf('previousDate_false') === -1) {
+                    this.options = ['previousDate_true'];
+                }
             }this.on("change:type",function () {
                 if(this.type==="name")
                     this.mandatory=true;
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FieldBuilderView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FieldBuilderView.js	(révision 11785)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FieldBuilderView.js	(révision 11786)
@@ -35,7 +35,7 @@
             var previousDate = true;
             if(this.checkPreviousDateOption(this.model.options) > -1 ){
                 var previousDateValue  = this.model.options[this.checkPreviousDateOption(this.model.options)];
-                previousDate = previousDateValue == 'previousDate_true' ? true : false;
+                previousDate = (previousDateValue === 'previousDate_true') ? true : false;
             }
 
             this.$el.html(this.template({field:this.model, previousDate:previousDate }));
@@ -54,19 +54,20 @@
         onChangePreviousDate: function (event) {
             var input = event.target;
             var isChecked = input.checked;
-        
             this.setPreviousDateValue(this.model.options, "previousDate_"+isChecked);
         },
         //return l'index de l'un de ces string dans le tableau model.options
         checkPreviousDateOption: function (options) {
-            return options.findIndex(item => item === 'previousDate_true' || item === 'previousDate_false');
+            return options.findIndex((function(item) {
+                return item === 'previousDate_true' || item === 'previousDate_false'; 
+            }));
         },
         //remplacement valeur 
         setPreviousDateValue: function (options, newValue) {
-            let hasPreviousDate = this.checkPreviousDateOption(options);
+            var indexPreviousDate = this.checkPreviousDateOption(options);
 
-            if ( parseInt(hasPreviousDate) > -1) {
-                options.splice(parseInt(hasPreviousDate), 1);
+            if ( parseInt(indexPreviousDate) > -1) {
+                options.splice(parseInt(indexPreviousDate), 1);
             }
             options.push(newValue);
            
