Revision: r11369
Date: 2023-10-05 11:18:56 +0300 (lkm 05 Okt 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: supprimer les scrollbar JS(suite)

## Files changed

## Full metadata
------------------------------------------------------------------------
r11369 | srazanandralisoa | 2023-10-05 11:18:56 +0300 (lkm 05 Okt 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Ancestors/Views/View.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Views/PageCollectionView.js

wishlist IDEO3.2: supprimer les scrollbar JS(suite)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Ancestors/Views/View.js
===================================================================
--- src/js/JEditor/Commons/Ancestors/Views/View.js	(révision 11368)
+++ src/js/JEditor/Commons/Ancestors/Views/View.js	(révision 11369)
@@ -170,20 +170,23 @@
                     return this;
                 },
                 scrollables: function (options) {
-                    this.destroyScrollables();
+                   // this.destroyScrollables();
                     this.dom[this.cid].scrollables = this.$('.scroll-container');
                     if (this.$el.hasClass('scroll-container'))
                         this.dom[this.cid].scrollables = this.dom[this.cid].scrollables.add(this.$el);
+                    if (this.$el.hasClass('available-items'))
+                        this.dom[this.cid].scrollables = this.dom[this.cid].scrollables.add(this.$el);
                     this.dom[this.cid].scrollables.each(function () {
-                        $(this).mCustomScrollbar(options);
+                        $(this).removeClass('scroll-container');
+                        $(this).addClass('scrollbar-classic');
                     });
                 },
                 updateScrollables: function () {
-                    if (this.dom[this.cid].scrollables) {
-                        this.dom[this.cid].scrollables.each(function () {
-                            $(this).mCustomScrollbar('update');
-                        });
-                    }
+                    // if (this.dom[this.cid].scrollables) {
+                    //     this.dom[this.cid].scrollables.each(function () {
+                    //         $(this).mCustomScrollbar('update');
+                    //     });
+                    // }
                 },
                 isVisible: function () {
                     return this.$el.is(':visible');
@@ -192,7 +195,8 @@
                     if (this.dom[this.cid].scrollables) {
                         this.dom[this.cid].scrollables.each(function () {
                             try {
-                                $(this).mCustomScrollbar('destroy');
+                                $(this).addClass('scroll-container');
+                                $(this).removeClass('scrollbar-classic');
                             } catch (e) {
 
                             }
Index: src/js/JEditor/PagePanel/Views/PageCollectionView.js
===================================================================
--- src/js/JEditor/PagePanel/Views/PageCollectionView.js	(révision 11368)
+++ src/js/JEditor/PagePanel/Views/PageCollectionView.js	(révision 11369)
@@ -19,7 +19,7 @@
 
   var PageCollectionView = ListView.extend({
     attributes: {
-      class: 'pageList scroll-container scrollbar-classic'
+      class: 'pageList scroll-container'
     },
     events: {
       'click ul li a[data-cid]': '_onPageClick',
@@ -257,7 +257,7 @@
     },
     render: function() {
       this._super();
-      // this.scrollables();
+      this.scrollables();
       return this;
     },
     show: function(animate) {
