Revision: r10321
Date: 2023-02-03 11:31:55 +0300 (zom 03 Feb 2023) 
Author: mpartaux 

## Commit message
add ckeditor text styles

## Files changed

## Full metadata
------------------------------------------------------------------------
r10321 | mpartaux | 2023-02-03 11:31:55 +0300 (zom 03 Feb 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js

add ckeditor text styles
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 10320)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 10321)
@@ -44,7 +44,11 @@
                                 __IDEO_CSS_PATH__ + 'ideo3-back.css'
                             ],
                             stylesSet: [
-                                { name: 'Highlight text 1', element: 'span', attributes: { 'class': 'text--hightlight-surface-mid' } }
+                                { name: 'Highlight 1', element: 'span', attributes: { 'class': 'txt-hightlight-1' } },
+                                { name: 'Highlight 2', element: 'span', attributes: { 'class': 'txt-hightlight-2' } },
+                                { name: 'Highlight 3', element: 'span', attributes: { 'class': 'txt-hightlight-3' } },
+                                { name: 'Highlight 4', element: 'span', attributes: { 'class': 'txt-hightlight-4' } },
+                                { name: 'Underline 1', element: 'span', attributes: { 'class': 'txt-underline-1' } }
                             ],
                             forcePasteAsPlainText: true,
                             pasteFromWordRemoveFontStyles: true,
