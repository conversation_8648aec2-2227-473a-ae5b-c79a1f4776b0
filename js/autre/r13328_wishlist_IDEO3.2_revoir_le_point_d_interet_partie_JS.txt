Revision: r13328
Date: 2024-10-22 15:35:25 +0300 (tlt 22 Okt 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: revoir le point d'interet (partie JS)

## Files changed

## Full metadata
------------------------------------------------------------------------
r13328 | srazanandralisoa | 2024-10-22 15:35:25 +0300 (tlt 22 Okt 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/focusEdit.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FocusEditView.js
   M /branches/ideo3_v2/integration/src/less/imports/filePanel.less

wishlist IDEO3.2: revoir le point d'interet (partie JS)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/FilePanel/Templates/focusEdit.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/focusEdit.html	(révision 13327)
+++ src/js/JEditor/FilePanel/Templates/focusEdit.html	(révision 13328)
@@ -2,9 +2,22 @@
     <div class="ifp-instruction">
         <%= __("focusPointInstruction")%>
     </div>
-    <div class="ifp-image">
-        <div class="ifp-image-wrapper"></div>
-        <div class="ifp-spinner"></div>
-        <div class="ifp-error"><%= __("focusPointError")%></div>
+    <div class="ifp-image-grid">
+        <div class="ifp-img-wrapper">
+            <div class="ifp-grid">
+                <img src="<%=imgUrl %>" alt="image" srcset="" class="background-image">
+                <div class="grid">
+                    <div class="grid-item <%=(focus==='left top')?'active':''%>" data-value="left top"></div>
+                    <div class="grid-item <%=(focus==='center top')?'active':''%>" data-value="center top"></div>
+                    <div class="grid-item <%=(focus==='right top')?'active':''%>" data-value="right top"></div>
+                    <div class="grid-item <%=(focus==='left center')?'active':''%>" data-value="left center"></div>
+                    <div class="grid-item <%=(focus==='center')?'active':''%>" data-value="center"></div>
+                    <div class="grid-item <%=(focus==='right center')?'active':''%>" data-value="right center"></div>
+                    <div class="grid-item <%=(focus==='left bottom')?'active':''%>" data-value="left bottom"></div>
+                    <div class="grid-item <%=(focus==='center bottom')?'active':''%>" data-value="center bottom"></div>
+                    <div class="grid-item <%=(focus==='right bottom')?'active':''%>" data-value="right bottom"></div>
+                </div>
+            </div>
+        </div>
     </div>
 </div>
\ No newline at end of file
Index: src/js/JEditor/FilePanel/Views/FocusEditView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FocusEditView.js	(révision 13327)
+++ src/js/JEditor/FilePanel/Views/FocusEditView.js	(révision 13328)
@@ -20,7 +20,8 @@
                  */
                 className: 'focus-editor',
                 events: {
-                    "dialogclose": "_onClose"
+                    "dialogclose": "_onClose",
+                    "click .grid-item" : "onFocusSelect"
                 },
                 constructor: function(options) {
                     var opts, defaultOptions;
@@ -59,21 +60,23 @@
                  */
                 render: function() {
                     this._super();
-                    this.$el.html( this._template() );
 
-                    var focus = this.model.get("focus");
+                    this.focus = this.model.get("focus");
 
-                    //init plugin
-                    this.$('#ideoFocusPoint').ideoFocusPoint({
+                    this.$el.html( this._template({
                         imgUrl : this.model.fileUrl,
-                        focusX : ( focus !== null ) ? focus.x : null,
-                        focusY : ( focus !== null ) ? focus.y : null
-                    });
+                        focus : ( this.focus !== null ) ? this.focus.value : null,
+                    }) );
 
-                    this.plugin = this.$('#ideoFocusPoint').data('plugin_ideoFocusPoint');
-
                     return this;
                 },
+                onFocusSelect : function (e){
+                    var $target = $(e.currentTarget);
+                    var targetUrl = $target.data('value');
+                    this.$('.grid-item').removeClass('active');
+                    $target.addClass('active');
+                    this.focus = {value: targetUrl};
+                },
                 /**
                  * Déclenché lorsque l'on annule la modif dans la dialog
                  * @private
@@ -89,11 +92,8 @@
                     console.log('save focus point in progress...');
 
                     var dialogFocus = this;
-                    var newFocus = dialogFocus.plugin.getFocusPoint();
 
-                    dialogFocus.plugin.saveProgress();
-
-                    this.model.save({focus: newFocus}, {
+                    this.model.save({focus: this.focus}, {
                         success: function () {
                             console.log('save focus point SUCCESS !');
                             dialogFocus.trigger(Events.FocusEditorEvents.SAVE);
@@ -111,7 +111,6 @@
                  * @private
                  */
                 _onClose: function() {
-                    this.plugin.destroy();
                     this.destroy();
                 }
 
Index: src/less/imports/filePanel.less
===================================================================
--- src/less/imports/filePanel.less	(révision 13327)
+++ src/less/imports/filePanel.less	(révision 13328)
@@ -2459,6 +2459,67 @@
 
 }
 /* ===================================================================
+  NEW FOCUS EDITOR
+   =================================================================== */
+
+   .ifp-image-grid{
+        display:flex;
+        text-align: center;
+        overflow: hidden;
+        padding: 30px;
+        min-height: 355px;
+        .ifp-img-wrapper{
+            cursor: crosshair;
+            display: inline-block;
+            position: relative;
+            margin: auto;
+            .ifp-grid {
+                width: fit-content;
+                height: fit-content;
+                position: relative;
+            /* Ajustez selon vos besoins */
+            .background-image {
+                max-width: 100%;
+                max-height: 355px;
+                display: inline-block;
+                vertical-align: middle;
+            }
+
+            .grid {
+                display: grid;
+                grid-template-columns: repeat(3, 1fr); /* 3 colonnes */
+                grid-template-rows: repeat(3, 1fr); /* 3 lignes */
+                position: absolute;
+                top: 0;
+                left: 0;
+                width: 100%;
+                height: 100%;
+                /*gap: 10px; /* Espace entre les items */
+            }
+            .grid-item:hover {
+                background-color: rgba(150, 150, 255, 0.8);
+                border: 5px solid #fff;/* Fond semi-transparent */
+            }
+            .grid-item.active {
+            background-color: rgba(255, 200, 0, 0.5);
+            border: 5px solid #ffea00;/* Fond semi-transparent */
+            }
+            .grid-item {
+                cursor: pointer;
+                background-color: rgba(255, 255, 255,0.1); /* Fond semi-transparent */
+                border: 3px solid #fff;
+                display: flex;
+                justify-content: center;
+                align-items: center;
+                font-size: 24px;
+            }
+        }
+    }
+   
+}
+
+  
+/* ===================================================================
    FOCUS EDITOR
    =================================================================== */
 .ifp-instruction {
