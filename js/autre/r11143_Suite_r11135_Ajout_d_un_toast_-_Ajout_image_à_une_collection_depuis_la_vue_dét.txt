Revision: r11143
Date: 2023-07-07 12:07:12 +0300 (zom 07 Jol 2023) 
Author: norajaonarivelo 

## Commit message
Suite r11135 : Ajout d'un toast - Ajout image à une collection depuis la vue détails

## Files changed

## Full metadata
------------------------------------------------------------------------
r11143 | norajaonarivelo | 2023-07-07 12:07:12 +0300 (zom 07 Jol 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/FilePanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/i18n.js

Suite r11135 : Ajout d'un toast - Ajout image à une collection depuis la vue détails
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/FilePanel/FilePanel.js
===================================================================
--- src/js/JEditor/FilePanel/FilePanel.js	(révision 11142)
+++ src/js/JEditor/FilePanel/FilePanel.js	(révision 11143)
@@ -34,6 +34,7 @@
     "JEditor/FilePanel/Views/CollectionDetailView",
     "i18n!./nls/i18n",
     // not in params
+    "jqueryPlugins/toaster",
     "owlCarousel",
 ], function (
         $,
@@ -249,11 +250,26 @@
                             this.dom[this.options.panelName].navCollections.addClass('active');
                             this.dom[this.options.panelName].navFiles.removeClass('active');
                             this.childViews.collectionManagerUIView.addCollection(arrFiles);
+                            this.ToastSuccess();
                             return false;
                         },
                         collectionAdded: function (cid) {
                             this.setLoading(false);
                         },
+                        ToastSuccess :function(){
+                            $.toast({
+                                text: translate("ImageAddToCollection"), 
+                                icon: 'icon-check-circle', 
+                                type:'success',
+                                appendTo:'#content',
+                                showHideTransition: 'fade', 
+                                hideAfter: 5000, 
+                                position: {top :350,right:50}, 
+                                textAlign: 'left', 
+                                allowToastClose:false,
+                                loader:false
+                            });
+                        },
                         /**
                          * insertion d'une sélection dans une collection
                          * création des nouvelles vues détaillées
@@ -270,6 +286,7 @@
                             this.childViews.collectionManagerUIView.listView = this.childViews.collectionListView;
                             this.childViews.collectionManagerUIView.addSelectionToCollection(cid, Files);
                             this.childViews.fileListView.selectNone();
+                            this.ToastSuccess();
                             //console.dir(collection);
                             //console.dir(arrFiles);
                             //this._initDetailsView();
Index: src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 11142)
+++ src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 11143)
@@ -124,5 +124,6 @@
     "introUrl" : "Ajouter une url utilisable dans une galerie (data-url)",
     "imageVideo": "C'est un fichier vidéo",
     "copy":"Copié",
-    "FileOn"    :   "fichier(s) sur"
+    "FileOn"    :   "fichier(s) sur",
+    "ImageAddToCollection" : " L’image à bien été ajoutée à la collection",
 });
\ No newline at end of file
Index: src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 11142)
+++ src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 11143)
@@ -128,5 +128,6 @@
     "introUrl" : "Ajouter une url utilisable dans une galerie (data-url)",
     "imageVideo": "C'est un fichier vidéo",
     "copy":"Copié",
-    "FileOn"    :   "fichier(s) sur"
+    "FileOn"    :   "fichier(s) sur",
+    "ImageAddToCollection" : " L’image à bien été ajoutée à la collection",
 });
\ No newline at end of file
Index: src/js/JEditor/FilePanel/nls/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/i18n.js	(révision 11142)
+++ src/js/JEditor/FilePanel/nls/i18n.js	(révision 11143)
@@ -130,7 +130,8 @@
         "introUrl" : "Add a url usable in a gallery (data-url)",
         "imageVideo": "C'est un fichier vidéo",
         "copy":"Copied",
-        "FileOn"    :   "file(s) on"
+        "FileOn"    :   "file(s) on",
+        "ImageAddToCollection" : " The image has been added to the collection",
     },
     "fr-fr": true, "fr-ca":true
 })
\ No newline at end of file
