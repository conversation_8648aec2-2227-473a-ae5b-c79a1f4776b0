Revision: r11451
Date: 2023-10-17 16:25:44 +0300 (tlt 17 Okt 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: upload de fonts via l'admin(partie JS)

## Files changed

## Full metadata
------------------------------------------------------------------------
r11451 | srazanandralisoa | 2023-10-17 16:25:44 +0300 (tlt 17 Okt 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Models/File.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Models/File.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileDetail.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileDetailView.js

wishlist IDEO3.2: upload de fonts via l'admin(partie JS)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Files/Models/File.js
===================================================================
--- src/js/JEditor/Commons/Files/Models/File.js	(révision 11450)
+++ src/js/JEditor/Commons/Files/Models/File.js	(révision 11451)
@@ -58,6 +58,16 @@
                                         return true;
                                     return false;
                                 },
+                                /**
+                                 * 
+                                 * @returns {Boolean}
+                                 */
+                                isFont: function() {
+                                    var regexpImg = /^font/;
+                                    if (regexpImg.test(this.mimeType))
+                                        return true;
+                                    return false;
+                                },
                                 isSvg: function() {
                                     var regexpImg = /^svg/;
                                     if(regexpImg.test(this.ext))
Index: src/js/JEditor/FilePanel/Models/File.js
===================================================================
--- src/js/JEditor/FilePanel/Models/File.js	(révision 11450)
+++ src/js/JEditor/FilePanel/Models/File.js	(révision 11451)
@@ -42,6 +42,16 @@
                                  * 
                                  * @returns {Boolean}
                                  */
+                                isFont: function() {
+                                    var regexpImg = /^font/;
+                                    if (regexpImg.test(this.mimeType))
+                                        return true;
+                                    return false;
+                                },
+                                /**
+                                 * 
+                                 * @returns {Boolean}
+                                 */
                                 isImg: function() {
                                     var regexpImg = /^image/;
                                     if (regexpImg.test(this.mimeType))
Index: src/js/JEditor/FilePanel/Templates/fileDetail.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileDetail.html	(révision 11450)
+++ src/js/JEditor/FilePanel/Templates/fileDetail.html	(révision 11451)
@@ -106,6 +106,8 @@
                 <% }else if(isVideo){%>
                     <div class="shortcode">ressources/videos/<%=name%></div>
                     <div class="shortcode">[[video_<%=id%>]]</div>
+                <% }else if(isFont){%>
+                    <div class="shortcode">ressources/fonts/<%=name%></div>
                 <% }else {%>
                     <div class="shortcode">ressources/fichiers/<%=name%></div>
                 <%}%>
Index: src/js/JEditor/FilePanel/Views/FileDetailView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileDetailView.js	(révision 11450)
+++ src/js/JEditor/FilePanel/Views/FileDetailView.js	(révision 11451)
@@ -55,7 +55,18 @@
 
                     this._super();
                     var currentLang = this.options.languages.get(this.lang);
-                    var params = {originalFileUrl: '', lang: currentLang, languages: this.options.languages.models, previous: this.previous, next: this.next, isFavicon: this.model.isFavicon(), isImg: this.model.isImg(), isVideo: this.model.isVideo(), previewClass: this.model.previewClass()};
+                    var params = {
+                        originalFileUrl: '',
+                        lang: currentLang, 
+                        languages: this.options.languages.models, 
+                        previous: this.previous, 
+                        next: this.next, 
+                        isFavicon: this.model.isFavicon(), 
+                        isImg: this.model.isImg(), 
+                        isVideo: this.model.isVideo(), 
+                        isFont: this.model.isFont(), 
+                        previewClass: this.model.previewClass()
+                    };
                     if (this.model.originalFile) {
                         var originalFile = this.options.fileList.get(this.model.originalFile);
                         params.originalFileUrl = originalFile.fileUrl;
Index: src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js
===================================================================
--- src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 11450)
+++ src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 11451)
@@ -56,8 +56,8 @@
             };
             $.event.props.push('dataTransfer');
             var settings = {
-                acceptedTypes: ['image', 'audio', 'video'],
-                acceptedExtensions: ['.doc', '.pdf', '.xls', '.zip', '.odp', '.odt', '.ods', '.json'],
+                acceptedTypes: ['image', 'audio', 'video','font'],
+                acceptedExtensions: ['.doc', '.pdf', '.xls', '.zip', '.odp', '.odt', '.ods', '.json','ttf','otf','woff','woff2','eot'],
                 refusedExtensions: ['.php', '.pl', '.rb', '.exe'],
                 refusedTypes: ['exploit'],
                 maxFiles: 5,
