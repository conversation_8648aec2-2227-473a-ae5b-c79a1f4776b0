Revision: r12036
Date: 2024-02-28 15:30:35 +0300 (lrb 28 Feb 2024) 
Author: rrakotoarinelina 

## Commit message
form : champs 'date range'

## Files changed

## Full metadata
------------------------------------------------------------------------
r12036 | rrakotoarinelina | 2024-02-28 15:30:35 +0300 (lrb 28 Feb 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/Field.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/FieldBuilder.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/Fields/DateRange.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/formBuilder.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FieldView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/form_block/contentOptions.less

form : champs 'date range'
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/Field.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/Field.js	(révision 12035)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/Field.js	(révision 12036)
@@ -1,5 +1,5 @@
 define(["JEditor/Commons/Ancestors/Models/Model"], function(Model) {
-    var allowedTypes = ["text", "number", "checkbox", "radio", "textarea", "email", "select", "tel", "separator","name","file","recipientlist","date","url","time","datetime-local"];
+    var allowedTypes = ["text", "number", "checkbox", "radio", "textarea", "email", "select", "tel", "separator","name","file","recipientlist","date","url","time","datetime-local","daterange"];
     var Field = Model.extend({
         defaults: function() {
             var ret = {
@@ -19,7 +19,7 @@
                 this.mandatory=true;
             else if(this.type === "url"){
                 this.placeholder ="https://www.exemple.com";
-            } else if (this.type === "date" || this.type === "datetime-local") {
+            } else if (this.type === "date" || this.type === "datetime-local" || this.type === "daterange") {
                 if (this.options.indexOf('previousDate_true') === -1 && this.options.indexOf('previousDate_false') === -1) {
                     this.options = ['previousDate_true'];
                 }
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/FieldBuilder.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/FieldBuilder.html	(révision 12035)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/FieldBuilder.html	(révision 12036)
@@ -49,7 +49,7 @@
             </label>
         </div>
 
-        <div class="checkbox previous-date" <%= field.type !== 'date' && field.type !== 'datetime-local' ? "style='display:none;'":'' %>>
+        <div class="checkbox previous-date" <%= field.type !== 'date' && field.type !== 'datetime-local' && field.type !== 'daterange' ? "style='display:none;'":'' %>>
             <input id="previous-date-<%=field.cid%>" name="previous-date" class="field-input" <%=previousDate?'checked="checked"':''%> type="checkbox" value="1" />
             <label class="checkbox-lbl" for="previous-date-<%=field.cid%>">
                 <span class="switch">
@@ -56,7 +56,7 @@
                     <span></span>
                 </span>
                 <span class="text">
-                    <%= __("previousDate") %>
+                    <%= __("previousDate") %> 
                 </span>
             </label>
         </div>
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/Fields/DateRange.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/Fields/DateRange.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/Fields/DateRange.html	(révision 12036)
@@ -0,0 +1,11 @@
+<label><%=field.mandatory?field.label+' *':field.label%><% if(field.description){%><span class="label-desc"><%=field.description%></span><%}%></label>
+<div class="" style="display: flex; flex-direction: row; align-items: stretch; width: 100%;">
+    <div class="" style="display: flex; align-items: center;  width: 100%">
+      <span style="margin-right:   5px; vertical-align: middle;"><%= __("from") %></span>
+      <input type="date" placeholder="<%=field.placeholder%>" <%= field.required?'required="required"':''%> />
+    </div>
+    <div class="" style="display: flex; align-items: center; width: 100%;">
+      <span style="margin-right:   5px; margin-left: 5px; vertical-align: middle;"><%= __("to") %></span>
+      <input type="date" placeholder="<%=field.placeholder%>" <%= field.required?'required="required"':''%> />
+    </div>
+  </div>
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/formBuilder.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/formBuilder.html	(révision 12035)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/formBuilder.html	(révision 12036)
@@ -58,6 +58,12 @@
                     </a>
                 </li>
                 <li>
+                    <a href="#" class="" data-type="daterange">
+                        <span class="icon-form-calendar"></span>
+                        <%=__("dateRangeField")%>
+                    </a>
+                </li>
+                <li>
                     <a href="#" class="" data-type="time">
                         <span class="icon-form-time"></span>
                         <%=__("timeField")%>
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FieldView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FieldView.js	(révision 12035)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FieldView.js	(révision 12036)
@@ -5,8 +5,10 @@
     "text!../Templates/Fields/Text.html",
     "text!../Templates/Fields/Textarea.html",
     "text!../Templates/Fields/Select.html",
-    "text!../Templates/Fields/Separator.html"
-],function (_,View,radioTpl,textTpl,textareaTpl,selectTpl,separatorTpl) {
+    "text!../Templates/Fields/Separator.html",
+    "text!../Templates/Fields/DateRange.html",
+    "i18n!../nls/i18n"
+],function (_,View,radioTpl,textTpl,textareaTpl,selectTpl,separatorTpl, daterangeTpl,i18n) {
     textTpl=_.template(textTpl);
     radioTpl=_.template(radioTpl);
     textareaTpl=_.template(textareaTpl);
@@ -29,13 +31,19 @@
             date:textTpl,
             url:textTpl,
             time:textTpl,
-            "datetime-local":textTpl
+            "datetime-local":textTpl,
         },
         initialize:function () {
             View.prototype.initialize.call(this);
+            //ajouter à daterangeTpl une traduction
+            this.template = this.buildTemplate(daterangeTpl,i18n);
         },
         render:function () {
-            this.$el.html(this.templates[this.model.type]({field:this.model}));
+            if (this.model.type == "daterange") {
+                this.$el.html(this.template({field:this.model}));
+            }else{
+                this.$el.html(this.templates[this.model.type]({field:this.model}));
+            }
             return this;
         }
     });
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js	(révision 12035)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js	(révision 12036)
@@ -79,4 +79,8 @@
     "datetime-local"    :   "date et heure ",
     "timeField" :   "Heure",
     "time"      :   "heure",
+    "dateRangeField":"Intervalle de dates",
+    'from':'Du',
+    'to':'au',
+    "daterange":"Intervalle de dates",
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js	(révision 12035)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js	(révision 12036)
@@ -79,4 +79,8 @@
     "datetime-local"    :   "date et heure ",
     "timeField" :   "Heure",
     "time"      :   "heure",
+    "dateRangeField":"Intervalle de dates",
+    'from':'Du',
+    'to':'au',
+    "daterange":"Intervalle de dates",
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js	(révision 12035)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js	(révision 12036)
@@ -84,7 +84,11 @@
         "timeField" :   "Time",
         "time"      :   "time",
         "DateTimeField" :   "Date and time",
-        "datetime-local"    :   "date and time"
+        "datetime-local"    :   "date and time",
+        "dateRangeField":"Interval of dates",
+        'from':'From',
+        'to':'to',
+        "daterange":"Interval of dates",
     },
     "fr-fr":true,
     "fr-ca": true
Index: src/less/imports/form_block/contentOptions.less
===================================================================
--- src/less/imports/form_block/contentOptions.less	(révision 12035)
+++ src/less/imports/form_block/contentOptions.less	(révision 12036)
@@ -119,6 +119,7 @@
                 &.text,
                 &.name,
                 &.date,
+                &.daterange,
                 &.number,
                 &.textarea,
                 &.email,
