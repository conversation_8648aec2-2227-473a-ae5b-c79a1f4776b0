Revision: r13874
Date: 2025-02-19 15:20:14 +0300 (lrb 19 Feb 2025) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2 : bug disparition du toast si on deplace des section dans la page

## Files changed

## Full metadata
------------------------------------------------------------------------
r13874 | srazanandralisoa | 2025-02-19 15:20:14 +0300 (lrb 19 Feb 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Ancestors/ContentView.js

wishlist IDEO3.2 : bug disparition du toast si on deplace des section dans la page
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Ancestors/ContentView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Ancestors/ContentView.js	(révision 13873)
+++ src/js/JEditor/PagePanel/Contents/Ancestors/ContentView.js	(révision 13874)
@@ -48,6 +48,7 @@
                 this._super();
                 this.childViews = [];
                 this.renderError = null;
+                this.popupView = null;
 
                 this.byViewCID = {};
                 this.elements = [];
@@ -467,8 +468,11 @@
             renderChildView: function(view) {
                 view.$el.data('model', view.model);
                 if (view.model.contentType === 'section'){
-                     if (view.model.isPopup()) // view.el.insertBefore(this.childContainer); 
-                        this.childContainer.children('#content-popup').append(view.el);    
+                     if (view.model.isPopup()) {
+                            // view.el.insertBefore(this.childContainer); 
+                            this.childContainer.children('#content-popup').append(view.el);
+                            this.popupView = view;  
+                     } 
                      else   this.childContainer.append(view.el);
                 }
                 else  this.childContainer.append(view.el);
@@ -548,6 +552,11 @@
                         if (childView.sensors)
                             childView.sensors.droppable('enable');
                     }
+                    if (order.length !== this.childViews.length && this.popupView) {
+                        orderNew = [this.popupView.model.cid];
+                        orderNew = orderNew.concat(order);
+                        order = orderNew;
+                    }
                 } catch (e) {
                     console.debug('error thrown but not fatal');
                 }
