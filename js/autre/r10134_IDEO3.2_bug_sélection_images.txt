Revision: r10134
Date: 2023-01-12 10:56:15 +0300 (lkm 12 Jan 2023) 
Author: norajaonarivelo 

## Commit message
IDEO3.2 bug sélection images

## Files changed

## Full metadata
------------------------------------------------------------------------
r10134 | norajaonarivelo | 2023-01-12 10:56:15 +0300 (lkm 12 Jan 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileList.html

IDEO3.2 bug sélection images
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/FilePanel/Templates/fileList.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileList.html	(révision 10133)
+++ src/js/JEditor/FilePanel/Templates/fileList.html	(révision 10134)
@@ -7,6 +7,7 @@
             %>
             <%if(!file.isLogo){%>
             <div class="menu-wrapper file image <%=(selected[file.cid]===true)?'selected':''%> <%=(file.previewClass())%>" data-cid="<%=file.cid %>"  <%if(file.isImg()){%>style="background-image: url(<%= file.thumb %>);"<%}%>>
+                <span class="select"><span class="icon-check"></span></span>
                 <%if(!file.isImg()){%>
                 <span class="icon-file ext">
                     <span class="extLabel"><%=file.ext%></span>
@@ -14,7 +15,6 @@
                 </span>
                 <%}%>
                 <span class="masque"></span>
-
                 <ul class="menu2">
                         <li class="action delete" data-cid="<%=file.cid %>" >
                             <span class="icon-bin"></span>
