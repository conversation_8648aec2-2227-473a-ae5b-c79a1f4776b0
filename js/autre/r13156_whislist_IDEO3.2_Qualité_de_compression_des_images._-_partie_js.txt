Revision: r13156
Date: 2024-10-07 16:10:15 +0300 (lts 07 Okt 2024) 
Author: rrakotoarinelina 

## Commit message
whislist IDEO3.2 : Qualité de compression des images. - partie js

## Files changed

## Full metadata
------------------------------------------------------------------------
r13156 | rrakotoarinelina | 2024-10-07 16:10:15 +0300 (lts 07 Okt 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/assets/ACLs/admin.json
   M /branches/ideo3_v2/integration/assets/ACLs/lpadmin.json
   M /branches/ideo3_v2/integration/assets/ACLs/root.json
   M /branches/ideo3_v2/integration/assets/ACLs/superadmin.json
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Models/Params.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/ParamsPanel.js
   A /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Templates/ImageCompressionQuality.html
   A /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/ImageCompressionQualityView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/i18n.js

whislist IDEO3.2 : Qualité de compression des images. - partie js
------------------------------------------------------------------------

## Diff
Index: assets/ACLs/admin.json
===================================================================
--- assets/ACLs/admin.json	(révision 13155)
+++ assets/ACLs/admin.json	(révision 13156)
@@ -214,5 +214,9 @@
  "change_news_layout": {
    "value": false,
    "comparison": null
-  }
+  },
+  "access_image_compression": {
+    "value": false,
+    "comparison": null
+ }
 }
Index: assets/ACLs/lpadmin.json
===================================================================
--- assets/ACLs/lpadmin.json	(révision 13155)
+++ assets/ACLs/lpadmin.json	(révision 13156)
@@ -214,6 +214,10 @@
      "change_news_layout": {
        "value": false,
        "comparison": null
+     },
+     "access_image_compression": {
+        "value": false,
+        "comparison": null
      }
    }
    
\ No newline at end of file
Index: assets/ACLs/root.json
===================================================================
--- assets/ACLs/root.json	(révision 13155)
+++ assets/ACLs/root.json	(révision 13156)
@@ -214,5 +214,9 @@
      "change_news_layout": {
        "value": true,
        "comparison": null
+     },
+     "access_image_compression": {
+        "value": true,
+        "comparison": null
      }
 }
Index: assets/ACLs/superadmin.json
===================================================================
--- assets/ACLs/superadmin.json	(révision 13155)
+++ assets/ACLs/superadmin.json	(révision 13156)
@@ -214,5 +214,9 @@
  "change_news_layout": {
    "value": true,
    "comparison": null
-  }
+  },
+  "access_image_compression": {
+   "value": false,
+   "comparison": null
 }
+}
Index: src/js/JEditor/ParamsPanel/Models/Params.js
===================================================================
--- src/js/JEditor/ParamsPanel/Models/Params.js	(révision 13155)
+++ src/js/JEditor/ParamsPanel/Models/Params.js	(révision 13156)
@@ -96,7 +96,8 @@
             Favicon: null,
             Opengraph:null,
             IconsCollection: "outline",
-            CustomShortcode: null
+            CustomShortcode: null,
+            ImageCompressionQuality: "low",
         },
         constructor: function () {
             if (arguments.callee.caller !== Params.getInstance)
Index: src/js/JEditor/ParamsPanel/ParamsPanel.js
===================================================================
--- src/js/JEditor/ParamsPanel/ParamsPanel.js	(révision 13155)
+++ src/js/JEditor/ParamsPanel/ParamsPanel.js	(révision 13156)
@@ -3,7 +3,8 @@
     "underscore",
     "JEditor/Commons/Ancestors/Views/PanelView",
     "JEditor/Commons/Events",
-    "text!./Templates/ParamsPanel.html", "i18n!./nls/i18n",
+    "text!./Templates/ParamsPanel.html",
+    "i18n!./nls/i18n",
     "./Views/SocialNetworks",
     "./Views/SeoView",
     "./Views/MobileStoreView",
@@ -14,12 +15,13 @@
     "./Views/IconsCollectionView",
     "./Views/ThemeSiteView",
     "./Views/CustomShortcode",
+    "./Views/ImageCompressionQualityView",
     "./Models/Params",
 
     //hidden
     "jqueryui/datepicker"
 ],
-        function ($, _, PanelView, Events, ParamsPanelTemplate, translate, SocialNetworks,SEOView, MobileStoreView, PolitiqueConfidentialiteView,DonneeStructureeView, MarqueClientView, FontsGoogleView, IconsCollectionView,ThemeSiteView,CustomShortcode,Params) {
+        function ($, _, PanelView, Events, ParamsPanelTemplate, translate, SocialNetworks,SEOView, MobileStoreView, PolitiqueConfidentialiteView,DonneeStructureeView, MarqueClientView, FontsGoogleView, IconsCollectionView,ThemeSiteView,CustomShortcode,ImageCompressionQualityView,Params) {
             var SocialPanel = PanelView.extend(
                     /**
                      * @lends JEditor.Panels.MessagePanel.prototype
@@ -113,10 +115,16 @@
                                             icon:"icon-html",
                                             title:translate("donneesStructurees") 
                                         }
-                                           
                                         
+                                    }
+                                    if(this.app.user.can('access_image_compression')) {
+                                        this.menuEntries.ImageCompressionQuality = 
+                                        {
+                                            object: new ImageCompressionQualityView({model:this.params}),
+                                            icon:"icon-html",
+                                            title:translate("qualityCompressionTitle")
+                                        }
                                     } 
-                                    //console.log(this.menuEntries) ;
                                     for( var menuEntry in this.menuEntries){
                                         this.childViews[menuEntry]=(this.menuEntries[menuEntry].object);
                                     }
Index: src/js/JEditor/ParamsPanel/Templates/ImageCompressionQuality.html
===================================================================
--- src/js/JEditor/ParamsPanel/Templates/ImageCompressionQuality.html	(nonexistent)
+++ src/js/JEditor/ParamsPanel/Templates/ImageCompressionQuality.html	(révision 13156)
@@ -0,0 +1,36 @@
+<div class="main-content__wrapper ">
+    <!--
+        ******************************************
+        créer nouvelle childView marque client,
+        y déplacer ce contenu
+        ******************************************
+        -->
+        <div class="ideo-title">
+            <h1>
+                <%=__("qualityCompressionTitle")%>
+                <span><%=__("qualityCompressionCompression")%></span>
+            </h1>
+        </div>
+
+        <div id="content" class="inline-params thin-border  radius  shadow">
+            <span class="inline-params__name">
+                <svg width="70px" height="70px" viewBox="0 0 70 70" enable-background="new 0 0 70 70" xml:space="preserve">
+                <path fill="#34495E" d="M35,0C15.67,0,0,15.67,0,35s15.67,35,35,35s35-15.67,35-35S54.33,0,35,0z M35,67C17.327,67,3,52.673,3,35S17.327,3,35,3s32,14.327,32,32S52.673,67,35,67z M22.436,35.002l4.769-4.528c0.556-0.527,0.556-1.385,0-1.912c-0.556-0.533-1.464-0.533-2.02,0l-5.782,5.484c-0.556,0.527-0.556,1.385,0,1.912l5.788,5.501c0.561,0.527,1.47,0.527,2.025,0s0.556-1.385,0-1.918L22.436,35.002z M38.728,28.149c-0.7-0.341-1.563-0.082-1.927,0.582l-6.152,11.305c-0.365,0.67-0.087,1.489,0.613,1.829c0.694,0.347,1.563,0.083,1.921-0.582l6.158-11.304C39.699,29.314,39.428,28.49,38.728,28.149z M44.77,28.562c-0.557-0.527-1.465-0.527-2.02,0c-0.563,0.527-0.563,1.385,0,1.912l4.779,4.539l-4.769,4.528c-0.562,0.533-0.562,1.391,0,1.918c0.556,0.527,1.458,0.527,2.021,0l5.775-5.484c0.561-0.527,0.561-1.391,0-1.918L44.77,28.562z"></path>
+                </svg>
+                <%=__("qualityCompression")%>
+            </span>
+            <label>
+                <span class="custom-input">
+                    <select id="qualityCompression" name="ImageCompressionQuality" class="neutral-input" data-autosave="true">
+                        <option value="low" <%=(ImageCompressionQuality==='low') ? 'selected':'' %> ><%=__("lowQuality") %></option>
+                        <option value="high" <%=(ImageCompressionQuality==='high') ? 'selected':'' %> ><%=__("highQuality") %></option>
+                    </select>
+                </span>
+            </label>
+        </div>
+        <!-- 
+        ******************
+        end new childView
+        ******************
+        -->
+    </div>
\ No newline at end of file

Property changes on: src/js/JEditor/ParamsPanel/Templates/ImageCompressionQuality.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/ParamsPanel/Views/ImageCompressionQualityView.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/ImageCompressionQualityView.js	(nonexistent)
+++ src/js/JEditor/ParamsPanel/Views/ImageCompressionQualityView.js	(révision 13156)
@@ -0,0 +1,34 @@
+define(['jquery',
+    "underscore", 
+    'JEditor/Commons/Ancestors/Views/View',
+    'text!../Templates/ImageCompressionQuality.html',
+    'i18n!../nls/i18n'], function (
+        $,
+        _,
+        View, 
+        template,
+        translate) {
+        var ImageCompressionQuality = View.extend({
+            events: {
+                'change #qualityCompression': 'onCompressionChange'
+            },
+            initialize: function () {
+                this._super();
+                this._template = this.buildTemplate(template,translate);
+            },
+           
+            onCompressionChange: function (model) {
+                var value = this.$('#qualityCompression').val();
+                this.model.attributes['ImageCompressionQuality'] = value;
+                this.model.save();  
+            },
+            render: function () {
+                this.undelegateEvents();
+                this.$el.html(this._template(this.model.toJSON()));
+                this.delegateEvents();
+                return this;
+            }
+        
+        });
+        return ImageCompressionQuality;
+    });
\ No newline at end of file

Property changes on: src/js/JEditor/ParamsPanel/Views/ImageCompressionQualityView.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 13155)
+++ src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 13156)
@@ -115,5 +115,10 @@
         "bodyEnd": "Fin du body",
         "customPosition": "Position personnalisé",
         "CustomShortcodePosition": "Position du shortcode",
-        "CustomShortcodeName": "Nom du shortcode"
+        "CustomShortcodeName": "Nom du shortcode",
+        "qualityCompressionTitle" : "Qualité de la compression d'image",
+        "qualityCompressionCompression" : "Choisissez la qualité de compression d'image à utiliser sur le site",
+        "qualityCompression" : "Qualité de compression",
+        "lowQuality" : "Basse qualité (par défaut)",
+        "highQuality" : "Haute qualité",
     });
Index: src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 13155)
+++ src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 13156)
@@ -121,4 +121,9 @@
         "CustomShortcodePosition": "Position du shortcode",
         "CustomShortcodeName": "Nom du shortcode",
         "confirmDeleteShortcode": "Vous \u00eates sur le point de supprimer d\u00e9finitivement un shortcode</br><strong><% name %></strong>",
+        "qualityCompressionTitle" : "Qualité de la compression d'image",
+        "qualityCompressionCompression" : "Choisissez la qualité de compression d'image à utiliser sur le site",
+        "qualityCompression" : "Qualité de compression",
+        "lowQuality" : "Basse qualité (par défaut)",
+        "highQuality" : "Haute qualité",
     });
Index: src/js/JEditor/ParamsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 13155)
+++ src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 13156)
@@ -116,6 +116,11 @@
         "CustomShortcodePosition": "Shortcode position",
         "CustomShortcodeName": "Shortcode name",
         "confirmDeleteShortcode": "You are about to permanently delete a shortcode</br><strong><% name %></strong>",
+        "qualityCompressionTitle" : "Image compression quality",
+        "qualityCompressionCompression" : "Choose the image compression quality to be used on the site",
+        "qualityCompression" : "Compression quality",
+        "lowQuality" : "Low quality (default)",
+        "highQuality" : "High quality",
     },
     "fr-fr": true,
     "fr-ca": true
