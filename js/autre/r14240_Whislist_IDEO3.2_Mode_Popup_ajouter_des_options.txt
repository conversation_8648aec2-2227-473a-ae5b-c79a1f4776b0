Revision: r14240
Date: 2025-05-14 09:18:28 +0300 (lrb 14 Mey 2025) 
Author: rrakotoarinelina 

## Commit message
Whislist IDEO3.2 : Mode Popup : ajouter des options

## Files changed

## Full metadata
------------------------------------------------------------------------
r14240 | rrakotoarinelina | 2025-05-14 09:18:28 +0300 (lrb 14 Mey 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/Models/PopupStyle.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/Templates/popupStyle.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/Views/PopupStyleView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/nls/i18n.js

Whislist IDEO3.2 : Mode Popup : ajouter des options
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Options/Models/PopupStyle.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/Models/PopupStyle.js	(révision 14239)
+++ src/js/JEditor/PagePanel/Contents/Options/Models/PopupStyle.js	(révision 14240)
@@ -16,7 +16,7 @@
                  * @lends PopupStyle.prototype
                  */
                         {
-                            defaults: {optionType: 'popupStyle', size:'medium', display:'toast',color:'surface',alignment:'bottom-right', priority: 60},
+                            defaults: {optionType: 'popupStyle', size:'medium', display:'toast',color:'surface',alignment:'bottom-right',displayCloseButton:true,closeOnBackdropClick:true, priority: 60},
                             initialize: function() {
                                 this._super();
                             },
@@ -27,7 +27,7 @@
                             },
                             translate: translate
                         });
-                PopupStyle.SetAttributes(['size','display','color','alignment']);
+                PopupStyle.SetAttributes(['size','display','color','alignment','displayCloseButton','closeOnBackdropClick']);
                 PopupStyle.tanslate = translate;
                 return PopupStyle;
             });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Options/Templates/popupStyle.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/Templates/popupStyle.html	(révision 14239)
+++ src/js/JEditor/PagePanel/Contents/Options/Templates/popupStyle.html	(révision 14240)
@@ -1,17 +1,18 @@
 <div class="panel-option-container  animated popup-size">
     <article class="panel-option">
-        <header>
-            <h3 class="option-name">
-                <span class="icon-button-size icon-midsize"></span>
-                <%=__("sizePopup")%>
-            </h3>
-            <span class="panel-content-legend">
-                <%=__("sizePopupLegend")%>
-            </span>
-        </header>
-        <div class="option-content ">
-            <div class="controlPanel-selector">
-                <div class="option radio">
+        <div>
+            <header>
+                <h3 class="option-name">
+                    <span class="icon-button-size icon-midsize"></span>
+                    <%=__("sizePopup")%>
+                </h3>
+                <span class="panel-content-legend">
+                    <%=__("sizePopupLegend")%>
+                </span>
+            </header>
+            <div class="option-content ">
+                <div class="controlPanel-selector">
+                    <div class="option radio">
                         <div class="button-size-radio">
                             <label for="tiny" class="inline-block-label">
                                 <input id="tiny" type="radio" class="field-input" value="tiny" name="size" <%=size==="tiny"? ' checked="checked" ':'' %> />
@@ -19,47 +20,46 @@
                                 <div class="inline-block-label__top">
                                     <span class="i-block-social-size_s"></span>
                                 </div>
-                                <div class="inline-block-label__bottom">
-                                    <span class="icon-radio-inactive"></span>
-                                    <span class="icon-radio-active"></span>
-                                </div>
-                            </label>
-                            <label for="small" class="inline-block-label">
-                                <input id="small" type="radio" class="field-input" value="small" name="size" <%=size==="small"? ' checked="checked" ':'' %> />
-                                <div class="inline-block-label__top">
-                                    <span class="i-block-social-size_m"></span>
-                                </div>
-                                <div class="inline-block-label__bottom">
-                                    <span class="icon-radio-inactive"></span>
-                                    <span class="icon-radio-active"></span>
-                                </div>
-                            </label>
-                            <label for="medium" class="inline-block-label">
-                                <input id="medium" type="radio" class="field-input" value="medium" name="size" <%=size==="medium"? ' checked="checked" ':'' %> />
-                                <div class="inline-block-label__top">
-                                    <span class="i-block-social-size_l"></span>
-                                </div>
-                                <div class="inline-block-label__bottom">
-                                    <span class="icon-radio-inactive"></span>
-                                    <span class="icon-radio-active"></span>
-                                </div>
-                            </label>
-                            <label for="large" class="inline-block-label">
-                                <input id="large" type="radio" class="field-input" value="large" name="size" <%=size==="large"? ' checked="checked" ':'' %> />
-                                <div class="inline-block-label__top">
-                                    <span class="i-block-social-size_xl"></span>
-                                </div>
-                                <div class="inline-block-label__bottom">
-                                    <span class="icon-radio-inactive"></span>
-                                    <span class="icon-radio-active"></span>
-                                </div>
-                            </label>
-                        </div>
+                                    <div class="inline-block-label__bottom">
+                                        <span class="icon-radio-inactive"></span>
+                                        <span class="icon-radio-active"></span>
+                                    </div>
+                                </label>
+                                <label for="small" class="inline-block-label">
+                                    <input id="small" type="radio" class="field-input" value="small" name="size" <%=size==="small"? ' checked="checked" ':'' %> />
+                                    <div class="inline-block-label__top">
+                                        <span class="i-block-social-size_m"></span>
+                                    </div>
+                                    <div class="inline-block-label__bottom">
+                                        <span class="icon-radio-inactive"></span>
+                                        <span class="icon-radio-active"></span>
+                                    </div>
+                                </label>
+                                <label for="medium" class="inline-block-label">
+                                    <input id="medium" type="radio" class="field-input" value="medium" name="size" <%=size==="medium"? ' checked="checked" ':'' %> />
+                                    <div class="inline-block-label__top">
+                                        <span class="i-block-social-size_l"></span>
+                                    </div>
+                                    <div class="inline-block-label__bottom">
+                                        <span class="icon-radio-inactive"></span>
+                                        <span class="icon-radio-active"></span>
+                                    </div>
+                                </label>
+                                <label for="large" class="inline-block-label">
+                                    <input id="large" type="radio" class="field-input" value="large" name="size" <%=size==="large"? ' checked="checked" ':'' %> />
+                                    <div class="inline-block-label__top">
+                                        <span class="i-block-social-size_xl"></span>
+                                    </div>
+                                    <div class="inline-block-label__bottom">
+                                        <span class="icon-radio-inactive"></span>
+                                        <span class="icon-radio-active"></span>
+                                    </div>
+                                </label>
+                            </div>
+                    </div>
                 </div>
             </div>
-        </div>
-        
-  
+        </div> 
         <!--  style alignment -->
         
         <!-- .gallery-template-option .category-content.radio-transformed -->
@@ -175,7 +175,6 @@
             </div>
         </div>
 
-
         <!--  style pour toast  -->
         <div class="alignment-container">
             <header>
@@ -239,6 +238,43 @@
             </div>
 
         </div>
+
+        <!--  style pour toast  -->
+        <div class="options-container">
+            <header>
+                <h3 class="option-name">
+                    <span class="icon-desktop icon-midsize"></span>
+                    <%=__("optionsPopup")%>
+                </h3>
+                <!-- <span class="panel-content-legend">
+                    <%=__("alignmentPopupLegend")%>
+                </span> -->
+            </header>
+            <div class="option-content">
+                <% var _id=_.uniqueId('option') %>
+                <% var _id2=_.uniqueId('option') %>
+                <div class="">
+                    <input type="checkbox" class="blue-bg " name="displayCloseButton" id="<%=_id%>" <%=displayCloseButton===true?' checked="checked" ':'' %>>
+                    <label for="<%=_id%>">
+                        <span class="checkbox-wrapper">
+                            <span class="icon-unchecked"></span>
+                            <span class="icon-checked"></span>
+                        </span>
+                        <span class="text"><%=__("closeButtonOption")%></span>
+                    </label>
+                </div>
+                <div class="">
+                    <input type="checkbox" class="blue-bg " name="closeOnBackdropClick" id="<%=_id2%>" <%=closeOnBackdropClick===true?' checked="checked" ':'' %>>
+                    <label for="<%=_id2%>">
+                        <span class="checkbox-wrapper">
+                            <span class="icon-unchecked"></span>
+                            <span class="icon-checked"></span>
+                        </span>
+                        <span class="text"><%=__("backdropClickOption")%></span>
+                    </label>
+                </div>
+            </div>
+        </div>
     </article>
 </div>
 
Index: src/js/JEditor/PagePanel/Contents/Options/Views/PopupStyleView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/Views/PopupStyleView.js	(révision 14239)
+++ src/js/JEditor/PagePanel/Contents/Options/Views/PopupStyleView.js	(révision 14240)
@@ -14,7 +14,10 @@
                             "change input[type=radio][name=size]": "sizeChange",
                             'click .display-choice': "displayChange",
                             'click .color-choice': "colorChange",
+                            'click .color-choice': "colorChange",
                             "change input[type=radio][name=alignment]": "alignmentChange",
+                            "change input[type=checkbox][name=displayCloseButton]": "onModalOptionChange",
+                            "change input[type=checkbox][name=closeOnBackdropClick]": "onModalOptionChange",
                         },
                         /**
                          * initialise l'objet
@@ -27,8 +30,9 @@
                          * actualise l'affichage de la vue
                          */
                         render: function() {
-       
-                            var templateVars = {size:this.model.size,display:this.model.display,color:this.model.color,alignement:this.model.alignment};
+                            var templateVars = {size:this.model.size,display:this.model.display,color:this.model.color,alignement:this.model.alignment,
+                                displayCloseButton:this.model.displayCloseButton,closeOnBackdropClick:this.model.closeOnBackdropClick
+                            };
                             this.$el.html(this.template(templateVars));
                             this.toggleColorAlignment(this.model.display);
                             this.scrollables();
@@ -70,13 +74,31 @@
                             if (display ==="modal") {
                                 this.$(".alignment-container").css("display", "none");
                                 this.$(".color-container").css("display", "none");
+                                this.$(".options-container").css("display", "block");
                                 this.model.alignement="bottom-right";
                                 this.model.color="surface";
                             }else{
+                                this.$(".options-container").css("display", "none");
                                 this.$(".alignment-container").css("display", "block");
                                 this.$(".color-container").css("display", "block");
                             }
                         },
+                        /**
+                         * Gère les changements des options de la fenêtre modale (displayCloseButton et closeOnBackdropClick)
+                         * @param {Event} event L'événement de changement
+                         */
+                        onModalOptionChange: function(event) {
+                            var input = event.target;
+                            var name = input.name;
+                            var value = input.checked;
+                            
+                            // Update the appropriate model property based on the checkbox name
+                            if (name === 'displayCloseButton') {
+                                this.model.displayCloseButton = value;
+                            } else if (name === 'closeOnBackdropClick') {
+                                this.model.closeOnBackdropClick = value;
+                            }
+                        },
             }
     );
     PopupStyleOptionView.translate = translate;
Index: src/js/JEditor/PagePanel/Contents/Options/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/nls/fr-ca/i18n.js	(révision 14239)
+++ src/js/JEditor/PagePanel/Contents/Options/nls/fr-ca/i18n.js	(révision 14240)
@@ -97,5 +97,8 @@
     "delayLabel" : "Délais",
     "delayDesc" : "Ajuster le Délais avant que l'animation se déclenche",
     "durationLabel" : "Durée",
-    "durationDesc":"Ajuster la durée de l'animation"
+    "durationDesc":"Ajuster la durée de l'animation",
+    "optionsPopup": "Options",
+    "closeButtonOption": "Bouton de fermeture",
+    "backdropClickOption": "Clicker sur le fond pour fermer"
 });
Index: src/js/JEditor/PagePanel/Contents/Options/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/nls/fr-fr/i18n.js	(révision 14239)
+++ src/js/JEditor/PagePanel/Contents/Options/nls/fr-fr/i18n.js	(révision 14240)
@@ -97,5 +97,8 @@
     "delayLabel" : "Délais",
     "delayDesc" : "Ajuster le Délais avant que l'animation se déclenche",
     "durationLabel" : "Durée",
-    "durationDesc":"Ajuster la durée de l'animation"
+    "durationDesc":"Ajuster la durée de l'animation",
+    "optionsPopup": "Options",
+    "closeButtonOption": "Bouton de fermeture",
+    "backdropClickOption": "Clicker sur le fond pour fermer"
 });
Index: src/js/JEditor/PagePanel/Contents/Options/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/nls/i18n.js	(révision 14239)
+++ src/js/JEditor/PagePanel/Contents/Options/nls/i18n.js	(révision 14240)
@@ -99,8 +99,10 @@
     "delayLabel" : "Delay",
     "delayDesc" : "Adjust Delay before animation starts",
     "durationLabel" : "Duration",
-    "durationDesc":"Adjust animation duration"
-    
+    "durationDesc":"Adjust animation duration",
+    "optionsPopup": "Options",
+    "closeButtonOption": "Close button",
+    "backdropClickOption": "Click on background to close"
   },
   "fr-fr": true,
   "fr-ca": true
