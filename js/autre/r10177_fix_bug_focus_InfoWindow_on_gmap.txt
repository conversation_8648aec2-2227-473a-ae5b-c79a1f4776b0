Revision: r10177
Date: 2023-01-20 14:35:43 +0300 (zom 20 Jan 2023) 
Author: anthony 

## Commit message
fix bug focus InfoWindow on gmap

## Files changed

## Full metadata
------------------------------------------------------------------------
r10177 | anthony | 2023-01-20 14:35:43 +0300 (zom 20 Jan 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Views/MapBlockView.js

fix bug focus InfoWindow on gmap
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Views/MapBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Views/MapBlockView.js	(révision 10176)
+++ src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Views/MapBlockView.js	(révision 10177)
@@ -138,7 +138,7 @@
                                         position: this.markers[index].getPosition()
                                     };
                                     var popup = new google.maps.InfoWindow(popupOptions);
-                                    popup.open(this.map)
+                                    popup.open({map: this.map, shouldFocus: false})
                                     this.popups.push(popup);
                                 }
                             }, this);
