Revision: r12044
Date: 2024-02-29 14:45:10 +0300 (lkm 29 Feb 2024) 
Author: rrakotoarinelina 

## Commit message
form : champs 'date range' - utilisation de class

## Files changed

## Full metadata
------------------------------------------------------------------------
r12044 | rrakotoarinelina | 2024-02-29 14:45:10 +0300 (lkm 29 Feb 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/Fields/DateRange.html
   M /branches/ideo3_v2/integration/src/less/imports/form_block/contentOptions.less

form : champs 'date range' - utilisation de class
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/Fields/DateRange.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/Fields/DateRange.html	(révision 12043)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/Fields/DateRange.html	(révision 12044)
@@ -1,11 +1,11 @@
 <label><%=field.mandatory?field.label+' *':field.label%><% if(field.description){%><span class="label-desc"><%=field.description%></span><%}%></label>
-<div class="" style="display: flex; flex-direction: row; align-items: stretch; width: 100%;">
-    <div class="" style="display: flex; align-items: center;  width: 100%">
-      <span style="margin-right:   5px; vertical-align: middle;"><%= __("from") %></span>
+<div class="daterange-container">
+  <div class="daterange-element">
+      <span class="from-text"><%= __("from") %></span>
       <input type="date" placeholder="<%=field.placeholder%>" <%= field.required?'required="required"':''%> />
     </div>
-    <div class="" style="display: flex; align-items: center; width: 100%;">
-      <span style="margin-right:   5px; margin-left: 5px; vertical-align: middle;"><%= __("to") %></span>
+    <div class="daterange-element">
+      <span class="to-text"><%= __("to") %></span>
       <input type="date" placeholder="<%=field.placeholder%>" <%= field.required?'required="required"':''%> />
     </div>
   </div>
Index: src/less/imports/form_block/contentOptions.less
===================================================================
--- src/less/imports/form_block/contentOptions.less	(révision 12043)
+++ src/less/imports/form_block/contentOptions.less	(révision 12044)
@@ -256,3 +256,27 @@
         background-color: #383838 !important;
     }
 }
+
+.daterange-container{
+    display: flex;
+    flex-direction: row;
+    align-items: stretch;
+    width: 100%;
+
+    .daterange-element{
+        display: flex;
+        align-items: center;
+        width: 100%;
+
+        .from-text{
+            margin-right:5px;
+            vertical-align: middle;
+        }
+
+        .to-text{
+            margin-right: 5px;
+            margin-left: 5px;
+            vertical-align: middle;
+        }
+    }
+}
\ No newline at end of file
