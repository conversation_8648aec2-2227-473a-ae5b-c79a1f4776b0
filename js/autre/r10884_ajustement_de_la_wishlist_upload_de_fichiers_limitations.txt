Revision: r10884
Date: 2023-05-04 13:34:15 +0300 (lkm 04 Mey 2023) 
Author: sraz<PERSON><PERSON><PERSON><PERSON> 

## Commit message
ajustement de la wishlist: upload de fichiers limitations

## Files changed

## Full metadata
------------------------------------------------------------------------
r10884 | srazanandralisoa | 2023-05-04 13:34:15 +0300 (lkm 04 Mey 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalleryBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalleryBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalleryBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/main.less

ajustement de la wishlist: upload de fichiers limitations
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Files/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/Commons/Files/nls/fr-ca/i18n.js	(révision 10883)
+++ src/js/JEditor/Commons/Files/nls/fr-ca/i18n.js	(révision 10884)
@@ -1 +1 @@
-define({"allFiles":"Tous les fichiers","Photos":"Photos","Files":"Fichiers","sortBy":"Classer par","fileSort":"Pas de classement","fileSortcreatedAtAsc":"Date d'ajout croissante","fileSortcreatedAtDesc":"Date d'ajout décroissante","fileSortnameAsc":"Fichiers de A à Z","fileSortnameDesc":"Fichiers de Z à A","deleteAction":"Suppression","cancel":"Annuler","File(s)":"Fichier(s) sur","selectFile":"Parcourir la base de mon site","choose":"Choisir","defaultUploaderMessage":"Cliquez ici ou glissez-déposez pour remplacer l'image","defaultUploaderMessageImage":"Cliquez ici ou glissez-déposez pour remplacer l'image","defaultUploaderMessageFile":"Cliquez ici ou glissez-déposez pour remplacer le fichier","browseImageOnComputer":"Parcourir les dossiers <br>sur mon ordinateur","imagesFromStock":"Parcourir la base d'images<br> de mon site","importFailBadType":"Impossible d'importer le fichier %name% car il représente un danger potentiel","progressLoadingFiles":"Veuillez patienter pendant le chargement des fichiers","uploadFailTooBig":"Le fichier %name% est trop volumineux pour être importé","uploadFailErrorsOccured":"Des erreurs ont eu lieu pendant le transfert de fichier","uploadSuccess":"Le transfert des fichiers s'est déroulé avec succès","uploadFailServerError":"Impossible de transférer le fichier %name% à cause de l'erreur: \"%error%\"","LoadMoreImage":"Charger plus de fichiers ..."});
\ No newline at end of file
+define({"allFiles":"Tous les fichiers","Photos":"Photos","Files":"Fichiers","sortBy":"Classer par","fileSort":"Pas de classement","fileSortcreatedAtAsc":"Date d'ajout croissante","fileSortcreatedAtDesc":"Date d'ajout décroissante","fileSortnameAsc":"Fichiers de A à Z","fileSortnameDesc":"Fichiers de Z à A","deleteAction":"Suppression","cancel":"Annuler","File(s)":"Fichier(s) sur","selectFile":"Parcourir la base de mon site","choose":"Choisir","defaultUploaderMessage":"Cliquez ici ou glissez-déposez pour remplacer l'image","defaultUploaderMessageImage":"Cliquez ici ou glissez-déposez pour remplacer l'image","defaultUploaderMessageFile":"Cliquez ici ou glissez-déposez pour remplacer le fichier","browseImageOnComputer":"Parcourir les dossiers <br>sur mon ordinateur","imagesFromStock":"Parcourir la base d'images<br> de mon site","importFailBadType":"Impossible d'importer le fichier %name% car il représente un danger potentiel","progressLoadingFiles":"Veuillez patienter pendant le chargement des fichiers","uploadFailTooBig": 'Le fichier "%name%" (%filesize% mo ) est trop volumineux pour être importé (limite %limite% mo)',"uploadFailErrorsOccured":"Des erreurs ont eu lieu pendant le transfert de fichier","uploadSuccess":"Le transfert des fichiers s'est déroulé avec succès","uploadFailServerError":"Impossible de transférer le fichier %name% à cause de l'erreur: \"%error%\"","LoadMoreImage":"Charger plus de fichiers ..."});
\ No newline at end of file
Index: src/js/JEditor/Commons/Files/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/Commons/Files/nls/fr-fr/i18n.js	(révision 10883)
+++ src/js/JEditor/Commons/Files/nls/fr-fr/i18n.js	(révision 10884)
@@ -1,3 +1,3 @@
-define({"allFiles":"Tous les fichiers","Photos":"Photos","Files":"Fichiers","sortBy":"Classer par","fileSort":"Pas de classement","fileSortcreatedAtAsc":"Date d'ajout croissante","fileSortcreatedAtDesc":"Date d'ajout décroissante","fileSortnameAsc":"Fichiers de A à Z","fileSortnameDesc":"Fichiers de Z à A","deleteAction":"Suppression","cancel":"Annuler","File(s)":"Fichier(s) sur","selectFile":"Parcourir la base de mon site","choose":"Choisir","defaultUploaderMessage":"Cliquez ici ou glissez-déposez pour remplacer l'image","defaultUploaderMessageImage":"Cliquez ici ou glissez-déposez pour remplacer l'image","defaultUploaderMessageFile":"Cliquez ici ou glissez-déposez pour remplacer le fichier","browseImageOnComputer":"Parcourir les dossiers <br>sur mon ordinateur","imagesFromStock":"Parcourir la base d'images<br> de mon site","importFailBadType":"Impossible d'importer le fichier %name% car il représente un danger potentiel","progressLoadingFiles":"Veuillez patienter pendant le chargement des fichiers","uploadFailTooBig":"Le fichier %name% est trop volumineux pour être importé","uploadFailErrorsOccured":"Des erreurs ont eu lieu pendant le transfert de fichier","uploadSuccess":"Le transfert des fichiers s'est déroulé avec succès",
+define({"allFiles":"Tous les fichiers","Photos":"Photos","Files":"Fichiers","sortBy":"Classer par","fileSort":"Pas de classement","fileSortcreatedAtAsc":"Date d'ajout croissante","fileSortcreatedAtDesc":"Date d'ajout décroissante","fileSortnameAsc":"Fichiers de A à Z","fileSortnameDesc":"Fichiers de Z à A","deleteAction":"Suppression","cancel":"Annuler","File(s)":"Fichier(s) sur","selectFile":"Parcourir la base de mon site","choose":"Choisir","defaultUploaderMessage":"Cliquez ici ou glissez-déposez pour remplacer l'image","defaultUploaderMessageImage":"Cliquez ici ou glissez-déposez pour remplacer l'image","defaultUploaderMessageFile":"Cliquez ici ou glissez-déposez pour remplacer le fichier","browseImageOnComputer":"Parcourir les dossiers <br>sur mon ordinateur","imagesFromStock":"Parcourir la base d'images<br> de mon site","importFailBadType":"Impossible d'importer le fichier %name% car il représente un danger potentiel","progressLoadingFiles":"Veuillez patienter pendant le chargement des fichiers","uploadFailTooBig":'Le fichier "%name%" (%filesize% mo ) est trop volumineux pour être importé (limite %limite% mo)',"uploadFailErrorsOccured":"Des erreurs ont eu lieu pendant le transfert de fichier","uploadSuccess":"Le transfert des fichiers s'est déroulé avec succès",
 "uploadFailServerError":"Impossible de transférer le fichier %name% à cause de l'erreur: \"%error%\"",
 "LoadMoreImage":"Charger plus de fichiers ..."});
\ No newline at end of file
Index: src/js/JEditor/Commons/Files/nls/i18n.js
===================================================================
--- src/js/JEditor/Commons/Files/nls/i18n.js	(révision 10883)
+++ src/js/JEditor/Commons/Files/nls/i18n.js	(révision 10884)
@@ -1,3 +1,3 @@
-define({ "root": {"allFiles":"All Files","Photos":"Photos","Files":"Files","sortBy":"Sort by","fileSort":"Not sorted","fileSortcreatedAtAsc":"Sort by date asc","fileSortcreatedAtDesc":"Sort by date desc","fileSortnameAsc":"Sort by name asc","fileSortnameDesc":"Sort by name desc","deleteAction":"Delete","cancel":"Cancel","File(s)":"File(s) on","selectFile":"Select File","choose":"Choose","defaultUploaderMessage":"Click here or drag-and-drop to change the picture","defaultUploaderMessageImage":"Click here or drag-and-drop to change the picture","defaultUploaderMessageFile":"Click here or drag-and-drop to change the file","browseImageOnComputer":"Browse directories <br> on my comptuer","imagesFromStock":"Browse pictures <br> on my site","importFailBadType":"Impossible to import file%name % due to a potential harm","progressLoadingFiles":"Please wait during file loading","uploadFailTooBig":"The file%name % is too big to be imported","uploadFailErrorsOccured":"There were errors during file transfer","uploadSuccess":"The file transfer was successful",
+define({ "root": {"allFiles":"All Files","Photos":"Photos","Files":"Files","sortBy":"Sort by","fileSort":"Not sorted","fileSortcreatedAtAsc":"Sort by date asc","fileSortcreatedAtDesc":"Sort by date desc","fileSortnameAsc":"Sort by name asc","fileSortnameDesc":"Sort by name desc","deleteAction":"Delete","cancel":"Cancel","File(s)":"File(s) on","selectFile":"Select File","choose":"Choose","defaultUploaderMessage":"Click here or drag-and-drop to change the picture","defaultUploaderMessageImage":"Click here or drag-and-drop to change the picture","defaultUploaderMessageFile":"Click here or drag-and-drop to change the file","browseImageOnComputer":"Browse directories <br> on my comptuer","imagesFromStock":"Browse pictures <br> on my site","importFailBadType":"Impossible to import file%name % due to a potential harm","progressLoadingFiles":"Please wait during file loading","uploadFailTooBig":  'The file "%name%" (%filesize% mo )  is too large to be imported (limit %limite% mo)',"uploadFailErrorsOccured":"There were errors during file transfer","uploadSuccess":"The file transfer was successful",
 "uploadFailServerError":"Impossible to transfer file %name% because \"%error%\"",
 "LoadMoreImage":"Load more files ..."}, "fr-fr":true, "fr-ca":true })
\ No newline at end of file
Index: src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js
===================================================================
--- src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 10883)
+++ src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 10884)
@@ -96,7 +96,7 @@
                     imagesFromStock: 'Parcourir la base d\'images<br> de mon site',
                     importFailBadType: 'Impossible d\'importer le fichier %name% car il représente un danger potentiel',
                     progressLoadingFiles: 'Veuillez patienter pendant le chargement des fichiers',
-                    uploadFailTooBig: 'Le fichier %name% est trop volumineux pour être importé',
+                    uploadFailTooBig: 'Le fichier "%name%" (%filesize% mo ) est trop volumineux pour être importé (limite %limite% mo)' ,
                     uploadFailErrorsOccured: 'Des erreurs ont eu lieu pendant le transfert de fichier',
                     uploadSuccess: 'Le transfert des fichiers s\'est déroulé avec succès',
                     uploadFailServerError: 'Impossible de transférer le fichier %name% à cause de l\'erreur: "%error%"'
@@ -313,6 +313,7 @@
                 this.previews = {};
             this.$frag = $(document.createDocumentFragment());
             for (var i = 0; i < files.length; i++) {
+                var filesize = Math.ceil(files[i].size  / (1024 * 1024));
                 if (this.options.maxFiles !== -1 && (i >= this.options.maxFiles))
                     break; //Yeah, this is a breakup reason
                 if (!(this.acceptedTypes.test(files[i].type.toLowerCase()) || (this.acceptedExt && this.acceptedExt.test(files[i].name.toLowerCase()))) ||
@@ -325,7 +326,7 @@
                 }
                 else if (uploader._isVideo(files[i]) && files[i].size > this.options.maxSizeVideo) {
                         message = this.options.lang.uploadFailTooBig;
-                        this.errors.push(message.replace('%name%', files[i].name));
+                        this.errors.push(message.replace('%name%', files[i].name).replace('%filesize%', filesize).replace('%limite%', "15"));
                         files.rejected++;
                         if (files.length === 1)
                             this._onComplete();
@@ -332,7 +333,7 @@
                 } 
                 else if (!uploader._isVideo(files[i]) && files[i].size > this.options.maxSizeFile) {
                     message = this.options.lang.uploadFailTooBig;
-                    this.errors.push(message.replace('%name%', files[i].name));
+                    this.errors.push(message.replace('%name%', files[i].name).replace('%filesize%', filesize).replace('%limite%', "5"));
                     files.rejected++;
                     if (files.length === 1)
                         this._onComplete();
@@ -383,6 +384,9 @@
                     fileReader.readAsDataURL(files[i]);
                 }
             }
+            if (files.length == files.rejected) {
+                this._onComplete();
+            }
 
         },
         uploaded: function(uploaded) {
@@ -491,7 +495,8 @@
             // amelioration message d'erreur pour l'upload image
             if (error !== 'abort') {
                 if(jqXHR.responseText == "Something went wrong : Check that the file isn't corrupted The uploaded file exceeds the upload_max_filesize directive in php.ini[]"){
-                    var err = this.options.lang.uploadFailTooBig.replace('%name%', currentUpload.name);
+                    var filesize = Math.ceil(currentUpload.size / 1000000);
+                    var err = this.options.lang.uploadFailTooBig.replace('%name%', currentUpload.name).replace('%filesize%', filesize).replace('%limite%', "15");
                     this.$message.find('p').text(err);
                 }else{
                     var err = this.options.lang.uploadFailServerError.replace('%name%', currentUpload.name).replace('%error%', error);
Index: src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 10883)
+++ src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 10884)
@@ -65,7 +65,7 @@
     "defaultUploaderMessage": "",
     "browseImageOnComputer": "Parcourir les dossiers <br>sur mon ordinateur",
     "progressLoadingFiles": "Veuillez patienter pendant le chargement des fichiers",
-    "uploadFailTooBig": "Le fichier %name% est trop volumineux pour être importé",
+    "uploadFailTooBig":  'Le fichier "%name%" (%filesize% mo ) est trop volumineux pour être importé (limite %limite% mo)',
     "uploadFailErrorsOccured": "Des erreurs ont eu lieu pendant le transfert de fichier",
     "uploadSuccess": "Le transfert des fichiers s'est déroulé avec succès",
     "uploadFailServerError": "Impossible de transférer le fichier %name% à cause de l'erreur: %error%",
Index: src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 10883)
+++ src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 10884)
@@ -67,7 +67,7 @@
     "defaultUploaderMessage": "",
     "browseImageOnComputer": "Parcourir les dossiers <br>sur mon ordinateur",
     "progressLoadingFiles": "Veuillez patienter pendant le chargement des fichiers",
-    "uploadFailTooBig": "Le fichier %name% est trop volumineux pour être importé",
+    "uploadFailTooBig":  'Le fichier "%name%" (%filesize% mo ) est trop volumineux pour être importé (limite %limite% mo)',
     "uploadFailErrorsOccured": "Des erreurs ont eu lieu pendant le transfert de fichier",
     "uploadSuccess": "Le transfert des fichiers s'est déroulé avec succès",
     "uploadFailServerError": "Impossible de transférer le fichier %name% à cause de l'erreur: %error%",
Index: src/js/JEditor/FilePanel/nls/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/i18n.js	(révision 10883)
+++ src/js/JEditor/FilePanel/nls/i18n.js	(révision 10884)
@@ -68,7 +68,7 @@
         "defaultUploaderMessage": "",
         "browseImageOnComputer": "Browse directories <br> on my comptuer",
         "progressLoadingFiles": "Please wait during file loading",
-        "uploadFailTooBig": "File %name% is too big to be imported",
+        "uploadFailTooBig":  'The file "%name%" (%filesize% mo )  is too large to be imported (limit %limite% mo)',
         "uploadFailErrorsOccured": "Errors occurred during file transfer",
         "uploadSuccess": "File transfer successful",
         "uploadFailServerError": "Impossible to transfer file %name% due to: %error%",
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalleryBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalleryBlock/nls/fr-ca/i18n.js	(révision 10883)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalleryBlock/nls/fr-ca/i18n.js	(révision 10884)
@@ -59,7 +59,7 @@
    "imagesFromStock":"Parcourir la base d'images<br> de mon site",
    "importFailBadType":"Impossible d'importer le fichier %name% car il représente un danger potentiel",
    "progressLoadingFiles":"Veuillez patienter pendant le chargement des fichiers",
-   "uploadFailTooBig":"Le fichier %name% est trop volumineux pour être importé",
+   "uploadFailTooBig": 'Le fichier "%name%" (%filesize% mo ) est trop volumineux pour être importé (limite %limite% mo)',
    "uploadFailErrorsOccured":"Des erreurs ont eu lieu pendant le transfert de fichier",
    "uploadSuccess":"Le transfert des fichiers s'est déroulé avec succès",
    "uploadFailServerError":"",
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalleryBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalleryBlock/nls/fr-fr/i18n.js	(révision 10883)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalleryBlock/nls/fr-fr/i18n.js	(révision 10884)
@@ -59,7 +59,7 @@
    "imagesFromStock":"Parcourir la base d'images<br> de mon site",
    "importFailBadType":"Impossible d'importer le fichier %name% car il représente un danger potentiel",
    "progressLoadingFiles":"Veuillez patienter pendant le chargement des fichiers",
-   "uploadFailTooBig":"Le fichier %name% est trop volumineux pour être importé",
+   "uploadFailTooBig": 'Le fichier "%name%" (%filesize% mo ) est trop volumineux pour être importé (limite %limite% mo)',
    "uploadFailErrorsOccured":"Des erreurs ont eu lieu pendant le transfert de fichier",
    "uploadSuccess":"Le transfert des fichiers s'est déroulé avec succès",
    "uploadFailServerError":"",
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalleryBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalleryBlock/nls/i18n.js	(révision 10883)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalleryBlock/nls/i18n.js	(révision 10884)
@@ -60,7 +60,7 @@
       "imagesFromStock":"Browse the image database<br> of my site",
       "importFailBadType":"Can not import the file% name% because it represents a potential danger",
       "progressLoadingFiles":"Please wait while loading files",
-      "uploadFailTooBig":"The file% name% is too large to be imported",
+      "uploadFailTooBig":'The file "%name%" (%filesize% mo )  is too large to be imported (limit %limite% mo)',
       "uploadFailErrorsOccured":"Errors occurred during the file transfer",
       "uploadSuccess":"File transfer was successful",
       "uploadFailServerError":"",
Index: src/less/main.less
===================================================================
--- src/less/main.less	(révision 10883)
+++ src/less/main.less	(révision 10884)
@@ -2811,4 +2811,7 @@
   background-color: #36b1c0;
   color: #fff;
   cursor: pointer;
+}
+.ui-dialog .ui-dialog-content li{
+  margin: 6px;
 }
\ No newline at end of file
