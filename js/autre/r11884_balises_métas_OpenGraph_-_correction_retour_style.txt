Revision: r11884
Date: 2024-02-15 15:33:04 +0300 (lkm 15 Feb 2024) 
Author: rrakotoarinelina 

## Commit message
balises métas OpenGraph - correction retour style 

## Files changed

## Full metadata
------------------------------------------------------------------------
r11884 | rrakotoarinelina | 2024-02-15 15:33:04 +0300 (lkm 15 Feb 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Templates/MarqueClient.html
   M /branches/ideo3_v2/integration/src/less/imports/params_panel/module/inline-social.less

balises métas OpenGraph - correction retour style 
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Templates/MarqueClient.html
===================================================================
--- src/js/JEditor/ParamsPanel/Templates/MarqueClient.html	(révision 11883)
+++ src/js/JEditor/ParamsPanel/Templates/MarqueClient.html	(révision 11884)
@@ -87,7 +87,7 @@
                                 <strong><em><%=__("Text_opengraph")%></em></strong>
                                 <!-- <span class="rigth-delete-image icon-bin" data-name="Opengraph" style="top: 32%;right: 17%;"> </span> -->
 
-                                <span class="rigth-delete-image icon-bin" data-name="Opengraph" style="top: 26%; right: 5%;"> </span>
+                                <span class="rigth-delete-image icon-bin" data-name="Opengraph" style="top: 26%; right: 21%;"> </span>
 
                                 <div class="menu-wrapper-opengraph file image">
                                     <header></header>
Index: src/less/imports/params_panel/module/inline-social.less
===================================================================
--- src/less/imports/params_panel/module/inline-social.less	(révision 11883)
+++ src/less/imports/params_panel/module/inline-social.less	(révision 11884)
@@ -254,6 +254,7 @@
     .group-content-opengraph {
         margin-bottom: 50px;
         margin-right: 50px;
+        width:750px;
 
     }
 
