Revision: r13420
Date: 2024-10-31 10:00:06 +0300 (lkm 31 Okt 2024) 
Author: frahajanirina 

## Commit message
Wishlist IDEO3.2: Page de remerciement: Afficher un message d'erreur si coché mais qu’aucune page choisi.

## Files changed

## Full metadata
------------------------------------------------------------------------
r13420 | frahajanirina | 2024-10-31 10:00:06 +0300 (lkm 31 Okt 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormBlockView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Views/NewsletterBlockView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/i18n.js

Wishlist IDEO3.2: Page de remerciement: Afficher un message d'erreur si coché mais qu’aucune page choisi.
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormBlockView.js	(révision 13419)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormBlockView.js	(révision 13420)
@@ -56,6 +56,16 @@
                         model: this.model.get("form")
                     });
                     this.listenTo(formOptionsView, "save", function() {
+                        isThankYouPage = this.model.get("form").renderPageThankYou;
+                        linkHref = this.model.get("form").link.href;
+                        if (isThankYouPage && linkHref == '#') {
+                            this.error({
+                                message: translate("errorWithoutThankYouPage"),
+                                title: translate("error")
+                            });
+
+                            return false
+                        }
                         this.app.currentPanel.rightPanelView.hidePanel();
                         this.app.currentPanel.rightPanelView.hideContent(formOptionsView);
                         this.app.currentPanel.rightPanelView.removeContent(formOptionsView);
Index: src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Views/NewsletterBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Views/NewsletterBlockView.js	(révision 13419)
+++ src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Views/NewsletterBlockView.js	(révision 13420)
@@ -83,6 +83,16 @@
                             advancedView = new AdvancedOptionView({model:this.model.options.advancedCSS});
 
                             this.listenTo(optionsView, "save", function() {
+                                isThankYouPage = this.model.options.NewsletterOption.renderPageThankYou;
+                                linkHref = this.model.options.NewsletterOption.link.href;
+                                if (isThankYouPage && linkHref == '#') {
+                                    this.error({
+                                        message: translate("errorWithoutThankYouPage"),
+                                        title: translate("error")
+                                    });
+
+                                    return false
+                                }
                                 this.app.currentPanel.rightPanelView.hidePanel();
                                 this.app.currentPanel.rightPanelView.hideContent(optionsView);
                                 this.app.currentPanel.rightPanelView.removeContent(optionsView);
Index: src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/fr-ca/i18n.js	(révision 13419)
+++ src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/fr-ca/i18n.js	(révision 13420)
@@ -15,5 +15,6 @@
     "askNames" : "Demander le nom et le prénom",
     "send":"Envoyer",
     "successMessage":"Merci, votre message nous a été transmis avec succès",
-    "renderPageThankYou": "Renvoyer vers une page de remerciement"
+    "renderPageThankYou": "Renvoyer vers une page de remerciement",
+    "errorWithoutThankYouPage": "Vous n’avez pas sélectionné la page de remerciement. Vous devez sélectionner une page ou désactiver l’option."
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/fr-fr/i18n.js	(révision 13419)
+++ src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/fr-fr/i18n.js	(révision 13420)
@@ -15,5 +15,6 @@
     "askNames" : "Demander le nom et le prénom",
     "send":"Envoyer",
     "successMessage":"Merci, votre message nous a été transmis avec succès",
-    "renderPageThankYou": "Renvoyer vers une page de remerciement"
+    "renderPageThankYou": "Renvoyer vers une page de remerciement",
+    "errorWithoutThankYouPage": "Vous n’avez pas sélectionné la page de remerciement. Vous devez sélectionner une page ou désactiver l’option."
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/i18n.js	(révision 13419)
+++ src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/i18n.js	(révision 13420)
@@ -17,6 +17,7 @@
         "askNames" : "Ask for first and last name",
         "send" : "Send",
         "successMessage" : "Your message has been sent successfully",
-        "renderPageThankYou": "Link to a thank you page"
+        "renderPageThankYou": "Link to a thank you page",
+        "errorWithoutThankYouPage": "You did not select the thank you page. You must select a page or disable the option."
 
     }, "fr-fr": true, "fr-ca": true});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js	(révision 13419)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js	(révision 13420)
@@ -89,5 +89,6 @@
     "numberMax" : "Maximum",
     "min"  :"Min",
     "max" : "Max",
-    "renderPageThankYou": "Renvoyer vers une page de remerciement"
+    "renderPageThankYou": "Renvoyer vers une page de remerciement",
+    "errorWithoutThankYouPage": "Vous n’avez pas sélectionné la page de remerciement. Vous devez sélectionner une page ou désactiver l’option."
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js	(révision 13419)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js	(révision 13420)
@@ -89,5 +89,6 @@
     "numberMax" : "Maximum",
     "min"  :"Min",
     "max" : "Max",
-    "renderPageThankYou": "Renvoyer vers une page de remerciement"
+    "renderPageThankYou": "Renvoyer vers une page de remerciement",
+    "errorWithoutThankYouPage": "Vous n’avez pas sélectionné la page de remerciement. Vous devez sélectionner une page ou désactiver l’option."
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js	(révision 13419)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js	(révision 13420)
@@ -95,7 +95,8 @@
         "numberMax" : "Maximum",
         "min"  :"Min",
         "max" : "Max",
-        "renderPageThankYou": "Link to a thank you page"
+        "renderPageThankYou": "Link to a thank you page",
+        "errorWithoutThankYouPage": "You did not select the thank you page. You must select a page or disable the option."
     },
     "fr-fr":true,
     "fr-ca": true
