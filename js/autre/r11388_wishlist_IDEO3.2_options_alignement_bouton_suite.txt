Revision: r11388
Date: 2023-10-09 11:40:13 +0300 (lts 09 Okt 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2 : options alignement bouton suite

## Files changed

## Full metadata
------------------------------------------------------------------------
r11388 | sraz<PERSON><PERSON><PERSON>oa | 2023-10-09 11:40:13 +0300 (lts 09 Okt 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js

wishlist IDEO3.2 : options alignement bouton suite
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js	(révision 11387)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js	(révision 11388)
@@ -29,7 +29,7 @@
                             this.$el.mCustomScrollbar('update');
                         },
                          /**
-                         * affichage de l'alignement du texte
+                         * annuler l'alignement du bouton
                          */
                          cancelButtonAligment:function (event) {
                             var $target = $(event.currentTarget);
