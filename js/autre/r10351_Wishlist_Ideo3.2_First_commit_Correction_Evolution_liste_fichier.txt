Revision: r10351
Date: 2023-02-08 10:46:24 +0300 (lrb 08 Feb 2023) 
Author: norajaonarivelo 

## Commit message
Wishlist Ideo3.2 :First commit Correction/Evolution liste fichier

## Files changed

## Full metadata
------------------------------------------------------------------------
r10351 | norajaonarivelo | 2023-02-08 10:46:24 +0300 (lrb 08 Feb 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Templates/selectFileListManager.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileDetail.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileList.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileManagerUIView.js
   M /branches/ideo3_v2/integration/src/less/imports/filePanel.less

Wishlist Ideo3.2 :First commit Correction/Evolution liste fichier
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Files/Templates/selectFileListManager.html
===================================================================
--- src/js/JEditor/Commons/Files/Templates/selectFileListManager.html	(révision 10350)
+++ src/js/JEditor/Commons/Files/Templates/selectFileListManager.html	(révision 10351)
@@ -26,7 +26,7 @@
         <div class="trier-par">
             <%= __('sortBy')%> :
             <a class="sort-dropdown-toggle" href="#">
-                <span class="text"><%= __('fileSort')%></span>
+                <span class="text"><%= __('fileSortcreatedAtDesc')%></span>
                 <span class="caret"></span>
             </a>
             <ul class="dropdown-menu">
Index: src/js/JEditor/FilePanel/Templates/fileDetail.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileDetail.html	(révision 10350)
+++ src/js/JEditor/FilePanel/Templates/fileDetail.html	(révision 10351)
@@ -56,17 +56,8 @@
                             </p>
                         </div>
                     </span>
-                    <% if(isRemovable===false || isInBlock ===true) {%>
-                        <span class="actions informations">
-                            <span class="icon-information"></span>
-                            <div class="infobulles">
-                                <p class="title"><%= __("locked")%> :</p>
-                                <p>
-                                    <%= __("lockimage")%>
-                                </p>
-                            </div>
-                        </span>
-                    <% } %>
+
+
                     <%if(isFavicon){%>
                         <span class="actions informations">
                             <span class="icon-information"></span>
Index: src/js/JEditor/FilePanel/Templates/fileList.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileList.html	(révision 10350)
+++ src/js/JEditor/FilePanel/Templates/fileList.html	(révision 10351)
@@ -1,3 +1,26 @@
+
+<div class="FileTop">
+    <div class="my-files fileList">
+        <div class="content scroll-container">
+            <div class="group-content <%=content.length===0?'empty':''%>">
+                <div class=" add-file" data-action="showuploader">
+    
+                    <span class="icon">
+                        <span class="icon-hexagon"></span>
+                        <span class="icon-add"></span>
+                    </span>
+                    <%= __("addFile")%>
+    
+                </div>
+                <!-- file add -->
+    
+            </div>
+        </div>
+    </div>
+    
+    <div class="warning-msg"><span class="icon-warning" style="color: #d42525;"></span> <span style="color: #d42525;"><%= __("imageWarning")%></span> <%= __("imageWarningMsg")%> <a href="https://www.tineye.com/" target="_blank">TinEye</a>.</div>
+</div>
+
 <div class="my-files fileList">
     <div class="content scroll-container">
         <div class="group-content <%=content.length===0?'empty':''%>">
@@ -28,19 +51,9 @@
             </div>
             <% } %>
             <% } %>
-            <div class="menu-wrapper add-file" data-action="showuploader">
-
-                <span class="icon">
-                    <span class="icon-hexagon"></span>
-                    <span class="icon-add"></span>
-                </span>
-                <%= __("addFile")%>
-
-            </div>
+           
             <!-- file add -->
 
         </div>
     </div>
 </div>
-
-<div class="warning-msg"><span class="icon-warning" style="color: #d42525;"></span> <span style="color: #d42525;"><%= __("imageWarning")%></span> <%= __("imageWarningMsg")%> <a href="https://www.tineye.com/" target="_blank">TinEye</a>.</div>
\ No newline at end of file
Index: src/js/JEditor/FilePanel/Views/FileManagerUIView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileManagerUIView.js	(révision 10350)
+++ src/js/JEditor/FilePanel/Views/FileManagerUIView.js	(révision 10351)
@@ -25,8 +25,8 @@
                 events: {
                     'click [data-action="addSelectionToNewCollection"]': 'addSelectionToNewCollection',
                 },
-                sortBy: '',
-                sortOrder: '',
+                sortBy: 'createdAt',
+                sortOrder: 'desc',
                 initialize: function() {
                     this._super();
                     this._template = this.buildTemplate(fileManagerUIView, translate);
Index: src/less/imports/filePanel.less
===================================================================
--- src/less/imports/filePanel.less	(révision 10350)
+++ src/less/imports/filePanel.less	(révision 10351)
@@ -903,14 +903,17 @@
 }
 
 /* ADD-FILE */
+.FileTop {
+    margin: auto 211px;
+  }
 .my-files .add-file {
-    width: 91px; height: 91px;
+    width: auto; height: 120px;
     padding: 14px;
     border: 1px solid #dddddd;
 
     color: #989898;
-    line-height: 14px;
-    font-size: 12px;
+    line-height: 60px;
+    font-size: 16px;
     text-align: center;
 
     cursor: pointer;
@@ -928,13 +931,13 @@
     }
 
     & .icon-hexagon {
-        font-size: 26px;
+        font-size: 34px;
     }
 
     & .icon-add {
         position: absolute;
         font-size: 14px;
-        top: 6px; left: 39px;
+        top: 15px; left: 0;right: 0;
         color: #FFFFFF;
     }
 
