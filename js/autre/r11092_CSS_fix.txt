Revision: r11092
Date: 2023-06-23 15:00:23 +0300 (zom 23 Jon 2023) 
Author: mpartaux 

## Commit message
CSS fix

## Files changed

## Full metadata
------------------------------------------------------------------------
r11092 | mpartaux | 2023-06-23 15:00:23 +0300 (zom 23 Jon 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/less/imports/page_panel/module/block-render/compare.less

CSS fix
------------------------------------------------------------------------

## Diff
Index: src/less/imports/page_panel/module/block-render/compare.less
===================================================================
--- src/less/imports/page_panel/module/block-render/compare.less	(révision 11091)
+++ src/less/imports/page_panel/module/block-render/compare.less	(révision 11092)
@@ -1,13 +1,18 @@
 .compareblock .content {
     display: grid;
+    align-items: stretch;
     grid-template-columns: 6rem 1fr 6rem;
 }
 .compareblock .content img:first-child {
-    grid-column: 1 / 3;
+    grid-column: ~"1 / 3";
+    width: 100%;
+    object-fit: cover;
     grid-row: 1;
 }
 .compareblock .content img:last-child {
-    grid-column: 2 / 4;
+    grid-column: ~"2 / 4";
+    width: 100%;
+    object-fit: cover;
     grid-row: 1;
     outline: 6px solid white;
 }
