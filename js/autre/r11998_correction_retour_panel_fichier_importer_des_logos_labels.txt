Revision: r11998
Date: 2024-02-26 09:36:09 +0300 (lts 26 Feb 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
correction retour panel fichier importer des logos/labels 

## Files changed

## Full metadata
------------------------------------------------------------------------
r11998 | sraz<PERSON><PERSON><PERSON>oa | 2024-02-26 09:36:09 +0300 (lts 26 Feb 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileList.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/selectIcon.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/SelectIconView.js
   M /branches/ideo3_v2/integration/src/less/imports/filePanel.less

correction retour panel fichier importer des logos/labels 
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/FilePanel/Templates/fileList.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileList.html	(révision 11997)
+++ src/js/JEditor/FilePanel/Templates/fileList.html	(révision 11998)
@@ -1,7 +1,7 @@
 <div class="FileTop ">
 <div class="my-files fileList">
     <div class="content scroll-container">
-        <div class="group-content <%=content.length===0?'empty':''%>">
+        <div class="group-content flex-15 <%=content.length===0?'empty':''%>">
             <div class=" add-file" data-action="showuploader">
     
                 <span class="icon">
Index: src/js/JEditor/FilePanel/Templates/selectIcon.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/selectIcon.html	(révision 11997)
+++ src/js/JEditor/FilePanel/Templates/selectIcon.html	(révision 11998)
@@ -1,4 +1,4 @@
-<div class="container-icon option-content">
+<div class="container-icon option-content baseFile">
     <header class="popup-bar"> 
         <a href="javascript:;" class="onglet mycollections active" data-switchto="labels">
             Labels
@@ -7,15 +7,23 @@
             LinkeoStock
         </a> -->
    </header>
-    <div class="grid-container-icon">
+    <div class="grid-container-icon content">
         <%for(i=0;i<content.length;i++){
-            svg = content[i];
+            file = content[i];
             %>
-            <div  class="box-icon <%=selected === svg.name ? 'selected-icon':''%>" data-name="<%= svg.name%>" >
+            <%if (type === 'labels'){%>
+                <div  class="oneFile <%=selected === file.name ? 'selected-icon':''%>" data-name="<%= file.name%>" >
+                    <span class="select"><span class="icon-check"></span></span>
                     <span class="wrapper-icon">
-                        <%= svg.content%>
+                        <%= file.content%>
                     </span>
-            </div>
+                </div>
+            <%} else {%>
+                <div  class="oneFile <%=selected === file.name ? 'selected-icon':''%>" style="background-image: url(<%=file.url%>);" data-url="<%=file.url%>" >
+                    <span class="select"><span class="icon-check"></span></span>
+                </div>
+           <%}%>
+           
             <%}%>
     </div>
 </div>
Index: src/js/JEditor/FilePanel/Views/SelectIconView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/SelectIconView.js	(révision 11997)
+++ src/js/JEditor/FilePanel/Views/SelectIconView.js	(révision 11998)
@@ -14,7 +14,7 @@
     var SelectIconView = DialogView.extend({
         className: 'selectIcon',
         events: {
-            'click .box-icon': 'onSelect',
+            'click .oneFile': 'onSelect',
             'click a[data-switchto]': '_switchListCollection',
         },
         currentList: [],
@@ -25,7 +25,7 @@
             options.width = 750;
             options.title = translate('browseIconTitle')
             options.height = 600;
-            options.allowMultipleSelect = false;
+            options.allowMultipleSelect = true;
             options.buttons = [
                 {
                     class: 'okay',
@@ -72,7 +72,7 @@
             var data = (this.svgCollection === undefined ? [] : this.svgCollection);
             this._super();
             this.undelegateEvents();
-            this.$el.html(this._template({content:data, selected: this.selected}));
+            this.$el.html(this._template({content:data , type:'labels', selected: this.selected}));
             this.dom[this.cid].type = this.$('.onglet.mycollections');
             this.delegateEvents();
             return this;
@@ -98,13 +98,13 @@
             }
             if (this.selected.hasOwnProperty(name)) {
                 delete  this.selected[selected.name];
-                $target.removeClass('selected-icon');
+                $target.removeClass('selected');
                 this.selectedLength--;
             }
             else {
                 this.selected[selected.name] = selected;
                 this.selectedLength++;
-                $target.addClass('selected-icon');
+                $target.toggleClass('selected');
             }
         },
     
Index: src/less/imports/filePanel.less
===================================================================
--- src/less/imports/filePanel.less	(révision 11997)
+++ src/less/imports/filePanel.less	(révision 11998)
@@ -1021,7 +1021,7 @@
 .FileTop {
     margin: auto 211px;
   }
-.my-files .content .group-content {
+.my-files .content .group-content.flex-15 {
     display: flex;
     .add-file:nth-child(1) {width: 100%;}
 	.add-file:nth-child(2) {
@@ -2629,4 +2629,159 @@
 /* ===================================================================
    PANEL LOADER
    =================================================================== */
-#files .view-loader.loader-outer .loader-wrapper .icon-hexagon{color:@fileColor;}
\ No newline at end of file
+#files .view-loader.loader-outer .loader-wrapper .icon-hexagon{color:@fileColor;}
+
+.baseFile .content {
+
+    padding-top: 30px;
+
+    & .oneFile{
+        & span {
+            -webkit-transition:	all 0.15s ease-in-out;
+            -moz-transition:	all 0.15s ease-in-out;
+            -o-transition:          all 0.15s ease-in-out;
+            -ms-transition:         all 0.15s ease-in-out;
+            transition:		all 0.15s ease-in-out;
+        }
+    }
+    & .oneFile:hover .select, & .oneFile.selected .select {
+        opacity: 1;
+    }
+
+    & .oneFile {
+        position: relative;
+        background-size: cover;
+        background-position: center;
+        color: #FFF;
+        text-align: center;
+        padding: 5px;
+        text-align: center;
+        border: 1px solid;
+        border-color: #f3eaea;
+        font-size: 50px;
+        & svg{
+                width: 50px;
+                height: 50px;
+        }
+        & .select {
+            cursor: pointer;
+            opacity: 0;
+            position: absolute;
+            top: -14px; left: -14px;
+            z-index: 3;
+
+            width: 21px; height: 21px;
+            border: 5px solid #FFFFFF;
+            background-color: #cccccc;
+            .border-radius(25px);
+
+            font-size: 10px;
+            color: #cccccc;
+            text-align: center;
+            line-height: 24px;
+
+            &:hover {
+                background-color: #999999;
+                color: #FFFFFF;
+            }
+
+            &:active {
+                background-color: @pageColor;
+                color: #FFFFFF;
+            }
+        }
+
+        & .icon-file.ext {
+            margin-top:20px;
+            display:block;
+            font-size:4em;
+            & .extLabel{
+                font-size:12px;
+                color:black;
+                position:absolute;
+                left:0;
+                right:0;
+                line-height:50px;
+                font-family:'Raleway', sans-serif;
+            }
+            & .fileName{
+                overflow:hidden;
+                display:block;
+                font-size:12px;
+                font-family:'Raleway', sans-serif;
+            }
+        }
+
+        &:hover .masque {
+            opacity: 1;
+        }
+
+        &:hover ul {
+            opacity: 1;
+        }
+        &.selected{
+            .select {
+                background-color: @pageColor;
+                color: #FFFFFF;
+            }
+        }
+        & .masque{
+            opacity: 0;
+            width: 120px; height: 120px;
+            position: absolute;
+            top: 0; left: 0;
+            background-color: rgba(0, 0, 0, 0.5);
+        }
+        & ul{
+            opacity: 0;
+            position: absolute;
+            z-index: 1;
+            top: -12px; right: 0;
+            list-style-type: none;
+
+            & li {
+                background-color: #1a1a1a;
+                width: 40px; height: 39px;
+                border-bottom: 1px solid #2f2f2f;
+
+                text-align: center;
+                line-height: 45px;
+                color: #FFFFFF;
+
+                &:hover {
+                    background-color: #2f2f2f;             
+                }
+
+            }
+
+            & li .number {
+                color: #f17230;
+            }
+
+            & li:nth-child(1) {
+                font-size: 14px;
+            }
+
+            & li:nth-child(2) {
+                line-height: 39px;
+                font-family: 'Open Sans', sans-serif;
+            }
+
+            & li:nth-child(3) {
+                background: #f17230;
+                border-bottom: 1px solid #f17230;
+
+                &:hover {
+                    background-color: #d7511d;
+                }
+            }  
+        }
+        & .wrapperTitle{
+            left:0;right:0;bottom:-20px;
+            position:absolute;
+            color:black;
+            overflow:hidden;
+            white-space:nowrap;
+        }
+    }
+}
\ No newline at end of file
