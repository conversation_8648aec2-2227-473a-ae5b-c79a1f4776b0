Revision: r10830
Date: 2023-04-18 15:49:16 +0300 (tlt 18 Apr 2023) 
Author: norajaonarivelo 

## Commit message
WishList 3.2 :Correctifs  évolution Liste fichiers coté librairies fix :r10824

## Files changed

## Full metadata
------------------------------------------------------------------------
r10830 | norajaonarivelo | 2023-04-18 15:49:16 +0300 (tlt 18 Apr 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/dev/#librairies/vendor/CoreRessourceManager/src/CoreRessourceManager/Controller/ResourceRestController.php
   M /branches/ideo3_v2/dev/#librairies/vendor/CoreRessourceManager/src/CoreRessourceManager/Service/ResourceCrud.php
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/MarqueClientView.js

WishList 3.2 :Correctifs  évolution Liste fichiers coté librairies fix :r10824
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Views/MarqueClientView.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 10829)
+++ src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 10830)
@@ -2,7 +2,7 @@
         "underscore", 
         'JEditor/Commons/Ancestors/Views/View', 
         'JEditor/Commons/Events', 
-        "collection!JEditor/Commons/Files/Models/FileDBCollection",
+        "JEditor/FilePanel/Models/FileCollection",
         "JEditor/Commons/Files/Views/FileUploaderView",
         "JEditor/Commons/Files/Models/File",
         'text!../Templates/MarqueClient.html',
@@ -14,7 +14,7 @@
             _, 
             View, 
             Events, 
-            FileDBCollection,
+            FileCollection,
             FileUploaderView,
             File,
             template,
@@ -43,7 +43,7 @@
             this.currentFileLogoSmall = null;
             this.currentFileFavicon = null;
             this._template = this.buildTemplate(template,translate);
-            this.fileCollection = FileDBCollection.getInstance();
+            this.fileCollection = new FileCollection();
             this.translations = translate.translations;
          },
 
@@ -108,7 +108,9 @@
         },
         _deleteLastFile: function (name) {
             var cid = this.model.get(name).id;
-            var model = this.fileCollection.get(cid);
+            this.fileCollection.setId(cid);
+            this.fileCollection.fetch({async:false});
+            var model =this.fileCollection.get(cid);
             model.destroy(); 
         },
         
@@ -247,7 +249,9 @@
             var $target = $(e.currentTarget);
             var name = $target.data('name');
             var cid = this.model.get(name).id;
-            var model = this.fileCollection.get(cid);
+            this.fileCollection.setId(cid);
+            this.fileCollection.fetch({async:false});
+            var model =this.fileCollection.get(cid);
             if(!this.app.user.can("delete_file")){
                 return false;
             }
