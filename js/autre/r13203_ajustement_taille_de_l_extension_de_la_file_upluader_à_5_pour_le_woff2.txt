Revision: r13203
Date: 2024-10-15 14:15:40 +0300 (tlt 15 Okt 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
ajustement taille de l'extension de la file upluader à 5 pour le woff2

## Files changed

## Full metadata
------------------------------------------------------------------------
r13203 | srazanandralisoa | 2024-10-15 14:15:40 +0300 (tlt 15 Okt 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js

ajustement taille de l'extension de la file upluader à 5 pour le woff2
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js
===================================================================
--- src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 13202)
+++ src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 13203)
@@ -301,7 +301,7 @@
             return (extPattern.test(file.name.toLowerCase()) || typePattern.test(file.type.toLowerCase()));
         },
         _getFileExt: function(file) {
-            var ext = /^.*\.([a-zA-Z0-9]{1,4})$/;
+            var ext = /^.*\.([a-zA-Z0-9]{1,5})$/;
             return ext.exec(file.name)[1];
         },
         _readFiles: function(files) {
