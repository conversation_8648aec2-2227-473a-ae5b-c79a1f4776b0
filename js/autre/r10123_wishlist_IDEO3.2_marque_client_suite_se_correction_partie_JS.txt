Revision: r10123
Date: 2023-01-10 12:16:07 +0300 (tlt 10 Jan 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2 marque client suite se correction partie JS

## Files changed

## Full metadata
------------------------------------------------------------------------
r10123 | srazana<PERSON>lisoa | 2023-01-10 12:16:07 +0300 (tlt 10 Jan 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Ancestors/Views/View.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Templates/selectFileList.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileList.html
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Templates/MarqueClient.html
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/MarqueClientView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/i18n.js

wishlist IDEO3.2 marque client suite se correction partie JS
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Ancestors/Views/View.js
===================================================================
--- src/js/JEditor/Commons/Ancestors/Views/View.js	(révision 10122)
+++ src/js/JEditor/Commons/Ancestors/Views/View.js	(révision 10123)
@@ -57,7 +57,7 @@
                         window.clearTimeout(this.___inputTimer[uid]);
                     this.___inputTimer[uid] = window.setTimeout(_.bind(function () {
                         this.___onInputChange($target);
-                    }, this), 100);
+                    }, this), 1000);
                     event.stopImmediatePropagation();
                 },
                 ___onModelChange: function (model, options) {
Index: src/js/JEditor/Commons/Files/Templates/selectFileList.html
===================================================================
--- src/js/JEditor/Commons/Files/Templates/selectFileList.html	(révision 10122)
+++ src/js/JEditor/Commons/Files/Templates/selectFileList.html	(révision 10123)
@@ -1,7 +1,7 @@
 <%for(i=0;i<content.length;i++){
     file = content[i];
     %>
-
+    <%if(!file.isLogo){%>
         <div class="wrapper oneFile <%=(selected && selected==file.id)?'selected':''%> <%=(file.previewClass())%>" data-id="<%=file.id%>" <%if(file.isImg()){%>style="background-image: url(<%= file.thumb %>);"<%}%>>
              <span class="select"><span class="icon-check"></span></span>
 
@@ -15,5 +15,5 @@
             <div class="wrapperTitle"><%=file.title[lang]%></div>
         </div>
         
-
+    <%}%>
     <%}%>
\ No newline at end of file
Index: src/js/JEditor/ParamsPanel/Templates/MarqueClient.html
===================================================================
--- src/js/JEditor/ParamsPanel/Templates/MarqueClient.html	(révision 10122)
+++ src/js/JEditor/ParamsPanel/Templates/MarqueClient.html	(révision 10123)
@@ -14,7 +14,25 @@
         margin-bottom: 15px;
         cursor: pointer;
     }
-
+    .relative{
+        position: relative;
+    }
+    .rigth-delete-image {
+        background-color: #f4f4f4;
+        padding: 0 5px;
+        height: 23px;
+        border-radius: 20px;
+        width: 10px;
+        border: 5px solid #fff;
+        position: absolute;
+        margin-right: -10px;
+        display: none;
+        line-height: 20px;
+        text-align: center;
+        font-size: 12px;
+        cursor: pointer;
+        z-index: 111;
+    }
     .shortcode {
         font-family: 'Open Sans',sans-serif;
         font-weight: 400;
@@ -64,10 +82,11 @@
                 <div class="content scroll-container">
                     <div class="inline-params thin-border radius shadow">
                         <div class="inline-logo">
-                            <div class="group-content-logo">
+                            <div class="group-content-logo relative">
                                 <span class="inline-params__nameLogo">
                                     <%=__("Add_logo")%>
                                 </span>
+                                <span class="rigth-delete-image icon-bin" data-name="Logo" style="top:26%;right:14%;"> </span>
                                 <div class="menu-wrapper-logo file image">
                                     <header></header>
                                 </div>
@@ -76,10 +95,11 @@
                                     [[contact_logo]]
                                 </span>
                             </div>      
-                            <div class="group-content-logo-small">
+                            <div class="group-content-logo-small relative">
                                 <span class="inline-params__nameLogo">
                                     <%=__("Add_logo_small")%>
                                 </span>
+                                <span class="rigth-delete-image icon-bin" data-name="LogoSmall" style="top: 24%;right: 22%;"> </span>
                                 <div class="menu-wrapper-logoSmall file image">
                                     <header></header>
                                 </div>
@@ -87,10 +107,11 @@
                                     [[contact_logo|format=small]]
                                 </span>
                             </div>
-                            <div class="group-content-favicon">
+                            <div class="group-content-favicon relative">
                                 <span class="inline-params__nameLogo">
                                     <%=__("Add_favicon")%>
                                 </span>
+                                <span class="rigth-delete-image icon-bin" data-name="Favicon" style="top: 24%;right: 14%;"> </span>
                                 <div class="menu-wrapper-favicon file image">
                                     <header></header>
                                 </div>
Index: src/js/JEditor/ParamsPanel/Views/MarqueClientView.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 10122)
+++ src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 10123)
@@ -6,6 +6,7 @@
         "JEditor/Commons/Files/Views/FileUploaderView",
         "JEditor/Commons/Files/Models/File",
         'text!../Templates/MarqueClient.html',
+        "JEditor/App/Messages/Confirm", 
         'i18n!../nls/i18n',
         "jqueryPlugins/uploader"
     ], function (
@@ -17,6 +18,7 @@
             FileUploaderView,
             File,
             template,
+            Confirm,
             translate) {
     var MarqueClient = View.extend({
         currentFile: null,
@@ -29,6 +31,7 @@
             'click .menu-wrapper-logo ' : '_onlyComputerLogo',
             'click .menu-wrapper-logoSmall ' : '_onlyComputerLogoSmall',
             'click .menu-wrapper-favicon ' : '_onlyComputerFavicon',
+            'click .rigth-delete-image[data-name]': 'deleteOne',
             'click .shortcode': 'copyToClipboard'
         },
 
@@ -45,7 +48,7 @@
          },
 
         _onUpload: function(file) {
-            file.attributes.isLogo = "logo";
+            if (this.currentFileLogo) this._deleteLastFile('Logo');
             this.currentFileLogo = new File(file.attributes);
             this.model.set('Logo', this.currentFileLogo);
             this.model.set('LogoSmall', this.currentFileLogoSmall);
@@ -54,10 +57,11 @@
             this.$('.group-content-logo .uploader .view').addClass('done');
             this.$('.menu-wrapper-logo .uploader .preview .imagepreview').css({opacity: 1});
             this.$('.menu-wrapper-logo .uploader .preview .imagepreview .progressbar').css({width: 0});
+            this.$('.group-content-logo .rigth-delete-image').show();
         },
 
         _onUpload2: function(file) {
-            file.attributes.isLogo = "logoSmall";
+            if (this.currentFileLogoSmall) this._deleteLastFile('LogoSmall');
             this.currentFileLogoSmall = new File(file.attributes);
             this.model.set('Logo', this.currentFileLogo);
             this.model.set('LogoSmall', this.currentFileLogoSmall);
@@ -66,10 +70,11 @@
             this.$('.group-content-logoSmall .uploader .view').addClass('done');
             this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview').css({opacity: 1});
             this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview .progressbar').css({width: 0});
+            this.$('.group-content-logo-small .rigth-delete-image').show();
         },
 
         _onUpload3: function(file) {
-            file.attributes.isLogo = "favicon";
+            if (this.currentFileFavicon) this._deleteLastFile('Favicon');
             this.currentFileFavicon = new File(file.attributes);
             this.model.set('Logo', this.currentFileLogo);
             this.model.set('LogoSmall', this.currentFileLogoSmall);
@@ -78,6 +83,7 @@
             this.$('.group-content-favicon .uploader .view').addClass('done');
             this.$('.menu-wrapper-favicon .uploader .preview .imagepreview').css({opacity: 1});
             this.$('.menu-wrapper-favicon .uploader .preview .imagepreview .progressbar').css({width: 0});
+            this.$('.group-content-favicon .rigth-delete-image').show();
         },
 
         _onlyComputerLogo: function(e) {
@@ -86,7 +92,11 @@
             this.$('.menu-wrapper-logo .uploader .actions-wrapper').removeClass('visible');
             this.$('.menu-wrapper-logo .uploader .actions-wrapper .actions .computer input:file.upload').trigger('click');
         },
-
+        _deleteLastFile: function (name) {
+            var cid = this.model.get(name).id;
+            var model = this.fileCollection.get(cid);
+            model.destroy(); 
+        },
         
         _onlyComputerLogoSmall: function(e) {
             e.preventDefault();
@@ -141,6 +151,7 @@
 					acceptedTypes : [ 'image' ],
 					acceptedExtensions : ['jpeg','jpg','gif','png', 'svg'],
                     refusedExtensions:['bmp'],
+                    uploadUrl : __IDEO_UPLOADLOGO_URL__? __IDEO_UPLOADLOGO_URL__:'/admin/resource-setting',
 				},
 
 			});
@@ -154,6 +165,7 @@
 					acceptedTypes : [ 'image' ],
 					acceptedExtensions : ['jpeg','jpg','gif','png', 'svg'],
                     refusedExtensions:['bmp'],
+                    uploadUrl : __IDEO_UPLOADLOGO_URL__? __IDEO_UPLOADLOGO_URL__:'/admin/resource-setting',
 				}
 			});
 
@@ -166,6 +178,7 @@
 					acceptedTypes : [ 'image' ],
 					acceptedExtensions : ['jpeg','jpg','png'],
                     refusedExtensions:['bmp'],
+                    uploadUrl : __IDEO_UPLOADLOGO_URL__? __IDEO_UPLOADLOGO_URL__:'/admin/resource-setting',
 				}
 			});
 
@@ -182,14 +195,17 @@
             if(this.model.attributes.Logo && (!Array.isArray(this.model.attributes.Logo))){
                 this.$('.group-content-logo .uploader').addClass('done');
                 this.$('.menu-wrapper-logo .uploader .preview .imagepreview').css({opacity: 1});
+                this.$('.group-content-logo .rigth-delete-image').show();
             }
             if(this.model.attributes.LogoSmall && (!Array.isArray(this.model.attributes.LogoSmall))){
                 this.$('.group-content-logo-small .uploader').addClass('done');
                 this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview').css({opacity: 1});
+                this.$('.group-content-logo-small .rigth-delete-image').show();
             }
             if(this.model.attributes.Favicon && (!Array.isArray(this.model.attributes.Favicon))){
                 this.$('.group-content-favicon .uploader').addClass('done');
                 this.$('.menu-wrapper-favicon .uploader .preview .imagepreview').css({opacity: 1});
+                this.$('.group-content-favicon .rigth-delete-image').show();
             }
             this.$('.menu-wrapper-logo .uploader .preview .imagepreview').after('<span style="font-size:9px;display:block;margin-top:35px;color: #666;text-align:center;">'+this.translations.DefaultMessageUploaderLogo+'</span>');
             this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview').after('<span style="font-size:9px;display:block;margin-top:35px;color: #666;text-align:center;">'+this.translations.DefaultMessageUploaderLogo+'</span>');
@@ -196,6 +212,40 @@
             this.$('.menu-wrapper-favicon .uploader .preview .imagepreview').after('<span style="font-size:9px;display:block;margin-top:35px;color: #666;text-align:center;">'+this.translations.DefaultMessageUploaderLogo+'</span>');
             return this;
         },
+        confirm: function(params) {
+            this.app.messageDelegate.set(new Confirm(params));
+        },
+        deleteOne: function(e) {
+            e.stopImmediatePropagation();
+            var $target = $(e.currentTarget);
+            var name = $target.data('name');
+            var cid = this.model.get(name).id;
+            var model = this.fileCollection.get(cid);
+            if(!this.app.user.can("delete_file")){
+                return false;
+            }
+                if (!this.app.params.dontAskAgainFor['deleteFileItem']) {
+                    this.confirm({
+                        message: translate('confirmDelete', {'name': name}),
+                        title: translate("deleteAction"),
+                        type: 'delete',
+                        onOk: _.bind(function() {
+                            this.model.set(name, null);
+                            model.destroy();
+                            this.model.save();
+                            this.render();
+                        }, this),
+                        options: {dialogClass: 'delete no-close', dontAskAgain: true, subject: 'deleteFileItem'}
+                    });
+                } else {
+                    this.model.set(name, null);
+                    model.destroy();
+                    this.model.save();
+                    this.render();
+                }
+            
+            return false;
+        },
 
         copyToClipboard : function (e){
             var copyText = $(e.currentTarget).text();
Index: src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 10122)
+++ src/js/JEditor/ParamsPanel/nls/fr-ca/i18n.js	(révision 10123)
@@ -62,6 +62,7 @@
         "set_marque_client": "Renseignez les informations de Marque du client tel que l'enseigne, le logo ...",
         "DefaultMessageUploaderLogo": "Cliquez ici ou glissez-déposez une image",
         "copy":"Copié", 
+        "confirmDelete": "Vous êtes sur le point de supprimer<br/>définitivement l'élément :</br><strong><% name %></strong>",
 
 
     });
Index: src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 10122)
+++ src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 10123)
@@ -65,5 +65,6 @@
         "set_marque_client": "Renseignez les informations de Marque du client tel que l'enseigne, le logo ...",
         "DefaultMessageUploaderLogo": "Cliquez ici ou glissez-déposez une image",
         "copy":"Copié", 
+        "confirmDelete": "Vous êtes sur le point de supprimer<br/>définitivement l'élément :</br><strong><% name %></strong>",
 
     });
Index: src/js/JEditor/ParamsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 10122)
+++ src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 10123)
@@ -67,6 +67,7 @@
         "set_marque_client": "Fill in the customer's brand information such as the brand name, the logo ...",
         "DefaultMessageUploaderLogo": "Click here or drag and drop an image",
         "copy":"Copied", 
+        "confirmDelete": "You are about to definitely <br/>suppress the element :</br><strong><% name %></strong>",
     },
     "fr-fr": true,
     "fr-ca": true
Index: src/js/JEditor/FilePanel/Templates/fileList.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileList.html	(révision 10122)
+++ src/js/JEditor/FilePanel/Templates/fileList.html	(révision 10123)
@@ -5,51 +5,8 @@
             <% for(var i=0; i< content.length; i++){
             var file = content[i];
             %>
+            <%if(!file.isLogo){%>
             <div class="menu-wrapper file image <%=(selected[file.cid]===true)?'selected':''%> <%=(file.previewClass())%>" data-cid="<%=file.cid %>"  <%if(file.isImg()){%>style="background-image: url(<%= file.thumb %>);"<%}%>>
-
-                 <span class="select"><span class="icon-check"></span></span>
-                 <%if(file.isFavicon() && file.isLogo){%>
-                    <div class="property-favicon">
-                        <span class="actions informations">
-                            <span class="icon-information"></span>
-                            <div class="infobulles">
-                                <p>
-                                    <%= __("imageFaviconAndLogo")%>
-                                </p>
-                            </div>
-                        </span>
-                    </div>
-                 <%}else{%>
-                    <%if(file.isFavicon()){%>
-                        <div class="property-favicon">
-                            <span class="actions informations">
-                                <span class="icon-information"></span>
-                                <div class="infobulles">
-                                    <p>
-                                        <%= __("imageFavicon")%>
-                                    </p>
-                                </div>
-                            </span>
-                        </div>
-                    
-                    <%}%>
-
-                    <%if(file.isLogo){%>
-                        <div class="property-favicon">
-                            <span class="actions informations">
-                                <span class="icon-information"></span>
-                                <div class="infobulles">
-                                    <p>
-                                        <%= __("imageLogo")%>
-                                    </p>
-                                </div>
-                            </span>
-                        </div>
-                    
-                    <%}%>
-                <% } %>
-
-
                 <%if(!file.isImg()){%>
                 <span class="icon-file ext">
                     <span class="extLabel"><%=file.ext%></span>
@@ -69,8 +26,8 @@
                 </ul>
 
             </div>
-
             <% } %>
+            <% } %>
             <div class="menu-wrapper add-file" data-action="showuploader">
 
                 <span class="icon">
