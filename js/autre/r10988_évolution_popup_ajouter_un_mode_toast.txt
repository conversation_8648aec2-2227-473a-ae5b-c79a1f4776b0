Revision: r10988
Date: 2023-06-02 07:40:49 +0300 (zom 02 Jon 2023) 
Author: rrakotoarinelina 

## Commit message
évolution popup : ajouter un mode toast

## Files changed

## Full metadata
------------------------------------------------------------------------
r10988 | rrakotoarinelina | 2023-06-02 07:40:49 +0300 (zom 02 Jon 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/dev/#librairies/vendor/CoreMoteurDeRendu/src/CoreMoteurDeRendu/Core/Ambiance/Section.php
   M /branches/ideo3_v2/dev/#librairies/vendor/CoreMoteurDeRendu/src/CoreMoteurDeRendu/Core/AmbianceEngineRenderer.php
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/Models/PopupStyle.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/Templates/popupStyle.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/Views/PopupStyleView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/page_panel/module/block-options/advanced-option.less

évolution popup : ajouter un mode toast
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Options/Models/PopupStyle.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/Models/PopupStyle.js	(révision 10987)
+++ src/js/JEditor/PagePanel/Contents/Options/Models/PopupStyle.js	(révision 10988)
@@ -9,12 +9,14 @@
          *  @property {String} size size du popup
          */
         var allowedSizes=["tiny","small","medium","large"];
+        var allowedDisplays=["modal","toast"];
+        var allowedAlignment=["bottom-right","top-right","bottom-left","top-left"];
         var PopupStyle = AbstractOption.extend(
                 /**
                  * @lends PopupStyle.prototype
                  */
                         {
-                            defaults: {optionType: 'popupStyle', size:'medium' , priority: 60},
+                            defaults: {optionType: 'popupStyle', size:'medium', display:'toast',alignment:'bottom-right', priority: 60},
                             initialize: function() {
                                 this._super();
                             },
@@ -25,7 +27,7 @@
                             },
                             translate: translate
                         });
-                PopupStyle.SetAttributes(['size']);
+                PopupStyle.SetAttributes(['size','display','alignment']);
                 PopupStyle.tanslate = translate;
                 return PopupStyle;
             });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Options/Templates/popupStyle.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/Templates/popupStyle.html	(révision 10987)
+++ src/js/JEditor/PagePanel/Contents/Options/Templates/popupStyle.html	(révision 10988)
@@ -1,4 +1,4 @@
-<div class="panel-option-container animated popup-size">
+<div class="panel-option-container  animated popup-size">
     <article class="panel-option">
         <header>
             <h3 class="option-name">
@@ -9,7 +9,7 @@
                 <%=__("sizePopupLegend")%>
             </span>
         </header>
-        <div class="option-content">
+        <div class="option-content ">
             <div class="controlPanel-selector">
                 <div class="option radio">
                         <div class="button-size-radio">
@@ -58,5 +58,123 @@
                 </div>
             </div>
         </div>
+        
+  
+        <!--  style alignment -->
+        
+        <!-- .gallery-template-option .category-content.radio-transformed -->
+        
+        <!--  style alignment -->
+        <div class="gallery-template-option popupDisplayContainer">
+       
+            <header>
+            <h3 class="option-name">
+                <span class="icon-stars icon-midsize"></span>
+                <%=__("displayPopup")%>
+            </h3>
+            <span class="panel-content-legend">
+            <%=__("displayPopupLegend")%>
+            </span>
+            </header>
+            <div class="category-content radio-transformed " style="">
+            <div>
+                <span class="effect-radio display-choice <%=(display==='modal')?'active':''%>" data-value="modal" data-helper="">
+                <span class="helper">
+                <span class="help">Dialog</span>
+                <span class="bottom"></span>
+                </span>
+                <span class="container">
+                <span class=" icon-modal">
+                </span>
+                <span class="switch-container">
+                <span class="radio"><span>
+                </span>
+                </span>
+                </span>
+            </div>
+            <div>
+                <span class="effect-radio display-choice <%=(display==='toast')?'active':''%>" id="" data-value="toast" data-helper="">
+                <span class="helper">
+                <span class="help">Toast</span>
+                <span class="bottom"></span>
+                </span>
+                <span class="container">
+                <span class="icon-toast">
+                </span>
+                <span class="switch-container">
+                <span class="radio"><span>
+                </span>
+                </span>
+                </span>
+            </div>
+            </div>
+        </div>
+        
+        <!--  style pour toast  -->
+        <div class="alignment-container">
+        <header>
+            <h3 class="option-name">
+                <span class="icon-button-size icon-midsize"></span>
+                <%=__("alignmentPopup")%>
+            </h3>
+            <span class="panel-content-legend">
+                <%=__("alignmentPopupLegend")%>
+            </span>
+        </header>
+        <div class="option-content">
+            <div class="controlPanel-selector">
+                <div class="option radio">
+                        <div class="button-size-radio">
+                            <label for="bottom-right" class="inline-block-label">
+                                <input id="bottom-right" type="radio" class="field-input" value="top-right" name="alignment" <%=alignement==="top-right"? ' checked="checked" ':'' %> />
+                                
+                                <div class="inline-block-label__top">
+                                    <span class="icon-align-top_right"></span>
+                                </div>
+                                <div class="inline-block-label__bottom">
+                                    <span class="icon-radio-inactive"></span>
+                                    <span class="icon-radio-active"></span>
+                                </div>
+                            </label>
+                            <label for="top-right" class="inline-block-label">
+                                <input id="top-right" type="radio" class="field-input" value="bottom-right" name="alignment"  <%=alignement==="bottom-right"? ' checked="checked" ':'' %>/>
+                                <div class="inline-block-label__top">
+                                    <span class="icon-align-bottom_right"></span>
+                                </div>
+                                <div class="inline-block-label__bottom">
+                                    <span class="icon-radio-inactive"></span>
+                                    <span class="icon-radio-active"></span>
+                                </div>
+                            </label>
+                            <label for="bottom-left" class="inline-block-label">
+                                <input id="bottom-left" type="radio" class="field-input" value="top-left" name="alignment" <%=alignement==="top-left"? ' checked="checked" ':'' %>/>
+                                <div class="inline-block-label__top">
+                                    <span class="icon-align-top_left"></span>
+                                </div>
+                                <div class="inline-block-label__bottom">
+                                    <span class="icon-radio-inactive"></span>
+                                    <span class="icon-radio-active"></span>
+                                </div>
+                            </label>
+
+                            <label for="top-left" class="inline-block-label">
+                                <input id="top-left" type="radio" class="field-input" value="bottom-left" name="alignment" <%=alignement==="bottom-left"? ' checked="checked" ':'' %> />
+                                <div class="inline-block-label__top">
+                                    <span class="icon-align-bottom_left"></span>
+                                </div>
+                                <div class="inline-block-label__bottom">
+                                    <span class="icon-radio-inactive"></span>
+                                    <span class="icon-radio-active"></span>
+                                </div>
+                            </label>
+                        </div>
+                </div>
+            </div>
+        </div>
+
+    </div>
     </article>
 </div>
+
+
+
Index: src/js/JEditor/PagePanel/Contents/Options/Views/PopupStyleView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/Views/PopupStyleView.js	(révision 10987)
+++ src/js/JEditor/PagePanel/Contents/Options/Views/PopupStyleView.js	(révision 10988)
@@ -12,6 +12,8 @@
                         translate: translate,
                         events: {
                             "change input[type=radio][name=size]": "sizeChange",
+                            'click .display-choice': "displayChange",
+                            "change input[type=radio][name=alignment]": "alignmentChange",
                         },
                         /**
                          * initialise l'objet
@@ -24,14 +26,44 @@
                          * actualise l'affichage de la vue
                          */
                         render: function() {
-                            var templateVars = {size:this.model.size};
+       
+                            var templateVars = {size:this.model.size,display:this.model.display,alignement:this.model.alignment};
                             this.$el.html(this.template(templateVars));
+                            if (this.model.display =="modal") {
+                                this.$(".alignment-container").css("display", "none");
+                                this.model.alignement="bottom-right";
+                            }else{
+                                this.$(".alignment-container").css("display", "block");
+                            }
                             this.scrollables();
                             return this;
+
                         },
                         sizeChange:function(event){
-                             this.model.size = $(event.currentTarget).value;
-                        }
+                            var size = $(event.currentTarget).val();
+                            this.model.size = size;
+                        },
+                        alignmentChange:function(event){
+                            this.model.alignement = $(event.currentTarget).alignement;
+                        },
+                        /**
+                         * action changer de style d'affichage
+                         * @param {*} event 
+                         */
+                        displayChange:function(event){
+                            this.$(".display-choice").removeClass("active");
+                            var $target = $(event.currentTarget);
+                            $target.addClass("active");
+                            var value = $target.attr("data-value");
+                            if (value =="modal") {
+                                this.$(".alignment-container").css("display", "none");
+                                this.model.alignement="bottom-right";
+                            }else{
+                                this.$(".alignment-container").css("display", "block");
+                            }
+
+                             this.model.display=value;
+                        },  
             }
     );
     PopupStyleOptionView.translate = translate;
Index: src/js/JEditor/PagePanel/Contents/Options/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/nls/fr-ca/i18n.js	(révision 10987)
+++ src/js/JEditor/PagePanel/Contents/Options/nls/fr-ca/i18n.js	(révision 10988)
@@ -65,6 +65,10 @@
     "popupStyle": "Style",
     "sizePopup": "Taille de la popup",
     "sizePopupLegend": "Sélectionnez la taille de la popup ici",
+    "displayPopup": "Style",
+    "displayPopupLegend": "Sélectionnez le style d'affichage du popup",
+    "alignmentPopup": "Alignement",
+    "alignmentPopupLegend": "Sélectionnez l'alignement du popup dans la fenêtre",
     "datePlaceholder": "dd/mm/yyyy",
     "styles" : "Styles",
     "BlockColor"  : "Couleurs du bloc",
Index: src/js/JEditor/PagePanel/Contents/Options/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/nls/fr-fr/i18n.js	(révision 10987)
+++ src/js/JEditor/PagePanel/Contents/Options/nls/fr-fr/i18n.js	(révision 10988)
@@ -65,6 +65,10 @@
     "popupStyle": "Style",
     "sizePopup": "Taille de la popup",
     "sizePopupLegend": "Sélectionnez la taille de la popup ici",
+    "displayPopup": "Style",
+    "displayPopupLegend": "Sélectionnez le style d'affichage du popup",
+    "alignmentPopup": "Alignement",
+    "alignmentPopupLegend": "Sélectionnez l'alignement du popup dans la fenêtre",
     "datePlaceholder": "dd/mm/yyyy",
     "styles" : "Styles",
     "BlockColor"  : "Couleurs du bloc",
Index: src/js/JEditor/PagePanel/Contents/Options/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/nls/i18n.js	(révision 10987)
+++ src/js/JEditor/PagePanel/Contents/Options/nls/i18n.js	(révision 10988)
@@ -67,6 +67,10 @@
     "popupStyle": "Style",
     "sizePopup": "Popup size",
     "sizePopupLegend": "Select popup size here",
+    "alignmentPopup": "Alignment",
+    "alignmentPopupLegend": "Select the alignment of the popup in the window",
+    "displayPopup": "Style",
+    "displayPopupLegend": "Select the popup display style",
     "datePlaceholder": "dd/mm/yyyy",
     "styles" : "Styles",
     "BlockColor"  : "Colors of block",
Index: src/less/imports/page_panel/module/block-options/advanced-option.less
===================================================================
--- src/less/imports/page_panel/module/block-options/advanced-option.less	(révision 10987)
+++ src/less/imports/page_panel/module/block-options/advanced-option.less	(révision 10988)
@@ -38,7 +38,32 @@
 	margin-top: 0.75rem;
 }
 
+span.icon-modal{
+	font-size: 2rem;
+	position: absolute;
+	margin-top: 0.75rem;
+	margin-left: -1rem;
+}
 
+
+span.icon-toast{
+	font-size: 2rem;
+	position: absolute;
+	margin-top: 0.75rem;
+	margin-left: -1rem;
+}
+
+.gallery-template-option.popupDisplayContainer{
+	padding-bottom: 1rem;
+}
+
+.gallery-template-option.popupDisplayContainer .category-content.radio-transformed{
+	padding-left: 20%;
+}
+
+
+
+
 .surface0{
 	background: var(--surface0);
 	color: var(--fg-color);
