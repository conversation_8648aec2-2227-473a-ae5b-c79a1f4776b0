Revision: r10878
Date: 2023-05-03 09:27:57 +0300 (lrb 03 Mey 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2 : upload fichiers limitations 

## Files changed

## Full metadata
------------------------------------------------------------------------
r10878 | sraz<PERSON><PERSON><PERSON><PERSON> | 2023-05-03 09:27:57 +0300 (lrb 03 Mey 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Views/FileUploaderView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/CollectionDetailView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileListView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/MarqueClientView.js

wishlist IDEO3.2 : upload fichiers limitations 
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Files/Views/FileUploaderView.js
===================================================================
--- src/js/JEditor/Commons/Files/Views/FileUploaderView.js	(révision 10877)
+++ src/js/JEditor/Commons/Files/Views/FileUploaderView.js	(révision 10878)
@@ -69,6 +69,9 @@
                 this.collection.add(this.currentFile);
                 this.trigger(Events.FileUploaderEvents.UPLOAD, this.currentFile, this.collection);
             }
+            else if (data.error) {
+                this.trigger(Events.FileUploaderEvents.TOOBIG, data);
+            }
         },
         openAllFileSelector: function() {
             var selectFileView;
@@ -129,7 +132,8 @@
     };
     Events.extend({
         FileUploaderEvents: {
-            UPLOAD: 'upload'
+            UPLOAD: 'upload',
+            TOOBIG: 'toobig',
         }
     });
     return FileUploaderView;
Index: src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js
===================================================================
--- src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 10877)
+++ src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 10878)
@@ -62,6 +62,9 @@
                 refusedTypes: ['exploit'],
                 maxFiles: 5,
                 maxSize: 20000000,
+                alertmsg: false,
+                maxSizeVideo: 15000000,
+                maxSizeFile : 5000000,
                 defaultPreview: undefined,
                 showMenu: true,
                 menuContainer: this.element,
@@ -282,6 +285,11 @@
             var typePattern = /^image\/.*$/;
             return (extPattern.test(file.name.toLowerCase()) || typePattern.test(file.type.toLowerCase()));
         },
+        _isVideo: function(file) {
+            var extPattern = /^.*\.mp4$/;
+            var typePattern = /^video\/.*$/;
+            return (extPattern.test(file.name.toLowerCase()) || typePattern.test(file.type.toLowerCase()));
+        },
         _getFileExt: function(file) {
             var ext = /^.*\.([a-zA-Z0-9]{1,4})$/;
             return ext.exec(file.name)[1];
@@ -315,7 +323,14 @@
                     if (files.length === 1)
                         this._onComplete();
                 }
-                else if (files[i].size > this.options.maxSize) {
+                else if (uploader._isVideo(files[i]) && files[i].size > this.options.maxSizeVideo) {
+                        message = this.options.lang.uploadFailTooBig;
+                        this.errors.push(message.replace('%name%', files[i].name));
+                        files.rejected++;
+                        if (files.length === 1)
+                            this._onComplete();
+                } 
+                else if (!uploader._isVideo(files[i]) && files[i].size > this.options.maxSizeFile) {
                     message = this.options.lang.uploadFailTooBig;
                     this.errors.push(message.replace('%name%', files[i].name));
                     files.rejected++;
@@ -392,11 +407,13 @@
             this._readFiles(files);
         },
         _onComplete: function() {
+            var errorMsg = '';
             if (this.errors.length > 0) {
                 var errorList = $(document.createElement('ul')).addClass('errors');
                 for (var i = 0; i < this.errors.length; i++) {
                     errorList.append('<li>' + this.errors[i] + '</li>');
                 }
+                errorMsg = errorList.html();
                 this.$message.find('p').text(this.options.lang.uploadFailErrorsOccured);
                 this.$message.append(errorList);
                 this.errors = [];
@@ -418,7 +435,7 @@
                 that.$menu.hide();
             });
             this.$menu.hide();
-            this._trigger('complete', null, {filesData: this.uploaded});
+            this._trigger('complete', null, {filesData: this.uploaded , error: errorMsg });
         },
         _globalProgress: function() {
             var total = 0;
Index: src/js/JEditor/FilePanel/Views/CollectionDetailView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/CollectionDetailView.js	(révision 10877)
+++ src/js/JEditor/FilePanel/Views/CollectionDetailView.js	(révision 10878)
@@ -37,7 +37,6 @@
             'click .file .action.delete[data-cid]': 'deleteOne',
             'change input.collectionName': 'setName',
             'uploadercomplete .group-content': 'uploadercomplete',
-           // 'uploadertoobig .group-content': 'uploaderTooBig',
             'click [data-action="showuploader"]': 'showUploader',
             'sortstop .group-content': 'onSortStop',
             'click .shortcode-collection.shortcode' : 'copyToClipboard'
@@ -124,16 +123,21 @@
                 this.trigger(Events.ListViewEvents.UPLOADER_COMPLETE, file, this.model);
             }
             this.model.save();
-
+            if (data.error!=='') {
+                this.uploaderTooBig(data.error)
+            }
             return false;
         },
-        // uploaderTooBig: function(e, data) {
-        //     var msg = data.error;
-        //     this.error({
-        //         title: translate("upload"),
-        //         message: msg
-        //     });
-        // },
+        /**
+         * affichage de la message d'erreur
+         * @param {*} msg 
+         */
+        uploaderTooBig: function(msg) {
+            this.error({
+                title: translate("upload"),
+                message: msg
+            });
+        },
         showUploader: function() {
             this.dom[this.cid].showUploader.trigger('click');
         },
Index: src/js/JEditor/FilePanel/Views/FileListView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileListView.js	(révision 10877)
+++ src/js/JEditor/FilePanel/Views/FileListView.js	(révision 10878)
@@ -24,7 +24,6 @@
             'click .file .action.delete[data-cid]': 'deleteOne',
             'uploadercomplete .group-content': 'uploadercomplete',
             'uploaderstart .group-content': 'uploaderstart',
-            'uploaderfail .group-content': 'uploaderfail',
             'click [data-action="showuploader"]': 'showUploader',
             'click .flex-load-more .load-more' : 'loadMore',
             'click .flex-load-more .load-more' : 'loadMore',
@@ -77,11 +76,10 @@
             $(e.currentTarget).children(".add-file").hide();
             $(e.currentTarget).children(".myfiles-empty").remove();
         },
-        uploaderfail: function(e, data){
-            console.log(data);
+        uploaderTooBig: function(msg) {
             this.error({
                 title: translate("upload"),
-                message: translate("uploadFailTooBig").replace('%name%', data.uploadedFile.name)
+                message: msg
             });
         },
         toggleSelected: function(e) {
@@ -194,11 +192,15 @@
         },
         render: function() {
             this._super();
+            var acceptedtypes =  ['image', 'audio']
             if(!this.app.user.can("delete_file")){
                 this.$("li.action.delete").replaceWith('<li class="" style="visibility: hidden;"></li>');
             }
+            if(this.app.user.can("upload_video")){
+               acceptedtypes.push('video/mp4');
+            }
             this.dom[this.cid].uploadZone = this.$('.group-content');
-            this.dom[this.cid].uploadZone.uploader({showMenu: false, maxFiles: -1, lang: translate.translations, acceptedTypes: ['image', 'audio', 'video/mp4']});
+            this.dom[this.cid].uploadZone.uploader({showMenu: false, maxFiles: -1, lang: translate.translations, acceptedTypes: acceptedtypes});
             this.dom[this.cid].showUploader = this.$('input.upload.button');
             return this;
         },
@@ -205,6 +207,7 @@
         uploadercomplete: function(e, data) {
             this.resetFilter();
             this.trigger(Events.ListViewEvents.UPLOADER_COMPLETE, e, data);
+            if (data.error !=='') this.uploaderTooBig(data.error);
             return false;
         },
         showUploader: function() {
Index: src/js/JEditor/ParamsPanel/Views/MarqueClientView.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 10877)
+++ src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 10878)
@@ -204,7 +204,12 @@
             this.listenTo(this.fileUploader, Events.FileUploaderEvents.UPLOAD, this._onUpload);
             this.listenTo(this.fileUploader2, Events.FileUploaderEvents.UPLOAD, this._onUpload2);
             this.listenTo(this.fileUploader3, Events.FileUploaderEvents.UPLOAD, this._onUpload3);
+            // en cas echec d'upload on re-render les marqueClients
+            this.listenTo(this.fileUploader, Events.FileUploaderEvents.TOOBIG, this.render);
+            this.listenTo(this.fileUploader2, Events.FileUploaderEvents.TOOBIG, this.render);
+            this.listenTo(this.fileUploader3, Events.FileUploaderEvents.TOOBIG, this.render);
 
+
             this.$('.inline-logo .group-content-logo header').after(this.fileUploader.el);
             this.$('.inline-logo .group-content-logo-small header').after(this.fileUploader2.el);
             this.$('.inline-logo .group-content-favicon header').after(this.fileUploader3.el);
