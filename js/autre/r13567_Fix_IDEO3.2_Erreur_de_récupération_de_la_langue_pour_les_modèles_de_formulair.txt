Revision: r13567
Date: 2024-12-04 12:42:15 +0300 (lrb 04 Des 2024) 
Author: jn.harison 

## Commit message
Fix IDEO3.2:Erreur de récupération de la langue pour les modèles de formulaires

## Files changed

## Full metadata
------------------------------------------------------------------------
r13567 | jn.harison | 2024-12-04 12:42:15 +0300 (lrb 04 Des 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormTemplateList.js

Fix IDEO3.2:Erreur de récupération de la langue pour les modèles de formulaires
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormTemplateList.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormTemplateList.js	(révision 13566)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormTemplateList.js	(révision 13567)
@@ -6,7 +6,7 @@
         },
         initialize:function () {
             View.prototype.initialize.call(this);
-            this.lang = (this.app.params) ? this.app.params.id : this.app.currentPanel.currentArticle.lang;
+            this.lang = (this.app.currentPanel.currentPage)? this.app.currentPanel.currentPage.lang : this.app.currentPanel.currentArticle.lang;
             this.template=this.buildTemplate(tpl,i18n);
         },
         render:function () {
