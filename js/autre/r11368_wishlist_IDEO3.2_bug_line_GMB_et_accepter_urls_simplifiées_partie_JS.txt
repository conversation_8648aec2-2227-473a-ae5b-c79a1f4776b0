Revision: r11368
Date: 2023-10-05 11:07:32 +0300 (lkm 05 Okt 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2 : bug line GMB et accepter urls simplifiées(partie JS)

## Files changed

## Full metadata
------------------------------------------------------------------------
r11368 | srazanandralisoa | 2023-10-05 11:07:32 +0300 (lkm 05 Okt 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Models/Params.js

wishlist IDEO3.2 : bug line GMB et accepter urls simplifiées(partie JS)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Models/Params.js
===================================================================
--- src/js/JEditor/ParamsPanel/Models/Params.js	(révision 11367)
+++ src/js/JEditor/ParamsPanel/Models/Params.js	(révision 11368)
@@ -19,7 +19,7 @@
         facebookUrl:/^(https?\:\/\/)?(www\.)?facebook\.com\/(.*)/,
         twitterUrl:/^(https?\:\/\/)?(www\.)?twitter\.com\/(?:#!\/)?([a-zA-Z0-9\._-]+)/,
         //googleUrl:/^(https?\:\/\/)([^\.]+\.)?plus.google.com\/((u\/0\/)?(?:[^\/]*))?/,
-        mybusinessUrl:/^(https?\:\/\/)?(www\.)?goo(\.)?gl(e\.com)?(\/maps\/.+)/,
+        mybusinessUrl:/^(https?\:\/\/)?(www\.)?goo(\.)?gl(e\.com)?(\/maps\/.+)|^(https?\:\/\/)?maps\.app\.goo\.gl\/(.*)/,
         pinterestUrl:/^(https?\:\/\/)?([a-zA-Z]*\.)?pinterest\.(fr|com)\/([a-zA-Z0-9\._-]+)\/?/,
         viadeoUrl:/^(https?\:\/\/)?([a-zA-Z]*\.)?(viadeo\.com|emploi\.lefigaro\.fr)\/(.*)/,
         linkedinUrl:/^(https?\:\/\/)?(www\.)?linkedin\.com\/(.*)/,
@@ -94,9 +94,9 @@
         validateSocialNetworks:function (attributes) {
             if (attributes) { 
                  var type = attributes.type ;
-                 console.log("attributes.url.match(regExes['quotes']");
-                 console.log(attributes.url.match(regExes['quotes']));
-                 if (attributes.url && (!attributes.url.match || !attributes.url.match(regExes[type]) || !attributes.url.match(regExes['quotes'])  ))
+                //  console.log("attributes.url.match(quotesRegex)");
+                //  console.log(attributes.url.match(quotesRegex));
+                 if (attributes.url && (!attributes.url.match || !attributes.url.match(regExes[type]) || !attributes.url.match(quotesRegex)  ))
                     return false;
                  else return true;
             }
@@ -108,7 +108,7 @@
             var socialNetworks = ["facebookUrl", "mybusinessUrl", "twitterUrl", "pinterestUrl", "youtubeUrl", "linkedinUrl", "viadeoUrl", "instagramUrl","skypeUrl","theforkUrl","tiktokUrl","tripadvisorUrl","wazeUrl","whatsappUrl","slideshareUrl"];
             //en gros c'est ça dans une boucle:
             for (var i = 0; i < socialNetworks.length; i++) {             
-                if (attributes[socialNetworks[i]] && (!attributes[socialNetworks[i]].match || !attributes[socialNetworks[i]].match(regExes[socialNetworks[i]])))
+                if (attributes[socialNetworks[i]] && (!attributes[socialNetworks[i]].match || !attributes[socialNetworks[i]].match(regExes[socialNetworks[i]]) || !attributes[socialNetworks[i]].match(quotesRegex)))
                     return {field: socialNetworks[i], message: translate("Invalid_" + socialNetworks[i])};
             }
             if(attributes.GAKey && (!attributes.GAKey.match || !attributes.GAKey.match(GARegex) ))
