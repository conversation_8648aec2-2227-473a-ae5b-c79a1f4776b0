Revision: r11784
Date: 2024-01-11 14:32:00 +0300 (lkm 11 Jan 2024) 
Author: rrakotoarinelina 

## Commit message
champs date : option pour limiter à la date du jour - partie Js

## Files changed

## Full metadata
------------------------------------------------------------------------
r11784 | rrakotoarinelina | 2024-01-11 14:32:00 +0300 (lkm 11 Jan 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/Field.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/FieldBuilder.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FieldBuilderView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js

champs date : option pour limiter à la date du jour - partie Js
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/Field.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/Field.js	(révision 11783)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/Field.js	(révision 11784)
@@ -19,8 +19,9 @@
                 this.mandatory=true;
             else if(this.type === "url"){
                 this.placeholder ="https://www.exemple.com";
-            }
-            this.on("change:type",function () {
+            } else if (this.type === "date" || this.type === "datetime-local") {
+                this.options = ['previousDate_true'];
+            }this.on("change:type",function () {
                 if(this.type==="name")
                     this.mandatory=true;
             });
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/FieldBuilder.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/FieldBuilder.html	(révision 11783)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/FieldBuilder.html	(révision 11784)
@@ -47,9 +47,21 @@
                     <%= __("formMandatory") %>
                 </span>
             </label>
+        </div>
 
+        <div class="checkbox previous-date" <%= field.type !== 'date' && field.type !== 'datetime-local' ? "style='display:none;'":'' %>>
+            <input id="previous-date-<%=field.cid%>" name="previous-date" class="field-input" <%=previousDate?'checked="checked"':''%> type="checkbox" value="1" />
+            <label class="checkbox-lbl" for="previous-date-<%=field.cid%>">
+                <span class="switch">
+                    <span></span>
+                </span>
+                <span class="text">
+                    <%= __("previousDate") %>
+                </span>
+            </label>
+        </div>
 
-        </div>
+
     </div>
     <div class="button-group save-or-cancel">
         <a class="button cancel">
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FieldBuilderView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FieldBuilderView.js	(révision 11783)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FieldBuilderView.js	(révision 11784)
@@ -4,7 +4,8 @@
             "click .icon-edit":"edit",
             "click .button.save":"save",
             "click .button.cancel":"cancel",
-            "click .delete":"onDeleteClick"
+            "click .delete":"onDeleteClick",
+            'click input[name="previous-date"].field-input': 'onChangePreviousDate',
         },
         tagName:"li",
         className:function () {
@@ -24,7 +25,6 @@
             this.listenTo(this.model,"change",function () {
                 console.log(that.model);
             });
-
         },
         optionsToArray:function (options) {
             return options.split("\n");
@@ -31,7 +31,15 @@
         },
         render:function () {
             View.prototype.render.apply(this,arguments);
-            this.$el.html(this.template({field:this.model}));
+            //cacher l'option previous date pour les autre champs 
+            var previousDate = true;
+            if(this.checkPreviousDateOption(this.model.options) > -1 ){
+                var previousDateValue  = this.model.options[this.checkPreviousDateOption(this.model.options)];
+                previousDate = previousDateValue == 'previousDate_true' ? true : false;
+            }
+
+            this.$el.html(this.template({field:this.model, previousDate:previousDate }));
+
             return this;
         },
         edit:function () {
@@ -43,6 +51,26 @@
                 this.$el.parentsUntil('.col-setup','.column-container').addClass("editing");
             }
         },
+        onChangePreviousDate: function (event) {
+            var input = event.target;
+            var isChecked = input.checked;
+        
+            this.setPreviousDateValue(this.model.options, "previousDate_"+isChecked);
+        },
+        //return l'index de l'un de ces string dans le tableau model.options
+        checkPreviousDateOption: function (options) {
+            return options.findIndex(item => item === 'previousDate_true' || item === 'previousDate_false');
+        },
+        //remplacement valeur 
+        setPreviousDateValue: function (options, newValue) {
+            let hasPreviousDate = this.checkPreviousDateOption(options);
+
+            if ( parseInt(hasPreviousDate) > -1) {
+                options.splice(parseInt(hasPreviousDate), 1);
+            }
+            options.push(newValue);
+           
+        },
         save:function () {
             this.$el.removeClass("editing");
             this.$el.parentsUntil('.col-setup','.column-container').removeClass("editing");
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js	(révision 11783)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js	(révision 11784)
@@ -9,6 +9,7 @@
     "Placeholder":"Placeholder",
     "options":"Renseignez une option par ligne",
     "formMandatory":"Ce champ est obligatoire",
+    "previousDate":"Empêcher la sélection d'une date antérieure",
     "cancel":"Annuler",
     "save":"Sauvegarder",
     "contentOptions":"Formulaire",
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js	(révision 11783)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js	(révision 11784)
@@ -9,6 +9,7 @@
     "Placeholder":"Placeholder",
     "options":"Renseignez une option par ligne",
     "formMandatory":"Ce champ est obligatoire",
+    "previousDate":"Empêcher la sélection d'une date antérieure",
     "cancel":"Annuler",
     "save":"Sauvegarder",
     "contentOptions":"Formulaire",
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js	(révision 11783)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js	(révision 11784)
@@ -12,6 +12,7 @@
         "Placeholder":"Placeholder",
         "options":"Enter one option per line",
         "formMandatory":"is mandatory",
+        "previousDate": "Prevent selection of a previous date",
         "cancel":"Cancel",
         "save":"Save",
         "contentOptions":"Form",
