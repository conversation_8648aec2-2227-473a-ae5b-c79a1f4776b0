Revision: r10819
Date: 2023-04-14 08:37:33 +0300 (zom 14 Apr 2023) 
Author: norajaonarivelo 

## Commit message
WishList3.2 :Correctifs  évolution Liste fichiers coté integration

## Files changed

## Full metadata
------------------------------------------------------------------------
r10819 | norajaonarivelo | 2023-04-14 08:37:33 +0300 (zom 14 Apr 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Templates/selectFile.html
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Templates/selectFileList.html
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Templates/selectFileListManager.html
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Views/FileUploaderView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Views/ReadOnlyFileListManagerView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Views/ReadOnlyFileListView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Views/SelectFileView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/FilePanel.js
   A /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Models
   A /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Models/File.js
   A /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Models/FileCollection.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileDetail.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileList.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileManagerUIView.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/CollectionDetailView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileDetailManagerView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileListView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileManagerUIView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/ImageGroupView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalleryBlock/Views/ImageGroupView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageOptionsView.js
   M /branches/ideo3_v2/integration/src/less/imports/filePanel.less
   M /branches/ideo3_v2/integration/src/less/imports/panel_image_image.less
   M /branches/ideo3_v2/integration/src/less/main.less

WishList3.2 :Correctifs  évolution Liste fichiers coté integration
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Files/Templates/selectFile.html
===================================================================
--- src/js/JEditor/Commons/Files/Templates/selectFile.html	(révision 10818)
+++ src/js/JEditor/Commons/Files/Templates/selectFile.html	(révision 10819)
@@ -10,4 +10,5 @@
         
     </div>
 
-</div>
\ No newline at end of file
+</div>
+<div class="flex-load-more" id="loadMore"><div class="load-more"><span class="label"> <%= __('LoadMoreImage')%></span></div></div>
\ No newline at end of file
Index: src/js/JEditor/Commons/Files/Views/FileUploaderView.js
===================================================================
--- src/js/JEditor/Commons/Files/Views/FileUploaderView.js	(révision 10818)
+++ src/js/JEditor/Commons/Files/Views/FileUploaderView.js	(révision 10819)
@@ -1,5 +1,6 @@
 define([
     "JEditor/Commons/Events",
+    "JEditor/FilePanel/Models/FileCollection",
     "JEditor/Commons/Ancestors/Views/BabblerView",
     "JEditor/Commons/Files/Models/File",
     "JEditor/Commons/Files/Views/FileSelectorDialog",
@@ -6,7 +7,7 @@
     "i18n!../nls/i18n",
     //not in params
     "jqueryPlugins/uploader"
-], function(Events, BabblerView, File, FileSelectorDialog, translate) {
+], function(Events, FileCollection,BabblerView, File, FileSelectorDialog, translate) {
     var FileUploaderView = BabblerView.extend({
         currentFile: null,
         rendered: false,
@@ -20,7 +21,9 @@
         },
         initialize: function() {
             this._super();
-
+            // on utilise la nouvelle collection
+            this.collection = new FileCollection;
+            this.resetFilter();
             this.translations = translate.translations;
 
         },
@@ -83,7 +86,12 @@
             var regexpImg = /^image/;
             return regexpImg.test(file.mimeType);
         },
+        resetFilter: function(){
+            this.collection.resetFilter();
+            this.collection.fetch();
+        },
         openImageFileSelector: function() {
+            this.resetFilter();
             var selectFileView;
             selectFileView = new FileSelectorDialog({collection: this.collection});
             this.listenToOnce(selectFileView, Events.ListViewEvents.CHOOSE_FILE, this._useExistingFile);
Index: src/js/JEditor/Commons/Files/Views/ReadOnlyFileListView.js
===================================================================
--- src/js/JEditor/Commons/Files/Views/ReadOnlyFileListView.js	(révision 10818)
+++ src/js/JEditor/Commons/Files/Views/ReadOnlyFileListView.js	(révision 10819)
@@ -30,7 +30,19 @@
             this._groupTemplate = this.buildTemplate(groupSelectFileList, translate);
             this._emptyGroupTemplate = this.buildTemplate(emptyGroupSelectFileList, translate);
             this._emptyTemplate = this.buildTemplate(emptySelectFileList, translate);
+             // un loading pour le temps de chargement
+             this.listenTo(this.collection, Events.BackboneEvents.REQUEST, this.onRequest);
+             this.listenTo(this.collection, Events.BackboneEvents.SYNC, this.onSync);
         },
+        onRequest: function (e,r) {
+            if (e.models) {
+                this.setLoading(true);
+            }
+        },
+        onSync: function () {
+            $("span.numFiles").text(this.collection.length);
+            this.setLoading(false);
+        },
         onSelect: function(e) {
             var $target = $(e.currentTarget), id = $target.data('id');
             if (!this.options.allowMultipleSelect) {
@@ -43,11 +55,46 @@
             $target.toggleClass('selected');
             this.trigger(Events.ListViewEvents.SELECT,this.selected, this.selected?1:0,this.collection);
         },
+        /*
+        * reload l'API avec la nouvelle Parametre 
+        */
+        resetFilter: function(){
+            this.collection.setType('all');
+            this.collection.setOffset(0);
+            this.collection.fetch({remove: true});
+        },
         onlyImages: function() {
-            return this.filter(ReadOnlyFileListView.FILTER_IMAGES_ONLY);
+            //return this.filter(ReadOnlyFileListView.FILTER_IMAGES_ONLY);
+            this.collection.setType('image');
+            this.collection.setOffset(0);
+            this.collection.fetch({remove: true});
         },
+        sortBy: function(property, asc) {
+            var value = asc? 'ASC' :'DESC';
+            if (property!='') {
+                var order = property+"|"+ value;
+                this.collection.setOrderBy(order);
+            }
+            this.collection.setOffset(0);
+            this.collection.fetch({remove: true});
+            this._super(property, asc);
+            this._rebuildList();
+            if (!property && !asc) {
+                this.render();
+            }
+            else {
+                this.sortedBy = (property==="createdAt")?"id":property;
+                this.sortAsc = asc;
+                this.currentList = this.sortList(this.currentList);
+            }
+            this.render();
+
+        },
         noImage: function() {
-            return this.filter(ReadOnlyFileListView.FILTER_NO_IMAGES);
+           // return this.filter(ReadOnlyFileListView.FILTER_NO_IMAGES);
+           this.collection.setType('file');
+           this.collection.setOffset(0);
+           this.collection.fetch({remove: true});
         },
         addTemplateParams: function(collection, currentList) {
             return {lang: this.app.params.defaultcontentlang};
Index: src/js/JEditor/Commons/Files/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/Commons/Files/nls/fr-ca/i18n.js	(révision 10818)
+++ src/js/JEditor/Commons/Files/nls/fr-ca/i18n.js	(révision 10819)
@@ -1 +1 @@
-define({"allFiles":"Tous les fichiers","Photos":"Photos","Files":"Fichiers","sortBy":"Classer par","fileSort":"Pas de classement","fileSortcreatedAtAsc":"Date d'ajout croissante","fileSortcreatedAtDesc":"Date d'ajout décroissante","fileSortnameAsc":"Fichiers de A à Z","fileSortnameDesc":"Fichiers de Z à A","deleteAction":"Suppression","cancel":"Annuler","File(s)":"Fichier(s)","selectFile":"Parcourir la base de mon site","choose":"Choisir","defaultUploaderMessage":"Cliquez ici ou glissez-déposez pour remplacer l'image","defaultUploaderMessageImage":"Cliquez ici ou glissez-déposez pour remplacer l'image","defaultUploaderMessageFile":"Cliquez ici ou glissez-déposez pour remplacer le fichier","browseImageOnComputer":"Parcourir les dossiers <br>sur mon ordinateur","imagesFromStock":"Parcourir la base d'images<br> de mon site","importFailBadType":"Impossible d'importer le fichier %name% car il représente un danger potentiel","progressLoadingFiles":"Veuillez patienter pendant le chargement des fichiers","uploadFailTooBig":"Le fichier %name% est trop volumineux pour être importé","uploadFailErrorsOccured":"Des erreurs ont eu lieu pendant le transfert de fichier","uploadSuccess":"Le transfert des fichiers s'est déroulé avec succès","uploadFailServerError":"Impossible de transférer le fichier %name% à cause de l'erreur: \"%error%\""});
\ No newline at end of file
+define({"allFiles":"Tous les fichiers","Photos":"Photos","Files":"Fichiers","sortBy":"Classer par","fileSort":"Pas de classement","fileSortcreatedAtAsc":"Date d'ajout croissante","fileSortcreatedAtDesc":"Date d'ajout décroissante","fileSortnameAsc":"Fichiers de A à Z","fileSortnameDesc":"Fichiers de Z à A","deleteAction":"Suppression","cancel":"Annuler","File(s)":"Fichier(s) sur","selectFile":"Parcourir la base de mon site","choose":"Choisir","defaultUploaderMessage":"Cliquez ici ou glissez-déposez pour remplacer l'image","defaultUploaderMessageImage":"Cliquez ici ou glissez-déposez pour remplacer l'image","defaultUploaderMessageFile":"Cliquez ici ou glissez-déposez pour remplacer le fichier","browseImageOnComputer":"Parcourir les dossiers <br>sur mon ordinateur","imagesFromStock":"Parcourir la base d'images<br> de mon site","importFailBadType":"Impossible d'importer le fichier %name% car il représente un danger potentiel","progressLoadingFiles":"Veuillez patienter pendant le chargement des fichiers","uploadFailTooBig":"Le fichier %name% est trop volumineux pour être importé","uploadFailErrorsOccured":"Des erreurs ont eu lieu pendant le transfert de fichier","uploadSuccess":"Le transfert des fichiers s'est déroulé avec succès","uploadFailServerError":"Impossible de transférer le fichier %name% à cause de l'erreur: \"%error%\"","LoadMoreImage":"Charger plus de fichiers ..."});
\ No newline at end of file
Index: src/js/JEditor/Commons/Files/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/Commons/Files/nls/fr-fr/i18n.js	(révision 10818)
+++ src/js/JEditor/Commons/Files/nls/fr-fr/i18n.js	(révision 10819)
@@ -1 +1,3 @@
-define({"allFiles":"Tous les fichiers","Photos":"Photos","Files":"Fichiers","sortBy":"Classer par","fileSort":"Pas de classement","fileSortcreatedAtAsc":"Date d'ajout croissante","fileSortcreatedAtDesc":"Date d'ajout décroissante","fileSortnameAsc":"Fichiers de A à Z","fileSortnameDesc":"Fichiers de Z à A","deleteAction":"Suppression","cancel":"Annuler","File(s)":"Fichier(s)","selectFile":"Parcourir la base de mon site","choose":"Choisir","defaultUploaderMessage":"Cliquez ici ou glissez-déposez pour remplacer l'image","defaultUploaderMessageImage":"Cliquez ici ou glissez-déposez pour remplacer l'image","defaultUploaderMessageFile":"Cliquez ici ou glissez-déposez pour remplacer le fichier","browseImageOnComputer":"Parcourir les dossiers <br>sur mon ordinateur","imagesFromStock":"Parcourir la base d'images<br> de mon site","importFailBadType":"Impossible d'importer le fichier %name% car il représente un danger potentiel","progressLoadingFiles":"Veuillez patienter pendant le chargement des fichiers","uploadFailTooBig":"Le fichier %name% est trop volumineux pour être importé","uploadFailErrorsOccured":"Des erreurs ont eu lieu pendant le transfert de fichier","uploadSuccess":"Le transfert des fichiers s'est déroulé avec succès","uploadFailServerError":"Impossible de transférer le fichier %name% à cause de l'erreur: \"%error%\""});
\ No newline at end of file
+define({"allFiles":"Tous les fichiers","Photos":"Photos","Files":"Fichiers","sortBy":"Classer par","fileSort":"Pas de classement","fileSortcreatedAtAsc":"Date d'ajout croissante","fileSortcreatedAtDesc":"Date d'ajout décroissante","fileSortnameAsc":"Fichiers de A à Z","fileSortnameDesc":"Fichiers de Z à A","deleteAction":"Suppression","cancel":"Annuler","File(s)":"Fichier(s) sur","selectFile":"Parcourir la base de mon site","choose":"Choisir","defaultUploaderMessage":"Cliquez ici ou glissez-déposez pour remplacer l'image","defaultUploaderMessageImage":"Cliquez ici ou glissez-déposez pour remplacer l'image","defaultUploaderMessageFile":"Cliquez ici ou glissez-déposez pour remplacer le fichier","browseImageOnComputer":"Parcourir les dossiers <br>sur mon ordinateur","imagesFromStock":"Parcourir la base d'images<br> de mon site","importFailBadType":"Impossible d'importer le fichier %name% car il représente un danger potentiel","progressLoadingFiles":"Veuillez patienter pendant le chargement des fichiers","uploadFailTooBig":"Le fichier %name% est trop volumineux pour être importé","uploadFailErrorsOccured":"Des erreurs ont eu lieu pendant le transfert de fichier","uploadSuccess":"Le transfert des fichiers s'est déroulé avec succès",
+"uploadFailServerError":"Impossible de transférer le fichier %name% à cause de l'erreur: \"%error%\"",
+"LoadMoreImage":"Charger plus de fichiers ..."});
\ No newline at end of file
Index: src/js/JEditor/Commons/Files/nls/i18n.js
===================================================================
--- src/js/JEditor/Commons/Files/nls/i18n.js	(révision 10818)
+++ src/js/JEditor/Commons/Files/nls/i18n.js	(révision 10819)
@@ -1 +1,3 @@
-define({ "root": {"allFiles":"All Files","Photos":"Photos","Files":"Files","sortBy":"Sort by","fileSort":"Not sorted","fileSortcreatedAtAsc":"Sort by date asc","fileSortcreatedAtDesc":"Sort by date desc","fileSortnameAsc":"Sort by name asc","fileSortnameDesc":"Sort by name desc","deleteAction":"Delete","cancel":"Cancel","File(s)":"File(s)","selectFile":"Select File","choose":"Choose","defaultUploaderMessage":"Click here or drag-and-drop to change the picture","defaultUploaderMessageImage":"Click here or drag-and-drop to change the picture","defaultUploaderMessageFile":"Click here or drag-and-drop to change the file","browseImageOnComputer":"Browse directories <br> on my comptuer","imagesFromStock":"Browse pictures <br> on my site","importFailBadType":"Impossible to import file%name % due to a potential harm","progressLoadingFiles":"Please wait during file loading","uploadFailTooBig":"The file%name % is too big to be imported","uploadFailErrorsOccured":"There were errors during file transfer","uploadSuccess":"The file transfer was successful","uploadFailServerError":"Impossible to transfer file %name% because \"%error%\""}, "fr-fr":true, "fr-ca":true })
\ No newline at end of file
+define({ "root": {"allFiles":"All Files","Photos":"Photos","Files":"Files","sortBy":"Sort by","fileSort":"Not sorted","fileSortcreatedAtAsc":"Sort by date asc","fileSortcreatedAtDesc":"Sort by date desc","fileSortnameAsc":"Sort by name asc","fileSortnameDesc":"Sort by name desc","deleteAction":"Delete","cancel":"Cancel","File(s)":"File(s) on","selectFile":"Select File","choose":"Choose","defaultUploaderMessage":"Click here or drag-and-drop to change the picture","defaultUploaderMessageImage":"Click here or drag-and-drop to change the picture","defaultUploaderMessageFile":"Click here or drag-and-drop to change the file","browseImageOnComputer":"Browse directories <br> on my comptuer","imagesFromStock":"Browse pictures <br> on my site","importFailBadType":"Impossible to import file%name % due to a potential harm","progressLoadingFiles":"Please wait during file loading","uploadFailTooBig":"The file%name % is too big to be imported","uploadFailErrorsOccured":"There were errors during file transfer","uploadSuccess":"The file transfer was successful",
+"uploadFailServerError":"Impossible to transfer file %name% because \"%error%\"",
+"LoadMoreImage":"Load more files ..."}, "fr-fr":true, "fr-ca":true })
\ No newline at end of file
Index: src/js/JEditor/FilePanel/FilePanel.js
===================================================================
--- src/js/JEditor/FilePanel/FilePanel.js	(révision 10818)
+++ src/js/JEditor/FilePanel/FilePanel.js	(révision 10819)
@@ -21,9 +21,11 @@
     "text!./Templates/filePanelTpl.html",
     "JEditor/Commons/Events",
     "JEditor/Commons/Ancestors/Views/PanelView",
-    "collection!JEditor/Commons/Files/Models/FileDBCollection",
+    //"collection!JEditor/Commons/Files/Models/FileDBCollection",
+    "JEditor/FilePanel/Models/FileCollection",
     "collection!JEditor/Commons/Files/Models/FileGroupCollection",
     "JEditor/FilePanel/Views/FileListView",
+    "JEditor/FilePanel/Models/FileCollection",
     "JEditor/FilePanel/Views/FileManagerUIView",
     "JEditor/FilePanel/Views/CollectionListView",
     "JEditor/FilePanel/Views/CollectionManagerUIView",
@@ -38,9 +40,10 @@
         filePanelTpl,
         Events,
         PanelView,
-        FileDBCollection,
+        FileCollection,
         FileGroupCollection,
         FileListView,
+        FileCollection,
         FileManagerUIView,
         CollectionListView,
         CollectionManagerUIView,
@@ -78,7 +81,8 @@
                             this._super();
                             this.childViews = {};
                             this._template = this.buildTemplate(filePanelTpl, translate);
-                            
+                            this.isGoback=false;
+                            this.fileList = new FileCollection();
                         },
                         /**
                          * @fires Events.LoadEvents.LOAD_SUCCESS
@@ -87,11 +91,13 @@
                          * @return undefined;
                          */
                         load: function () {
-                            this.fileList = FileDBCollection.getInstance();
-                            this.fileGroupList = FileGroupCollection.getInstance();
-                            this.loadingEnd();
+                           // this.fileList = FileDBCollection.getInstance();
+                           this.fileGroupList = FileGroupCollection.getInstance();
+                           this.fileList.resetFilter();
+                           this.fileList.fetch({
+                              success: this.loadingEnd()
+                            });
                             
-                            
                         },
                         /**
                          * déclenché lors de la fin du chargement du panneau
@@ -121,7 +127,7 @@
                             this.listenTo(this.childViews.collectionManagerUIView, Events.ListViewEvents.DETAIL_COLLECTION, this.goToCollection);
                             this.listenTo(this.childViews.collectionManagerUIView, Events.ListViewEvents.COLLECTION_ADDED, this.collectionAdded);
                             
-                            this.listenTo(this.fileList, Events.BackboneEvents.ADD, this._createFilesDetailsView);
+                            //this.listenTo(this.fileList, Events.BackboneEvents.ADD, this._createFilesDetailsView);
                             this.listenTo(this.fileGroupList, Events.BackboneEvents.ADD, this._createOneCollectionDetailsView);
 
                          
@@ -150,9 +156,11 @@
                             this._hideAllViews();
                             this.childViews.fileManagerUIView.render();
                             this.childViews.fileManagerUIView.show();
+                            this.childViews.fileManagerUIView.updateCount();
                             this.childViews.fileListView.selectNone();
                             this.childViews.fileListView.render();
                             this.childViews.fileListView.show();
+                            this.childViews.fileListView.loadMoreView();
 //                            this.display = 'files';
                             this._setSortDropdown();
                             this._setFilterActive();
@@ -204,8 +212,9 @@
                          * - rend la vue détaillée de collection souhaitée
                          */
                         goToCollection: function (cid) {
-                            //console.dir(cid);
-                            //console.dir(this.fileGroupList);
+                            if(!this.isGoback){
+                                this._createOneCollectionDetailsView(this.fileGroupList.get(cid));
+                            }
                             this._hideAllViews();
                             this.childViews.collectionManagerUIView.listView = this._byCID[cid];
                             this.childViews.collectionManagerUIView.parentCollection = this.fileGroupList.get(cid);
@@ -214,6 +223,7 @@
                             this._byCID[cid].render();
                             this._byCID[cid].show();
                             this.app.router.navigate('files/collections/' + this.fileGroupList.get(cid).id, {trigger: false});
+                            this.isGoback=false;
                         },
                         /**
                          * méthode de chargement de la vue détaillée d'une collection à partir du routeur et donc par l'ID de la collection
@@ -271,15 +281,13 @@
                         goToFile: function (cid, referrer) {
                             this._hideAllViews();
                             //console.log('show ' + cid);
-                            this.childViews.fileDetailManagerView.referrer = referrer;
-                            this.childViews.fileDetailManagerView.FileDetailView = this._byCID[cid];
-                            this.childViews.fileDetailManagerView.render();
-                            this.childViews.fileDetailManagerView.show();
                             /**
                              * récupération du fichier précédent et du fichier suivant par rapport au cid donnée
                              */
                             var orderedList = [];
                             if (referrer) { // l'image est issue d'une collection
+                                if(this.fileListDetail)
+                                    this.fileListDetail=null;
                                 orderedList = this._orderedFilesInCollectionsCID[referrer];
                                 var collection = this.fileGroupList.get(referrer);
                                 if (collection && cid) {
@@ -291,15 +299,53 @@
                                         }
                                     }
                                 }
+                                this.renderFileDetail_DetailManager(cid,referrer,orderedList);
                             } else {
                                 orderedList = this._orderedFilesCID;
+                                var self=this;
                                 var file = this.fileList.get(cid);
-                                if (file)
-                                    this.app.router.navigate('files/files/' + file.id, {trigger: false});
+                                var id= (file)?file.id:cid;
+                                this.fileListDetail = new FileCollection();
+                                this.fileListDetail.setId(id);
+                                this.fileListDetail.fetch({
+                                    success :function(){
+                                        var file2= self.fileListDetail._byId[id];
+                                        self._createFilesDetailsView(file2);
+                                        self.renderFileDetail_DetailManager(file2.cid,referrer,orderedList);
+                                    }
+                                });
+                                this.app.router.navigate('files/files/' + id, {trigger: false ,replace:true});    
                             }
-                            var length = orderedList.length;
-                            this._byCID[cid].previous = orderedList[(orderedList.lastIndexOf(cid) + length - 1) % length];
-                            this._byCID[cid].next = orderedList[(orderedList.lastIndexOf(cid) + 1) % length];
+                        },
+                        renderFileDetail_DetailManager :function(cid,referrer,orderedList){
+                            this.childViews.fileDetailManagerView.referrer = referrer;
+                            this.childViews.fileDetailManagerView.FileDetailView = this._byCID[cid];
+                            if(this.fileListDetail && !referrer)
+                                this.childViews.fileDetailManagerView.setMyFile(this.fileListDetail._byId[cid]);
+                            this.childViews.fileDetailManagerView.render();
+                            this.childViews.fileDetailManagerView.show();
+
+                            if(referrer){
+                                var length = orderedList.length;
+                                this._byCID[cid].previous = orderedList[(orderedList.lastIndexOf(cid) + length - 1) % length];
+                                this._byCID[cid].next = orderedList[(orderedList.lastIndexOf(cid) + 1) % length];
+                            }else{
+                                var keyArray=Object.keys(this.fileListDetail._byId);
+                                var idFile=(this.fileListDetail._byId[cid].id).toString();
+                                var nextIndex = keyArray.indexOf(idFile) +1;
+                                var prevIndex = keyArray.indexOf(idFile) -1;
+                                var previousId=keyArray[prevIndex];
+                                var nextId=keyArray[nextIndex];
+                                if(!previousId)
+                                    previousId=false;
+                                if(_.contains(nextId,"c"))
+                                    nextId=false;
+                             
+                                this._byCID[cid].previous=previousId;
+                                this._byCID[cid].next=nextId;
+                            }
+                            
+                            this._byCID[cid]
                             //console.dir(cid)
                             //console.dir(referrer);
                             //console.dir(orderedList);
@@ -325,7 +371,7 @@
                                 this.childViews.fileManagerUIView.render();
                                 this.childViews.fileListView.render();
                                 var file = this.fileList.get(fileID);
-                                this.goToFile(file.cid);
+                                this.goToFile(fileID);
                             }
                         },
                         onEdit: function (newFile, referrer, oldFile) {
@@ -380,8 +426,10 @@
                          * gestion des boutons retour depuis une fiche détaillée
                          */
                         goBack: function (referrer) {
-                            if (referrer)
-                                this.goToCollection(referrer); // on provient d'une collection
+                            if (referrer){  
+                                this.isGoback=true;
+                                this.goToCollection(referrer);    // on provient d'une collection
+                            }
                             else
                                 this.switchToFiles(); // on provient de la liste de fichiers
                         },
@@ -455,22 +503,9 @@
                         _initDetailsView: function () {
                             //console.log('recreating details view');
                             this._byCID = {};
-                            /**
-                             * création des différentes vues détaillées pour chaque collection
-                             */
-                            this.fileGroupList.each(function (model, index, collection) {
-                                this._createOneCollectionDetailsView(model);
-                            }, this);
-                            /**
-                             * création des différentes vues détaillées de chaque fichier hors collection
-                             */
-                            this.fileList.each(function (model, index, collection) {
-                                if(!model.isLogo)this._createFilesDetailsView(model);
-                            }, this);
-
                         },
                         _createFilesDetailsView: function (model) {
-                            //console.log('_createFilesDetailsView');
+                            this.$(".fileDetail").remove();
                             this._byCID[model.cid] = new FileDetailView({model: model, fileList: this.fileList, languages: this.languages});
                             this.listenTo(this._byCID[model.cid], Events.FileDetailManagerViewEvents.NAV_PREVNEXT, this.goToFile);
                             this._byCID[model.cid].hide(false);
@@ -515,28 +550,30 @@
                             }, this);
                         },
                         _deleteOneDetailView: function (model, collection, deleteCollection) {
-                            if (this._byCID[model.cid]) {
-                                this._byCID[model.cid].hide(false);
-                                delete this._byCID[model.cid];
-                                if (collection) {
-                                    var indexToDelete = this._orderedFilesInCollectionsCID[collection.cid] ? this._orderedFilesInCollectionsCID[collection.cid].lastIndexOf(model.cid) : -1;
-                                    //console.log('delete detail view ' + indexToDelete);
-                                    if (indexToDelete >= 0)
-                                        this._orderedFilesInCollectionsCID[collection.cid].splice(indexToDelete, 1);
-                                    if (deleteCollection)
-                                        this._deleteOrderedCollection(collection);
-                                } else {
-                                    var indexToDelete = this._orderedFilesCID.lastIndexOf(model.cid);
-                                    //console.log('delete detail view ' + indexToDelete);
-                                    if (indexToDelete >= 0)
-                                        this._orderedFilesCID.splice(indexToDelete, 1);
-                                }
-                            }
+                            //this.fileList.fetch();
+                            this.childViews.fileManagerUIView.trigger("UPDATECOUNT");
+                            // if (this._byCID[model.cid]) {
+                                
+                            //     this._byCID[model.cid].hide(false);
+                            //     delete this._byCID[model.cid];
+                            //     if (collection) {
+                            //         var indexToDelete = this._orderedFilesInCollectionsCID[collection.cid] ? this._orderedFilesInCollectionsCID[collection.cid].lastIndexOf(model.cid) : -1;
+                            //         //console.log('delete detail view ' + indexToDelete);
+                            //         if (indexToDelete >= 0)
+                            //             this._orderedFilesInCollectionsCID[collection.cid].splice(indexToDelete, 1);
+                            //         if (deleteCollection)
+                            //             this._deleteOrderedCollection(collection);
+                            //     } else {
+                            //         var indexToDelete = this._orderedFilesCID.lastIndexOf(model.cid);
+                            //         //console.log('delete detail view ' + indexToDelete);
+                            //         if (indexToDelete >= 0)
+                            //             this._orderedFilesCID.splice(indexToDelete, 1);
+                            //     }
+                            // }
                         },
                         _deleteOrderedCollection: function (collection) {
                             delete this._orderedFilesInCollectionsCID[collection.cid];
                         },
-                       
                         /**
                          * ajoute les variables à l'objet this.dom
                          */
Index: src/js/JEditor/FilePanel/Models/File.js
===================================================================
--- src/js/JEditor/FilePanel/Models/File.js	(nonexistent)
+++ src/js/JEditor/FilePanel/Models/File.js	(révision 10819)
@@ -0,0 +1,110 @@
+define([
+    "underscore",
+    "JEditor/Commons/Files/Models/File",
+    "JEditor/Commons/Ancestors/Models/Model"
+], function(_,
+        File,
+        Model
+        ) {
+    var /**
+     * Un fichier chargé par l'utilisateur
+     * @class File
+     * @property {string} id l'id du fichier
+     * @property {string} title le titre du fichier (optionnel)
+     * @property {string} desc description du fichier (optionel)
+     * @property {string} fileUrl fileUrl du fichier
+     * @property {string} thumb fileUrl d'une miniature si c'est une image, fileUrl d'une image reprÃ©sentant un fichier (icÃ´ne)
+     * @property {string} mimeType  type de fichier 
+     * @property {string} ext extension de fichier sans le point
+     * @property {string} name nom du fichier
+     * @property {string} originalName nom original du fichier
+     * @property {string} originalFile id du fichier original (sur les images qui ont Ã©tÃ© modifiÃ©es dans le back office)
+     * @property {Boolean} isRemovable si on peut supprimer l'image
+     */
+            File = Model.extend(
+                    /**
+                     * @lends File.prototype
+                     */
+                            {
+                                url: function() {
+                                    return __IDEO_API_PATH__ + "/resources" + (this.id ? '/' + this.id : "");
+                                },
+                                defaults: {fileUrl: "#", thumb: ""},
+                                initialize: function() {
+                                    if (!this.title)
+                                        this.title = {};
+                                    if (!this.desc)
+                                        this.desc = {};
+                                    if (!this.link)
+                                        this.link = {};
+                                },
+                                /**
+                                 * 
+                                 * @returns {Boolean}
+                                 */
+                                isImg: function() {
+                                    var regexpImg = /^image/;
+                                    if (regexpImg.test(this.mimeType))
+                                        return true;
+                                    return false;
+                                },
+                                 /**
+                                 * 
+                                 * @returns {Boolean}
+                                 */
+                                isVideo: function() {
+                                    var regexpImg = /^video/;
+                                    if (regexpImg.test(this.mimeType))
+                                        return true;
+                                    return false;
+                                },
+                                isSvg: function() {
+                                    var regexpImg = /^svg/;
+                                    if(regexpImg.test(this.ext))
+                                        return true;
+                                    return false;
+                                },
+                                /**
+                                 * 
+                                 * @returns {Boolean}
+                                 */
+                                isFavicon: function() {
+                                    if(!this.desc.flag)
+                                    {
+                                        return false;
+                                    }
+                                    else
+                                    {
+                                        return true;
+                                    }
+                                },
+                                /**
+                                 * retourne la classe de la preview
+                                 * @returns {String}
+                                 */
+                                previewClass: function() {
+                                    if (this.isImg())
+                                        return '';
+                                    var knownExt = ['pdf', 'doc', 'ppt', 'xls'];
+                                    for (var i = 0; i < knownExt.length; i++) {
+                                        if (this.ext == knownExt[i])
+                                            return this.ext;
+                                    }
+                                    return 'unknown';
+                                },
+                                // todo : supprimer après fix du serializer
+                                parse: function(data) {
+                                    if (data.title && _.isArray(data.title))
+                                        data.title = {};
+                                    if (data.desc && _.isArray(data.desc))
+                                        data.desc = {};
+                                    if (data.link && _.isArray(data.link))
+                                        data.link = {};
+                                    return data;
+                                },
+                            }
+                    );
+                    File.SetAttributes(['title', 'desc', 'fileUrl', 'thumb', 'mimeType', 'ext', 'name', 'originalName', 'originalFile', 'useCount','isLogo','link']);
+
+                    return File;
+                });
\ No newline at end of file
Index: src/js/JEditor/FilePanel/Models/FileCollection.js
===================================================================
--- src/js/JEditor/FilePanel/Models/FileCollection.js	(nonexistent)
+++ src/js/JEditor/FilePanel/Models/FileCollection.js	(révision 10819)
@@ -0,0 +1,94 @@
+define(["underscore", "JEditor/Commons/Ancestors/Models/Collection", "JEditor/Commons/Events","JEditor/FilePanel/Models/File"], function (_, Collection, Events,File) {
+    var FileCollection = Collection.extend({
+        options: {
+            orderBy: null ,
+            type: null,
+            rowcount: 100,
+            offset: 0,
+            id :null
+        },
+        hasmore:true,
+        countFile : 0,
+        model:File,
+        initialize: function () {
+            Collection.prototype.initialize.apply(this, arguments);
+            this.setOffset(0);
+            this.on(Events.BackboneEvents.SYNC, this.onSync);
+        },
+        onSync: function (coll,resp) {
+            this.options.offset = this.length;
+            if(resp.length === this.options.rowcount)
+                this.hasmore=true;
+            else
+                this.hasmore=false;
+                
+            this.countFile=0;
+            if(resp[0]){
+                this.countFile = resp[0].countFile;
+            }
+            if(typeof resp === 'object' && resp !== null && !Array.isArray(resp)){
+                if(resp.countFile){
+                    this.countFile =resp.countFile;
+                }
+            }
+        },
+        url: function () {
+            var url = __IDEO_API_PATH__ + "/resources/";
+            var args = _.pick(this.options, function (value) {
+                return value !== null;
+            });
+            if(args.id){
+                url +=this.getId();
+                this.setId(null);
+            }else{
+             
+                var i=0;
+                url+=_.reduce(args,function(memo,value,index){
+                        var ret= memo+((i>0?"&":"?")+encodeURIComponent(index)+"="+encodeURIComponent(value));
+                        i++;
+                    return ret;
+                },"");
+            }
+           
+            return url;
+        },
+        resetFilter:function(){
+            this.setType("all");
+            this.setOrderBy("createdAt");
+            this.setOffset(0);
+        },
+        compileArguments: function () {
+            var args = {};
+            for (var option in this.options) {
+                if (this.options[option] !== null)
+                    args[option] = this.options[option];
+            }
+            return args;
+        },
+        setOrderBy: function (orderBy) {
+            this.options.orderBy = orderBy;
+        },
+        getOrderBy:function(){
+            return this.options.orderBy;
+        },
+        setType: function (type){
+            this.options.type = type
+        },
+        getType:function(){
+            return this.options.type;
+        },
+        setOffset: function (offset) {
+            this.options.offset = offset;
+        },
+        getOffset:function(){
+            return this.options.offset;
+        },
+        getId:function(){
+            return this.options.id;
+        },
+        setId:function(id){
+            this.options.id=id;
+        }
+    });
+    return FileCollection;
+});
Index: src/js/JEditor/FilePanel/Templates/fileDetail.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileDetail.html	(révision 10818)
+++ src/js/JEditor/FilePanel/Templates/fileDetail.html	(révision 10819)
@@ -1,10 +1,11 @@
 <div class="content-fiche">
 
     <!-- PRECEDENT -->
+    <%if(previous){%>
     <span class="image-prev">
         <span class="icon-prev" data-cid="<%=previous%>"></span>
     </span>
-
+    <%}%>
     <div class="content-fiche-image">
 
         <div class="view-image">
@@ -56,17 +57,6 @@
                             </p>
                         </div>
                     </span>
-                    <% if(isRemovable===false || isInBlock ===true) {%>
-                        <span class="actions informations">
-                            <span class="icon-information"></span>
-                            <div class="infobulles">
-                                <p class="title"><%= __("locked")%> :</p>
-                                <p>
-                                    <%= __("lockimage")%>
-                                </p>
-                            </div>
-                        </span>
-                    <% } %>
                     <%if(isFavicon){%>
                         <span class="actions informations">
                             <span class="icon-information"></span>
@@ -148,8 +138,10 @@
     </div>
 
     <!-- SUIVANT -->
+    <%if(next){%>
     <span class="image-next">
         <span class="icon-next" data-cid="<%=next%>"></span>
     </span>
+    <%}%>
 
 </div><!-- end #content-fiche-image -->
\ No newline at end of file
Index: src/js/JEditor/FilePanel/Templates/fileList.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileList.html	(révision 10818)
+++ src/js/JEditor/FilePanel/Templates/fileList.html	(révision 10819)
@@ -1,7 +1,25 @@
+<div class="FileTop ">
 <div class="my-files fileList">
     <div class="content scroll-container">
         <div class="group-content <%=content.length===0?'empty':''%>">
+            <div class=" add-file" data-action="showuploader">
+    
+                <span class="icon">
+                    <span class="icon-hexagon"></span>
+                    <span class="icon-add"></span>
+                </span>
+                <%= __("addFile")%>
 
+            </div>
+        </div>
+           
+    </div>
+</div>
+
+<div class="warning-msg"><span class="icon-warning" style="color: #d42525;"></span> <span style="color: #d42525;"><%= __("imageWarning")%></span> <%= __("imageWarningMsg")%> <a href="https://www.tineye.com/" target="_blank">TinEye</a>.</div>
+</div>
+<div class="my-files fileList">
+    <div class="content scroll-container">
             <% for(var i=0; i< content.length; i++){
             var file = content[i];
             %>
@@ -28,19 +46,9 @@
             </div>
             <% } %>
             <% } %>
-            <div class="menu-wrapper add-file" data-action="showuploader">
-
-                <span class="icon">
-                    <span class="icon-hexagon"></span>
-                    <span class="icon-add"></span>
-                </span>
-                <%= __("addFile")%>
-
-            </div>
             <!-- file add -->
 
-        </div>
     </div>
 </div>
-
-<div class="warning-msg"><span class="icon-warning" style="color: #d42525;"></span> <span style="color: #d42525;"><%= __("imageWarning")%></span> <%= __("imageWarningMsg")%> <a href="https://www.tineye.com/" target="_blank">TinEye</a>.</div>
\ No newline at end of file
+</div>
+<div class="flex-load-more"><div class="load-more"><span class="label"> Afficher plus d'images ...</span></div></div>
\ No newline at end of file
Index: src/js/JEditor/FilePanel/Views/FileDetailManagerView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileDetailManagerView.js	(révision 10818)
+++ src/js/JEditor/FilePanel/Views/FileDetailManagerView.js	(révision 10819)
@@ -31,6 +31,7 @@
         'click a[data-replace]': 'showUploader',
         'click a[data-action="focus"]': 'editFocusPoint',
     },
+    MyFile:null,
     initialize: function() {
         this._super();
         this._template = this.buildTemplate(fileDetailManager,translate);
@@ -50,8 +51,11 @@
         this.$el.html(this._template(params));
         if (this.referrer)
             var file = this.FileDetailView.model;
-        else
+        else{
             var file = this.options.childViews.fileList.collection.get(this.FileDetailView.model.id);
+            if(!file && this.MyFile)
+                file = this.MyFile;
+        }
         this.ImageEditView = new ImageEditView({model: file});
         this.ImageEditView.attach(this.$('[data-action="crop"]'));
         this.listenToOnce(this.ImageEditView, Events.ImageEditorEvents.SAVE, this._onImageEdited);
@@ -156,7 +160,9 @@
         }
         return false;
     },
-
+    setMyFile :function(muFile){
+        this.MyFile=muFile;
+    }
    
 });
 Events.extend({
Index: src/js/JEditor/FilePanel/Views/FileListView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileListView.js	(révision 10818)
+++ src/js/JEditor/FilePanel/Views/FileListView.js	(révision 10819)
@@ -26,6 +26,8 @@
             'uploaderstart .group-content': 'uploaderstart',
             'uploaderfail .group-content': 'uploaderfail',
             'click [data-action="showuploader"]': 'showUploader',
+            'click .flex-load-more .load-more' : 'loadMore',
+            'click .flex-load-more .load-more' : 'loadMore',
         },
         initialize: function() {
             this._super();
@@ -33,8 +35,46 @@
             this._groupTemplate = this.buildTemplate(groupFileList, translate);
             this._emptyGroupTemplate = this.buildTemplate(emptyGroupFileList, translate);
             this._emptyTemplate = this.buildTemplate(emptyFileList, translate);
+            this.listenTo(this.collection, Events.BackboneEvents.REQUEST, this.onRequest);
+            this.listenTo(this.collection, Events.BackboneEvents.SYNC, this.onSync);
         },
+        onRequest: function (e,r) {
+            if (e.models) {
+                this.setLoading(true);
+            }
+        },
+        onSync: function () {
+            this.setLoading(false);
+            this.loadMoreView();
+           
+        },
+        loadMoreView :function (){
+            if(this.collection.hasmore){
+                this.$(".flex-load-more").show();
+            }
+            else this.$(".flex-load-more").hide();
+            this.listenTo(this.collection, Events.BackboneEvents.REQUEST, this.onRequest);
+            this.listenTo(this.collection, Events.BackboneEvents.SYNC, this.onSync);
+        },
+        onRequest: function (e,r) {
+            if (e.models) {
+                this.setLoading(true);
+            }
+        },
+        onSync: function () {
+            this.setLoading(false);
+            this.loadMoreView();
+           
+        },
+        loadMoreView :function (){
+            if(this.collection.hasmore){
+                this.$(".flex-load-more").show();
+            }
+            else this.$(".flex-load-more").hide();
+        },
         uploaderstart:function(e){
+            $(e.currentTarget).children(".add-file").hide();
+            $(e.currentTarget).children(".add-file").hide();
             $(e.currentTarget).children(".myfiles-empty").remove();
         },
         uploaderfail: function(e, data){
@@ -75,6 +115,24 @@
 //
 //        },
         sortBy: function(property, asc) {
+            var value = asc? 'ASC' :'DESC';
+            this.collection.setOffset(0);
+            if (property!='') {
+                var order = property+"|"+ value;
+                this.collection.setOrderBy(order);
+            }
+            this.collection.fetch({
+                remove: true
+            });
+            var value = asc? 'ASC' :'DESC';
+            this.collection.setOffset(0);
+            if (property!='') {
+                var order = property+"|"+ value;
+                this.collection.setOrderBy(order);
+            }
+            this.collection.fetch({
+                remove: true
+            });
             this._super(property, asc);
             this._rebuildList();
             if (!property && !asc) {
@@ -81,7 +139,9 @@
                 this.render();
             }
             else {
+                this.sortedBy = (property==="createdAt")?"id":property;
                 this.sortedBy = property;
+                this.sortedBy = (property==="createdAt")?"id":property;
                 this.sortAsc = asc;
                 this.currentList = this.sortList(this.currentList);
             }
@@ -115,6 +175,8 @@
                             this.trigger(Events.ListViewEvents.FILE_CLEARVIEW, model);
                             this.trigger(Events.ListViewEvents.FILE_CHECKCOLLECTION_ONDELETE, model);
                             model.destroy();
+                            this.collection.setOffset(0);
+                            this.collection.fetch();
                             this.selectNone();
                         }, this),
                         options: {dialogClass: 'delete no-close', dontAskAgain: true, subject: 'deleteFileItem'}
@@ -123,6 +185,8 @@
                     this.trigger(Events.ListViewEvents.FILE_CLEARVIEW, model);
                     this.trigger(Events.ListViewEvents.FILE_CHECKCOLLECTION_ONDELETE, model);
                     model.destroy();
+                    this.collection.setOffset(0);
+                    this.collection.fetch();
                     this.selectNone();
                 }
             
@@ -147,10 +211,46 @@
             this.dom[this.cid].showUploader.trigger('click');
         },
         onlyImages: function() {
+        // return this.filter(FileListView.FILTER_IMAGES_ONLY);
+        this.collection.setType('image');
+        this.collection.setOffset(0);
+        this.collection.fetch({remove: true});
+        },
+        resetFilter :function(){
+            this.collection.setType(null);
+            this.collection.setOffset(0);
+            this.collection.fetch({remove: true});
             return this.filter(FileListView.FILTER_IMAGES_ONLY);
+        // return this.filter(FileListView.FILTER_IMAGES_ONLY);
+        this.collection.setType('image');
+        this.collection.setOffset(0);
+        this.collection.fetch({remove: true});
         },
+        resetFilter :function(){
+            this.collection.setType(null);
+            this.collection.setOffset(0);
+            this.collection.fetch({remove: true});
+        },
         noImage: function() {
+            //return this.filter(FileListView.FILTER_NO_IMAGES);
+            this.collection.setType('file');
+            this.collection.setOffset(0);
+            this.collection.fetch({remove: true});
+        },
+        loadMore: function(){
+            if(this.collection.hasmore){
+                this.collection.fetch({remove: false});
+            }
             return this.filter(FileListView.FILTER_NO_IMAGES);
+            //return this.filter(FileListView.FILTER_NO_IMAGES);
+            this.collection.setType('file');
+            this.collection.setOffset(0);
+            this.collection.fetch({remove: true});
+        },
+        loadMore: function(){
+            if(this.collection.hasmore){
+                this.collection.fetch({remove: false});
+            }
         }
     });
     var regexpImg = /^image/;
@@ -167,7 +267,9 @@
             UPLOADER_COMPLETE: 'uploaderComplete',
             FILE_CLEARVIEW: 'fileClearView',
             CHOOSE_IMG: 'selectImage',
+            UPDATE_COUNT_VIEW: "UPDATECOUNT",
             
+            UPDATE_COUNT_VIEW: "UPDATECOUNT",
         },
     })
     return FileListView;
Index: src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 10818)
+++ src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 10819)
@@ -124,4 +124,5 @@
     "introUrl" : "Ajouter une url utilisable dans une galerie (data-url)",
     "imageVideo": "C'est un fichier vidéo",
     "copy":"Copié",
+    "FileOn"    :   "fichier(s) sur"
 });
\ No newline at end of file
Index: src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 10818)
+++ src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 10819)
@@ -128,4 +128,5 @@
     "introUrl" : "Ajouter une url utilisable dans une galerie (data-url)",
     "imageVideo": "C'est un fichier vidéo",
     "copy":"Copié",
+    "FileOn"    :   "fichier(s) sur"
 });
\ No newline at end of file
Index: src/js/JEditor/FilePanel/nls/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/i18n.js	(révision 10818)
+++ src/js/JEditor/FilePanel/nls/i18n.js	(révision 10819)
@@ -130,6 +130,7 @@
         "introUrl" : "Add a url usable in a gallery (data-url)",
         "imageVideo": "C'est un fichier vidéo",
         "copy":"Copied",
+        "FileOn"    :   "file(s) on"
     },
     "fr-fr": true, "fr-ca":true
 })
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/ImageGroupView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/ImageGroupView.js	(révision 10818)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/ImageGroupView.js	(révision 10819)
@@ -5,7 +5,7 @@
     "JEditor/Commons/Events",
     "JEditor/Commons/Ancestors/Views/View",
     "JEditor/FilePanel/Views/ImageEditView",
-    "collection!JEditor/Commons/Files/Models/FileDBCollection",
+    "JEditor/FilePanel/Models/FileCollection",
     "JEditor/Commons/Files/Views/FileSelectorDialog",
     "JEditor/Commons/Utils",
     "i18n!../nls/i18n",
@@ -12,7 +12,7 @@
     //not in params
     "jqueryPlugins/dropdown",
     "jqueryPlugins/uploader"
-], function($, _, imageGroup, Events, View, ImageEditView, FileDBCollection, FileSelectorDialog, Utils, translate) {
+], function($, _, imageGroup, Events, View, ImageEditView, FileCollection, FileSelectorDialog, Utils, translate) {
     var ImageGroupView = View.extend({
         selected: null,
         events: {
@@ -26,6 +26,9 @@
         },
         initialize: function() {
             this._super();
+            //remplacer par la nouvelle Filecolletion 
+            this.fileCollection =  new FileCollection();
+            this.fileCollection.fetch();
             this._template = this.buildTemplate(imageGroup, translate);
             this.selected = {};
             this.model.files.each(function(file) {
@@ -33,7 +36,6 @@
             }, this);
             this.listenTo(this.model.files, Events.BackboneEvents.ADD, this.render);
             this.listenTo(this.model.files, Events.BackboneEvents.REMOVE, this.render);
-            this.fileCollection = FileDBCollection.getInstance();
             this.selectFileView = new FileSelectorDialog({collection: this.fileCollection, allowMultipleSelect: true});
             this.listenTo(this.selectFileView, Events.ListViewEvents.CHOOSE_FILE, this._useExistingFile);
         },
@@ -161,7 +163,7 @@
             var files = data.filesData;
             var toAdd = [];
             for (var i = 0; i < files.length; i++) {
-                var file = FileDBCollection.getInstance().create(files[i].response);
+                var file = this.fileCollection.create(files[i].response);
                 this.selected[file.id] = false;
                 toAdd.push(file);
             }
@@ -173,8 +175,13 @@
             }
             this.model.save();
         },
+        resetFilter: function(){
+            this.fileCollection.resetFilter();
+          //  this.fileCollection.fetch();
+        },
         openImageFileSelector: function() {
             var selectFileView, that = this;
+            this.resetFilter();
             selectFileView = new FileSelectorDialog({collection: this.fileCollection, allowMultipleSelect: true});
             this.listenTo(selectFileView, Events.ListViewEvents.CHOOSE_FILE, this._useExistingFile);
             this.listenTo(selectFileView, Events.DialogEvents.CLOSE, function() {
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalleryBlock/Views/ImageGroupView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalleryBlock/Views/ImageGroupView.js	(révision 10818)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalleryBlock/Views/ImageGroupView.js	(révision 10819)
@@ -5,7 +5,7 @@
     "JEditor/Commons/Events",
     "JEditor/Commons/Ancestors/Views/View",
     "JEditor/FilePanel/Views/ImageEditView",
-    "collection!JEditor/Commons/Files/Models/FileDBCollection",
+    "JEditor/FilePanel/Models/FileCollection",
     "JEditor/Commons/Files/Views/FileSelectorDialog",
     "JEditor/Commons/Utils",
     "i18n!../nls/i18n",
@@ -12,7 +12,7 @@
     //not in params
     "jqueryPlugins/dropdown",
     "jqueryPlugins/uploader"
-], function($, _, imageGroup, Events, View, ImageEditView, FileDBCollection, FileSelectorDialog, Utils, translate) {
+], function($, _, imageGroup, Events, View, ImageEditView, FileCollection, FileSelectorDialog, Utils, translate) {
     var ImageGroupView = View.extend({
         selected: null,
         events: {
@@ -26,6 +26,9 @@
         },
         initialize: function() {
             this._super();
+            // remplacer par la nouvelle Filecolletion 
+            this.fileCollection =  new FileCollection();
+            this.fileCollection.fetch();
             this._template = this.buildTemplate(imageGroup, translate);
             this.selected = {};
             this.model.files.each(function(file) {
@@ -33,7 +36,6 @@
             }, this);
             this.listenTo(this.model.files, Events.BackboneEvents.ADD, this.render);
             this.listenTo(this.model.files, Events.BackboneEvents.REMOVE, this.render);
-            this.fileCollection = FileDBCollection.getInstance();
             this.selectFileView = new FileSelectorDialog({collection: this.fileCollection, allowMultipleSelect: true});
             this.listenTo(this.selectFileView, Events.ListViewEvents.CHOOSE_FILE, this._useExistingFile);
         },
@@ -161,7 +163,7 @@
             var files = data.filesData;
             var toAdd = [];
             for (var i = 0; i < files.length; i++) {
-                var file = FileDBCollection.getInstance().create(files[i].response);
+                var file = this.fileCollection.create(files[i].response);
                 this.selected[file.id] = false;
                 toAdd.push(file);
             }
@@ -173,8 +175,13 @@
             }
             this.model.save();
         },
+        resetFilter: function(){
+            this.fileCollection.resetFilter();
+          //  this.fileCollection.fetch();
+        },
         openImageFileSelector: function() {
             var selectFileView, that = this;
+            this.resetFilter();
             selectFileView = new FileSelectorDialog({collection: this.fileCollection, allowMultipleSelect: true});
             this.listenTo(selectFileView, Events.ListViewEvents.CHOOSE_FILE, this._useExistingFile);
             this.listenTo(selectFileView, Events.DialogEvents.CLOSE, function() {
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageOptionsView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageOptionsView.js	(révision 10818)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageOptionsView.js	(révision 10819)
@@ -175,7 +175,7 @@
 		},
 		_useExistingFile : function(file) {
 			if (file) {
-				this.model.file = file;
+				this.model.file = file.attributes;
 				this.render();
 			}
 
Index: src/less/imports/panel_image_image.less
===================================================================
--- src/less/imports/panel_image_image.less	(révision 10818)
+++ src/less/imports/panel_image_image.less	(révision 10819)
@@ -402,9 +402,9 @@
             & .header {
                 margin-left:10px;
                 & .wrapper{
-                    padding-top: 7px;
-                    float:left;
-                    font-size:40px;
+                    padding-top: 14px;
+                    float: left;
+                    font-size: 27px;
                 }
             }
             & .title{
@@ -415,10 +415,13 @@
             }
             & .numFiles{
                 float:left;
-                margin-right:20px;
+                margin-right:10px;
                 margin-left:10px;
                 color:#cccccc;
             }
+            & .numberFiles{
+                font-size: 27px;
+            }
         }
 
         & .content {
Index: src/less/main.less
===================================================================
--- src/less/main.less	(révision 10818)
+++ src/less/main.less	(révision 10819)
@@ -2798,4 +2798,17 @@
 .icon-landing-page:before {
   content: "\e99f";
   color: #00b6c0;
+}
+/* LOAD MORE */
+.selectFile .flex-load-more {
+  position: relative;
+  float: left;
+  margin-bottom: 15px;
+  margin-left: 40%;
+}
+.selectFile .flex-load-more .load-more{
+  padding: 12px;
+  background-color: #36b1c0;
+  color: #fff;
+  cursor: pointer;
 }
\ No newline at end of file
Index: src/js/JEditor/Commons/Files/Templates/selectFileList.html
===================================================================
--- src/js/JEditor/Commons/Files/Templates/selectFileList.html	(révision 10818)
+++ src/js/JEditor/Commons/Files/Templates/selectFileList.html	(révision 10819)
@@ -11,8 +11,10 @@
                 <span class="fileName"><%=file.originalName%></span>
             </span>
             <%}%>
+            <%if(file.title){ %>
+                <div class="wrapperTitle"><%=file.title[lang]%></div>
 
-            <div class="wrapperTitle"><%=file.title[lang]%></div>
+            <%}%>
         </div>
         
     <%}%>
Index: src/js/JEditor/Commons/Files/Templates/selectFileListManager.html
===================================================================
--- src/js/JEditor/Commons/Files/Templates/selectFileListManager.html	(révision 10818)
+++ src/js/JEditor/Commons/Files/Templates/selectFileListManager.html	(révision 10819)
@@ -1,23 +1,27 @@
 <div class="header">
     <span class="wrapper">
-        <span class="numFiles"><%=content.length%></span>
-        <%=__('File(s)')%>
+        <%
+        if(content.length>0){%>
+            <span class="numFiles"><%=content.length%></span><%=__('File(s)')%> <span class="numberFiles"><%=content[0].attributes.countFile%></span>
+        <%}else{%>
+            <span class="numFiles">0</span><%=__('File(s)')%> <span class="numberFiles"><%=content.length%></span>
+        <%}%>
     </span>
 
     <div class="wrapperAction">
         <%if(showFilters){%>
         <!-- MES FILTRES -->
-        <a href="#" class="filtres" data-filterBy="">
+        <a href="#" class="filtres" id="all-files" data-filterBy="">
             <span class="icon-all-files"></span>
             <span class="infobulles add"><%= __('allFiles')%></span>
         </a>
 
-        <a href="#" class="filtres" data-filterBy="photos">
+        <a href="#" class="filtres" id="images" data-filterBy="photos">
             <span class="icon-image"></span>
             <span class="infobulles"><%= __('Photos')%></span>
         </a>
 
-        <a href="#" class="filtres" data-filterBy="fichiers">
+        <a href="#" class="filtres" id="files" data-filterBy="fichiers">
             <span class="icon-file"></span>
             <span class="infobulles"><%= __('Files')%></span>
         </a>
@@ -26,7 +30,7 @@
         <div class="trier-par">
             <%= __('sortBy')%> :
             <a class="sort-dropdown-toggle" href="#">
-                <span class="text"><%= __('fileSort')%></span>
+                <span class="text"><%= __('fileSortcreatedAtDesc')%></span>
                 <span class="caret"></span>
             </a>
             <ul class="dropdown-menu">
Index: src/js/JEditor/Commons/Files/Views/ReadOnlyFileListManagerView.js
===================================================================
--- src/js/JEditor/Commons/Files/Views/ReadOnlyFileListManagerView.js	(révision 10818)
+++ src/js/JEditor/Commons/Files/Views/ReadOnlyFileListManagerView.js	(révision 10819)
@@ -11,8 +11,8 @@
                 },
                 defaultOptions: {
                     showFilters: true,
-                    sortBy: '',
-                    sortOrder: ''
+                    sortBy: 'createdAt',
+                    sortOrder: 'asc'
                 },
                 initialize: function() {
                     this._super();
@@ -54,6 +54,9 @@
                     this.dom[this.cid].labelSort = this.$('.sort-dropdown-toggle .text');
                     this.dom[this.cid].filters = this.$('.filtres');
                     this.dom[this.cid].numFiles = this.$('.numFiles');
+                     //active class filtres
+                     var typeListe= this.listView.collection.getType();
+                     this.activeIconFiltre(typeListe);
                     this.delegateEvents();
                     return this;
                 },
@@ -80,6 +83,15 @@
                         return this.options.showFilters;
                     else
                         return this.options.showFilters = visible;
+                },
+                activeIconFiltre: function(type){
+                    if(type ==="all"){
+                        this.$("#all-files").addClass("active");
+                    }else if(type ==="image"){
+                        this.$("#images").addClass("active");
+                    }else if(type ==="file"){
+                        this.$("#files").addClass("active");
+                    }
                 }
             });
             return FileListManagerView;
Index: src/js/JEditor/Commons/Files/Views/SelectFileView.js
===================================================================
--- src/js/JEditor/Commons/Files/Views/SelectFileView.js	(révision 10818)
+++ src/js/JEditor/Commons/Files/Views/SelectFileView.js	(révision 10819)
@@ -18,8 +18,12 @@
                     this.childViews.listView = new ReadOnlyFileListView({collection: this.collection, allowMultipleSelect: this.options.allowMultipleSelect});
                     this.childViews.listManagerView = new FileListManagerView({collection: this.collection, listView: this.childViews.listView});
                     this.listenTo(this.childViews.listView, Events.ListViewEvents.SELECT, this.onSelect);
+                    this.listenTo(this.collection, Events.BackboneEvents.SYNC, this.onSync);
                     this.render();
                 },
+                events: {
+                    'click .flex-load-more .load-more' : 'loadMore',
+                },
                 render: function() {
                     this._super();
                     this.childViews.listView.selected = {};
@@ -35,6 +39,25 @@
                     this.delegateEvents();
                     return this;
                 },
+                onSync: function () {
+                    this.setLoading(false);
+                    this.loadMoreView();  
+                },
+                loadMoreView :function (){
+                    if(this.collection.hasmore){
+                        this.$(".flex-load-more").show();
+                    }
+                    else this.$(".flex-load-more").hide();
+                },
+                /**
+                 * afficher plus d'image sans effacer ce qui déjà afficher
+                 */
+                loadMore: function(){
+                    if(this.collection.hasmore){
+                        this.collection.fetch({remove: false});
+                        this.render();
+                    }
+                },
                 onSelect: function(file, length, collection) {
                     this.selected = file;
                 },
Index: src/js/JEditor/FilePanel/Templates/fileManagerUIView.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileManagerUIView.html	(révision 10818)
+++ src/js/JEditor/FilePanel/Templates/fileManagerUIView.html	(révision 10819)
@@ -1,16 +1,16 @@
 
 <!-- MES FILTRES -->
-<a href="#" class="filtres" data-filterBy="">
+<a href="#" class="filtres" id="all-files" data-filterBy="">
     <span class="icon-all-files"></span>
     <span class="infobulles add"><%= __("allFiles")%></span>
 </a>
 
-<a href="#" class="filtres" data-filterBy="photos">
+<a href="#" class="filtres" id="images" data-filterBy="photos">
     <span class="icon-image"></span>
     <span class="infobulles"><%= __("Photos")%></span>
 </a>
 
-<a href="#" class="filtres" data-filterBy="fichiers">
+<a href="#" class="filtres" id="files" data-filterBy="fichiers">
     <span class="icon-file"></span>
     <span class="infobulles"><%= __("Files")%></span>
 </a>
@@ -30,7 +30,10 @@
         <li><a href="#" data-sortBy="name" data-sortOrder="desc"><%= __("fileSortnameDesc")%></a></li>
     </ul>
 </div>
-
+<!-- COUNTFILE -->
+<div class="countFile">
+    <span></span>
+</div>
 <!-- SEPARATION -->
 <div class="separation"></div>
 
Index: src/js/JEditor/FilePanel/Views/CollectionDetailView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/CollectionDetailView.js	(révision 10818)
+++ src/js/JEditor/FilePanel/Views/CollectionDetailView.js	(révision 10819)
@@ -53,6 +53,7 @@
             this.toggleOne(model, !this.selected[model.cid])
         },
         backToList: function() {
+            this.$el.remove();
             this.trigger(Events.ListViewEvents.GOTO_COLLECTIONLIST);
             return false;
         },
Index: src/js/JEditor/FilePanel/Views/FileManagerUIView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileManagerUIView.js	(révision 10818)
+++ src/js/JEditor/FilePanel/Views/FileManagerUIView.js	(révision 10819)
@@ -25,8 +25,8 @@
                 events: {
                     'click [data-action="addSelectionToNewCollection"]': 'addSelectionToNewCollection',
                 },
-                sortBy: '',
-                sortOrder: '',
+                sortBy: 'createdAt',
+                sortOrder: 'desc',
                 initialize: function() {
                     this._super();
                     this._template = this.buildTemplate(fileManagerUIView, translate);
@@ -40,8 +40,13 @@
                     this.listenTo(this.options.listView, Events.ListViewEvents.SELECT, this.onSelect);
                     this.listenTo(this.selectCollectionView, Events.ListManagerViewEvents.ADD_TO_COLLECTION, this.addSelectionToExistingCollection);
                     this.listenTo(this.options.listView, Events.ListViewEvents.UPLOADER_COMPLETE, this.onUploaded);
-                    //this.listenToOnce(this.options.fileListView,Events.FileListViewEvents.SELECT,this.onSelect);
+                    this.listenTo(this.collection, Events.BackboneEvents.SYNC, this.updateCount);
+                    this.on("UPDATECOUNT",this.updateCount);
                 },
+                updateCount: function() {
+                    var fileLength=this.collection.length;
+                    this.$('.countFile span').html("<strong>"+fileLength+"</strong><span> "+translate("FileOn")+" "+this.collection.countFile+" </span>");
+                },
                 _onSortByClick: function(event) {
                     var $target = $(event.currentTarget);
                     var sortAttr = $target.data('sortby');
@@ -78,6 +83,9 @@
                     this.dom[this.cid].selectCollection = this.$('[data-action="selectCollection"]');
                     this.selectCollectionView.attach(this.dom[this.cid].selectCollection);
                     this.dom[this.cid].filters = this.$('.filtres');
+                     //Active class Filters
+                     var typeListe= this.collection.getType();
+                     this.activeIconFiltre(typeListe);
                     this.delegateEvents();
                     return this;
                 },
@@ -99,6 +107,15 @@
                     this.listView.selectNone();
                     return false;
                 },
+                activeIconFiltre: function(type){
+                    if(type ==="all"){
+                        this.$("#all-files").addClass("active");
+                    }else if(type ==="image"){
+                        this.$("#images").addClass("active");
+                    }else if(type ==="file"){
+                        this.$("#files").addClass("active");
+                    }
+                },
                 onSelect: function(selected, length) {
                     this._super(selected, length);
                     this.selected = selected;
@@ -127,10 +144,12 @@
                                             if (this.selected[cid] === true) {
                                                 var model = this.collection.get(cid);
                                                 this.trigger(Events.ListViewEvents.FILE_CLEARVIEW, model);
-                                                this.trigger(Events.ListViewEvents.FILE_CHECKCOLLECTION_ONDELETE, model);
+                                                // this.trigger(Events.ListViewEvents.FILE_CHECKCOLLECTION_ONDELETE, model);
                                                 model.destroy();
                                             }
                                         }
+                                        this.collection.setOffset(0);
+                                        this.collection.fetch();
                                         this.listView.selectNone();
                                     }, this),
                                     options: {dialogClass: 'delete no-close', dontAskAgain: true, subject: 'deleteFileItem'}
@@ -140,10 +159,12 @@
                                     if (this.selected[cid] === true) {
                                         var model = this.collection.get(cid);
                                         this.trigger(Events.ListViewEvents.FILE_CLEARVIEW, model);
-                                        this.trigger(Events.ListViewEvents.FILE_CHECKCOLLECTION_ONDELETE, model);
+                                        // this.trigger(Events.ListViewEvents.FILE_CHECKCOLLECTION_ONDELETE, model);//
                                         model.destroy();
                                     }
                                 }
+                                this.collection.setOffset(0);
+                                this.collection.fetch();
                                 this.listView.selectNone();
                             }
                     }
Index: src/less/imports/filePanel.less
===================================================================
--- src/less/imports/filePanel.less	(révision 10818)
+++ src/less/imports/filePanel.less	(révision 10819)
@@ -721,7 +721,6 @@
     -ms-transform:scale(1.3);
     -o-transform:scale(1.3);
     transform:scale(1.3);
-    
 }
 
 @media screen and (max-width: 1440px) {
@@ -772,7 +771,28 @@
 #files .file:hover .select ,#files .file.selected .select {
     opacity: 1;
 }
+.menu-wrapper.add-file {
+    width: 91px  !important;
+    height: 91px !important;
+    padding: 14px !important;
+    border: 1px solid #ddd;
+    color: #989898;
+    line-height: 14px !important;
+    font-size: 12px !important;
+    text-align: center;
 
+  }
+  .menu-wrapper.add-file> .icon > .icon-add {
+    position: absolute;
+    font-size: 14px;
+    top: 6px;
+    left: 0;
+    color: #FFFFFF;
+    right: 0;
+  }
+  .menu-wrapper.add-file> .icon > .icon-hexagon {
+    font-size: 26px;
+  }
 #files .my-files .file {
     width: 120px; height: 120px;
     position: relative;
@@ -901,16 +921,44 @@
     }
 
 }
-
+/* CountFile*/
+.countFile {
+    float: left;
+    position: relative;
+    margin: 20px 30px;
+    padding: 0 10px;
+    color: #888;
+    line-height: 30px;
+    background-color: #fff;
+    -webkit-border-radius: 4px;
+    -moz-border-radius: 4px;
+    border-radius: 4px;
+    font-weight: 500;
+    font-size: 16px;
+}
+/* LOAD MORE */
+.flex-load-more {
+    display: flex;
+    justify-content: center;
+}
+.flex-load-more .load-more{
+    padding: 12px;
+    background-color: #f27230;
+    color: #fff;
+    cursor: pointer;
+}
 /* ADD-FILE */
+.FileTop {
+    margin: auto 211px;
+  }
 .my-files .add-file {
-    width: 91px; height: 91px;
+    width: auto; height: 120px;
     padding: 14px;
     border: 1px solid #dddddd;
 
     color: #989898;
-    line-height: 14px;
-    font-size: 12px;
+    line-height: 60px;
+    font-size: 16px;
     text-align: center;
 
     cursor: pointer;
@@ -928,13 +976,13 @@
     }
 
     & .icon-hexagon {
-        font-size: 26px;
+        font-size: 34px;
     }
 
     & .icon-add {
         position: absolute;
         font-size: 14px;
-        top: 6px; left: 39px;
+        top: 15px; left: 0;right: 0;
         color: #FFFFFF;
     }
 
