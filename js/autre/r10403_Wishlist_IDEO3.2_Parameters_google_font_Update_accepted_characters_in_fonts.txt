Revision: r10403
Date: 2023-02-15 11:13:58 +0300 (lrb 15 Feb 2023) 
Author: jn.harison 

## Commit message
Wishlist IDEO3.2:Parameters google font:Update accepted characters in fonts

## Files changed

## Full metadata
------------------------------------------------------------------------
r10403 | jn.harison | 2023-02-15 11:13:58 +0300 (lrb 15 Feb 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Models/Params.js

Wishlist IDEO3.2:Parameters google font:Update accepted characters in fonts
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Models/Params.js
===================================================================
--- src/js/JEditor/ParamsPanel/Models/Params.js	(révision 10402)
+++ src/js/JEditor/ParamsPanel/Models/Params.js	(révision 10403)
@@ -111,7 +111,7 @@
                 //const fontRule1 = RegExp('<link (?:rel=["\']preconnect["\'].*href=["\']https://fonts.googleapis.com["\']|href=["\']https://fonts.googleapis.com["\'].*rel=["\']preconnect["\'])>', 'g');
                 const fontRule1 = RegExp('<link (rel=["\']preconnect["\'] href=["\']https:\/\/fonts.googleapis.com["\']|href=["\']https:\/\/fonts.googleapis.com["\'] rel=["\']preconnect["\'])>', 'g');
                 const fontRule2 = RegExp('<link (rel=["\']preconnect["\'] href=["\']https:\/\/fonts.gstatic.com["\']|href=["\']https:\/\/fonts.gstatic.com["\'] rel=["\']preconnect["\']) crossorigin>', 'g');
-                const fontRule3 = RegExp('<link (href=["\']([a-zA-Z0-9\-_=?\/.:&\@]*)\&display=swap["\'] rel=["\']stylesheet["\']|rel=["\']stylesheet["\'] href=["\']([a-zA-Z0-9\-_=?\/.:&\@]*)\&display=swap["\'])>', 'g');
+                const fontRule3 = RegExp('<link (href=["\']https:\/\/fonts.googleapis.com\/css2\?((.*))\&display=swap["\'] rel=["\']stylesheet["\']|rel=["\']stylesheet["\'] href=["\']https:\/\/fonts.googleapis.com\/css2\?((.*))\&display=swap["\'])>', 'g');
                 while (fontRule1.exec(attributes.FontsGoogle) !== null) {
                     countFontGoogleApi++;
                 }
