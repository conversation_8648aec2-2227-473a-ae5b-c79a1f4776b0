Revision: r10205
Date: 2023-01-24 12:33:16 +0300 (tlt 24 Jan 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
 suite correction bug gestion de menu 

## Files changed

## Full metadata
------------------------------------------------------------------------
r10205 | srazanandralisoa | 2023-01-24 12:33:16 +0300 (tlt 24 Jan 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NavigationPanel/Views/MenuItemView.js

 suite correction bug gestion de menu 
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NavigationPanel/Views/MenuItemView.js
===================================================================
--- src/js/JEditor/NavigationPanel/Views/MenuItemView.js	(révision 10204)
+++ src/js/JEditor/NavigationPanel/Views/MenuItemView.js	(révision 10205)
@@ -250,16 +250,15 @@
                                 var collection = new MenuItemCollection();
                                 var hierachy = that.$el.nestedSortable('toHierarchy', {attribute: 'data-item', expression: /.*/});
                                 var depth = 0;
-                                var noeud = false;
-                                var recurse = _.bind(function(hierarchy, collection, parent) {
+                                var recurse = _.bind(function(hierarchy, collection, parent, noeud = false) {
                                     var menuItems = [];
                                     for (var i = 0; i < hierarchy.length; i++) {
                                         depth++;
-                                       
                                         if( i === index && noeud === false){
                                             menuItem.position = i;
                                             menuItem.depth = depth;
                                             menuItem.parent = parent;
+                                            parent.add(menuItem, index);
                                             collection.add(menuItem);
                                             menuItems.push(menuItem);
                                         }
@@ -267,19 +266,17 @@
                                         item.position = i;
                                         item.depth = depth;
                                         item.parent = parent;
-                                        // droppable at the  first list not in the child 
-                                        // if (hierarchy[i].children){
-                                        //     noeud = true;
-                                        //     item.contents = recurse(hierarchy[i].children, collection, item);
-                                        //     noeud = false;
-                                        // }     
-                                        // else
-                                        //     item.contents = [];
+                                        if (hierarchy[i].children){
+                                            item.contents = recurse(hierarchy[i].children, collection, item, true);
+                                        }     
+                                        else
+                                            item.contents = [];
                                     
                                         collection.add(item);
                                         menuItems.push(item);
                                     }
                                     if(index == hierarchy.length && noeud === false){
+                                        menuItem.parent = parent;
                                         collection.add(menuItem);
                                         menuItems.push(menuItem);
                                     }
@@ -286,7 +283,7 @@
                                     return menuItems;
                                 }, this);
 
-                                that.model.contents = recurse(hierachy, collection, that.model);
+                                that.model.contents = recurse(hierachy, collection, that.model, false);
                                 that.listenTo(that.model, Events.BackboneEvents.CHANGE, that.render)
                                         .resetChildren();
                                 that.model.parentMenu.itemCollection = collection;
