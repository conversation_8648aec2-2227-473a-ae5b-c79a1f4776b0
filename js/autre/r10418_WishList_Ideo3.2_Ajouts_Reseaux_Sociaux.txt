Revision: r10418
Date: 2023-02-16 10:41:19 +0300 (lkm 16 Feb 2023) 
Author: noraja<PERSON>rive<PERSON> 

## Commit message
WishList Ideo3.2:Ajouts Reseaux Sociaux

## Files changed

## Full metadata
------------------------------------------------------------------------
r10418 | norajaonarivelo | 2023-02-16 10:41:19 +0300 (lkm 16 Feb 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Ancestors/Views/View.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Models/Params.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Templates/ListSocialNetwork.html
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Templates/SocialNetwork.html
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/ListSocialNetworks.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/SocialNetworks.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/nls/i18n.js

WishList Ideo3.2:Ajouts Reseaux Sociaux
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Ancestors/Views/View.js
===================================================================
--- src/js/JEditor/Commons/Ancestors/Views/View.js	(révision 10417)
+++ src/js/JEditor/Commons/Ancestors/Views/View.js	(révision 10418)
@@ -109,17 +109,7 @@
                     if ($target.attr("type") === "checkbox") {
                         this.model[field] = $target.data("negate") ? !$target.is(":checked") : $target.is(":checked");
                     }
-                    else {
-                        if(field.includes("_")){
-                            var arraySpliteField=field.split('_');
-                            field =arraySpliteField[0];
-                            var keyField =arraySpliteField[1];
-                            var listeSocialNetwork =this.model.get("SocialNetworksUrl");
-                            listeSocialNetwork[keyField].url=value;
-                            this.model.KeySocialNetworks=keyField;
-                            this.model.set("SocialNetworksUrl", listeSocialNetwork);
-                        }
-                        
+                    else {                        
                         this.model.set(field, value, {validate: true});
                     }
                     //error_positioning
Index: src/js/JEditor/ParamsPanel/Models/Params.js
===================================================================
--- src/js/JEditor/ParamsPanel/Models/Params.js	(révision 10417)
+++ src/js/JEditor/ParamsPanel/Models/Params.js	(révision 10418)
@@ -20,8 +20,15 @@
         viadeoUrl:/^(https?\:\/\/)?([a-zA-Z]*\.)?(viadeo\.com|emploi\.lefigaro\.fr)\/(.*)/,
         linkedinUrl:/^(https?\:\/\/)?(www\.)?linkedin\.com\/(.*)/,
         youtubeUrl:/^(https?\:\/\/)?(www\.)?youtube\.com\/(.*)/,
-        instagramUrl:/^(https?\:\/\/)?(www\.)?instagram\.com\/(.*)/
-    };
+        instagramUrl:/^(https?\:\/\/)?(www\.)?instagram\.com\/(.*)/,
+        skypeUrl :/^(https?\:\/\/)?(www\.)?skype\.com\/(.*)|^(https?\:\/\/)?(www\.)?web.skype\.com\/(.*)|^(skype)\:(.*)\?(call|chat)/,
+        theforkUrl:/^(https?\:\/\/)?(www\.)?thefork\.(com|com\.au|fr)\/(.*)/,
+        tiktokUrl:/^(https?\:\/\/)?(www\.)?tiktok\.com\/(.*)/,
+        tripadvisorUrl:/^(https?\:\/\/)?(www\.)?tripadvisor\.(com|com\.au|fr|ca)\/(.*)/,
+        wazeUrl:/^(https?\:\/\/)?(www\.)?waze\.com\/(.*)/,
+        whatsappUrl:/^(https?\:\/\/)?(www\.)?(api\.whatsapp\.com|wa\.me)\/(.*)/,
+        slideshareUrl:/^(https?\:\/\/)?(www\.)?slideshare\.net\/(.*)/,
+    };  
     //verification for google fonts
 
     var Params = Model.extend({
@@ -58,7 +65,7 @@
         },
         initialize: function () {
             this._super();
-            this.on("change:facebookUrl change:twitterUrl change:pinterestUrl change:mybusinessUrl change:youtubeUrl change:linkedinUrl change:viadeoUrl change:instagramUrl", this.onSocialNetworkUrlChange);
+            this.on("change:facebookUrl change:twitterUrl change:pinterestUrl change:mybusinessUrl change:youtubeUrl change:linkedinUrl change:viadeoUrl change:instagramUrl change:skypeUrl change:theforkUrl change:tiktokUrl change:tripadvisorUrl change:wazeUrl change:whatsappUrl change:slideshareUrl", this.onSocialNetworkUrlChange);
             this.on("change:MarqueClient", this.onMarqueClientChange);
         },
         onSocialNetworkUrlChange: function (model) {
@@ -65,7 +72,7 @@
             var changed = this.changedAttributes();
             var that = this;
             _.each(changed, function (value, key) {
-                if ((key === "facebookUrl" || key === "twitterUrl" || key === "pinterestUrl" || key === "mybusinessUrl" || key==="instagramUrl"||key==="youtubeUrl"||key==="linkedinUrl"||key==="viadeoUrl")) {
+                if ((key === "facebookUrl" || key === "twitterUrl" || key === "pinterestUrl" || key === "mybusinessUrl" || key==="instagramUrl"||key==="youtubeUrl"||key==="linkedinUrl"||key==="viadeoUrl" || key==="skypeUrl" || key==="theforkUrl" || key ==="tiktokUrl" || key ==="tripadvisorUrl" || key==="wazeUrl" || key==="whatsappUrl" || key==="slideshareUrl")) {
                     that.trigger(Events.SettingsEvents.SOCIAL_NETWORK__CHANGE, key, value, model);
                 }
             });
@@ -79,9 +86,9 @@
             }
         },
         validate: function (attributes, options) {
-            var socialNetworks = ["facebookUrl", "mybusinessUrl", "twitterUrl", "pinterestUrl", "youtubeUrl", "linkedinUrl", "viadeoUrl", "instagramUrl"];
+            var socialNetworks = ["facebookUrl", "mybusinessUrl", "twitterUrl", "pinterestUrl", "youtubeUrl", "linkedinUrl", "viadeoUrl", "instagramUrl","skypeUrl","theforkUrl","tiktokUrl","tripadvisorUrl","wazeUrl","whatsappUrl","slideshareUrl"];
             //en gros c'est ça dans une boucle:
-            for (var i = 0; i < socialNetworks.length; i++) {
+            for (var i = 0; i < socialNetworks.length; i++) {             
                 if (attributes[socialNetworks[i]] && (!attributes[socialNetworks[i]].match || !attributes[socialNetworks[i]].match(regExes[socialNetworks[i]])))
                     return {field: socialNetworks[i], message: translate("Invalid_" + socialNetworks[i])};
             }
Index: src/js/JEditor/ParamsPanel/Templates/ListSocialNetwork.html
===================================================================
--- src/js/JEditor/ParamsPanel/Templates/ListSocialNetwork.html	(révision 10417)
+++ src/js/JEditor/ParamsPanel/Templates/ListSocialNetwork.html	(révision 10418)
@@ -170,6 +170,141 @@
                         
                     </span>
                     
+                <% } if( social.type =="skypeUrl") { %>
+                    <span class="inline-params__name  skyoe">
+                        <svg width="30px" height="30px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+                            <g clip-path="url(#clip0_23_95)">
+                            <path d="M24.9178 40.1316C16.6118 40.1316 12.8289 35.9375 12.8289 32.8125C12.8289 31.25 14.0625 30.0987 15.7072 30.0987C19.2434 30.0987 18.3388 35.4441 25 35.4441C28.3717 35.4441 30.3454 33.3882 30.3454 31.4967C30.3454 30.3454 29.6875 29.0296 27.4671 28.5362L20.0658 26.6447C14.1447 25.1645 13.0757 21.7928 13.0757 18.75C13.0757 12.4178 18.9145 10.1151 24.4243 10.1151C29.523 10.1151 35.6086 12.9112 35.6086 16.7763C35.6086 18.4211 34.2105 19.3257 32.648 19.3257C29.6053 19.3257 30.0987 15.0493 24.0132 15.0493C20.9704 15.0493 19.4079 16.4474 19.4079 18.4211C19.4079 20.3947 21.7928 21.0526 23.8487 21.5461L29.2763 22.7796C35.2796 24.0954 36.8421 27.6316 36.8421 31.0033C36.8421 36.102 32.8125 40.1316 24.9178 40.1316ZM47.8619 29.1118C48.1086 27.7961 48.1908 26.398 48.1908 25C48.1908 12.0066 37.7467 1.48026 24.8355 1.48026C23.4375 1.48026 22.1217 1.5625 20.8059 1.80921C18.75 0.657895 16.4474 0 13.898 0C6.25 0 0 6.25 0 13.9803C0 16.5296 0.657895 18.8322 1.80921 20.8882C1.5625 22.2039 1.48026 23.602 1.48026 25C1.48026 37.9934 11.9243 48.5197 24.8355 48.5197C26.2336 48.5197 27.5493 48.4375 28.8651 48.1908C30.9211 49.3421 33.2237 50 35.773 50C43.4211 50 49.6711 43.75 49.6711 36.0197C49.6711 33.5526 49.0132 31.1678 47.8619 29.1118Z" fill="#00B0F0"/>
+                            </g>
+                            <defs>
+                            <clipPath id="clip0_23_95">
+                            <rect width="50" height="50" fill="white"/>
+                            </clipPath>
+                            </defs>
+                        </svg>
+                            
+                        <% 
+                            var skypeUrl= (social.title ==="")?__("Skype"):social.title;
+                        %>
+                        <input class="content rs" type="text" name="<%=social.type%>_<%=i %>" value='<%-skypeUrl%>' data-titleId="<%=social.type%>_<%=i %>" data-reseaux="Skype" />
+                        
+                    </span>
+                    
+                <% } if( social.type =="theforkUrl") { %>
+                    <span class="inline-params__name  thefork">
+                        <svg width="30px" height="30px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+                            <g clip-path="url(#clip0_23_37)">
+                                <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.58172 0 0 3.58172 0 8V42C0 46.4183 3.58172 50 8 50H42C46.4183 50 50 46.4183 50 42V8C50 3.58172 46.4183 0 42 0H8ZM25.4035 20.8704H23.9171V5H25.4035V20.8704ZM27.6047 5.21953L29.0912 5.41963V20.3696L27.6047 20.6554V5.21953ZM20.2383 5.39653L21.7247 5.19643V20.6037L20.2383 20.3179V5.39653ZM23.0362 40.2233C23.0362 40.7378 22.979 42.3385 22.9218 43.3105C22.8647 44.2825 22.8075 45.0543 22.7503 44.997C13.889 44.1395 7 37.079 7 28.5321C7 21.6431 11.3735 15.8118 17.7194 13.3249C17.605 14.7255 17.4335 17.4125 17.3764 18.4702C17.3192 19.5278 17.2906 21.4144 17.5765 23.0438C17.8337 24.473 18.4626 25.9308 19.12 27.16C19.3078 27.5042 19.4998 27.8484 19.6879 28.1855C20.1871 29.0804 20.6588 29.9258 20.9495 30.5903C21.0205 30.7525 21.0869 30.9022 21.1499 31.0439C21.4418 31.701 21.6576 32.1868 21.8928 32.9628C22.2072 33.906 22.4073 34.6493 22.4931 35.278C22.5201 35.4271 22.5521 35.5745 22.5865 35.7336C22.6974 36.2461 22.8346 36.8798 22.9218 38.0795C23.0431 39.438 23.0409 39.6641 23.0374 40.0159C23.0368 40.0786 23.0362 40.1454 23.0362 40.2233ZM26.2635 40.2233C26.2635 40.7378 26.3207 42.3385 26.3779 43.3105C26.4351 44.2825 26.4922 45.0543 26.5494 44.997C35.4108 44.1395 42.2998 37.079 42.2998 28.5321C42.2998 21.6431 37.9263 15.8118 31.5803 13.3249C31.6948 14.7255 31.8663 17.4125 31.9233 18.4702C31.9805 19.5278 32.009 21.4144 31.7233 23.0438C31.466 24.473 30.8373 25.9308 30.1798 27.16C29.9922 27.5039 29.8003 27.8478 29.6123 28.1846C29.1129 29.0799 28.641 29.9256 28.3502 30.5903C28.2793 30.7525 28.2128 30.9021 28.1498 31.0438C27.8579 31.701 27.6421 32.1868 27.4069 32.9628C27.0925 33.906 26.8924 34.6493 26.8067 35.278C26.7796 35.4271 26.7476 35.5745 26.7132 35.7336C26.6023 36.246 26.4651 36.8798 26.3779 38.0795C26.2566 39.4379 26.2588 39.6641 26.2623 40.0159C26.2629 40.0786 26.2635 40.1454 26.2635 40.2233Z" fill="#558F40"/>
+                                </g>
+                                <defs>
+                                <clipPath id="clip0_23_37">
+                                <rect width="50" height="50" fill="white"/>
+                                </clipPath>
+                                </defs>
+                        </svg>
+                            
+                        <% 
+                            var theforkUrl= (social.title ==="")?__("Thefork"):social.title;
+                        %>
+                        <input class="content rs" type="text" name="<%=social.type%>_<%=i %>" value='<%-theforkUrl%>' data-titleId="<%=social.type%>_<%=i %>" data-reseaux="Thefork" />
+                        
+                    </span>
+                    
+                <% } if( social.type =="tiktokUrl") { %>
+                    <span class="inline-params__name  tiktok">
+                        <svg width="30px" height="30px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+                            <g clip-path="url(#clip0_23_35)">
+                                <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.58172 0 0 3.58172 0 8V42C0 46.4183 3.58172 50 8 50H42C46.4183 50 50 46.4183 50 42V8C50 3.58172 46.4183 0 42 0H8ZM41.0201 21.3758H42.094V14.396C36.7248 14.1275 32.698 10.1007 32.1611 5H26.5235V32.9195C26.5235 36.4094 23.5705 39.094 20.0805 39.094C16.5906 39.094 13.906 36.4094 13.906 32.9195C13.906 29.4295 16.5906 26.745 20.0805 26.745H20.8859V20.8389H20.0805C13.3691 20.8389 8 26.2081 8 32.9195C8 39.6309 13.3691 45 20.0805 45C26.7919 45 32.1611 39.6309 32.1611 32.9195V16.5436C34.0403 19.4966 37.5302 21.3758 41.0201 21.3758Z" fill="#000000"/>
+                                </g>
+                                <defs>
+                                <clipPath id="clip0_23_35">
+                                <rect width="50" height="50" fill="white"/>
+                                </clipPath>
+                                </defs>
+                        </svg>
+                            
+                        <% 
+                            var tiktokUrl= (social.title ==="")?__("Tiktok"):social.title;
+                        %>
+                        <input class="content rs" type="text" name="<%=social.type%>_<%=i %>" value='<%-tiktokUrl%>' data-titleId="<%=social.type%>_<%=i %>" data-reseaux="Tiktok" />
+                    </span>
+                <% } if( social.type =="tripadvisorUrl") { %>
+                    <span class="inline-params__name  tripadvisor">
+                        <svg width="30px" height="30px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+                            <g clip-path="url(#clip0_23_35)">
+                                <g clip-path="url(#clip0_23_43)">
+                                    <path d="M13.75 26C13.75 26.75 14.25 27.25 15 27.25C15.75 27.25 16.25 26.75 16.25 26C16.25 25.25 15.75 24.75 15 24.75C14.25 24.75 13.75 25.25 13.75 26Z" fill="#32D99C"/>
+                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M8.25 26C8.25 29.75 11.25 32.75 15 32.75C18.75 32.75 21.75 29.75 21.75 26C21.75 22.25 18.75 19.25 15 19.25C11.25 19.25 8.25 22.25 8.25 26ZM10.75 26C10.75 23.75 12.75 21.75 15 21.75C17.25 21.75 19.25 23.75 19.25 26C19.25 28.25 17.25 30.25 15 30.25C12.75 30.25 10.75 28.25 10.75 26Z" fill="#32D99C"/>
+                                    <path d="M17 16.25C21.5 17 25 21.25 25 26C25 21.25 28.5 17.25 33 16.25C30.5 15.5 28 15 25 15C22 15 19.5 15.5 17 16.25Z" fill="#32D99C"/>
+                                    <path d="M33.75 26C33.75 26.75 34.25 27.25 35 27.25C35.75 27.25 36.25 26.75 36.25 26C36.25 25.25 35.75 24.75 35 24.75C34.25 24.75 33.75 25.25 33.75 26Z" fill="#32D99C"/>
+                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M28.25 26C28.25 29.75 31.25 32.75 35 32.75C38.75 32.75 41.75 29.75 41.75 26C41.75 22.25 38.75 19.25 35 19.25C31.25 19.25 28.25 22.25 28.25 26ZM30.75 26C30.75 23.75 32.75 21.75 35 21.75C37.25 21.75 39.25 23.75 39.25 26C39.25 28.25 37.25 30.25 35 30.25C32.75 30.25 30.75 28.25 30.75 26Z" fill="#32D99C"/>
+                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.58172 0 0 3.58172 0 8V42C0 46.4183 3.58172 50 8 50H42C46.4183 50 50 46.4183 50 42V8C50 3.58172 46.4183 0 42 0H8ZM39.25 16H45C44.25 16.75 43.25 18.75 43 20C44.25 21.75 45 23.75 45 26C45 31.5 40.5 36 35 36C32.5 36 30 35 28.25 33.25L25 38L21.75 33.25C20 35 17.75 36 15 36C9.5 36 5 31.5 5 26C5 23.75 5.75 21.75 7 20C6.75 18.75 5.75 16.75 5 16H10.75C14.5 13.25 19.5 12 25 12C30.5 12 35.5 13.25 39.25 16Z" fill="#32D99C"/>
+                                    </g>
+                                    <defs>
+                                    <clipPath id="clip0_23_43">
+                                    <rect width="50" height="50" fill="white"/>
+                                    </clipPath>
+                                    </defs>
+                        </svg>
+                            
+                        <% 
+                            var tripadvisorUrl= (social.title ==="")?__("Tripadvisor"):social.title;
+                        %>
+                        <input class="content rs" type="text" name="<%=social.type%>_<%=i %>" value='<%-tripadvisorUrl%>' data-titleId="<%=social.type%>_<%=i %>" data-reseaux="Tripadvisor" />
+                    </span>
+                <% } if( social.type =="wazeUrl") { %>
+                    <span class="inline-params__name  waze">
+                        <svg width="30px" height="30px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+                            <g clip-path="url(#clip0_23_39)">
+                                <path d="M15.522 39.3771C15.522 40.2174 16.2026 40.9065 17.0513 40.9065C17.9 40.9065 18.5807 40.2174 18.5807 39.3771C18.5807 38.5368 17.9 37.8477 17.0513 37.8477C16.211 37.8477 15.522 38.5284 15.522 39.3771Z" fill="#32CDFD"/>
+                                <path d="M30.2778 39.3771C30.2778 40.2174 30.9584 40.9065 31.8072 40.9065C32.6559 40.9065 33.3449 40.2174 33.3365 39.3771C33.3365 38.5368 32.6475 37.8477 31.8072 37.8477C30.9668 37.8477 30.2778 38.5284 30.2778 39.3771Z" fill="#32CDFD"/>
+                                <path fill-rule="evenodd" clip-rule="evenodd" d="M31.8072 35.1335C32.7819 35.1335 33.6894 35.4697 34.4037 36.0243C39.7313 33.6126 43.1177 28.68 43.1177 23.1843C43.1177 15.2854 36.0843 8.86549 27.446 8.86549C18.8076 8.86549 11.7826 15.2854 11.7826 23.1843C11.7826 23.4571 11.7903 23.726 11.7979 23.9911C11.8015 24.118 11.8051 24.244 11.8078 24.3692C11.8582 25.8817 11.8918 27.3019 11.2028 28.5119C10.4969 29.7388 9.16924 30.537 7.02645 31.016C7.95079 33.2092 10.4549 34.8898 14.4632 35.9991C15.1774 35.4529 16.0766 35.1251 17.0429 35.1251C18.5723 35.1251 19.9084 35.9318 20.6563 37.1419C23.32 37.436 25.7737 37.5032 27.446 37.5032C27.6308 37.5032 27.8157 37.5032 28.0006 37.4948C28.698 36.0999 30.1433 35.1335 31.8072 35.1335ZM24.488 19.6046C24.488 20.5885 23.6904 21.3861 22.7066 21.3861C21.7227 21.3861 20.9251 20.5885 20.9251 19.6046C20.9251 18.6208 21.7227 17.8232 22.7066 17.8232C23.6904 17.8232 24.488 18.6208 24.488 19.6046ZM33.3281 24.5625C33.3281 24.0499 33.7483 23.6297 34.2609 23.6297C34.7734 23.6297 35.1936 24.0415 35.1936 24.5625C35.1936 26.1422 34.3869 27.6128 32.9164 28.6968C31.5299 29.7135 29.7148 30.2766 27.7821 30.2766C25.8494 30.2766 24.0343 29.7219 22.6478 28.6968C21.1773 27.6128 20.3706 26.1422 20.3706 24.5625C20.3706 24.0499 20.7907 23.6297 21.3033 23.6297C21.8159 23.6297 22.236 24.0499 22.236 24.5625C22.236 25.5372 22.7822 26.4699 23.757 27.1926C24.8242 27.9825 26.2527 28.4111 27.7821 28.4111C29.3114 28.4111 30.74 27.9741 31.8072 27.1926C32.7903 26.4699 33.3281 25.5372 33.3281 24.5625ZM34.2608 19.6046C34.2608 20.5885 33.4632 21.3861 32.4794 21.3861C31.4955 21.3861 30.6979 20.5885 30.6979 19.6046C30.6979 18.6208 31.4955 17.8232 32.4794 17.8232C33.4632 17.8232 34.2608 18.6208 34.2608 19.6046Z" fill="#32CDFD"/>
+                                <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.58172 0 0 3.58172 0 8V42C0 46.4183 3.58172 50 8 50H42C46.4183 50 50 46.4183 50 42V8C50 3.58172 46.4183 0 42 0H8ZM27.4544 7C32.1181 7 36.5129 8.67221 39.8237 11.7141C43.1513 14.7729 44.9832 18.8484 45 23.2011C45 26.4784 43.9496 29.6295 41.9497 32.3269C40.3279 34.5201 38.1599 36.2932 35.639 37.52C35.9163 38.083 36.0675 38.7132 36.0675 39.3771C36.0675 41.7216 34.16 43.6207 31.824 43.6207C29.4795 43.6207 27.5804 41.7132 27.5804 39.3771H27.4628C25.9166 39.3771 23.715 39.3183 21.2949 39.0914C21.2949 39.1376 21.297 39.186 21.2991 39.2342C21.3012 39.2826 21.3033 39.3309 21.3033 39.3771C21.3033 41.7216 19.4042 43.6207 17.0597 43.6207C14.7153 43.6207 12.8162 41.7132 12.8162 39.3771C12.8162 38.7385 12.959 38.1334 13.2111 37.5872C8.69867 36.1839 5.95926 33.9991 5.06013 31.0748C4.94249 30.6967 5.00131 30.2766 5.20298 29.9404C5.41306 29.5959 5.75759 29.3606 6.15253 29.285C10.06 28.5539 10.0264 27.2598 9.94232 24.4196C9.92552 24.0247 9.91711 23.6129 9.91711 23.1843C9.91711 18.8484 11.7574 14.7729 15.085 11.7141C18.3874 8.67221 22.7822 7 27.4544 7Z" fill="#32CDFD"/>
+                                </g>
+                                <defs>
+                                <clipPath id="clip0_23_39">
+                                <rect width="50" height="50" fill="white"/>
+                                </clipPath>
+                                </defs>
+                        </svg>
+                            
+                        <% 
+                            var wazeUrl= (social.title ==="")?__("Waze"):social.title;
+                        %>
+                        <input class="content rs" type="text" name="<%=social.type%>_<%=i %>" value='<%-wazeUrl%>' data-titleId="<%=social.type%>_<%=i %>" data-reseaux="Waze" />
+                    </span>
+                <% } if( social.type =="whatsappUrl") { %>
+                    <span class="inline-params__name  whatsapp">
+                        <svg width="30px" height="30px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+                            <g clip-path="url(#clip0_23_39)">
+                                <path fill-rule="evenodd" clip-rule="evenodd" d="M16.6585 39.1785C19.1981 40.6866 22.1127 41.4837 25.0899 41.4837C34.2166 41.4837 41.6399 34.0603 41.6478 24.9336C41.6478 20.5108 39.9287 16.346 36.8031 13.2204C33.6696 10.0947 29.5126 8.36784 25.1055 8.36784C15.9709 8.36784 8.53975 15.7912 8.53975 24.9179C8.53975 28.0436 9.41492 31.091 11.0715 33.7243L11.4622 34.3495L9.79 40.46L16.0568 38.8191L16.6585 39.1785ZM30.7706 27.4653C31.2239 27.6294 33.6696 28.8328 34.1697 29.0828C34.2654 29.1307 34.3549 29.1739 34.4379 29.2141C34.7886 29.3839 35.0249 29.4983 35.1387 29.6689C35.2637 29.8798 35.2637 30.8722 34.8496 32.0365C34.4354 33.2008 32.4428 34.2635 31.4895 34.4042C30.63 34.537 29.5517 34.5839 28.3561 34.2088C27.6372 33.9822 26.7074 33.6775 25.5196 33.1617C20.8451 31.1427 17.6909 26.6102 17.1023 25.7643C17.0621 25.7066 17.0339 25.6661 17.018 25.6446L17.01 25.634C16.7326 25.2615 14.9863 22.9166 14.9863 20.4952C14.9863 18.2181 16.1039 17.0179 16.6239 16.4596C16.6624 16.4183 16.6976 16.3804 16.7288 16.346C17.1899 15.8459 17.729 15.7208 18.0572 15.7208C18.3854 15.7208 18.7214 15.7208 19.0105 15.7365C19.0466 15.7383 19.0841 15.7381 19.1228 15.7379C19.4114 15.7361 19.7697 15.7339 20.1279 16.5882C20.2537 16.8919 20.4328 17.329 20.6248 17.7974C21.0648 18.8714 21.5724 20.1101 21.6595 20.2842C21.7845 20.5343 21.8627 20.8234 21.6986 21.1594C21.6627 21.2278 21.6309 21.2912 21.6008 21.3512C21.4866 21.579 21.3964 21.7589 21.1985 21.9877C21.129 22.0702 21.0576 22.157 20.986 22.2442C20.8 22.4705 20.6119 22.6992 20.4483 22.8629C20.1983 23.1129 19.9404 23.3786 20.2295 23.8787C20.5186 24.3788 21.5188 26.0041 22.9957 27.3247C24.5841 28.7431 25.9646 29.3413 26.6663 29.6454C26.8035 29.7048 26.9147 29.753 26.9965 29.7939C27.4966 30.0439 27.7857 29.9971 28.0748 29.6689C28.3639 29.3329 29.3251 28.2155 29.6532 27.7154C29.9814 27.2153 30.3174 27.3012 30.7706 27.4653Z" fill="#25D366"/>
+                            <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.58172 0 0 3.58172 0 8V42C0 46.4183 3.58172 50 8 50H42C46.4183 50 50 46.4183 50 42V8C50 3.58172 46.4183 0 42 0H8ZM25.0899 5C30.419 5.00781 35.42 7.07853 39.1785 10.8449C42.9371 14.6112 45.0078 19.6122 45 24.9414C45 35.9201 36.0607 44.8515 25.082 44.8515H25.0742C21.7454 44.8515 18.4636 44.0154 15.5567 42.4292L5 45.1953L7.82086 34.873C6.07833 31.8568 5.16409 28.4264 5.16409 24.9179C5.17191 13.9392 14.1112 5 25.0899 5Z" fill="#25D366"/>
+                            </g>
+                            <defs>
+                            <clipPath id="clip0_23_45">
+                            <rect width="50" height="50" fill="white"/>
+                            </clipPath>
+                            </defs>
+                        </svg>
+                            
+                        <% 
+                            var whatsappUrl= (social.title ==="")?__("Whatsapp"):social.title;
+                        %>
+                        <input class="content rs" type="text" name="<%=social.type%>_<%=i %>" value='<%-whatsappUrl%>' data-titleId="<%=social.type%>_<%=i %>" data-reseaux="Whatsapp" />
+                    </span>
+                <% } if( social.type =="slideshareUrl") { %>
+                    <span class="inline-params__name  whatsapp">
+                        <svg width="30px" height="30px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+                            <path d="M10.9746 5.00104H39.2425C40.1547 5.00608 41.0282 5.37071 41.6734 6.01582C42.3185 6.66092 42.683 7.53442 42.6882 8.44672V24.4094C42.9215 24.2546 43.1521 24.095 43.3799 23.9305C44.5949 23.0756 45.4649 24.2755 44.73 25.3554C42.4447 27.9415 39.5385 29.9031 36.2853 31.0551C36.8081 32.7207 37.0809 34.4543 37.0953 36.1998C37.0953 40.8196 34.5905 45.1994 29.8358 45.1994C29.261 45.236 28.6853 45.1481 28.1475 44.9419C27.6098 44.7357 27.1229 44.4161 26.72 44.0047C26.3171 43.5932 26.0077 43.0997 25.813 42.5579C25.6181 42.0159 25.5423 41.4385 25.591 40.8647V34.22C26.1832 34.3426 26.786 34.4079 27.3908 34.415C28.6964 34.4499 29.9798 34.0713 31.0572 33.3331C32.1347 32.595 32.9514 31.5353 33.3906 30.3053C34.1555 28.1753 32.7156 27.6203 31.6506 29.2552C30.0007 31.8351 27.8108 31.8351 25.501 30.3353V30.0652C25.501 27.8103 26.9179 27.8197 28.3332 27.8291C28.4242 27.8297 28.5152 27.8303 28.6058 27.8303C32.979 28.3092 37.3708 27.4196 41.1882 25.3177V8.45307C41.1848 7.93639 40.9781 7.4418 40.6127 7.07642C40.2474 6.71103 39.7528 6.5043 39.2361 6.50095H10.9441C10.6825 6.49372 10.422 6.53855 10.178 6.63337C9.93407 6.72819 9.71148 6.87081 9.5234 7.05284C9.3353 7.23487 9.18547 7.45264 9.0827 7.69337C8.98016 7.93357 8.92655 8.19179 8.92497 8.45293V26.2848H7.42505V8.44699C7.42742 7.98543 7.52201 7.529 7.70321 7.1045C7.88443 6.67999 8.14863 6.29597 8.48032 5.97499C8.812 5.65401 9.20447 5.40253 9.63468 5.23533C10.0615 5.06945 10.5169 4.98984 10.9746 5.00104Z" fill="#026C97"/>
+                            <path d="M30.9452 26.0006C31.5678 26.0226 32.1885 25.9216 32.772 25.7037C33.3556 25.4857 33.8903 25.1548 34.3458 24.7298C34.8014 24.3049 35.1688 23.7944 35.4268 23.2275C35.6849 22.6605 35.8286 22.0484 35.85 21.4257C35.7838 20.183 35.2332 19.0158 34.3162 18.1745C33.3992 17.3333 32.189 16.885 30.9452 16.926C29.6891 16.8855 28.468 17.344 27.5487 18.2013C26.6297 19.0585 26.0874 20.2448 26.0405 21.5008C26.1065 22.7435 26.6572 23.9108 27.5741 24.752C28.4911 25.5932 29.7015 26.0416 30.9452 26.0006Z" fill="#026C97"/>
+                            <path fill-rule="evenodd" clip-rule="evenodd" d="M24.4651 21.3657C24.4497 21.9954 24.3092 22.6156 24.0521 23.1907C23.7949 23.7656 23.426 24.2837 22.9669 24.7149C22.5078 25.1462 21.9676 25.4818 21.3776 25.7025C20.7877 25.9232 20.1597 26.0245 19.5304 26.0005C18.9079 26.0225 18.2871 25.9215 17.7036 25.7036C17.1201 25.4856 16.5853 25.1547 16.1297 24.7297C15.6742 24.3048 15.3069 23.7943 15.0488 23.2274C14.7907 22.6604 14.6469 22.0483 14.6257 21.4257C14.6917 20.1829 15.2423 19.0157 16.1593 18.1744C17.0763 17.3332 18.2866 16.8849 19.5304 16.9259C20.7694 16.8763 21.9789 17.3128 22.9008 18.1422C23.8226 18.9716 24.384 20.1282 24.4651 21.3657ZM21.0303 27.8303C15.9007 28.2858 10.7761 26.9037 6.5711 23.9305C5.35617 23.0756 4.51621 24.2755 5.31117 25.3405C7.58693 27.9304 10.4894 29.893 13.7407 31.0402C10.9059 40.6996 16.0956 45.1994 20.1903 45.1844C20.7667 45.2278 21.3455 45.1454 21.887 44.943C22.4284 44.7407 22.9193 44.4231 23.3258 44.0122C23.7324 43.6014 24.0449 43.1071 24.2416 42.5637C24.4383 42.0202 24.5146 41.4404 24.4651 40.8647C24.4651 37.9848 24.4651 33.2 24.4651 33.2C25.4173 33.4838 26.3985 33.66 27.39 33.725C28.6617 33.8257 29.9329 33.5235 31.0233 32.8613C32.1135 32.1989 32.9677 31.2102 33.4646 30.0352C34.2896 28.0553 32.8797 27.4854 31.7847 29.0003C29.6698 31.9101 26.955 31.3101 24.1502 28.6253C23.3851 27.9204 22.9652 27.7254 21.0003 27.8303H21.0303Z" fill="#D8711C"/>                        
+                        </svg>
+                            
+                        <% 
+                            var slideshareUrl= (social.title ==="")?__("Slideshare"):social.title;
+                        %>
+                        <input class="content rs" type="text" name="<%=social.type%>_<%=i %>" value='<%-slideshareUrl%>' data-titleId="<%=social.type%>_<%=i %>" data-reseaux="slideshare" />
+                    </span>
                 <% } %>
                 <label>
                     <span class="custom-input">
Index: src/js/JEditor/ParamsPanel/Templates/SocialNetwork.html
===================================================================
--- src/js/JEditor/ParamsPanel/Templates/SocialNetwork.html	(révision 10417)
+++ src/js/JEditor/ParamsPanel/Templates/SocialNetwork.html	(révision 10418)
@@ -142,6 +142,122 @@
                     <%=__("viadeo")%>
                 </a>
             </li>
+            <li>
+                <a  data-id="skypeUrl" class="skype">
+                    <svg width="20px" height="20px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+                        <g clip-path="url(#clip0_23_95)">
+                        <path d="M24.9178 40.1316C16.6118 40.1316 12.8289 35.9375 12.8289 32.8125C12.8289 31.25 14.0625 30.0987 15.7072 30.0987C19.2434 30.0987 18.3388 35.4441 25 35.4441C28.3717 35.4441 30.3454 33.3882 30.3454 31.4967C30.3454 30.3454 29.6875 29.0296 27.4671 28.5362L20.0658 26.6447C14.1447 25.1645 13.0757 21.7928 13.0757 18.75C13.0757 12.4178 18.9145 10.1151 24.4243 10.1151C29.523 10.1151 35.6086 12.9112 35.6086 16.7763C35.6086 18.4211 34.2105 19.3257 32.648 19.3257C29.6053 19.3257 30.0987 15.0493 24.0132 15.0493C20.9704 15.0493 19.4079 16.4474 19.4079 18.4211C19.4079 20.3947 21.7928 21.0526 23.8487 21.5461L29.2763 22.7796C35.2796 24.0954 36.8421 27.6316 36.8421 31.0033C36.8421 36.102 32.8125 40.1316 24.9178 40.1316ZM47.8619 29.1118C48.1086 27.7961 48.1908 26.398 48.1908 25C48.1908 12.0066 37.7467 1.48026 24.8355 1.48026C23.4375 1.48026 22.1217 1.5625 20.8059 1.80921C18.75 0.657895 16.4474 0 13.898 0C6.25 0 0 6.25 0 13.9803C0 16.5296 0.657895 18.8322 1.80921 20.8882C1.5625 22.2039 1.48026 23.602 1.48026 25C1.48026 37.9934 11.9243 48.5197 24.8355 48.5197C26.2336 48.5197 27.5493 48.4375 28.8651 48.1908C30.9211 49.3421 33.2237 50 35.773 50C43.4211 50 49.6711 43.75 49.6711 36.0197C49.6711 33.5526 49.0132 31.1678 47.8619 29.1118Z" fill="#00B0F0"/>
+                        </g>
+                        <defs>
+                        <clipPath id="clip0_23_95">
+                        <rect width="50" height="50" fill="white"/>
+                        </clipPath>
+                        </defs>
+                        </svg>
+                        
+                    <%=__("Skype")%>
+                </a>
+            </li>
+            <li>
+                <a  data-id="theforkUrl" class="thefork">
+                    <svg width="20px" height="20px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+                        <g clip-path="url(#clip0_23_37)">
+                            <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.58172 0 0 3.58172 0 8V42C0 46.4183 3.58172 50 8 50H42C46.4183 50 50 46.4183 50 42V8C50 3.58172 46.4183 0 42 0H8ZM25.4035 20.8704H23.9171V5H25.4035V20.8704ZM27.6047 5.21953L29.0912 5.41963V20.3696L27.6047 20.6554V5.21953ZM20.2383 5.39653L21.7247 5.19643V20.6037L20.2383 20.3179V5.39653ZM23.0362 40.2233C23.0362 40.7378 22.979 42.3385 22.9218 43.3105C22.8647 44.2825 22.8075 45.0543 22.7503 44.997C13.889 44.1395 7 37.079 7 28.5321C7 21.6431 11.3735 15.8118 17.7194 13.3249C17.605 14.7255 17.4335 17.4125 17.3764 18.4702C17.3192 19.5278 17.2906 21.4144 17.5765 23.0438C17.8337 24.473 18.4626 25.9308 19.12 27.16C19.3078 27.5042 19.4998 27.8484 19.6879 28.1855C20.1871 29.0804 20.6588 29.9258 20.9495 30.5903C21.0205 30.7525 21.0869 30.9022 21.1499 31.0439C21.4418 31.701 21.6576 32.1868 21.8928 32.9628C22.2072 33.906 22.4073 34.6493 22.4931 35.278C22.5201 35.4271 22.5521 35.5745 22.5865 35.7336C22.6974 36.2461 22.8346 36.8798 22.9218 38.0795C23.0431 39.438 23.0409 39.6641 23.0374 40.0159C23.0368 40.0786 23.0362 40.1454 23.0362 40.2233ZM26.2635 40.2233C26.2635 40.7378 26.3207 42.3385 26.3779 43.3105C26.4351 44.2825 26.4922 45.0543 26.5494 44.997C35.4108 44.1395 42.2998 37.079 42.2998 28.5321C42.2998 21.6431 37.9263 15.8118 31.5803 13.3249C31.6948 14.7255 31.8663 17.4125 31.9233 18.4702C31.9805 19.5278 32.009 21.4144 31.7233 23.0438C31.466 24.473 30.8373 25.9308 30.1798 27.16C29.9922 27.5039 29.8003 27.8478 29.6123 28.1846C29.1129 29.0799 28.641 29.9256 28.3502 30.5903C28.2793 30.7525 28.2128 30.9021 28.1498 31.0438C27.8579 31.701 27.6421 32.1868 27.4069 32.9628C27.0925 33.906 26.8924 34.6493 26.8067 35.278C26.7796 35.4271 26.7476 35.5745 26.7132 35.7336C26.6023 36.246 26.4651 36.8798 26.3779 38.0795C26.2566 39.4379 26.2588 39.6641 26.2623 40.0159C26.2629 40.0786 26.2635 40.1454 26.2635 40.2233Z" fill="#558F40"/>
+                            </g>
+                            <defs>
+                            <clipPath id="clip0_23_37">
+                            <rect width="50" height="50" fill="white"/>
+                            </clipPath>
+                            </defs>
+                    </svg>
+                        
+                    <%=__("Thefork")%>
+                </a>
+            </li>
+            <li>
+                <a  data-id="tiktokUrl" class="tiktok">
+                    <svg width="20px" height="20px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+                        <g clip-path="url(#clip0_23_35)">
+                            <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.58172 0 0 3.58172 0 8V42C0 46.4183 3.58172 50 8 50H42C46.4183 50 50 46.4183 50 42V8C50 3.58172 46.4183 0 42 0H8ZM41.0201 21.3758H42.094V14.396C36.7248 14.1275 32.698 10.1007 32.1611 5H26.5235V32.9195C26.5235 36.4094 23.5705 39.094 20.0805 39.094C16.5906 39.094 13.906 36.4094 13.906 32.9195C13.906 29.4295 16.5906 26.745 20.0805 26.745H20.8859V20.8389H20.0805C13.3691 20.8389 8 26.2081 8 32.9195C8 39.6309 13.3691 45 20.0805 45C26.7919 45 32.1611 39.6309 32.1611 32.9195V16.5436C34.0403 19.4966 37.5302 21.3758 41.0201 21.3758Z" fill="#000000"/>
+                            </g>
+                            <defs>
+                            <clipPath id="clip0_23_35">
+                            <rect width="50" height="50" fill="white"/>
+                            </clipPath>
+                            </defs>
+                    </svg>
+                        
+                    <%=__("Tiktok")%>
+                </a>
+            </li>
+            <li>
+                <a  data-id="tripadvisorUrl" class="tripadvisor">
+                    <svg width="20px" height="20px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+                        <g clip-path="url(#clip0_23_43)">
+                            <path d="M13.75 26C13.75 26.75 14.25 27.25 15 27.25C15.75 27.25 16.25 26.75 16.25 26C16.25 25.25 15.75 24.75 15 24.75C14.25 24.75 13.75 25.25 13.75 26Z" fill="#32D99C"/>
+                            <path fill-rule="evenodd" clip-rule="evenodd" d="M8.25 26C8.25 29.75 11.25 32.75 15 32.75C18.75 32.75 21.75 29.75 21.75 26C21.75 22.25 18.75 19.25 15 19.25C11.25 19.25 8.25 22.25 8.25 26ZM10.75 26C10.75 23.75 12.75 21.75 15 21.75C17.25 21.75 19.25 23.75 19.25 26C19.25 28.25 17.25 30.25 15 30.25C12.75 30.25 10.75 28.25 10.75 26Z" fill="#32D99C"/>
+                            <path d="M17 16.25C21.5 17 25 21.25 25 26C25 21.25 28.5 17.25 33 16.25C30.5 15.5 28 15 25 15C22 15 19.5 15.5 17 16.25Z" fill="#32D99C"/>
+                            <path d="M33.75 26C33.75 26.75 34.25 27.25 35 27.25C35.75 27.25 36.25 26.75 36.25 26C36.25 25.25 35.75 24.75 35 24.75C34.25 24.75 33.75 25.25 33.75 26Z" fill="#32D99C"/>
+                            <path fill-rule="evenodd" clip-rule="evenodd" d="M28.25 26C28.25 29.75 31.25 32.75 35 32.75C38.75 32.75 41.75 29.75 41.75 26C41.75 22.25 38.75 19.25 35 19.25C31.25 19.25 28.25 22.25 28.25 26ZM30.75 26C30.75 23.75 32.75 21.75 35 21.75C37.25 21.75 39.25 23.75 39.25 26C39.25 28.25 37.25 30.25 35 30.25C32.75 30.25 30.75 28.25 30.75 26Z" fill="#32D99C"/>
+                            <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.58172 0 0 3.58172 0 8V42C0 46.4183 3.58172 50 8 50H42C46.4183 50 50 46.4183 50 42V8C50 3.58172 46.4183 0 42 0H8ZM39.25 16H45C44.25 16.75 43.25 18.75 43 20C44.25 21.75 45 23.75 45 26C45 31.5 40.5 36 35 36C32.5 36 30 35 28.25 33.25L25 38L21.75 33.25C20 35 17.75 36 15 36C9.5 36 5 31.5 5 26C5 23.75 5.75 21.75 7 20C6.75 18.75 5.75 16.75 5 16H10.75C14.5 13.25 19.5 12 25 12C30.5 12 35.5 13.25 39.25 16Z" fill="#32D99C"/>
+                            </g>
+                            <defs>
+                            <clipPath id="clip0_23_43">
+                            <rect width="50" height="50" fill="white"/>
+                            </clipPath>
+                            </defs>
+                    </svg>
+                        
+                    <%=__("Tripadvisor")%>
+                </a>
+            </li>
+            <li>
+                <a  data-id="wazeUrl" class="waze">
+                    <svg width="20px" height="20px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+                        <g clip-path="url(#clip0_23_39)">
+                            <path d="M15.522 39.3771C15.522 40.2174 16.2026 40.9065 17.0513 40.9065C17.9 40.9065 18.5807 40.2174 18.5807 39.3771C18.5807 38.5368 17.9 37.8477 17.0513 37.8477C16.211 37.8477 15.522 38.5284 15.522 39.3771Z" fill="#32CDFD"/>
+                            <path d="M30.2778 39.3771C30.2778 40.2174 30.9584 40.9065 31.8072 40.9065C32.6559 40.9065 33.3449 40.2174 33.3365 39.3771C33.3365 38.5368 32.6475 37.8477 31.8072 37.8477C30.9668 37.8477 30.2778 38.5284 30.2778 39.3771Z" fill="#32CDFD"/>
+                            <path fill-rule="evenodd" clip-rule="evenodd" d="M31.8072 35.1335C32.7819 35.1335 33.6894 35.4697 34.4037 36.0243C39.7313 33.6126 43.1177 28.68 43.1177 23.1843C43.1177 15.2854 36.0843 8.86549 27.446 8.86549C18.8076 8.86549 11.7826 15.2854 11.7826 23.1843C11.7826 23.4571 11.7903 23.726 11.7979 23.9911C11.8015 24.118 11.8051 24.244 11.8078 24.3692C11.8582 25.8817 11.8918 27.3019 11.2028 28.5119C10.4969 29.7388 9.16924 30.537 7.02645 31.016C7.95079 33.2092 10.4549 34.8898 14.4632 35.9991C15.1774 35.4529 16.0766 35.1251 17.0429 35.1251C18.5723 35.1251 19.9084 35.9318 20.6563 37.1419C23.32 37.436 25.7737 37.5032 27.446 37.5032C27.6308 37.5032 27.8157 37.5032 28.0006 37.4948C28.698 36.0999 30.1433 35.1335 31.8072 35.1335ZM24.488 19.6046C24.488 20.5885 23.6904 21.3861 22.7066 21.3861C21.7227 21.3861 20.9251 20.5885 20.9251 19.6046C20.9251 18.6208 21.7227 17.8232 22.7066 17.8232C23.6904 17.8232 24.488 18.6208 24.488 19.6046ZM33.3281 24.5625C33.3281 24.0499 33.7483 23.6297 34.2609 23.6297C34.7734 23.6297 35.1936 24.0415 35.1936 24.5625C35.1936 26.1422 34.3869 27.6128 32.9164 28.6968C31.5299 29.7135 29.7148 30.2766 27.7821 30.2766C25.8494 30.2766 24.0343 29.7219 22.6478 28.6968C21.1773 27.6128 20.3706 26.1422 20.3706 24.5625C20.3706 24.0499 20.7907 23.6297 21.3033 23.6297C21.8159 23.6297 22.236 24.0499 22.236 24.5625C22.236 25.5372 22.7822 26.4699 23.757 27.1926C24.8242 27.9825 26.2527 28.4111 27.7821 28.4111C29.3114 28.4111 30.74 27.9741 31.8072 27.1926C32.7903 26.4699 33.3281 25.5372 33.3281 24.5625ZM34.2608 19.6046C34.2608 20.5885 33.4632 21.3861 32.4794 21.3861C31.4955 21.3861 30.6979 20.5885 30.6979 19.6046C30.6979 18.6208 31.4955 17.8232 32.4794 17.8232C33.4632 17.8232 34.2608 18.6208 34.2608 19.6046Z" fill="#32CDFD"/>
+                            <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.58172 0 0 3.58172 0 8V42C0 46.4183 3.58172 50 8 50H42C46.4183 50 50 46.4183 50 42V8C50 3.58172 46.4183 0 42 0H8ZM27.4544 7C32.1181 7 36.5129 8.67221 39.8237 11.7141C43.1513 14.7729 44.9832 18.8484 45 23.2011C45 26.4784 43.9496 29.6295 41.9497 32.3269C40.3279 34.5201 38.1599 36.2932 35.639 37.52C35.9163 38.083 36.0675 38.7132 36.0675 39.3771C36.0675 41.7216 34.16 43.6207 31.824 43.6207C29.4795 43.6207 27.5804 41.7132 27.5804 39.3771H27.4628C25.9166 39.3771 23.715 39.3183 21.2949 39.0914C21.2949 39.1376 21.297 39.186 21.2991 39.2342C21.3012 39.2826 21.3033 39.3309 21.3033 39.3771C21.3033 41.7216 19.4042 43.6207 17.0597 43.6207C14.7153 43.6207 12.8162 41.7132 12.8162 39.3771C12.8162 38.7385 12.959 38.1334 13.2111 37.5872C8.69867 36.1839 5.95926 33.9991 5.06013 31.0748C4.94249 30.6967 5.00131 30.2766 5.20298 29.9404C5.41306 29.5959 5.75759 29.3606 6.15253 29.285C10.06 28.5539 10.0264 27.2598 9.94232 24.4196C9.92552 24.0247 9.91711 23.6129 9.91711 23.1843C9.91711 18.8484 11.7574 14.7729 15.085 11.7141C18.3874 8.67221 22.7822 7 27.4544 7Z" fill="#32CDFD"/>
+                            </g>
+                            <defs>
+                            <clipPath id="clip0_23_39">
+                            <rect width="50" height="50" fill="white"/>
+                            </clipPath>
+                            </defs>
+                    </svg>
+                        
+                    <%=__("Waze")%>
+                </a>
+            </li>
+            <li>
+                <a  data-id="whatsappUrl" class="whatsapp">
+                    <svg width="20px" height="20px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+                        <g clip-path="url(#clip0_23_45)">
+                            <path fill-rule="evenodd" clip-rule="evenodd" d="M16.6585 39.1785C19.1981 40.6866 22.1127 41.4837 25.0899 41.4837C34.2166 41.4837 41.6399 34.0603 41.6478 24.9336C41.6478 20.5108 39.9287 16.346 36.8031 13.2204C33.6696 10.0947 29.5126 8.36784 25.1055 8.36784C15.9709 8.36784 8.53975 15.7912 8.53975 24.9179C8.53975 28.0436 9.41492 31.091 11.0715 33.7243L11.4622 34.3495L9.79 40.46L16.0568 38.8191L16.6585 39.1785ZM30.7706 27.4653C31.2239 27.6294 33.6696 28.8328 34.1697 29.0828C34.2654 29.1307 34.3549 29.1739 34.4379 29.2141C34.7886 29.3839 35.0249 29.4983 35.1387 29.6689C35.2637 29.8798 35.2637 30.8722 34.8496 32.0365C34.4354 33.2008 32.4428 34.2635 31.4895 34.4042C30.63 34.537 29.5517 34.5839 28.3561 34.2088C27.6372 33.9822 26.7074 33.6775 25.5196 33.1617C20.8451 31.1427 17.6909 26.6102 17.1023 25.7643C17.0621 25.7066 17.0339 25.6661 17.018 25.6446L17.01 25.634C16.7326 25.2615 14.9863 22.9166 14.9863 20.4952C14.9863 18.2181 16.1039 17.0179 16.6239 16.4596C16.6624 16.4183 16.6976 16.3804 16.7288 16.346C17.1899 15.8459 17.729 15.7208 18.0572 15.7208C18.3854 15.7208 18.7214 15.7208 19.0105 15.7365C19.0466 15.7383 19.0841 15.7381 19.1228 15.7379C19.4114 15.7361 19.7697 15.7339 20.1279 16.5882C20.2537 16.8919 20.4328 17.329 20.6248 17.7974C21.0648 18.8714 21.5724 20.1101 21.6595 20.2842C21.7845 20.5343 21.8627 20.8234 21.6986 21.1594C21.6627 21.2278 21.6309 21.2912 21.6008 21.3512C21.4866 21.579 21.3964 21.7589 21.1985 21.9877C21.129 22.0702 21.0576 22.157 20.986 22.2442C20.8 22.4705 20.6119 22.6992 20.4483 22.8629C20.1983 23.1129 19.9404 23.3786 20.2295 23.8787C20.5186 24.3788 21.5188 26.0041 22.9957 27.3247C24.5841 28.7431 25.9646 29.3413 26.6663 29.6454C26.8035 29.7048 26.9147 29.753 26.9965 29.7939C27.4966 30.0439 27.7857 29.9971 28.0748 29.6689C28.3639 29.3329 29.3251 28.2155 29.6532 27.7154C29.9814 27.2153 30.3174 27.3012 30.7706 27.4653Z" fill="#25D366"/>
+                            <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.58172 0 0 3.58172 0 8V42C0 46.4183 3.58172 50 8 50H42C46.4183 50 50 46.4183 50 42V8C50 3.58172 46.4183 0 42 0H8ZM25.0899 5C30.419 5.00781 35.42 7.07853 39.1785 10.8449C42.9371 14.6112 45.0078 19.6122 45 24.9414C45 35.9201 36.0607 44.8515 25.082 44.8515H25.0742C21.7454 44.8515 18.4636 44.0154 15.5567 42.4292L5 45.1953L7.82086 34.873C6.07833 31.8568 5.16409 28.4264 5.16409 24.9179C5.17191 13.9392 14.1112 5 25.0899 5Z" fill="#25D366"/>
+                            </g>
+                            <defs>
+                            <clipPath id="clip0_23_45">
+                            <rect width="50" height="50" fill="white"/>
+                            </clipPath>
+                            </defs>
+                    </svg>
+                        
+                    <%=__("Whatsapp")%>
+                </a>
+            </li>
+            <li>
+                <a  data-id="slideshareUrl" class="slideshare">
+                    <svg width="20px" height="20px" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+                        <path d="M10.9746 5.00104H39.2425C40.1547 5.00608 41.0282 5.37071 41.6734 6.01582C42.3185 6.66092 42.683 7.53442 42.6882 8.44672V24.4094C42.9215 24.2546 43.1521 24.095 43.3799 23.9305C44.5949 23.0756 45.4649 24.2755 44.73 25.3554C42.4447 27.9415 39.5385 29.9031 36.2853 31.0551C36.8081 32.7207 37.0809 34.4543 37.0953 36.1998C37.0953 40.8196 34.5905 45.1994 29.8358 45.1994C29.261 45.236 28.6853 45.1481 28.1475 44.9419C27.6098 44.7357 27.1229 44.4161 26.72 44.0047C26.3171 43.5932 26.0077 43.0997 25.813 42.5579C25.6181 42.0159 25.5423 41.4385 25.591 40.8647V34.22C26.1832 34.3426 26.786 34.4079 27.3908 34.415C28.6964 34.4499 29.9798 34.0713 31.0572 33.3331C32.1347 32.595 32.9514 31.5353 33.3906 30.3053C34.1555 28.1753 32.7156 27.6203 31.6506 29.2552C30.0007 31.8351 27.8108 31.8351 25.501 30.3353V30.0652C25.501 27.8103 26.9179 27.8197 28.3332 27.8291C28.4242 27.8297 28.5152 27.8303 28.6058 27.8303C32.979 28.3092 37.3708 27.4196 41.1882 25.3177V8.45307C41.1848 7.93639 40.9781 7.4418 40.6127 7.07642C40.2474 6.71103 39.7528 6.5043 39.2361 6.50095H10.9441C10.6825 6.49372 10.422 6.53855 10.178 6.63337C9.93407 6.72819 9.71148 6.87081 9.5234 7.05284C9.3353 7.23487 9.18547 7.45264 9.0827 7.69337C8.98016 7.93357 8.92655 8.19179 8.92497 8.45293V26.2848H7.42505V8.44699C7.42742 7.98543 7.52201 7.529 7.70321 7.1045C7.88443 6.67999 8.14863 6.29597 8.48032 5.97499C8.812 5.65401 9.20447 5.40253 9.63468 5.23533C10.0615 5.06945 10.5169 4.98984 10.9746 5.00104Z" fill="#026C97"/>
+                        <path d="M30.9452 26.0006C31.5678 26.0226 32.1885 25.9216 32.772 25.7037C33.3556 25.4857 33.8903 25.1548 34.3458 24.7298C34.8014 24.3049 35.1688 23.7944 35.4268 23.2275C35.6849 22.6605 35.8286 22.0484 35.85 21.4257C35.7838 20.183 35.2332 19.0158 34.3162 18.1745C33.3992 17.3333 32.189 16.885 30.9452 16.926C29.6891 16.8855 28.468 17.344 27.5487 18.2013C26.6297 19.0585 26.0874 20.2448 26.0405 21.5008C26.1065 22.7435 26.6572 23.9108 27.5741 24.752C28.4911 25.5932 29.7015 26.0416 30.9452 26.0006Z" fill="#026C97"/>
+                        <path fill-rule="evenodd" clip-rule="evenodd" d="M24.4651 21.3657C24.4497 21.9954 24.3092 22.6156 24.0521 23.1907C23.7949 23.7656 23.426 24.2837 22.9669 24.7149C22.5078 25.1462 21.9676 25.4818 21.3776 25.7025C20.7877 25.9232 20.1597 26.0245 19.5304 26.0005C18.9079 26.0225 18.2871 25.9215 17.7036 25.7036C17.1201 25.4856 16.5853 25.1547 16.1297 24.7297C15.6742 24.3048 15.3069 23.7943 15.0488 23.2274C14.7907 22.6604 14.6469 22.0483 14.6257 21.4257C14.6917 20.1829 15.2423 19.0157 16.1593 18.1744C17.0763 17.3332 18.2866 16.8849 19.5304 16.9259C20.7694 16.8763 21.9789 17.3128 22.9008 18.1422C23.8226 18.9716 24.384 20.1282 24.4651 21.3657ZM21.0303 27.8303C15.9007 28.2858 10.7761 26.9037 6.5711 23.9305C5.35617 23.0756 4.51621 24.2755 5.31117 25.3405C7.58693 27.9304 10.4894 29.893 13.7407 31.0402C10.9059 40.6996 16.0956 45.1994 20.1903 45.1844C20.7667 45.2278 21.3455 45.1454 21.887 44.943C22.4284 44.7407 22.9193 44.4231 23.3258 44.0122C23.7324 43.6014 24.0449 43.1071 24.2416 42.5637C24.4383 42.0202 24.5146 41.4404 24.4651 40.8647C24.4651 37.9848 24.4651 33.2 24.4651 33.2C25.4173 33.4838 26.3985 33.66 27.39 33.725C28.6617 33.8257 29.9329 33.5235 31.0233 32.8613C32.1135 32.1989 32.9677 31.2102 33.4646 30.0352C34.2896 28.0553 32.8797 27.4854 31.7847 29.0003C29.6698 31.9101 26.955 31.3101 24.1502 28.6253C23.3851 27.9204 22.9652 27.7254 21.0003 27.8303H21.0303Z" fill="#D8711C"/>                        
+                    </svg>
+                        
+                    <%=__("Slideshare")%>
+                </a>
+            </li>
         </ul>
         </div>
     </div>
Index: src/js/JEditor/ParamsPanel/Views/ListSocialNetworks.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/ListSocialNetworks.js	(révision 10417)
+++ src/js/JEditor/ParamsPanel/Views/ListSocialNetworks.js	(révision 10418)
@@ -22,6 +22,66 @@
             
             this.listenTo(this.model, Events.BackboneEvents.CHANGE, this.onChange);
         },
+        ___onInputChange: function ($target) {
+            var field = $target.attr("name");
+            var shouldSave = $target.data("autosave") == true;
+            var output, error;
+            var $outputTarget = $target;
+            var pos = !$target.data("error-position") ? "before" : $target.data("error-position");
+            var filter = $target.data("filter");
+            filter = _.isFunction(this[filter])?this[filter]:function (val) {
+                return val?val:null;
+            };
+            var value =filter($target.val());
+            //onError
+            function onInvalid(model, validationError, options) {
+                if (validationError.field === field) {
+                    error = validationError;
+                }
+            }
+            //listen to errors
+            this.listenToOnce(this.model, Events.BackboneEvents.INVALID, onInvalid);
+            if ($target.attr("type") === "checkbox") {
+                this.model[field] = $target.data("negate") ? !$target.is(":checked") : $target.is(":checked");
+            }
+            else {
+                if(field.includes("_")){
+                    var arraySpliteField=field.split('_');
+                    field =arraySpliteField[0];
+                    var keyField =arraySpliteField[1];
+                    var listeSocialNetwork =this.model.get("SocialNetworksUrl");
+                    listeSocialNetwork[keyField].url=value;
+                    this.model.KeySocialNetworks=keyField;
+                    this.model.set("SocialNetworksUrl", listeSocialNetwork);
+                }
+                
+                this.model.set(field, value, {validate: true});
+            }
+            //error_positioning
+            if (pos !== "after" && pos !== "before") {
+                if (this.$(pos).length > 0) {
+                    $outputTarget = this.$(pos);
+                    pos = "append";
+                    $outputTarget.children(".input-error." + field).remove();
+                }
+                else
+                    pos = "before";
+            } else {
+                $outputTarget.siblings(".input-error." + field).remove();
+            }
+            if (error) {
+                output = "<span class=\"input-error " + field + "\"><span class=\"icon icon-warning\"></span>";
+                if (error.message)
+                    output += "<span class=\"text\">" + error.message + "</span>";
+                output += "</span>";
+                $outputTarget[pos](output);
+                $target.addClass("error");
+            }
+            this.stopListening(this.model, Events.BackboneEvents.INVALID, onInvalid);
+            if (shouldSave && !error)
+                this.model.save();
+
+        },
         confirm: function(params) {
             this.app.messageDelegate.set(new Confirm(params));
         },
Index: src/js/JEditor/ParamsPanel/Views/SocialNetworks.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/SocialNetworks.js	(révision 10417)
+++ src/js/JEditor/ParamsPanel/Views/SocialNetworks.js	(révision 10418)
@@ -21,6 +21,7 @@
             this.ChildViews={};
             
         },
+        
         DeleteOne :function(key){
             this.model.get("SocialNetworksUrl").splice(key,1)
             console.log(this.model.get("SocialNetworksUrl"));
Index: src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 10417)
+++ src/js/JEditor/ParamsPanel/nls/fr-fr/i18n.js	(révision 10418)
@@ -17,6 +17,7 @@
         "Invalid_ItunesAppId":"L'identifant de l'application ne semble pas valide",
         "Invalid_PVYoutubeId":"L'identifant de la vidéo youtube ne semble pas valide",
         "socialNetworksDesc":"Définissez les adresses de vos pages sur les réseaux sociaux.",
+        "Skype":    "Skype",
         "Facebook":"Facebook",
         "Twitter":"Twitter",
         "Pinterest":"Pinterest",
Index: src/js/JEditor/ParamsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 10417)
+++ src/js/JEditor/ParamsPanel/nls/i18n.js	(révision 10418)
@@ -1,6 +1,13 @@
 define({
     "root": {
         "socialNetworks": "Social networks",
+        "Invalid_slideshareUrl":"This is not a valid url",
+        "Invalid_whatsappUrl":"This is not a valid url",
+        "Invalid_wazeUrl":"This is not a valid url",
+        "Invalid_tripadvisorUrl":"This is not a valid url",
+        "Invalid_tiktokUrl":"This is not a valid url",
+        "Invalid_skypeUrl":"This is not a valid url",
+        "Invalid_theforkUrl":"This is not a valid url",
         "Invalid_facebookUrl":"This is not a valid url",
         "Invalid_twitterUrl":"This is not a valid url",
         "Invalid_googleUrl":"This is not a valid url",
@@ -18,6 +25,13 @@
         "Invalid_ItunesAppId":"This is not a valid app id",
         "Invalid_PVYoutubeId":"This is not a valid youtube video id",
         "socialNetworksDesc":"Enabled or disabled the networks you want to display on your site",
+        "Thefork"   :  "TheFork",
+        "Tiktok"    :   "TikTok",
+        "Tripadvisor"   :   "Tripadvisor",
+        "Waze"      :   "Waze",
+        "Whatsapp"  :   "Whatsapp",
+        'Slideshare'    :   "Slideshare",
+        "Skype":    "Skype",
         "Facebook":"Facebook",
         "Twitter":"Twitter",
         "Pinterest":"Pinterest",
