Revision: r14044
Date: 2025-04-03 10:04:27 +0300 (lkm 03 Apr 2025) 
Author: rrakotoarinelina 

## Commit message
Whislist IDEO3.2 : Nom des icons + filtre recherche (comme les pages) - correction retour

## Files changed

## Full metadata
------------------------------------------------------------------------
r14044 | rrakotoarinelina | 2025-04-03 10:04:27 +0300 (lkm 03 Apr 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/Templates/selectIcon.html
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/Templates/selectLabel.html
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/Templates/selectSocial.html
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/Views/SelectIconView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/Views/SelectLabelView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/Views/SelectSocialView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Svgs/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/button_block/styleOptions.less
   M /branches/ideo3_v2/integration/src/less/imports/filePanel.less
   M /branches/ideo3_v2/integration/src/less/main.less

Whislist IDEO3.2 : Nom des icons + filtre recherche (comme les pages) - correction retour
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Svgs/Templates/selectIcon.html
===================================================================
--- src/js/JEditor/Commons/Svgs/Templates/selectIcon.html	(révision 14043)
+++ src/js/JEditor/Commons/Svgs/Templates/selectIcon.html	(révision 14044)
@@ -1,7 +1,7 @@
 <div class="container-icon option-content">
     <div id="form-search-svg">
         <span class="search-svg-selector">
-            <input type="text" class="svg-search" placeholder="Recherche ..." value="">
+            <input type="text" class="svg-search" placeholder="<%=__("search")%> ..." value="">
         </span>
     </div>
     <div class="grid-container-icon">
@@ -12,6 +12,10 @@
                     <span class="wrapper-icon">
                         <%= svg.content%>
                     </span>
+                     <span class="svg-name">
+                        <%= svg.name.replace('.svg', '')%>
+                    </span>
+                        
             </div>
             <%}%>
     </div>
Index: src/js/JEditor/Commons/Svgs/Templates/selectLabel.html
===================================================================
--- src/js/JEditor/Commons/Svgs/Templates/selectLabel.html	(révision 14043)
+++ src/js/JEditor/Commons/Svgs/Templates/selectLabel.html	(révision 14044)
@@ -9,7 +9,7 @@
    </header>
     <div id="form-search-svg">
         <span class="search-svg-selector">
-            <input type="text" class="svg-search" placeholder="Recherche ..." value="">
+            <input type="text" class="svg-search" placeholder="<%=__("search")%> ..." value="">
         </span>
     </div>
     <div class="grid-container-icon content">
@@ -22,6 +22,9 @@
                     <span class="wrapper-icon">
                         <%= file.content%>
                     </span>
+                    <span class="svg-name">
+                        <%= file.name.replace('.svg', '')%>
+                    </span>
                 </div>
             <%} else {%>
                 <div  class="oneFile <%=selected === file.name ? 'selected-icon':''%>" style="background-image: url(<%=file.url%>);" data-url="<%=file.url%>" >
Index: src/js/JEditor/Commons/Svgs/Templates/selectSocial.html
===================================================================
--- src/js/JEditor/Commons/Svgs/Templates/selectSocial.html	(révision 14043)
+++ src/js/JEditor/Commons/Svgs/Templates/selectSocial.html	(révision 14044)
@@ -1,3 +1,8 @@
+<div id="form-search-svg" class="search-social-svg">
+    <span class="search-svg-selector">
+        <input type="text" class="svg-search" placeholder="<%=__("search")%> ..." value="">
+    </span>
+</div>
 <div class="container-icon option-content baseFile">
     <div class="grid-container-icon content">
         <%for(i=0;i<content.length;i++){
@@ -7,6 +12,9 @@
                 <span class="wrapper-icon">
                     <%= file.content%>
                 </span>
+                <span class="svg-name">
+                    <%= file.name.replace('.svg', '')%>
+                </span>
             </div>
             <%}%>
     </div>
Index: src/js/JEditor/Commons/Svgs/Views/SelectIconView.js
===================================================================
--- src/js/JEditor/Commons/Svgs/Views/SelectIconView.js	(révision 14043)
+++ src/js/JEditor/Commons/Svgs/Views/SelectIconView.js	(révision 14044)
@@ -35,6 +35,8 @@
                 width: 720,
                 height: 600
             }, options);
+            this.on('open', this.onOpen);
+            this.on('close', this.onClose);
             return DialogView.call(this, opts);
         },
         initialize: function(options) {
@@ -56,6 +58,12 @@
             this.delegateEvents();
             return this;
         },
+        onOpen: function() {
+            $('body').css('overflow', 'hidden');
+        },
+        onClose: function() {
+            $('body').css('overflow', '');
+        },
         onCancel: function() {
             $('.box-icon').css("background-color", "transparent");
             this.$el.dialog('close');
Index: src/js/JEditor/Commons/Svgs/Views/SelectLabelView.js
===================================================================
--- src/js/JEditor/Commons/Svgs/Views/SelectLabelView.js	(révision 14043)
+++ src/js/JEditor/Commons/Svgs/Views/SelectLabelView.js	(révision 14044)
@@ -38,7 +38,8 @@
                 }
             ]
             DialogView.call(this, options);
-            this.on('open close', this.onToggle);
+            this.on('open', this.onOpen);
+            this.on('close', this.onClose);
         },
         initialize: function(options) {
             this._super();
@@ -108,6 +109,12 @@
                 $target.toggleClass('selected');
             }
         },
+        onOpen: function() {
+            $('body').css('overflow', 'hidden');
+        },
+        onClose: function() {
+            $('body').css('overflow', '');
+        },
     
     });
     return SelectLabelView;
Index: src/js/JEditor/Commons/Svgs/Views/SelectSocialView.js
===================================================================
--- src/js/JEditor/Commons/Svgs/Views/SelectSocialView.js	(révision 14043)
+++ src/js/JEditor/Commons/Svgs/Views/SelectSocialView.js	(révision 14044)
@@ -13,6 +13,7 @@
         className: 'socialSvgSelector',
         events: {
             'click .oneFile': 'onSelect',
+            'input .svg-search' : 'onSvgSearch'
         },
         currentList: [],
         currentType : 'social',
@@ -35,6 +36,8 @@
                 height: 600,
                 allowMultipleSelect: false
             }, options);
+            this.on('open', this.onOpen);
+            this.on('close', this.onClose);
             return DialogView.call(this, opts);
         },
         initialize: function(options) {
@@ -71,6 +74,23 @@
             $target.toggleClass('selected');
             this.trigger(Events.ListViewEvents.SELECT,this.selected, this.selected?1:0,this.svgCollection);
         },
+        onSvgSearch: function(e){
+            var searchTerm = value = e.target.value.toLowerCase();
+                $('.grid-container-icon .oneFile').each(function() {
+                    var iconName = $(this).data('name').toLowerCase();
+                    if (iconName.includes(searchTerm)) {
+                        $(this).show();
+                    } else {
+                        $(this).hide();
+                    }
+                });
+        },
+        onOpen: function() {
+            $('body').css('overflow', 'hidden');
+        },
+        onClose: function() {
+            $('body').css('overflow', '');
+        },
     
     });
     return SelectSocialView;
Index: src/js/JEditor/Commons/Svgs/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/Commons/Svgs/nls/fr-ca/i18n.js	(révision 14043)
+++ src/js/JEditor/Commons/Svgs/nls/fr-ca/i18n.js	(révision 14044)
@@ -3,5 +3,6 @@
     "cancel": "Annuler",
     "choose": "Choisir",
     "okay": "Ok",
+    "search": "Rechercher",
     "browseLabelTitle":"Parcourir la base d'images",
 });
\ No newline at end of file
Index: src/js/JEditor/Commons/Svgs/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/Commons/Svgs/nls/fr-fr/i18n.js	(révision 14043)
+++ src/js/JEditor/Commons/Svgs/nls/fr-fr/i18n.js	(révision 14044)
@@ -3,5 +3,6 @@
     "cancel": "Annuler",
     "choose": "Choisir",
     "okay": "Ok",
+    "search": "Rechercher",
     "browseLabelTitle":"Parcourir la base d'images",
 });
\ No newline at end of file
Index: src/js/JEditor/Commons/Svgs/nls/i18n.js
===================================================================
--- src/js/JEditor/Commons/Svgs/nls/i18n.js	(révision 14043)
+++ src/js/JEditor/Commons/Svgs/nls/i18n.js	(révision 14044)
@@ -4,6 +4,7 @@
         "cancel": "Cancel",
         "choose": "Choose",
         "okay": "Ok",
+        "search": "Search",
         "browseLabelTitle":"Browse the image database",
     }
     , "fr-fr":true, "fr-ca":true })
\ No newline at end of file
Index: src/less/imports/button_block/styleOptions.less
===================================================================
--- src/less/imports/button_block/styleOptions.less	(révision 14043)
+++ src/less/imports/button_block/styleOptions.less	(révision 14044)
@@ -191,7 +191,7 @@
   }
   .container-icon.option-content .grid-container-icon {
     display: grid;
-    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
+    grid-template-columns: repeat(auto-fit, minmax(125px, 1fr));
     grid-gap: 5px;
   }
   
@@ -200,12 +200,22 @@
     text-align: center;
     border: 1px solid;
     border-color: #f3eaea;
-    font-size: 50px;
+    font-size: 25px;
   }
+  
   .container-icon.option-content .box-icon svg {
     width:  50px;
     height:  50px;
   }
+
+  .container-icon.option-content .svg-name {
+    font-size: 12px;
+    display: block;
+}
+
+    .container-icon.option-content .wrapper-icon {
+        display: block;
+    }
   .container-icon.option-content .selected-icon{
       background-color: #41ffbe;
   }
Index: src/less/imports/filePanel.less
===================================================================
--- src/less/imports/filePanel.less	(révision 14043)
+++ src/less/imports/filePanel.less	(révision 14044)
@@ -2763,7 +2763,7 @@
 
 .baseFile .content {
 
-    padding-top: 30px;
+    // padding-top: 30px;
 
     & .oneFile{
         & span {
@@ -2782,7 +2782,7 @@
         position: relative;
         background-size: cover;
         background-position: center;
-        color: #FFF;
+        // color: #FFF;
         text-align: center;
         padding: 5px;
         text-align: center;
Index: src/less/main.less
===================================================================
--- src/less/main.less	(révision 14043)
+++ src/less/main.less	(révision 14044)
@@ -2964,21 +2964,34 @@
 .card-panel-content {
   margin-top: 22px;
 }
-.page-search, .svg-search {
-  width: 59%;
+.search-common {
   border: 1px solid #ccc;
   border-radius: 4px;
-  background-image: url("data:image/svg+xml,%3Csvg width='16' height='16' viewBox='0 0 512 512' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' fill='%23FFFFFF' class='icon-search01'%3E%3Cpath class='color1' d='M483.3,451.3l-97.7-97.7c29.6-35.7,47.4-81.5,47.4-131.4c0-113.6-92.4-206-206-206c-113.6,0-206,92.4-206,206 c0,113.6,92.4,206,206,206c45,0,86.7-14.5,120.6-39.1l98.9,98.9c5.1,5.1,11.7,7.6,18.4,7.6c6.7,0,13.3-2.5,18.4-7.6 C493.5,478,493.5,461.5,483.3,451.3z M73,222.3c0-84.9,69.1-154,154-154c84.9,0,154,69.1,154,154c0,84.9-69.1,154-154,154 C142.2,376.4,73,307.2,73,222.3z'/%3E%3C/svg%3E");
-  background-position: 10px 6px; 
+  background-position: 10px 6px;
   background-repeat: no-repeat;
   padding: 5px 20px 7px 30px;
   margin-left: 35px;
+}
+
+.page-search {
+  .search-common;
+  width: 59%;
   background-color: #b2b2b2;
   color: #fff;
+  background-image: url("data:image/svg+xml,%3Csvg width='16' height='16' viewBox='0 0 512 512' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' fill='%23FFFFFF' class='icon-search01'%3E%3Cpath class='color1' d='M483.3,451.3l-97.7-97.7c29.6-35.7,47.4-81.5,47.4-131.4c0-113.6-92.4-206-206-206c-113.6,0-206,92.4-206,206 c0,113.6,92.4,206,206,206c45,0,86.7-14.5,120.6-39.1l98.9,98.9c5.1,5.1,11.7,7.6,18.4,7.6c6.7,0,13.3-2.5,18.4-7.6 C493.5,478,493.5,461.5,483.3,451.3z M73,222.3c0-84.9,69.1-154,154-154c84.9,0,154,69.1,154,154c0,84.9-69.1,154-154,154 C142.2,376.4,73,307.2,73,222.3z'/%3E%3C/svg%3E");
 }
-.svg-search{
+
+.svg-search {
+  .search-common;
+  background-color: #fff;
+  color: #000;
   margin-bottom: 10px;
+  background-image: url("data:image/svg+xml,%3Csvg width='16' height='16' viewBox='0 0 512 512' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' fill='%23000000' class='icon-search01'%3E%3Cpath class='color1' d='M483.3,451.3l-97.7-97.7c29.6-35.7,47.4-81.5,47.4-131.4c0-113.6-92.4-206-206-206c-113.6,0-206,92.4-206,206 c0,113.6,92.4,206,206,206c45,0,86.7-14.5,120.6-39.1l98.9,98.9c5.1,5.1,11.7,7.6,18.4,7.6c6.7,0,13.3-2.5,18.4-7.6 C493.5,478,493.5,461.5,483.3,451.3z M73,222.3c0-84.9,69.1-154,154-154c84.9,0,154,69.1,154,154c0,84.9-69.1,154-154,154 C142.2,376.4,73,307.2,73,222.3z'/%3E%3C/svg%3E");
 }
+
+.search-social-svg{
+  margin-top : 20px;
+}
 .page-search::placeholder {
   color: #fff;
 }
