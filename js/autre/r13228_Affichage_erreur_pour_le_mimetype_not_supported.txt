Revision: r13228
Date: 2024-10-17 10:25:50 +0300 (lkm 17 Okt 2024) 
Author: sraz<PERSON><PERSON><PERSON><PERSON> 

## Commit message
Affichage erreur pour le mimetype not supported

## Files changed

## Full metadata
------------------------------------------------------------------------
r13228 | srazanandralisoa | 2024-10-17 10:25:50 +0300 (lkm 17 Okt 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileListView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileManagerUIView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/i18n.js

Affichage erreur pour le mimetype not supported
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/FilePanel/Views/FileListView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileListView.js	(révision 13227)
+++ src/js/JEditor/FilePanel/Views/FileListView.js	(révision 13228)
@@ -93,6 +93,9 @@
         },
         onUploadFail: function(e, data) {
             message = (data.msg.toLowerCase() =='font already uploaded create resource failed')? translate('fontExiste'):data.msg ;
+            if (message.includes("not supported. Create resource failed")) {
+                message = message.replace("not supported. Create resource failed", translate("mimeNotsupporte"));
+            }
             this.error({
                 title: translate("upload"),
                 message: message
@@ -100,6 +103,7 @@
             this.render()
             return false;
         },
+
         toggleSelected: function(e) {
             var $target = $(e.currentTarget);
             $target.toggleClass('selected');
Index: src/js/JEditor/FilePanel/Views/FileManagerUIView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileManagerUIView.js	(révision 13227)
+++ src/js/JEditor/FilePanel/Views/FileManagerUIView.js	(révision 13228)
@@ -243,7 +243,13 @@
                     this.dom[this.cid].filters.removeClass('active'); // reinitialisation des filtres
                     for (var i = 0; i < data.filesData.length; i++) {
                         var file = data.filesData[i].response;
-                        this.collection.create(file);
+                        if (file.includes("not supported. Create resource failed")) {
+                            message = file.replace("not supported. Create resource failed", translate("mimeNotsupporte"));
+                            this.error({
+                                title: translate("upload"),
+                                message: message
+                            });
+                        }else this.collection.create(file);
                     }
                     if (this.options.listView.sortBy)
                         this.options.listView.sortBy(this.sortBy, this.sortOrder === 'asc');
Index: src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 13227)
+++ src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 13228)
@@ -132,4 +132,5 @@
     "addFileExist": "Ajouter des images existantes",
     "browseIconTitle":"Parcourir la base d'icônes",
     "showIcons" : "Importer depuis une base d'images",
+    "mimeNotsupporte": "non pris en charge. Échec de la création d'une ressource",
 });
\ No newline at end of file
Index: src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 13227)
+++ src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 13228)
@@ -136,4 +136,5 @@
     "addFileExist": "Ajouter des images existantes",
     "browseIconTitle":"Parcourir la base d'icônes",
     "showIcons" : "Importer depuis une base d'images",
+    "mimeNotsupporte": "non pris en charge. Échec de la création d'une ressource",
 });
\ No newline at end of file
Index: src/js/JEditor/FilePanel/nls/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/i18n.js	(révision 13227)
+++ src/js/JEditor/FilePanel/nls/i18n.js	(révision 13228)
@@ -138,6 +138,7 @@
         "addFileExist": "Add existing images",
         "browseIconTitle":"Browse the icon database",
         "showIcons" : "Import from image database",
+        "mimeNotsupporte":"not supported. Create resource failed",
     },
     "fr-fr": true, "fr-ca":true
 })
\ No newline at end of file
