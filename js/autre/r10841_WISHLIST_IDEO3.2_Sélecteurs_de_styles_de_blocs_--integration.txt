Revision: r10841
Date: 2023-04-19 14:06:58 +0300 (lrb 19 Apr 2023) 
Author: norajaonarivelo 

## Commit message
WISHLIST IDEO3.2:Sélecteurs de styles de blocs --integration

## Files changed

## Full metadata
------------------------------------------------------------------------
r10841 | norajaonarivelo | 2023-04-19 14:06:58 +0300 (lrb 19 Apr 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Ancestors/Content.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Ancestors/ContentView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/FormBlock.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormBlockView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/TextBlock.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Columns/Models/Column.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Columns/Views/ColumnView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/Models/StylesOption.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/Templates/stylesOption.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/Views/StylesOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Sections/Models/Section.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Sections/Views/SectionView.js
   M /branches/ideo3_v2/integration/src/less/imports/page_panel/module/block-options/advanced-option.less

WISHLIST IDEO3.2:Sélecteurs de styles de blocs --integration
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Ancestors/Content.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Ancestors/Content.js	(révision 10840)
+++ src/js/JEditor/PagePanel/Contents/Ancestors/Content.js	(révision 10841)
@@ -8,6 +8,8 @@
     "JEditor/PagePanel/Contents/Options/Views/ColorsOptionView",
     "JEditor/PagePanel/Contents/Options/Models/EffectsOption",
     "JEditor/PagePanel/Contents/Options/Views/EffectsOptionView",
+    "JEditor/PagePanel/Contents/Options/Models/StylesOption",
+    "JEditor/PagePanel/Contents/Options/Views/StylesOptionView",
     "JEditor/PagePanel/Contents/Options/Models/AdvancedOption",
     "JEditor/PagePanel/Contents/Options/Views/AdvancedOptionView",
     "JEditor/PagePanel/Contents/Options/Models/PopupOption",
@@ -14,7 +16,7 @@
     "JEditor/PagePanel/Contents/Options/Views/PopupOptionView",
     "JEditor/PagePanel/Contents/Options/Models/PopupStyle",
     "JEditor/PagePanel/Contents/Options/Views/PopupStyleView"
-], function(_, Events, NestedModel, OptionsCollection, OptionLib, ColorsOption, ColorsOptionView, EffectsOption, EffectsOptionView,AdvancedOption,AdvancedOptionView, PopupOption, PopupOptionView, PopupStyle, PopupStyleView) {
+], function(_, Events, NestedModel, OptionsCollection, OptionLib, ColorsOption, ColorsOptionView, EffectsOption, EffectsOptionView,StylesOption,StylesOptionView,AdvancedOption,AdvancedOptionView, PopupOption, PopupOptionView, PopupStyle, PopupStyleView) {
     var /**
      * @class Content
      * Définit les modèles contenant des options (blocks, colonnes, sections)
@@ -268,7 +270,7 @@
             }
         }
     });
-    OptionLib.registerModel(ColorsOption, EffectsOption, PopupOption, PopupStyle, AdvancedOption );
-    OptionLib.registerView(ColorsOptionView, EffectsOptionView, PopupOptionView, PopupStyleView, AdvancedOptionView);
+    OptionLib.registerModel(ColorsOption, EffectsOption, StylesOption,PopupOption, PopupStyle, AdvancedOption );
+    OptionLib.registerView(ColorsOptionView, EffectsOptionView,StylesOptionView, PopupOptionView, PopupStyleView, AdvancedOptionView);
     return Content;
-});
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Ancestors/ContentView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Ancestors/ContentView.js	(révision 10840)
+++ src/js/JEditor/PagePanel/Contents/Ancestors/ContentView.js	(révision 10841)
@@ -298,6 +298,19 @@
             renderOptions: function(model, opts) {
                 var options, colors, effects, properties;
                 try {
+                    if(model.optionType ==="styles"){
+                        options =this.model.options.styles.attributes;
+                        var attr= this.attributes();
+                        className=attr.class+" view ";
+                        var keyArray =Object.keys(options);
+                        for(var i=0;i<keyArray.length;i++){
+                            if( keyArray[i] == "colorOption" || keyArray[i] == "shadeWorn"){
+                                className +=  options[keyArray[i]]+" ";
+                            }
+                        }
+                        this.$el.removeClass();
+                        this.$el.addClass(className);
+                    }
                     if (model.optionType === 'effects' || model.optionType === 'colors') {
                         options = this.model.options;
                         colors = {};
@@ -556,4 +569,4 @@
     };
     
     return ContentView;
-});
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/FormBlock.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/FormBlock.js	(révision 10840)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/FormBlock.js	(révision 10841)
@@ -1,7 +1,8 @@
 define([
     "JEditor/PagePanel/Contents/Blocks/Block/Block",
+    "JEditor/PagePanel/Contents/Options/Models/StylesOption",
     "./Models/Form"
-], function(Block, Form) {
+], function(Block,StylesOption, Form) {
     var FormBlock = Block.extend({
         /*
          * pour rajouter le bloc dans la page, vous pouvez utiliser la commande
@@ -19,6 +20,8 @@
             if(!(this.get("form") instanceof Form)){
                 this.set("form",new Form(Form.prototype.parse(this.get("form")||{})));
             }
+            if(!this.options.styles)
+                this.options.add(new StylesOption({colorOption:"surface0"}, {content: this}));    
         }
     });
 
@@ -25,4 +28,4 @@
     FormBlock.ICON = 'icon-mail';
     FormBlock.CATEGORY = "advanced";
     return FormBlock;
-});
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormBlockView.js	(révision 10840)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormBlockView.js	(révision 10841)
@@ -5,6 +5,7 @@
         "JEditor/PagePanel/Contents/Blocks/Block/Views/BlockView",
         "JEditor/Commons/Ancestors/Views/SaveCancelPanel",
         "JEditor/PagePanel/Contents/Options/Views/AdvancedOptionView",
+        "JEditor/PagePanel/Contents/Options/Views/StylesOptionView",
         "./FieldView",
         "./FormOptionView",
         "./FormDestView",
@@ -11,7 +12,7 @@
         "./AdvancedFormView",
         "i18n!../nls/i18n"
     ],
-    function($, formTpl, BlockView, SaveCancelPanel,AdvancedOptionView, FieldView, FormOptionView, FormDestView, AdvancedFormView, translate) {
+    function($, formTpl, BlockView, SaveCancelPanel,AdvancedOptionView, StylesOptionView,FieldView, FormOptionView, FormDestView, AdvancedFormView, translate) {
         var FormBlockView = BlockView.extend(
 
             {
@@ -36,14 +37,20 @@
 
 
                 },
-
+                attributes :function(){
+                    return {
+                        "class": "block form-block",
+                        tabindex: 1
+                    }
+                },
                 edit: function() {
-                    var formOptionsView, contentOptions, destOptions, index,advancedView;
+                    var formOptionsView, contentOptions, destOptions, index,advancedView,stylesOptionView;
                     this.model.get("form").snapshot();
                     formOptionsView = new SaveCancelPanel();
                     contentOptions = new FormOptionView({
                         model: this.model.get("form")
                     });
+                    stylesOptionView = new StylesOptionView({model:this.model.options.styles});
                     advancedView = new AdvancedOptionView({model:this.model.options.advancedCSS});
                     destOptions = new FormDestView({
                         model: this.model.get("form")
@@ -62,6 +69,7 @@
                     });
                     formOptionsView.addPane(translate("contentOptions"), contentOptions);
                     formOptionsView.addPane(translate("destOptions"), destOptions);
+                    formOptionsView.addPane(translate("styles"), stylesOptionView);
                     formOptionsView.addPane(translate("advancedCSS"), advancedView);
                     formOptionsView.setTitle(translate("formOption"));
                     this.app.currentPanel.rightPanelView.addContent(formOptionsView);
@@ -94,4 +102,4 @@
             });
 
         return FormBlockView;
-    });
+    });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js	(révision 10840)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js	(révision 10841)
@@ -77,6 +77,7 @@
         "proximite" : "Local shops",
         "mailInternaute" : "Send notification to user",
         "copy":"Copied",
+        "styles"    :   "Styles"
     },
     "fr-fr":true,
     "fr-ca": true
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/TextBlock.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/TextBlock.js	(révision 10840)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/TextBlock.js	(révision 10841)
@@ -2,11 +2,14 @@
     "JEditor/PagePanel/Contents/Blocks/TextBlock/TextBlock",
     "JEditor/PagePanel/Contents/Blocks/Block/Block",
     "JEditor/PagePanel/Contents/Options/Models/ColorsOption",
-    "JEditor/PagePanel/Contents/Options/Models/EffectsOption"
-], function(TextBlock,
+    "JEditor/PagePanel/Contents/Options/Models/EffectsOption",
+    "JEditor/PagePanel/Contents/Options/Models/StylesOption",
+], function(
+        TextBlock,
         Block,
         ColorsOption,
-        EffectsOption
+        EffectsOption,
+        StylesOption
         ) {
     var /**
      * @class TextBlock
@@ -22,10 +25,12 @@
                                  */
                                 initialize: function() {
                                     this._super();
-                                    if (!this.options.colors)
-                                        this.options.add(new ColorsOption());
-                                    if (!this.options.effects)
-                                        this.options.add(new EffectsOption());
+                                    if (this.options.colors)
+                                        this.options.remove(this.options.colors);
+                                    if (this.options.effects)
+                                        this.options.remove(this.options.effects);
+                                    if(!this.options.styles)
+                                        this.options.add(new StylesOption()); 
                                     this.on('change:content', this.toHistory);
                                 },
                                 content: null,
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 10840)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 10841)
@@ -87,6 +87,9 @@
                                 this.CKconfig.toolbar = this.defaultToolbar;
                             }
                         },
+                        attributes:function(){
+                            return {"class": "block", tabindex: 1};
+                        },
                         saveText: function(dependency) {
                             if (this.constructor.editor)
                                 this.constructor.editor.destroy();
Index: src/js/JEditor/PagePanel/Contents/Columns/Models/Column.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Columns/Models/Column.js	(révision 10840)
+++ src/js/JEditor/PagePanel/Contents/Columns/Models/Column.js	(révision 10841)
@@ -3,8 +3,9 @@
     "JEditor/PagePanel/Contents/Blocks/Block/Block",
     "JEditor/PagePanel/Contents/Options/Models/ColorsOption",
     "JEditor/PagePanel/Contents/Options/Models/EffectsOption",
+    "JEditor/PagePanel/Contents/Options/Models/StylesOption",
     "i18n!JEditor/PagePanel/Contents/Columns/nls/i18n"
-], function(Content, Block, ColorsOption, EffectsOption, translate) {
+], function(Content, Block, ColorsOption, EffectsOption,StylesOption, translate) {
 
     /**
      * Une colonne
@@ -31,10 +32,12 @@
                          */
                         initialize: function() {
                             this._super();
-                            if (!this.options.colors)
-                                this.options.add(new ColorsOption({}, {content: this}));
-                            if (!this.options.effects)
-                                this.options.add(new EffectsOption({}, {content: this}));
+                            if (this.options.colors)
+                                this.options.remove(this.options.colors);
+                            if (this.options.effects)
+                                this.options.remove(this.options.effects);
+                            if(!this.options.styles)
+                                this.options.add(new StylesOption({}, {content: this}));  
                         },
                         translate: translate
                     });
Index: src/js/JEditor/PagePanel/Contents/Columns/Views/ColumnView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Columns/Views/ColumnView.js	(révision 10840)
+++ src/js/JEditor/PagePanel/Contents/Columns/Views/ColumnView.js	(révision 10841)
@@ -40,7 +40,8 @@
                             },
                             sensorText: "Bloc",
                             modelClass: Block,
-                            handle: '.overlay'
+                            handle: '.overlay',
+                            
                         },
                         childViewClass: function(attrs) {
                             var typeName = attrs.model.type.charAt(0).toUpperCase() + attrs.model.type.slice(1) + 'Block';
Index: src/js/JEditor/PagePanel/Contents/Options/Models/StylesOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/Models/StylesOption.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Options/Models/StylesOption.js	(révision 10841)
@@ -0,0 +1,169 @@
+define( [
+    "underscore",
+    "JEditor/PagePanel/Contents/Options/Models/AbstractOption",
+    "i18n!../nls/i18n"
+], function(_, AbstractOption, translate) {
+    /**
+     * 
+     * @class StylesOption
+     * @extends AbstractOption
+     * @property {String} boxShadow La valeur css de l'ombre portée
+     * @property {String} border La valeur css de la bordure
+     * @property {String} borderRadius La valeur css des angles arrondis
+     * @todo revoir les méthodes get/set
+     */
+    var StylesOption = AbstractOption.extend(
+            /**
+             * @lends JEditor.Contents.Options.EffectOption.prototype
+             */
+                    {
+                        translate: translate,
+                        defaults: {optionType: 'styles', priority: 90},
+                        initialize: function() {
+                            this._id = this.cid;
+                            this._super();
+                        },
+                        setColorOption :function(value){
+                            this.colorOption=value;
+                        },
+                        getColorOption : function(){
+                            return this.colorOption;
+                        },
+                        setContentSize :function(value){
+                            this.contentSize = this.getConst().SizeContent[value];
+                        },
+                        getContentSize : function(){
+                            return this.contentSize;
+                        },
+                        /**
+                         * get int key of SizeContent
+                         * @param {*} value 
+                         * @returns 
+                         */
+                        getContentSizeKeyByValue :function(value){
+                            return this.getKeyByValue(this.getConst().SizeContent,value);
+                        },
+                        setShadeWorn :function(key){
+                            this.shadeWorn = this.getConst().ShadowWorn[key];
+                        },
+                        getShadeWorn :function(){
+                            return this.shadeWorn;
+                        },
+                        getShadeWornKeyByValue: function(stringValue){
+                            return this.getKeyByValue(this.getConst().ShadowWorn,stringValue);
+                        },
+                        setSectionWidth :function(key){
+                            this.sectionWidth=  this.getConst().SectionWidth[key];
+                        },
+                        getSectionWidth :function(){
+                            return this.sectionWidth;
+                        },
+                        getSectionWidthKeyByValue :function(stringValue){
+                            return this.getKeyByValue(this.getConst().SectionWidth,stringValue);
+                        },
+                        getKeyByValue :function(object,value){
+                            return Object.keys(object).find(function(key) {
+                                return object[key] === value;
+                            });
+                        },
+                        getConst:function(){
+                            return StylesOption.const;
+                        },
+                        getDefaultConst:    function(){
+                            return StylesOption.const.default;
+                        }
+                    });
+            Object.defineProperties(StylesOption.prototype, {
+                sectionWidth: {
+                    get: function() {
+                        if (this.attributes && this.attributes.sectionWidth)
+                            return this.attributes.sectionWidth;
+                        else
+                            return undefined;
+                    },
+                    set: function(sectionWidth) {
+                        if (this.set)
+                            this.set('sectionWidth', sectionWidth);
+                    }
+                },
+                shadeWorn: {
+                    get: function() {
+                        if (this.attributes && this.attributes.shadeWorn)
+                            return this.attributes.shadeWorn;
+                        else
+                            return undefined
+                    },
+                    set: function(shadeWorn) {
+                        if (this.set)
+                            this.set('shadeWorn', shadeWorn);
+                    }
+                },
+                contentSize: {
+                    get: function() {
+                        if (this.attributes && this.attributes.contentSize)
+                            return this.attributes.contentSize;
+                        else
+                            return undefined;
+                    },
+                    set: function(contentSize) {
+                        if (this.set)
+                            this.set('contentSize', contentSize);
+                    }
+                },
+                colorOption: {
+                    get: function() {
+                        if (this.attributes && this.attributes.colorOption)
+                            return this.attributes.colorOption;
+                        else
+                            return undefined;
+                    },
+                    set: function(colorOption) {
+                        if (this.set)
+                            this.set('colorOption', colorOption);
+                    }
+                }
+            });
+            StylesOption.const = {
+               SizeContent: {
+                    1   :   "xs",
+                    2   :   "sm",
+                    3   :   "md",
+                    4   :   "lg",
+                    5   :   "xl",
+                    6   :   "xxl",
+                    7   :   "xxxl"
+               },
+               ShadowWorn   :   {
+                    0   :   "",
+                    1   :   "blk-level-low",
+                    2   :   "blk-level-medium",
+                    3   :   "blk-level-hight"
+               },
+               SectionWidth:    {
+                    0:  "tight",
+                    1:  "",
+                    2:  "wide",
+                    3:  "screen"
+               },
+               default:{
+                    SizeContent:    3,
+                    ShadowWorn:     0,
+                    SectionWidth:   1
+               },
+               classColor:{
+                    "surface0" : "Surface 0",
+                    "surface1" : "Surface 1",
+                    "surface2" : "Surface 2",
+                    "surface3" : "Surface 3",
+                    "surface4" : "Surface 4",
+                    "surface5" : "Surface 5",
+                    "accent0" : "Accent 0",
+                    "accent1" : "Accent 1",
+                    "lead0" : "Lead 0",
+                    "lead1" : "Lead 1",
+                },
+            },
+            //StylesOption.SetAttributes(['contentSize', 'shadeWorn','sectonWidx']);
+            StylesOption.translate = translate;
+            return StylesOption;
+        });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Options/Templates/stylesOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/Templates/stylesOption.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Options/Templates/stylesOption.html	(révision 10841)
@@ -0,0 +1,93 @@
+
+<div class="panel-option-container animated">
+    <!-- Debut Couleur du bloc-->
+    <article class="panel-option animations">
+        <header>
+            <h3 class="option-name"><%= __("BlockColor")%></h3>
+            <p class="panel-content-legend"><%= __("BlockColorDesc")%></p>
+        </header>
+        <div class="category-content radio-transformed">
+            <%  var _id=_.uniqueId('couleur');%>
+           
+            <div>
+                <span class="effect-radio <%=(defaultColorOption==='')?'active':''%>" id="<%=_id%>" data-value="" data-helper="">
+                    <span class="helper">
+                        <span class="help"><%=__("aucune")%></span>
+                        <span class="bottom"></span>
+                    </span>
+                    <span class="container">
+                        <span class=" icon-form-long_text">
+                
+                        </span>
+                        <span class="switch-container">
+                            <span class="radio"><span>
+
+                        </span>
+                    </span>
+                </span>
+            </div>
+            <%  
+            _.each(classColor,function(value,key){%>
+                <%  var _id=_.uniqueId('couleur');%>
+                <div>
+                    <span class="effect-radio <%=(defaultColorOption===key)?'active':''%>" id="<%=_id%>" data-value="<%=key%>" data-helper="<%=value%>">
+                        <span class="helper">
+                            <span class="help"><%=value%></span>
+                            <span class="bottom"></span>
+                        </span>
+                        <span class="<%=key%>">
+                            <span class=" icon-form-long_text">
+                    
+                            </span>
+                            <span class="switch-container">
+                                <span class="radio"><span>
+    
+                            </span>
+                        </span>
+                    </span>
+                </div>
+            <% });%>
+            
+            
+    </div>
+    </article>
+    <!-- Fin du couleur bloc -->
+    <!-- Ombre porté start -->
+    <article class="panel-option shadeWorn">
+        <header>
+            <h3 class="option-name"><%= __("shadeWorn")%></h3>
+            <p class="panel-content-legend"><%= __("shadeWornDesc")%></p>
+        </header>
+        <div class="option-content">
+            <div class="slider-container"><span class="icon-less"></span><div class="slider" id="shadeWorn"></div><span class="icon-more"></span></div>
+        </div>
+    </article>
+     <!-- Ombre porté end-->
+     <!-- Taille du contunue start -->
+     <article class="panel-option contentSize">
+        <header>
+            <h3 class="option-name"><%= __("contentSize")%></h3>
+            <p class="panel-content-legend"><%= __("contentSizeDesc")%></p>
+        </header>
+        <div class="option-content">
+            <div class="slider-container"><span class="icon-less"></span><div class="slider" id="contentSize"></div><span class="icon-more"></span></div>
+        </div>
+    </article>
+     <!-- Taille du contenu end -->
+
+     <!-- largueur de la section start -->
+     <% if(contentType ==="section"){%>
+     <article class="panel-option sectionWidth">
+        <header>
+            <h3 class="option-name"><%= __("sectionWidth")%></h3>
+            <p class="panel-content-legend"><%= __("sectionWidthDesc")%></p>
+        </header>
+        <div class="option-content">
+            <div class="slider-container"><span class="icon-less"></span><div class="slider" id="sectionWidth"></div><span class="icon-more"></span></div>
+        </div>
+    </article>
+    <%}%>
+     <!-- largueur de la section end -->
+
+</div>
+
Index: src/js/JEditor/PagePanel/Contents/Options/Views/StylesOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/Views/StylesOptionView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Options/Views/StylesOptionView.js	(révision 10841)
@@ -0,0 +1,148 @@
+define( [
+    "jquery",
+    "underscore",
+    "text!../Templates/stylesOption.html",
+    "JEditor/PagePanel/Contents/Options/Views/AbstractOptionView",
+    "JEditor/PagePanel/Contents/Options/Models/StylesOption",
+    "i18n!../nls/i18n",
+    //not in params
+    "jqueryPlugins/radio",
+    "jqueryPlugins/colors",
+    "mCustomScrollbar",
+    "jqueryPlugins/switcher"
+], function(
+        $,
+        _,
+        stylesOption,
+        AbstractOptionView,
+        StylesOption,
+        translate
+        ) {
+    var /**
+     * Vues des options d'effets utilisée par les blocs, colonnes et sections
+     * @class StylesOptionView
+     * @extends AbstractOptionView
+     */
+            StylesOptionView = AbstractOptionView.extend(
+                    /**
+                     * @lends StylesOptionView.prototype
+                     */
+                            {
+                                optionType: 'styles',
+                                tagName: "div",
+                                className: 'panel-content fx-panel effects gallery-template-option',
+                                translate: translate,
+                                events: {
+                                    'click  div .effect-radio'   : '_onChangeStyleColor',
+                                    'slide .slider': '_onSliderChange',
+                                    // 'slidechange .slider': '_onSliderChange',
+                                    // 'radiochange .panel-option-container.borders': '_onRadioChange',
+                                    // 'changeminicolors input.border-color': '_onBorderColorChange'
+                                },
+                                /**
+                                 * initialise la vue
+                                 */
+                                initialize: function() {
+                                    this._super();
+                                    this._template = this.buildTemplate(stylesOption, translate);
+                                    console.log(this.options);
+                                },
+                                
+                                /**
+                                 * actualise l'affichage
+                                 */ 
+                                render: function() {
+                                    var ContentType =this.model.content.contentType;
+                                    var defaultColorOption=(this.model.getColorOption())?this.model.getColorOption():'';
+                                    if(ContentType ==="formBlock" && defaultColorOption==="")
+                                        defaultColorOption="surface0";
+                                    this.$el.html(this._template({contentType:ContentType,classColor:this.model.getConst().classColor,defaultColorOption:defaultColorOption}));
+                                    //Size content 1 -> 7
+                                    var defaultContentSize =(this.model.contentSize)? (this.model.getContentSizeKeyByValue(this.model.contentSize)) :this.model.getDefaultConst().SizeContent;
+                                    this._slideOption(".contentSize",1,7,defaultContentSize);
+                                    //Shade Worn 0 -> 3
+                                    var defaultShadeWorn = (this.model.shadeWorn)? (this.model.getShadeWornKeyByValue(this.model.shadeWorn)) : this.model.getDefaultConst().ShadowWorn; 
+                                    this._slideOption(".shadeWorn" ,0,3,defaultShadeWorn);
+                                    //Section Width Show only section 0 -> 3
+                                    var defaultSectionWidth =   (this.model.sectionWidth) ? (this.model.getSectionWidthKeyByValue(this.model.sectionWidth)) : this.model.getDefaultConst().SectionWidth;
+                                    if(ContentType ==="section")
+                                        this._slideOption(".sectionWidth",0,3,defaultSectionWidth );
+                                    
+
+                                    this.scrollables();
+                                    return this;
+                                },
+                                _onChangeStyleColor : function(event){
+                                    this.$(".effect-radio").removeClass("active");
+                                    var $target = $(event.currentTarget);
+                                    $target.addClass("active");
+                                    var value = $target.attr("data-value");
+                                    this.model.setColorOption(value);
+                                },
+                                _slideOption : function(className,min,max,value){
+                                    var slider = this.$(className+' .slider');
+                                    slider.slider({
+                                        min: min,
+                                        max: max,
+                                        step: 1,
+                                        value: value,
+                                        range:"min"
+                                    });
+                                    this.dom[this.cid].slider = slider;
+                                },
+                                /**
+                                 * Déclenchée quand on clic sur une pseudo-checkbox...
+                                 * @private
+                                 */
+                                _onSwitcherChange: function(event, data) {
+                                    var target = data.target;
+                                    var $slider = target.find('.slider');
+                                    if ($slider.length > 0) {
+                                        var max = $slider.slider('option', 'max');
+                                        var min = $slider.slider('option', 'min');
+                                        if (data.value) {
+                                            $slider.slider('value', max > 1 ? Math.round(max / 2) : max / 2);
+                                            if (data.name === 'border') {
+                                                this.dom[this.cid].minicolors.minicolors('value', '#000000');
+                                                this.dom[this.cid].radios.radio('currentValue', 'solid');
+                                            }
+                                        }
+                                        else
+                                            this.model.unset(data.name);
+                                    }
+                                    this.$el.mCustomScrollbar('update');
+                                },
+                                /**
+                                 * Déclenché au changment du color picker des bordures
+                                 * @private
+                                 */
+                                _onBorderColorChange: function(event, data) {
+                                    this.model.setBorderColor(data.hex);
+                                },
+                                /**
+                                 * changement des style de bordure via clic sur pseudo radio
+                                 * @private
+                                 */
+                                _onRadioChange: function(event, data) {
+                                    this.model.setBorderStyle(data.value);
+                                },
+                                /**
+                                 * changement des valeurs depuis un slider
+                                 * @private
+                                 */
+
+                                _onSliderChange: function(event, ui) {
+                                   var id = event.target.id;
+                                    if(id ==="contentSize")
+                                        this.model.setContentSize(parseInt(ui.value));
+                                    else if(id ==="shadeWorn")
+                                        this.model.setShadeWorn(parseInt(ui.value));
+                                    else if(id === "sectionWidth")
+                                        this.model.setSectionWidth(parseInt(ui.value));
+
+                                    
+                                }
+                            });
+                    StylesOptionView.translate = translate;
+                    return StylesOptionView;
+                });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Options/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/nls/fr-ca/i18n.js	(révision 10840)
+++ src/js/JEditor/PagePanel/Contents/Options/nls/fr-ca/i18n.js	(révision 10841)
@@ -65,5 +65,15 @@
     "popupStyle": "Style",
     "sizePopup": "Taille de la popup",
     "sizePopupLegend": "Sélectionnez la taille de la popup ici",
-    "datePlaceholder": "jj/mm/aaaa"
+    "datePlaceholder": "dd/mm/yyyy",
+    "styles" : "Styles",
+    "BlockColor"  : "Couleurs du bloc",
+    "BlockColorDesc"  : "Appliquer un ensemble de couleurs",
+    "shadeWorn" : "Ombres portée",
+    "shadeWornDesc" :"Faire glisser pour ajuster l'intensité de l'ombre",
+    "contentSize" : "Taille du contenu",
+    "contentSizeDesc" : "Glisser pour ajuster la taille du contenu.<br>(Cette option n'a pas d'effet dans la zone d'administration)",
+    "sectionWidth"  :"Largeur de la section",
+    "sectionWidthDesc"  : "Glisser pour ajuster la largeur de la section (cette option n'a pas d'effet dans la zone d'administration).",
+    "aucune"  : "Aucune"
 });
Index: src/js/JEditor/PagePanel/Contents/Options/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/nls/fr-fr/i18n.js	(révision 10840)
+++ src/js/JEditor/PagePanel/Contents/Options/nls/fr-fr/i18n.js	(révision 10841)
@@ -65,5 +65,15 @@
     "popupStyle": "Style",
     "sizePopup": "Taille de la popup",
     "sizePopupLegend": "Sélectionnez la taille de la popup ici",
-    "datePlaceholder": "jj/mm/aaaa"
+    "datePlaceholder": "dd/mm/yyyy",
+    "styles" : "Styles",
+    "BlockColor"  : "Couleurs du bloc",
+    "BlockColorDesc"  : "Appliquer un ensemble de couleurs",
+    "shadeWorn" : "Ombres portée",
+    "shadeWornDesc" :"Faire glisser pour ajuster l'intensité de l'ombre",
+    "contentSize" : "Taille du contenu",
+    "contentSizeDesc" : "Glisser pour ajuster la taille du contenu.<br>(Cette option n'a pas d'effet dans la zone d'administration)",
+    "sectionWidth"  :"Largeur de la section",
+    "sectionWidthDesc"  : "Glisser pour ajuster la largeur de la section (cette option n'a pas d'effet dans la zone d'administration).",
+    "aucune"  : "Aucune"
 });
Index: src/js/JEditor/PagePanel/Contents/Options/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/nls/i18n.js	(révision 10840)
+++ src/js/JEditor/PagePanel/Contents/Options/nls/i18n.js	(révision 10841)
@@ -67,7 +67,17 @@
     "popupStyle": "Style",
     "sizePopup": "Popup size",
     "sizePopupLegend": "Select popup size here",
-    "datePlaceholder": "dd/mm/yyyy"
+    "datePlaceholder": "dd/mm/yyyy",
+    "styles" : "Styles",
+    "BlockColor"  : "Colors of block",
+    "BlockColorDesc"  : "Apply a set of colors",
+    "shadeWorn" : "Shade worn",
+    "shadeWornDesc" :"Drag to adjust the shadow intensity",
+    "contentSize" : "Size of content",
+    "contentSizeDesc" : "Drag to adjust the size of the content.<br>(This option has no effect in the adminstration area)",
+    "sectionWidth"  :"Section width",
+    "sectionWidthDesc"  : "Slide to adjust the width of the section.<br>(This option has no effect in the adminstration area)",
+    "aucune"  : "None"
   },
   "fr-fr": true,
   "fr-ca": true
Index: src/js/JEditor/PagePanel/Contents/Sections/Models/Section.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Sections/Models/Section.js	(révision 10840)
+++ src/js/JEditor/PagePanel/Contents/Sections/Models/Section.js	(révision 10841)
@@ -5,6 +5,7 @@
     "JEditor/PagePanel/Contents/Columns/Models/Column",
     "JEditor/PagePanel/Contents/Options/Models/ColorsOption",
     "JEditor/PagePanel/Contents/Options/Models/EffectsOption",
+    "JEditor/PagePanel/Contents/Options/Models/StylesOption",
     "JEditor/PagePanel/Contents/Options/Models/PopupOption",
     "JEditor/PagePanel/Contents/Options/Models/PopupStyle",
     "i18n!JEditor/PagePanel/Contents/Sections/nls/i18n"
@@ -14,6 +15,7 @@
         Column,
         ColorsOption,
         EffectsOption,
+        StylesOption,
         PopupOption,
         PopupStyle,
         translate
@@ -43,10 +45,12 @@
                                 initialize: function() {
                                     this._super();
                                     if (!this.options.popup) {
-                                        if (!this.options.colors)
-                                            this.options.add(new ColorsOption({}, {content: this}));
-                                        if (!this.options.effects)
-                                            this.options.add(new EffectsOption({}, {content: this}));     
+                                        if (this.options.colors)
+                                            this.options.remove(this.options.colors);
+                                        if (this.options.effects)
+                                            this.options.remove(this.options.effects);
+                                        if(!this.options.styles)
+                                            this.options.add(new StylesOption({}, {content: this}));     
                                     }                           
                                     this._initColumns();
                                 },
@@ -65,6 +69,7 @@
                                 createPopup: function (){
                                    this.options.remove(this.options.colors);
                                    this.options.remove(this.options.effects);
+                                   this.options.remove(this.options.styles);
                                    this.options.unshift(new PopupOption({}, {content:this}));
                                    this.options.add(new PopupStyle({}, {content:this}),{at : 1} );
                                 },
@@ -71,10 +76,8 @@
                                 removePopup: function (){
                                    this.options.remove(this.options.popup);
                                    this.options.remove(this.options.popupStyle);
-                                    if (!this.options.colors)
-                                        this.options.add(new ColorsOption({}, {content:this}));
-                                    if (!this.options.effects)
-                                        this.options.add(new EffectsOption({}, {content:this}));
+                                   if(!this.options.styles)
+                                    this.options.add(new StylesOption({}, {content: this}));     
                                     
                                 },
                                 isPopup: function(){
Index: src/js/JEditor/PagePanel/Contents/Sections/Views/SectionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Sections/Views/SectionView.js	(révision 10840)
+++ src/js/JEditor/PagePanel/Contents/Sections/Views/SectionView.js	(révision 10841)
@@ -89,6 +89,7 @@
                             //this.render();
                             this.on(Events.ContentEvents.REMOVE, this.onColumnRemove);
                             this.on(Events.ContentEvents.UPDATE, this.computeHeight);
+                            
 //                        this.listenTo(this.model, Events.ContentEvents.ADD_CHILD, this.onAdd);
                         },
                         onAdd: function(model, at) {
Index: src/less/imports/page_panel/module/block-options/advanced-option.less
===================================================================
--- src/less/imports/page_panel/module/block-options/advanced-option.less	(révision 10840)
+++ src/less/imports/page_panel/module/block-options/advanced-option.less	(révision 10841)
@@ -10,4 +10,46 @@
 		
 		color: @greyM;
 	}
-}
\ No newline at end of file
+}
+.Surface0{
+	background: var(--surface0);
+	color: var(--fg-color);
+} 
+.Surface1 {
+	background: var(--surface1);
+	color: var(--fg-color);
+}
+.Surface2 {
+ 	background: var(--surface2); 
+	color: var(--fg-color);
+}	
+.Surface3 {
+	background: var(--surface3); 
+	color: var(--fg-color);
+}
+.Surface4 {
+	background: var(--surface4); 
+	color: var(--fg-color);
+} 
+.Surface5 {
+	background: var(--surface5); 
+	color: var(--fg-color);
+} 
+
+.Accent0 {
+	background: var(--accent-surface0); 
+	color: var(--accent-text0);
+} 
+
+.Accent1 {
+	background: var(--accent-surface1); 
+	color: var(--accent-text1);
+} 
+.Lead0 {
+	background: var(--lead-surface0); 
+	color: var(--lead-text0);
+} 
+.Lead1 {
+	background: var(--lead-surface1); 
+	color: var(--lead-text1);
+} 
\ No newline at end of file
