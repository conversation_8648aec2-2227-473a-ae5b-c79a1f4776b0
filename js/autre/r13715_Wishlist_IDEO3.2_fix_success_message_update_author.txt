Revision: r13715
Date: 2025-01-16 14:45:00 +0300 (lkm 16 Jan 2025) 
Author: frahajanirina 

## Commit message
Wishlist:IDEO3.2:fix success message & update author

## Files changed

## Full metadata
------------------------------------------------------------------------
r13715 | frahajanirina | 2025-01-16 14:45:00 +0300 (lkm 16 Jan 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/NewsPanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/nls/i18n.js

Wishlist:IDEO3.2:fix success message & update author
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/NewsPanel.js
===================================================================
--- src/js/JEditor/NewsPanel/NewsPanel.js	(révision 13714)
+++ src/js/JEditor/NewsPanel/NewsPanel.js	(révision 13715)
@@ -816,7 +816,12 @@
                                 articleSyncSuccess :function (e){
                                     this.stopListening(this.currentArticle, Events.BackboneEvents.ERROR, this.articleSyncError);
                                     if (e.content.state > 0 ) {
+                                        var type;
+                                        if (e.previous('content').client != e.content.client) {
+                                            type = 'authorChangedDone';
+                                        } else {
                                         var type = (e.content.state === 1)? 'programDone':'publishDone';
+                                        }
                                         $.toast({
                                             text: translate(type), 
                                             icon: 'icon-check-circle', 
@@ -830,19 +835,6 @@
                                             loader:false,
                                             stack :1
                                         });
-                                    }else {
-                                        $.toast({
-                                            text: translate("saveSuccesful"), 
-                                            icon: 'icon-check-circle', 
-                                            type:'success',
-                                            appendTo:'#news-editor .zone .zone-toolbox',
-                                            showHideTransition: 'fade', 
-                                            hideAfter: 5000, 
-                                            position: 'top-right', 
-                                            textAlign: 'left', 
-                                            allowToastClose:false,
-                                            loader:false
-                                        });
                                     }
                                 },
                                 articleSyncError :function (e){
Index: src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 13714)
+++ src/js/JEditor/NewsPanel/nls/fr-ca/i18n.js	(révision 13715)
@@ -170,5 +170,6 @@
 	"changeAuthor": "Changer l'auteur",
 	"month": ["Janvier", "Février", "Mars", "Avril", "Mai", "Juin", "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"],
 	"day": ["Dimanche", "Lundi", "Mardi", "Mercredi", "Jeudi", "Vendredi", "Samedi"],
-	"dayMin": ["Di", "Lu", "Ma", "Me", "Je", "Ve", "Sa"]
+	"dayMin": ["Di", "Lu", "Ma", "Me", "Je", "Ve", "Sa"],
+	"authorChangedDone": "L'auteur a été changé avec succès."
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 13714)
+++ src/js/JEditor/NewsPanel/nls/fr-fr/i18n.js	(révision 13715)
@@ -170,5 +170,6 @@
 	"changeAuthor": "Changer l'auteur",
 	"month": ["Janvier", "Février", "Mars", "Avril", "Mai", "Juin", "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"],
 	"day": ["Dimanche", "Lundi", "Mardi", "Mercredi", "Jeudi", "Vendredi", "Samedi"],
-	"dayMin": ["Di", "Lu", "Ma", "Me", "Je", "Ve", "Sa"]
+	"dayMin": ["Di", "Lu", "Ma", "Me", "Je", "Ve", "Sa"],
+	"authorChangedDone": "L'auteur a été changé avec succès."
 });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/nls/i18n.js
===================================================================
--- src/js/JEditor/NewsPanel/nls/i18n.js	(révision 13714)
+++ src/js/JEditor/NewsPanel/nls/i18n.js	(révision 13715)
@@ -167,7 +167,8 @@
         "changeAuthor": "Change author",
         "month": ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"],
         "day": ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"],
-        "dayMin": ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"]
+        "dayMin": ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"],
+        "authorChangedDone": "The author has been changed successfully."
     },
     "fr-fr": true,
     "fr-ca": true
