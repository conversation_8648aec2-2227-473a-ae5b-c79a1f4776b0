Revision: r11992
Date: 2024-02-23 08:53:27 +0300 (zom 23 Feb 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: panel fichiers: importer des logos/labels(partie JS)

## Files changed

## Full metadata
------------------------------------------------------------------------
r11992 | srazanandralisoa | 2024-02-23 08:53:27 +0300 (zom 23 Feb 2024) | 1 ligne
Chemins modifiés :
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Basefile
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Basefile/Models
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Basefile/Models/LabelsCollection.js
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Basefile/Models/Svg.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/main.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileList.html
   A /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/selectIcon.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileListView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/IconListView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/SelectIconView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/main.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/filePanel.less

wishlist IDEO3.2: panel fichiers: importer des logos/labels(partie JS)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Basefile/Models/LabelsCollection.js
===================================================================
--- src/js/JEditor/Commons/Basefile/Models/LabelsCollection.js	(nonexistent)
+++ src/js/JEditor/Commons/Basefile/Models/LabelsCollection.js	(révision 11992)
@@ -0,0 +1,39 @@
+define([
+    "JEditor/Commons/Ancestors/Models/Collection",
+    "JEditor/Commons/Basefile/Models/Svg"
+],function(
+	Collection,
+	Svg
+){
+var LabelsCollection = Collection.extend(
+    /**
+     * @lends LabelsCollection.prototype
+     */
+    {
+        model: Svg,
+        url:  __IDEO_API_PATH__ + "/resources-svgcollection/labels",
+        
+        /**
+         * initialize l'objet
+         */
+        initialize: function() {
+        },
+        parse: function(response) {
+            return response.data; // assuming the array of files is in the 'data' property of the response
+        },
+
+        fetchIcons: function(callback){
+            this.fetch({
+                success: function(collection, response, options) {
+                    callback(null, response);
+                },
+                error: function(collection, response, options) {
+                    callback(new Error('Failed to fetch SVG'));
+                }
+            });
+        },
+
+
+    });
+return LabelsCollection;
+});
\ No newline at end of file

Property changes on: src/js/JEditor/Commons/Basefile/Models/LabelsCollection.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/Commons/Basefile/Models/Svg.js
===================================================================
--- src/js/JEditor/Commons/Basefile/Models/Svg.js	(nonexistent)
+++ src/js/JEditor/Commons/Basefile/Models/Svg.js	(révision 11992)
@@ -0,0 +1,15 @@
+define([
+    "JEditor/Commons/Ancestors/Models/Model",
+
+], function (Model) {
+
+    var Svg = Model.extend({
+        
+        defaults: {
+            'name': '',
+            'content': '',
+        },
+    });
+
+    return Svg;
+});

Property changes on: src/js/JEditor/Commons/Basefile/Models/Svg.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/Commons/main.js
===================================================================
--- src/js/JEditor/Commons/main.js	(révision 11991)
+++ src/js/JEditor/Commons/main.js	(révision 11992)
@@ -22,6 +22,8 @@
     "JEditor/Commons/Ancestors/Views/SaveCancelPanel",
     "JEditor/Commons/Ancestors/Views/TabbedView",
     "JEditor/Commons/Ancestors/Views/View",
+    "JEditor/Commons/Basefile/Models/LabelsCollection",
+    "JEditor/Commons/Basefile/Models/Svg",
     "JEditor/Commons/Events",
     "JEditor/Commons/Files/Models/File",
     "JEditor/Commons/Files/Models/FileCollection",
Index: src/js/JEditor/FilePanel/Views/FileListView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileListView.js	(révision 11991)
+++ src/js/JEditor/FilePanel/Views/FileListView.js	(révision 11992)
@@ -8,10 +8,11 @@
     "JEditor/Commons/Events",
     "JEditor/Commons/Ancestors/Views/ListView",
     "JEditor/Commons/Utils",
+    "JEditor/FilePanel/Views/SelectIconView",
     "i18n!../nls/i18n",
     //not in params
     "jqueryPlugins/uploader"
-], function($, _, fileList, groupFileList, emptyGroupFileList, emptyFileList, Events, ListView, Utils, translate) {
+], function($, _, fileList, groupFileList, emptyGroupFileList, emptyFileList, Events, ListView, Utils, SelectIconView, translate) {
     var FileListView = ListView.extend({
         selected: {},
         selectedLength: 0,
@@ -26,6 +27,7 @@
             'uploaderfail .group-content': 'onUploadFail',
             'uploaderstart .group-content': 'uploaderstart',
             'click [data-action="showuploader"]': 'showUploader',
+            'click [data-action="showIcons"]': 'showIcons',
             'click .flex-load-more .load-more' : 'loadMore',
             'click .flex-load-more .load-more' : 'loadMore',
         },
@@ -40,6 +42,9 @@
             this.listenTo(this.collection, Events.BackboneEvents.ADD, this._rebuildList);
             this.listenTo(this.collection, Events.BackboneEvents.REMOVE, this._rebuildList);
             this.listenTo(this.collection, Events.BackboneEvents.CHANGE, this._rebuildList);
+            this.selectIconDialog = new SelectIconView();
+            this.listenTo( this.selectIconDialog,Events.ListViewEvents.SELECT_ICON,this.onSelectedIcon);
+
         },
         onRequest: function (e,r) {
             if (e.models) {
@@ -236,17 +241,7 @@
             this.collection.setType(null);
             this.collection.setOffset(0);
             this.collection.fetch({remove: true});
-            return this.filter(FileListView.FILTER_IMAGES_ONLY);
-        // return this.filter(FileListView.FILTER_IMAGES_ONLY);
-        this.collection.setType('image');
-        this.collection.setOffset(0);
-        this.collection.fetch({remove: true});
         },
-        resetFilter :function(){
-            this.collection.setType(null);
-            this.collection.setOffset(0);
-            this.collection.fetch({remove: true});
-        },
         noImage: function() {
             //return this.filter(FileListView.FILTER_NO_IMAGES);
             this.collection.setType('file');
@@ -267,6 +262,63 @@
             if(this.collection.hasmore){
                 this.collection.fetch({remove: false});
             }
+        },
+        onSelectedIcon:function(files){
+        var self = this;
+            _.each(files, function (file) {
+                if(file.type == 'svg'){
+                    var blob = new Blob([file.content ], { type: 'image/svg+xml' });
+                    self.uploaderFichier(blob,file.name);
+               }else{
+                self.telecharger(file);
+               }
+            });     
+          
+        },
+        // Fonction pour télécharger le fichier s'il s'agit d'un lien externe
+        telecharger: function (fichier) {
+            const self = this;
+           
+            fetch(fichier.url, {
+                method: 'GET',
+            })
+            .then(function (response){
+                if (!response.ok) {
+                throw new Error('Erreur lors du téléchargement du fichier');
+                }
+                return response.blob();
+            })
+            .then(function(data) {
+                // Appeler la fonction d'upload avec le fichier téléchargé
+                self.uploaderFichier(data, fichier.name);
+            })
+            .catch(function (error) {
+                console.error('Erreur lors du téléchargement du fichier:', error);
+            });
+        },
+        // Fonction pour uploader les fichier à partir d'un blob d'image 
+        uploaderFichier: function (blob, fileName) {
+            var that = this;
+            if (fileName && blob) {
+                var postData = new FormData();
+                    postData.append('name', fileName);
+                    postData.append('value', blob, fileName);
+                    $.ajax({
+                        type: "POST",
+                        context: that,
+                        url: __IDEO_UPLOAD_URL__ ? __IDEO_UPLOAD_URL__ : '/admin/resources',
+                        data: postData,
+                        contentType: false,
+                        processData: false,
+                        success: function(data, xhr, statut) {
+                            that.resetFilter();
+                        }
+                    });
+            }
+          },
+        showIcons: function (){
+            this.selectIconDialog.getIcons();
+            this.selectIconDialog.open();
         }
     });
     var regexpImg = /^image/;
@@ -284,7 +336,7 @@
             FILE_CLEARVIEW: 'fileClearView',
             CHOOSE_IMG: 'selectImage',
             UPDATE_COUNT_VIEW: "UPDATECOUNT",
-            
+            SELECT_ICON:"selectIcon",
             UPDATE_COUNT_VIEW: "UPDATECOUNT",
         },
     })
Index: src/js/JEditor/FilePanel/Views/IconListView.js
===================================================================

Property changes on: src/js/JEditor/FilePanel/Views/IconListView.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/FilePanel/Views/main.js
===================================================================
--- src/js/JEditor/FilePanel/Views/main.js	(révision 11991)
+++ src/js/JEditor/FilePanel/Views/main.js	(révision 11992)
@@ -10,7 +10,8 @@
 	"./ImageEditView",
 	"./ImageGroupEditionView",
 	"./SelectCollectionView",
-	"./FocusEditView"
+	"./FocusEditView",
+	"./SelectIconView"
 ],function(
 	CollectionDetailView,
 	CollectionListView,
@@ -23,7 +24,8 @@
 	ImageEditView,
 	ImageGroupEditionView,
 	SelectCollectionView,
-	FocusEditView
+	FocusEditView,
+	SelectIconView
 	){
 		var component = {
 		"CollectionDetailView":CollectionDetailView,
@@ -37,7 +39,8 @@
 		"ImageEditView":ImageEditView,
 		"ImageGroupEditionView":ImageGroupEditionView,
 		"SelectCollectionView":SelectCollectionView,
-		"FocusEditView":FocusEditView
+		"FocusEditView":FocusEditView,
+		"SelectIconView":SelectIconView
 	};
 	return component;
 	});
\ No newline at end of file
Index: src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 11991)
+++ src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 11992)
@@ -128,5 +128,7 @@
     "FileOn"    :   "fichier(s) sur",
     "ImageAddToCollection" : " L’image à bien été ajoutée à la collection",
     "fontExiste": "Cette font existe déjà",
-    "addFileExist": "Ajouter des images existantes"
+    "addFileExist": "Ajouter des images existantes",
+    "browseIconTitle":"Parcourir la base d'icônes",
+    "showIcons" : "Importer depuis une base d'images",
 });
\ No newline at end of file
Index: src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 11991)
+++ src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 11992)
@@ -132,5 +132,7 @@
     "FileOn"    :   "fichier(s) sur",
     "ImageAddToCollection" : " L’image à bien été ajoutée à la collection",
     "fontExiste": "Cette font existe déjà",
-    "addFileExist": "Ajouter des images existantes"
+    "addFileExist": "Ajouter des images existantes",
+    "browseIconTitle":"Parcourir la base d'icônes",
+    "showIcons" : "Importer depuis une base d'images",
 });
\ No newline at end of file
Index: src/js/JEditor/FilePanel/nls/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/i18n.js	(révision 11991)
+++ src/js/JEditor/FilePanel/nls/i18n.js	(révision 11992)
@@ -135,6 +135,8 @@
         "ImageAddToCollection" : " The image has been added to the collection",
         "fontExiste":"This font already exists",
         "addFileExist": "Add existing images",
+        "browseIconTitle":"Browse the icon database",
+        "showIcons" : "Import from image database",
     },
     "fr-fr": true, "fr-ca":true
 })
\ No newline at end of file
Index: src/js/JEditor/FilePanel/Templates/fileList.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileList.html	(révision 11991)
+++ src/js/JEditor/FilePanel/Templates/fileList.html	(révision 11992)
@@ -11,6 +11,16 @@
                 <%= __("addFile")%>
 
             </div>
+            <div class=" add-file" data-action="showIcons">
+    
+                <span class="icon">
+                    <span class="icon-hexagon"></span>
+                    <span class="icon-add"></span>
+                </span>
+                <p><%= __("showIcons")%></p> 
+
+            </div>
+            
         </div>
            
     </div>
Index: src/js/JEditor/FilePanel/Templates/selectIcon.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/selectIcon.html	(nonexistent)
+++ src/js/JEditor/FilePanel/Templates/selectIcon.html	(révision 11992)
@@ -0,0 +1,22 @@
+<div class="container-icon option-content">
+    <header class="popup-bar"> 
+        <a href="javascript:;" class="onglet mycollections active" data-switchto="labels">
+            Labels
+        </a>
+        <!-- <a href="javascript:;" class="onglet mycollections " data-switchto="LinkeoStock">
+            LinkeoStock
+        </a> -->
+   </header>
+    <div class="grid-container-icon">
+        <%for(i=0;i<content.length;i++){
+            svg = content[i];
+            %>
+            <div  class="box-icon <%=selected === svg.name ? 'selected-icon':''%>" data-name="<%= svg.name%>" >
+                    <span class="wrapper-icon">
+                        <%= svg.content%>
+                    </span>
+            </div>
+            <%}%>
+    </div>
+</div>
+

Property changes on: src/js/JEditor/FilePanel/Templates/selectIcon.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/FilePanel/Views/SelectIconView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/SelectIconView.js	(nonexistent)
+++ src/js/JEditor/FilePanel/Views/SelectIconView.js	(révision 11992)
@@ -0,0 +1,113 @@
+define([
+    "jquery",
+    "underscore",
+    "text!../Templates/selectIcon.html",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/DialogView",
+    "JEditor/ParamsPanel/Models/Params",
+    "JEditor/Commons/Basefile/Models/LabelsCollection",
+    "JEditor/Commons/Basefile/Models/Svg",
+    "i18n!../nls/i18n",
+    //not in params
+    "jqueryPlugins/dropdown"
+], function($, _, selectIcon, Events, DialogView,Params,LabelsCollection,Svg, translate) {
+    var SelectIconView = DialogView.extend({
+        className: 'selectIcon',
+        events: {
+            'click .box-icon': 'onSelect',
+            'click a[data-switchto]': '_switchListCollection',
+        },
+        currentList: [],
+        currentType : 'labels',
+        constructor: function(options) {
+            if (!options)
+            var options = {};
+            options.width = 750;
+            options.title = translate('browseIconTitle')
+            options.height = 600;
+            options.allowMultipleSelect = false;
+            options.buttons = [
+                {
+                    class: 'okay',
+                    text: translate("okay"),
+                    click: _.bind(this.onOk, this)
+                },
+                {
+                    text: translate("cancel"),
+                    class: 'cancel',
+                    click: _.bind(this.onCancel, this)
+                }
+            ]
+            DialogView.call(this, options);
+            this.on('open close', this.onToggle);
+        },
+        initialize: function(options) {
+            this._super();
+            this._template = this.buildTemplate(selectIcon, translate);
+            this.currentList = this.options.svgList;
+            this.render();
+        },
+        getIcons: function(){
+            var svgCollectionObj = new LabelsCollection();
+            svgCollectionObj.fetchIcons(_.bind(function(error, svgs) {
+                if (error) {
+                    console.error(error);
+                } else {
+                    this.svgCollection = svgs;
+                    this.trigger('data:fetched');
+                }
+            },this));
+
+            this.listenTo(this, 'data:fetched', this.render);
+        },
+        _switchListCollection : function(e){
+            var $target = $(e.currentTarget);
+            this.dom[this.cid].type.removeClass('active');
+            $target.addClass('active');
+            this.currentType = $target.data('switchto');
+        },
+        render: function() {
+            this.selected = {};
+            this.selectedLength = 0;
+            var data = (this.svgCollection === undefined ? [] : this.svgCollection);
+            this._super();
+            this.undelegateEvents();
+            this.$el.html(this._template({content:data, selected: this.selected}));
+            this.dom[this.cid].type = this.$('.onglet.mycollections');
+            this.delegateEvents();
+            return this;
+        },
+        onCancel: function() {
+            $('.box-icon').css("background-color", "transparent");
+            this.selected = {};
+            this.$el.dialog('close');
+        },
+        onOk: function() {
+            this.trigger(Events.ListViewEvents.SELECT_ICON, this.selected);
+            this.$el.dialog('close');
+        },
+        onSelect: function(e) {
+            var $target = $(e.currentTarget);
+            var name = $target.data('name');
+            var content = $target.find('.wrapper-icon').html().trim();
+            var type = 'svg';
+            var selected = {
+                'name':name,
+                'content': content,
+                'type' : type
+            }
+            if (this.selected.hasOwnProperty(name)) {
+                delete  this.selected[selected.name];
+                $target.removeClass('selected-icon');
+                this.selectedLength--;
+            }
+            else {
+                this.selected[selected.name] = selected;
+                this.selectedLength++;
+                $target.addClass('selected-icon');
+            }
+        },
+    
+    });
+    return SelectIconView;
+});

Property changes on: src/js/JEditor/FilePanel/Views/SelectIconView.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/less/imports/filePanel.less
===================================================================
--- src/less/imports/filePanel.less	(révision 11991)
+++ src/less/imports/filePanel.less	(révision 11992)
@@ -408,8 +408,71 @@
 .top-bar a.file-action:hover .infobulles {
     display: block;
 }
+/* ===================================================================
+   #CONTENT-FILES
+   =================================================================== */
 
+.popup-bar {
+    font-family: 'Raleway', sans-serif;
+    font-weight: 500;
+    font-size: 12px;
+    width: 100%;
+    height: 70px;
 
+    & a {
+        color: #0f0f0f;
+        text-decoration: none;
+        font-weight: bold;
+    }
+
+    &.onglet:active {
+        color: #d5d5d5; 
+    }       
+    
+    & a.filtres:nth-child(1){
+        margin: 20px 1px 0 20px;
+        .border-radius(4px 0 0 4px);
+        line-height: 3;
+    }
+    & a.filtres:nth-child(2){
+        margin: 20px 0 0 0;
+        line-height: 39px;
+    }
+    & a.filtres:nth-child(3){
+        margin: 20px 0 0 1px;
+        .border-radius(0 4px 4px 0);
+        line-height: 37px;
+    }
+}
+.popup-bar .onglet  {
+    -webkit-transition:	all 0.15s ease-in-out;
+    -moz-transition:	all 0.15s ease-in-out;
+    -o-transition:          all 0.15s ease-in-out;
+    -ms-transition:         all 0.15s ease-in-out;
+    transition:		all 0.15s ease-in-out;
+}
+.popup-bar .onglet {
+    float: left;
+    padding: 0 25px;
+    line-height: 50px;
+    text-align: center;
+    background-color: #666666; 
+
+    &:hover {
+        background-color: #34d399;
+    }
+
+    &:active,&.active {
+        color:#d5d5d5 ;
+        background-color: #34d399;
+    }
+    & span {
+        font-size: 20px;
+        margin: 25px 5px 0 0;
+        float:left;
+    }
+}
+
 /* ===================================================================
    #CONTENT-FILES
    =================================================================== */
@@ -958,6 +1021,17 @@
 .FileTop {
     margin: auto 211px;
   }
+.my-files .content .group-content {
+    display: flex;
+    .add-file:nth-child(1) {width: 100%;}
+	.add-file:nth-child(2) {
+        width: 15%;
+        p{
+            line-height: 25px;
+            font-family: sans-serif;
+        }
+    }
+}
 .my-files .add-file {
     width: auto; height: 120px;
     padding: 14px;
