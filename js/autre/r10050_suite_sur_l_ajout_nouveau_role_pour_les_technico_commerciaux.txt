Revision: r10050
Date: 2022-12-23 12:16:07 +0300 (zom 23 Des 2022) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
suite sur l'ajout nouveau role pour les technico commerciaux

## Files changed

## Full metadata
------------------------------------------------------------------------
r10050 | srazanandralisoa | 2022-12-23 12:16:07 +0300 (zom 23 Des 2022) | 1 ligne
Chemins modifiés :
   A /branches/ideo3_v2/integration/assets/ACLs/lpadmin.json

suite sur l'ajout nouveau role pour les technico commerciaux
------------------------------------------------------------------------

## Diff
Index: assets/ACLs/lpadmin.json
===================================================================
--- assets/ACLs/lpadmin.json	(nonexistent)
+++ assets/ACLs/lpadmin.json	(révision 10050)
@@ -0,0 +1,183 @@
+{
+    "access_panel_params": {
+     "value": false,
+     "comparison": null
+    },
+    "uncustomize_zone": {
+     "value": false,
+     "comparison": null
+    },
+    "restore_zone": {
+     "value": false,
+     "comparison": null
+    },
+    "delete_page": {
+     "value": false,
+     "comparison": null
+    },
+    "access_panel_stats": {
+     "value": true,
+     "comparison": null
+    },
+    "access_layout": {
+     "value": false,
+     "comparison": null
+    },
+    "access_panel_navigation": {
+     "value": false,
+     "comparison": null
+    },
+    "access_panel_pages": {
+     "value": true,
+     "comparison": null
+    },
+    "access_panel_evaluation": {
+      "value": true,
+      "comparison": "==="
+    },
+    "set_css_classes": {
+     "value": false,
+     "comparison": null
+    },
+    "access_non_main_zone": {
+     "value": false,
+     "comparison": null
+    },
+    "access_panel_logs": {
+     "value": false,
+     "comparison": null
+    },
+    "access_panel_messages": {
+     "value": true,
+     "comparison": null
+    },
+    "access_panel_clickrdv": {
+     "value": true,
+     "comparison": "==="
+    },
+    "access_panel_mobileapp": {
+        "value": true,
+        "comparison": "==="
+    },
+    "access_panel_quote": {
+        "value": true,
+        "comparison": "==="
+    },
+    "access_panel_icom": {
+        "value": true,
+        "comparison": "==="
+    },
+    "access_panel_restaurant": {
+        "value": true,
+        "comparison": "==="
+    },
+    "useNewsletterBlock": {
+     "value": false,
+     "comparison": null
+    },
+    "create_page": {
+     "value": false,
+     "comparison": null
+    },
+    "access_panel_storelocator": {
+     "value": true,
+     "comparison": "==="
+    },
+    "set_page_name": {
+     "value": false,
+     "comparison": null
+    },
+    "useClickRdvBlock": {
+     "value": false,
+     "comparison": null
+    },
+    "useEvaluationBlock": {
+        "value": false,
+        "comparison": null
+    },
+    "useRestaurantBlock": {
+        "value": false,
+        "comparison": null
+    },
+    "useHtmlBlock": {
+        "value": false,
+        "comparison": null
+    },
+    "access_panel_social": {
+     "value": true,
+     "comparison": "==="
+    },
+    "access_batch_action": {
+     "value": false,
+     "comparison": null
+    },
+    "access_panel_newnamepaneltemplate": {
+     "value": false,
+     "comparison": null
+    },
+    "set_page_layout": {
+     "value": false,
+     "comparison": null
+    },
+    "access_panel_dashboard": {
+     "value": true,
+     "comparison": null
+    },
+    "access_panel_files": {
+     "value": true,
+     "comparison": null
+    },
+    "access_panel_newsletter": {
+     "value": true,
+     "comparison": "==="
+    },
+    "access_panel_design": {
+     "value": false,
+     "comparison": null
+    },
+    "access_panel_notFound": {
+     "value": true,
+     "comparison": null
+    },
+    "lock_page":{
+        "value":true,
+        "comparison":null
+    },
+    "lock_section":{
+        "value":false,
+        "comparison":null
+    },
+    "display_file_url":{
+        "value":false,
+        "comparison":null
+    },
+    "access_lpsupport_page":{
+       "value":false,
+       "comparison":null
+    },
+    "delete_file": {
+       "value": true,
+       "comparison": null
+    },
+    "view_jsonLd": {
+       "value": false,
+       "comparison": null
+    },
+    "config_page":{
+       "value": false,
+       "comparison": null
+    },
+    "export_page":{
+       "value": false,
+       "comparison": null
+    },
+    "export_contenu_page":{
+       "value": false,
+       "comparison": null
+    },
+    "homeless_msg":{
+       "value": false,
+       "comparison": null
+    }
+   }
+   
\ No newline at end of file
