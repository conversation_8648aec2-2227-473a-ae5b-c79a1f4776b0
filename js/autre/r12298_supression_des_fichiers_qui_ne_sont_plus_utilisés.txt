Revision: r12298
Date: 2024-05-16 10:08:04 +0300 (lkm 16 Mey 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
 supression des fichiers qui ne sont plus utilisés 

## Files changed

## Full metadata
------------------------------------------------------------------------
r12298 | srazana<PERSON><PERSON>oa | 2024-05-16 10:08:04 +0300 (lkm 16 Mey 2024) | 1 ligne
Chemins modifiés :
   D /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/Articles.js
   D /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Models/News.js
   D /branches/ideo3_v2/integration_news/src/js/JEditor/NewsPanel/Views/NewsListView.js

 supression des fichiers qui ne sont plus utilisés 
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Models/News.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/News.js	(révision 12297)
+++ src/js/JEditor/NewsPanel/Models/News.js	(nonexistent)
@@ -1,19 +0,0 @@
-define([
-    "JEditor/Commons/Ancestors/Models/Model",
-    "JEditor/Commons/Events",
-    "i18n!../nls/i18n"
-], function (Model, Events, translate) {
-    var Feedget = Model.extend({
-        defaults: {
-        },
-        initialize: function () {
-            console.log("init");
-            this._super();
-        },
-        validate: function (attributes, options) {
-            
-        }
-    });
-    
-    return Feedget;
-});
\ No newline at end of file

Property changes on: src/js/JEditor/NewsPanel/Models/News.js
___________________________________________________________________
Deleted: svn:executable
## -1 +0,0 ##
-*
\ No newline at end of property
Index: src/js/JEditor/NewsPanel/Models/Articles.js
===================================================================
--- src/js/JEditor/NewsPanel/Models/Articles.js	(révision 12297)
+++ src/js/JEditor/NewsPanel/Models/Articles.js	(nonexistent)
@@ -1,22 +0,0 @@
-define([
-    "underscore",
-    "JEditor/Commons/Events",
-    "JEditor/Commons/Ancestors/Models/LockableModel"
-], function(_, Events,LockableModel) {
-    var Article = Content.extend(
-            /**
-             * @lends Article
-             */
-                    {
-                        defaults: {sectionCount: 0},
-                        urlRoot: __IDEO_API_PATH__ + '/news/content/',
-                        depSaved: 0,
-                        depError: 0,
-                        childrenAttribute: 'sections',
-                        childClass: Section,
-                    });
-
-            Article.prototype = _.extend(Article.prototype, LockableModel.prototype);
-            Article.SetAttributes(['sectionCount', 'name', 'sections', 'customized']);
-            return Article;
-        });
\ No newline at end of file
Index: src/js/JEditor/NewsPanel/Views/NewsListView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/NewsListView.js	(révision 12297)
+++ src/js/JEditor/NewsPanel/Views/NewsListView.js	(nonexistent)
@@ -1,17 +0,0 @@
-define([
-    "JEditor/Commons/Ancestors/Views/ListView",
-    "text!../Templates/NewsList.html",
-    "i18n!../nls/i18n"
-  ], function (ListView, template, translate) {
-    var NewsListView = ListView.extend({
-        initialize: function() {
-            this.template = this.buildTemplate(template, translate);
-        },
-        render: function () {
-            this.$el.html(this.template());
-            return this;
-        }
-    });
-   
-    return NewsListView;
-  });
\ No newline at end of file

Property changes on: src/js/JEditor/NewsPanel/Views/NewsListView.js
___________________________________________________________________
Deleted: svn:executable
## -1 +0,0 ##
-*
\ No newline at end of property
