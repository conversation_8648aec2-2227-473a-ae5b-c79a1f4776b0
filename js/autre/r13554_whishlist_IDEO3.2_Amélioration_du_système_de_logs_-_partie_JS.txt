Revision: r13554
Date: 2024-12-03 11:49:44 +0300 (tlt 03 Des 2024) 
Author: rrakotoarinelina 

## Commit message
whishlist IDEO3.2 : Amélioration du système de logs - partie JS

## Files changed

## Full metadata
------------------------------------------------------------------------
r13554 | rrakotoarinelina | 2024-12-03 11:49:44 +0300 (tlt 03 Des 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/LogsPanel/Templates/Log.html
   M /branches/ideo3_v2/integration/src/js/JEditor/LogsPanel/Views/LogView.js

whishlist IDEO3.2 : Amélioration du système de logs - partie JS
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/LogsPanel/Templates/Log.html
===================================================================
--- src/js/JEditor/LogsPanel/Templates/Log.html	(révision 13553)
+++ src/js/JEditor/LogsPanel/Templates/Log.html	(révision 13554)
@@ -44,6 +44,19 @@
                     <span class="inline-data__sub-data">page :</span>
                     <span class="action-change"><%=page%></span>
                     <% } %>
+                    <% if ( pageRef ) { %>
+                        <span class="inline-data__sub-data">pageRef :</span>
+                        <span class="action-change"><%=pageRef%></span>
+                    <% } %>
+                    <% if ( pageLang ) { %>
+                        <span class="inline-data__sub-data">pageLang :</span>
+                        <span class="action-change"><%=pageLang%></span>
+                    <% } %>
+                    <% if ( composantType ) { %>
+                        <span class="inline-data__sub-data">composantType :</span>
+                        <span class="action-change"><%=composantType%></span>
+                    <% } %>
+
                 </p>
                 <% for(var i=0; i<changes.length; i++){%>
                 <p class="action-detail">
@@ -51,7 +64,7 @@
                         for(var key in change){ %>
                         <% if ( change[key] ) { %>
                     <span class="inline-data__sub-data"><%=key%> :</span>
-                    <span class="action-change"><%=change[key]%></span>
+                    <span class="action-change"><%-change[key]%></span>
                         <% } %>
                     <%
                     }
Index: src/js/JEditor/LogsPanel/Views/LogView.js
===================================================================
--- src/js/JEditor/LogsPanel/Views/LogView.js	(révision 13553)
+++ src/js/JEditor/LogsPanel/Views/LogView.js	(révision 13554)
@@ -21,20 +21,79 @@
             this.template = this.buildTemplate(template, translate);
         },
         render: function () {
+
+            var pageName  = null;
+            var pageRef = null;
+            var pageLang = null;
+            // pour la référence de page
+            var pageData  = this.parseAndValidate(this.model.page);
+            if (typeof pageData === 'object' && pageData !== null) {
+                pageName = pageData.pageName;
+                pageRef = pageData.pageRef;
+                pageLang = pageData.pageLang;
+            } else {
+                pageName = pageData;
+            }
+
+            var action  = (typeof this.model.changes[0].action != "undefined") && this.model.changes[0].action != null ?  this.model.changes[0].action : this.model.action;
             var date = moment(this.model.date);
             var params = {
                 user: this.model.user,
                 userMail: this.model.userMail,
                 module: this.model.module,
-                action: this.model.action,
+                action: action,
                 date: moment(date).format("DD/MM/YY"),
                 time: moment(date).format("HH:mm:ss"),
+                newValue: this.model.attributes.newValue,
+                oldValue: this.model.attributes.oldValue,
                 changes: this.model.changes,
-                page: this.model.page
+                page: this.model.action == "RENAME_PAGE" ? this.model.attributes.oldValue+'.php > ' +  this.model.attributes.newValue+'.php' : pageName,
+                pageRef : pageRef,
+                pageLang : pageLang,
+                composantType: this.model.attributes.composantType
             };
             this.$el.html(this.template(params));
             return this;
+        },
+        
+        /**
+         * Analyse et valide une chaîne de caractères représentant un objet page.
+         *
+         * Si la chaîne est un JSON valide et représente un objet avec les propriétés requises
+         * ('pageName', 'pageRef', 'pageLang'), retourne un objet avec ces propriétés.
+         * Sinon, retourne la chaîne d'origine.
+         *
+         * @param {string} str - La chaîne à analyser et valider
+         * @returns {object|string} - Un objet avec les propriétés requises, ou la chaîne d'origine
+         */
+
+        parseAndValidate: function(str) {
+            try {
+                var parsed = JSON.parse(str);
+                
+                if (!parsed || typeof parsed !== 'object' || Array.isArray(parsed)) {
+                    return str;
+                }
+                
+                if (parsed.hasOwnProperty('pageName')
+                    && parsed.hasOwnProperty('pageRef')
+                    && parsed.hasOwnProperty('pageLang')
+                    ) {
+                    return {
+                        pageName: parsed.pageName,
+                        pageRef: parsed.pageRef,
+                        pageLang: parsed.pageLang
+                    };
+                }
+                
+                return str;
+                
+            } catch (e) {
+                return str;
+            }
         }
+
+
     });
     return LogView;
 });
\ No newline at end of file
