Revision: r12548
Date: 2024-07-08 11:11:36 +0300 (lts 08 Jol 2024) 
Author: rrakotoarinelina 

## Commit message
whislist IDEO3.2 : Bug admin Safari 16.0 et antérieur

## Files changed

## Full metadata
------------------------------------------------------------------------
r12548 | rrakotoarinelina | 2024-07-08 11:11:36 +0300 (lts 08 Jol 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Models/Params.js

whislist IDEO3.2 : Bug admin Safari 16.0 et antérieur
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Models/Params.js
===================================================================
--- src/js/JEditor/ParamsPanel/Models/Params.js	(révision 12547)
+++ src/js/JEditor/ParamsPanel/Models/Params.js	(révision 12548)
@@ -7,10 +7,10 @@
     var GTMRegex=/^(GTM)-[a-zA-Z0-9]{1,}$/;
     var FBPixelRegex=/^[0-9]*$/;
     var FBDomainRegex=/^[^\s]{1,}$/;
-    var WTRegex=/^(?!['"]).*[^\s]+(?<!['"])$/;
-    var ItunesAppIdRegex=/^(?!['"]).*[0-9]*(?<!['"])$/;
+    var WTRegex=/^(?!['"]).*[^\s'"]$/;
+    var ItunesAppIdRegex=/^(?!['"]).*[0-9][^'"\s]*$/;
     var PVYoutubeIdRegex=/^[a-zA-Z0-9\-_]*$/;
-    var quotesRegex = /^(?!['"]).*(?<!['"])$/;
+    var quotesRegex = /^(?!['"]).*[^'"\s]$/;
     var RGPDLinkRegex=/^(http(s)?:\/\/www\.|http(s)?:\/\/)[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\#[a-z0-9]+)?(\/[^"'<>]*)?$/;
     var regExes = {
         airbnbUrl:      /^(http(s)?:\/\/)?(www\.)?airbnb\.(fr|com|ca|com\.au)\/(.*)/,
