Revision: r10206
Date: 2023-01-24 14:13:41 +0300 (tlt 24 Jan 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
 suite2 correction bug gestion de menu 

## Files changed

## Full metadata
------------------------------------------------------------------------
r10206 | srazanandralisoa | 2023-01-24 14:13:41 +0300 (tlt 24 Jan 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NavigationPanel/Views/MenuItemView.js

 suite2 correction bug gestion de menu 
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NavigationPanel/Views/MenuItemView.js
===================================================================
--- src/js/JEditor/NavigationPanel/Views/MenuItemView.js	(révision 10205)
+++ src/js/JEditor/NavigationPanel/Views/MenuItemView.js	(révision 10206)
@@ -250,7 +250,7 @@
                                 var collection = new MenuItemCollection();
                                 var hierachy = that.$el.nestedSortable('toHierarchy', {attribute: 'data-item', expression: /.*/});
                                 var depth = 0;
-                                var recurse = _.bind(function(hierarchy, collection, parent, noeud = false) {
+                                var recurse = _.bind(function(hierarchy, collection, parent, noeud ) {
                                     var menuItems = [];
                                     for (var i = 0; i < hierarchy.length; i++) {
                                         depth++;
