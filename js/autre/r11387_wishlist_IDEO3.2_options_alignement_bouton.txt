Revision: r11387
Date: 2023-10-09 11:35:25 +0300 (lts 09 Okt 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2 : options alignement bouton

## Files changed

## Full metadata
------------------------------------------------------------------------
r11387 | sraz<PERSON><PERSON><PERSON>oa | 2023-10-09 11:35:25 +0300 (lts 09 Okt 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonStyleOption.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js

wishlist IDEO3.2 : options alignement bouton
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonStyleOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonStyleOption.js	(révision 11386)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonStyleOption.js	(révision 11387)
@@ -6,7 +6,7 @@
 ], function(_,Events, AbstractOption) {
     var allowedSizes=["tiny","small","medium","large"];
     var allowedColors=["pastel","vibrante","contour","pastel-lead","vibrante-lead","contour-lead"];
-    var allowedButtonAlign=["align-left","align-center","align-right","full-width"];
+    var allowedButtonAlign=["","align-left","align-center","align-right","full-width"];
     var allowedTextAlign=["text-left","text-right","text-center"];
 
     /**
@@ -22,7 +22,7 @@
              * @lends ImageOptionss
              */
                     {
-                        defaults: {optionType: 'ButtonStyleOption', priority: 80, size:'medium', buttonAlignment:'align-center', textAlignment:'text-center', color:'pastel'},
+                        defaults: {optionType: 'ButtonStyleOption', priority: 80, size:'medium', buttonAlignment:'', textAlignment:'text-center', color:'pastel'},
                         initialize: function() {
                             this._super();
                         },
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js	(révision 11386)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js	(révision 11387)
@@ -10,7 +10,8 @@
                         tagName: "div",
                         className: "panel-content button-style-panel ",
                         events: {
-                            'click .button-color .effect-radio': "_onChangeColor"
+                            'click .button-color .effect-radio': "_onChangeColor",
+                            "click input[name='buttonAlignment']:checked" : "cancelButtonAligment"
                         },
                         /**
                          * initialise l'objet
@@ -27,6 +28,18 @@
                             this.$(".button-textAlignment").toggleClass("hidden",this.model.buttonAlignment != "full-width");
                             this.$el.mCustomScrollbar('update');
                         },
+                         /**
+                         * affichage de l'alignement du texte
+                         */
+                         cancelButtonAligment:function (event) {
+                            var $target = $(event.currentTarget);
+                          if ($target.attr("value") == this.model.buttonAlignment) {
+                            this.model.buttonAlignment = "";
+                            $target.removeAttr('checked');
+
+                          }
+                           
+                        },
                         /**
                          * action changer le color du boutton
                          * @param {*} event 
