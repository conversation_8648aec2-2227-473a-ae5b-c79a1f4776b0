Revision: r14162
Date: 2025-04-25 15:19:13 +0300 (zom 25 Apr 2025) 
Author: mpartaux 

## Commit message
revamp categories list

## Files changed

## Full metadata
------------------------------------------------------------------------
r14162 | mpartaux | 2025-04-25 15:19:13 +0300 (zom 25 Apr 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Templates/categorieList.html
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/CategorieCollectionView.js
   M /branches/ideo3_v2/integration/src/less/imports/news_panel/main.less

revamp categories list
------------------------------------------------------------------------

## Diff
Index: src/less/imports/news_panel/main.less
===================================================================
--- src/less/imports/news_panel/main.less	(révision 14161)
+++ src/less/imports/news_panel/main.less	(révision 14162)
@@ -93,6 +93,17 @@
 	color: #ddd;
 }
 .categorie-nav-list{
+
+	display: flex;
+	justify-content: space-between;
+	align-items: center;
+	padding: 7px 15px 7px 0px;
+	border-bottom: 1px solid #bcbebd;
+
+	&:hover {
+		background: #bcbebd;
+	}
+
 	&.edit{
 		background-color: #fff;
 	}
@@ -99,44 +110,71 @@
 	a {
 	  cursor: pointer;
 	  position: relative;
-	  display: block;
+	  //display: block;
 	  text-decoration: none;
 	  font-family: 'Raleway', sans-serif;
 	  font-size: 14px;
+	  line-height: 16px;
 	  font-weight: 400;
-	  padding: 11px 55px 11px 31px;
-	  text-overflow: ellipsis;
+	  //padding: 11px 55px 11px 31px;
+	  //text-overflow: ellipsis;
 	  .transition(background 300ms ease);
+	  //overflow: hidden;
+	  //border-bottom: 1px solid #bcbebd;
+	  display: -webkit-box;
+	  -webkit-line-clamp: 2;
+	  -webkit-box-orient: vertical;
 	  overflow: hidden;
-	  border-bottom: 1px solid #bcbebd;
-	  }
+	  flex-grow: 1;
+	  padding: 2px 2px 2px 15px;
+	}
 	  
 	.action {
 		text-align: right;
-		position: absolute;
-		right: 10px;
-		top: 10px;
-		width: 60px;
-		height: 10px;
-		line-height: 10px;
+		//position: absolute;
+		//right: 10px;
+		//top: 10px;
+		//width: 60px;
+		//height: 10px;
+		//line-height: 10px;
+
+		flex-shrink: 0;
+		display: flex;
+		gap: 3px;
 		.transition(opacity 300ms ease);
+
 		.number {
 			background: @greyM;
-			padding: 3px 9px;
+			//padding: 3px 9px;
 			color:@greyXL ;
 			border-radius: 100%;
 			font-size: small;
+
+			width: 22px;
+			height: 22px;
+			display: flex;
+			justify-content: center;
+			align-items: center;
 		}
+
 		.icon{
-			display: inline-block;
-			font-size: 10px;
+			cursor: pointer;
+			//display: inline-block;
+			//font-size: 10px;
 			background: @newsColorLight;
 			color:@greyXL ;
-			padding: 5px 5px;
+			//padding: 5px 5px;
 			border-radius: 100%;
 			font-size: small;
+
+			width: 22px;
+			height: 22px;
+			display: flex;
+			justify-content: center;
+			align-items: center;
+
 			&:hover {
-				color: #d42525;
+				background: @newsColorDark;
 			  }
 		}
 	  }
Index: src/js/JEditor/NewsPanel/Templates/categorieList.html
===================================================================
--- src/js/JEditor/NewsPanel/Templates/categorieList.html	(révision 14161)
+++ src/js/JEditor/NewsPanel/Templates/categorieList.html	(révision 14162)
@@ -5,18 +5,16 @@
                 <li class="categorie-nav-list <%= categorie==current?'edit':''%> ">
                     <% if(categorie.id == 'uncategorized'){%>
                         <% if(categorie.lang[currentlang].nbArticle > 0){%>
-                        <a data-cid="<%= categorie.cid %>" > <%= categorie.lang[currentlang].title %>
-                            <span class="action">
-                                <span class="number"><%= categorie.lang[currentlang].nbArticle %></span>
-                            </span>
-                        </a>
-                    <% } } else {%>
-                    <a data-cid="<%= categorie.cid %>" > <%= categorie.lang[currentlang].title %>
+                        <a data-cid="<%= categorie.cid %>" > <%= categorie.lang[currentlang].title %> </a>
                         <span class="action">
                             <span class="number"><%= categorie.lang[currentlang].nbArticle %></span>
-                            <span class="icon icon-add" data-model="<%=categorie.cid%>"></span>
                         </span>
-                    </a>
+                    <% } } else {%>
+                    <a data-cid="<%= categorie.cid %>" > <%= categorie.lang[currentlang].title %> </a>
+                    <span class="action">
+                        <span class="number"><%= categorie.lang[currentlang].nbArticle %></span>
+                        <span class="icon icon-add" data-model="<%=categorie.cid%>"></span>
+                    </span>
                 </li>
                 <% }
             });%>
Index: src/js/JEditor/NewsPanel/Views/CategorieCollectionView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/CategorieCollectionView.js	(révision 14161)
+++ src/js/JEditor/NewsPanel/Views/CategorieCollectionView.js	(révision 14162)
@@ -23,7 +23,7 @@
       },
       events: {
         'click ul li.categorie-nav-list a[data-cid]': '_onCategorieClick',
-        'click ul li.categorie-nav-list a .icon-add' : 'addArticleClick'
+        'click ul li.categorie-nav-list span.icon-add' : 'addArticleClick'
       },
       language: null,
       fadeInEffect: 'fadeIn',
