Revision: r11314
Date: 2023-09-21 08:05:24 +0300 (lkm 21 Sep 2023) 
Author: rrakotoarinelina 

## Commit message
Bug RS : pas de title Whatsapp

## Files changed

## Full metadata
------------------------------------------------------------------------
r11314 | rrakotoarinelina | 2023-09-21 08:05:24 +0300 (lkm 21 Sep 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Models/Params.js

Bug RS : pas de title Whatsapp
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Models/Params.js
===================================================================
--- src/js/JEditor/ParamsPanel/Models/Params.js	(révision 11313)
+++ src/js/JEditor/ParamsPanel/Models/Params.js	(révision 11314)
@@ -7,10 +7,14 @@
     var GTMRegex=/^(GTM)-[a-zA-Z0-9]{1,}$/;
     var FBPixelRegex=/^[0-9]*$/;
     var FBDomainRegex=/^[^\s]{1,}$/;
-    var WTRegex=/^[^\s]{1,}$/;
-    var ItunesAppIdRegex=/^[0-9]*$/;
+    // var WTRegex=/^[^\s]{1,}$/;
+    var WTRegex=/^(?!['"]).*[^\s]+(?<!['"])$/;
+    // var ItunesAppIdRegex=/^[0-9]*$/;
+    var ItunesAppIdRegex=/^(?!['"]).*[0-9]*(?<!['"])$/;
     var PVYoutubeIdRegex=/^[a-zA-Z0-9\-_]*$/;
-    var RGPDLinkRegex=/^(http(s)?:\/\/www\.|http(s)?:\/\/){1}[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\#[a-z0-9]+)?(\/.*)?$/;
+    var quotesRegex = /^(?!['"]).*(?<!['"])$/;
+    // var RGPDLinkRegex=/^(http(s)?:\/\/www\.|http(s)?:\/\/){1}[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\#[a-z0-9]+)?(\/.*)?$/;
+    var RGPDLinkRegex=/^(http(s)?:\/\/www\.|http(s)?:\/\/)[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\#[a-z0-9]+)?(\/[^"'<>]*)?$/;
     var regExes = {
         facebookUrl:/^(https?\:\/\/)?(www\.)?facebook\.com\/(.*)/,
         twitterUrl:/^(https?\:\/\/)?(www\.)?twitter\.com\/(?:#!\/)?([a-zA-Z0-9\._-]+)/,
@@ -28,6 +32,7 @@
         wazeUrl:/^(https?\:\/\/)?(www\.)?waze\.com\/(.*)/,
         whatsappUrl:/^(https?\:\/\/)?(www\.)?(api\.whatsapp\.com|wa\.me)\/(.*)/,
         slideshareUrl:/^(https?\:\/\/)?(www\.)?((fr|de|es|pt)\.)?slideshare\.net\/(.*)/,
+        quotes:/^(https?:\/\/)?(www\.)?(?!.*['"]).+\/(.*)$/
     };  
     //verification for google fonts
 
@@ -89,12 +94,17 @@
         validateSocialNetworks:function (attributes) {
             if (attributes) { 
                  var type = attributes.type ;
-                 if (attributes.url && (!attributes.url.match || !attributes.url.match(regExes[type])))
+                 console.log("attributes.url.match(regExes['quotes']");
+                 console.log(attributes.url.match(regExes['quotes']));
+                 if (attributes.url && (!attributes.url.match || !attributes.url.match(regExes[type]) || !attributes.url.match(regExes['quotes'])  ))
                     return false;
                  else return true;
             }
         },
         validate: function (attributes, options) {
+            
+       
+
             var socialNetworks = ["facebookUrl", "mybusinessUrl", "twitterUrl", "pinterestUrl", "youtubeUrl", "linkedinUrl", "viadeoUrl", "instagramUrl","skypeUrl","theforkUrl","tiktokUrl","tripadvisorUrl","wazeUrl","whatsappUrl","slideshareUrl"];
             //en gros c'est ça dans une boucle:
             for (var i = 0; i < socialNetworks.length; i++) {             
@@ -101,19 +111,19 @@
                 if (attributes[socialNetworks[i]] && (!attributes[socialNetworks[i]].match || !attributes[socialNetworks[i]].match(regExes[socialNetworks[i]])))
                     return {field: socialNetworks[i], message: translate("Invalid_" + socialNetworks[i])};
             }
-            if(attributes.GAKey && (!attributes.GAKey.match || !attributes.GAKey.match(GARegex)))
+            if(attributes.GAKey && (!attributes.GAKey.match || !attributes.GAKey.match(GARegex) ))
                 return {field: "GAKey", message: translate("Invalid_GAKey")};
             if(attributes.GTMKey && (!attributes.GTMKey.match || !attributes.GTMKey.match(GTMRegex)))
                 return {field: "GTMKey", message: translate("Invalid_GTMKey")};
-            if(attributes.FBPixelKey && (!attributes.FBPixelKey.match || !attributes.FBPixelKey.match(FBPixelRegex)))
+            if(attributes.FBPixelKey && (!attributes.FBPixelKey.match || !attributes.FBPixelKey.match(FBPixelRegex)|| !attributes.FBPixelKey.match(quotesRegex)))
                 return {field: "FBPixelKey", message: translate("Invalid_FBPixelKey")};
-            if(attributes.FBDomainVerification && (!attributes.FBDomainVerification.match || !attributes.FBDomainVerification.match(FBDomainRegex)))
+            if(attributes.FBDomainVerification && (!attributes.FBDomainVerification.match || !attributes.FBDomainVerification.match(FBDomainRegex)|| !attributes.FBDomainVerification.match(quotesRegex)))
                 return {field: "FBDomainVerification", message: translate("Invalid_FBDomainVerification")};
             if(attributes.WTKey && (!attributes.WTKey.match || !attributes.WTKey.match(WTRegex)))
-                return {field: "WTKey", message: translate("Invalid_WTKey")};
+                return {field: "WTKey", message: translate("Invalid_WTKey")}; 
             if(attributes.ItunesAppId && (!attributes.ItunesAppId.match || !attributes.ItunesAppId.match(ItunesAppIdRegex)))
                 return {field: "ItunesAppId", message: translate("Invalid_ItunesAppId")};
-            if(attributes.PVYoutubeId && (!attributes.PVYoutubeId.match || !attributes.PVYoutubeId.match(PVYoutubeIdRegex)))
+            if(attributes.PVYoutubeId && (!attributes.PVYoutubeId.match || !attributes.PVYoutubeId.match(PVYoutubeIdRegex)|| !attributes.PVYoutubeId.match(quotesRegex)))
                 return {field: "PVYoutubeId", message: translate("Invalid_PVYoutubeId")};
             if(attributes.RGPDLink && (!attributes.RGPDLink.match || !attributes.RGPDLink.match(RGPDLinkRegex)))
                 return {field: "RGPDLink", message: translate("Invalid_RGPDLink")};
