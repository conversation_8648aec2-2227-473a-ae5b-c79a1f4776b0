Revision: r10336
Date: 2023-02-06 08:23:30 +0300 (lts 06 Feb 2023) 
Author: jn.harison 

## Commit message
Wishlist IDEO3.2 debug: Rendering svg as a whole just after upload

## Files changed

## Full metadata
------------------------------------------------------------------------
r10336 | jn.harison | 2023-02-06 08:23:30 +0300 (lts 06 Feb 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Views/MarqueClientView.js

Wishlist IDEO3.2 debug: Rendering svg as a whole just after upload
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Views/MarqueClientView.js
===================================================================
--- src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 10335)
+++ src/js/JEditor/ParamsPanel/Views/MarqueClientView.js	(révision 10336)
@@ -128,18 +128,20 @@
         
         render: function () {
             this.$el.html(this._template(this.model.toJSON()));
-            if(this.model.attributes.Logo && (!Array.isArray(this.model.attributes.Logo)))
+            var attrs = this.model.attributes;
+            if(attrs.Logo && (!Array.isArray(attrs.Logo)))
             {
-                this.currentFileLogo = this.model.attributes.Logo;
+                this.currentFileLogo = attrs.Logo;
                 this.currentFileLogo = new File(this.currentFileLogo);
             }
             else
             {
                 this.currentFileLogo = null;
+                console.log('or here');
             }
-            if(this.model.attributes.LogoSmall && (!Array.isArray(this.model.attributes.LogoSmall)))
+            if(attrs.LogoSmall && (!Array.isArray(attrs.LogoSmall)))
             {
-                this.currentFileLogoSmall = this.model.attributes.LogoSmall;
+                this.currentFileLogoSmall = attrs.LogoSmall;
                 this.currentFileLogoSmall = new File(this.currentFileLogoSmall);
             }
             else
@@ -146,9 +148,9 @@
             {
                 this.currentFileLogoSmall = null;
             }
-            if(this.model.attributes.Favicon && (!Array.isArray(this.model.attributes.Favicon)))
+            if(attrs.Favicon && (!Array.isArray(attrs.Favicon)))
             {
-                this.currentFileFavicon = this.model.attributes.Favicon;
+                this.currentFileFavicon = attrs.Favicon;
                 this.currentFileFavicon = new File(this.currentFileFavicon);
             }
             else
@@ -155,7 +157,6 @@
             {
                 this.currentFileFavicon = null;
             }
-           
               //Logo
               this.fileUploader = new FileUploaderView({
                 currentFile: this.currentFileLogo,
@@ -206,7 +207,7 @@
 			this.fileUploader.render();
             this.fileUploader2.render();
             this.fileUploader3.render();
-            if(this.model.attributes.Logo && (!Array.isArray(this.model.attributes.Logo))){
+            if(attrs.Logo && (!Array.isArray(attrs.Logo))){
                 this.$('.group-content-logo .uploader').addClass('done');
                 this.$('.menu-wrapper-logo .uploader .preview .imagepreview').css({opacity: 1});
                 this.$('.group-content-logo .rigth-delete-image').show();   
@@ -215,7 +216,7 @@
                     this.$('.menu-wrapper-logo .uploader .preview .imagepreview').css({backgroundRepeat:'no-repeat'});
                 }             
             }
-            if(this.model.attributes.LogoSmall && (!Array.isArray(this.model.attributes.LogoSmall))){
+            if(attrs.LogoSmall && (!Array.isArray(attrs.LogoSmall))){
                 this.$('.group-content-logo-small .uploader').addClass('done');
                 this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview').css({opacity: 1});
                 this.$('.group-content-logo-small .rigth-delete-image').show();
@@ -224,7 +225,7 @@
                     this.$('.menu-wrapper-logoSmall .uploader .preview .imagepreview').css({backgroundRepeat:'no-repeat'});
                 }       
             }
-            if(this.model.attributes.Favicon && (!Array.isArray(this.model.attributes.Favicon))){
+            if(attrs.Favicon && (!Array.isArray(attrs.Favicon))){
                 this.$('.group-content-favicon .uploader').addClass('done');
                 this.$('.menu-wrapper-favicon .uploader .preview .imagepreview').css({opacity: 1});
                 this.$('.group-content-favicon .rigth-delete-image').show();
