Revision: r11692
Date: 2023-12-07 16:39:02 +0300 (lkm 07 Des 2023) 
Author: mpartaux 

## Commit message
update page models text

## Files changed

## Full metadata
------------------------------------------------------------------------
r11692 | mpartaux | 2023-12-07 16:39:02 +0300 (lkm 07 Des 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/nls/i18n.js

update page models text
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/nls/fr-ca/i18n.js	(révision 11691)
+++ src/js/JEditor/PagePanel/nls/fr-ca/i18n.js	(révision 11692)
@@ -104,8 +104,10 @@
     "Landing page":"Landing page",
     "landingPage01Name":"Landing page 01",
     "landingPage01Desc":"Landing page classique",
-	"landingPage01SupportName":"LP Support",
-	"landingPage01SupportDesc":"Modèle LP Support",
+	"landingPageSupport01Name":"LP Support",
+	"landingPageSupport01Desc":"Modèle LP Support",
+    "landingPageSEA01Name":"Landing page SEA 01",
+    "landingPageSEA01Desc":"Template Lp SEA",
     "successImportAction" : "Import effectuée avec succès",
     "failedImportAction": "Le modèle de page n'a pas pu être importé",
     "previousVersionSuccesful":"La zone a été restaurée avec succès et <br/>la page a été sauvegardée",
Index: src/js/JEditor/PagePanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/nls/fr-fr/i18n.js	(révision 11691)
+++ src/js/JEditor/PagePanel/nls/fr-fr/i18n.js	(révision 11692)
@@ -109,8 +109,10 @@
     "Landing page":"Landing page",
     "landingPage01Name":"Landing page 01",
     "landingPage01Desc":"Landing page classique",
-	"landingPage01SupportName":"LP Support",
-	"landingPage01SupportDesc":"Modèle LP Support",
+	"landingPageSupport01Name":"LP Support",
+	"landingPageSupport01Desc":"Modèle LP Support",
+    "landingPageSEA01Name":"Landing page SEA 01",
+    "landingPageSEA01Desc":"Template Lp SEA",
     "successImportAction" : "Import effectuée avec succès",
     "failedImportAction": "Le modèle de page n'a pas pu être importé",
     "previousVersionSuccesful":"La zone a été restaurée avec succès et <br/>la page a été sauvegardée",
Index: src/js/JEditor/PagePanel/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/nls/i18n.js	(révision 11691)
+++ src/js/JEditor/PagePanel/nls/i18n.js	(révision 11692)
@@ -110,8 +110,10 @@
         "Landing page":"Landing page",
         "landingPage01Name":"Landing page 01",
         "landingPage01Desc":"Classic landing page",
-		"landingPage01SupportName":"LP Support",
-		"landingPage01SupportDesc":"LP Support",
+		"landingPageSupport01Name":"LP Support",
+		"landingPageSupport01Desc":"LP Support",
+        "landingPageSEA01Name":"Landing page SEA 01",
+		"landingPageSEA01Desc":"Lp SEA template",
         "previousVersionSuccesful":"The content has been successfully restored and the page has been saved",
         "deleteContact": "Please set a different contact page <br\/> before you can delete it",
         "deleteLegale": "Please set a different Legal page <br\/> before you can delete it",
