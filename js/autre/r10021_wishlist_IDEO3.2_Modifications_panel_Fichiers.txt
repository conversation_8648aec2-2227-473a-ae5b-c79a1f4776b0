Revision: r10021
Date: 2022-12-20 09:38:39 +0300 (tlt 20 Des 2022) 
Author: sraz<PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2 : Modifications panel Fichiers

## Files changed

## Full metadata
------------------------------------------------------------------------
r10021 | srazanandralisoa | 2022-12-20 09:38:39 +0300 (tlt 20 Des 2022) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileDetail.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileDetailView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/filePanel.less

wishlist IDEO3.2 : Modifications panel Fichiers
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/FilePanel/Templates/fileDetail.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileDetail.html	(révision 10020)
+++ src/js/JEditor/FilePanel/Templates/fileDetail.html	(révision 10021)
@@ -112,12 +112,15 @@
             <% if(user.can('display_file_url')){ %>
                 <%if(isImg){%>
                     <div class="shortcode">ressources/images/<%=name%></div>
+                    <div class="shortcode">[[img_<%=id%>]]</div>
                 <% }else if(isVideo){%>
                     <div class="shortcode">ressources/videos/<%=name%></div>
+                    <div class="shortcode">[[video_<%=id%>]]</div>
                 <% }else {%>
                     <div class="shortcode">ressources/fichiers/<%=name%></div>
                 <%}%>
             <% } %>
+            <%if(isImg){%>
             <!-- Choix de la langue -->
             <div class="btn-group lang">
                 <a class="btn dropdown-toggle lang-dropdown-toggle" href="#">
@@ -140,6 +143,7 @@
             <input value="<%=_.escape(title[lang.id])%>" class="fileTitle" placeholder="<%= __("imageTitle")%>"></input>
             <input value="<%=_.escape(desc[lang.id])%>" class="fileDesc" placeholder="<%= __("imageDesc")%>"></input><br>
             <div class="linkBlock"></div>
+            <% } %>
         </div><!-- end .edit-image -->
     </div>
 
Index: src/js/JEditor/FilePanel/Views/FileDetailView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileDetailView.js	(révision 10020)
+++ src/js/JEditor/FilePanel/Views/FileDetailView.js	(révision 10021)
@@ -32,6 +32,7 @@
                     'change input.fileTitle': 'setTitle',
                     'change input.fileDesc': 'setDesc',
                     'click [data-language]': '_onLangClick',
+                    'click .shortcode' : 'copyToClipboard'
                 },
                 lang: null,
                 attributes: {
@@ -116,11 +117,37 @@
                     this.dom[this.cid].langDropdownWrapper.removeClass('open'); // on referme le dropdown
                     return false;
                 },
-              //  clickradio: function(event) { 
                 clickradio: function(link) { 
                     this.model.link[this.lang]=link
                     this.model.save();
                 },
+                copyToClipboard : function (e){
+                    var copyText = $(e.currentTarget).text();
+                    const clipboard = navigator.clipboard;
+                    if (clipboard !== undefined && clipboard !== "undefined") {
+                        navigator.clipboard.writeText(copyText).then(this.successfully($(e.currentTarget)));
+                    } else {
+                        if (document.execCommand) {
+                        const el = document.createElement("input");
+                        el.value = copyText;
+                        document.body.append(el);
+
+                        el.select();
+                        el.setSelectionRange(0, value.length);
+
+                        if (document.execCommand("copy")) {
+                            this.successfully();
+                        }
+                        el.remove();
+                        }
+                    }
+                },
+                successfully :function (el){
+                    el.before('<div role="alert" aria-live="polite" style="left: 75%; top: auto;position: absolute;" class="toastcopy"><div class="jq-toast-single jq-icon-success" style="text-align: left; /*! display: none; */"><span class="icon icon-check-circle"></span><span>'+translate("copy")+'</span></div></div>')
+                    window.setTimeout(function(){
+                        el.parent().find('.toastcopy').remove();
+                    }, 2000);
+                }
             });
             Events.extend({
                 FileDetailManagerViewEvents: {
Index: src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 10020)
+++ src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 10021)
@@ -123,5 +123,5 @@
     "selectPage":"Sélectionnez une page",
     "introUrl" : "Ajouter une url utilisable dans une galerie (data-url)",
     "imageVideo": "C'est un fichier vidéo",
-    
+    "copy":"Copié",
 });
\ No newline at end of file
Index: src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 10020)
+++ src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 10021)
@@ -127,4 +127,5 @@
     "locked" : "Verrouilée",
     "introUrl" : "Ajouter une url utilisable dans une galerie (data-url)",
     "imageVideo": "C'est un fichier vidéo",
+    "copy":"Copié",
 });
\ No newline at end of file
Index: src/js/JEditor/FilePanel/nls/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/i18n.js	(révision 10020)
+++ src/js/JEditor/FilePanel/nls/i18n.js	(révision 10021)
@@ -129,6 +129,7 @@
         "locked" : "Locked",
         "introUrl" : "Add a url usable in a gallery (data-url)",
         "imageVideo": "C'est un fichier vidéo",
+        "copy":"Copied",
     },
     "fr-fr": true, "fr-ca":true
 })
\ No newline at end of file
Index: src/less/imports/filePanel.less
===================================================================
--- src/less/imports/filePanel.less	(révision 10020)
+++ src/less/imports/filePanel.less	(révision 10021)
@@ -1656,6 +1656,10 @@
     padding: 0 15px;
     margin-top: 1px;
     margin-bottom: 15px;
+    white-space: nowrap;
+    overflow: hidden;
+    text-overflow: ellipsis !important;
+    cursor: pointer !important;
 }
 
 /* TITRE & DESCRIPTION DE L'IMAGE */
