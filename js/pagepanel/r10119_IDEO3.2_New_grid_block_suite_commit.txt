Revision: r10119
Date: 2023-01-10 08:38:44 +0300 (tlt 10 Jan 2023) 
Author: norajaonarivelo 

## Commit message
IDEO3.2 New grid block (suite commit )

## Files changed

## Full metadata
------------------------------------------------------------------------
r10119 | norajaonarivelo | 2023-01-10 08:38:44 +0300 (tlt 10 Jan 2023) | 1 ligne
Chemins modifiés :
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridOptionTypeLien.html

IDEO3.2 New grid block (suite commit )
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridOptionTypeLien.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridOptionTypeLien.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridOptionTypeLien.html	(révision 10119)
@@ -0,0 +1,31 @@
+<p class="panel-legend"><%=__("selectTypeLink")%></p>
+<% var _id= "link_type1" %>
+<input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkType" value="1"  <%=(TypeLien==1)?"checked":""%> />
+<label  class="block-label" for="<%= _id %>" >
+    <div class="radio-wrapper">
+    <span class="icon  icon-radio-active"></span>
+    <span class="icon  icon-radio-inactive"></span>
+    </div>
+    <div class="block-label-radio"><%= __("LinkImage")%></div>
+
+</label>
+<% var _id= "link_type2" %>
+<input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkType" value="2"  <%=(TypeLien==2)?"checked":""%>/>
+<label  class="block-label <%=(LinkText)?'carrouselTypeLinkDisable':''%>" for="<%= _id %>" >
+    <div class="radio-wrapper">
+    <span class="icon  icon-radio-active"></span>
+    <span class="icon  icon-radio-inactive"></span>
+    </div>
+    <div class="block-label-radio"><%= __("LinkText")%></div>
+
+</label>
+<% var _id= "link_type3" %>
+<input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkType" value="3"  <%=(TypeLien==3)?"checked":""%> />
+<label  class="block-label <%=(BouttonMoreInfo)?'carrouselTypeLinkDisable':''%>" for="<%= _id %>" >
+    <div class="radio-wrapper">
+    <span class="icon  icon-radio-active"></span>
+    <span class="icon  icon-radio-inactive"></span>
+    </div>
+    <div class="block-label-radio"><%= __("ButtonReadMore")%></div>
+
+</label>
\ No newline at end of file
