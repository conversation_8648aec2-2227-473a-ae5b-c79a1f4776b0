Revision: r10152
Date: 2023-01-17 07:59:04 +0300 (tlt 17 Jan 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: affets/styles block image et tag (partie JS)

## Files changed

## Full metadata
------------------------------------------------------------------------
r10152 | srazanandralisoa | 2023-01-17 07:59:04 +0300 (tlt 17 Jan 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/ImageBlock.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Models/ImageStyles.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Models/main.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Templates/imageStyles.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageStylesView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/main.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/i18n.js

wishlist IDEO3.2: affets/styles block image et tag (partie JS)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/ImageBlock.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/ImageBlock.js	(révision 10151)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/ImageBlock.js	(révision 10152)
@@ -1,12 +1,12 @@
 define([
     "JEditor/PagePanel/Contents/Blocks/ImageBlock/ImageBlock",
     "JEditor/PagePanel/Contents/Blocks/Block/Block",
-    "JEditor/PagePanel/Contents/Options/Models/EffectsOption",
-    "JEditor/PagePanel/Contents/Blocks/ImageBlock/Models/ImageOptions"
+    "JEditor/PagePanel/Contents/Blocks/ImageBlock/Models/ImageOptions",
+    "JEditor/PagePanel/Contents/Blocks/ImageBlock/Models/ImageStyles"
 ], function(ImageBlock,
         Block,
-        EffectsOption,
-        ImageOptions
+        ImageOptions,
+        ImageStyles
         ) {
     var /**
      * @class ImageBlock
@@ -23,8 +23,8 @@
                                  */
                                 initialize: function() {
                                     this._super();
-                                    if (!this.options.effects)
-                                        this.options.add(new EffectsOption());
+                                    if (!this.options.imageStyles)
+                                        this.options.add(new ImageStyles());
                                     if (!this.options.image)
                                         this.options.add(new ImageOptions());
                                 }
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Models/ImageStyles.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Models/ImageStyles.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Models/ImageStyles.js	(révision 10152)
@@ -0,0 +1,29 @@
+define( [
+    "underscore",
+    "JEditor/PagePanel/Contents/Options/Models/AbstractOption",
+    "i18n!../nls/i18n"
+], function(_, AbstractOption, translate) {
+    /**
+     * 
+     * @class ImageStyles
+     * @extends AbstractOption
+     * @todo revoir les méthodes get/set
+     */
+     var ImageStyles = AbstractOption.extend(
+                {
+                    translate: translate,
+                    defaults: {optionType: 'imageStyles', priority: 80, style:'amb'},
+                    initialize: function() {
+                        this._id = this.cid;
+                        this._super();
+                    },
+                    setStyle: function(value) {
+                        this.style = value;
+                    },
+                    getStyle: function() {
+                       return this.style;
+                    }
+                });
+                ImageStyles.SetAttributes(['style']);
+                return ImageStyles;
+            });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Models/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Models/main.js	(révision 10151)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Models/main.js	(révision 10152)
@@ -1,7 +1,8 @@
 // this file has been auto-generated by a grunt task, it will be overriden, do not modify it
-define(["./ImageOptions"],function(ImageOptions){
+define(["./ImageOptions","./ImageStyles"],function(ImageOptions,ImageStyles){
     var comp={
-        "ImageOptions":ImageOptions
+        "ImageOptions":ImageOptions,
+        "ImageStyles":ImageStyles,
     };
     return comp;
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Templates/imageStyles.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Templates/imageStyles.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Templates/imageStyles.html	(révision 10152)
@@ -0,0 +1,50 @@
+<style>
+   .image-style .option-content .wrapper{
+    width: 33.33333333%;
+    float: left;
+    text-align: center;
+   } 
+
+</style>
+<div class="panel-option-container animated">
+    <article class="panel-option image-style" >
+        <header>
+            <h3 class="option-name"><span class="icon-stars"></span><%= __("imagestyle")%></h3>
+            <p class="panel-content-legend"><%= __("imageStyleDesc")%></p>
+        </header>
+        <div class="option-content">
+            <div class="wrapper">
+                <!--switch-->
+                <span class="effect-radio  <%=(style=='amb')? 'active':''%>" id="radio_amb_<%= _id %>"class="style-amb" data-value="amb">
+                    <span class="helper">
+                        <span class="help"><%= __("styleAmb")%></span>
+                        <span class="bottom"></span>
+                        </span>
+                        <span class="container">
+                            <span class="icon icon-stars"></span>
+                            <span class="switch-container">
+                                <span class="radio">
+                            </span>
+                        </span>
+                    </span>
+                </span>
+            </div>
+            <div class="wrapper">
+                <!--switch border dashed-->
+                    <span class="effect-radio <%=(style=='none')? 'active':''%>" id="radio_none_<%= _id %>" class="style-none" data-value="none" >
+                    <span class="helper">
+                        <span class="help"><%= __("styleNone")%></span>
+                        <span class="bottom"></span>
+                        </span>
+                        <span class="container">
+                            <span class="icon icon-delete"></span>
+                            <span class="switch-container">
+                                <span class="radio">
+                            </span>
+                        </span>
+                    </span>
+                </span>
+            </div>
+        </div>
+    </article>
+</div>
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageStylesView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageStylesView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageStylesView.js	(révision 10152)
@@ -0,0 +1,76 @@
+define( [
+    "jquery",
+    "underscore",
+    "text!../Templates/imageStyles.html",
+    "JEditor/PagePanel/Contents/Options/Views/AbstractOptionView",
+    "i18n!../nls/i18n",
+    //not in params
+    "jqueryPlugins/radio",
+    "mCustomScrollbar",
+    "jqueryPlugins/switcher"
+], function(
+        $,
+        _,
+        imageStyles,
+        AbstractOptionView,
+        translate
+        ) {
+    var /**
+     * Vues des options d'effets utilisée par les blocs, colonnes et sections
+     * @class ImageStyleView
+     * @extends AbstractOptionView
+     */
+    ImageStylesView = AbstractOptionView.extend(
+            /**
+             * @lends ImageStyleView.prototype
+             */
+                    {
+                        optionType: 'imageStyles',
+                        tagName: "div",
+                        className: 'panel-content fx-panel imageStyle',
+                        translate: translate,
+                        events: {
+                            'click .effect-radio': '_onRadioChange',
+                        },
+                        /**
+                         * initialise la vue
+                         */
+                        initialize: function() {
+                            this._super();
+                            this._template = this.buildTemplate(imageStyles, translate);
+                        },
+                        /**
+                         * actualise l'affichage
+                         */
+                        render: function() {
+                            if (this.dom[this.cid].radios) {
+                                try {
+                                    this.dom[this.cid].radios.radio('destroy');
+                                } catch (e) {
+                                }
+                            }
+                           console.log(this.model.toJSON());
+                            this.$el.html(this._template(this.model.toJSON()));
+                            //radios
+                            this.dom[this.cid].radios = this.$('.panel-option-container.image-style').radio(
+                                { 
+                                    group: this.$('.panel-option'), 
+                                    value: this.model.getStyle()
+                                });
+                            this.scrollables();
+                            return this;
+                        },
+                        /**
+                         * changement des style de bordure via clic sur pseudo radio
+                         * @private
+                         */
+                        _onRadioChange: function(event) {
+                            this.$(".effect-radio").removeClass("active");
+                            var $target = $(event.currentTarget);
+                            $target.addClass("active");
+                            var value = $target.attr("data-value");
+                            this.model.setStyle(value);
+                        },
+                    });
+            return ImageStylesView;
+        });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/main.js	(révision 10151)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/main.js	(révision 10152)
@@ -1,8 +1,9 @@
 // this file has been auto-generated by a grunt task, it will be overriden, do not modify it
-define(["./ImageBlockView","./ImageOptionsView"],function(ImageBlockView,ImageOptionsView){
+define(["./ImageBlockView","./ImageOptionsView","./ImageStylesView"],function(ImageBlockView,ImageOptionsView,ImageStylesView){
     var comp={
         "ImageBlockView":ImageBlockView,
-        "ImageOptionsView":ImageOptionsView
+        "ImageOptionsView":ImageOptionsView,
+        "ImageStylesView":ImageStylesView,
     };
     return comp;
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-ca/i18n.js	(révision 10151)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-ca/i18n.js	(révision 10152)
@@ -15,4 +15,9 @@
   "figShowTitle": "Afficher le titre",
   "figShowAll":"Afficher le titre et la description",
   "figinactive":"Ne rien afficher",
+  "imagestyle" : "Style de l'image",
+  "imageStyleDesc" :"Appliquez un style d'image",
+  "styleAmb" : "Style de l'ambiance",
+  "styleNone": "Aucun style",
+  "imageStyles" :"Style",
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-fr/i18n.js	(révision 10151)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-fr/i18n.js	(révision 10152)
@@ -15,4 +15,9 @@
   "figShowTitle": "Afficher le titre",
   "figShowAll":"Afficher le titre et la description",
   "figinactive":"Ne rien afficher",
+  "imagestyle" : "Style de l'image",
+  "imageStyleDesc" :"Appliquez un style d'image",
+  "styleAmb" : "Style de l'ambiance",
+  "styleNone": "Aucun style",
+  "imageStyles" :"Style",
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/i18n.js	(révision 10151)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/i18n.js	(révision 10152)
@@ -16,6 +16,11 @@
       "figShowTitle": "Show title",
       "figShowAll":"Show title and description",
       "figinactive":"Show nothing",
+      "imagetyle" : "Picture style",
+      "imageStyleDesc" :"Apply an image style",
+      "styleAmb" : "Style of the templates",
+      "styleNone": "No styles",
+      "imageStyles" :"Style",
    },
    "fr-fr":true,
    "fr-ca":true
