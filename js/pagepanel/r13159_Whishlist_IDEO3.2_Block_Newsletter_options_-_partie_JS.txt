Revision: r13159
Date: 2024-10-07 16:33:00 +0300 (lts 07 Okt 2024) 
Author: rrakotoarinelina 

## Commit message
Whishlist IDEO3.2 : Block Newsletter : options - partie JS

## Files changed

## Full metadata
------------------------------------------------------------------------
r13159 | rrakotoarinelina | 2024-10-07 16:33:00 +0300 (lts 07 Okt 2024) | 1 ligne
Chemins modifiés :
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Models/NewsletterOption.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Models/main.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/NewsletterBlock.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Templates/NewsletterBlock.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Templates/newsletterOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Views/NewsletterBlockView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Views/NewsletterOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/i18n.js

Whishlist IDEO3.2 : Block Newsletter : options - partie JS
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Models/NewsletterOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Models/NewsletterOption.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Models/NewsletterOption.js	(révision 13159)
@@ -0,0 +1,27 @@
+define([
+    "underscore",
+    "JEditor/Commons/Events",
+    "JEditor/PagePanel/Contents/Options/Models/AbstractOption",
+    "JEditor/PagePanel/Contents/Zones/Models/ZoneDependency",
+    "i18n!../nls/i18n"
+], function(_, Events, AbstractOption,ZoneDependency, translate) {
+    /**
+     * Les Options des blocs NewsletterOption
+     * @class NewsletterOption
+     */
+    var NewsletterOption = AbstractOption.extend(
+            /**
+             * @lends NewsletterOption
+             */
+                    {
+                        defaults: {optionType: 'NewsletterOption', priority: 70, askNames:true ,buttonText: null,successMessage: null,useCaptcha: "google"},
+                        initialize: function() {
+                            this._super();
+                            var zone = this.getZone();
+                        },
+
+                    });
+            NewsletterOption.SetAttributes(['askNames','buttonText','successMessage','useCaptcha']);
+            return NewsletterOption;
+        });
+
Index: src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Models/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Models/main.js	(révision 13158)
+++ src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Models/main.js	(révision 13159)
@@ -1,7 +1,7 @@
 // this file has been auto-generated by a grunt task, it will be overriden, do not modify it
-define([],function(){
+define(["./NewsletterOption"],function(NewsletterOption){
     var comp={
-
+        "NewsletterOption":NewsletterOption,
     };
     return comp;
-});
\ No newline at end of file
+});
Index: src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/NewsletterBlock.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/NewsletterBlock.js	(révision 13158)
+++ src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/NewsletterBlock.js	(révision 13159)
@@ -1,9 +1,11 @@
 define([
     "JEditor/PagePanel/Contents/Blocks/Block/Block",
     "JEditor/PagePanel/Contents/Options/Models/StylesOption",
+    "./Models/NewsletterOption",
 ], function (
         Block,
-        StylesOption
+        StylesOption,
+        NewsletterOption
         ) {
     /**
      * @class NewsletterBlock
@@ -26,6 +28,8 @@
                                 this.options.remove(this.options.effects);
                             if(!this.options.styles)
                                 this.options.add(new StylesOption()); 
+                            if(!this.options.NewsletterOption)
+                                this.options.add(new NewsletterOption()); 
                             
                         }
                     });
Index: src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Templates/NewsletterBlock.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Templates/NewsletterBlock.html	(révision 13158)
+++ src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Templates/NewsletterBlock.html	(révision 13159)
@@ -1,15 +1,27 @@
 <p><%= i18nNewsletterBlock.info %></p>
 <div class="field-container col-1">
 	<div class="col-collection col-1">
-		<label><%= i18nNewsletterBlock.lastName %></label>
-		<input type="text">
-		<label><%= i18nNewsletterBlock.firstName %></label>
-		<input type="text">
+		<% if (newsletterOption.askNames) { %>
+			<label><%= i18nNewsletterBlock.lastName %></label>
+			<input type="text">
+			<label><%= i18nNewsletterBlock.firstName %></label>
+			<input type="text">
+		<% } %>
 		<label><%= i18nNewsletterBlock.email %> *</label>
 		<input type="email">
 	</div>
 </div>
 <div>
-    <img src="<%= urlReCaptcha %>" style="max-width:100%">
+
+	<% if(newsletterOption.useCaptcha ==="linkeo"){%>
+        <div class="captcha">
+            <input type="text"><img src="<%= urlCaptcha %>">
+        </div>
+    <% } else if(newsletterOption.useCaptcha ==="google"){%>
+        <div>
+            <img src="<%= urlReCaptcha %>" style="max-width:100%">
+        </div>
+    <% } %>
+
 </div>
-<input type="submit" class="button" value="<%= i18nNewsletterBlock.subscribe %>">
\ No newline at end of file
+<input type="submit" class="button" <%=newsletterOption.buttonText?'value="'+newsletterOption.buttonText+'"':''%>>
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Templates/newsletterOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Templates/newsletterOption.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Templates/newsletterOption.html	(révision 13159)
@@ -0,0 +1,81 @@
+ <div class="panel-option-container animated button-icon">
+    <article class="panel-option">
+        <header>
+            <div class="panel-content-intro ">
+                <h3 class="panel-content-title address option-name"><span class="icon-mail"></span>
+                    <%= __("formSecurity")%>
+                </h3>
+                <p class="panel-legend">
+                    <%= __("formSecurityMessage")%>
+                </p>
+            </div>
+        </header>
+              <%= __("askNames")%> 
+        <div class="option-content">
+            <% var _id=_.uniqueId('askNames') %>
+                <input id="<%=_id %>" type="checkbox" class="blue-bg ask-names" name="ask-names" <%=newsletterOption.askNames ? ' checked="checked" ':'' %> >
+                <label for="<%=_id %>">
+                    <span class="checkbox-wrapper">
+                        <span class="icon-unchecked"></span>
+                        <span class="icon-checked"></span>
+                    </span>
+                    <span class="text">
+                       <%= __("yes")%> 
+                    </span>
+                </label>
+                <span class="selected-icon-wrapper"></span>
+                
+            </div>
+    </article>
+</div> 
+
+<div class="panel-option-container animated form-advanced">
+    <article class="panel-option" id="advanced-options">
+        <div class="form-row">
+            <label >
+                <%= __("sendButtonText")%> 
+                </label>
+            <input type="text" class="field-input" name="buttonText" value="<%= buttonText %>" />
+        </div>
+        <div class="form-row">
+            <label for="successMessage">
+                <%= __("confirmationMessage")%> 
+                </label>
+            <textarea id="successMessage" class="field-input" name="successMessage"><%= successMessage %></textarea>
+        </div>
+
+        <header>
+            <div class="panel-content-intro ">
+                <h3 class="panel-content-title address option-name"><span class="icon-mail"></span>
+                    <%= __("formSecurity")%>
+                </h3>
+                <p class="panel-legend">
+                    <%= __("formSecurityMessage")%>
+                </p>
+            </div>
+        </header>
+        <div class="button-text">
+
+          <%  var _id= _.uniqueId('useCaptcha'); %>
+          <input id="<%=_id%>" class="field-input  for--block-label" type="radio" name="useCaptcha" value="linkeo" <%=newsletterOption.useCaptcha==="linkeo"? ' checked="checked" ':'' %> />
+          <label  class="block-label" for="<%=_id%>">
+            <div class="radio-wrapper">
+              <span class="icon  icon-radio-active"></span>
+              <span class="icon  icon-radio-inactive"></span>
+            </div>
+            <div class="block-label-radio">Linkeo Captcha</div>
+          </label>
+
+          <%  var _id= _.uniqueId('useCaptcha'); %>
+          <input id="<%=_id%>" class="field-input  for--block-label" type="radio" name="useCaptcha" value="google" <%=newsletterOption.useCaptcha==="google"? ' checked="checked" ':'' %> />
+          <label  class="block-label" for="<%=_id%>">
+            <div class="radio-wrapper">
+              <span class="icon  icon-radio-active"></span>
+              <span class="icon  icon-radio-inactive"></span>
+            </div>
+            <div class="block-label-radio">Google reCAPTCHA</div>
+          </label>
+
+        </div>
+    </article>
+</div>
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Templates/newsletterOption.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Views/NewsletterBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Views/NewsletterBlockView.js	(révision 13158)
+++ src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Views/NewsletterBlockView.js	(révision 13159)
@@ -12,9 +12,10 @@
     "JEditor/PagePanel/Contents/Options/Models/StylesOption",
     "JEditor/PagePanel/Contents/Options/Models/AdvancedOption",
     "JEditor/PagePanel/Contents/Zones/Models/ZoneDependency",
+    "./NewsletterOptionView",
     "ckeditor",
     "i18n!../nls/i18n"
-], function ($, _, template, Events, ContentView, BlockView, Block,SaveCancelPanel,AdvancedOptionView,StylesOptionView,StylesOption,AdvancedOption, ZoneDependency, CKEDITOR, translate) {
+], function ($, _, template, Events, ContentView, BlockView, Block,SaveCancelPanel,AdvancedOptionView,StylesOptionView,StylesOption,AdvancedOption, ZoneDependency, NewsletterOptionView, CKEDITOR, translate) {
     
 
     var i18nNewsletterBlock = {
@@ -67,7 +68,41 @@
                         initialize: function () {
                             this._super();
                             this._contentTemplate = this.buildTemplate(template, translate);
+                            this.listenTo(this.model.options.NewsletterOption, "change", this.render);
                         },
+
+                        edit: function() {
+                            var optionsView, contentOptions, index,advancedView,stylesOptionView;
+                            // this.model.get("newsletterOption").snapshot();
+
+                            optionsView = new SaveCancelPanel();
+                            contentOptions = new NewsletterOptionView({
+                                model: this.model.options.NewsletterOption
+                            });
+                            stylesOptionView = new StylesOptionView({model:this.model.options.styles});
+                            advancedView = new AdvancedOptionView({model:this.model.options.advancedCSS});
+
+                            this.listenTo(optionsView, "save", function() {
+                                this.app.currentPanel.rightPanelView.hidePanel();
+                                this.app.currentPanel.rightPanelView.hideContent(optionsView);
+                                this.app.currentPanel.rightPanelView.removeContent(optionsView);
+                            });
+                            this.listenTo(optionsView, "cancel", function() {
+                                this.app.currentPanel.rightPanelView.hidePanel();
+                                this.app.currentPanel.rightPanelView.hideContent(optionsView);
+                                this.app.currentPanel.rightPanelView.removeContent(optionsView);
+
+                                // this.model.get("newsletterOption").revert();
+                            });
+                            optionsView.addPane(translate("contentOptions"), contentOptions);
+                            optionsView.addPane(translate("styles"), stylesOptionView);
+                            optionsView.addPane(translate("advancedCSS"), advancedView);
+                            optionsView.setTitle(translate("newsletterBlockOption"));
+                            this.app.currentPanel.rightPanelView.addContent(optionsView);
+                            this.app.currentPanel.rightPanelView.showContent(optionsView);
+                            this.app.currentPanel.rightPanelView.showPanel();
+                        },
+
                         render: function () {
                             this._super();
                             var currentLang = this.app.currentPanel.currentLang.id.substr(0, 2);
@@ -74,7 +109,10 @@
                             if(this.app.currentPanel.currentLang.id === "fr_CA") { i18nNewsletterBlock["fr"].email = "Courriel"; }
                             var i18nBlock = (i18nNewsletterBlock[currentLang]) ? i18nNewsletterBlock[currentLang] : i18nNewsletterBlock["en"] ;
 
+                            var newsletterOption =  this.model.options.NewsletterOption;
+
                             this.$(".content").html(this._contentTemplate({
+                                newsletterOption : newsletterOption,
                                 i18nNewsletterBlock: i18nBlock,
                                 urlCaptcha:__IDEO_API_PATH__+'/../formulaire01/images/captcha.png',
                                 urlReCaptcha: __IDEO_API_PATH__+'/../formulaire01/images/recaptcha.png'
Index: src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Views/NewsletterOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Views/NewsletterOptionView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Views/NewsletterOptionView.js	(révision 13159)
@@ -0,0 +1,72 @@
+/*define([
+    "JEditor/Commons/Ancestors/Views/View",
+    "./FormBuilderView",
+    "JEditor/Commons/Ancestors/Views/SaveCancelPanel",
+    "text!JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Templates/newsletterOptions.html",
+    "i18n!../nls/i18n"
+], */
+
+
+define([
+    "jquery",
+    "underscore",
+    "text!../Templates/newsletterOption.html",
+    "JEditor/PagePanel/Contents/Options/Views/AbstractOptionView",
+    "i18n!../nls/i18n"
+], function($, _, newsletterOptionTemplate, AbstractOptionView, translate) {
+
+    /**
+     * Vue des Options du bloc newsletter
+     * 
+     * @class NewsletterOptionView
+     */
+    var NewsletterOptionView = AbstractOptionView.extend(
+            /**
+             * @lends NewsletterOptionView.prototype
+             */
+                    {
+                        optionType: 'NewsletterOption',
+                        tagName: "div",
+                        className: "panel-content button-panel ",
+                        events: {
+
+                            'change input[type="checkbox"].ask-names': '_onChangeAskNames', 
+                        },
+                        /**
+                         * initialise l'objet
+                         */
+                        initialize: function() {
+                            this._super();
+                            this.template = this.buildTemplate(newsletterOptionTemplate, translate);
+
+                        },
+
+                        _onChangeAskNames: function(event) {
+                            
+                             var input = event.target;
+                             if ( input.checked ) {
+                                this.model.askNames = true;
+                            } else {
+                                this.model.askNames = false;
+                            }
+                        },
+
+                        /**
+                         * actualise l'affichage de la vue
+                         */
+
+                        render: function() {
+                            
+                            var buttonText = this.model.attributes.buttonText == null ? translate("send") : this.model.attributes.buttonText;
+                            var successMessage = this.model.attributes.successMessage == null ? translate("successMessage") : this.model.attributes.successMessage;
+
+                            var templateVars = {newsletterOption: this.model.attributes, buttonText : buttonText, successMessage : successMessage};
+                            this.$el.html(this.template(templateVars));
+                            // this.$el.append(this._linkSelectorView.render({model:this.model.link}).el);
+                            this.scrollables();
+                            return this;
+                        }
+                    });
+
+            return NewsletterOptionView;
+        });
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Views/NewsletterOptionView.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/fr-ca/i18n.js	(révision 13158)
+++ src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/fr-ca/i18n.js	(révision 13159)
@@ -1,4 +1,18 @@
 define({
     "BLOCK_NAME": "Newsletter",
-    "newsletterBlockOption": "Options du bloc newsletter"
+    "newsletterBlockOption": "Options du bloc newsletter",
+    "cancel":"Annuler",
+    "save":"Sauvegarder",
+    "advancedCSS":"Avancé",
+    "contentOptions":"Formulaire",
+    "formSecurity" : "Sécurisez votre formulaire",
+    "formSecurityMessage" : "Choisissez un système de captcha",
+    "sendButtonText":"Personnaliser le texte du bouton d'envoi :",
+    "confirmationMessage":"Personnaliser le message de confirmation :",
+    "formOption":"Options du formulaire",
+    "createFormMessage":"Créez et personnalisez ici votre formulaire.",
+    "yes" : "Oui",
+    "askNames" : "Demander le nom et le prénom",
+    "send":"Envoyer",
+    "successMessage":"Merci, votre message nous a été transmis avec succès",
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/fr-fr/i18n.js	(révision 13158)
+++ src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/fr-fr/i18n.js	(révision 13159)
@@ -1,4 +1,18 @@
 define({
     "BLOCK_NAME": "Newsletter",
-    "newsletterBlockOption": "Options du bloc newsletter"
+    "newsletterBlockOption": "Options du bloc newsletter",
+    "cancel":"Annuler",
+    "save":"Sauvegarder",
+    "advancedCSS":"Avancé",
+    "contentOptions":"Formulaire",
+    "formSecurity" : "Sécurisez votre formulaire",
+    "formSecurityMessage" : "Choisissez un système de captcha",
+    "sendButtonText":"Personnaliser le texte du bouton d'envoi :",
+    "confirmationMessage":"Personnaliser le message de confirmation :",
+    "formOption":"Options du formulaire",
+    "createFormMessage":"Créez et personnalisez ici votre formulaire.",
+    "yes" : "Oui",
+    "askNames" : "Demander le nom et le prénom",
+    "send":"Envoyer",
+    "successMessage":"Merci, votre message nous a été transmis avec succès",
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/i18n.js	(révision 13158)
+++ src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/nls/i18n.js	(révision 13159)
@@ -1,5 +1,21 @@
 define({"root": {
         "BLOCK_NAME": "Newsletter",
         "newsletterBlockOption": "Newsletter options",
-        "Option": "Newsletter"
+        "Option": "Newsletter",
+        "advancedCSS":"Advanced",
+        "styles"    :   "Styles",
+        "cancel":"Cancel",
+        "save":"Save",
+        "contentOptions":"Form",
+        "formSecurity" : "Secure your form",
+        "formSecurityMessage" : "Choose a captcha system",
+        "sendButtonText":"Customize text of the submit button :",
+        "confirmationMessage":"Customize confirmation message :",
+        "formOption":"Form's options",
+        "createFormMessage":"create and customize your form right here.",
+        "yes" : "Yes",
+        "askNames" : "Ask for first and last name",
+        "send" : "Send",
+        "successMessage" : "Your message has been sent successfully",
+
     }, "fr-fr": true, "fr-ca": true});
\ No newline at end of file
