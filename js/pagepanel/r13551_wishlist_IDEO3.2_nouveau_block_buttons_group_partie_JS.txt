Revision: r13551
Date: 2024-12-03 08:38:00 +0300 (tlt 03 Des 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: nouveau block buttons group (partie JS) 

## Files changed

## Full metadata
------------------------------------------------------------------------
r13551 | srazanandralisoa | 2024-12-03 08:38:00 +0300 (tlt 03 Des 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Articles/Blocks.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/AvailableView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Ancestors/ContentView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Ancestors/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Ancestors/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Ancestors/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/Block/Block.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/BlockView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/ButtonBlock.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/ButtonGroupBlock.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Models
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Models/ButtonGroupStyleOption.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Models/main.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Templates
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Templates/buttonGroupBlock.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Templates/buttonGroupStyleOption.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Views
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Views/ButtonGroupBlockView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Views/ButtonGroupStyleOptionView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Views/main.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/main.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/de-de
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/de-de/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/en-au
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/en-au/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/en-us
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/en-us/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/es-es
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/es-es/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/fr-ca
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/fr-ca/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/fr-fr
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/fr-fr/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json
   M /branches/ideo3_v2/integration/src/js/build.js
   M /branches/ideo3_v2/integration/src/js/main.js
   M /branches/ideo3_v2/integration/src/less/imports/edit.less
   M /branches/ideo3_v2/integration/src/less/imports/news_panel/main.less
   M /branches/ideo3_v2/integration/src/less/imports/page_panel/main.less
   A /branches/ideo3_v2/integration/src/less/imports/page_panel/module/block-render/group-btn.less

wishlist IDEO3.2: nouveau block buttons group (partie JS) 
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Articles/Blocks.js
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Blocks.js	(révision 13550)
+++ src/js/JEditor/NewsPanel/Articles/Blocks.js	(révision 13551)
@@ -21,7 +21,8 @@
     "JEditor/PagePanel/Contents/Blocks/CompareBlock",
     "JEditor/PagePanel/Contents/Blocks/LoopBlock",
     "JEditor/PagePanel/Contents/Blocks/SlideshowBlock",
-    "JEditor/PagePanel/Contents/Blocks/CardBlock"
+    "JEditor/PagePanel/Contents/Blocks/CardBlock",
+    "JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock"
   ], function (
     ImageBlock,
     HtmlBlock,
@@ -45,7 +46,8 @@
     CompareBlock,
     LoopBlock,
     SlideshowBlock,
-    CardBlock
+    CardBlock,
+    ButtonGroupBlock
   ) {
     var component = {
       "ImageBlock": ImageBlock,
@@ -70,7 +72,8 @@
       "CompareBlock": CompareBlock,
       "LoopBlock": LoopBlock,
       "SlideshowBlock": SlideshowBlock,
-      "CardBlock": CardBlock
+      "CardBlock": CardBlock,
+      "ButtonGroupBlock" : ButtonGroupBlock
     };
     return component;
   });
Index: src/js/JEditor/NewsPanel/Views/AvailableView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/AvailableView.js	(révision 13550)
+++ src/js/JEditor/NewsPanel/Views/AvailableView.js	(révision 13551)
@@ -41,7 +41,7 @@
                             {
                                 events: {'click .block.available': 'addToCurrentContainer', 'dragstart .bloc.available': '_ondragStart'},
                                 blockOrder: {
-                                    'standard': ["TextBlock", "ImageBlock", "ButtonBlock", "SeparatorBlock", "TableBlock", "CardBlock"],
+                                    'standard': ["TextBlock", "ImageBlock", "ButtonBlock", "SeparatorBlock", "TableBlock", "CardBlock",,"ButtonGroupBlock"],
                                     'advanced': ["CarrouselBlock", "GridBlock","GalerieBlock","CompareBlock","SlideshowBlock", "LoopBlock", "FormBlock", /*"VideoLinkeoBlock",*/ "MapBlock", "VideoBlock", "HtmlBlock"],
                                     'others': ["LegalsBlock","SiteMapBlock","SocialNetworkBlock"],
                                     'ignored':['TwitterTimelineBlock']
Index: src/js/JEditor/PagePanel/Contents/Ancestors/ContentView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Ancestors/ContentView.js	(révision 13550)
+++ src/js/JEditor/PagePanel/Contents/Ancestors/ContentView.js	(révision 13551)
@@ -229,12 +229,20 @@
                     }
                 }
                 this.sensors = this.childContainer.children('.sensor');
-                this.sensors.droppable({
-                    tolerance: 'touch',
-                    refreshPositions: true,
-                    hoverClass: 'placeholder',
-                    accept: this.accept
-                });
+                if ((this.$el.hasClass('bouton'))) {
+                    this.sensors.droppable({
+                        tolerance: 'touch',
+                        refreshPositions: true,
+                        accept: this.accept
+                    });
+                } else {
+                    this.sensors.droppable({
+                        tolerance: 'touch',
+                        refreshPositions: true,
+                        hoverClass: 'placeholder',
+                        accept: this.accept
+                    });
+                }
                 return this;
             },
             accept: function() {
@@ -256,6 +264,12 @@
                     sensor.removeClass('custom-placeholder');
                     this.dom.body.removeClass('drag');
                     clone.dummy = false;
+                    if (clone.type === 'button') {
+                       if (this.model.type === 'buttonGroup')
+                        parent = this.model;
+                       else if (parent.type === 'buttonGroup')
+                        parent = parent.parent;
+                    }
                     while (!(parent instanceof this.model.constructor)) {
                         parents.push(parent.constructor);
                         parent = parent.parent;
@@ -279,7 +293,12 @@
                     this.model.addChild(futureChild, sensor.data('index'));
                     event.stopImmediatePropagation();
                 } catch (e) {
-                    this.error({
+                    if (e.message === "parent is null" ){
+                        this.error({
+                            message: translate('cannotAddAnotherContent')
+                        });
+                    }
+                   else this.error({
                         message: translate('cannotAddContent')
                     });
                 }
Index: src/js/JEditor/PagePanel/Contents/Ancestors/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Ancestors/nls/fr-ca/i18n.js	(révision 13550)
+++ src/js/JEditor/PagePanel/Contents/Ancestors/nls/fr-ca/i18n.js	(révision 13551)
@@ -27,5 +27,6 @@
    "separator":"Séparation",
    "table":"Tableau",
    "carrousel":"Carrousel",
-   "cannotAddContent":"Impossible d'ajouter une nouvelle colonne. <br>Le maximum est de 4 colonnes."
+   "cannotAddContent":"Impossible d'ajouter une nouvelle colonne. <br>Le maximum est de 4 colonnes.",
+   "cannotAddAnotherContent" : "Impossible d'ajouter cet élément. <br>Seuls les boutons sont autorisés."
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Ancestors/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Ancestors/nls/fr-fr/i18n.js	(révision 13550)
+++ src/js/JEditor/PagePanel/Contents/Ancestors/nls/fr-fr/i18n.js	(révision 13551)
@@ -27,5 +27,6 @@
    "separator":"Séparation",
    "table":"Tableau",
    "carrousel":"Carrousel",
-   "cannotAddContent":"Impossible d'ajouter une nouvelle colonne. <br>Le maximum est de 4 colonnes."
+   "cannotAddContent":"Impossible d'ajouter une nouvelle colonne. <br>Le maximum est de 4 colonnes.",
+   "cannotAddAnotherContent" : "Impossible d'ajouter cet élément. <br>Seuls les boutons sont autorisés."
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Ancestors/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Ancestors/nls/i18n.js	(révision 13550)
+++ src/js/JEditor/PagePanel/Contents/Ancestors/nls/i18n.js	(révision 13551)
@@ -28,7 +28,8 @@
       "separator":"Separator",
       "table":"Table",
       "carrousel":"Carousel",
-      "cannotAddContent":"You can not add a new column. <br> The maximum is 4 columns."
+      "cannotAddContent":"You can not add a new column. <br> The maximum is 4 columns.",
+      "cannotAddAnotherContent" : "Cannot add this element. Only buttons are allowed."
    },
    "fr-fr":true,
    "fr-ca":true
Index: src/js/JEditor/PagePanel/Contents/Blocks/Block/Block.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/Block/Block.js	(révision 13550)
+++ src/js/JEditor/PagePanel/Contents/Blocks/Block/Block.js	(révision 13551)
@@ -56,7 +56,11 @@
                 },
                 children: {
                     get: function() {
-                        return [];
+                        return this.get(this.childrenAttribute) ? this.get(this.childrenAttribute) : [];
+                    },
+                    set: function(children) {
+                        var ret = this[this.childrenAttribute] = children;
+                        return ret;
                     }
                 }
             });
Index: src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js	(révision 13550)
+++ src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js	(révision 13551)
@@ -42,7 +42,7 @@
                             {
                                 events: {'click .block.available': 'addToCurrentContainer', 'dragstart .bloc.available': '_ondragStart'},
                                 blockOrder: {
-                                    'standard': ["TextBlock", "ImageBlock", "ButtonBlock", "SeparatorBlock", "TableBlock", "CardBlock"],
+                                    'standard': ["TextBlock", "ImageBlock", "ButtonBlock", "SeparatorBlock", "TableBlock", "CardBlock","ButtonGroupBlock"],
                                     'advanced': ["CarrouselBlock", "GridBlock","GalerieBlock","CompareBlock","SlideshowBlock", "LoopBlock", "FormBlock", /*"VideoLinkeoBlock",*/ "MapBlock", "VideoBlock", "HtmlBlock"],
                                     'others': ["LegalsBlock","SiteMapBlock","SocialNetworkBlock"],
                                     'ignored':['TwitterTimelineBlock']
Index: src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/BlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/BlockView.js	(révision 13550)
+++ src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/BlockView.js	(révision 13551)
@@ -38,11 +38,13 @@
                          * @private
                          */
                         _onClick: function(e) {
+                            e.stopPropagation();   
                             this.$el.focus();
                             if (BlockView.editor)
                                 BlockView.editor.destroy();
                         },
                         duplicate : function (e) {
+                            e.stopPropagation();
                             var clone = this.model.clone();
                             //delete clone.id;
                             this.model.parent.addChild(clone);
Index: src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js	(révision 13550)
+++ src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js	(révision 13551)
@@ -21,7 +21,8 @@
   "./CompareBlock",
   "./LoopBlock",
   "./SlideshowBlock",
-  "./CardBlock"
+  "./CardBlock",
+  "./ButtonGroupBlock"
 ], function (
   ImageBlock,
   HtmlBlock,
@@ -45,7 +46,8 @@
   CompareBlock,
   LoopBlock,
   SlideshowBlock,
-  CardBlock
+  CardBlock,
+  ButtonGroupBlock
 ) {
   var component = {
     "ImageBlock": ImageBlock,
@@ -70,7 +72,8 @@
     "CompareBlock": CompareBlock,
     "LoopBlock": LoopBlock,
     "SlideshowBlock": SlideshowBlock,
-    "CardBlock": CardBlock
+    "CardBlock": CardBlock,
+    "ButtonGroupBlock":ButtonGroupBlock
   };
   return component;
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/ButtonBlock.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/ButtonBlock.js	(révision 13550)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/ButtonBlock.js	(révision 13551)
@@ -31,6 +31,6 @@
                                 }
                             }
                     );
-                    ButtonBlock.ICON = 'icon-button-block_icon';
+                    ButtonBlock.ICON = 'icon-button-block2_icon';
                     return ButtonBlock;
                 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js	(révision 13550)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js	(révision 13551)
@@ -27,7 +27,10 @@
                             this.buttonOption = this.model.options.ButtonOption;
                             this.listenTo(this.buttonOption, "radio:change", this.render); 
                             this.listenTo(this.buttonOption, "input:change", this.onInputChange);
-                            this.delegateEvents();                   
+                            this.delegateEvents();         
+                            this.svgContent = '';   
+                            this.svgName = this.model.options.ButtonStyleOption.icon;
+                            if(this.svgName) this.fetchSvg(this.svgName);       
                         },
                         onInputChange: function(length){
                             var txtWarning = this.$('.txt-warning');
@@ -41,7 +44,6 @@
                             this.render();
                         },
                         render: function() {
-                            var svgName = this.model.options.ButtonStyleOption.icon;
                             var iconContent ="";
 
                             this._super();
@@ -55,41 +57,32 @@
 
                             this.$(".blk-button__label").css('margin-top','');
                             this.$(".blk-button__icon").empty();
-                            if(svgName !== ""  ){
-                                var svgCollectionObj = new SvgCollection({"svgName":svgName});
-                                svgCollectionObj.fetchIcons(_.bind(function(error, svg) {
-                                    if (error) {
-                                        console.error(error);
-                                    } else {
-                                        iconContent = svg.content;
-                                        this.$(".blk-button__label").css('margin-top','-15px');
-                                        this.$('.content').append(this._contentTemplate({icon: iconContent,size: sizeButton, text: textButton, buttonAlignment:alignButton, textAlignment:alignText, color :color, isDifferentText: isDifferentText, textOnMobile: textOnMobile}));
-                                         return this;
-                                    }
-                                },this));
-                            }else{
-                                this.$('.content').append(this._contentTemplate({icon: iconContent,size: sizeButton, text: textButton, buttonAlignment:alignButton, textAlignment:alignText, color :color, isDifferentText: isDifferentText, textOnMobile: textOnMobile}));
-                                return this;
-                            }
-                            
+                            this.$('.content').append(this._contentTemplate({icon: iconContent,size: sizeButton, text: textButton, buttonAlignment:alignButton, textAlignment:alignText, color :color, isDifferentText: isDifferentText, textOnMobile: textOnMobile}));
                         },
+                        fetchSvg: function (svgName) {
+                            var svgCollectionObj = new SvgCollection({"svgName":svgName});
+                            svgCollectionObj.fetchIcons(_.bind(function(error, svg) {
+                                if (error) {
+                                    console.error(error);
+                                } else {
+                                    this.svgContent = svg.content;
+                                    this.$(".blk-button__label").css('margin-top','-15px');
+                                    this.$(".blk-button__icon").empty();
+                                    this.$(".blk-button__icon").append(this.svgContent);
+                                }
+                            },this));
+                        },
                         renderOptions: function(model, options) {
                             this.$(".blk-button__icon").empty();
                             this.$(".blk-button__label").css('margin-top','');
                             var svgName = this.model.options.ButtonStyleOption.icon;
                             // fetch icon content
-                            if(svgName !== "" && model.attributes.optionType === "ButtonStyleOption" ){
-                                var svgCollectionObj = new SvgCollection({"svgName":svgName});
-                                svgCollectionObj.fetchIcons(_.bind(function(error, svg) {
-                                    if (error) {
-                                        console.error(error);
-                                    } else {
-                                        this.$(".blk-button__label").css('margin-top','-15px');
-                                        this.$(".blk-button__icon").empty();
-                                        this.$(".blk-button__icon").append(svg.content);
-                                    }
-                                },this));
+                            if(svgName !== "" && svgName !== this.svgName && model.attributes.optionType === "ButtonStyleOption" ){
+                                this.fetchSvg(svgName);
                             }
+                            this.$(".blk-button__label").css('margin-top','-15px');
+                            this.$(".blk-button__icon").empty();
+                            this.$(".blk-button__icon").append(this.svgContent);
                             var sizeButton = this.model.options.ButtonStyleOption.size;
                             var alignButton = this.model.options.ButtonStyleOption.buttonAlignment;
                             var alignText = this.model.options.ButtonStyleOption.textAlignment;
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/ButtonGroupBlock.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/ButtonGroupBlock.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/ButtonGroupBlock.js	(révision 13551)
@@ -0,0 +1,55 @@
+define([
+    "JEditor/PagePanel/Contents/Blocks/Block/Block",
+    "./Models/ButtonGroupStyleOption",
+    "JEditor/PagePanel/Contents/Blocks/ButtonBlock"
+],function (
+    Block,
+    ButtonGroupStyleOption,
+    ButtonBlock
+    ) {
+    /**
+     * @class ImageBlock
+     * @extends Block
+     */
+    var ButtonGroupBlock = Block.extend(
+        /**
+         * @lends ImageBlock.prototype
+         */
+            {
+                childrenAttribute: 'content',
+                childClass: Block.buildBlock,
+                /**
+                 * la largeur d'une colonne (nombre<=24)
+                 * @type Number
+                 */
+                defaults: {type: "buttonGroup", options: [], contentType: 'buttonGroupBlock'},
+                /**
+                 * initialise la vue
+                 */
+                initialize: function () {
+
+                    if (typeof this.content === "string") {
+                        try {
+                            var parsedContent = JSON.parse(this.content);
+                            this.content = parsedContent;
+                        } catch (error) {
+                            console.error("Erreur de parsing JSON pour 'content':", error);
+                            this.content = []; 
+                        }
+                    }  
+                    this._super();
+                    this.children = this.content;
+                    if(!this.options.buttonGroupStyleOption)
+                        this.options.add(new ButtonGroupStyleOption());
+
+                },
+                addBouton:function(){
+                    var newBtn = new ButtonBlock();
+                    newBtn.parent = this;
+                    this.addChild(newBtn);
+                },
+            }
+        );
+        ButtonGroupBlock.ICON = 'icon-button-group_icon';
+        return ButtonGroupBlock;
+    });

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/ButtonGroupBlock.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Models/ButtonGroupStyleOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Models/ButtonGroupStyleOption.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Models/ButtonGroupStyleOption.js	(révision 13551)
@@ -0,0 +1,31 @@
+define([
+    "underscore",
+    "JEditor/Commons/Events",
+    "JEditor/PagePanel/Contents/Options/Models/AbstractOption"
+
+], function(_,Events, AbstractOption) {
+    var allowedButtonGroupAlign=["","center","end","full-width"];
+
+    /**
+     * ButtonGroupStyleOption
+     */
+    var ButtonGroupStyleOption = AbstractOption.extend(
+            /**
+             * @lends ButtonGroupStyleOption
+             */
+                    {
+                        defaults: {
+                            optionType: 'buttonGroupStyleOption', 
+                            priority: 80, 
+                            buttonAlignment:'',
+                            buttonMargin: "1",
+                            mobileButtonWrap:false,
+                        },
+                        initialize: function() {
+                            this._super();
+                        },
+                       
+                    });
+            ButtonGroupStyleOption.SetAttributes(['buttonAlignment','buttonMargin','mobileButtonWrap']);
+            return ButtonGroupStyleOption;
+        });

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Models/ButtonGroupStyleOption.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Models/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Models/main.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Models/main.js	(révision 13551)
@@ -0,0 +1,7 @@
+// this file has been auto-generated by a grunt task, it will be overriden, do not modify it
+define(["./ButtonGroupStyleOption"],function(ButtonGroupStyleOption){
+    var comp={
+        "ButtonGroupStyleOption":ButtonGroupStyleOption
+    };
+    return comp;
+});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Models/main.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Templates/buttonGroupBlock.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Templates/buttonGroupBlock.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Templates/buttonGroupBlock.html	(révision 13551)
@@ -0,0 +1,10 @@
+<div class="btn-grp-content">
+    <div class="child-container">
+    </div>
+    <div class="block-button blk-button">
+    <span class="button add-button">
+      <span class="plus-btn" >+</span>
+      <span class="txt blk-button__label"><span>Ajouter un bouton</span></span>
+    </span>
+  </div>
+</div>
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Templates/buttonGroupBlock.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Templates/buttonGroupStyleOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Templates/buttonGroupStyleOption.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Templates/buttonGroupStyleOption.html	(révision 13551)
@@ -0,0 +1,131 @@
+
+<div class="panel-option-container animated button-alignment">
+    <article class="panel-option">
+        <header>
+            <h3 class="option-name">
+                <span class="icon-button-alignment"></span>
+                <%=__("alignButton")%>
+            </h3>
+            <span class="panel-content-legend">
+                <%=__("alignButtonLegend")%>
+            </span>
+        </header>
+        <div class="option-content">
+            <div class="controlPanel-selector">
+                <div class="option radio">
+                    <% var _id= _.uniqueId('alignButton'); %>
+                        <div class="button-align-radio">
+
+                            <label for="<%=_id %>" class="inline-block-label">
+                                <input id="<%= _id%>" type="radio" class="field-input" value="" name="buttonAlignment" <%=buttonAlignment===""? ' checked="checked" ':'' %> />
+                                <div class="inline-block-label__top">
+                                    <span class="icon-align-left"></span>
+                                </div>
+                                <div class="inline-block-label__bottom">
+                                    <span class="icon-radio-inactive"></span>
+                                    <span class="icon-radio-active"></span>
+                                </div>
+                            </label>
+
+                            <% var _id= _.uniqueId('alignButton'); %>
+
+                            <label for="<%=_id %>" class="inline-block-label">
+                                <input id="<%= _id%>" type="radio" class="field-input" value="center" name="buttonAlignment" <%=buttonAlignment==="center"? ' checked="checked" ':'' %> />
+                                <div class="inline-block-label__top">
+                                    <span class="icon-align-center"></span>
+                                </div>
+                                <div class="inline-block-label__bottom">
+                                    <span class="icon-radio-inactive"></span>
+                                    <span class="icon-radio-active"></span>
+                                </div>
+                            </label>
+
+                            <% var _id= _.uniqueId('alignButton'); %>
+
+                            <label for="<%=_id %>" class="inline-block-label">
+                                <input id="<%= _id%>" type="radio" class="field-input" value="end" name="buttonAlignment" <%=buttonAlignment==="end"? ' checked="checked" ':'' %> />
+                                <div class="inline-block-label__top">
+                                    <span class="icon-align-right"></span>
+                                </div>
+                                <div class="inline-block-label__bottom">
+                                    <span class="icon-radio-inactive"></span>
+                                    <span class="icon-radio-active"></span>
+                                </div>
+                            </label>
+
+                            <% var _id= _.uniqueId('alignButton'); %>
+
+                            <label for="<%=_id %>" class="inline-block-label">
+                                <input id="<%= _id%>" type="radio" class="field-input" value="full-width" name="buttonAlignment" <%=buttonAlignment==="full-width"? ' checked="checked" ':'' %> />
+                                <div class="inline-block-label__top">
+                                    <span class="icon-align-justify"></span>
+                                </div>
+                                <div class="inline-block-label__bottom">
+                                    <span class="icon-radio-inactive"></span>
+                                    <span class="icon-radio-active"></span>
+                                </div>
+                            </label>
+
+                        </div>
+
+                </div>
+            </div>
+        </div>
+    </article>
+</div>
+<div class="panel-option-container animated button-size">
+    <article class="panel-option">
+        <header>
+            <h3 class="option-name">
+                <span class="icon-button-size icon-midsize"></span>
+                <%=__("margeButton")%>
+            </h3>
+            <span class="panel-content-legend">
+                <%=__("margeButtonLegend")%>
+            </span>
+        </header>
+        <div class="option-content">
+            <div class="controlPanel-selector">
+                <div class="option radio">
+                    <div class="button-size-radio">
+                        <%  var _id= _.uniqueId('margeButton'); %>
+                        <label for="<%=_id %>" class="inline-block-label">
+                            <input id="<%= _id%>" type="radio" class="field-input" value="1" name="buttonMargin" <%=buttonMargin==="1"? ' checked="checked" ':'' %> />
+                            <div class="inline-block-label__top">
+                                <span class="icon-uncollapse"></span>
+                            </div>
+                            <div class="inline-block-label__bottom">
+                                <span class="icon-radio-inactive"></span>
+                                <span class="icon-radio-active"></span>
+                            </div>
+                        </label>
+                        <% _id= _.uniqueId('margeButton');      %>
+
+                        <label for="<%=_id %>" class="inline-block-label">
+                            <input id="<%= _id%>" type="radio" class="field-input" value="0" name="buttonMargin" <%=buttonMargin==="0"? ' checked="checked" ':'' %> />
+                            <div class="inline-block-label__top">
+                                <span class="icon-collapse"></span>
+                            </div>
+                            <div class="inline-block-label__bottom">
+                                <span class="icon-radio-inactive"></span>
+                                <span class="icon-radio-active"></span>
+                            </div>
+                        </label>
+                    </div>
+                </div>
+            </div>
+        </div>
+        <div class="option-content">
+         <% var _id=_.uniqueId('iconButton') %>
+            <input type="checkbox" class="blue-bg show-browse-icon-button field-input" name="mobileButtonWrap" id="<%=_id %>" <%= mobileButtonWrap ? 'checked="checked"' : '' %> >
+            <label for="<%=_id %>">
+                <span class="checkbox-wrapper">
+                    <span class="icon-unchecked"></span>
+                    <span class="icon-checked"></span>
+                </span>
+                <span class="text"><%=__("underlineMobileLegend")%></span>
+            </label>
+        </div>
+    </article>
+</div>
+

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Templates/buttonGroupStyleOption.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Views/ButtonGroupBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Views/ButtonGroupBlockView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Views/ButtonGroupBlockView.js	(révision 13551)
@@ -0,0 +1,120 @@
+define([
+    "JEditor/PagePanel/Contents/Blocks/Block/Views/BlockView",
+    "JEditor/PagePanel/Contents/Blocks/Blocks",
+    "JEditor/PagePanel/Contents/Blocks/Block/Block",
+    "JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView",
+    "text!../Templates/buttonGroupBlock.html",
+    "i18n!../nls/i18n",
+    "jqueryui/sortable",
+], function(BlockView, Blocks, Block, ButtonBlockView, template, translate) {
+    /**
+     * Vue des blocs d'images
+     * @class ButtonGroupBlockView
+     * @extends BlockView;
+     */
+    var ButtonGroupBlockView = BlockView.extend(
+            /**
+             * @lends ImageBlockView.prototype
+             */
+                    {
+                       
+                       
+                        attributes: {"class": "block buttonGroupblock", tabindex: 1},
+                        childViewClass: ButtonBlockView,
+                        options: {
+                            childSelector: ".block",
+                            sortableItemsClass: "block",
+                            /**
+                             * Renvoie le bon type de vue en fonction du type de block
+                             * Majuscule+Block+View
+                             */
+                            singleViewType: function(attrs) {
+                                var typeName = attrs.model.type.charAt(0).toUpperCase() + attrs.model.type.slice(1) + 'Block';
+                                var viewName = typeName + 'View';
+                                return new Blocks[typeName].Views[typeName + 'View'](attrs);
+                            },
+                            sensorText: "Bloc",
+                            modelClass: Block,
+                            handle: '.overlay',
+                            
+                        },
+                        events: {
+                            'click .btn-grp-content .block-button .add-button': '_clickAddButton',
+                        },
+                        initialize: function() {
+                            this._super();
+                            this.childViews = [];
+                            this._contentTemplate = this.buildTemplate(template, translate);
+                            this._viewFromChildren();
+                        },
+                        _onLoad: function() {
+                            this.render();
+                        },
+                        render: function() {
+                            this._super();
+                            this.undelegateEvents();
+                            //renderCHildrens
+                            this.$('.content').html('');
+                            var param = this.model.toJSON();
+                            this.$('.content').append(this._contentTemplate(param));
+                            this.$('.child-container').children('.buttonblock').remove()
+                            delete this.childContainer;
+                            if (!this.childContainer)
+                                this.childContainer = this.$('.child-container');
+                            this.childContainer.children('.sensor').remove();
+                            for (var i = 0; i < this.childViews.length; i++) {
+                                this.renderChildView(this.childViews[i]);
+                            }
+                            //endRenderChildren
+                            this.delegateEvents();
+                            return this;
+                        },
+                        renderChildView: function(view) {
+                            view.$el.data('model', view.model);
+                            this.childContainer.append(view.el);
+                            view.render();
+                            if (view.renderError !== null) {
+                                this.$el.addClass('children-error');
+                                this.renderError = view.renderError;
+                            }
+                            return this;
+                        },
+                        _viewFromChildren: function() {
+                            this.childViews = [];
+                            var i, index, model, view;
+                            for (i = 0; i < this.model.children.length; i++) {
+                                model = this.model.children[i];
+                                view = new ButtonBlockView({
+                                    model: model,
+                                    parentView: this
+                                });
+                                this.listenTo(view, 'all', this._propagateEvent);
+                                index = this.childViews.push(view) - 1;
+                                this.byViewCID[view.cid] = view;
+                                this.byModelCID[model.cid] = view;
+                                this.elements[i] = view.$el;
+                            }
+                        },
+                        renderOptions: function(model) {
+                            return this;
+                        },
+                        _clickAddButton:function(e){
+                            e.preventDefault();
+                           this.model.addBouton();
+                           return false;
+                        },
+                        onDropOver: function(event, ui) {
+                            var $target = $(event.currentTarget);
+                            var $draggedElement = ui.helper;
+                            if (!$draggedElement.hasClass('button-block')) {
+                                $target.removeClass('placeholder');
+                            } else {
+                                $target.addClass('placeholder');
+                            }
+                            $target.siblings('.sensor').droppable('disable').removeClass('placeholder');
+                            this.disableChildDrop();
+                            return false;
+                        },
+                    });
+            return ButtonGroupBlockView;
+        });
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Views/ButtonGroupBlockView.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Views/ButtonGroupStyleOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Views/ButtonGroupStyleOptionView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Views/ButtonGroupStyleOptionView.js	(révision 13551)
@@ -0,0 +1,54 @@
+define( [
+    "jquery",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/SaveCancelPanel",
+    "text!../Templates/buttonGroupStyleOption.html",
+    "JEditor/PagePanel/Contents/Options/Views/AbstractOptionView",
+    "i18n!../nls/i18n"
+], function(
+    $,
+    Events,
+    SaveCancelPanel,
+    buttonGroupStyleOption, 
+    AbstractOptionView,
+    translate
+    ) {
+    var ButtonGroupStyleOptionView = AbstractOptionView.extend(
+            {
+                optionType: 'buttonGroupStyleOption',
+                        tagName: "div",
+                        className: "panel-content buttonGroup-style-panel ",
+                       
+                        /**
+                         * initialise l'objet
+                         */
+                        initialize: function() {
+                            this._super();
+                            this.template = this.buildTemplate(buttonGroupStyleOption, translate);
+                            
+                        },
+                        /**
+                         * actualise l'affichage de la vue
+                         */
+                        render: function() {
+                            var templateVars = {
+                                icon:this.model.icon,
+                                buttonAlignment:this.model.buttonAlignment, 
+                                buttonMargin:this.model.buttonMargin, 
+                                mobileButtonWrap:this.model.mobileButtonWrap
+                            };
+                            this.$el.html(this.template(templateVars));
+                            this.scrollables(); 
+                            return this;
+                        }
+                        
+            }
+    );
+    Events.extend({
+        ButtonGroupEvents: {
+            SELECT_ICON: 'selectIcon',
+        },
+    })
+
+    return ButtonGroupStyleOptionView;
+});

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Views/ButtonGroupStyleOptionView.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Views/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Views/main.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Views/main.js	(révision 13551)
@@ -0,0 +1,8 @@
+// this file has been auto-generated by a grunt task, it will be overriden, do not modify it
+define(["./ButtonGroupBlockView","./ButtonGroupStyleOptionView"],function(ButtonGroupBlockView,ButtonGroupStyleOptionView){
+    var comp={
+        "ButtonGroupBlockView":ButtonGroupBlockView,
+        "ButtonGroupStyleOptionView":ButtonGroupStyleOptionView
+    };
+    return comp;
+});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Views/main.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/main.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/main.js	(révision 13551)
@@ -0,0 +1,7 @@
+// this file has been auto-generated by a grunt task, it will be overriden, do not modify it
+define(["./ButtonGroupBlock","./Views/main","./Models/main","i18n!./nls/i18n"],function(ButtonGroupBlock,Views,Models,i18n){
+    ButtonGroupBlock.Models=Models;
+    ButtonGroupBlock.Views=Views;
+    ButtonGroupBlock.i18n=i18n;
+    return ButtonGroupBlock;
+});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/main.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/de-de/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/de-de/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/de-de/i18n.js	(révision 13551)
@@ -0,0 +1,2 @@
+define({
+});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/de-de/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/en-au/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/en-au/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/en-au/i18n.js	(révision 13551)
@@ -0,0 +1,2 @@
+define({
+});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/en-au/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/en-us/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/en-us/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/en-us/i18n.js	(révision 13551)
@@ -0,0 +1,2 @@
+define({
+});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/en-us/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/es-es/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/es-es/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/es-es/i18n.js	(révision 13551)
@@ -0,0 +1,2 @@
+define({
+});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/es-es/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/fr-ca/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/fr-ca/i18n.js	(révision 13551)
@@ -0,0 +1,15 @@
+define({
+    "BLOCK_NAME": "Group de bouton",
+    "buttonGroupBlockOption": "Options du groupe de boutons",
+    "alignButton": "Alignement des boutons",
+    "alignButtonLegend": "Sélectionnez l'alignement des boutons dans le groupe",
+    "leftAlignButton": "Aligné à gauche",
+    "centerAlignButton": "Centré",
+    "rightAlignButton": "Aligné à droite",
+    "justifyAlignButton": "Justifié",
+    "buttonGroupStyleOption": "Style",
+    "buttonGroup":"group de boutons",
+    "margeButton":"Marges des boutons",
+    "margeButtonLegend": "Ajouter ou retirer les marges entre les boutons",
+    "underlineMobileLegend":"Passer les boutons à la ligne en mobile"
+});

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/fr-ca/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/fr-fr/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/fr-fr/i18n.js	(révision 13551)
@@ -0,0 +1,16 @@
+define({
+    "BLOCK_NAME": "Group de bouton",
+    "buttonGroupBlockOption": "Options du groupe de boutons",
+    "alignButton": "Alignement des boutons",
+    "alignButtonLegend": "Sélectionnez l'alignement des boutons dans le groupe",
+    "leftAlignButton": "Aligné à gauche",
+    "centerAlignButton": "Centré",
+    "rightAlignButton": "Aligné à droite",
+    "justifyAlignButton": "Justifié",
+    "buttonGroupStyleOption": "Style",
+    "buttonGroup":"group de boutons",
+    "margeButton":"Marges des boutons",
+    "margeButtonLegend": "Ajouter ou retirer les marges entre les boutons",
+    "underlineMobileLegend":"Passer les boutons à la ligne en mobile"
+
+});

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/fr-fr/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/i18n.js	(révision 13551)
@@ -0,0 +1,20 @@
+define({
+    "root": {
+        "BLOCK_NAME": "Button group",
+        "buttonGroupBlockOption":"Button group options",
+        "alignButton": "Button alignment",
+        "alignButtonLegend": "Select the alignment of your button on the group",
+        "leftAlignButton": "Left-aligned",
+        "centerAlignButton": "Center",
+        "rightAlignButton": "Right-aligned",
+        "justifyAlignButton": "Justified",
+        "buttonGroupStyleOption": "Style",
+        "buttonGroup":"Button group",
+        "margeButton":"Button margins",
+        "margeButtonLegend": "Add or remove margins between buttons",
+        "underlineMobileLegend":"Pass line buttons on mobile"
+
+    },
+    "fr-fr": true,
+    "fr-ca": true
+});

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json	(révision 13550)
+++ src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json	(révision 13551)
@@ -21,5 +21,6 @@
  "CompareBlock":"CompareBlock",
  "LoopBlock":"LoopBlock",
  "Slideshow":"Slideshow",
- "CardBlock":"CardBlock"
+ "CardBlock":"CardBlock",
+ "ButtonGroupBlock":"ButtonGroupBlock"
 }
Index: src/js/build.js
===================================================================
--- src/js/build.js	(révision 13550)
+++ src/js/build.js	(révision 13551)
@@ -66,7 +66,8 @@
         "JEditor/PagePanel/Contents/Blocks/EvaluationBlock",
 	    "JEditor/PagePanel/Contents/Zones/Versions",
         "JEditor/App/routes",
-        "JEditor/PagePanel/Contents/Blocks/CardBlock"
+        "JEditor/PagePanel/Contents/Blocks/CardBlock",
+        "JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock"
     ],
     shim: {
         'Modernizr': {
Index: src/js/main.js
===================================================================
--- src/js/main.js	(révision 13550)
+++ src/js/main.js	(révision 13551)
@@ -70,7 +70,8 @@
     "JEditor/PagePanel/Contents/Blocks/EvaluationBlock",
     "JEditor/PagePanel/Contents/Zones/Versions",
     "JEditor/App/routes",
-    "JEditor/PagePanel/Contents/Blocks/CardBlock"
+    "JEditor/PagePanel/Contents/Blocks/CardBlock",
+    "JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock"
   ],
   shim: {
     "Modernizr": {
Index: src/less/imports/edit.less
===================================================================
--- src/less/imports/edit.less	(révision 13550)
+++ src/less/imports/edit.less	(révision 13551)
@@ -549,7 +549,7 @@
                     //min-height:10px;
 
                     &:hover,&:focus{
-                        & .block-actions.btn-group{
+                        &  > .overlay .block-actions.btn-group{
                             display:block;
                             margin:10px;
                             & .btn{
Index: src/less/imports/news_panel/main.less
===================================================================
--- src/less/imports/news_panel/main.less	(révision 13550)
+++ src/less/imports/news_panel/main.less	(révision 13551)
@@ -46,6 +46,7 @@
 @import '../page_panel/module/block-render/table';
 @import '../page_panel/module/block-render/render-fake';
 @import '../page_panel/module/block-render/compare';
+@import '../page_panel/module/block-render/group-btn';
 
 @import '../page_panel/module/checkbox';
 @import '../page_panel/module/classic-modifier';
Index: src/less/imports/page_panel/main.less
===================================================================
--- src/less/imports/page_panel/main.less	(révision 13550)
+++ src/less/imports/page_panel/main.less	(révision 13551)
@@ -23,6 +23,7 @@
 @import 'module/block-render/table';
 @import 'module/block-render/render-fake';
 @import 'module/block-render/compare';
+@import 'module/block-render/group-btn';
 
 @import 'module/checkbox';
 @import 'module/classic-modifier';
Index: src/less/imports/page_panel/module/block-render/group-btn.less
===================================================================
--- src/less/imports/page_panel/module/block-render/group-btn.less	(nonexistent)
+++ src/less/imports/page_panel/module/block-render/group-btn.less	(révision 13551)
@@ -0,0 +1,55 @@
+.buttonGroupblock {
+	&.block {
+		&:hover, &:focus {
+			.child-container .buttonblock,
+			.block-button{
+				z-index: 100;
+				opacity: 1;
+			}
+		}
+		.content {
+			.btn-grp-content {
+				display: flex;
+				justify-content: flex-start;
+			}
+			
+			.child-container {
+				display: flex; 
+				flex-wrap: wrap; 
+				justify-content: flex-start;
+				.buttonblock{
+					opacity: 1;
+					min-width: 190px;
+					padding: auto;
+					height: fit-content;
+				}
+			}
+			
+			
+			.block-button {
+				opacity: 1;
+				color: #fff;
+				flex-shrink: 0;
+				flex-grow: 1;
+				min-width: 200px;
+				display: flex;
+				align-items: flex-start;
+			}
+			.btn-grp-content {
+				margin: 60px 5px 15px auto;
+				.add-button{
+					background-color: #10b981;
+					cursor: pointer;
+					span.plus-btn {
+						display: inline-block;
+						vertical-align: middle;
+						font-size: 30px;
+						margin-left: 1px;
+						margin-right: 6px;
+					  }
+					  
+				}
+			}
+		}
+	}
+}
