Revision: r9939
Date: 2022-12-06 08:11:23 +0300 (tlt 06 Des 2022) 
Author: sraz<PERSON><PERSON>lis<PERSON> 

## Commit message
correction sur l'option figcaption du bloc image et edition msg erreur si erreur upload

## Files changed

## Full metadata
------------------------------------------------------------------------
r9939 | srazanandralisoa | 2022-12-06 08:11:23 +0300 (tlt 06 Des 2022) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Templates/imageOptions.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageOptionsView.js

correction sur l'option figcaption du bloc image et edition msg erreur si erreur upload
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js
===================================================================
--- src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 9938)
+++ src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 9939)
@@ -471,10 +471,15 @@
             currentUpload.uploaded = 100;
             //this.dataArray.remove(currentUpload.index);
             //this._globalProgress();
+            // amelioration message d'erreur pour l'upload image
             if (error !== 'abort') {
-                var err = this.options.lang.uploadFailTooBig.replace('%name%', currentUpload.name);
-                this.$message.find('p').text(err);
-               // this.errors.push(err);
+                if(jqXHR.responseText == "Something went wrong : Check that the file isn't corrupted The uploaded file exceeds the upload_max_filesize directive in php.ini[]"){
+                    var err = this.options.lang.uploadFailTooBig.replace('%name%', currentUpload.name);
+                    this.$message.find('p').text(err);
+                }else{
+                    var err = this.options.lang.uploadFailServerError.replace('%name%', currentUpload.name).replace('%error%', error);
+                    this.$message.find('p').text(err);
+                }
             }
             currentUpload.preview.off('click.abort').remove();
             //delete this.previews[currentUpload.name];
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Templates/imageOptions.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Templates/imageOptions.html	(révision 9938)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Templates/imageOptions.html	(révision 9939)
@@ -14,25 +14,36 @@
         </div>
         <div class="figcaption-img">
             <p class="panel-legend"><%=__("figcaptionImage")%></p>
-            <div class="panel-radio <%=figcaption==1?'selected':''%>" data-value="1" >
-                <div class="panel-radio-title">
-                    <div class="radio-wrapper"><span class="icon-radio-active"></span><span class="icon-radio-inactive"></span></div>
-                    <div class="radio-label"><%= __("figShowTitle")%></div>
-                </div>
-            </div>
-            <div class="panel-radio <%=figcaption==2?'selected':''%>" data-value="2" >
-                <div class="panel-radio-title">
-                    <div class="radio-wrapper"><span class="icon-radio-active"></span><span class="icon-radio-inactive"></span></div>
-                    <div class="radio-label"><%= __("figShowAll")%></div>
-                </div>
-            </div>
-            <div class="panel-radio <%=figcaption==0?'selected':''%>" data-value="0" >
-                <div class="panel-radio-title">
-                    <div class="radio-wrapper"><span class="icon-radio-active"></span><span class="icon-radio-inactive"></span></div>
-                    <div class="radio-label"><%= __("figinactive")%></div>
-                </div>
-            </div>
-        </div>
+            <% var _id=_.uniqueId('figcaption') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="figcaption" value="1" <%=figcaption=="1"? ' checked="checked" ':'' %> />
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><%= __("figShowTitle")%></div>
+
+            </label>
+            <% var _id=_.uniqueId('figcaption') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="figcaption" value="2" <%=figcaption=="2"? ' checked="checked" ':'' %> />
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><%= __("figShowAll")%></div>
+            </label>
+            <% var _id=_.uniqueId('figcaption') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="figcaption" value="0" <%=figcaption=="0"? ' checked="checked" ':'' %> />
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><%= __("figinactive")%></div>
+            </label>
+      
+          </div>
         <div class="link-img">
             <p class="panel-legend"><%=__("selectClickActionImage")%></p>
 
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageOptionsView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageOptionsView.js	(révision 9938)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageOptionsView.js	(révision 9939)
@@ -31,7 +31,7 @@
 			'uploadercomplete .uploader' : '_onUploaded',
 			'change .infos-img>input' : 'updateImageInfos',
 			'click .cropbtn' : 'editImage',
-			'radiopanelchange': '_onRadioChange'
+			'change .figcaption-img>input': '_onRadioChange'
 		},
 		updateImageInfos : function(event) {
 			var $target = $(event.currentTarget);
@@ -130,45 +130,38 @@
 		 * actualise l'affichage de la vue
 		 */
 		render : function() {
-			try {
-			this.$('.figcaption-img').radioPanel('destroy');
-			} catch (e) {
-			//not so bad
-			} finally {
-				var lang = this.app.currentPanel.currentLang.id;
-				var titlePlaceholder = this.model.file.title[lang] ? this.model.file.title[lang] : this.translate('imageTitle');
-				var descPlaceholder = this.model.file.desc[lang] ? this.model.file.desc[lang] : this.translate('imageAlt');
-				var disable = (this.model.file.fileUrl === '#')? 'disabled=""' : '';
-				var templateVars = {
-					file : this.model.file,
-					link : this.model.link,
-					contextInfos : this.model.contextInfos,
-					titlePlaceholder : titlePlaceholder,
-					descPlaceholder : descPlaceholder,
-					disable : disable,
-					figcaption : this.model.figcaption
-				};
-				_.extend(templateVars, {
-					currentPage : false
-				});
-				this.$el.html(this._template(templateVars));
-				this.$('.image-option header').after(this.fileUploader.el);
-				this.fileUploader.render();
-				this._linkSelectorView = new LinkView({
-					model : this.model.link,
-					pageCollection : PageCollection.getInstance()
-				});
-				this.$('.link-img').append(this._linkSelectorView.render({model:this.model.link}).el);
-				this.scrollables({
-					advanced:{ autoScrollOnFocus: false }
-				});
-				this.$('.figcaption-img').radioPanel(); 
-			}
+			var lang = this.app.currentPanel.currentLang.id;
+			var titlePlaceholder = this.model.file.title[lang] ? this.model.file.title[lang] : this.translate('imageTitle');
+			var descPlaceholder = this.model.file.desc[lang] ? this.model.file.desc[lang] : this.translate('imageAlt');
+			var disable = (this.model.file.fileUrl === '#')? 'disabled=""' : '';
+			var templateVars = {
+				file : this.model.file,
+				link : this.model.link,
+				contextInfos : this.model.contextInfos,
+				titlePlaceholder : titlePlaceholder,
+				descPlaceholder : descPlaceholder,
+				disable : disable,
+				figcaption : this.model.figcaption
+			};
+			_.extend(templateVars, {
+				currentPage : false
+			});
+			this.$el.html(this._template(templateVars));
+			this.$('.image-option header').after(this.fileUploader.el);
+			this.fileUploader.render();
+			this._linkSelectorView = new LinkView({
+				model : this.model.link,
+				pageCollection : PageCollection.getInstance()
+			});
+			this.$('.link-img').append(this._linkSelectorView.render({model:this.model.link}).el);
+			this.scrollables({
+				advanced:{ autoScrollOnFocus: false }
+			});
 			return this;
-			
 		},
-		_onRadioChange: function (event, data) {
-			this.model.figcaption = data.value;
+		_onRadioChange: function (event ) {
+			var value = $(event.currentTarget).data('value');
+			this.model.figcaption = value;
 		},
 		/**
 		 * Déclenché lorsqu'on enregistre l'image
