Revision: r10388
Date: 2023-02-10 17:03:01 +0300 (zom 10 Feb 2023) 
Author: mpartaux 

## Commit message
new class in textblock + model names

## Files changed

## Full metadata
------------------------------------------------------------------------
r10388 | mpartaux | 2023-02-10 17:03:01 +0300 (zom 10 Feb 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/nls/i18n.js

new class in textblock + model names
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 10387)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 10388)
@@ -49,7 +49,10 @@
                                 { name: 'Highlight 3', element: 'span', attributes: { 'class': 'txt-hightlight-3' } },
                                 { name: 'Highlight 4', element: 'span', attributes: { 'class': 'txt-hightlight-4' } },
                                 { name: 'Underline 1', element: 'span', attributes: { 'class': 'txt-underline-1' } },
-                                { name: 'Color 1', element: 'span', attributes: { 'class': 'txt-color-1' } }
+                                { name: 'Color 1', element: 'span', attributes: { 'class': 'txt-color-1' } },
+                                { name: 'Color 2', element: 'span', attributes: { 'class': 'txt-color-2' } },
+                                { name: 'Color 3', element: 'span', attributes: { 'class': 'txt-color-3' } },
+                                { name: 'Color 4', element: 'span', attributes: { 'class': 'txt-color-4' } }
                             ],
                             forcePasteAsPlainText: true,
                             pasteFromWordRemoveFontStyles: true,
Index: src/js/JEditor/PagePanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/nls/fr-ca/i18n.js	(révision 10387)
+++ src/js/JEditor/PagePanel/nls/fr-ca/i18n.js	(révision 10388)
@@ -99,69 +99,13 @@
     "introPageTxt04":"Glissez et déposez un bloc dans votre page pour l\'ajouter.",
     "introPageTxt05":"Survolez les éléments existants dans vos pages pour les déplacer ou les éditer.",
     "introPageEnd":"C'est parti !",
-    "Landing page":"Landing page",
-    "landingPage01Name":"Page 2COL-1",
-    "landingPage02Name":"Page 2COL-2",
-    "landingPage03Name":"Page 2COL-3",
-    "landingPage04Name":"Page 2COL-4",
-    "landingPage05Name":"Page 3COL-1",
-    "landingPage06Name":"Page 3COL-2",
-    "landingPage07Name":"Page 3COL-3",
-    "landingPage08Name":"Page 3COL-3.2",
-    "landingPage09Name":"Page 3COL-4",
-    "landingPage10Name":"Page 3COL-5",
-    "landingPage01Desc":"Pour home ou landing page deux colonnes. Idéal avec une image paysage format 3:2.",
-    "landingPage02Desc":"Pour home ou landing page deux colonnes. Idéal avec deux images paysage format 3:2.",
-    "landingPage03Desc":"Pour home ou landing page deux colonnes. Idéal avec deux images portrait et paysage format 3:2.",
-    "landingPage04Desc":"Pour home ou landing page deux colonnes. Idéal avec deux images paysage format 3:2.",
-    "landingPage05Desc":"Pour home ou landing page 3 colonnes. Idéal avec deux images paysage format 3:2.",
-    "landingPage06Desc":"Pour home ou landing page 3 colonnes. Idéal avec une image carrée format 1:1.",
-    "landingPage07Desc":"Pour home ou landing page 3 colonnes. Idéal avec deux image aux formats 16:9 et 1:1.",
-    "landingPage08Desc":"Pour home ou landing page 3 colonnes. Variante de LP 3COL-3 avec plus d’images (une 16:9, une 1:1 et cinq 3:2)",
-    "landingPage09Desc":"Pour home ou landing page 3 colonnes. Idéal avec trois images aux formats 1:1 et 3:2.",
-    "landingPage10Desc":"Pour home ou landing page 3 colonnes. Idéal avec cinq images aux formats 3:2 et 4:3.",
     "Tarifs":"Tarifs",
     "tarifPageDesc":"Idéal pour présenter les différentes prestations que vous assurez, ainsi que les prix correspondant.",
-    "landingPage01HomeName":"Home idéale 1",
-    "landingPage02HomeName":"(Test) HP idéale v2",
-	"landingPage03HomeName":"HP idéale v2 'classique'",
-	"landingPage04HomeName":"HP idéale v2 'neutre'",
-	"landingPage05HomeName":"HP idéale v2 'grid'",
-	"landingPage06HomeName":"HP idéale v2 '#1'",
-	"landingPage07HomeName":"HP idéale v2 '#2'",
-	"landingPage08HomeName":"HP idéale v2 '#3'",
-	"landingPage09HomeName":"HP idéale v2 '#4'",
-    "landingPage01HomeDesc":"Le modèle idéal pour votre page d'accueil.",
-	"landingPage03HomeDesc":"Nouveau modèle de home page (v2). Modèle classique. N'utiliser qu'avec une ambiance GRID",
-	"landingPage04HomeDesc":"Nouveau modèle de home page (v2). Modèle neutre. N'utiliser qu'avec une ambiance GRID",
-	"landingPage05HomeDesc":"Nouveau modèle de home page (v2). Modèle grid. N'utiliser qu'avec une ambiance GRID",
-	"landingPage06HomeDesc":"Nouveau modèle de home page (v2). Modèle #1. N'utiliser qu'avec une ambiance GRID",
-	"landingPage07HomeDesc":"Nouveau modèle de home page (v2). Modèle #2. N'utiliser qu'avec une ambiance GRID",
-	"landingPage08HomeDesc":"Nouveau modèle de home page (v2). Modèle #3. N'utiliser qu'avec une ambiance GRID",
-	"landingPage09HomeDesc":"Nouveau modèle de home page (v2). Modèle #4. N'utiliser qu'avec une ambiance GRID",
-    "landingPage01IdealName":"Landing Page idéale 1",
-    "landingPage02IdealName":"Landing Page idéale 2",
-    "landingPage03IdealName":"Landing Page idéale 3",
-    "landingPage04IdealName":"(Test) LP idéale v2",
-	"landingPage05IdealName":"LP idéale v2 'classique'",
-	"landingPage06IdealName":"LP idéale v2 'neutre'",
-	"landingPage07IdealName":"LP idéale v2 'grid'",
-	"landingPage08IdealName":"LP idéale v2 '#1'",
-	"landingPage09IdealName":"LP idéale v2 '#2'",
-	"landingPage10IdealName":"(Test) LP v1 'presite'",
-	"landingPage11IdealName":"(Test) LP v2 'presite'",
-	"landingPage12IdealName":"LP SEA 1",
-	"landingPage01SupportName":"Démo LP Support",
-    "landingPage01IdealDesc":"Le modèle idéal pour votre landing page.",
-	"landingPage05IdealDesc":"Nouveau modèle de landing page (v2). Modèle classique. N'utiliser qu'avec une ambiance GRID",
-	"landingPage06IdealDesc":"Nouveau modèle de landing page (v2). Modèle neutre. N'utiliser qu'avec une ambiance GRID",
-	"landingPage07IdealDesc":"Nouveau modèle de landing page (v2). Modèle grid. N'utiliser qu'avec une ambiance GRID",
-	"landingPage08IdealDesc":"Nouveau modèle de landing page (v2). Modèle #1. N'utiliser qu'avec une ambiance GRID",
-	"landingPage09IdealDesc":"Nouveau modèle de landing page (v2). Modèle #2. N'utiliser qu'avec une ambiance GRID",
-	"landingPage10IdealDesc":"Test modèle de landing page (v1). Type Presite. Utilise le format LP idéale v1",
-	"landingPage11IdealDesc":"Test modèle de landing page (v2). Type Presite. Utilise le format LP idéale v2",
-	"landingPage12IdealDesc":"Nouveau modèle de landing page SEA. Modèle #1. N'utiliser qu'avec une ambiance GRID",
-	"landingPage01SupportDesc":"Modèle démo pour tests LP Support",
+    "Landing page":"Landing page",
+    "landingPage01Name":"Landing page 01",
+    "landingPage01Desc":"Landing page classique",
+	"landingPage01SupportName":"LP Support",
+	"landingPage01SupportDesc":"Modèle LP Support",
     "successImportAction" : "Import effectuée avec succès",
     "failedImportAction": "Le modèle de page n'a pas pu être importé",
     "previousVersionSuccesful":"La zone a été restaurée avec succès et <br/>la page a été sauvegardée",
Index: src/js/JEditor/PagePanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/nls/fr-fr/i18n.js	(révision 10387)
+++ src/js/JEditor/PagePanel/nls/fr-fr/i18n.js	(révision 10388)
@@ -104,69 +104,13 @@
     "introPageTxt04":"Glissez et déposez un bloc dans votre page pour l\'ajouter.",
     "introPageTxt05":"Survolez les éléments existants dans vos pages pour les déplacer ou les éditer.",
     "introPageEnd":"C'est parti !",
-    "Landing page":"Landing page",
-    "landingPage01Name":"Page 2COL-1",
-    "landingPage02Name":"Page 2COL-2",
-    "landingPage03Name":"Page 2COL-3",
-    "landingPage04Name":"Page 2COL-4",
-    "landingPage05Name":"Page 3COL-1",
-    "landingPage06Name":"Page 3COL-2",
-    "landingPage07Name":"Page 3COL-3",
-    "landingPage08Name":"Page 3COL-3.2",
-    "landingPage09Name":"Page 3COL-4",
-    "landingPage10Name":"Page 3COL-5",
-    "landingPage01Desc":"Pour home ou landing page deux colonnes. Idéal avec une image paysage format 3:2.",
-    "landingPage02Desc":"Pour home ou landing page deux colonnes. Idéal avec deux images paysage format 3:2.",
-    "landingPage03Desc":"Pour home ou landing page deux colonnes. Idéal avec deux images portrait et paysage format 3:2.",
-    "landingPage04Desc":"Pour home ou landing page deux colonnes. Idéal avec deux images paysage format 3:2.",
-    "landingPage05Desc":"Pour home ou landing page 3 colonnes. Idéal avec deux images paysage format 3:2.",
-    "landingPage06Desc":"Pour home ou landing page 3 colonnes. Idéal avec une image carrée format 1:1.",
-    "landingPage07Desc":"Pour home ou landing page 3 colonnes. Idéal avec deux image aux formats 16:9 et 1:1.",
-    "landingPage08Desc":"Pour home ou landing page 3 colonnes. Variante de LP 3COL-3 avec plus d’images (une 16:9, une 1:1 et cinq 3:2)",
-    "landingPage09Desc":"Pour home ou landing page 3 colonnes. Idéal avec trois images aux formats 1:1 et 3:2.",
-    "landingPage10Desc":"Pour home ou landing page 3 colonnes. Idéal avec cinq images aux formats 3:2 et 4:3.",
     "Tarifs":"Tarifs",
     "tarifPageDesc":"Idéal pour présenter les différentes prestations que vous assurez, ainsi que les prix correspondant.",
-    "landingPage01HomeName":"Home idéale 1",
-    "landingPage02HomeName":"(Test) HP idéale v2",
-	"landingPage03HomeName":"HP idéale v2 'classique'",
-	"landingPage04HomeName":"HP idéale v2 'neutre'",
-	"landingPage05HomeName":"HP idéale v2 'grid'",
-	"landingPage06HomeName":"HP idéale v2 '#1'",
-	"landingPage07HomeName":"HP idéale v2 '#2'",
-	"landingPage08HomeName":"HP idéale v2 '#3'",
-	"landingPage09HomeName":"HP idéale v2 '#4'",
-    "landingPage01HomeDesc":"Le modèle idéal pour votre page d'accueil.",
-	"landingPage03HomeDesc":"Nouveau modèle de home page (v2). Modèle classique. N'utiliser qu'avec une ambiance GRID",
-	"landingPage04HomeDesc":"Nouveau modèle de home page (v2). Modèle neutre. N'utiliser qu'avec une ambiance GRID",
-	"landingPage05HomeDesc":"Nouveau modèle de home page (v2). Modèle grid. N'utiliser qu'avec une ambiance GRID",
-	"landingPage06HomeDesc":"Nouveau modèle de home page (v2). Modèle #1. N'utiliser qu'avec une ambiance GRID",
-	"landingPage07HomeDesc":"Nouveau modèle de home page (v2). Modèle #2. N'utiliser qu'avec une ambiance GRID",
-	"landingPage08HomeDesc":"Nouveau modèle de home page (v2). Modèle #3. N'utiliser qu'avec une ambiance GRID",
-	"landingPage09HomeDesc":"Nouveau modèle de home page (v2). Modèle #4. N'utiliser qu'avec une ambiance GRID",
-    "landingPage01IdealName":"Landing Page idéale 1",
-    "landingPage02IdealName":"Landing Page idéale 2",
-    "landingPage03IdealName":"Landing Page idéale 3",
-    "landingPage04IdealName":"(Test) LP idéale v2",
-	"landingPage05IdealName":"LP idéale v2 'classique'",
-	"landingPage06IdealName":"LP idéale v2 'neutre'",
-	"landingPage07IdealName":"LP idéale v2 'grid'",
-	"landingPage08IdealName":"LP idéale v2 '#1'",
-	"landingPage09IdealName":"LP idéale v2 '#2'",
-	"landingPage10IdealName":"(Test) LP v1 'presite'",
-	"landingPage11IdealName":"(Test) LP v2 'presite'",
-	"landingPage12IdealName":"LP SEA 1",
-	"landingPage01SupportName":"Démo LP Support",
-    "landingPage01IdealDesc":"Le modèle idéal pour votre landing page.",
-	"landingPage05IdealDesc":"Nouveau modèle de landing page (v2). Modèle classique. N'utiliser qu'avec une ambiance GRID",
-	"landingPage06IdealDesc":"Nouveau modèle de landing page (v2). Modèle neutre. N'utiliser qu'avec une ambiance GRID",
-	"landingPage07IdealDesc":"Nouveau modèle de landing page (v2). Modèle grid. N'utiliser qu'avec une ambiance GRID",
-	"landingPage08IdealDesc":"Nouveau modèle de landing page (v2). Modèle #1. N'utiliser qu'avec une ambiance GRID",
-	"landingPage09IdealDesc":"Nouveau modèle de landing page (v2). Modèle #2. N'utiliser qu'avec une ambiance GRID",
-	"landingPage10IdealDesc":"Test modèle de landing page (v1). Type Presite. Utilise le format LP idéale v1",
-	"landingPage11IdealDesc":"Test modèle de landing page (v2). Type Presite. Utilise le format LP idéale v2",
-	"landingPage12IdealDesc":"Nouveau modèle de landing page SEA. Modèle #1. N'utiliser qu'avec une ambiance GRID",
-	"landingPage01SupportDesc":"Modèle démo pour tests LP Support",
+    "Landing page":"Landing page",
+    "landingPage01Name":"Landing page 01",
+    "landingPage01Desc":"Landing page classique",
+	"landingPage01SupportName":"LP Support",
+	"landingPage01SupportDesc":"Modèle LP Support",
     "successImportAction" : "Import effectuée avec succès",
     "failedImportAction": "Le modèle de page n'a pas pu être importé",
     "previousVersionSuccesful":"La zone a été restaurée avec succès et <br/>la page a été sauvegardée",
Index: src/js/JEditor/PagePanel/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/nls/i18n.js	(révision 10387)
+++ src/js/JEditor/PagePanel/nls/i18n.js	(révision 10388)
@@ -105,69 +105,13 @@
         "introPageTxt04":"Drag and drop a block in your page to add it.",
         "introPageTxt05":"Hover existing elements in your pages to move or edit them.",
         "introPageEnd":"Let's go !",
-        "Landing page":"Landing page",
-        "landingPage01Name":"Page 2COL-1",
-        "landingPage02Name":"Page 2COL-2",
-        "landingPage03Name":"Page 2COL-3",
-        "landingPage04Name":"Page 2COL-4",
-        "landingPage05Name":"Page 3COL-1",
-        "landingPage06Name":"Page 3COL-2",
-        "landingPage07Name":"Page 3COL-3",
-        "landingPage08Name":"Page 3COL-3.2",
-        "landingPage09Name":"Page 3COL-4",
-        "landingPage10Name":"Page 3COL-5",
-        "landingPage01Desc":"For home or landing page two columns. Ideal with a landscape image aspect ratio 3:2.",
-        "landingPage02Desc":"For home or landing page two columns. Ideal with two landscape images aspect ratio 3:2.",
-        "landingPage03Desc":"For home or landing page two columns. Ideal with two landscape and portrait images aspect ratio 3:2.",
-        "landingPage04Desc":"For home or landing page two columns. Ideal with two landscape images aspect ratio 3:2.",
-        "landingPage05Desc":"For home or landing page three columns. Ideal with two landscape images aspect ratio 3:2.",
-        "landingPage06Desc":"For home or landing page three columns. Ideal with a square image aspect ratio 3:2.",
-        "landingPage07Desc":"For home or landing page three columns. Ideal with two images aspect ratio 16:9 et 1:1.",
-        "landingPage08Desc":"For home or landing page three columns. Variant of Landing page 3COL-3 with more images (16:9, 1:1 and 3:2)",
-        "landingPage09Desc":"For home or landing page three columns. Ideal with three images aspect ratio 1:1 et 3:2.",
-        "landingPage10Desc":"For home or landing page three columns. Ideal with five images aspect ratio 3:2 et 4:3.",
         "Tarifs":"Prices",
         "tarifPageDesc":"Perfect to show your different services and the corresponding price.",
-        "landingPage01HomeName":"Ideal Homepage 1",
-        "landingPage02HomeName":"(Test) Ideal HP v2",
-		"landingPage03HomeName":"Ideal HP v2 'classic'",
-		"landingPage04HomeName":"Ideal HP v2 'neutral'",
-		"landingPage05HomeName":"Ideal HP v2 'grid'",
-		"landingPage06HomeName":"Ideal HP v2 '#1'",
-		"landingPage07HomeName":"Ideal HP v2 '#2'",
-		"landingPage08HomeName":"Ideal HP v2 '#3'",
-		"landingPage09HomeName":"Ideal HP v2 '#4'",
-		"landingPage01HomeDesc":"The ideal template for your homepage.",
-        "landingPage03HomeDesc":"New home page layout (v2). Classic model. Only use with GRID templates",
-		"landingPage04HomeDesc":"New home page layout (v2). Neutral model. Only use with GRID templates",
-		"landingPage05HomeDesc":"New home page layout (v2). Grid model. Only use with GRID templates",
-		"landingPage06HomeDesc":"New home page layout (v2). Model #1. Only use with GRID templates",
-		"landingPage07HomeDesc":"New home page layout (v2). Model #2. Only use with GRID templates",
-		"landingPage08HomeDesc":"New home page layout (v2). Model #3. Only use with GRID templates",
-		"landingPage09HomeDesc":"New home page layout (v2). Model #4. Only use with GRID templates",
-        "landingPage01IdealName":"Ideal Landingpage 1",
-        "landingPage02IdealName":"Ideal Landingpage 2",
-        "landingPage03IdealName":"Ideal Landingpage 3",
-        "landingPage04IdealName":"(Test) Ideal LP v2",
-		"landingPage05IdealName":"Ideal LP v2 'classic'",
-		"landingPage06IdealName":"Ideal LP v2 'neutral'",
-		"landingPage07IdealName":"Ideal LP v2 'grid'",
-		"landingPage08IdealName":"Ideal LP v2 '#1'",
-		"landingPage09IdealName":"Ideal LP v2 '#2'",
-		"landingPage10IdealName":"(Test) LP v1 'presite'",
-		"landingPage11IdealName":"(Test) LP v2 'presite'",
-		"landingPage12IdealName":"LP SEA 1",
-		"landingPage01SupportName":"LP Support demo",
-        "landingPage01IdealDesc":"The ideal template for your landingpage.",
-		"landingPage05IdealDesc":"New landing page layout (v2). Classic model. Only use with GRID templates",
-		"landingPage06IdealDesc":"New landing page layout (v2). Neutral model. Only use with GRID templates",
-		"landingPage07IdealDesc":"New landing page layout (v2). Grid model. Only use with GRID templates",
-		"landingPage08IdealDesc":"New landing page layout (v2). Model #1. Only use with GRID templates",
-		"landingPage09IdealDesc":"New landing page layout (v2). Model #2. Only use with GRID templates",
-		"landingPage10IdealDesc":"Test landing page layout (v1). Presite format. Landingpage v1 format",
-		"landingPage11IdealDesc":"Test landing page layout (v2). Presite format. Landingpage v2 format",
-		"landingPage12IdealDesc":"New landing page SEA. Model #1. Only use with GRID templates",
-		"landingPage01SupportDesc":"Demo for LP Support",
+        "Landing page":"Landing page",
+        "landingPage01Name":"Landing page 01",
+        "landingPage01Desc":"Classic landing page",
+		"landingPage01SupportName":"LP Support",
+		"landingPage01SupportDesc":"LP Support",
         "previousVersionSuccesful":"The content has been successfully restored and the page has been saved",
         "deleteContact": "Please set a different contact page <br\/> before you can delete it",
         "deleteLegale": "Please set a different Legal page <br\/> before you can delete it",
