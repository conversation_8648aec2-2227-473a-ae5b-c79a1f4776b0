Revision: r13066
Date: 2024-09-19 10:20:45 +0300 (lkm 19 Sep 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: formulaire, ordre des destinataires

## Files changed

## Full metadata
------------------------------------------------------------------------
r13066 | srazanandralisoa | 2024-09-19 10:20:45 +0300 (lkm 19 Sep 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/Form.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/Dest.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormDestView.js

wishlist IDEO3.2: formulaire, ordre des destinataires
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/Form.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/Form.js	(révision 13065)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/Form.js	(révision 13066)
@@ -220,6 +220,10 @@
             this.recipients.push(recipient);
             this.trigger("add:recipient", this, recipient);
         },
+        updateRecipientsOrder: function(orderedRecipients) {
+            this.recipients = orderedRecipients;
+            this.trigger('change:recipients', this.recipients);
+        },
         removeRecipient: function(recipient) {
             var index;
             if (recipient instanceof Recipient)
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/Dest.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/Dest.html	(révision 13065)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/Dest.html	(révision 13066)
@@ -1,4 +1,4 @@
-<div class="double-input deletable">
+<div class="double-input deletable" data-cid="<%= recipient.cid %>">
     <div class="top name">
         <input name="name" class="text field-input" value="<%=recipient.name%>" placeholder="<%=__('recipientName')%>">
 
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormDestView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormDestView.js	(révision 13065)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FormDestView.js	(révision 13066)
@@ -3,7 +3,9 @@
     "text!../Templates/formDestinataire.html",
     "JEditor/Commons/Ancestors/Views/View",
     "./DestView",
-    "i18n!../nls/i18n"
+    "i18n!../nls/i18n",
+    //not in param
+    "jqueryui/sortable"
 ], function($, formDestinataire, View, RecipientView,translate) {
     var FormDestinataireView = View.extend({
         events: {
@@ -37,7 +39,34 @@
                 that.$(".receiver-list").append(view.el);
                 view.render();
             });
+            this.makeSortable();
             return this;
+        },
+    
+        makeSortable: function() {
+            var that = this;
+            var $element = this.$(".receiver-list");
+            if ($element.children().length) {
+                $element.sortable({
+                    update: function(event, ui) {
+                        that.updateModelOrder();
+                    }
+                });
+            }
+            return this;
+        },
+
+        updateModelOrder: function() {
+            var that = this;
+            var $element = this.$(".receiver-list");
+            var orderedModels = $element.children().map(function() {
+                var modelCid = $(this).find('div.deletable').data('cid');
+                return that.model.recipients.find(function(recipient) {
+                    return recipient.cid === modelCid;
+                });
+            }).get();
+            // Assuming `this.model` has a method to update the order
+            this.model.updateRecipientsOrder(orderedModels);
         }
     });
 
