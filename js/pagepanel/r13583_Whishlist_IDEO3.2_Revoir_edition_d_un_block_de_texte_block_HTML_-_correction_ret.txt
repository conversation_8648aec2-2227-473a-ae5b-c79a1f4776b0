Revision: r13583
Date: 2024-12-09 16:05:09 +0300 (lts 09 Des 2024) 
Author: r<PERSON><PERSON>arinelina 

## Commit message
Whishlist IDEO3.2 : Revoir edition d'un block de texte / block HTML - correction retour

## Files changed

## Full metadata
------------------------------------------------------------------------
r13583 | rrakotoarinelina | 2024-12-09 16:05:09 +0300 (lts 09 Des 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Views/HtmlEditionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Templates/textEdition.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js

Whishlist IDEO3.2 : Revoir edition d'un block de texte / block HTML - correction retour
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Views/HtmlEditionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Views/HtmlEditionView.js	(révision 13582)
+++ src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Views/HtmlEditionView.js	(révision 13583)
@@ -56,6 +56,7 @@
         onCancel: function() {
 
             this.$el.dialog('close');
+            this.onClose();
         },
 
         onClose: function() {
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Templates/textEdition.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Templates/textEdition.html	(révision 13582)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Templates/textEdition.html	(révision 13583)
@@ -7,6 +7,14 @@
     background-color: #000000 !important;
 }
 
+select.cke_dialog_ui_input_select{
+    color: black !important;
+}
+
+.cke_dialog_ui_input_textarea{
+    color: black !important;
+}
+
 </style>
 <div class="text-edition-container">
     <div class="editor-wrapper">
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 13582)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 13583)
@@ -87,7 +87,7 @@
                             } else {
                                 this.CKconfig.toolbar = this.defaultToolbar;
                             }
-                            //initialiser modal 
+                           
                             this.textEditionDialog = new TextEditionView({textObject:this});
                         
                         },
@@ -111,7 +111,10 @@
                          * @argument {jQuery.Event} event Évenement jQuery
                          */
                         edit: function(event) {
-                            
+
+                            // Trigger a custom event
+                            this.textEditionDialog.trigger('updateOriginalContent', this.model.get('content'));
+
                             var editable = this.dom[this.cid].content;
                             editable.on('click.editBlock', function(event) {
                                 event.stopPropagation();
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js	(révision 13582)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js	(révision 13583)
@@ -14,13 +14,11 @@
     var TextEditionView = DialogView.extend({
         className: 'textEdition',
         events: {
-            'click .cke_dialog_ui_input_text': 'handleInputClick',
         },
         // events:  {'click .action.delete': "confirmDelete", 'keydown .content': '_onKeyPress', 'dblclick .content': 'avoidPropagation'},
-        
         constructor: function(options) {
             this.textBlockView = options.textObject;
-            this.originalContent = this.textBlockView.model.get('content');
+            // this.originalContent = this.textBlockView.model.get('content');
             this.editor = null;
 
             var opts = _.extend({
@@ -45,6 +43,10 @@
             return DialogView.call(this, opts);
         },
 
+        initContent: function() {
+            this.originalContent = this.textBlockView.model.get('content');
+        },
+
         initialize: function(options) {
             this._super();
             this._template = this.buildTemplate(textEdition, translate);
@@ -64,6 +66,11 @@
             return this;
         },
 
+        open: function() {
+            this.initContent();
+            DialogView.prototype.open.call(this);
+        },
+
         initializeCKEditor: function() {
             // Récupère la configuration CKEditor depuis textBlockView
             var editorConfig = _.extend({}, this.textBlockView.CKconfig, {
@@ -77,7 +84,7 @@
             this.editor.on('instanceReady', this._onCKReady, this);
 
             // z-index pour les barres d'outils
-            this.setupStyleComboZIndexHandler();
+            this.setupStyleHandler();
 
             // Gère les changements de l'éditeur
             this.editor.on('change', _.bind(this.onEditorChange, this));
@@ -98,13 +105,14 @@
             }
             this.constructor.editor.JEditorTextBlockView = this;
             
+
             this.constructor.editor.setReadOnly(false);
             this.constructor.editor.setData(this.originalContent);
             if (this.$el.hasClass('focused'))
                 this.$el.toggleClass('focused');
 
-            this.constructor.editor.on('dialogShow', function(event) {
-           
+                this.constructor.editor.on('dialogShow', function(event) {
+            
                 var dialogElement = $(event.data._.element.$);
                 var firstModal = $('.textEdition');
             
@@ -130,12 +138,15 @@
                 }).on('click.preventEscape keydown.preventEscape', function(e) {
                     e.stopPropagation();
                 });
+           });
 
-            });
-
             event.editor.focus();
             this.trigger(Events.LoadEvents.LOAD_SUCCESS, this);
         },
+        open: function() {
+            this.initContent();
+            DialogView.prototype.open.call(this);
+        },
 
         onEditorChange: function() {
             // Gère les changements en temps réel dans l'éditeur
@@ -154,6 +165,7 @@
                 this.textBlockView.model.set('content', this.originalContent);
             }
             this.$el.dialog('close');
+            this.onClose();
         },
 
         onOk: function() {
@@ -162,7 +174,7 @@
                 
                 // mettre a jour le model si ok
                 this.textBlockView.model.set('content', newContent);
-                
+
                 this.textBlockView.triggerUpdate();
                 
                 if (this.textBlockView.textDependency) {
@@ -169,8 +181,8 @@
                     this.textBlockView.textDependency.saved();
                 }
             }
-            
             this.$el.dialog('close');
+            this.onClose();
         },
 
         onClose: function() {
@@ -181,7 +193,11 @@
             
             this.remove();
         },
-        setupStyleComboZIndexHandler: function() {
+        updateOriginalContent: function() {
+            this.originalContent = this.textBlockView.model.get('content');
+            this.render();
+        },
+        setupStyleHandler: function() {
             //pour styles, format et fontsize du toolbar ckeditor
             $(document).on('click', '.cke_combo', _.bind(function(event) {
                 var $comboPanel = $('.cke_combopanel');
@@ -192,6 +208,43 @@
                     });
                 }
             }, this));
+            $(document).on('click', '.cke_button__ideolink', _.bind(function(event) {
+
+                var $ckePanel = $('.cke_panel');
+                if ($ckePanel.length) {
+                    $ckePanel.css({
+                        'z-index': '1000000004'
+                    });
+                }
+
+                var $ckPanelBlock = $('iframe.cke_panel_frame').contents().find('.cke_panel_block');
+                if ($ckPanelBlock.length) {
+                    $ckPanelBlock.css({
+                        'background-color': '#313131'
+                    });
+                }
+
+            }, this));
+            $(document).on('click', '.cke_button__blockquote', _.bind(function(event) {
+               
+                var $iframe = $('.ui-dialog iframe');
+                var $blockquotes = $iframe.contents().find('blockquote');
+                //add class to this element 
+                $blockquotes.css({
+                    'background': '#eee',
+                    'border-left': '10px solid #ccc',
+                    'padding': '15px',
+                    'color' : 'rgb(204, 204, 204)',
+                });
+            
+                $blockquotes.find('p').css({
+                    'display': 'inline',
+                    'font-style': 'italic'
+                });
+            
+               
+            }, this));
+
         },
         showOptionPanel: function() {
             //fermeture du modal
