Revision: r9945
Date: 2022-12-08 02:05:03 +0300 (lkm 08 Des 2022) 
Author: anthony 

## Commit message
remplacement plan google maps par version img statics (inte)

## Files changed

## Full metadata
------------------------------------------------------------------------
r9945 | anthony | 2022-12-08 02:05:03 +0300 (lkm 08 Des 2022) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Templates/mapPointStyle.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Views/MapPointView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Views/MapStyleOptionView.js

remplacement plan google maps par version img statics (inte)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Templates/mapPointStyle.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Templates/mapPointStyle.html	(révision 9944)
+++ src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Templates/mapPointStyle.html	(révision 9945)
@@ -3,7 +3,6 @@
     <span class="address-line edit-toggle"><span class="text"><%=address.address.name %></span><span class="icon-edit"></span></span>
     <span class="address-actions">
         <span class="delete"><span class="icon-delete"></span></span>
-        <span class="popup <%=address.popup?'active':''%>"><span class="icon-popup-enabled"></span><span class="icon-popup-disabled"></span></span>
         <span class="pointer"><span class="icon-pointer-<%=address.pointer%>"></span></span>
     </span>
 </div>
@@ -12,20 +11,8 @@
     <div class="container">
         <div class="row">
             <div class="wrapper pointer selected" ><span data-pointer="standard" class="icon-pointer-standard"></span></div>
-            <div class="wrapper pointer" ><span data-pointer="thin" class="icon-pointer-thin"></span></div>
-            <div class="wrapper pointer" ><span data-pointer="big" class="icon-pointer-big"></span></div>
-            <div class="wrapper pointer" ><span data-pointer="no-circle" class="icon-pointer-no-circle"></span></div>
-            <div class="wrapper pointer" ><span data-pointer="needle" class="icon-pointer-needle"></span></div>
         </div>
-        <div class="row">
-            <div class="wrapper pointer" ><span data-pointer="split-bubble" class="icon-pointer-split-bubble"></span></div>
-            <div class="wrapper pointer" ><span data-pointer="comma" class="icon-pointer-comma"></span></div>
-            <div class="wrapper pointer" ><span data-pointer="flag" class="icon-pointer-flag"></span></div>
-            <div class="wrapper pointer" ><span data-pointer="circle" class="icon-pointer-circle"></span></div>
-            <div class="wrapper pointer" ><span data-pointer="triangle" class="icon-pointer-triangle"></span></div>
-        </div>
 
-
         <div class="row pointer-color-selector">
             <% _.each(colors,function(color){ %>
             <div class="column <%=address.color===color?' active':'' %>"><span class="color <%= color %>" data-color="<%= color %>" ></span></div>
Index: src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Views/MapPointView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Views/MapPointView.js	(révision 9944)
+++ src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Views/MapPointView.js	(révision 9945)
@@ -15,8 +15,8 @@
             'click .selector': 'selectAddress',
             'click .delete': '_delete'
         },
-        colors: ["white", "black", "yellow", "red", "orange", "green", "blue", "turkish"],
-        pointers: ["standard", "thin", "big", "no-circle", "needle", "split-bubble", "comma", "flag", "circle", "triangle"],
+        colors: ["white", "black", "yellow", "red", "orange", "green", "blue"],
+        pointers: ["standard"],
         tagName: 'li',
         attributes: function() {
             var attrs = {
Index: src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Views/MapStyleOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Views/MapStyleOptionView.js	(révision 9944)
+++ src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Views/MapStyleOptionView.js	(révision 9945)
@@ -33,7 +33,7 @@
                 range: 'min'
             });
             this.dom[this.cid].heightSlider = this.$('.slider.height').slider({
-                max: 177,
+                max: 100,
                 min: 30,
                 value: this.model.aspectRatio,
                 range: 'min'
