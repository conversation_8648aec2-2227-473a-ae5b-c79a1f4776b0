Revision: r9929
Date: 2022-12-05 13:29:37 +0300 (lts 05 Des 2022) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
suite r9928

## Files changed

## Full metadata
------------------------------------------------------------------------
r9929 | srazanandralisoa | 2022-12-05 13:29:37 +0300 (lts 05 Des 2022) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageOptionsView.js

suite r9928
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageOptionsView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageOptionsView.js	(révision 9928)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageOptionsView.js	(révision 9929)
@@ -130,7 +130,6 @@
 		 * actualise l'affichage de la vue
 		 */
 		render : function() {
-			this.undelegateEvents();
 			try {
 			this.$('.figcaption-img').radioPanel('destroy');
 			} catch (e) {
@@ -165,7 +164,6 @@
 				});
 				this.$('.figcaption-img').radioPanel(); 
 			}
-			this.delegateEvents();
 			return this;
 			
 		},
