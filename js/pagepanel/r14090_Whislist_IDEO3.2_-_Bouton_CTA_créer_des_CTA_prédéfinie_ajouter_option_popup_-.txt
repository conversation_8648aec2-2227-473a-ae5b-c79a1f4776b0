Revision: r14090
Date: 2025-04-10 13:52:26 +0300 (lkm 10 Apr 2025) 
Author: rrakotoarinelina 

## Commit message
Whislist IDEO3.2 - Bouton CTA : créer des CTA prédéfinie + ajouter option popup - (partie JS) correction retour

## Files changed

## Full metadata
------------------------------------------------------------------------
r14090 | rrakotoarinelina | 2025-04-10 13:52:26 +0300 (lkm 10 Apr 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/CTACollection.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonCTAOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/UpdateCTAView.js

Whislist IDEO3.2 - Bouton CTA : créer des CTA prédéfinie + ajouter option popup - (partie JS) correction retour
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/CTACollection.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/CTACollection.js	(révision 14089)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/CTACollection.js	(révision 14090)
@@ -11,9 +11,9 @@
         parse: function(response) {
             return response; // Assuming the array of files is in the 'data' property of the response
         },
-        _getByShortCode: function(shortcode) {
+        _getByShortCodeAndLang: function(shortcode,lang) {
             return this.find(function(model) {
-                return model.get('ButtonOption').shortcode === shortcode;
+                return model.get('ButtonOption').shortcode === shortcode && model.get('lang') === lang.split('_')[0];
             });
         },
         //ensure that this functino work properly rendering one or many elements
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js	(révision 14089)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js	(révision 14090)
@@ -29,11 +29,12 @@
                         initialize: function() {
                             this._super();
                             this._contentTemplate = this.buildTemplate(template, translate);
-
+                            this.currentLang = this.app.currentPanel.currentLang
                             //utilisation donnée cta si bouton utilise un cta 
-                            this.listeCTA = CTACollection.getInstance();
+                            this.listeAllCTA = CTACollection.getInstance();
+                            this.listeCTA = this.listeAllCTA._getByLang(this.currentLang.id);
                             if (this.model.options.ButtonOption.shortcode && this.model.options.ButtonOption.shortcode != ""){
-                                this.usedCTA =  this.listeCTA._getByShortCode(this.model.options.ButtonOption.shortcode);
+                                this.usedCTA =  this.listeAllCTA._getByShortCodeAndLang(this.model.options.ButtonOption.shortcode,this.currentLang.id);
                                 if(this.usedCTA){
                                     Common.setCTAToButtonModel(this.model, this.usedCTA);
                                 }
@@ -48,11 +49,15 @@
                             this.listenTo(this.buttonOption, "input:change", this.onInputChange);
                             this.listenTo(this.buttonStyleOption, "remove:icon", this.removeIcon);
                             
-                            // this.listenTo(this.model, 'cta:selected', this.createButtonOptionView);
+                            this.listenTo(this.model, 'cta:selected', function(){
+                                this.svgName = this.model.options.ButtonStyleOption.icon;
+                                this.svgContent   = this.iconsSvgCollection.getSvgByName(this.svgName).content;
+                                this.render();
+                            }.bind(this));
 
                             this.buttonCTAOptionView = new ButtonCTAOptionView({
                                 buttonModel: this.model,
-                                currentLang : this.app.currentPanel.currentLang
+                                currentLang : this.currentLang
                             });
                             this.listenTo(this.model, 'change', this.onModelChange);
                             this.delegateEvents();         
@@ -159,7 +164,7 @@
                                     return false
                                 }
                                 if (this.model.options.ButtonOption.shortcode && this.model.options.ButtonOption.shortcode != "") {
-                                    var cta  = this.listeCTA._getByShortCode(this.model.options.ButtonOption.shortcode);
+                                    var cta  = this.listeAllCTA._getByShortCodeAndLang(this.model.options.ButtonOption.shortcode,this.currentLang.id);
                                     var compareResult = Common.areButtonModelAndCTASame(this.model, cta);
                                     if (!compareResult) {
                                         this.confirm({
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonCTAOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonCTAOptionView.js	(révision 14089)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonCTAOptionView.js	(révision 14090)
@@ -80,6 +80,7 @@
                 'listeCTAViewOnglet' : this.listeCTAView,
                 'listeCTAView' : listeCTAView2,
                 'buttonModel' : this.buttonModel,
+                'currentLang' : this.currentLang
             });
            this.updateCTADialog.open();
            
@@ -92,7 +93,7 @@
             //add class
             this._addSelectClass(e);
 
-            var selectedCTA = this.listeCTA._getByShortCode(ctaShortcode);
+            var selectedCTA = this.listeCTA._getByShortCodeAndLang(ctaShortcode,this.currentLang.id);
             Common.setCTAToButtonModel(this.buttonModel, selectedCTA);
 
             this.buttonModel.trigger('cta:selected');
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/UpdateCTAView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/UpdateCTAView.js	(révision 14089)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/UpdateCTAView.js	(révision 14090)
@@ -22,6 +22,7 @@
             //this.listeCTAViewOnglet instance utilisé dans onglet cta
             this.listeCTAViewOnglet = options.listeCTAViewOnglet;
             this.listeCTAView = options.listeCTAView;
+            this.currentLang = options.currentLang;
             this.listeCTA = CTACollection.getInstance();
             this.buttonModel = options.buttonModel;
             this.ctaShortcode = null;
@@ -61,7 +62,7 @@
             //add class
             this._addSelectClass(e);
 
-            var selectedCTA =  this.listeCTA._getByShortCode(ctaShortcode);
+            var selectedCTA =  this.listeCTA._getByShortCodeAndLang(ctaShortcode,this.currentLang.id);
             this.ctaShortcode = selectedCTA.ButtonOption.shortcode;
 
             // Enable save button after CTA selection
@@ -68,7 +69,7 @@
             this.$el.parent().find('.okay').removeClass('disabled');
         },
         _handleUpdateCTASuccess: function() {
-            var selectedCTA = this.listeCTA._getByShortCode(this.ctaShortcode);
+            var selectedCTA = this.listeCTA._getByShortCodeAndLang(this.ctaShortcode,this.currentLang.id);
             Common.setButtonModelToCTA(selectedCTA, this.ctaShortcode, this.buttonModel);
             
             this.listeCTAViewOnglet.render();
@@ -82,7 +83,8 @@
                 ButtonOption : this.buttonModel.options.ButtonOption.toJSON(),
                 ButtonStyleOption : this.buttonModel.options.ButtonStyleOption.toJSON(),
                 advancedCSS : this.buttonModel.options.advancedCSS.toJSON(),
-                id: this.ctaShortcode
+                id: this.ctaShortcode,
+                lang: this.currentLang.id.split('_')[0]
             });
             //ajout reference cta
             cta.ButtonOption.shortcode = this.ctaShortcode;
