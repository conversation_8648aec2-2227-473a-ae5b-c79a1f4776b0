Revision: r13599
Date: 2024-12-12 15:56:47 +0300 (lkm 12 Des 2024) 
Author: rrakotoarinelina 

## Commit message
Whishlist IDEO3.2 : Revoir edition d'un block de texte / block HTML - correction retour 2

## Files changed

## Full metadata
------------------------------------------------------------------------
r13599 | rrakotoarinelina | 2024-12-12 15:56:47 +0300 (lkm 12 Des 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Templates/htmlEdition.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Templates/textEdition.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/i18n.js

Whishlist IDEO3.2 : Revoir edition d'un block de texte / block HTML - correction retour 2
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Templates/htmlEdition.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Templates/htmlEdition.html	(révision 13598)
+++ src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Templates/htmlEdition.html	(révision 13599)
@@ -1,5 +1,27 @@
 <style>
-.ui-dialog .ui-dialog-content {
+.ui-dialog.htmlEdition {
+  position: fixed !important;
+  width: 90vw !important;
+  max-width: 80em !important;
+  max-height: 90vh !important;
+  left: 50% !important;
+  transform: translateX(-50%) !important;
+  top: 10vh !important;
+  z-index: 1000000000 !important;
+  display: block ;
+}
+.ui-dialog.htmlEdition .ui-dialog-content {
+   height: 70vh !important;
+}
+.ui-dialog.htmlEdition .cke_inner
+{
+    top: 0 !important;
+	
+}
+.ui-dialog.htmlEdition .cke_contents {
+    height: calc(70vh - 70px) !important;
+}
+.ui-dialog.htmlEdition .ui-dialog-content {
   text-align: left;
 }
 </style>
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Templates/textEdition.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Templates/textEdition.html	(révision 13598)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Templates/textEdition.html	(révision 13599)
@@ -1,12 +1,27 @@
 <style>
-/* Styles pour assurer la visibilité du texte dans CKEditor */
-.cke_editable,
-.cke_wysiwyg_frame, 
-.cke_wysiwyg_div {
-    color: black !important;
-    background-color: #000000 !important;
+.ui-dialog.textEdition {
+  position: fixed !important;
+  width: 90vw !important;
+  max-width: 80em !important;
+  max-height: 90vh !important;
+  left: 50% !important;
+  transform: translateX(-50%) !important;
+  top: 10vh !important;
+  z-index: 1000000000 !important;
+  display: block;
 }
+.ui-dialog.textEdition .ui-dialog-content {
+   height: 70vh !important;
+}
+.ui-dialog.textEdition .cke_inner{
+    top: 0 !important;
+	
+}
+.ui-dialog.textEdition .cke_contents {
+    height: calc(70vh - 70px) !important;
+}
 
+
 select.cke_dialog_ui_input_select{
     color: black !important;
 }
@@ -15,6 +30,10 @@
     color: black !important;
 }
 
+.ui-dialog.textEdition .cke_button__blockstyles_label{
+    display: inline !important;
+}
+
 </style>
 <div class="text-edition-container">
     <div class="editor-wrapper">
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 13598)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 13599)
@@ -42,7 +42,8 @@
                             skin: 'ideo',
                             fontSize_sizes: 'tiny/0.75em;small/0.875em;large/1.25em;xlarge/1.5em;xxlarge/2em;xxxlarge/2.5em',
                             contentsCss: [
-                                __IDEO_CSS_PATH__ + 'ideo3-back.css'
+                                __IDEO_CSS_PATH__ + 'ideo3-back.css',
+                                'body.cke_editable { color: #000000; background-color: #ffffff; font-family: Arial, Helvetica, sans-serif }'
                             ],
                             stylesSet: [
                                 { name: 'Highlight 1', element: 'span', attributes: { 'class': 'txt-highlight-1' } },
@@ -65,7 +66,8 @@
                             format_tags: 'p;h2;h3;h4;h5;h6',
                             entities:false,
                             autoParagraph:false,
-                            title:false
+                            title:false,
+                       
                         },
                         changeTimeout: null,
                         _onKeyPress: function(event) {
@@ -128,7 +130,8 @@
                          */
                         updateHeight: function(that) {
                             that.triggerUpdate();
-                        }
+                        },
+                        
                     });
             return TextBlockView;
         });
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js	(révision 13598)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js	(révision 13599)
@@ -8,9 +8,10 @@
     "ckeditor",
     "JEditor/PagePanel/Contents/Zones/Models/ZoneDependency",
     "JEditor/PagePanel/Contents/Ancestors/ContentView",
+    "JEditor/App/Messages/ConfirmUnsaved",
     //not in params
     "jqueryPlugins/dropdown"
-], function($, _, textEdition, Events, DialogView, translate, CKEDITOR,ZoneDependency,ContentView) {
+], function($, _, textEdition, Events, DialogView, translate, CKEDITOR,ZoneDependency,ContentView,ConfirmUnsaved) {
     var TextEditionView = DialogView.extend({
         className: 'textEdition',
         events: {
@@ -18,7 +19,6 @@
         // events:  {'click .action.delete': "confirmDelete", 'keydown .content': '_onKeyPress', 'dblclick .content': 'avoidPropagation'},
         constructor: function(options) {
             this.textBlockView = options.textObject;
-            // this.originalContent = this.textBlockView.model.get('content');
             this.editor = null;
 
             var opts = _.extend({
@@ -35,8 +35,6 @@
                         click: _.bind(this.onCancel, this)
                     }
                 ],
-                width: 1000,
-                height: 560,
                 close: _.bind(this.onClose, this)
             }, options);
 
@@ -52,7 +50,6 @@
             this._template = this.buildTemplate(textEdition, translate);
             this.listenTo(this.textBlockView.model, Events.BackboneEvents.CHANGE + ':content', this.onContentChange);
             this.trigger(Events.LoadEvents.LOAD_START, this);
-            // initialisation CKEditor
         },
         
         render: function() {
@@ -72,9 +69,12 @@
         },
 
         initializeCKEditor: function() {
+            var bodyFontFamily = $('body').css('font-family');
+            
             // Récupère la configuration CKEditor depuis textBlockView
             var editorConfig = _.extend({}, this.textBlockView.CKconfig, {
-                height: '400px'
+                height: '400px',
+                resize_enabled: false
             });
 
             // Initialise CKEditor dans le div content du template
@@ -169,6 +169,12 @@
         },
 
         onOk: function() {
+            this.updateModel();
+            this.$el.dialog('close');
+            this.onClose();
+        },
+
+        updateModel : function() {
             if (this.editor && this.textBlockView) {
                 var newContent = this.editor.getData();
                 
@@ -181,8 +187,6 @@
                     this.textBlockView.textDependency.saved();
                 }
             }
-            this.$el.dialog('close');
-            this.onClose();
         },
 
         onClose: function() {
@@ -246,11 +250,43 @@
             }, this));
 
         },
+        hasUnsavedChanges: function() {
+            var editorContent = this.editor.getData().trim();
+            var modelContent = this.textBlockView.model.get('content').trim();
+            var result = !(_.isEqual(editorContent, modelContent));
+            return result;
+        },
         showOptionPanel: function() {
-            //fermeture du modal
-            this.$el.dialog('close');
-            return ContentView.prototype.edit.call(this.textBlockView);
+
+            if (this.textBlockView && this.hasUnsavedChanges()) {
+                this.confirmUnsaved({
+                    message : translate("quitWithoutSaving"),
+                    title : translate("unsavedChanges"),
+                    type : 'delete-not-saved',
+                    onYes : _.bind(function() {
+                        this.updateModel();
+                        this.$el.dialog('close');
+                        return ContentView.prototype.edit.call(this.textBlockView);
+                    }, this),
+                    onNo : _.bind(function() {
+                        this.$el.dialog('close');
+                        return ContentView.prototype.edit.call(this.textBlockView);
+                    }, this),
+                    options : {
+                        dialogClass : 'delete no-close',
+                        dontAskAgain : true
+                    }
+                });
+            } 
+            else{
+                //fermeture du modal
+                this.$el.dialog('close');
+                return ContentView.prototype.edit.call(this.textBlockView);
+            }
         },
+        confirmUnsaved: function(params) {
+            this.app.messageDelegate.set(new ConfirmUnsaved(params));
+        }
 
     });
 
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/fr-ca/i18n.js	(révision 13598)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/fr-ca/i18n.js	(révision 13599)
@@ -2,4 +2,6 @@
     "textEdition":"Modification de texte",
     "save":"Enregistrer",
     "cancel":"Annuler",
+    "quitWithoutSaving" : "Vous n'avez pas enregistré les modifications apportées aux textes, voulez-vous les enregistrer ?",
+    "unsavedChanges" : "Sauvegarder les changements"
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/fr-fr/i18n.js	(révision 13598)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/fr-fr/i18n.js	(révision 13599)
@@ -2,4 +2,6 @@
     "textEdition":"Modification de texte",
     "save":"Enregistrer",
     "cancel":"Annuler",
+    "quitWithoutSaving" : "Vous n'avez pas enregistré les modifications apportées aux textes, voulez-vous les enregistrer ?",
+    "unsavedChanges" : "sauvegarder les changements"
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/i18n.js	(révision 13598)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/i18n.js	(révision 13599)
@@ -2,4 +2,6 @@
     "textEdition":"Text edit",
     "save":"Save",
     "cancel":"Cancel",
+    "quitWithoutSaving": "You haven't saved the changes you've made to the texts. Would you like to save them ?",
+    "unsavedChanges": "Save changes"
 }, "fr-fr":true, "fr-ca":true })
\ No newline at end of file
