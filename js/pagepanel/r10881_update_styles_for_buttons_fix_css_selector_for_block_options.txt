Revision: r10881
Date: 2023-05-04 12:07:23 +0300 (lkm 04 Mey 2023) 
Author: mpartaux 

## Commit message
update styles for buttons + fix css selector for block options

## Files changed

## Full metadata
------------------------------------------------------------------------
r10881 | mpartaux | 2023-05-04 12:07:23 +0300 (lkm 04 Mey 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonStyleOption.html
   M /branches/ideo3_v2/integration/src/less/imports/button_block/button_color.less
   M /branches/ideo3_v2/integration/src/less/imports/page_panel/module/block-options/advanced-option.less

update styles for buttons + fix css selector for block options
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonStyleOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonStyleOption.html	(révision 10880)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonStyleOption.html	(révision 10881)
@@ -226,7 +226,7 @@
                     <span class="bottom"></span>
                 </span>
                 <span class="container">
-                    <span class="icon icon-form-long_text"></span>
+                    <span class="icon-form-long_text"></span>
                     <span class="switch-container">
                         <span class="radio">
                             <span></span>
@@ -242,7 +242,7 @@
                     <span class="bottom"></span>
                 </span>
                 <span class="container">
-                    <span class="icon icon-form-long_text"></span>
+                    <span class="icon-form-long_text"></span>
                     <span class="switch-container">
                         <span class="radio">
                             <span></span>
@@ -258,7 +258,7 @@
                     <span class="bottom"></span>
                 </span>
                 <span class="container">
-                    <span class="icon icon-form-long_text"></span>
+                    <span class="icon-form-long_text"></span>
                     <span class="switch-container">
                         <span class="radio">
                             <span></span>
@@ -274,7 +274,7 @@
                     <span class="bottom"></span>
                 </span>
                 <span class="container">
-                    <span class="icon icon-form-long_text"></span>
+                    <span class="icon-form-long_text"></span>
                     <span class="switch-container">
                         <span class="radio">
                             <span></span>
@@ -290,7 +290,7 @@
                     <span class="bottom"></span>
                 </span>
                 <span class="container">
-                    <span class="icon icon-form-long_text"></span>
+                    <span class="icon-form-long_text"></span>
                     <span class="switch-container">
                         <span class="radio">
                             <span></span>
@@ -306,7 +306,7 @@
                     <span class="bottom"></span>
                 </span>
                 <span class="container">
-                    <span class="icon icon-form-long_text"></span>
+                    <span class="icon-form-long_text"></span>
                     <span class="switch-container">
                         <span class="radio">
                             <span></span>
Index: src/less/imports/button_block/button_color.less
===================================================================
--- src/less/imports/button_block/button_color.less	(révision 10880)
+++ src/less/imports/button_block/button_color.less	(révision 10881)
@@ -1,9 +1,12 @@
-.color-radio, .block-button{
-    background-color: var(--bg-actions);
-    color: var(--fg-actions);
+.color-radio .container,
+.block.buttonblock.view .content .block-button .button {
+    background-color: var(--bg-actions)!important;
+    color: var(--fg-actions)!important;
     border: 2px solid var(--border-actions);
     box-sizing: border-box;
+    font-weight: 600!important;
 }
+
 .vibrante{
     --bg-actions: var(--accent-surface1);
     --border-actions: var(--accent-border);
@@ -10,8 +13,9 @@
     --fg-actions: var(--accent-text1);
 }
 .contour{
-    --bg-actions: transparent;
-     --fg-actions: var(--fg-links);
+    --bg-actions: white;
+    --border-actions: var(--fg-links);
+    --fg-actions: var(--fg-links);
 }
 .pastel-lead{
     --bg-actions: var(--lead-surface0); 
@@ -24,5 +28,7 @@
     --fg-actions: var(--lead-text1);
 }
 .contour-lead{
-    --bg-actions: transparent; --border-actions: var(--lead-border); --fg-actions: var(--fg-lead);
+    --bg-actions: white;
+    --border-actions: var(--fg-lead);
+    --fg-actions: var(--fg-lead);
 }
\ No newline at end of file
Index: src/less/imports/page_panel/module/block-options/advanced-option.less
===================================================================
--- src/less/imports/page_panel/module/block-options/advanced-option.less	(révision 10880)
+++ src/less/imports/page_panel/module/block-options/advanced-option.less	(révision 10881)
@@ -12,7 +12,7 @@
 	}
 }
 
-.effect-radio {
+.effects .effect-radio {
 	[class^='surface'],
 	[class^='accent'],
 	[class^='lead'] {
