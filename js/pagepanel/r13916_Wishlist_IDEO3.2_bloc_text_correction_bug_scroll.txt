Revision: r13916
Date: 2025-02-24 10:21:56 +0300 (lts 24 Feb 2025) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
Wishlist IDEO3.2: bloc text correction bug scroll

## Files changed

## Full metadata
------------------------------------------------------------------------
r13916 | sraz<PERSON><PERSON><PERSON>oa | 2025-02-24 10:21:56 +0300 (lts 24 Feb 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js

Wishlist IDEO3.2: bloc text correction bug scroll
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js	(révision 13915)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js	(révision 13916)
@@ -68,6 +68,7 @@
         open: function() {
             this.initContent();
             DialogView.prototype.open.call(this);
+            $('body').css('overflow', 'hidden');
         },
         beforeClose: function(event, ui) {
             event.preventDefault();
@@ -202,6 +203,7 @@
                 this.editor.destroy();
                 this.editor = null;
             }
+            $('body').css('overflow', '');
             this.$el.dialog('close');
             this.remove();
         },
@@ -213,7 +215,6 @@
             //pour styles, format et fontsize du toolbar ckeditor
             $(document).on('click', '.cke_combo', _.bind(function(event) {
                 var $comboPanel = $('.cke_combopanel');
-                $('body').css('overflow', 'hidden');
                 if ($comboPanel.length) {
                     $comboPanel.css({
                         'z-index': '1000000004', 
