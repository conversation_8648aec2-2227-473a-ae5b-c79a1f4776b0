Revision: r11724
Date: 2023-12-12 11:12:57 +0300 (tlt 12 Des 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: Bug d'affichage admin block grille

## Files changed

## Full metadata
------------------------------------------------------------------------
r11724 | srazanandralisoa | 2023-12-12 11:12:57 +0300 (tlt 12 Des 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridStyleOption.html

wishlist IDEO3.2: Bug d'affichage admin block grille
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html	(révision 11723)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html	(révision 11724)
@@ -9,7 +9,7 @@
                 <% var _id= _.uniqueId('galerieStyle'); %>
                 <div><span class="effect-radio <%=(galerieStyle===0)?'active':''%>" id="<%=_id %>" data-value="0" data-helper="masonry"><span class="helper"><span class="help"><%=__("masonryLegend")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-photocss-column"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
                 <% var _id= _.uniqueId('galerieStyle'); %>
-                <div><span class="effect-radio <%=(galerieStyle===1)?'active':''%>" id="<%=_id %>" data-value="1" data-helper="grid"><span class="helper"><span class="help"><%=__("galerieLegend")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-photogalerie-block"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
+                <div><span class="effect-radio <%=(galerieStyle===1)?'active':''%>" id="<%=_id %>" data-value="1" data-helper="grid"><span class="helper"><span class="help"><%=__("galerieLegend")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-photogrid-block"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
             </div>
         </div>
     </article>
@@ -24,9 +24,9 @@
             <% var _id= _.uniqueId('formatImage'); %>
             <div><span class="effect-radio <%=(galerieFormat=='landscape')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="landscape" data-helper="landscape"><span class="helper"><span class="help"><%=__("landscape")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-transition-alpha"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
             <% var _id= _.uniqueId('formatImage'); %>
-            <div><span class="effect-radio <%=(galerieFormat=='portrait')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="portrait" data-helper="portrait"><span class="helper"><span class="help"><%=__("portrait")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-image-square"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
+            <div><span class="effect-radio <%=(galerieFormat=='portrait')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="portrait" data-helper="portrait"><span class="helper"><span class="help"><%=__("portrait")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-image-portrait"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
             <% var _id= _.uniqueId('formatImage'); %>
-            <div><span class="effect-radio <%=(galerieFormat=='square')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="square" data-helper="square"><span class="helper"><span class="help"><%=__("square")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-image-portrait"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
+            <div><span class="effect-radio <%=(galerieFormat=='square')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="square" data-helper="square"><span class="helper"><span class="help"><%=__("square")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-image-square"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
         </div>
     </div>
 </article>
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js	(révision 11723)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js	(révision 11724)
@@ -29,7 +29,7 @@
    "galerieStyleContent" :   "Style de grille",
    "galerieStyleLegend" :   "Appliquez un style de grille",
    "masonryLegend"         :   "Tuiles",
-   "galerieLegend"            :   "Galerie",
+   "galerieLegend"            :   "Grille",
    "galerieNombreImage"       :   "Nombre d'image",
    "galerieNombreImageLegend" :   "Glissez pour ajuster le nombre d'images affichées",
    "galerieStyleAffichage"    :   "Style des images",
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridStyleOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridStyleOption.html	(révision 11723)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridStyleOption.html	(révision 11724)
@@ -23,9 +23,9 @@
                 <% var _id= _.uniqueId('formatImage'); %>
                 <div><span class="effect-radio <%=(grilleFormat=='landscape')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="landscape" data-helper="landscape"><span class="helper"><span class="help"><%=__("landscape")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-transition-alpha"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
                 <% var _id= _.uniqueId('formatImage'); %>
-                <div><span class="effect-radio <%=(grilleFormat=='portrait')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="portrait" data-helper="portrait"><span class="helper"><span class="help"><%=__("portrait")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-image-square"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
+                <div><span class="effect-radio <%=(grilleFormat=='portrait')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="portrait" data-helper="portrait"><span class="helper"><span class="help"><%=__("portrait")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-image-portrait"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
                 <% var _id= _.uniqueId('formatImage'); %>
-                <div><span class="effect-radio <%=(grilleFormat=='square')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="square" data-helper="square"><span class="helper"><span class="help"><%=__("square")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-image-portrait"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
+                <div><span class="effect-radio <%=(grilleFormat=='square')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="square" data-helper="square"><span class="helper"><span class="help"><%=__("square")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-image-square"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
             </div>
         </div>
     </article>
