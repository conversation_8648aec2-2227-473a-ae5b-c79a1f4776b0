Revision: r11944
Date: 2024-02-20 14:18:24 +0300 (tlt 20 Feb 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
correction FileUploderView, bug preview pour le block Image

## Files changed

## Full metadata
------------------------------------------------------------------------
r11944 | srazanandralisoa | 2024-02-20 14:18:24 +0300 (tlt 20 Feb 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Views/FileUploaderView.js

correction FileUploderView, bug preview pour le block Image
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Files/Views/FileUploaderView.js
===================================================================
--- src/js/JEditor/Commons/Files/Views/FileUploaderView.js	(révision 11943)
+++ src/js/JEditor/Commons/Files/Views/FileUploaderView.js	(révision 11944)
@@ -48,7 +48,10 @@
                 if (this.currentFile) {
                    
                     if (this.currentFile.isImg()){
-                        var toDisplay = this.options.uploadParams.isOpengraph ? this.currentFile.fileUrl : this.currentFile.thumb;
+                        isOpengraph = false
+                        if (this.options.uploadParams)
+                            isOpengraph = this.options.uploadParams.isOpengraph;
+                        var toDisplay = (isOpengraph) ? this.currentFile.fileUrl : this.currentFile.thumb;
                         defaultPreview = '<div class="preview"><div class="imagepreview shown" style="background-image: url(' + toDisplay + '); background-size: cover; display: block; background-position: initial initial; background-repeat: initial initial;"></div></div>';
                     }
                     else{
