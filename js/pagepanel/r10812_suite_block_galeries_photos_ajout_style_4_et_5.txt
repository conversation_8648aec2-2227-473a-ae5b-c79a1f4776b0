Revision: r10812
Date: 2023-04-12 16:00:29 +0300 (lrb 12 Apr 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
 suite block galeries photos(ajout style 4 et 5) 

## Files changed

## Full metadata
------------------------------------------------------------------------
r10812 | srazana<PERSON>lisoa | 2023-04-12 16:00:29 +0300 (lrb 12 Apr 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/i18n.js

 suite block galeries photos(ajout style 4 et 5) 
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html	(révision 10811)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html	(révision 10812)
@@ -76,6 +76,34 @@
                         </div>
                     </div>
                 </label>
+            
+                <%  var _id=_.uniqueId('galerieStyleAffichage');%>
+                <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="4" id="<%=_id %>" <%=(galerieStyleAff==4)?'checked':''%>>
+                   <label for="<%=_id %>">
+                    <div class="wrapper">
+                        <div class="vertical-wrap">
+                            <span class="icon-wrapper">
+                                <span class="icon-collection-style4"></span>
+                            </span>
+                            <span class="name"><%= __("Style")%> 4</span>
+                            <span class="desc"><%= __("DescStyle4")%></span>
+                        </div>
+                    </div>
+                </label>
+
+                <%  var _id=_.uniqueId('galerieStyleAffichage');%>
+                <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="5"  id="<%=_id %>" <%=(galerieStyleAff==5)?'checked':''%>>
+                   <label for="<%=_id %>">
+                    <div class="wrapper">
+                        <div class="vertical-wrap">
+                            <span class="icon-wrapper">
+                                <span class="icon-collection-style5"></span>
+                            </span>
+                            <span class="name"><%= __("Style")%> 5</span>
+                            <span class="desc"><%= __("DescStyle5")%></span>
+                        </div>
+                    </div>
+                </label>
                 
         </div>
     </article>
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-ca/i18n.js	(révision 10811)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-ca/i18n.js	(révision 10812)
@@ -50,4 +50,6 @@
    "DescStyle2"           :  "Texte encadré sur l'image",
    "DescStyle3"           :  "Texte sur l'image" ,
    "addGalerieField": "ajouter une collection", 
+   "DescStyle4"           :  "Textes sous l'image avec bordures",
+   "DescStyle5"           :  "Textes sous l'image avec images arrondies"
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js	(révision 10811)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js	(révision 10812)
@@ -50,4 +50,6 @@
    "DescStyle2"           :  "Texte encadré sur l'image",
    "DescStyle3"           :  "Texte sur l'image",
    "addGalerieField": "ajouter une collection", 
+   "DescStyle4"           :  "Textes sous l'image avec bordures",
+   "DescStyle5"           :  "Textes sous l'image avec images arrondies"
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/i18n.js	(révision 10811)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/i18n.js	(révision 10812)
@@ -59,6 +59,8 @@
       "DescStyle2"           :  "Text framed on the image",
       "DescStyle3"           :  "Text on the image",
       "addGalerieField": "add collection",  
+      "DescStyle4"           : "Text under the image with borders",
+      "DescStyle5"           :  "Text under the image with rounded pictures" 
    },
    "fr-fr":true,
    "fr-ca":true
