Revision: r9928
Date: 2022-12-05 12:25:53 +0300 (lts 05 Des 2022) 
Author: sraz<PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2 block image: option figcaption partie Js

## Files changed

## Full metadata
------------------------------------------------------------------------
r9928 | srazanandralisoa | 2022-12-05 12:25:53 +0300 (lts 05 Des 2022) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Models/ImageOptions.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Templates/imageOptions.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageOptionsView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/i18n.js

wishlist IDEO3.2 block image: option figcaption partie Js
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Models/ImageOptions.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Models/ImageOptions.js	(révision 9927)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Models/ImageOptions.js	(révision 9928)
@@ -23,7 +23,8 @@
             defaults: {
                 optionType: 'image',
                 priority: 70,
-                contextInfos: null
+                contextInfos: null,
+                figcaption : 0
             },
             initialize: function() {
                 this._super();
@@ -70,6 +71,6 @@
                 return json;
             }
         });
-    ImageOptions.SetAttributes(['file', 'link', 'contextInfos']);
+    ImageOptions.SetAttributes(['file', 'link', 'contextInfos', 'figcaption']);
     return ImageOptions;
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-ca/i18n.js	(révision 9927)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-ca/i18n.js	(révision 9928)
@@ -10,5 +10,9 @@
   "image": "Image",
   "imageBlockOption": "Options du bloc d'image",
   "imageWarning": "Attention :",
-  "imageWarningMsg": "les images trouvées sur internet ne sont généralement pas libres d’utilisation. Pour vous aider à déterminer si une image est protégée par des droits d’auteur vous pouvez utiliser le service"
+  "imageWarningMsg": "les images trouvées sur internet ne sont généralement pas libres d’utilisation. Pour vous aider à déterminer si une image est protégée par des droits d’auteur vous pouvez utiliser le service",
+  "figcaptionImage" : "Afficher les informations de l'image",
+  "figShowTitle": "Afficher le titre",
+  "figShowAll":"Afficher le titre et la description",
+  "figinactive":"Ne rien afficher",
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-fr/i18n.js	(révision 9927)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-fr/i18n.js	(révision 9928)
@@ -10,5 +10,9 @@
   "image": "Image",
   "imageBlockOption": "Options du bloc d'image",
   "imageWarning": "Attention :",
-  "imageWarningMsg": "les images trouvées sur internet ne sont généralement pas libres d’utilisation. Pour vous aider à déterminer si une image est protégée par des droits d’auteur vous pouvez utiliser le service"
+  "imageWarningMsg": "les images trouvées sur internet ne sont généralement pas libres d’utilisation. Pour vous aider à déterminer si une image est protégée par des droits d’auteur vous pouvez utiliser le service",
+  "figcaptionImage" : "Afficher les information de l'image",
+  "figShowTitle": "Afficher le titre",
+  "figShowAll":"Afficher le titre et la description",
+  "figinactive":"Ne rien afficher",
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/i18n.js	(révision 9927)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/i18n.js	(révision 9928)
@@ -11,7 +11,11 @@
       "image":"Image",
       "imageBlockOption":"Options of the box image",
       "imageWarning": "Warning:",
-      "imageWarningMsg": "pictures found on the internet are usually not free to use. To help you identify if a picture is protected by copyright you can use"
+      "imageWarningMsg": "pictures found on the internet are usually not free to use. To help you identify if a picture is protected by copyright you can use",
+      "figcaptionImage" : "Show image information",
+      "figShowTitle": "Show title",
+      "figShowAll":"Show title and description",
+      "figinactive":"Show nothing",
    },
    "fr-fr":true,
    "fr-ca":true
Index: src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js
===================================================================
--- src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 9927)
+++ src/js/JEditor/Commons/libraries/jquery-plugins/uploader.js	(révision 9928)
@@ -472,8 +472,9 @@
             //this.dataArray.remove(currentUpload.index);
             //this._globalProgress();
             if (error !== 'abort') {
-                var err = this.options.lang.uploadFailServerError.replace('%name%', currentUpload.name).replace('%error%', error);
-                this.errors.push(err);
+                var err = this.options.lang.uploadFailTooBig.replace('%name%', currentUpload.name);
+                this.$message.find('p').text(err);
+               // this.errors.push(err);
             }
             currentUpload.preview.off('click.abort').remove();
             //delete this.previews[currentUpload.name];
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Templates/imageOptions.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Templates/imageOptions.html	(révision 9927)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Templates/imageOptions.html	(révision 9928)
@@ -12,6 +12,27 @@
             <input value="<%= contextInfos.title %>" data-name="title" placeholder="<%= titlePlaceholder %>" <%= disable %>/>
             <input value="<%= contextInfos.desc %>" data-name="desc" placeholder="<%= descPlaceholder %>"  <%= disable %>/>
         </div>
+        <div class="figcaption-img">
+            <p class="panel-legend"><%=__("figcaptionImage")%></p>
+            <div class="panel-radio <%=figcaption==1?'selected':''%>" data-value="1" >
+                <div class="panel-radio-title">
+                    <div class="radio-wrapper"><span class="icon-radio-active"></span><span class="icon-radio-inactive"></span></div>
+                    <div class="radio-label"><%= __("figShowTitle")%></div>
+                </div>
+            </div>
+            <div class="panel-radio <%=figcaption==2?'selected':''%>" data-value="2" >
+                <div class="panel-radio-title">
+                    <div class="radio-wrapper"><span class="icon-radio-active"></span><span class="icon-radio-inactive"></span></div>
+                    <div class="radio-label"><%= __("figShowAll")%></div>
+                </div>
+            </div>
+            <div class="panel-radio <%=figcaption==0?'selected':''%>" data-value="0" >
+                <div class="panel-radio-title">
+                    <div class="radio-wrapper"><span class="icon-radio-active"></span><span class="icon-radio-inactive"></span></div>
+                    <div class="radio-label"><%= __("figinactive")%></div>
+                </div>
+            </div>
+        </div>
         <div class="link-img">
             <p class="panel-legend"><%=__("selectClickActionImage")%></p>
 
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageOptionsView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageOptionsView.js	(révision 9927)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageOptionsView.js	(révision 9928)
@@ -10,7 +10,10 @@
 		"collection!JEditor/Commons/Pages/Models/PageCollection",
 		"JEditor/FilePanel/Views/ImageEditView",
 		"JEditor/PagePanel/Contents/Zones/Models/ZoneDependency",
-		"i18n!../nls/i18n" ], function($, _, imageOptions, Events, AbstractOptionView, FileDBCollection, FileUploaderView, LinkView, PageCollection,ImageEditView, ZoneDependency,  translate) {
+		"i18n!../nls/i18n",
+		// not in param
+		"jqueryPlugins/radioPanel",
+	 ], function($, _, imageOptions, Events, AbstractOptionView, FileDBCollection, FileUploaderView, LinkView, PageCollection,ImageEditView, ZoneDependency,  translate) {
 	var /**
 		 * Vue des Options du bloc image
 		 *
@@ -27,7 +30,8 @@
 		events : {
 			'uploadercomplete .uploader' : '_onUploaded',
 			'change .infos-img>input' : 'updateImageInfos',
-			'click .cropbtn' : 'editImage'
+			'click .cropbtn' : 'editImage',
+			'radiopanelchange': '_onRadioChange'
 		},
 		updateImageInfos : function(event) {
 			var $target = $(event.currentTarget);
@@ -126,34 +130,48 @@
 		 * actualise l'affichage de la vue
 		 */
 		render : function() {
-			var lang = this.app.currentPanel.currentLang.id;
-			var titlePlaceholder = this.model.file.title[lang] ? this.model.file.title[lang] : this.translate('imageTitle');
-			var descPlaceholder = this.model.file.desc[lang] ? this.model.file.desc[lang] : this.translate('imageAlt');
-			var disable = (this.model.file.fileUrl === '#')? 'disabled=""' : '';
-			var templateVars = {
-				file : this.model.file,
-				link : this.model.link,
-				contextInfos : this.model.contextInfos,
-				titlePlaceholder : titlePlaceholder,
-				descPlaceholder : descPlaceholder,
-				disable : disable
-			};
-			_.extend(templateVars, {
-				currentPage : false
-			});
-			this.$el.html(this._template(templateVars));
-			this.$('.image-option header').after(this.fileUploader.el);
-			this.fileUploader.render();
-			this._linkSelectorView = new LinkView({
-				model : this.model.link,
-				pageCollection : PageCollection.getInstance()
-			});
-			this.$('.link-img').append(this._linkSelectorView.render({model:this.model.link}).el);
-			this.scrollables({
-                advanced:{ autoScrollOnFocus: false }
-            });
+			this.undelegateEvents();
+			try {
+			this.$('.figcaption-img').radioPanel('destroy');
+			} catch (e) {
+			//not so bad
+			} finally {
+				var lang = this.app.currentPanel.currentLang.id;
+				var titlePlaceholder = this.model.file.title[lang] ? this.model.file.title[lang] : this.translate('imageTitle');
+				var descPlaceholder = this.model.file.desc[lang] ? this.model.file.desc[lang] : this.translate('imageAlt');
+				var disable = (this.model.file.fileUrl === '#')? 'disabled=""' : '';
+				var templateVars = {
+					file : this.model.file,
+					link : this.model.link,
+					contextInfos : this.model.contextInfos,
+					titlePlaceholder : titlePlaceholder,
+					descPlaceholder : descPlaceholder,
+					disable : disable,
+					figcaption : this.model.figcaption
+				};
+				_.extend(templateVars, {
+					currentPage : false
+				});
+				this.$el.html(this._template(templateVars));
+				this.$('.image-option header').after(this.fileUploader.el);
+				this.fileUploader.render();
+				this._linkSelectorView = new LinkView({
+					model : this.model.link,
+					pageCollection : PageCollection.getInstance()
+				});
+				this.$('.link-img').append(this._linkSelectorView.render({model:this.model.link}).el);
+				this.scrollables({
+					advanced:{ autoScrollOnFocus: false }
+				});
+				this.$('.figcaption-img').radioPanel(); 
+			}
+			this.delegateEvents();
 			return this;
+			
 		},
+		_onRadioChange: function (event, data) {
+			this.model.figcaption = data.value;
+		},
 		/**
 		 * Déclenché lorsqu'on enregistre l'image
 		 *
