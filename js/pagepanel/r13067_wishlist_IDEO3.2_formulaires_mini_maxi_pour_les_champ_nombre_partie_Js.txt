Revision: r13067
Date: 2024-09-19 10:26:11 +0300 (lkm 19 Sep 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: formulaires, mini maxi pour les champ nombre (partie Js)

## Files changed

## Full metadata
------------------------------------------------------------------------
r13067 | srazanandralisoa | 2024-09-19 10:26:11 +0300 (lkm 19 Sep 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/Field.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/FieldBuilder.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FieldBuilderView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js

wishlist IDEO3.2: formulaires, mini maxi pour les champ nombre (partie Js)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/Field.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/Field.js	(révision 13066)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Models/Field.js	(révision 13067)
@@ -23,6 +23,11 @@
                 if (this.options.indexOf('previousDate_true') === -1 && this.options.indexOf('previousDate_false') === -1) {
                     this.options = ['previousDate_true'];
                 }
+            }else if (this.type === "number") {
+               if (this.options.length === 0) this.options = {
+                min: 0
+               };
+
             }this.on("change:type",function () {
                 if(this.type==="name")
                     this.mandatory=true;
@@ -38,6 +43,12 @@
             if (allowedTypes.lastIndexOf(attrs.type) === -1) {
                 ret.push("invalidInputType");
             }
+            if (attrs.type == 'number'){
+               if(typeof attrs.min !== 'undefined')
+                    attrs.options.min =  (attrs.min !== null) ? parseInt(attrs.min) :'';
+               if(typeof attrs.max !== 'undefined')
+                    attrs.options.max =  (attrs.max !== null) ? parseInt(attrs.max) :'';
+            }
             if (ret.length === 0)
                 ret = void(0);
             return ret;
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/FieldBuilder.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/FieldBuilder.html	(révision 13066)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Templates/FieldBuilder.html	(révision 13067)
@@ -22,17 +22,26 @@
                 <label><%=__("labelEndDate")%> :</label>
                 <input class="field-input" name="labelenddate" value="<%= dateRangeLabels.endDate %>" type="text" placeholder="<%=__('Label')%>">
         </div>
-
+        <% if(field.type !== 'number'){%>
         <div class="options">
             <label>
             <%=__("formFieldOptionsMessage")%> :</label>
             <textarea name="options" class="options-list field-input" data-filter="optionsToArray" placeholder="<%=__('options')%>"><%= field.options.join("\n") %></textarea>
         </div>
+        <%}%>
         <div class="placeholder">
             <label>
                 <%=__("placeholder")%> :</label>
             <input name="placeholder" class="field-input" value="<%= field.placeholder %>" type="text" placeholder="<%= __('placeholder') %>">
         </div>
+        <% if(field.type == 'number'){%>
+        <div class="number-options">
+            <label ><%=__("numberMin")%> :</label>
+            <input class="field-input" name="min"  value="<%= field.options.min%>" type="text" placeholder="<%=__('min')%>">
+            <label><%=__("numberMax")%> :</label>
+            <input class="field-input" name="max"  value="<%= field.options.max%>" type="text" placeholder="<%=__('max')%>">
+        </div>
+        <%}%>
         <div class="checkbox underline">
             <input id="underline-<%=field.cid%>" name="underline" class="field-input" <%=field.underline?'checked="checked"':''%> type="checkbox" value="1" />
             <label class="checkbox-lbl" for="underline-<%=field.cid%>">
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FieldBuilderView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FieldBuilderView.js	(révision 13066)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/Views/FieldBuilderView.js	(révision 13067)
@@ -34,7 +34,7 @@
             View.prototype.render.apply(this,arguments);
             //cacher l'option previous date pour les autre champs 
             var previousDate = true;
-            if(this.checkPreviousDateOption(this.model.options) > -1 ){
+            if(this.model.type != 'number' && this.checkPreviousDateOption(this.model.options) > -1 ){
                 var previousDateValue  = this.model.options[this.checkPreviousDateOption(this.model.options)];
                 previousDate = (previousDateValue === 'previousDate_true') ? true : false;
             }
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js	(révision 13066)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-ca/i18n.js	(révision 13067)
@@ -85,4 +85,8 @@
     'from':'Du',
     'to':'au',
     "daterange":"Intervalle de dates",
+    "numberMin" : "Minimim",
+    "numberMax" : "Maximum",
+    "min"  :"Min",
+    "max" : "Max",
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js	(révision 13066)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/fr-fr/i18n.js	(révision 13067)
@@ -85,4 +85,8 @@
     'from':'Du',
     'to':'au',
     "daterange":"Intervalle de dates",
+    "numberMin" : "Minimim",
+    "numberMax" : "Maximum",
+    "min"  :"Min",
+    "max" : "Max"
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js	(révision 13066)
+++ src/js/JEditor/PagePanel/Contents/Blocks/FormBlock/nls/i18n.js	(révision 13067)
@@ -91,6 +91,10 @@
         'from':'From',
         'to':'to',
         "daterange":"Interval of dates",
+        "numberMin" : "Minimim",
+        "numberMax" : "Maximum",
+        "min"  :"Min",
+        "max" : "Max",
     },
     "fr-fr":true,
     "fr-ca": true
