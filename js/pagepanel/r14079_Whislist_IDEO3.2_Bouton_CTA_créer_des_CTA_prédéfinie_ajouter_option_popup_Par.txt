Revision: r14079
Date: 2025-04-09 11:17:24 +0300 (lrb 09 Apr 2025) 
Author: rrak<PERSON>arinelina 

## Commit message
Whislist IDEO3.2 : Bouton CTA : créer des CTA prédéfinie + ajouter option popup (<PERSON>ie JS) - retour

## Files changed

## Full metadata
------------------------------------------------------------------------
r14079 | rrakotoarinelina | 2025-04-09 11:17:24 +0300 (lrb 09 Apr 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonCTAOptionView.js

Whislist IDEO3.2 : Bouton CTA : créer des CTA prédéfinie + ajouter option popup (Partie JS) - retour
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonCTAOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonCTAOptionView.js	(révision 14078)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonCTAOptionView.js	(révision 14079)
@@ -73,7 +73,9 @@
         _onUpdateCTA: function(e) {
 
             e.preventDefault();
-            var listeCTAView2 = new listeCTAView({});
+            var listeCTAView2 = new listeCTAView({
+                'currentLang' : this.currentLang
+            });
             this.updateCTADialog  = new UpdateCTAView({
                 'listeCTAViewOnglet' : this.listeCTAView,
                 'listeCTAView' : listeCTAView2,
