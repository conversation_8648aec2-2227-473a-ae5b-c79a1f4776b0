Revision: r10098
Date: 2023-01-06 13:45:13 +0300 (zom 06 Jan 2023) 
Author: anthony 

## Commit message
suppression de GalleryBlock (pas le dossier utilisé par d'autre block)

## Files changed

## Full metadata
------------------------------------------------------------------------
r10098 | anthony | 2023-01-06 13:45:13 +0300 (zom 06 Jan 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/main.js
   M /branches/ideo3_v2/integration/src/js/build.js
   M /branches/ideo3_v2/integration/src/js/config.js
   M /branches/ideo3_v2/integration/src/js/main.js

suppression de GalleryBlock (pas le dossier utilisé par d'autre block)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js	(révision 10097)
+++ src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js	(révision 10098)
@@ -1,6 +1,5 @@
 define([
   "./ImageBlock",
-  "./GalleryBlock",
   "./HtmlBlock",
   "./TwitterTimelineBlock",
   "./SiteMapBlock",
@@ -20,7 +19,6 @@
   "./CarrouselBlock"
 ], function (
   ImageBlock,
-  GalleryBlock,
   HtmlBlock,
   TwitterTimelineBlock,
   SiteMapBlock,
@@ -41,7 +39,6 @@
 ) {
   var component = {
     "ImageBlock": ImageBlock,
-    "GalleryBlock": GalleryBlock,
     "HtmlBlock": HtmlBlock,
     "TwitterTimelineBlock": TwitterTimelineBlock,
     "SiteMapBlock": SiteMapBlock,
Index: src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json	(révision 10097)
+++ src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json	(révision 10098)
@@ -1,6 +1,5 @@
 {
  "ImageBlock": "ImageBlock",
- "GalleryBlock": "GalleryBlock",
  "HtmlBlock": "HtmlBlock",
  "TwitterTimelineBlock": "TwitterTimelineBlock",
  "SiteMapBlock": "SiteMapBlock",
Index: src/js/JEditor/PagePanel/Contents/Blocks/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/main.js	(révision 10097)
+++ src/js/JEditor/PagePanel/Contents/Blocks/main.js	(révision 10098)
@@ -2,7 +2,6 @@
   "./Block/main",
   "./Blocks",
   "./FormBlock/main",
-  "./GalleryBlock/main",
   "./HtmlBlock/main",
   "./ImageBlock/main",
   "./LegalsBlock/main",
@@ -16,7 +15,6 @@
   Block,
   Blocks,
   FormBlock,
-  GalleryBlock,
   HtmlBlock,
   ImageBlock,
   LegalsBlock,
@@ -31,7 +29,6 @@
     "Block": Block,
     "Blocks": Blocks,
     "FormBlock": FormBlock,
-    "GalleryBlock": GalleryBlock,
     "HtmlBlock": HtmlBlock,
     "ImageBlock": ImageBlock,
     "LegalsBlock": LegalsBlock,
Index: src/js/build.js
===================================================================
--- src/js/build.js	(révision 10097)
+++ src/js/build.js	(révision 10098)
@@ -43,7 +43,6 @@
         "JEditor/PagePanel/Contents/Options",
         "JEditor/PagePanel/Contents/Blocks/Block",
         "JEditor/PagePanel/Contents/Blocks/FormBlock",
-        "JEditor/PagePanel/Contents/Blocks/GalleryBlock",
         "JEditor/PagePanel/Contents/Blocks/CarrouselBlock",
         "JEditor/PagePanel/Contents/Blocks/HtmlBlock",
         "JEditor/PagePanel/Contents/Blocks/ImageBlock",
Index: src/js/config.js
===================================================================
--- src/js/config.js	(révision 10097)
+++ src/js/config.js	(révision 10098)
@@ -39,7 +39,6 @@
         "JEditor/PagePanel/Contents/Options",
         "JEditor/PagePanel/Contents/Blocks/Block",
         "JEditor/PagePanel/Contents/Blocks/FormBlock",
-        "JEditor/PagePanel/Contents/Blocks/GalleryBlock",
         "JEditor/PagePanel/Contents/Blocks/HtmlBlock",
         "JEditor/PagePanel/Contents/Blocks/ImageBlock",
         "JEditor/PagePanel/Contents/Blocks/LegalsBlock",
Index: src/js/main.js
===================================================================
--- src/js/main.js	(révision 10097)
+++ src/js/main.js	(révision 10098)
@@ -48,7 +48,6 @@
     "JEditor/PagePanel/Contents/Options",
     "JEditor/PagePanel/Contents/Blocks/Block",
     "JEditor/PagePanel/Contents/Blocks/FormBlock",
-    "JEditor/PagePanel/Contents/Blocks/GalleryBlock",
     "JEditor/PagePanel/Contents/Blocks/CarrouselBlock",
     "JEditor/PagePanel/Contents/Blocks/HtmlBlock",
     "JEditor/PagePanel/Contents/Blocks/ImageBlock",
