Revision: r13838
Date: 2025-02-17 15:19:40 +0300 (lts 17 Feb 2025) 
Author: rrakotoarinelina 

## Commit message
Whislist IDEO3.2 : De<PERSON><PERSON> des cta - partie JS

## Files changed

## Full metadata
------------------------------------------------------------------------
r13838 | rrakotoarinelina | 2025-02-17 15:19:40 +0300 (lts 17 Feb 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonOption.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/CTACollection.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/CTAModel.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonCTAOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonOption.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/listeCTA.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/updateCTA.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Utils
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Utils/Common.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonCTAOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/UpdateCTAView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/listeCTAView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/PagePanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/button_block/styleOptions.less

Whislist IDEO3.2 : Definir des cta - partie JS
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonOption.js	(révision 13837)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonOption.js	(révision 13838)
@@ -19,7 +19,7 @@
              * @lends ImageOptionss
              */
                     {
-                        defaults: {optionType: 'ButtonOption', priority: 70, text: null, isDifferentText: false, textOnMobile: null},
+                        defaults: {optionType: 'ButtonOption', priority: 70, text: null, isDifferentText: false, textOnMobile: null, shortcode:null},
                         initialize: function() {
                             this._super();
                             if (!(this.link instanceof Link)) {
@@ -40,8 +40,10 @@
                             this.text = this.savedState.text;
                             this.isDifferentText = this.savedState.isDifferentText;
                             this.textOnMobile = this.savedState.textOnMobile;
+                            this.shortcode = this.savedState.shortcode;
+                            this.link.set(this.savedState.link);
                         }
                     });
-            ButtonOption.SetAttributes(['text','link', 'isDifferentText', 'textOnMobile']);
+            ButtonOption.SetAttributes(['text','link', 'isDifferentText', 'textOnMobile','shortcode']);
             return ButtonOption;
         });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/CTACollection.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/CTACollection.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/CTACollection.js	(révision 13838)
@@ -0,0 +1,23 @@
+define([
+    "JEditor/Commons/Ancestors/Models/DBCollection",
+    "JEditor/Commons/Ancestors/Models/Collection",
+    "JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/CTAModel",
+], function(DBCollection, Collection, CTAModel) {
+    var CTACollection = DBCollection.extend({
+        model: CTAModel,
+        url: function() {
+            return __IDEO_API_PATH__ + "/cta/";
+        },
+        parse: function(response) {
+            return response; // Assuming the array of files is in the 'data' property of the response
+        },
+        _getByShortCode: function(shortcode) {
+            return this.find(function(model) {
+                return model.get('ButtonOption').shortcode === shortcode;
+            });
+        }
+        
+    });
+
+    return CTACollection;
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/CTAModel.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/CTAModel.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/CTAModel.js	(révision 13838)
@@ -0,0 +1,31 @@
+define([
+    "JEditor/Commons/Ancestors/Models/NestedModel", 
+    "./ButtonOption",
+    "JEditor/Commons/Links/Models/Link",
+], function(NestedModel,ButtonOption,Link) {
+    /**
+     * Custom Button Model
+     * @class CustomButtonModel
+     * @extends NestedModel
+
+     */
+    var CTAModel = NestedModel.extend({
+        urlRoot: __IDEO_API_PATH__ + '/cta/',
+        defaults: {
+            ButtonOption : {},
+            ButtonStyleOption : {},
+            advancedCSS : {},
+        },
+
+        initialize: function() {
+            this._super();
+            // Fix: ButtonOption should handle its own link initialization
+            if (!(this.get('ButtonOption').link instanceof Link)) {
+                this.get('ButtonOption').link = new Link(this.get('ButtonOption').link || {});
+            }
+        },
+
+    });
+    CTAModel.SetAttributes(['ButtonOption', 'ButtonStyleOption', 'advancedCSS']);
+    return CTAModel;
+});
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonCTAOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonCTAOption.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonCTAOption.html	(révision 13838)
@@ -0,0 +1,43 @@
+<div class="panel-option-container animated create-call-to-action">
+    <article class="panel-option">
+        <header>
+            <h3 class="option-name">
+                <span class="icon-button-block_icon icon-midsize"></span>
+                <%=__("createCTA")%>
+            </h3>
+            <span class="panel-content-legend">
+                <%=__("createCTALegent")%>
+            </span>
+        </header>
+        <div class="option-content">
+            <button class="page-selector btn-add-cta">
+                <span class="icon-add"></span>
+                <span class="label"><%=__("addCTA")%></span>
+            </button>
+            <br>
+            <% if (ctaNotEmpty) { %>
+            <button class="dialog-view-trigger page-selector btn-update-cta">
+                <span class="icon-refresh"></span>
+                <span class="label"><%=__("updateExistingCTA")%></span>
+            </button>
+            <% } %>
+        </div>
+    </article>
+</div> 
+<% if (ctaNotEmpty) { %>
+<div class="panel-option-container animated cta-button">
+    <article class="panel-option">
+       <header>
+          <h3 class="option-name">
+             <span class="icon-drop"></span>
+             <%=__("applyCTA")%>
+          </h3>
+          <span class="panel-content-legend"> <%=__("applyCTALegend")%></span>
+       </header>
+       <div class="category-content cta-list-content">
+          
+       </div>
+    </article>
+ </div>
+ <% } %>
+
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonOption.html	(révision 13837)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonOption.html	(révision 13838)
@@ -1,6 +1,10 @@
 <div class="panel-option-container animated template-option">
     <article class="panel-option template-option">
         <header>
+            <h3 class="option-name"><span class="icon-button-text"></span><%=__("callToAction")%></h3>
+            <p class="panel-content-legend"><%= __("callToActionLegend")%></p>
+        </header>
+        <header>
             <h3 class="option-name"><span class="icon-button-text"></span><%=__("addButton")%></h3>
             <p class="panel-content-legend"><%= __("buttonOptionLegend")%></p>
         </header>
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/listeCTA.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/listeCTA.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/listeCTA.html	(révision 13838)
@@ -0,0 +1,28 @@
+ <%
+             for(var i=0;i< liste.length; i++){
+             
+             var cta=liste[i];
+             
+             %>
+          <% var _id= _.uniqueId('cta'); %>
+          <div class="color-radio cta-radio-container">
+            <!-- add an if statment here  -->
+
+            <span class="effect-radio <%= cta.color %> <%=(buttonShortcode && buttonShortcode==cta.shortcode)?'selected':''%>"" id="cta_<%= _id %>" data-value="<%= cta.shortcode %>">
+            <span class="container">
+            <span class="cta-text"><%= cta.text %></span><br>
+            <span class=""><%= cta.href %></span>
+            <span class="switch-container">
+                <span class="radio">
+                    <span></span>
+                </span>
+            </span>
+            </span>
+            </span>
+             <span class="cta-shortcode">[[<%= cta.shortcode %>]]</span> 
+         </div>
+         
+          <%
+             }
+             
+             %>
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/updateCTA.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/updateCTA.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/updateCTA.html	(révision 13838)
@@ -0,0 +1,6 @@
+<div class="panel-option-container animated cta-radio-update">
+    <article class="panel-option">
+       <div class="category-content" id="liste-cta">
+       </div>
+    </article>
+ </div>
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Utils/Common.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Utils/Common.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Utils/Common.js	(révision 13838)
@@ -0,0 +1,128 @@
+define([
+    'backbone',
+    'underscore'
+  ], function(Backbone, _) {
+    
+    var Common = {
+      
+    setCTAToButtonModel: function(buttonModel, cta) {
+        var buttonOption = cta.attributes.ButtonOption;
+        var buttonStyleOption = cta.attributes.ButtonStyleOption;
+        var advancedCSS = cta.attributes.advancedCSS;
+        var link = buttonOption.link.toJSON();
+        var buttonOptionWithoutLink = this.extractButtonOptionWithoutLink(buttonOption);
+
+        // Set button options
+        buttonModel.options.ButtonOption.set(buttonOptionWithoutLink);
+        buttonModel.options.ButtonOption.link.set(link);
+        buttonModel.options.ButtonStyleOption.set(buttonStyleOption);
+        buttonModel.options.advancedCSS.set(advancedCSS);
+        buttonModel.options.ButtonOption.shortcode = buttonOption.shortcode;
+    },
+
+    setButtonModelToCTA: function(cta, ctaShortcode, buttonModel) {
+        var buttonOptions = buttonModel.options;
+        var link = buttonOptions.ButtonOption.link;
+        var buttonOptionWithoutLink = this.extractButtonOptionWithoutLink(buttonOptions.ButtonOption.toJSON());
+
+        // Set CTA properties
+        cta.ButtonOption = buttonOptionWithoutLink;
+        cta.ButtonStyleOption = buttonOptions.ButtonStyleOption.toJSON();
+        cta.advancedCSS = buttonOptions.advancedCSS.toJSON();
+        cta.ButtonOption.shortcode = ctaShortcode;
+        cta.ButtonOption.link = link;
+    },
+
+    extractButtonOptionWithoutLink: function(buttonOption) {
+        var clonedOption = _.clone(buttonOption);
+        delete clonedOption.link;
+        return clonedOption;
+    },
+    
+    areButtonModelAndCTASame: function(buttonModel, cta) {
+      var buttonModelJson = {
+          ButtonOption: buttonModel.options.ButtonOption.toJSON(),
+          ButtonStyleOption: buttonModel.options.ButtonStyleOption.toJSON(),
+          advancedCSS: buttonModel.options.advancedCSS.toJSON()
+      };
+  
+      var ctaJson = {
+          ButtonOption: cta.ButtonOption,
+          ButtonStyleOption: cta.ButtonStyleOption,
+          advancedCSS: cta.advancedCSS
+      };
+  
+      buttonModelJson = JSON.parse(JSON.stringify(buttonModelJson));
+      ctaJson = JSON.parse(JSON.stringify(ctaJson));
+  
+      // Compare link attributes
+      var linkMatch = true;
+      for (var key in buttonModelJson.ButtonOption.link) {
+          var modelValue = String(buttonModelJson.ButtonOption.link[key] || '').trim();
+          var ctaValue = String(ctaJson.ButtonOption.link[key] || '').trim();
+          if (modelValue !== ctaValue) {
+              linkMatch = false;
+              break;
+          }
+      }
+  
+      // Compare ButtonOption attributes
+      var buttonOptionMatch = true;
+      for (var key in buttonModelJson.ButtonOption) {
+          if (key === 'link') continue;
+          var modelValue = String(buttonModelJson.ButtonOption[key] || '').trim();
+          var ctaValue = String(ctaJson.ButtonOption[key] || '').trim();
+          if (modelValue !== ctaValue) {
+              buttonOptionMatch = false;
+              break;
+          }
+      }
+  
+      // Compare ButtonStyleOption attributes
+      var styleOptionMatch = true;
+      for (var key in buttonModelJson.ButtonStyleOption) {
+          var modelValue = String(buttonModelJson.ButtonStyleOption[key] || '').trim();
+          var ctaValue = String(ctaJson.ButtonStyleOption[key] || '').trim();
+          if (modelValue !== ctaValue) {
+              styleOptionMatch = false;
+              break;
+          }
+      }
+  
+      // Compare advancedCSS attributes
+      var advancedCSSMatch = true;
+      for (var key in buttonModelJson.advancedCSS) {
+          var modelValue = String(buttonModelJson.advancedCSS[key] || '').trim();
+          var ctaValue = String(ctaJson.advancedCSS[key] || '').trim();
+          if (modelValue !== ctaValue) {
+              advancedCSSMatch = false;
+              break;
+          }
+      }
+  
+      return linkMatch && buttonOptionMatch && styleOptionMatch && advancedCSSMatch;
+  },
+  _showToast: function(containerSelector,message){
+
+    var toastOptions = {
+        text: message,
+        icon: 'icon-check-circle',
+        type: 'success',
+        appendTo: containerSelector,
+        showHideTransition: 'fade',
+        hideAfter: 5000,
+        position: 'top-right',
+        textAlign: 'left',
+        allowToastClose: false,
+        loader: false
+    };
+
+    $.toast(toastOptions);
+},
+  
+  
+  
+  } 
+    return Common;
+});
+  
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js	(révision 13837)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js	(révision 13838)
@@ -6,8 +6,12 @@
     "JEditor/Commons/Ancestors/Views/SaveCancelPanel",
     "./ButtonOptionView",
     "./ButtonStyleOptionView",
-    "JEditor/PagePanel/Contents/Options/Views/AdvancedOptionView"
-], function(BlockView, template, translate,SvgCollection, SaveCancelPanel, ButtonOptionView, ButtonStyleOptionView, AdvancedOptionView) {
+    "./ButtonCTAOptionView",
+    "JEditor/PagePanel/Contents/Options/Views/AdvancedOptionView",
+    "./../Models/CTACollection",
+    "JEditor/PagePanel/Contents/Blocks/ButtonBlock/Utils/Common",
+    "JEditor/App/Messages/Confirm"
+], function(BlockView, template, translate,SvgCollection, SaveCancelPanel, ButtonOptionView, ButtonStyleOptionView,ButtonCTAOptionView, AdvancedOptionView,CTACollection,Common,Confirm) {
     /**
      * Vue des blocs d'images
      * @class ImageBlockView
@@ -24,14 +28,54 @@
                         initialize: function() {
                             this._super();
                             this._contentTemplate = this.buildTemplate(template, translate);
+
+                            //utilisation donnée cta si bouton utilise un cta 
+                            this.listeCTA = CTACollection.getInstance();
+                            if (this.model.options.ButtonOption.shortcode && this.model.options.ButtonOption.shortcode != ""){
+                                this.usedCTA =  this.listeCTA._getByShortCode(this.model.options.ButtonOption.shortcode);
+                                if(this.usedCTA){
+                                    Common.setCTAToButtonModel(this.model, this.usedCTA);
+                                }
+                            }
+
                             this.buttonOption = this.model.options.ButtonOption;
+                            this.buttonStyleOption = this.model.options.ButtonStyleOption;
+                            if(this.usedCTA){
+                                this.listenTo(this.usedCTA , "cta:updated", this.resetAndRender); 
+                            }
                             this.listenTo(this.buttonOption, "radio:change", this.render); 
                             this.listenTo(this.buttonOption, "input:change", this.onInputChange);
+                            this.listenTo(this.buttonStyleOption, "remove:icon", this.removeIcon);
+                            
+                            // this.listenTo(this.model, 'cta:selected', this.createButtonOptionView);
+
+                            this.buttonCTAOptionView = new ButtonCTAOptionView({
+                                buttonModel: this.model
+                            });
+                            this.listenTo(this.model, 'change', this.onModelChange);
                             this.delegateEvents();         
                             this.svgContent = '';   
                             this.svgName = this.model.options.ButtonStyleOption.icon;
                             if(this.svgName) this.fetchSvg(this.svgName);       
+                            
                         },
+                        removeIcon: function(){
+                            this.$(".blk-button__icon").empty();
+                        },
+                        resetAndRender:function(){
+                            Common.setCTAToButtonModel(this.model, this.usedCTA);
+                            this.render();
+                        },
+             
+                        onModelChange: function() {
+                            // Check if ButtonCTAOptionView exists
+                            if (this.buttonCTAOptionView) {
+                                // Update the model in ButtonCTAOptionView
+                                this.buttonCTAOptionView.buttonModel = this.model;
+                                // Re-render ButtonCTAOptionView
+                                this.buttonCTAOptionView.render();
+                            }
+                        },
                         onInputChange: function(length){
                             var txtWarning = this.$('.txt-warning');
                             if (length === 0) {
@@ -54,7 +98,6 @@
                             var color = this.model.options.ButtonStyleOption.color;
                             var isDifferentText = this.model.options.ButtonOption.isDifferentText;
                             var textOnMobile = this.model.options.ButtonOption.textOnMobile;
-
                             this.$(".blk-button__label").css('margin-top','');
                             this.$(".blk-button__icon").empty();
                             this.$('.content').append(this._contentTemplate({icon: iconContent,size: sizeButton, text: textButton, buttonAlignment:alignButton, textAlignment:alignText, color :color, isDifferentText: isDifferentText, textOnMobile: textOnMobile}));
@@ -106,6 +149,7 @@
                             buttonStylesOptionView = new ButtonStyleOptionView({
                                 model: options.ButtonStyleOption
                             });
+
                             advancedView = new AdvancedOptionView({
                                 model: options.advancedCSS
                             });
@@ -118,13 +162,37 @@
                                         message: translate("errorWithoutTextOnMobile"),
                                         title: translate("error")
                                     });
-
+                                    
                                     return false
                                 }
-                                this.app.currentPanel.rightPanelView.hidePanel();
-                                this.app.currentPanel.rightPanelView.hideContent(buttonOptionsView);
-                                this.app.currentPanel.rightPanelView.removeContent(buttonOptionsView);
-                            });
+                                if (this.model.options.ButtonOption.shortcode && this.model.options.ButtonOption.shortcode != "") {
+                                    var cta  = this.listeCTA._getByShortCode(this.model.options.ButtonOption.shortcode);
+                                    var compareResult = Common.areButtonModelAndCTASame(this.model, cta);
+                                    if (!compareResult) {
+                                        this.confirm({
+                                            message: translate('confirmBtnNotCTAMessage'),
+                                            title: translate("confirmBtnNotCTA"),
+                                            type: 'delete',
+                                            onOk: _.bind(function() {
+                                                this.model.options.ButtonOption.shortcode = "";
+                                                //gerer separement icon
+                                                if (this.model.options.ButtonStyleOption.icon == "")
+                                                    this.removeIcon(); 
+                                                this.hideRightPanelView(buttonOptionsView);
+                                            }, this),
+                                            options: {dialogClass: 'delete no-close', dontAskAgain: true,
+                                                onCancel: _.bind(function() {
+                                                    return false
+                                                }, this),
+                                            }
+                                        });
+                                    }else{
+                                        this.hideRightPanelView(buttonOptionsView);   
+                                    }
+                                }else{
+                                    this.hideRightPanelView(buttonOptionsView);
+                                }
+                            }.bind(this));
                             this.listenTo(buttonOptionsView, "cancel", function() {
                                 this.app.currentPanel.rightPanelView.hidePanel();
                                 this.app.currentPanel.rightPanelView.hideContent(buttonOptionsView);
@@ -132,9 +200,12 @@
                                 
                                 options.ButtonOption.revert();
                                 options.ButtonStyleOption.revert();
+                                if (options.ButtonStyleOption.icon == "")
+                                    this.removeIcon(); 
                             });
                             buttonOptionsView.addPane(translate("ButtonOption"), contentOptions);
                             buttonOptionsView.addPane(translate("ButtonStyleOption"), buttonStylesOptionView);
+                            buttonOptionsView.addPane(translate("CTATitle"), this.buttonCTAOptionView);
                             buttonOptionsView.addPane(translate("advancedCSS"), advancedView);
                             buttonOptionsView.setTitle(translate("buttonBlockOption"));
                             
@@ -141,6 +212,14 @@
                             this.app.currentPanel.rightPanelView.addContent(buttonOptionsView);
                             this.app.currentPanel.rightPanelView.showContent(buttonOptionsView);
                             this.app.currentPanel.rightPanelView.showPanel();
+                        },
+                        confirmUnsaved: function(params) {
+                            this.app.messageDelegate.set(new Confirm(params));
+                        },
+                        hideRightPanelView: function(buttonOptionsView){
+                            this.app.currentPanel.rightPanelView.hidePanel();
+                            this.app.currentPanel.rightPanelView.hideContent(buttonOptionsView);
+                            this.app.currentPanel.rightPanelView.removeContent(buttonOptionsView);
                         }
                     });
             return ButtonBlockView;
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonCTAOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonCTAOptionView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonCTAOptionView.js	(révision 13838)
@@ -0,0 +1,126 @@
+define([
+    "jquery",
+    "underscore",
+    "JEditor/Commons/Ancestors/Views/View", // Ensure to import the base View class
+    "text!../Templates/buttonCTAOption.html",
+    "./listeCTAView",
+    "./UpdateCTAView",
+    "./../Models/CTACollection",
+    "./../Models/CTAModel",
+    "JEditor/PagePanel/Contents/Blocks/ButtonBlock/Utils/Common",
+    "JEditor/App/Messages/ClipboardModule", 
+    "i18n!../nls/i18n" // Import translation if needed
+], function($, _, View,buttonCTAOption,listeCTAView,UpdateCTAView,CTACollection,CTAModel,Common,ClipboardModule,translate) {
+    var ButtonCTAOptionView = View.extend({
+        tagName: "div", // The HTML tag for this view
+        className: "simple-view", // CSS class for styling
+        buttonModel: null,
+        events: {
+            'click .btn-add-cta': '_onAddCTA',
+            'click .btn-update-cta': '_onUpdateCTA',
+            'click .cta-button .effect-radio': '_onSelectCTA',
+            "click .cta-shortcode" : "copyToClipboard"
+        },
+        initialize: function(options) {
+            this._super(); // Call the parent class's initialize method
+            this.template = this.buildTemplate(buttonCTAOption, translate);
+            this.buttonModel = options.buttonModel;
+            this.listeCTAView = new listeCTAView({
+                'buttonModel' : this.buttonModel,
+            });
+            this.listeCTA = CTACollection.getInstance();
+            this.ctaNotEmpty = this.listeCTA.length > 0;
+           // Listen to collection changes
+            this.listenTo(this.listeCTA, 'add', function() {
+                this.ctaNotEmpty = this.listeCTA.length > 0;
+                this.listeCTAView.render();
+                this.render();
+            }.bind(this));
+            this.listenTo(this.buttonModel, 'cta:remove', function() {
+                this.listeCTAView.render();
+                this.render();
+            }.bind(this));
+
+            // this.listenTo(this.listeCTA, 'liste_cta:updated', function() {
+            //    this._showToast('updateCTA','#item-config');
+            // }.bind(this));
+        },
+        _onAddCTA: function(e) {
+
+            e.preventDefault();
+
+            var cta = new CTAModel({
+                ButtonOption : this.buttonModel.options.ButtonOption.toJSON(),
+                ButtonStyleOption : this.buttonModel.options.ButtonStyleOption.toJSON(),
+                advancedCSS : this.buttonModel.options.advancedCSS.toJSON(),
+            });
+            //ajout reference cta
+            cta.ButtonOption.shortcode = "cta_" + (parseInt(this.listeCTA.length, 10) + 1);
+            cta.save({}, {
+                success: function(response) {
+                    console.log("CTA saved successfully:", response);
+                }.bind(this),
+                error: function(xhr) {
+                    console.log("Request details:", {
+                        status: xhr.status,
+                        response: xhr.responseText,
+                        data: cta.toJSON()
+                    });
+                    console.error("Failed to save CTA:", xhr);
+                }
+            });
+            //le cta crée est actif pour le bouton en cours 
+            this.buttonModel.options.ButtonOption.shortcode = cta.ButtonOption.shortcode;
+            //ajouter la nouvelle cta à la collection
+            this.listeCTA.add(cta);
+            Common._showToast('.cta-list-content',translate("ctaSavedSuccessfully"));
+           
+        },
+        _onUpdateCTA: function(e) {
+
+            e.preventDefault();
+            var listeCTAView2 = new listeCTAView({});
+            this.updateCTADialog  = new UpdateCTAView({
+                'listeCTAViewOnglet' : this.listeCTAView,
+                'listeCTAView' : listeCTAView2,
+                'buttonModel' : this.buttonModel,
+            });
+           this.updateCTADialog.open();
+           
+        },
+
+        _onSelectCTA: function(e) {
+            
+            e.preventDefault();
+            var ctaShortcode = $(e.currentTarget).data('value');
+            //add class
+            this._addSelectClass(e);
+
+            var selectedCTA = this.listeCTA._getByShortCode(ctaShortcode);
+            Common.setCTAToButtonModel(this.buttonModel, selectedCTA);
+
+            // Alternative 1: Trigger on the model itself
+            this.buttonModel.trigger('cta:selected');
+
+        },
+
+        _addSelectClass : function (e) {
+            this.$(".effect-radio").removeClass("selected");
+            var $target = $(e.currentTarget);
+            $target.addClass("selected");
+        },
+
+        render: function() {
+            
+            this.$el.html(this.template({ctaNotEmpty : this.ctaNotEmpty}));
+            this.$('.cta-list-content').append(this.listeCTAView.render().$el);
+            return this; // Return the view instance for chaining
+        },
+        copyToClipboard : function (e){
+            ClipboardModule.copyToClipboard(e);
+        },
+
+    });
+
+    return ButtonCTAOptionView; // Return the new view class
+});
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js	(révision 13837)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js	(révision 13838)
@@ -78,6 +78,8 @@
                                 this.selectedIconEl.hide();
                                 this.selectedIconEl.empty();
                                 this.model.icon="";
+                                this.model.trigger('remove:icon');
+                                
                              }
                         },
 
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/UpdateCTAView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/UpdateCTAView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/UpdateCTAView.js	(révision 13838)
@@ -0,0 +1,159 @@
+define([
+    "jquery",
+    "underscore",
+    "text!../Templates/updateCTA.html",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/DialogView",
+    "i18n!../nls/i18n",
+    "JEditor/App/Messages/ConfirmUnsaved",
+    "./../Models/CTACollection",
+    "./../Models/CTAModel",
+    "JEditor/PagePanel/Contents/Blocks/ButtonBlock/Utils/Common",
+    //not in params
+    "jqueryPlugins/dropdown",
+], function($, _, updateCTA, Events, DialogView, translate,ConfirmUnsaved,CTACollection,CTAModel,Common ) {
+    var UpdateCTAView = DialogView.extend({
+        className: 'updateCTA',
+        events: {
+            'click .cta-radio-update .effect-radio': '_onSelectCTA',
+        },
+        
+        constructor: function(options) {
+            //this.listeCTAViewOnglet instance utilisé dans onglet cta
+            this.listeCTAViewOnglet = options.listeCTAViewOnglet;
+            this.listeCTAView = options.listeCTAView;
+            this.listeCTA = CTACollection.getInstance();
+            this.buttonModel = options.buttonModel;
+            this.ctaShortcode = null;
+
+            var opts = _.extend({
+                title: translate("chooseCTAToUpdate"),
+                width : 600,
+                height : 600,
+                buttons: [
+                    {
+                        text: translate("save"),
+                        class: 'okay disabled',
+                        click: _.bind(this.onOk, this)
+                    },
+                    {
+                        text: translate("cancel"),
+                        class: 'cancel',
+                        click: _.bind(this.onCancel, this)
+                    }
+                ],
+                // close: _.bind(this.onClose, this),
+            }, options);
+
+            return DialogView.call(this, opts);
+        },
+
+        initialize: function(options) {
+            this._super();
+            this._template = this.buildTemplate(updateCTA, translate);
+        },
+
+
+        _onSelectCTA: function(e) {
+
+            e.preventDefault();
+            var ctaShortcode = $(e.currentTarget).data('value');
+            //add class
+            this._addSelectClass(e);
+
+            var selectedCTA =  this.listeCTA._getByShortCode(ctaShortcode);
+            this.ctaShortcode = selectedCTA.ButtonOption.shortcode;
+
+            // Enable save button after CTA selection
+            this.$el.parent().find('.okay').removeClass('disabled');
+        },
+        _onUpdateOK: function() {
+            var cta = new CTAModel({
+                ButtonOption : this.buttonModel.options.ButtonOption.toJSON(),
+                ButtonStyleOption : this.buttonModel.options.ButtonStyleOption.toJSON(),
+                advancedCSS : this.buttonModel.options.advancedCSS.toJSON(),
+                id: this.ctaShortcode
+            });
+            //ajout reference cta
+            cta.ButtonOption.shortcode = this.ctaShortcode;
+            cta.save({}, {
+                wait: true,
+                success: function(response) {
+                    console.log("CTA updated successfully:", response);
+                    this.onClose();
+                }.bind(this),
+                error: function(xhr) {
+                    console.log("Request details:", {
+                        status: xhr.status,
+                        response: xhr.responseText,
+                        data: cta.toJSON()
+                    });
+                    console.error("Failed to save CTA:", xhr);
+                }
+            });
+            
+            //update cta dans collection
+            var selectedCTA =  this.listeCTA._getByShortCode(this.ctaShortcode);
+            Common.setButtonModelToCTA(selectedCTA,this.ctaShortcode, this.buttonModel);
+
+            //peut etre remplacé par un evenement
+            this.listeCTAViewOnglet.render();
+           
+            selectedCTA.trigger('cta:updated');
+            this.listeCTA.trigger('liste_cta:updated');
+        },
+
+        _addSelectClass : function (e) {
+            this.$(".effect-radio").removeClass("selected");
+            var $target = $(e.currentTarget);
+            $target.addClass("selected");
+        },
+
+        render: function() {
+            this._super();
+            this.undelegateEvents();
+            this.$el.html(this._template({}));
+            this.$('#liste-cta').append(this.listeCTAView.render().$el);
+            this.delegateEvents();
+            return this;
+        },
+
+        onOk: function() {
+            this.confirmUnsaved({
+                message: translate("confirmUpdateMessage"),
+                title: translate("confirmUpdateTitle"),
+                type: 'delete-not-saved',
+                onYes: _.bind(function() {
+                    this._onUpdateOK();
+                }, this),
+                onNo: _.bind(function() {
+                    this.onClose();
+                }, this),
+
+                options: {
+                    dialogClass: 'delete no-close',
+                    dontAskAgain: true,
+                    onCancel: _.bind(function() {
+                    }, this),
+                }
+            });
+        },
+        onCancel: function() {
+            this.onClose();
+        },
+
+        onClose: function() {
+            
+            this.$el.dialog('close');
+            // Remove the dialog
+            this.remove();
+        },
+
+        confirmUnsaved: function(params) {
+            this.app.messageDelegate.set(new ConfirmUnsaved(params));
+        },
+        
+    });
+
+    return UpdateCTAView;
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/listeCTAView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/listeCTAView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/listeCTAView.js	(révision 13838)
@@ -0,0 +1,71 @@
+define(['jquery',
+    "underscore", 
+    'JEditor/Commons/Ancestors/Views/View',
+    "text!../Templates/listeCTA.html",
+    "./../Models/CTACollection",
+    'i18n!../nls/i18n'], function (
+        $,
+        _,
+        View, 
+        template,
+        CTACollection,
+        translate) {
+        var listeCTAView = View.extend({
+
+            constructor: function(options) {
+                this.buttonModel = options.buttonModel;
+                View.apply(this, arguments);
+            },
+            initialize: function () {
+                this._super();
+                this._template = this.buildTemplate(template,translate);
+                this.listeCTA = CTACollection.getInstance();
+            },
+
+            render: function () {
+                // this.undelegateEvents();
+                var listeCTAForTemplate = this.listeCTA.length > 0 ? this.processCTAData(this.listeCTA) : [];
+                this.$el.html(this._template({liste: listeCTAForTemplate, buttonShortcode : typeof this.buttonModel != "undefined" ? this.buttonModel.options.ButtonOption.shortcode : ""}));
+                // this.delegateEvents();
+                return this;
+            },
+            processCTAData : function (data) {
+                // Check if data is a string and try to parse it
+                if (typeof data === 'string') {
+                    try {
+                        data = JSON.parse(data); // Attempt to parse the JSON string
+                    } catch (e) {
+                        console.error("Failed to parse JSON string:", e);
+                        return []; // Return an empty array or handle the error
+                    }
+                }
+                var resultArray = [];
+                
+                data.forEach(function(cta) {
+                    if (!cta || 
+                        !cta.ButtonOption || 
+                        !cta.ButtonOption.text || 
+                        !cta.ButtonOption.link || 
+                        !cta.ButtonOption.link.href || 
+                        !cta.ButtonStyleOption || 
+                        !cta.ButtonStyleOption.color) {
+                        return; // Skip this iteration
+                    }
+                
+                    var buttonInfo = {
+                        shortcode: cta.ButtonOption.shortcode,
+                        text: cta.ButtonOption.text,
+                        href: cta.ButtonOption.link.href,
+                        color: cta.ButtonStyleOption.color
+                    };
+                
+                    resultArray.push(buttonInfo);
+                });
+                
+
+                return resultArray;
+            }
+        
+        });
+        return listeCTAView;
+    });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js	(révision 13837)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js	(révision 13838)
@@ -1,10 +1,26 @@
 define({
     "BLOCK_NAME": "Bouton",
+    "callToAction": "Call to action",
+    "callToActionLegend": "Voir la partie 'CTA' (Call to action) pour appliquer un bouton prédéfinie.",
+    "CTATitle" : "CTA",
+    "createCTA": "Créer un Call to action ",
+    "createCTALegent" : "Sauvegarder la configuration du bouton comme CTA afin de pouvoir le réutiliser.",
+    "addCTA": "ajouter un CTA",
+    "updateExistingCTA": "mettre à jour un CTA existant",
+    "applyCTA": "Appliquer un Call to action",
+    "applyCTALegend" : "Choisir un CTA à appliquer au bouton",
+    "ctaSavedSuccessfully":"CTA sauvegardé avec succès",
+    "chooseCTAToUpdate": "Choisir un CTA à mettre à jour",
+    "confirmUpdateTitle":"Mise à jour du CTA",
+    "confirmUpdateMessage":"Êtes-vous sûr de vouloir mettre à jour ce CTA ?",
+    "confirmBtnNotCTA":"Confirmation",
+    "confirmBtnNotCTAMessage":"Les données du bouton diffèrent du CTA. En enregistrant, il ne l'utilisera plus. Êtes-vous sûr ?",
     "addButton": "Texte du bouton",
     "buttonOptionLegend": "Renseignez le texte de votre boutton ici.",
     "buttonTitle": "Texte du bouton",
     "selectClickActionButton": "Action au clic sur le bouton",
     "buttonActionLegend": "Sélectionnez et configurez l'action souhaitée au clic sur le bouton",
+    "save": "Sauvegarder",
     "cancel": "Annuler",
     "image": "Image",
     "buttonBlockOption": "Options du bouton",
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js	(révision 13837)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js	(révision 13838)
@@ -1,10 +1,26 @@
 define({
     "BLOCK_NAME": "Bouton",
+    "callToAction": "Call to action",
+    "callToActionLegend": "Voir la partie 'CTA' (Call to action) pour appliquer un bouton prédéfinie.",
+    "CTATitle" : "CTA",
+    "createCTA": "Créer un Call to action ",
+    "createCTALegent" : "Sauvegarder la configuration du bouton comme CTA afin de pouvoir le réutiliser.",
+    "addCTA": "ajouter un CTA",
+    "updateExistingCTA": "mettre à jour un CTA existant",
+    "chooseCTAToUpdate": "Choisir un CTA à mettre à jour",
+    "applyCTA": "Appliquer un Call to action",
+    "applyCTALegend" : "Choisir un CTA à appliquer au bouton",
+    "ctaSavedSuccessfully":"CTA sauvegardé avec succès",
+    "confirmUpdateTitle":"Mise à jour du CTA",
+    "confirmUpdateMessage":"Êtes-vous sûr de vouloir mettre à jour ce CTA ?",
+    "confirmBtnNotCTA":"Confirmation",
+    "confirmBtnNotCTAMessage":"Les données du bouton diffèrent du CTA. En enregistrant, il ne l'utilisera plus. Êtes-vous sûr ?",
     "addButton": "Texte du bouton",
     "buttonOptionLegend": "Renseignez le texte de votre boutton ici.",
     "buttonTitle": "Texte du bouton",
     "selectClickActionButton": "Action au clic sur le bouton",
     "buttonActionLegend": "Sélectionnez et configurez l'action souhaitée au clic sur le bouton",
+    "save": "Sauvegarder",
     "cancel": "Annuler",
     "image": "Image",
     "buttonBlockOption": "Options du bouton",
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js	(révision 13837)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js	(révision 13838)
@@ -1,6 +1,22 @@
 define({
     "root": {
         "BLOCK_NAME": "Button",
+        "callToAction": "Call to action",
+        "callToActionLegend": "See the “CTA” (Call to action) section to apply a predefined button.",
+        "CTATitle" : "CTA",
+        "createCTA": "Create a Call to action",
+        "createCTALegent" : "Save the button's configuration as a CTA so that you can use it again.",
+        "addCTA": "Add a CTA",
+        "chooseCTAToUpdate": "Choose a CTA to update",
+        "updateExistingCTA": "update existing CTA",
+        "applyCTA": "Apply a Call to action",
+        "applyCTALegend" : "Choose a CTA to apply to the button",
+        "ctaSavedSuccessfully":"CTA saved successfully",
+        "ctaUpdatedSuccessfully":"CTA updated successfully, please refresh the page to see the changes.",
+        "confirmUpdateTitle":"Confirm update",
+        "confirmUpdateMessage":"Are you sure you want to update this CTA ?",
+        "confirmBtnNotCTA":"Confirm",
+        "confirmBtnNotCTAMessage":"The button's data differs from the CTA. If you save, it will no longer use it. Are you sure ?",
         "addButton": "Button text",
         "buttonOptionLegend": "Enter the text of your button here.",
         "buttonTitle": "Title of the button",
@@ -7,6 +23,7 @@
         "selectClickActionButton": "Action on button click",
         "buttonActionLegend": "Select and configure the desired action on button click",
         "cancel": "Cancel",
+        "save": "Save",
         "image": "Image",
         "buttonBlockOption": "Button options",
         "iconButton": "Icon",
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js	(révision 13837)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js	(révision 13838)
@@ -213,7 +213,7 @@
             //pour styles, format et fontsize du toolbar ckeditor
             $(document).on('click', '.cke_combo', _.bind(function(event) {
                 var $comboPanel = $('.cke_combopanel');
-                
+                $('body').css('overflow', 'hidden');
                 if ($comboPanel.length) {
                     $comboPanel.css({
                         'z-index': '1000000004', 
Index: src/js/JEditor/PagePanel/PagePanel.js
===================================================================
--- src/js/JEditor/PagePanel/PagePanel.js	(révision 13837)
+++ src/js/JEditor/PagePanel/PagePanel.js	(révision 13838)
@@ -18,10 +18,12 @@
         "JEditor/PagePanel/Contents/Zones/Versions/Views/VersionsCollectionView",
 		"JEditor/PagePanel/Views/PageLpManagerView",
 		"JEditor/PagePanel/Views/FilterPageView",
+		"JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/CTACollection",
+		"JEditor/PagePanel/Contents/Blocks/ButtonBlock/Utils/Common",
 		"i18n!./nls/i18n",
 		// not in params
 		"owlCarousel",
-		"jqueryPlugins/affix" ], function($, _, pagePanel, Events, PanelView, RightPanelView,  PageCollectionView, PageCollection, PageSupportCollection, PageListManagerView, AvailableView, AddPageView, LayoutCollection, PageView, LanguagesDropDown, ContentModelCollection, VersionsCollectionView, PageLpManagerView, FilterPageView, translate) {
+		"jqueryPlugins/affix" ], function($, _, pagePanel, Events, PanelView, RightPanelView,  PageCollectionView, PageCollection, PageSupportCollection, PageListManagerView, AvailableView, AddPageView, LayoutCollection, PageView, LanguagesDropDown, ContentModelCollection, VersionsCollectionView, PageLpManagerView, FilterPageView,CTACollection,Common, translate) {
 	 /**
 		 * not main Vue du Panneau de page, gère les vues de page (nom, etc),
 		 * langues, panneaux latéraux
@@ -69,6 +71,7 @@
 			this.rightPanelView = new RightPanelView();
 			this._byLang = {};
 			this._template = this.buildTemplate(pagePanel, translate);
+
 		},
                 /**
                  * Affiche le panel de droite en inserant les versions de la zone courante
@@ -167,7 +170,14 @@
 			});
 			this.childViews.filterPage = new FilterPageView();
 
-			this.loadingEnd();
+			this.ctaCollection = CTACollection.getInstance();
+            this.ctaCollection.fetch().done(function(response) {
+				this.loadingEnd();
+            }.bind(this));
+			this.listenTo(this.ctaCollection, 'liste_cta:updated', function() {
+				Common._showToast('.panel-container .cta-list-content',translate("ctaUpdatedSuccessfully"));
+			});
+			
 		},
 		/*
 		 * recherche d'une ou plusieurs pages
Index: src/js/JEditor/PagePanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/nls/fr-ca/i18n.js	(révision 13837)
+++ src/js/JEditor/PagePanel/nls/fr-ca/i18n.js	(révision 13838)
@@ -130,6 +130,7 @@
     "landingPage" : "Landing page",
     "urlText" : "Ouvrir la page 🡥",
     "filter": "Filtre ...",
+    "ctaUpdatedSuccessfully":"Le CTA a été modifié avec succès",
     "none_result": "Aucune page correspond à la recherche",
     "title" : "Titre",
     "allPages": "Toutes les pages",
Index: src/js/JEditor/PagePanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/nls/fr-fr/i18n.js	(révision 13837)
+++ src/js/JEditor/PagePanel/nls/fr-fr/i18n.js	(révision 13838)
@@ -136,6 +136,7 @@
     "landingPage" : "Landing page",
     "urlText" : "Ouvrir la page 🡥",
     "filter": "Filtre ...",
+    "ctaUpdatedSuccessfully":"Le CTA a été modifié avec succès",
     "none_result": "Aucune page correspond à la recherche",
     "title" : "Titre",
     "allPages": "Toutes les pages",
Index: src/js/JEditor/PagePanel/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/nls/i18n.js	(révision 13837)
+++ src/js/JEditor/PagePanel/nls/i18n.js	(révision 13838)
@@ -134,6 +134,7 @@
         "landingPage" : "Landing page",
         "urlText" : "Open the webpage 🡥",
         "filter": "Filter ...",
+        "ctaUpdatedSuccessfully":"CTA updated successfully",
         "none_result": "No pages match the search",
         "title" : "Title",
         "allPages": "All pages",
Index: src/less/imports/button_block/styleOptions.less
===================================================================
--- src/less/imports/button_block/styleOptions.less	(révision 13837)
+++ src/less/imports/button_block/styleOptions.less	(révision 13838)
@@ -212,4 +212,97 @@
  //rendu button backoffice
   .force-horizontal-align {
     margin-top: -15px;
-  }
\ No newline at end of file
+  }
+
+
+  //update cta 
+  .cta-radio-update {
+    padding-top: 0;
+  }
+  
+  .color-radio.cta-radio-update {
+    margin-bottom: 10px;
+  }
+  
+  /* Selected State */
+  .color-radio.cta-radio-update .selected {
+    box-shadow: 
+        0px 0px 0px 2px #0b2428,   /* First dark greenish shadow */
+        0px 0px 0px 5px #ffffff,   /* Second white shadow */
+        0px 0px 0px 6px #0b2428;   /* Third dark greenish shadow */
+    border-radius: 4px;
+  }
+  
+  /* Effect Radio */
+  .cta-radio-update .effect-radio {
+    display: block;
+    margin-bottom: 1px;
+    text-align: left;
+  }
+  
+  .cta-radio-update .effect-radio .container .cta-text {
+    font-weight: bold;
+  }
+  
+  /* Dialog Styles */
+  .ui-dialog.updateCTA {
+    position: fixed !important;
+  }
+  
+  .ui-dialog-content .cta-radio-update #liste-cta {
+    margin: 10px;
+  }
+  
+  .ui-dialog.updateCTA .ui-dialog-content .cta-shorcode {
+    text-align: left;
+  }
+  
+  /* Disabled State */
+  .ui-dialog.updateCTA .ui-dialog-buttonset .disabled {
+    cursor: not-allowed;
+    opacity: 0.5;
+    pointer-events: none;
+  }
+  
+
+  //liste cta 
+  .color-radio.cta-radio-container{
+    margin-bottom: 15px;
+}
+.color-radio.cta-radio-container .selected {
+    box-shadow: 
+        0px 0px 0px 2px #0b2428,   /* First dark greenish shadow */
+        0px 0px 0px 5px #ffffff,   /* Second white shadow */
+        0px 0px 0px 6px #0b2428;   /* Third dark greenish shadow */
+    border-radius: 4px;
+}
+
+.cta-radio-container .effect-radio .container{
+    display: block;
+    text-align: left;
+    padding-left: 5px;
+    margin-bottom: 5px;
+}
+
+.cta-radio-container .effect-radio {
+    margin-bottom: 1px;
+}
+
+.cta-radio-container .effect-radio .container .cta-text{
+    font-weight: bold;
+}
+
+.cta-radio-container .cta-shortcode{
+    margin-left: 10px; 
+    text-align: left;
+    display: block;
+}
+
+
+//onglet cta 
+.cta-list-content{
+    margin : 5px;
+}
+.btn-add-cta{
+    margin-bottom: 10px;
+}
