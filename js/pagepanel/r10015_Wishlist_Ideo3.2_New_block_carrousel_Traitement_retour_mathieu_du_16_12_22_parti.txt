Revision: r10015
Date: 2022-12-19 17:19:38 +0300 (lts 19 Des 2022) 
Author: norajaonarivelo 

## Commit message
Wishlist Ideo3.2 New block carrousel Traitement retour mathieu du 16/12/22 partie integration

## Files changed

## Full metadata
------------------------------------------------------------------------
r10015 | norajaonarivelo | 2022-12-19 17:19:38 +0300 (lts 19 Des 2022) | 1 ligne
Chemins modifiés :
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/CarrouselOptionTypeLien.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/OptionTypeLienView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/main.js
   M /branches/ideo3_v2/integration/src/less/imports/panel.less

Wishlist Ideo3.2 New block carrousel Traitement retour mathieu du 16/12/22 partie integration
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/CarrouselOptionTypeLien.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/CarrouselOptionTypeLien.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/CarrouselOptionTypeLien.html	(révision 10015)
@@ -0,0 +1,31 @@
+<p class="panel-legend"><%=__("selectTypeLink")%></p>
+<% var _id= "link_type1" %>
+<input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkType" value="1"  <%=(TypeLien==1)?"checked":""%> />
+<label  class="block-label" for="<%= _id %>" >
+    <div class="radio-wrapper">
+    <span class="icon  icon-radio-active"></span>
+    <span class="icon  icon-radio-inactive"></span>
+    </div>
+    <div class="block-label-radio"><%= __("LinkImage")%></div>
+
+</label>
+<% var _id= "link_type2" %>
+<input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkType" value="2"  <%=(TypeLien==2)?"checked":""%>/>
+<label  class="block-label <%=(LinkText)?'carrouselTypeLinkDisable':''%>" for="<%= _id %>" >
+    <div class="radio-wrapper">
+    <span class="icon  icon-radio-active"></span>
+    <span class="icon  icon-radio-inactive"></span>
+    </div>
+    <div class="block-label-radio"><%= __("LinkText")%></div>
+
+</label>
+<% var _id= "link_type3" %>
+<input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkType" value="3"  <%=(TypeLien==3)?"checked":""%> />
+<label  class="block-label <%=(BouttonMoreInfo)?'carrouselTypeLinkDisable':''%>" for="<%= _id %>" >
+    <div class="radio-wrapper">
+    <span class="icon  icon-radio-active"></span>
+    <span class="icon  icon-radio-inactive"></span>
+    </div>
+    <div class="block-label-radio"><%= __("ButtonReadMore")%></div>
+
+</label>
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselOption.html	(révision 10014)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselOption.html	(révision 10015)
@@ -86,7 +86,7 @@
 
             </label>
             <% var _id=_.uniqueId('link_action') %>
-            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkAction" value="0" <%=(Action==0)?"checked":""%>/>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkAction" value="0"  <%=(Action==0)?"checked":""%>/>
             <label  class="block-label" for="<%= _id %>">
               <div class="radio-wrapper">
                 <span class="icon  icon-radio-active"></span>
@@ -96,38 +96,8 @@
 
             </label>
         </div>
-        <div class="link-img">
-            <p class="panel-legend"><%=__("selectTypeLink")%></p>
-            <% var _id=_.uniqueId('link_type') %>
-            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkType" value="1"  <%=(TypeLien==1)?"checked":""%> />
-            <label  class="block-label" for="<%= _id %>">
-              <div class="radio-wrapper">
-                <span class="icon  icon-radio-active"></span>
-                <span class="icon  icon-radio-inactive"></span>
-              </div>
-              <div class="block-label-radio"><%= __("LinkImage")%></div>
-
-            </label>
-            <% var _id=_.uniqueId('link_type') %>
-            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkType" value="2"  <%=(TypeLien==2)?"checked":""%>/>
-            <label  class="block-label" for="<%= _id %>">
-              <div class="radio-wrapper">
-                <span class="icon  icon-radio-active"></span>
-                <span class="icon  icon-radio-inactive"></span>
-              </div>
-              <div class="block-label-radio"><%= __("LinkText")%></div>
-
-            </label>
-            <% var _id=_.uniqueId('link_type') %>
-            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkType" value="3"  <%=(TypeLien==3)?"checked":""%> />
-            <label  class="block-label" for="<%= _id %>">
-              <div class="radio-wrapper">
-                <span class="icon  icon-radio-active"></span>
-                <span class="icon  icon-radio-inactive"></span>
-              </div>
-              <div class="block-label-radio"><%= __("ButtonReadMore")%></div>
-
-            </label>
+        <div class="link-img" id="typeDeLien">
+            
         </div>
     </article>
 </div>
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js	(révision 10014)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js	(révision 10015)
@@ -7,12 +7,13 @@
         "JEditor/Commons/Files/Models/FileGroup",
         "JEditor/PagePanel/Contents/Options/Views/AbstractOptionView",
         "JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselFileEditionView",
+        "JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/OptionTypeLienView",
         "JEditor/App/Views/RightPanelView",
         "JEditor/PagePanel/Contents/Options/Views/OptionCollectionView",
         "JEditor/PagePanel/Contents/Options/Models/OptionsCollection",
         "JEditor/Commons/Utils"
     ],
-    function($, _, carrouselOption, Events, FileGroup, AbstractOptionView,CarrouselFileEditionView, Utils) {
+    function($, _, carrouselOption, Events, FileGroup, AbstractOptionView,CarrouselFileEditionView,OptionTypeLienView, Utils) {
         /**
          * Options de la galerie
          * @class CarrouselOptionView
@@ -30,6 +31,8 @@
                 this._super();
                 this._template = this.buildTemplate(carrouselOption, this.translate);
                 this.listenTo(this.model, 'change:fileGroup', this.onFileGroupChange);
+                this.CarrouselOptionTypeLien =new OptionTypeLienView(this.model);
+                this.listenTo(this.CarrouselOptionTypeLien,'ChangeCarrouselTypeLien',this.ChangeCarrouselTypeLien);
             },
             openFileGroupDialog: function() {
                 var fileGroup;
@@ -71,9 +74,12 @@
                 this.undelegateEvents();
                 if (this.dom[this.cid].dialogTrigger)
                     this._fileGroupDialog.detach(this.dom[this.cid].dialogTrigger[0]);
+              
                 this.$el.empty();
                 this.$el.html(this._template(this.model));
-               
+                this.$("#typeDeLien").append(this.CarrouselOptionTypeLien.el);
+                this.CarrouselOptionTypeLien.render();
+
                 this.scrollables({
                     advanced:{ autoScrollOnFocus: false }
                 });
@@ -87,12 +93,66 @@
                 var name =event.currentTarget.name;
                 if (name ==="figcaption"){
                     this.model.Info= event.currentTarget.value;
+                    this.CarrouselOptionTypeLien.render();
                 }else if(name === "LinkAction"){
                     this.model.Action= event.currentTarget.value;
+                    this.CarrouselOptionTypeLien.render();
                 }else if(name === "LinkType"){
                     this.model.TypeLien= event.currentTarget.value;
                 }
             },
+            ChangeCarrouselTypeLien :function(TypeLien){
+                console.log(TypeLien);
+                this.model.TypeLien=TypeLien;
+            }
+            // //Onclick sur ne rien afficher de l'information des images  
+            // HideLinkText :function(ValueInfo,Rerender){
+            //     var noThing= this.$("input[name=LinkType][value=2]");
+            //     var label=this.$("label[for="+noThing.attr("id")+"]");
+            //     if(ValueInfo ==0){
+            //         if(Rerender){
+            //             this.model.TypeLien=1;
+            //             this.CarrouselOptionTypeLien.render();
+            //             noThing= this.$("input[name=LinkType][value=2]");
+            //             label=this.$("label[for="+noThing.attr("id")+"]");
+            //         }
+            //         label.css({
+            //             "pointer-events":"none",
+            //             "opacity"  :".5"
+            //         });
+            //         return true;
+            //     }else{
+            //         label.css({
+            //             "pointer-events":"",
+            //             "opacity"  :""
+            //         });
+            //         return false;
+            //     }
+            // },
+            // //OnClick sur Zoom sur Image
+            // HideBouttonMoreInfo :function(ValueAction,Rerender){
+            //     var noThing= this.$("input[name=LinkType][value=3]");
+            //     var label=this.$("label[for="+noThing.attr("id")+"]");
+            //     if(ValueAction ==1){
+            //         if(Rerender){
+            //             this.model.TypeLien=1;
+            //             this.CarrouselOptionTypeLien.render();
+            //             noThing= this.$("input[name=LinkType][value=3]");
+            //             label=this.$("label[for="+noThing.attr("id")+"]");
+            //         }
+            //         label.css({
+            //             "pointer-events":"none",
+            //             "opacity"  :".5"
+            //         });
+            //         return true;
+            //     }else{
+            //         label.css({
+            //             "pointer-events":"",
+            //             "opacity"  :""
+            //         });
+            //         return false;
+            //     }
+            // }
         });
         return CarrouselOptionView;
     });
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/main.js	(révision 10014)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/main.js	(révision 10015)
@@ -6,7 +6,8 @@
         "./ImageGroupView",
         "./EditImageInfo",
         "./FileGroupListView",
-        "./CarrouselFileEditionView"
+        "./CarrouselFileEditionView",
+        "./OptionTypeLienView"
     ],
     function(  
         CarrouselBlockView,
@@ -15,7 +16,8 @@
         ImageGroupView,
         EditImageInfo,
         FileGroupListView,
-        CarrouselFileEditionView
+        CarrouselFileEditionView,
+        OptionTypeLienView
     ){
         var comp={
         "CarrouselBlockView"        :   CarrouselBlockView,
@@ -24,7 +26,8 @@
         "ImageGroupView"            :   ImageGroupView,
         "EditImageInfo"             :   EditImageInfo,
         "FileGroupListView"         :   FileGroupListView,
-        "CarrouselFileEditionView"  :   CarrouselFileEditionView
+        "CarrouselFileEditionView"  :   CarrouselFileEditionView,
+        "OptionTypeLienView"        :   OptionTypeLienView
         };
         return comp;
 });
\ No newline at end of file
Index: src/less/imports/panel.less
===================================================================
--- src/less/imports/panel.less	(révision 10014)
+++ src/less/imports/panel.less	(révision 10015)
@@ -665,6 +665,10 @@
         }
     }
 }
+.carrouselTypeLinkDisable{
+    pointer-events:none;
+    opacity :.5;
+}
 .carrousel-option-home{
     .btn.select-design{
         background-color:#444444;
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/OptionTypeLienView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/OptionTypeLienView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/OptionTypeLienView.js	(révision 10015)
@@ -0,0 +1,56 @@
+define([
+    "jquery",
+    "text!../Templates/CarrouselOptionTypeLien.html",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/View",
+    "i18n!../nls/i18n",
+    
+], function($, CarrouselOptionTypeLien, Events, View, translate) {
+    var OptionTypeLienView = View.extend({
+        events: {
+        },
+        initialize: function() {
+            this._super();
+            this.BouttonMoreInfo=false;
+            this.LinkText=false;
+            this._template = this.buildTemplate(CarrouselOptionTypeLien, translate);
+            if (!this.options.TypeLien)
+                this.options.TypeLien = null;
+
+           
+        },
+        render: function() {
+            this.BouttonMoreInfo=false;
+            this.LinkText=false;
+            var TypeLien,Info,Action;
+            TypeLien = this.options.TypeLien ? this.options.TypeLien : null;
+            Action = this.options.Action ? this.options.Action : null;
+            Info = this.options.Info ? this.options.Info : null;
+            if(Info ==0){
+                this.LinkText=true;
+                if(TypeLien ==2){
+                    TypeLien =1;
+                    this.trigger("ChangeCarrouselTypeLien",TypeLien);
+                }
+            }
+            if(Action == 1){
+                this.BouttonMoreInfo =true;
+                if((TypeLien ==3) ||(TypeLien== 2 && Info==0)){
+                    TypeLien =1;
+                    this.trigger("ChangeCarrouselTypeLien",TypeLien);
+                }
+            }
+                
+            this.undelegateEvents();
+            this.$el.empty();
+            this.$el.html(this._template({TypeLien: TypeLien,LinkText:this.LinkText,BouttonMoreInfo:this.BouttonMoreInfo}));
+
+            this.delegateEvents();
+            return this;
+        },
+    });
+
+    Events.extend({
+    });
+    return OptionTypeLienView;
+});
\ No newline at end of file
