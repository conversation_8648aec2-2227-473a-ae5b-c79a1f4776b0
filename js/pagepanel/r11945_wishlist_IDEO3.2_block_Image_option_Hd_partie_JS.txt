Revision: r11945
Date: 2024-02-20 14:23:03 +0300 (tlt 20 Feb 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2 : block Image option Hd (partie JS)

## Files changed

## Full metadata
------------------------------------------------------------------------
r11945 | srazanandralisoa | 2024-02-20 14:23:03 +0300 (tlt 20 Feb 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Models/ImageOptions.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Templates/imageOptions.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageOptionsView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/i18n.js

wishlist IDEO3.2 : block Image option Hd (partie JS)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Models/ImageOptions.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Models/ImageOptions.js	(révision 11944)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Models/ImageOptions.js	(révision 11945)
@@ -24,7 +24,8 @@
                 optionType: 'image',
                 priority: 70,
                 contextInfos: null,
-                figcaption : 0
+                figcaption : 0,
+                hd : false
             },
             initialize: function() {
                 this._super();
@@ -71,6 +72,6 @@
                 return json;
             }
         });
-    ImageOptions.SetAttributes(['file', 'link', 'contextInfos', 'figcaption']);
+    ImageOptions.SetAttributes(['file', 'link', 'contextInfos', 'figcaption', 'hd']);
     return ImageOptions;
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Templates/imageOptions.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Templates/imageOptions.html	(révision 11944)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Templates/imageOptions.html	(révision 11945)
@@ -8,6 +8,22 @@
         <div class="cropbtn">
             <a><span class="icon"></span><%=__("editImage")%></a>
         </div>
+        <div class="hd-img">
+          <h5><%=__("useHd")%></h5>
+          <p class="panel-content-legend">
+            <span class="icon-warning" style="color: #d42525;"></span> 
+            <span style="color: #d42525;"><%= __("imageWarning")%></span> 
+            <%= __("hdWarningMsg")%>.
+          </p>
+          <input type="checkbox" class="blue-bg hd " name="hd" id="hd_<%=_id %>" <%=hd? ' checked="checked" ':'' %> >
+          <label for="hd_<%=_id %>">
+              <span class="checkbox-wrapper">
+                  <span class="icon-unchecked"></span>
+                  <span class="icon-checked"></span>
+              </span>
+              <span class="text"><%= __("hdTitle")%></span>
+          </label>
+        </div>
         <div class="infos-img">
             <input value="<%= contextInfos.title %>" data-name="title" placeholder="<%= titlePlaceholder %>" <%= disable %>/>
             <input value="<%= contextInfos.desc %>" data-name="desc" placeholder="<%= descPlaceholder %>"  <%= disable %>/>
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageOptionsView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageOptionsView.js	(révision 11944)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageOptionsView.js	(révision 11945)
@@ -31,7 +31,8 @@
 			'uploadercomplete .uploader' : '_onUploaded',
 			'change .infos-img>input' : 'updateImageInfos',
 			'click .cropbtn' : 'editImage',
-			'change .figcaption-img>input': '_onRadioChange'
+			'change .figcaption-img>input': '_onChangeFigcaption',
+			'change input[type="checkbox"].hd' : '_onChangeHd'
 		},
 		updateImageInfos : function(event) {
 			var $target = $(event.currentTarget);
@@ -141,7 +142,8 @@
 				titlePlaceholder : titlePlaceholder,
 				descPlaceholder : descPlaceholder,
 				disable : disable,
-				figcaption : this.model.figcaption
+				figcaption : this.model.figcaption,
+				hd : this.model.hd
 			};
 			_.extend(templateVars, {
 				currentPage : false
@@ -160,10 +162,15 @@
 			});
 			return this;
 		},
-		_onRadioChange: function (event ) {
+		_onChangeFigcaption: function (event ) {
 			var value = $(event.currentTarget).data('value');
 			this.model.figcaption = value;
 		},
+		_onChangeHd: function (event ) {
+			var input = event.target;
+			var value = input.checked;
+			this.model.hd = value;
+		},
 		/**
 		 * Déclenché lorsqu'on enregistre l'image
 		 *
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-ca/i18n.js	(révision 11944)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-ca/i18n.js	(révision 11945)
@@ -20,4 +20,7 @@
   "styleAmb" : "Style de l'ambiance",
   "styleNone": "Aucun style",
   "imageStyles" :"Style",
+  "useHd" : "Utiliser un meilleure définition d’image (HD) :",
+  "hdWarningMsg" : "activer cette option chargera une image de meilleure qualité dans la page. Cela pourra avoir un impact négatif sur la vitesse de chargement",
+  "hdTitle" : "Activer la haute définition",
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-fr/i18n.js	(révision 11944)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-fr/i18n.js	(révision 11945)
@@ -20,4 +20,7 @@
   "styleAmb" : "Style de l'ambiance",
   "styleNone": "Aucun style",
   "imageStyles" :"Style",
+  "useHd" : "Utiliser un meilleure définition d’image (HD) :",
+  "hdWarningMsg" : "activer cette option chargera une image de meilleure qualité dans la page. Cela pourra avoir un impact négatif sur la vitesse de chargement",
+  "hdTitle" : "Activer la haute définition",
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/i18n.js	(révision 11944)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/i18n.js	(révision 11945)
@@ -21,6 +21,9 @@
       "styleAmb" : "Style of the templates",
       "styleNone": "No styles",
       "imageStyles" :"Style",
+      "useHd" : "Using a higher-definition (HD) image:",
+      "hdWarningMsg" : "enabling this option will load a higher-quality image onto the page. This may have a negative impact on loading speed",
+      "hdTitle" : "Enable high definition",
    },
    "fr-fr":true,
    "fr-ca":true
