Revision: r11082
Date: 2023-06-22 14:31:27 +0300 (lkm 22 Jon 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: Block Compare(avant/après) partie BO

## Files changed

## Full metadata
------------------------------------------------------------------------
r11082 | srazanandralisoa | 2023-06-22 14:31:27 +0300 (lkm 22 Jon 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/CompareBlock.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Models
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Models/CompareImage.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Models/CompareOption.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Models/main.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Templates
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Templates/compareImage.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Templates/compareOption.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Views
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Views/CompareBlockView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Views/CompareImageView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Views/CompareOptionView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Views/main.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/main.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/nls
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/nls/fr-ca
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/nls/fr-fr
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/nls/fr-fr/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json
   M /branches/ideo3_v2/integration/src/js/build.js
   M /branches/ideo3_v2/integration/src/js/main.js
   M /branches/ideo3_v2/integration/src/less/imports/page_panel/main.less
   A /branches/ideo3_v2/integration/src/less/imports/page_panel/module/block-options/compare-option.less
   A /branches/ideo3_v2/integration/src/less/imports/page_panel/module/block-render/compare.less

wishlist IDEO3.2: Block Compare(avant/après) partie BO
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js	(révision 11081)
+++ src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js	(révision 11082)
@@ -42,7 +42,7 @@
                                 events: {'click .block.available': 'addToCurrentContainer', 'dragstart .bloc.available': '_ondragStart'},
                                 blockOrder: {
                                     'standard': ["TextBlock", "ImageBlock", "ButtonBlock", "SeparatorBlock", "TableBlock"],
-                                    'advanced': ["CarrouselBlock", "GridBlock","GalerieBlock", "FormBlock", /*"VideoLinkeoBlock",*/ "MapBlock", "VideoBlock", "HtmlBlock"],
+                                    'advanced': ["CarrouselBlock", "GridBlock","GalerieBlock","CompareBlock", "FormBlock", /*"VideoLinkeoBlock",*/ "MapBlock", "VideoBlock", "HtmlBlock"],
                                     'others': ["LegalsBlock","SiteMapBlock","SocialNetworkBlock"],
                                     'ignored':['TwitterTimelineBlock']
                                 },
Index: src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js	(révision 11081)
+++ src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js	(révision 11082)
@@ -17,7 +17,8 @@
   "./LegalsBlock",
   "./EvaluationBlock",
   "./CarrouselBlock",
-  "./GalerieBlock"
+  "./GalerieBlock",
+  "./CompareBlock"
 ], function (
   ImageBlock,
   HtmlBlock,
@@ -37,7 +38,8 @@
   LegalsBlock,
   EvaluationBlock,
   CarrouselBlock,
-  GalerieBlock
+  GalerieBlock,
+  CompareBlock
 ) {
   var component = {
     "ImageBlock": ImageBlock,
@@ -59,6 +61,7 @@
     "EvaluationBlock": EvaluationBlock,
     "CarrouselBlock"  : CarrouselBlock,
     "GalerieBlock" : GalerieBlock,
+    "CompareBlock": CompareBlock
   };
   return component;
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/CompareBlock.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/CompareBlock.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/CompareBlock.js	(révision 11082)
@@ -0,0 +1,27 @@
+define( [
+    "JEditor/PagePanel/Contents/Blocks/Block/Block",
+    "JEditor/PagePanel/Contents/Blocks/CompareBlock/Models/CompareOption"
+], function(Block,CompareOption) {
+    /**
+     * Bloc de galerie
+     * @class CompareBlock
+     * @extends Block
+     */
+    var CompareBlock = Block.extend(
+            /**
+             * @lends CompareBlock
+             */
+                    {
+                        defaults: {type: 'compare', options: [], contentType: 'compareBlock'},
+                        initialize: function() {
+                            this._super();
+                            if (!this.options.compare)
+                                this.options.add(new CompareOption());
+                        },
+            
+                    }
+            );
+
+            CompareBlock.ICON = 'icon-button-alignment';
+            return CompareBlock;
+        });
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/CompareBlock.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Models/CompareImage.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Models/CompareImage.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Models/CompareImage.js	(révision 11082)
@@ -0,0 +1,30 @@
+/**
+ * <AUTHOR>
+ * nouveau model pour comparer l'image avant et après
+ * @property {integer} id id de l'image
+ * @property {string} name nom de l'image 
+ * @property {string} fileUrl url de l'image pour l'affichage en vue
+ */
+define(["JEditor/Commons/Ancestors/Models/Model"], function(Model) {
+    var CompareImage = Model.extend({
+        defaults: function() {
+            var ret = {
+                id:null,
+                name: "",
+                fileUrl:""
+            };
+            return ret;
+        },
+        constructor: function(attrs, options) 
+        {
+            Model.call(this, attrs, options);
+        },
+        remove:function () 
+        {
+            this.files.removeField(null,null,this);
+            return this;
+        },
+    }).setAttributes(["name","file"]);
+
+    return CompareImage;
+});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Models/CompareImage.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Models/CompareOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Models/CompareOption.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Models/CompareOption.js	(révision 11082)
@@ -0,0 +1,85 @@
+define([
+    "JEditor/Commons/Events",
+    "JEditor/PagePanel/Contents/Options/Models/AbstractOption",
+    "./CompareImage"
+], function(Events,
+        AbstractOption,
+        CompareImage
+        ) {
+    /**
+     * @class CompareOption
+     * @extends AbstractOption
+     */
+    var CompareOption = AbstractOption.extend(
+            /**
+             * @lends CompareOptions.prototype
+             */
+                    {
+                        defaults: {
+                            priority: 70, 
+                            optionType: 'compare', 
+                            title: 'compare', 
+                            files: [], 
+                            figcaption : 0,
+                            alignment:'slider-middle'
+                        },
+                        initialize: function() {
+                            this._super();
+                            that = this;
+                            this.files = (this.files ? this.files : [] ).map(function(element,i) {
+                                     var image = new CompareImage(element);
+                                    image.files = that;
+                                    image.index = i ;
+                                    that.files[i] = image;
+                                    return image;
+                            });
+                        },
+                        /**
+                         * structuration d'un model file pour etre un model CompareImage
+                         * @param {FileGroup} image  
+                         * @returns ret
+                         */
+                        ToImage: function (image)
+                        {
+                            var ret = {
+                                id:image.id,
+                                name: image.originalName,
+                                fileUrl :image.fileUrl
+                            };
+                            return ret;
+                        },
+                        /**
+                        * creation d'un model CompareImage et on l'ajoute dans notre files
+                        * @param {*} image 
+                        */
+                        addImage: function(image) 
+                        {
+                                var image = new CompareImage(this.ToImage(image));
+                                image.files = this;
+                                image.index = this.files.length ;
+                                this.files.push(image);
+                    
+                                this.trigger("add:imagecompare", this);
+                        },
+                        /**
+                        * supression d'un model CompareImage dans les liste de files
+                        * @param {*} image 
+                        * @returns 
+                        */
+                        removeImage: function(image)
+                        {
+                            var index;
+                            if (image instanceof CompareImage)
+                                index = this.files.lastIndexOf(image);
+                            else
+                                index = image;
+                            files = this.files.splice(index, 1)[0];
+                            this.trigger("remove:imagecompare", this);
+                            return this;
+                        }
+                    }
+            );
+            CompareOption.SetAttributes(['files', 'figcaption', 'alignment']);
+
+            return CompareOption;
+        });

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Models/CompareOption.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Models/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Models/main.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Models/main.js	(révision 11082)
@@ -0,0 +1,8 @@
+// this file has been auto-generated by a grunt task, it will be overriden, do not modify it
+define(["./CompareOption","./CompareImage"],function(CompareOption, CompareImage){
+    var comp = {
+        "CompareOption": CompareOption,
+        "CompareImage" : CompareImage
+    };
+    return comp;
+});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Models/main.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Templates/compareImage.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Templates/compareImage.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Templates/compareImage.html	(révision 11082)
@@ -0,0 +1,7 @@
+
+    <div class = "show-part " id="<%= image.id %>" >
+        <span class="icon-grip"></span>
+        <span class="field-name"> <%= image.name %>  </span> 
+        <span class="delete">&nbsp;x</span>
+    </div> 
+   </li> 
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Templates/compareImage.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Templates/compareOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Templates/compareOption.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Templates/compareOption.html	(révision 11082)
@@ -0,0 +1,118 @@
+<div class="panel-option-container animated ">
+    <article class="panel-option ">
+        <header>
+            <h3 class="option-name"><span class="icon-image"></span><%= __("compareContent")%></h3>
+            <p class="panel-content-legend"></span> <%= __("compareLegend")%>.</p>
+        </header>
+        <div class="option-content images-content">
+            <div class="wrapper  black-dropdown">
+                <div class="add-image"  data-index="0">
+                    <a class="btn" href="#">
+                        <span class="text">
+                            <span class="icon-add"></span>
+                            <%=__("addImage")%>
+                        </span>
+                    </a>
+                </div>
+                <div id="file-select"></div>
+                <div class="wrapper row-wrapper column-container">
+                    <div class="wrapper col-wrapper">
+                        <ul class="image-column" >
+                        </ul>
+                    </div>
+                </div>
+            </div>
+        </div>
+        <div class="figcaption-img" id="figcaptionfigcaption">
+            <p class="panel-legend"><%=__("figcaptionImage")%></p>
+            <% var _id=_.uniqueId('figcaption') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="figcaption" value="1" <%=(figcaption==1)?"checked":""%>/>
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><%= __("figShowTitle")%></div>
+
+            </label>
+            <% var _id=_.uniqueId('figcaption') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="figcaption" value="2" <%=(figcaption==2)?"checked":""%>/>
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><%= __("figShowAll")%></div>
+            </label>
+            <% var _id=_.uniqueId('figcaption') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="figcaption" value="0"  <%=(figcaption==0)?"checked":""%>/>
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><%= __("doNothing")%></div>
+            </label>
+        </div>
+    </article>
+  </div>
+<div class="panel-option-container animated button-alignment">
+  <article class="panel-option">
+      <header>
+          <h3 class="option-name">
+              <span class="icon-button-alignment"></span>
+              <%=__("alignCurseur")%>
+          </h3>
+          <span class="panel-content-legend">
+              <%=__("alignCurseurLegend")%>
+          </span>
+      </header>
+        <div class="option-content">
+          <div class="controlPanel-selector">
+              <div class="option radio">
+                  <% var _id= _.uniqueId('alignment'); %>
+                      <div class="button-align-radio">
+
+                          <label for="<%=_id %>" class="inline-block-label">
+                              <input id="<%= _id%>" type="radio" class="field-input" value="slider-left" name="alignment" <%=alignment==="slider-left"? ' checked="checked" ':'' %> />
+                              <div class="inline-block-label__top">
+                                  <span class="i-block-social-align_left"></span>
+                              </div>
+                              <div class="inline-block-label__bottom">
+                                  <span class="icon-radio-inactive"></span>
+                                  <span class="icon-radio-active"></span>
+                              </div>
+                          </label>
+
+                          <% var _id= _.uniqueId('alignment'); %>
+
+                          <label for="<%=_id %>" class="inline-block-label">
+                              <input id="<%= _id%>" type="radio" class="field-input" value="slider-middle" name="alignment" <%=alignment==="slider-middle"? ' checked="checked" ':'' %> />
+                              <div class="inline-block-label__top">
+                                  <span class="icon-button-align_center"></span>
+                              </div>
+                              <div class="inline-block-label__bottom">
+                                  <span class="icon-radio-inactive"></span>
+                                  <span class="icon-radio-active"></span>
+                              </div>
+                          </label>
+
+                          <% var _id= _.uniqueId('alignment'); %>
+
+                          <label for="<%=_id %>" class="inline-block-label">
+                              <input id="<%= _id%>" type="radio" class="field-input" value="slider-right" name="alignment" <%=alignment==="slider-right"? ' checked="checked" ':'' %> />
+                              <div class="inline-block-label__top">
+                                  <span class="i-block-social-align_right"></span>
+                              </div>
+                              <div class="inline-block-label__bottom">
+                                  <span class="icon-radio-inactive"></span>
+                                  <span class="icon-radio-active"></span>
+                              </div>
+                          </label>
+                      </div>
+
+              </div>
+          </div>
+      </div>
+    </article>
+</div>
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Templates/compareOption.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Views/CompareBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Views/CompareBlockView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Views/CompareBlockView.js	(révision 11082)
@@ -0,0 +1,57 @@
+define([
+    "jquery",
+    "underscore",
+    "JEditor/Commons/Events",
+    "JEditor/PagePanel/Contents/Blocks/Block/Views/BlockView",
+    "JEditor/Commons/Files/Models/FileGroup",
+    "less"
+], function($, _, Events, BlockView, FileGroup, less) {
+    var CompareBlockView = BlockView.extend({
+        attributes: {
+            tabindex: 1,
+            class: "block compareblock"
+        },
+        initialize: function() {
+            this._super();
+        },
+        _onLoad: function() {
+            this.render();
+        },
+        _onImageChange: function() {
+            this.stopListening(this.model.options.files, Events.BackboneEvents.SYNC);
+            this.listenTo(this.model.options.compare.files, Events.BackboneEvents.SYNC, this.onLoad);
+            this.render();
+        },
+        render: function() {
+            this.undelegateEvents()
+            this._super();
+            this.triggerUpdate();
+            this.delegateEvents()
+            return this;
+        },
+        renderOptions: function(model, options) {
+            var image = this.model.options.compare.files;
+            const that = this;
+            console.log(model,options);
+            this.dom[this.cid].content.html('');
+            if (image.length > 0 ) {
+                this.$el.removeClass('empty');
+                image.forEach(function(element){
+                    if (element.attributes.fileUrl !== '#') {
+                        that.dom[that.cid].content.append('<img src="' + element.attributes.fileUrl + '"/>');
+                    }
+                    else {
+                        that.dom[that.cid].content.append('<div class="preview"><span class="icon-image"></span></div>');
+                    }
+                });
+            }
+            else{
+                this.dom[this.cid].content.html('<div class="empty-compare"><span class="icon-button-alignment"></span></div>');
+                this.$el.addClass('empty');
+            }
+            this.triggerUpdate();
+            return this;
+        }
+    });
+    return CompareBlockView;
+});

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Views/CompareBlockView.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Views/CompareImageView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Views/CompareImageView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Views/CompareImageView.js	(révision 11082)
@@ -0,0 +1,29 @@
+/**
+ * <AUTHOR>
+ * View pour chaque compareImage
+ */
+define(["JEditor/Commons/Ancestors/Views/View","text!../Templates/compareImage.html","i18n!../nls/i18n"],function (View,template,i18n) {
+    var CompareImageView = View.extend({
+        tagName:'li',
+        className:'image-element',
+        events:{
+            "click .delete":"onDeleteClick"
+        },
+        initialize:function () {
+            View.prototype.initialize.call(this);
+            this.template=this.buildTemplate(template,i18n);
+        },
+        /**
+         * on appel la fonction removeImage du model files pour effacer une compareImage 
+         * @param {*} event 
+         */
+        onDeleteClick:function (event) {
+            this.model.files.removeImage(this.model);
+        },
+        render:function () {
+            View.prototype.render.call(this);
+            this.$el.html(this.template({image:this.model}));
+        }
+    });
+    return CompareImageView;
+});

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Views/CompareImageView.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Views/CompareOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Views/CompareOptionView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Views/CompareOptionView.js	(révision 11082)
@@ -0,0 +1,105 @@
+define(
+    [
+        "jquery",
+        "underscore",
+        "text!../Templates/compareOption.html",
+        "JEditor/Commons/Events",
+        "JEditor/PagePanel/Contents/Blocks/CompareBlock/Views/CompareImageView",
+        "JEditor/PagePanel/Contents/Options/Views/AbstractOptionView",
+        "JEditor/Commons/Files/Views/FileSelectorDialog",
+        "JEditor/FilePanel/Models/FileCollection"
+    ],
+    function($, _, compareOption, Events, CompareImageView, AbstractOptionView, FileSelectorDialog, FileCollection) {
+        /**
+         * Options de la galerie
+         * @class CompareOptionView
+         * @extends AbstractOptionView
+         */
+        var CompareOptionView = AbstractOptionView.extend({
+            optionType: 'compare',
+            events: {
+                "click .add-image .btn":"onAddClick",
+                'click input[type=radio]':'_onChangeRadio'
+                },
+            className: 'compare-option-home panel-content',
+            initialize: function() {
+                this._super();
+                this._template = this.buildTemplate(compareOption, this.translate);
+                this.collection = new FileCollection;
+                this.resetFilter();
+                this.listenTo(this.model,"remove:imagecompare",this.render);
+                this.listenTo(this.model,"add:imagecompare",this.render);
+            },
+            resetFilter: function(){
+                this.collection.resetFilter();
+                this.collection.fetch();
+            },
+            onlyImages: function(file) {
+                var regexpImg = /^image/;
+                return regexpImg.test(file.mimeType);
+            },
+            onAddClick: function (){
+                if (this.model.files.length < 2) {
+                    var selectFileView, that = this;
+                    this.resetFilter();
+                    selectFileView = new FileSelectorDialog({collection: this.collection, allowMultipleSelect: false});
+                    
+                    this.listenTo(selectFileView, Events.ListViewEvents.CHOOSE_FILE, function(selected) {
+                        this.model.addImage( selected );
+                    });
+                    this.listenTo(selectFileView, Events.DialogEvents.CLOSE, function() {
+                        this.stopListening(selectFileView);
+                        selectFileView.remove();
+                    });
+                    selectFileView.imagesOnly();
+                    selectFileView.open();
+                }
+                return false;
+            },
+            render: function() {
+                this.undelegateEvents();
+                var that = this;
+                this.$el.empty();
+                this.$el.html(this._template(this.model));
+                this.model.files.forEach(function (field) {
+                    var view = new CompareImageView({model:field});
+                    that.$("ul.image-column").append(view.el);
+                    view.render();
+                });
+                this.$("ul.image-column").sortable({
+                    cursor: "move",
+                    placeholder:"field-placeholder",
+                    update:function(event,ui){
+                        var newfiles = [];
+                        var listeSN = that.model.files;
+                        $(this).children().each(function(index){
+                            console.log($(this).children(".show-part"));
+                            var id = $(this).children(".show-part").attr('id');
+                            var idx = listeSN.findIndex(function(item) {
+                                return item.id == id;
+                            });
+                            newfiles.push(listeSN[idx]);
+                        });
+                        that.model.files = newfiles;
+                        that.$("ul.image-column").remove();
+                        that.render();
+                    }
+                });
+                this.scrollables({
+                    advanced:{ autoScrollOnFocus: false }
+                });
+                this.delegateEvents();
+                return this;
+            },
+            _onChangeRadio: function(event){
+                var name = event.currentTarget.name;
+                if (name === "figcaption"){
+                    this.model.figcaption = event.currentTarget.value;
+                }else if(name === "alignment"){
+                    this.model.alignment = event.currentTarget.value;
+                }
+            },
+            
+        });
+        return CompareOptionView;
+    });

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Views/CompareOptionView.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Views/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Views/main.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Views/main.js	(révision 11082)
@@ -0,0 +1,18 @@
+// this file has been auto-generated by a grunt task, it will be overriden, do not modify it
+define([
+        "./CompareBlockView",
+        "./CompareOptionView",
+        "./CompareImageView"
+    ],
+    function(  
+        CompareBlockView,
+        CompareOptionView,
+        CompareImageView
+    ){
+        var comp={
+        "CompareBlockView"        :   CompareBlockView,
+        "CompareOptionView"       :   CompareOptionView,
+        "CompareImageView"          :   CompareImageView
+        };
+        return comp;
+});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/Views/main.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/main.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/main.js	(révision 11082)
@@ -0,0 +1,7 @@
+// this file has been auto-generated by a grunt task, it will be overriden, do not modify it
+define(["./CompareBlock","./Views/main","./Models/main","i18n!./nls/i18n"],function(CompareBlock,Views,Models,i18n){
+    CompareBlock.Models=Models;
+    CompareBlock.Views=Views;
+    CompareBlock.i18n=i18n;
+    return CompareBlock;
+});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/main.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/nls/fr-ca
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/nls/fr-ca	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/nls/fr-ca	(révision 11082)
@@ -0,0 +1 @@
+link fr-fr
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/nls/fr-ca
___________________________________________________________________
Added: svn:special
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/nls/fr-fr/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/nls/fr-fr/i18n.js	(révision 11082)
@@ -0,0 +1,17 @@
+define({  
+    "BLOCK_NAME":"Comparer",
+    "compare"  :  "Comparer",
+    "compareBlockOption"    :     "Compare options",
+    "compareBlockOption"  :   "Compare options",
+    "compareContent" :  " Créer un comparateur d'images",
+    "compareLegend" :   "créez et personnalisez ici votre comparateur d'images. Ajouter deux images",
+    "addImage"  :   "Ajouter une image",
+    "figcaptionImage"   :   "Afficher les informations des images",
+    "figcaption"    :   "figcaption",
+    "figShowTitle"  :   "Afficher le titre",
+    "figShowAll"    :   "Afficher le titre et la description",
+    "doNothing" :   "Ne rien afficher",
+    "alignCurseur"  :   "Alignement du curseur",
+    "alignCurseurLegend"    :   "Sélectionnez l'alignement du curseur",
+    "alignment" :   "alignement",            
+});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/nls/fr-fr/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/nls/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/nls/i18n.js	(révision 11082)
@@ -0,0 +1,21 @@
+define({  
+    "root":{  
+       "BLOCK_NAME":"Compare",
+       "compare"  :  "Compare",
+       "compareBlockOption"    :     "Compare options",
+       "compareBlockOption"  :   "Compare options",
+       "compareContent" :  "Create an image comparator",
+       "compareLegend"  :  "create and customize your image comparator here. Add two images",
+       "addImage" :  "Add an image",
+       "figcaptionImage"   :  "Display image information",
+       "figcaption"  :  "figcaption",
+       "figShowTitle"   :  "Show title",
+       "figShowAll"  :  "Display title and description",
+       "doNothing"   :  "Display nothing",
+       "alignCurseur"   :  "Cursor alignment",
+       "alignCurseurLegend"   :  "Select cursor alignment",
+       "alignment"   :  "alignment",
+    },
+    "fr-fr":true,
+    "fr-ca": true
+ });
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CompareBlock/nls/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json	(révision 11081)
+++ src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json	(révision 11082)
@@ -17,5 +17,6 @@
  "LegalsBlock": "LegalsBlock",
  "EvaluationBlock":"EvaluationBlock",
  "CarrouselBlock":"CarrouselBlock",
- "GalerieBlock":"GalerieBlock"
+ "GalerieBlock":"GalerieBlock",
+ "CompareBlock":"CompareBlock"
 }
Index: src/js/build.js
===================================================================
--- src/js/build.js	(révision 11081)
+++ src/js/build.js	(révision 11082)
@@ -44,6 +44,7 @@
         "JEditor/PagePanel/Contents/Blocks/Block",
         "JEditor/PagePanel/Contents/Blocks/FormBlock",
         "JEditor/PagePanel/Contents/Blocks/CarrouselBlock",
+        "JEditor/PagePanel/Contents/Blocks/CompareBlock",
         "JEditor/PagePanel/Contents/Blocks/GalerieBlock",
         "JEditor/PagePanel/Contents/Blocks/HtmlBlock",
         "JEditor/PagePanel/Contents/Blocks/ImageBlock",
Index: src/js/main.js
===================================================================
--- src/js/main.js	(révision 11081)
+++ src/js/main.js	(révision 11082)
@@ -49,6 +49,7 @@
     "JEditor/PagePanel/Contents/Blocks/Block",
     "JEditor/PagePanel/Contents/Blocks/FormBlock",
     "JEditor/PagePanel/Contents/Blocks/CarrouselBlock",
+    "JEditor/PagePanel/Contents/Blocks/CompareBlock",
     "JEditor/PagePanel/Contents/Blocks/GalerieBlock",
     "JEditor/PagePanel/Contents/Blocks/HtmlBlock",
     "JEditor/PagePanel/Contents/Blocks/ImageBlock",
Index: src/less/imports/page_panel/main.less
===================================================================
--- src/less/imports/page_panel/main.less	(révision 11081)
+++ src/less/imports/page_panel/main.less	(révision 11082)
@@ -15,6 +15,7 @@
 @import 'module/block-options/btn-group';
 @import 'module/block-options/col-setup';
 @import 'module/block-options/show-part';
+@import 'module/block-options/compare-option';
 
 @import 'module/block-render/social-bar';
 @import 'module/block-render/grid';
@@ -21,6 +22,7 @@
 @import 'module/block-render/click-rdv';
 @import 'module/block-render/table';
 @import 'module/block-render/render-fake';
+@import 'module/block-render/compare';
 
 @import 'module/checkbox';
 @import 'module/classic-modifier';
Index: src/less/imports/page_panel/module/block-options/compare-option.less
===================================================================
--- src/less/imports/page_panel/module/block-options/compare-option.less	(nonexistent)
+++ src/less/imports/page_panel/module/block-options/compare-option.less	(révision 11082)
@@ -0,0 +1,66 @@
+.images-content {
+	background-color: #0f0f0f;
+	.add-image {
+		margin-bottom: 10px;
+		a {
+			width: 88%;
+		}
+	}
+	ul.image-column {
+		list-style-type: none;
+		padding: 0;
+		margin: 5px;
+		min-height: 100px;
+		-webkit-border-radius: 4px;
+		-moz-border-radius: 4px;
+		border-radius: 4px;
+		.field-placeholder {
+			border-radius: 5px;
+			background: #0f0f0f;
+			height: 5px;
+			border: 1px dashed #1a1a1a;
+		}
+		.image-element {
+			margin: 5px;
+			background: #474747;
+			height: 30px;
+			.show-part {
+				position: relative;
+				padding: 5px;
+				* {
+					display: inline-block;
+				}
+				.delete {
+					width: 22px;
+					height: 22px;
+					font-size: 22px;
+					text-align: center;
+					position: absolute;
+					line-height: 22px;
+					cursor: pointer;
+				}
+				.collection-nb {
+					right: 22px;
+					position: absolute;
+					font-size: 10px;
+				}
+				.icon-grip {
+					left: 0;
+					opacity: 0.3;
+					cursor: move;
+				}
+				.field-name {
+					line-height: 22px;
+					position: absolute;
+					top: 5px;
+					right: 32px;
+					left: 30px;
+					bottom: 5px;
+					font-size: 14px;
+					white-space: nowrap;
+					overflow: hidden;
+				}
+			}
+		}
+	}
+}
\ No newline at end of file

Property changes on: src/less/imports/page_panel/module/block-options/compare-option.less
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/less/imports/page_panel/module/block-render/compare.less
===================================================================
--- src/less/imports/page_panel/module/block-render/compare.less	(nonexistent)
+++ src/less/imports/page_panel/module/block-render/compare.less	(révision 11082)
@@ -0,0 +1,33 @@
+.compareblock .content {
+    display: grid;
+    grid-template-columns: 6rem 1fr 6rem;
+}
+.compareblock .content img:first-child {
+    grid-column: 1 / 3;
+    grid-row: 1;
+}
+.compareblock .content img:last-child {
+    grid-column: 2 / 4;
+    grid-row: 1;
+    outline: 6px solid white;
+}
+.compareblock.empty .content{
+    display: block !important;
+}
+.empty-compare {
+    display: block;
+    position: relative;
+    min-height: 200px;
+    background-color: #efefef;
+    border: 1px solid #ffffff;
+    .border-radius(4px);
+
+    .icon-button-alignment{
+        top: 50%;
+        left: 50%;
+        .translate(-50%, -50%);
+        position: absolute;
+        font-size: 2.2em;
+        color: #b3b3b3;
+    }
+}
\ No newline at end of file

Property changes on: src/less/imports/page_panel/module/block-render/compare.less
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
