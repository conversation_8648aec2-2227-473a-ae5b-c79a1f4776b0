Revision: r11436
Date: 2023-10-16 12:43:20 +0300 (lts 16 Okt 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: block loop et shortcode nouvelles options (partie Js)

## Files changed

## Full metadata
------------------------------------------------------------------------
r11436 | srazanandralisoa | 2023-10-16 12:43:20 +0300 (lts 16 Okt 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Models/LoopStyleOption.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Templates/loopStyle.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopStyleOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/main.less

wishlist IDEO3.2: block loop et shortcode nouvelles options (partie Js)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Models/LoopStyleOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Models/LoopStyleOption.js	(révision 11435)
+++ src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Models/LoopStyleOption.js	(révision 11436)
@@ -15,7 +15,9 @@
                             size : 'medium', 
                             colorFilter : false,
                             escapeSize :   2,
-                            speedLoop : 2
+                            speedLoop : 2,
+                            reverse: false,
+                            solidBg: false,
                         },
                         toJSON: function() {
                             return {
@@ -23,11 +25,13 @@
                                 size :   this.size,
                                 colorFilter :   this.colorFilter,
                                 escapeSize  :this.escapeSize,
-                                speedLoop  :this.speedLoop
+                                speedLoop  :this.speedLoop,
+                                reverse: this.reverse,
+                                solidBg: this.solidBg
                             }
                         },
                     }
             );
-            LoopStyleOption.SetAttributes(['size', 'colorFilter','escapeSize','speedLoop']);
+            LoopStyleOption.SetAttributes(['size', 'colorFilter','escapeSize','speedLoop','reverse', 'solidBg']);
             return LoopStyleOption;
         });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Templates/loopStyle.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Templates/loopStyle.html	(révision 11435)
+++ src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Templates/loopStyle.html	(révision 11436)
@@ -75,14 +75,14 @@
 <div class="panel-option-container animated ">
     <article class="panel-option background-color">
         <header>
-            <h3 class="option-name"><%=__("blackwhite")%></h3>
-            <p class="panel-content-legend"><%=__("blackwhitedesc")%></p>
+            <h3 class="option-name"><%=__("option")%></h3>
+            <p class="panel-content-legend"><%=__("optiondesc")%></p>
         </header>
         <div class="option-content colorFilter">
-            <% var _id= _.uniqueId('colorFilter'); %>
+            <% var _id= _.uniqueId('id'); %>
             <div class="button-align-checkbox">
                 <label for="bw-<%=_id %>" class="inline-label">
-                    <input id="bw-<%= _id%>" type="checkbox" class="field-input" name="colorFilter" <%=colorFilter===true? ' checked="checked" ':'' %> />
+                    <input id="bw-<%= _id%>" type="checkbox" class="field-input"  name="colorFilter" <%= colorFilter===true? ' checked="checked" ':'' %> />
                     <div class="inline-label__container label">
                         <span class="checkbox-wrap">
                             <span class="icon-unchecked"></span>
@@ -94,6 +94,34 @@
                     </div>
                 </label>
             </div>
+            <div class="button-align-checkbox">
+                <label for="sb-<%=_id %>" class="inline-label">
+                    <input id="sb-<%= _id%>" type="checkbox" class="field-input" name="solidBg"<%=solidBg===true? ' checked="checked" ':'' %> />
+                    <div class="inline-label__container solid-bg label">
+                        <span class="checkbox-wrap">
+                            <span class="icon-unchecked"></span>
+                            <span class="icon-checked"></span>
+                        </span>
+                        <span class="text">
+                            <%= __("solidBg") %>
+                        </span>
+                    </div>
+                </label>
+            </div>
+            <div class="button-align-checkbox">
+                <label for="rs-<%=_id %>" class="inline-label">
+                    <input id="rs-<%= _id%>" type="checkbox" class="field-input" name="reverse" <%=reverse===true? ' checked="checked" ':'' %> />
+                    <div class="inline-label__container label">
+                        <span class="checkbox-wrap">
+                            <span class="icon-unchecked"></span>
+                            <span class="icon-checked"></span>
+                        </span>
+                        <span class="text">
+                            <%= __("reverse") %>
+                        </span>
+                    </div>
+                </label>
+            </div>
         </div>
     </article>
 </div>
Index: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopStyleOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopStyleOptionView.js	(révision 11435)
+++ src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopStyleOptionView.js	(révision 11436)
@@ -22,6 +22,7 @@
                         events: {
                             'slidechange .slider.spacing-size': '_setEscapeSize',
                             'slidechange .slider.speed-slider': '_setSpeedLoop',
+                            'click input[name="colorFilter"]': 'clickColorFilter',
                             'change input[type="radio"].select-box': '_onStyleAffichageChange',
                             'click .effect-radio': '_onChangeFormatImage'
                         },
@@ -44,7 +45,29 @@
                             var $target = $(event.currentTarget);
                             this.model.StyleAffichage = $target.val();
                         },
+                         /** 
+                         * @param {*} event 
+                         */
+                        clickColorFilter :function(event){
+                        var $target = $(event.currentTarget);
+                        this._controlSolidBg($target);
+                        },
                         /**
+                         * controller l'input solidBg 
+                         * @param {*} event 
+                         */
+                        _controlSolidBg:function($target){
+                            if ($target.is(':checked')) {
+                                this.model.solidBg=false;
+                                $('.colorFilter .solid-bg').addClass('disableSolid');
+                                $('input[name="solidBg"]').attr("disabled", "disabled").removeAttr("checked");;
+                            }
+                            else{
+                                 $('.colorFilter .solid-bg').removeClass('disableSolid');
+                                 $('input[name="solidBg"]').removeAttr("disabled");
+                            }
+                        },
+                        /**
                          * Slider change
                          */
                         _setSpeedLoop: function(event,ui){
@@ -68,7 +91,9 @@
                            // this.undelegateEvents();
                             var templateVars = {
                                 size :   this.model.size,
-                                colorFilter :   this.model.colorFilter
+                                colorFilter :   this.model.colorFilter,
+                                solidBg :   this.model.solidBg,
+                                reverse :   this.model.reverse
                             };
                             this.$el.html(this.template(templateVars));
                            
@@ -87,6 +112,7 @@
                                 max: 4,
                                 step: 1
                             });
+                            this._controlSolidBg(this.$('input[name="colorFilter"]'));
                             this.scrollables();
                             return this;
                         }
Index: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/fr-fr/i18n.js	(révision 11435)
+++ src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/fr-fr/i18n.js	(révision 11436)
@@ -28,10 +28,13 @@
     "emptyCollection"       :   "Votre collection d’images est vide.",
     "size"                  :   "Taille des images",
     "sizeLegend"            :   "Séléctionnez la taille des images",
-    "blackwhite"            :   "Noir et blanc",
-    "blackwhitedesc"        :   "Cocher la case pour forcer l'affichage du contenu en noir et blanc",
+    "option"                :   "Option",
+    "optiondesc"            :   "Cocher la case de l'option souhaitée",
     "escapeSize"            :   "Espaces",
     "escapeSizeDesc"        :   "Glisser pour ajuster les espaces entre les contenus",
     "speedLoop"             :   "Vitesse",
     "speedLoopDesc"         :   "Glisser pour ajuster la vitesse du défilement",
+    "blackwhite"            :   "Forcer noir et blanc",
+    "solidBg"               :   "Fond blanc d'images",
+    "reverse"               :   "Inverser l'animation",
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/i18n.js	(révision 11435)
+++ src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/i18n.js	(révision 11436)
@@ -32,12 +32,15 @@
        "emptyCollection"       :   "Your image collection is empty",
        "size"                  :   "Image size",
        "sizeLegend"            :   "Select image size",
-       "blackwhite"            :   "Black and white",
-       "blackwhitedesc"        :   "Check box to force content to be displayed in black and white",
+       "option"                :   "Option",
+       "optiondesc"            :   "Tick the box of the desired option",
        "escapeSize"            :   "Spaces",
        "escapeSizeDesc"        :   "Drag to adjust spaces between content",
        "speedLoop"             :   "Speed",
        "speedLoopDesc"         :   "Drag to adjust scrolling speed",
+       "blackwhite"            :   "Forcing black and white",
+       "solidBg"               :   "White background images",
+       "reverse"               :   "Reverse the animation",
     },
     "fr-fr":true,
     "fr-ca": true
Index: src/less/main.less
===================================================================
--- src/less/main.less	(révision 11435)
+++ src/less/main.less	(révision 11436)
@@ -2889,4 +2889,15 @@
 //ckeditor fix
 .cke_reset_all textarea {
   white-space: break-spaces!important;
+}
+
+.disableSolid{
+  background-color: #313131 !important;
+  color: #848484;
+  .checkbox-wrap {
+    background-color: #313131 !important;
+    span{
+      color: #999 !important;
+    }
+  }
 }
\ No newline at end of file
