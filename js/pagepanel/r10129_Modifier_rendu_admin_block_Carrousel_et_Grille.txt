Revision: r10129
Date: 2023-01-11 15:52:41 +0300 (lrb 11 Jan 2023) 
Author: norajaonarivelo 

## Commit message
Modifier rendu admin block Carrousel et Grille

## Files changed

## Full metadata
------------------------------------------------------------------------
r10129 | norajaonarivelo | 2023-01-11 15:52:41 +0300 (lrb 11 Jan 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselBlockView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/ImageGroupView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridBlock.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridBlockView.js
   M /branches/ideo3_v2/integration/src/less/imports/gallery_style.less

Modifier rendu admin block Carrousel et Grille
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselBlockView.js	(révision 10128)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselBlockView.js	(révision 10129)
@@ -59,12 +59,13 @@
                     classes.push(designOptions.galleryAnimations.transition);
             }
             return classes.join(' ');
-        },
+        }, 
         renderOptions: function(model, options) {
             var template, uid, css, html, fileGroup;
             fileGroup = this.getFileGroup();
-            if(fileGroup){
-                this.dom[this.cid].content.html('<div class="empty-gallery"><span class="icon-gallery"></span></div>');
+            if ((fileGroup instanceof FileGroup) && fileGroup.length != 0) {
+                this.dom[this.cid].content.html(
+                    '<div class="exist-gallery"><div><span class="icon-gallery"></span><span class="count">&nbsp;'+fileGroup.length+'</span></div></div>');
             }else{
                 this.dom[this.cid].content.html('<div class="empty-gallery"><span class="icon-gallery"></span></div>');
             }
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js	(révision 10128)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js	(révision 10129)
@@ -61,7 +61,10 @@
                     this.stopListening(oldFilegroup, 'change:length');
                 if (fileGroup && !fileGroup.length && !fileGroup.name) {
                     var date = new Date();
-                    fileGroup.name = this.translate("newCarrousel") + Utils.dateFormat(this.translate('dateFormat'));
+                    var options = { year: 'numeric', month: 'numeric', day: 'numeric',hour: 'numeric', minute: 'numeric', second: 'numeric' };
+                    var dateString = date.toLocaleDateString('fr-FR', options);
+                    
+                    fileGroup.name = this.translate("newGallery")+dateString ;//+ Utils.dateFormat(this.translate('dateFormat'));
                     fileGroup.save();
                     this.listenTo(this.model.getFileGroup(), 'change:length', this.render);
                 }
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/ImageGroupView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/ImageGroupView.js	(révision 10128)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/ImageGroupView.js	(révision 10129)
@@ -169,7 +169,7 @@
             if (!this.model.name) {
                 var date = new Date();
                 var dateString = date;
-                this.model.name = this.translate("newCarrousel") + Utils.dateFormat(this.translate('dateFormat'))
+                this.model.name = this.translate("newCollectionName") + Utils.dateFormat(this.translate('dateFormat'))
             }
             this.model.save();
         },
@@ -198,7 +198,7 @@
             if (!this.model.name) {
                 var date = new Date();
                 var dateString = date;
-                this.model.name = this.translate("newGallery") + Utils.dateFormat(this.translate('dateFormat'));
+                this.model.name = this.translate("newCollectionName") + Utils.dateFormat(this.translate('dateFormat'));
             }
             this.model.save();
 
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js	(révision 10128)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js	(révision 10129)
@@ -47,5 +47,5 @@
     'square'                :   "Carré",
     "arrowImage"            :   "Afficher des flèches de navigation",
     "ShowArrow"             :   "Afficher les boutons de navigation",
-   
+    "emptyCollection":"Votre collection d’images est vide.",
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js	(révision 10128)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js	(révision 10129)
@@ -51,6 +51,7 @@
        'square'                :   "Square",
        "arrowImage"            :   "Display navigation arrows",
        "ShowArrow"             :   "Show navigation buttons",
+       "emptyCollection"      :"Your image collection is empty",
     },
     "fr-fr":true,
  });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridBlock.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridBlock.html	(révision 10128)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridBlock.html	(révision 10129)
@@ -1,5 +1,15 @@
 <div id="<%= uid %>">
-    <% if(empty){ %>
+    
+    <% if(empty){%>
         <div class="empty-gallery"><span class="icon-photogrid-block"></span></div>
-    <% } %>
+    <% } else{
+    %>
+        <div class="exist-gallery">
+            <div>
+                <span class="icon-photogrid-block"></span>
+                <span class="count">&nbsp;<%=CountFileGroup%></span>
+            </div>
+           
+        </div>
+    <% }%>
 </div>
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridBlockView.js	(révision 10128)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridBlockView.js	(révision 10129)
@@ -25,14 +25,22 @@
             return this;
         },
         renderOptions: function(model, options) {
-            var uid, opts, styleOpts, caption, fileGroup, emptyGroup, mesfichiers, malangue, htmlContent, justify, zoom, rowHeight, margins;
+            var uid, opts, styleOpts, fileGroup, CountFileGroup,  htmlContent;
             uid = _.uniqueId('grid');
             opts = this.model.options.gridOption;
             styleOpts = this.model.options.gridStyleOption;
             fileGroup = this.getFileGroup();
-            htmlContent = this.template({uid: uid,  empty: true});
+           
+
+            if ((fileGroup instanceof FileGroup) && fileGroup.length != 0) {
+                CountFileGroup=fileGroup.length;
+                htmlContent = this.template({uid: uid,  empty: false,CountFileGroup:CountFileGroup});
+            }
+            else {
+                emptyGroup = true;
+                htmlContent = this.template({uid: uid,  empty: true});
+            }
             this.$('.content').html(htmlContent);
-
             /* 
             if ((fileGroup instanceof FileGroup) && fileGroup.length != 0) {
                 emptyGroup = false;
Index: src/less/imports/gallery_style.less
===================================================================
--- src/less/imports/gallery_style.less	(révision 10128)
+++ src/less/imports/gallery_style.less	(révision 10129)
@@ -40,7 +40,7 @@
             }
         }
     }
-}
+}   
 .empty-gallery {
     position: relative;
     min-height: 200px;
@@ -56,4 +56,32 @@
         font-size: 2.2em;
         color: #b3b3b3;
     }
+}
+.exist-gallery {
+    position: relative;
+    min-height: 200px;
+    background-color: #ffffff;
+    border:  1px solid #acacaceb;
+    .border-radius(4px);
+    div{
+        top: 50%;
+        left: 50%;
+        .translate(-50%, -50%);
+        position: absolute;
+        color: #747474;   
+        font-size: 2.2em;
+        font-family: Raleway,sans-serif;
+        .icon-gallery, .icon-photogrid-block {
+        
+            
+           
+        }
+        .count{
+            font-style: italic;
+            line-height: 40px;
+            font-size: 1.0em;
+        }
+    }
+    
+
 }
\ No newline at end of file
