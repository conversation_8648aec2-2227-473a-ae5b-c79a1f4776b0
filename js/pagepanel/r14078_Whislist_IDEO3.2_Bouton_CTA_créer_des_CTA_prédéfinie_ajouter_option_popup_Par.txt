Revision: r14078
Date: 2025-04-09 10:38:11 +0300 (lrb 09 Apr 2025) 
Author: rrakotoarinelina 

## Commit message
Whislist IDEO3.2 : Bouton CTA : créer des CTA prédéfinie + ajouter option popup (Partie JS)

## Files changed

## Full metadata
------------------------------------------------------------------------
r14078 | rrakotoarinelina | 2025-04-09 10:38:11 +0300 (lrb 09 Apr 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Links/Templates/LinkSelector.html
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Links/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Links/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Links/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonOption.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/CTACollection.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/CTAModel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonCTAOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/listeCTAView.js

Whislist IDEO3.2 : Bouton CTA : créer des CTA prédéfinie + ajouter option popup (Partie JS)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Links/Templates/LinkSelector.html
===================================================================
--- src/js/JEditor/Commons/Links/Templates/LinkSelector.html	(révision 14077)
+++ src/js/JEditor/Commons/Links/Templates/LinkSelector.html	(révision 14078)
@@ -1,5 +1,5 @@
 <%
-if(model.context && model.context.isImg()){
+ if(model.context && typeof model.context.isImg === 'function' && model.context.isImg()){
 %>
 <div class="panel-radio <%=model.type===TYPES.IMAGE ?'selected':''%>" data-value="<%=TYPES.IMAGE%>" >
     <div class="panel-radio-title">
@@ -25,6 +25,20 @@
             <% var _id=_.uniqueId('link_target'); %>
             <input type="radio" name="target" value="_blank" data-target="_blank" id="<%=_id %>" <%=model.target==='_blank'?' checked="checked" ':'' %> /><label for="<%=_id%>"><span class="radio"><span class="icon-radio-active"></span><span class="icon-radio-inactive"></span></span><%=__("targetBlank")%></label>
         </div>
+        <div class="option radio">
+            <% if (model.context && model.context.get('optionType') === 'ButtonOption') { %>
+                <% var _id=_.uniqueId('link_target'); %>
+                <input type="radio" name="target" value="_popup" data-target="_popup" id="<%=_id %>" <%=model.target==='_popup'?' checked="checked" ':'' %> />
+                <label for="<%=_id%>">
+                    <span class="radio">
+                        <span class="icon-radio-active"></span>
+                        <span class="icon-radio-inactive"></span>
+                    </span>
+                    <%=__("targetPopup")%>
+                </label>
+            <% } %>
+        </div>
+            
         <!--
         <div class="option radio">
             <% var _id=_.uniqueId('link_target'); %>
Index: src/js/JEditor/Commons/Links/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/Commons/Links/nls/fr-ca/i18n.js	(révision 14077)
+++ src/js/JEditor/Commons/Links/nls/fr-ca/i18n.js	(révision 14078)
@@ -1 +1,15 @@
-define({"imageZoom":"Zoomer sur l'image","imageZoomEnabled":"Le zoom sur l'image est activé","imageZoomTitle":"Afficher le titre de l’image sur le zoom","imageZoomDesc":"Afficher la description de l’image sur le zoom","openLink":"Ouvrir un lien","targetSelf":"Ouvrir le lien dans la même fenêtre","targetBlank":"Ouvrir le lien dans une autre fenêtre","relLitebox":"Ouvrir le lien dans une boîte de dialogue","sendToPage":"Envoyer vers une autre page du site","selectPage":"Sélectionnez une page","downloadFile":"Télécharger un fichier","downloadTheFile":"Téléchargement du fichier","openInNewWindow":"Ouvrir le fichier dans une autre fenêtre","doNothing":"Ne rien faire"});
\ No newline at end of file
+define({"imageZoom":"Zoomer sur l'image",
+    "imageZoomEnabled":"Le zoom sur l'image est activé",
+    "imageZoomTitle":"Afficher le titre de l’image sur le zoom",
+    "imageZoomDesc":"Afficher la description de l’image sur le zoom",
+    "openLink":"Ouvrir un lien",
+    "targetSelf":"Ouvrir le lien dans la même fenêtre",
+    "targetBlank":"Ouvrir le lien dans une autre fenêtre",
+    "targetPopup":"Ouvrir le lien dans une popup",
+    "relLitebox":"Ouvrir le lien dans une boîte de dialogue",
+    "sendToPage":"Envoyer vers une autre page du site",
+    "selectPage":"Sélectionnez une page",
+    "downloadFile":"Télécharger un fichier",
+    "downloadTheFile":"Téléchargement du fichier",
+    "openInNewWindow":"Ouvrir le fichier dans une autre fenêtre",
+    "doNothing":"Ne rien faire"});
\ No newline at end of file
Index: src/js/JEditor/Commons/Links/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/Commons/Links/nls/fr-fr/i18n.js	(révision 14077)
+++ src/js/JEditor/Commons/Links/nls/fr-fr/i18n.js	(révision 14078)
@@ -1 +1,15 @@
-define({"imageZoom":"Zoomer sur l'image","imageZoomEnabled":"Le zoom sur l'image est activé","imageZoomTitle":"Afficher le titre de l’image sur le zoom","imageZoomDesc":"Afficher la description de l’image sur le zoom","openLink":"Ouvrir un lien","targetSelf":"Ouvrir le lien dans la même fenêtre","targetBlank":"Ouvrir le lien dans une autre fenêtre","relLitebox":"Ouvrir le lien dans une boîte de dialogue","sendToPage":"Envoyer vers une autre page du site","selectPage":"Sélectionnez une page","downloadFile":"Télécharger un fichier","downloadTheFile":"Téléchargement du fichier","openInNewWindow":"Ouvrir le fichier dans une autre fenêtre","doNothing":"Ne rien faire"});
\ No newline at end of file
+define({"imageZoom":"Zoomer sur l'image",
+    "imageZoomEnabled":"Le zoom sur l'image est activé",
+    "imageZoomTitle":"Afficher le titre de l’image sur le zoom",
+    "imageZoomDesc":"Afficher la description de l’image sur le zoom",
+    "openLink":"Ouvrir un lien",
+    "targetSelf":"Ouvrir le lien dans la même fenêtre",
+    "targetBlank":"Ouvrir le lien dans une autre fenêtre",
+    "targetPopup":"Ouvrir le lien dans une popup",
+    "relLitebox":"Ouvrir le lien dans une boîte de dialogue",
+    "sendToPage":"Envoyer vers une autre page du site",
+    "selectPage":"Sélectionnez une page",
+    "downloadFile":"Télécharger un fichier",
+    "downloadTheFile":"Téléchargement du fichier",
+    "openInNewWindow":"Ouvrir le fichier dans une autre fenêtre",
+    "doNothing":"Ne rien faire"});
\ No newline at end of file
Index: src/js/JEditor/Commons/Links/nls/i18n.js
===================================================================
--- src/js/JEditor/Commons/Links/nls/i18n.js	(révision 14077)
+++ src/js/JEditor/Commons/Links/nls/i18n.js	(révision 14078)
@@ -1 +1,16 @@
-define({ "root": {"imageZoom":"Zoom on","imageZoomEnabled":"Zoom enabled","imageZoomTitle":"Show picture title on zoom","imageZoomDesc":"Show picture description on zoom","openLink":"Open a link","targetSelf":"Open the link in the same window","targetBlank":"Open the link in another window","relLitebox":"Open the link in another dialog box","sendToPage":"Send to another page of the site","selectPage":"Select a page","downloadFile":"Download a file","downloadTheFile":"Download the file","openInNewWindow":"Open the file in another window","doNothing":"Do nothing"}, "fr-fr":true, "fr-ca":true })
\ No newline at end of file
+define({ "root": {"imageZoom":"Zoom on",
+    "imageZoomEnabled":"Zoom enabled",
+    "imageZoomTitle":"Show picture title on zoom",
+    "imageZoomDesc":"Show picture description on zoom",
+    "openLink":"Open a link",
+    "targetSelf":"Open the link in the same window",
+    "targetBlank":"Open the link in another window",
+    "targetPopup":"Open link in popup window",
+    "relLitebox":"Open the link in another dialog box",
+    "sendToPage":"Send to another page of the site",
+    "selectPage":"Select a page",
+    "downloadFile":"Download a file",
+    "downloadTheFile":"Download the file",
+    "openInNewWindow":"Open the file in another window",
+    "doNothing":"Do nothing"}
+    , "fr-fr":true, "fr-ca":true })
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonOption.js	(révision 14077)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonOption.js	(révision 14078)
@@ -23,7 +23,9 @@
                         initialize: function() {
                             this._super();
                             if (!(this.link instanceof Link)) {
-                                this.link = new Link(this.link||{});
+                                this.link = new Link(this.link||{},{
+                                    context: this
+                                });
                             }
                             this.lastLink = this.link;
                             var zone = this.getZone();
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/CTACollection.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/CTACollection.js	(révision 14077)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/CTACollection.js	(révision 14078)
@@ -15,6 +15,12 @@
             return this.find(function(model) {
                 return model.get('ButtonOption').shortcode === shortcode;
             });
+        },
+        //ensure that this functino work properly rendering one or many elements
+        _getByLang: function(lang) {
+            return this.filter(function(model) {
+                return model.get('lang') === lang.split('_')[0];
+            });
         }
         
     });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/CTAModel.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/CTAModel.js	(révision 14077)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/CTAModel.js	(révision 14078)
@@ -15,6 +15,7 @@
             ButtonOption : {},
             ButtonStyleOption : {},
             advancedCSS : {},
+            lang : ''
         },
 
         initialize: function() {
@@ -26,6 +27,6 @@
         },
 
     });
-    CTAModel.SetAttributes(['ButtonOption', 'ButtonStyleOption', 'advancedCSS']);
+    CTAModel.SetAttributes(['ButtonOption', 'ButtonStyleOption', 'advancedCSS', 'lang']);
     return CTAModel;
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js	(révision 14077)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js	(révision 14078)
@@ -51,7 +51,8 @@
                             // this.listenTo(this.model, 'cta:selected', this.createButtonOptionView);
 
                             this.buttonCTAOptionView = new ButtonCTAOptionView({
-                                buttonModel: this.model
+                                buttonModel: this.model,
+                                currentLang : this.app.currentPanel.currentLang
                             });
                             this.listenTo(this.model, 'change', this.onModelChange);
                             this.delegateEvents();         
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonCTAOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonCTAOptionView.js	(révision 14077)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonCTAOptionView.js	(révision 14078)
@@ -25,8 +25,10 @@
             this._super(); // Call the parent class's initialize method
             this.template = this.buildTemplate(buttonCTAOption, translate);
             this.buttonModel = options.buttonModel;
+            this.currentLang = options.currentLang;
             this.listeCTAView = new listeCTAView({
                 'buttonModel' : this.buttonModel,
+                'currentLang' : this.currentLang
             });
             this.listeCTA = CTACollection.getInstance();
             this.ctaNotEmpty = this.listeCTA.length > 0;
@@ -50,14 +52,17 @@
         
         _onAddCTA: function(e) {
             e.preventDefault();
-
+        
             var cta = new CTAModel({
                 ButtonOption : this.buttonModel.options.ButtonOption.toJSON(),
                 ButtonStyleOption : this.buttonModel.options.ButtonStyleOption.toJSON(),
                 advancedCSS : this.buttonModel.options.advancedCSS.toJSON(),
+                lang :   this.currentLang.id.split('_')[0] //fr_FR garder seulement la partie avant le  _
             });
             //ajout reference cta
-            cta.ButtonOption.shortcode = "cta_" + (parseInt(this.listeCTA.length, 10) + 1);
+            //nombre cta pour currena lang 
+            var ctaCurrentLang = this.listeCTA._getByLang(this.currentLang.id);
+            cta.ButtonOption.shortcode = "cta_" + (parseInt(ctaCurrentLang.length, 10) + 1);
             //valeur par defaut si text button vide
             if(cta.ButtonOption.text == null) {
                 cta.ButtonOption.text = cta.ButtonOption.shortcode ;
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/listeCTAView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/listeCTAView.js	(révision 14077)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/listeCTAView.js	(révision 14078)
@@ -14,15 +14,18 @@
 
             constructor: function(options) {
                 this.buttonModel = options.buttonModel;
+                this.currentLang = options.currentLang;
                 View.apply(this, arguments);
             },
             initialize: function () {
                 this._super();
                 this._template = this.buildTemplate(template,translate);
-                this.listeCTA = CTACollection.getInstance();
+                this.ctaCollection = CTACollection.getInstance();
             },
-
+            
             render: function () {
+                //liste by current lang
+                this.listeCTA = this.ctaCollection._getByLang(this.currentLang.id);
                 // this.undelegateEvents();
                 var listeCTAForTemplate = this.listeCTA.length > 0 ? this.processCTAData(this.listeCTA) : [];
                 this.$el.html(this._template({liste: listeCTAForTemplate, buttonShortcode : typeof this.buttonModel != "undefined" ? this.buttonModel.options.ButtonOption.shortcode : ""}));
