Revision: r13132
Date: 2024-10-01 08:01:54 +0300 (tlt 01 Okt 2024) 
Author: frahajanirina 

## Commit message
Nouveau block Card avec option d'affichage Carrousel ou Grille

## Files changed

## Full metadata
------------------------------------------------------------------------
r13132 | frahajanirina | 2024-10-01 08:01:54 +0300 (tlt 01 Okt 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Articles/Blocks.js
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/AvailableView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/CardBlock.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/Card.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/CardOption.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/CardStyleOption.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/Svg.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/SvgCollection.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/main.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardBlock.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardList.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardOption.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardStyleOption.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/selectIcon.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardBlockView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardListView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardOptionView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardStyleOptionView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/SelectIconView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/main.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/main.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/de-de
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/de-de/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/en-au
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/en-au/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/en-ca
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/en-ca/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/es-es
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/es-es/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/fr-ca
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/fr-ca/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/fr-fr
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/fr-fr/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json
   M /branches/ideo3_v2/integration/src/js/build.js
   M /branches/ideo3_v2/integration/src/js/config.js
   M /branches/ideo3_v2/integration/src/js/main.js

Nouveau block Card avec option d'affichage Carrousel ou Grille
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Articles/Blocks.js
===================================================================
--- src/js/JEditor/NewsPanel/Articles/Blocks.js	(révision 13131)
+++ src/js/JEditor/NewsPanel/Articles/Blocks.js	(révision 13132)
@@ -20,7 +20,8 @@
     "JEditor/PagePanel/Contents/Blocks/GalerieBlock",
     "JEditor/PagePanel/Contents/Blocks/CompareBlock",
     "JEditor/PagePanel/Contents/Blocks/LoopBlock",
-    "JEditor/PagePanel/Contents/Blocks/SlideshowBlock"
+    "JEditor/PagePanel/Contents/Blocks/SlideshowBlock",
+    "JEditor/PagePanel/Contents/Blocks/CardBlock"
   ], function (
     ImageBlock,
     HtmlBlock,
@@ -43,7 +44,8 @@
     GalerieBlock,
     CompareBlock,
     LoopBlock,
-    SlideshowBlock
+    SlideshowBlock,
+    CardBlock
   ) {
     var component = {
       "ImageBlock": ImageBlock,
@@ -67,7 +69,8 @@
       "GalerieBlock" : GalerieBlock,
       "CompareBlock": CompareBlock,
       "LoopBlock": LoopBlock,
-      "SlideshowBlock": SlideshowBlock
+      "SlideshowBlock": SlideshowBlock,
+      "CardBlock": CardBlock
     };
     return component;
   });
Index: src/js/JEditor/NewsPanel/Views/AvailableView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/AvailableView.js	(révision 13131)
+++ src/js/JEditor/NewsPanel/Views/AvailableView.js	(révision 13132)
@@ -41,7 +41,7 @@
                             {
                                 events: {'click .block.available': 'addToCurrentContainer', 'dragstart .bloc.available': '_ondragStart'},
                                 blockOrder: {
-                                    'standard': ["TextBlock", "ImageBlock", "ButtonBlock", "SeparatorBlock", "TableBlock"],
+                                    'standard': ["TextBlock", "ImageBlock", "ButtonBlock", "SeparatorBlock", "TableBlock", "CardBlock"],
                                     'advanced': ["CarrouselBlock", "GridBlock","GalerieBlock","CompareBlock","SlideshowBlock", "LoopBlock", "FormBlock", /*"VideoLinkeoBlock",*/ "MapBlock", "VideoBlock", "HtmlBlock"],
                                     'others': ["LegalsBlock","SiteMapBlock","SocialNetworkBlock"],
                                     'ignored':['TwitterTimelineBlock']
Index: src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js	(révision 13131)
+++ src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js	(révision 13132)
@@ -42,7 +42,7 @@
                             {
                                 events: {'click .block.available': 'addToCurrentContainer', 'dragstart .bloc.available': '_ondragStart'},
                                 blockOrder: {
-                                    'standard': ["TextBlock", "ImageBlock", "ButtonBlock", "SeparatorBlock", "TableBlock"],
+                                    'standard': ["TextBlock", "ImageBlock", "ButtonBlock", "SeparatorBlock", "TableBlock", "CardBlock"],
                                     'advanced': ["CarrouselBlock", "GridBlock","GalerieBlock","CompareBlock","SlideshowBlock", "LoopBlock", "FormBlock", /*"VideoLinkeoBlock",*/ "MapBlock", "VideoBlock", "HtmlBlock"],
                                     'others': ["LegalsBlock","SiteMapBlock","SocialNetworkBlock"],
                                     'ignored':['TwitterTimelineBlock']
Index: src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js	(révision 13131)
+++ src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js	(révision 13132)
@@ -20,7 +20,8 @@
   "./GalerieBlock",
   "./CompareBlock",
   "./LoopBlock",
-  "./SlideshowBlock"
+  "./SlideshowBlock",
+  "./CardBlock"
 ], function (
   ImageBlock,
   HtmlBlock,
@@ -43,7 +44,8 @@
   GalerieBlock,
   CompareBlock,
   LoopBlock,
-  SlideshowBlock
+  SlideshowBlock,
+  CardBlock
 ) {
   var component = {
     "ImageBlock": ImageBlock,
@@ -67,7 +69,8 @@
     "GalerieBlock" : GalerieBlock,
     "CompareBlock": CompareBlock,
     "LoopBlock": LoopBlock,
-    "SlideshowBlock": SlideshowBlock
+    "SlideshowBlock": SlideshowBlock,
+    "CardBlock": CardBlock
   };
   return component;
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/CardBlock.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/CardBlock.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/CardBlock.js	(révision 13132)
@@ -0,0 +1,36 @@
+define(
+    [
+        "JEditor/PagePanel/Contents/Blocks/Block/Block",
+        "JEditor/PagePanel/Contents/Blocks/CardBlock/Models/CardOption",
+        "JEditor/PagePanel/Contents/Blocks/CardBlock/Models/CardStyleOption"
+    ],
+    function(Block, CardOption, CardStyleOption) {
+        /**
+         * @class CardBlock
+         * @extends Block
+         */
+        var CardBlock = Block.extend(
+            /**
+            * @lends CardBlock
+            */
+            {
+                defaults: {
+                    type: 'card',
+                    options: [],
+                    contentType: 'cardBlock'
+                },
+                initialize: function() {
+                    this._super();
+                    if (!this.options.cardOption)
+                        this.options.add(new CardOption());
+                    if(!this.options.cardStyleOption)
+                        this.options.add(new CardStyleOption());
+                },
+            }
+        );
+        // icon du block carte
+        CardBlock.ICON = 'icon-card-style1';
+        
+        return CardBlock;
+    }
+);
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/CardBlock.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/Card.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/Card.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/Card.js	(révision 13132)
@@ -0,0 +1,68 @@
+define(
+    [
+        "JEditor/Commons/Ancestors/Models/Model",
+        "JEditor/Commons/Links/Models/Link",
+        "i18n!../nls/i18n"
+    ],
+    function (Model, Link, translate) {
+        var allowedSizes=["tiny","small","medium","large"];
+        var allowedColors=["pastel","vibrante","contour","pastel-lead","vibrante-lead","contour-lead"];
+        var allowedButtonAlign=["","align-left","align-center","align-right","full-width"];
+        var allowedTextAlign=["text-left","text-right","text-center"];
+        /**
+         * Les Options des blocs d'image
+         * @class ImageOptionss
+         * @extends AbstractOption
+         * @property {File} file Fichier correspondant à l'image
+         * @property {Link} link Les paramètres du lien de l'image
+         * @property {Object} Ccontext_infos infos contextuelles (context_infos.title, context_infos.desc)
+         */
+        var Card = Model.extend({
+            /**
+             * @lends ImageOptionss
+             */
+            defaults: {
+                title: "", 
+                description: "",
+                options:[],
+                icon: "",
+                buttonText : "",
+                link : null,
+                index: null,
+                size:'medium',
+                buttonAlignment:'align-center',
+                textAlignment:'text-center',
+                color:'pastel',
+                existeBtn: false
+
+            },
+            initialize:function () {
+                this._super();
+                Model.prototype.initialize.call(this);
+                if (!(this.link instanceof Link)) {
+                    this.link = new Link(this.link||{});
+                }
+                this.lastLink = this.link;
+            },
+            validate: function(attributes) {
+                if(allowedSizes.lastIndexOf(attributes.size)<=-1){
+                    return {field: "size", message: translate("Invalid_size" )};
+                }
+                if(allowedButtonAlign.lastIndexOf(attributes.buttonAlignment)<=-1){
+                    return {field: "buttonAlignment", message: translate("Invalid_buttonAlignment" )};
+                }
+                if(allowedTextAlign.lastIndexOf(attributes.textAlignment)<=-1){
+                    return {field: "textAlignment", message: translate("Invalid_textAlignment" )};
+                }
+                if(allowedColors.lastIndexOf(attributes.color)<=-1){
+                    return {field: "color", message: translate("Invalid_color" )};
+                }
+                if (!attributes.text || !_.isString(attributes.text) || _.isString(attributes.text) && attributes.text.length > 50)
+                    return {field: "text", message: translate("Invalid_text")};
+            },
+            
+        }).setAttributes(["title", "description", "options", "icon", "buttonText", "link", "index", "color", "size", "buttonAlignment", "textAlignment", "existeBtn"]);
+
+        return Card;
+    }
+);

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/Card.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/CardOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/CardOption.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/CardOption.js	(révision 13132)
@@ -0,0 +1,86 @@
+define(
+    [
+        "underscore",
+        "JEditor/PagePanel/Contents/Options/Models/AbstractOption",
+        "./Card"
+    ],
+    function(_, AbstractOption, Card) {
+        /**
+         * @class CardOption
+         * @extends AbstractOption
+         * @type @exp;JEditor@pro;Contents@pro;Options@pro;AbstractOption@call;extend
+         */
+        var CardOption = AbstractOption.extend(
+            /**
+             * @lends CardOptions.prototype
+             */
+            {
+                defaults: {
+                    priority: 70, 
+                    optionType: 'cardOption',
+                    cards:[]
+                },
+                initialize: function() {
+                    that = this;
+                    this.cards = (this.cards ? this.cards : []).map(function(card) {
+                        if (!(card instanceof Card)) {
+                            card = new Card(card);
+                            card.cards = that;
+                            card.index = that.cards.length;
+                        }
+                        return card;
+                    });
+                    this._super();
+                },
+                addCardToList: function() {
+                    var card = new Card();
+                    card.cards = this;
+                    card.index = this.cards.length;
+                    this.cards.push(card);
+                    this.trigger("add:card", this, card);
+                },
+                removeCardToList:function (card) {
+                    var index;
+                    if (card instanceof Card)
+                        index = this.cards.lastIndexOf(card);
+                    else
+                        index = card;
+                    card = this.cards.splice(index, 1)[0];
+                    this.trigger("remove:card", this.cards, card);
+                    return this;
+                },
+                updateCardToList: function(card, newAttributes) {
+                    var index;
+                    if (card instanceof Card) {
+                        index = this.cards.lastIndexOf(card);
+                    } else {
+                        index = card;
+                        card = this.cards[index];
+                    }
+                    if (index >= 0 && index < this.cards.length) {
+                        if (!(card instanceof Card)) {
+                            card = new Card(card);
+                            this.cards[index] = card;
+                        }
+                        card.set(newAttributes);
+
+                        this.trigger("update:card", this, card);
+                    }
+                },                
+                parse: function(response) {
+                    response.cards = (response.cards ? response.cards : []).map(function(card) {
+                        if (!(card instanceof Card)) {
+                            card = new Card(card);
+                        }
+                        return card;
+                    });
+
+                return response;
+                },
+            }
+        );
+        CardOption.SetAttributes(['cards']);
+
+        return CardOption;
+    }
+);
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/CardStyleOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/CardStyleOption.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/CardStyleOption.js	(révision 13132)
@@ -0,0 +1,32 @@
+define([
+    "JEditor/PagePanel/Contents/Options/Models/AbstractOption",
+    "JEditor/Commons/Events"
+], function(AbstractOption) {
+    /**
+     * Styles de la galerie
+     * @class GalleryStyleOption
+     * @extends AbstractOption
+     * @property {array} colors Les couleurs personnalisées dans le design
+     * @property {string} theme classe css du thème de la galerie
+     */
+    var CardStyleOption = AbstractOption.extend(
+            /**
+             * @lends GalleryStyleOptions.prototype
+             */
+                    {
+                        defaults: {
+                            optionType: 'cardStyleOption', 
+                            priority: 80, 
+                            cardStyle :   1,
+                            cardNbreCard :   3,
+                            cardStyleAff  :   1,
+                            Arrow       :   0,
+                            Autoplay: false,
+                            Duration: 5000,
+                        }
+                    }
+            );
+            CardStyleOption.SetAttributes(['cardStyle', 'cardNbreCard','cardStyleAff', 'Arrow', 'Autoplay', 'Duration']);
+            
+            return CardStyleOption;
+        });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/Svg.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/Svg.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/Svg.js	(révision 13132)
@@ -0,0 +1,13 @@
+define([
+    "JEditor/Commons/Ancestors/Models/Model",
+
+], function (Model) {
+    var Svg = Model.extend({
+        defaults: {
+            'name': '',
+            'content': ''
+        },
+    });
+
+    return Svg;
+});
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/SvgCollection.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/SvgCollection.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/SvgCollection.js	(révision 13132)
@@ -0,0 +1,59 @@
+define([
+	"JEditor/Commons/Events",
+	"JEditor/Commons/Ancestors/Models/Collection",
+    "JEditor/PagePanel/Contents/Blocks/CardBlock/Models/Svg",
+    "JEditor/ParamsPanel/Models/Params",
+],function(
+    Events,
+	Collection,
+	Svg,
+    Params
+){
+var /**
+ * 
+ * @class SvgCollection
+ * @extends Collection
+ * @property {MapPoint} model Le type de modèle à créer
+ */
+SvgCollection = Collection.extend(
+        /**
+         * @lends MapPointCollection.prototype
+         */
+                {
+                    model: Svg,
+                    constructor: function(options) {
+                        if(options && options.svgName !== ''){
+                            this.svgName = options.svgName;
+                        }else{
+                            this.svgName = '';
+                        }
+                    },
+                    /**
+                     * initialize l'objet
+                     */
+                    initialize: function() {
+                    },
+                    url: function() {
+                        return __IDEO_API_PATH__ + "/resources-svgcollection/" + this.name + ( this.svgName != '' ?  "/" + this.svgName :'' );
+                    },
+                    parse: function(response) {
+                        return response.data; // assuming the array of files is in the 'data' property of the response
+                    },
+
+                    fetchIcons: function(callback){
+                        this.params = Params.getInstance();
+                        this.name =  this.params.attributes.IconsCollection || 'outline';
+                        this.fetch({
+                            success: function(collection, response, options) {
+                                callback(null, response);
+                            },
+                            error: function(collection, response, options) {
+                                callback(new Error('Failed to fetch SVG'));
+                            }
+                        });
+                    },
+
+
+                });
+return SvgCollection;
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/main.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/main.js	(révision 13132)
@@ -0,0 +1,9 @@
+// this file has been auto-generated by a grunt task, it will be overriden, do not modify it
+define(["./CardOption", "./Card", "./CardStyleOption"],function(CardOption, Card, CardStyleOption){
+    var comp={
+        "CardOption":CardOption,
+        "Card":Card,
+        "CardStyleOption": CardStyleOption
+    };
+    return comp;
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardBlock.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardBlock.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardBlock.html	(révision 13132)
@@ -0,0 +1,31 @@
+<div class="blk-card">
+    <div class="blk-card__head">
+        <% if (card.icon) { %>
+            <span class="blk-card__icon"><%= card.icon %></span>
+        <% } %>
+        <p class="blk-card__title"><%= card.title ? __("cardOption")+' '+'"'+card.title+'"' : __("cardOption") %></p>
+    </div>
+    <div class="blk-card__content">
+        <% if (card.description) { %>
+            <p class="blk-card__description"><%= card.description %></p>
+        <% } %>
+        <ul class="blk-card__list">
+            <% _.each(card.options, function(option) { %>
+                <% if (option != '') { %>
+                    <li><%= option %></li>
+                <% } %>
+            <% }); %>
+        </ul>
+    </div>
+    <% if (card.existeBtn) { %>
+        <div class="blk-card__action">
+            <div class="blk-button block-button <%= card.color %> <%= card.size %> <%= card.buttonAlignment %> <%= card.textAlignment %>">
+                <a href="/" class="button blk-button__link">
+                    <span class="txt blk-button__label">
+                        <span><%= card.buttonText ||  __("button") %></span>
+                    </span>
+                </a>
+            </div>
+        </div>
+    <% } %>
+</div>
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardList.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardList.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardList.html	(révision 13132)
@@ -0,0 +1,390 @@
+<div class="show-part" data-edit-id="<%= id %>" style="position:relative;padding:5px;">
+    <span class="icon icon-grip" style="cursor: move;"></span>
+    <span class="text field-name" style="line-height:22px;position:absolute;top:5px;right:32px;left:30px;bottom:5px;text-overflow:ellipsis;"><%= title ? __("cardOption")+' '+'"'+title+'"' : __("cardOption") %></span>
+    <span class="icon icon-edit" data-edit-id="<%= index %>" style="width:22px;height:22px;font-size:16px;text-align:center;position:absolute;line-height:22px;cursor:pointer;"></span>
+    <span class="delete delete-card" data-edit-id="<%= index %>" style="width:22px;height:22px;font-size:16px;text-align:center;position:absolute;line-height:22px;cursor:pointer;">
+        <span class="icon icon-delete"></span>
+    </span>
+</div>
+
+<div class="edit-part hidden" data-edit-id="<%= index %>" style="position:absolute;background:#1a1a1a;width:250px;padding:10px;z-index:99;">
+    <div>
+        <label><%=__("title")%> :</label>
+        <input class="field-input" name="label" value="<%= title %>" type="text" placeholder="<%=__('title')%>"><br/>
+        <label><%=__("description")%> :</label>
+        <textarea class="field-input" name="description" placeholder="<%=__('description')%>"><%= description %></textarea><br/>
+        <div class="options">
+            <label><%=__("cardFieldOptionsMessage")%> :</label>
+            <textarea name="options" class="options-list field-input" placeholder="<%=__('options')%>"><%= options.join("\n") %></textarea>
+        </div><br/>
+        <div class="option-content">
+            <span class="switch card-icon" style="cursor: pointer;">
+                <span></span>
+            </span>
+            <span class="text"><%=__("showIconLegend")%></span>
+            <span class="selected-icon-wrapper"><%= icon %></span>
+            <div class="action-options radio-group btn-browse-icons" >
+                <button class="dialog-view-trigger page-selector btn-browse-icons">
+                    <span class="icon-page"></span>
+                    <span class="label"><%=__("browseIconsLegend")%></span>
+                </button>
+            </div>
+        </div><br/>
+        <div class="option-content">
+            <span class="switch card-button" style="cursor: pointer;">
+                <span></span>
+            </span>
+            <span class="text"><%=__("showButtonLegend")%></span><br/>
+            <div class="panel-option-container animated card-template-option">
+                <article class="panel-option template-option">
+                    <header>
+                        <h3 class="option-name"><%=__("addButton")%></h3>
+                    </header>
+                    <div class="button-text">
+                        <input class="field-input text-btn" name="text" placeholder="<%=__('addButton')%>"value="<%= buttonText %>" />
+                    </div>
+                    <header class="action-btn">
+                        
+                    </header>
+                </article>
+            </div>
+            <div class="panel-option-container animated button-color card-template-option-color">
+                <article class="panel-option">
+                    <header>
+                        <h3 class="option-name">
+                            <span class="icon-drop"></span>
+                            <%=__("colorButton")%>
+                        </h3>
+                        <span class="panel-content-legend"> <%=__("colorButtonLegend")%></span>
+                    </header>
+                    <div class="category-content radio-transformed">
+                        <%  var _id= _.uniqueId('sizeButton'); %>
+                        <div class="color-radio">
+                            <span class="effect-radio pastel <%=(color==='pastel')?'active':''%>" id="radio_color<%=_id%>" data-value="pastel" data-helper="pastel">
+                                <span class="helper">
+                                    <span class="help"><%=__("pastel")%></span>
+                                    <span class="bottom"></span>
+                                </span>
+                                <span class="container">
+                                    <span class="icon-form-long_text"></span>
+                                    <span class="switch-container">
+                                        <span class="radio">
+                                            <span></span>
+                                        </span>
+                                    </span>
+                                </span>
+                            </span>
+                        </div> 
+                        <div class="color-radio">
+                            <span class="effect-radio vibrante <%=(color==='vibrante')?'active':''%>" id="radio_color<%=_id%>" data-value="vibrante" data-helper="vibrante">
+                                <span class="helper">
+                                    <span class="help"><%=__("vibrante")%></span>
+                                    <span class="bottom"></span>
+                                </span>
+                                <span class="container">
+                                    <span class="icon-form-long_text"></span>
+                                    <span class="switch-container">
+                                        <span class="radio">
+                                            <span></span>
+                                        </span>
+                                    </span>
+                                </span>
+                            </span>
+                        </div> 
+                        <div class="color-radio">
+                            <span class="effect-radio contour <%=(color==='contour')?'active':''%>" id="radio_color<%=_id%>" data-value="contour" data-helper="contour">
+                                <span class="helper">
+                                    <span class="help"><%=__("contour")%></span>
+                                    <span class="bottom"></span>
+                                </span>
+                                <span class="container">
+                                    <span class="icon-form-long_text"></span>
+                                    <span class="switch-container">
+                                        <span class="radio">
+                                            <span></span>
+                                        </span>
+                                    </span>
+                                </span>
+                            </span>
+                        </div>
+                        <div class="color-radio">
+                            <span class="effect-radio pastel-lead <%=(color==='pastel-lead')?'active':''%>" id="radio_color<%=_id%>" data-value="pastel-lead" data-helper="pastel-lead">
+                                <span class="helper">
+                                    <span class="help"><%=__("pastel-lead")%></span>
+                                    <span class="bottom"></span>
+                                </span>
+                                <span class="container">
+                                    <span class="icon-form-long_text"></span>
+                                    <span class="switch-container">
+                                        <span class="radio">
+                                            <span></span>
+                                        </span>
+                                    </span>
+                                </span>
+                            </span>
+                        </div> 
+                        <div class="color-radio">
+                            <span class="effect-radio vibrante-lead <%=(color==='vibrante-lead')?'active':''%>" id="radio_color<%=_id%>" data-value="vibrante-lead" data-helper="vibrante-lead">
+                                <span class="helper">
+                                    <span class="help"><%=__("vibrante-lead")%></span>
+                                    <span class="bottom"></span>
+                                </span>
+                                <span class="container">
+                                    <span class="icon-form-long_text"></span>
+                                    <span class="switch-container">
+                                        <span class="radio">
+                                            <span></span>
+                                        </span>
+                                    </span>
+                                </span>
+                            </span>
+                        </div> 
+                        <div class="color-radio">
+                            <span class="effect-radio contour-lead <%=(color==='contour-lead')?'active':''%>" id="radio_color<%=_id%>" data-value="contour-lead" data-helper="contour-lead">
+                                <span class="helper">
+                                    <span class="help"><%=__("contour-lead")%></span>
+                                    <span class="bottom"></span>
+                                </span>
+                                <span class="container">
+                                    <span class="icon-form-long_text"></span>
+                                    <span class="switch-container">
+                                        <span class="radio">
+                                            <span></span>
+                                        </span>
+                                    </span>
+                                </span>
+                            </span>
+                        </div>  
+                    </div>
+                </article>
+            </div>
+            <div class="panel-option-container animated button-size card-template-option-size">
+                <article class="panel-option">
+                    <header>
+                        <h3 class="option-name">
+                            <span class="icon-button-size icon-midsize"></span>
+                            <%=__("sizeButton")%>
+                        </h3>
+                        <span class="panel-content-legend">
+                            <%=__("sizeButtonLegend")%>
+                        </span>
+                    </header>
+                    <div class="option-content">
+                        <div class="controlPanel-selector">
+                            <div class="option radio">
+                                <%  var _id= _.uniqueId('sizeButton'); %>
+                                    <div class="button-size-radio">
+            
+                                        <label for="<%=_id %>" class="inline-block-label">
+                                            <input id="<%= _id%>" type="radio" class="field-input" value="tiny" name="size" <%=size==="tiny"? ' checked="checked" ':'' %> />
+                                            
+                                            <div class="inline-block-label__top">
+                                                <span class="i-block-social-size_s"></span>
+                                            </div>
+                                            <div class="inline-block-label__bottom">
+                                                <span class="icon-radio-inactive"></span>
+                                                <span class="icon-radio-active"></span>
+                                            </div>
+                                        </label>
+            
+            
+                                        <% _id= _.uniqueId('sizeButton');      %>
+            
+                                        <label for="<%=_id %>" class="inline-block-label">
+                                            <input id="<%= _id%>" type="radio" class="field-input" value="small" name="size" <%=size==="small"? ' checked="checked" ':'' %> />
+                                            <div class="inline-block-label__top">
+                                                <span class="i-block-social-size_m"></span>
+                                            </div>
+                                            <div class="inline-block-label__bottom">
+                                                <span class="icon-radio-inactive"></span>
+                                                <span class="icon-radio-active"></span>
+                                            </div>
+                                        </label>
+            
+                                        <% _id= _.uniqueId('sizeButton');      %>
+            
+                                        <label for="<%=_id %>" class="inline-block-label">
+                                            <input id="<%= _id%>" type="radio" class="field-input" value="medium" name="size" <%=size==="medium"? ' checked="checked" ':'' %> />
+                                            <div class="inline-block-label__top">
+                                                <span class="i-block-social-size_l"></span>
+                                            </div>
+                                            <div class="inline-block-label__bottom">
+                                                <span class="icon-radio-inactive"></span>
+                                                <span class="icon-radio-active"></span>
+                                            </div>
+                                        </label>
+            
+                                        <% _id= _.uniqueId('sizeButton');      %>
+            
+                                        <label for="<%=_id %>" class="inline-block-label">
+                                            <input id="<%= _id%>" type="radio" class="field-input" value="large" name="size" <%=size==="large"? ' checked="checked" ':'' %> />
+                                            <div class="inline-block-label__top">
+                                                <span class="i-block-social-size_xl"></span>
+                                            </div>
+                                            <div class="inline-block-label__bottom">
+                                                <span class="icon-radio-inactive"></span>
+                                                <span class="icon-radio-active"></span>
+                                            </div>
+                                        </label>
+                                    </div>
+                            </div>
+                        </div>
+                    </div>
+                </article>
+            </div>
+            <div class="panel-option-container animated button-alignment card-template-option-alignement">
+                <article class="panel-option">
+                    <header>
+                        <h3 class="option-name">
+                            <span class="icon-button-alignment"></span>
+                            <%=__("alignButton")%>
+                        </h3>
+                        <span class="panel-content-legend">
+                            <%=__("alignButtonLegend")%>
+                        </span>
+                    </header>
+                    <div class="option-content">
+                        <div class="controlPanel-selector">
+                            <div class="option radio">
+            
+                                <% var _id= _.uniqueId('alignButton'); %>
+                                    <div class="button-align-radio">
+            
+                                        <label for="<%=_id %>" class="inline-block-label">
+                                            <input id="<%= _id%>" type="radio" class="field-input" value="align-left" name="buttonAlignment" <%=buttonAlignment==="align-left"? ' checked="checked" ':'' %> />
+                                            <div class="inline-block-label__top">
+                                                <span class="i-block-social-align_left"></span>
+                                            </div>
+                                            <div class="inline-block-label__bottom">
+                                                <span class="icon-radio-inactive"></span>
+                                                <span class="icon-radio-active"></span>
+                                            </div>
+                                        </label>
+            
+                                        <% var _id= _.uniqueId('alignButton'); %>
+            
+                                        <label for="<%=_id %>" class="inline-block-label">
+                                            <input id="<%= _id%>" type="radio" class="field-input" value="align-center" name="buttonAlignment" <%=buttonAlignment==="align-center"? ' checked="checked" ':'' %> />
+                                            <div class="inline-block-label__top">
+                                                <span class="icon-button-align_center"></span>
+                                            </div>
+                                            <div class="inline-block-label__bottom">
+                                                <span class="icon-radio-inactive"></span>
+                                                <span class="icon-radio-active"></span>
+                                            </div>
+                                        </label>
+            
+                                        <% var _id= _.uniqueId('alignButton'); %>
+            
+                                        <label for="<%=_id %>" class="inline-block-label">
+                                            <input id="<%= _id%>" type="radio" class="field-input" value="align-right" name="buttonAlignment" <%=buttonAlignment==="align-right"? ' checked="checked" ':'' %> />
+                                            <div class="inline-block-label__top">
+                                                <span class="i-block-social-align_right"></span>
+                                            </div>
+                                            <div class="inline-block-label__bottom">
+                                                <span class="icon-radio-inactive"></span>
+                                                <span class="icon-radio-active"></span>
+                                            </div>
+                                        </label>
+            
+                                        <% var _id= _.uniqueId('alignButton'); %>
+            
+                                        <label for="<%=_id %>" class="inline-block-label">
+                                            <input id="<%= _id%>" type="radio" class="field-input" value="full-width" name="buttonAlignment" <%=buttonAlignment==="full-width"? ' checked="checked" ':'' %> />
+                                            <div class="inline-block-label__top">
+                                                <span class="icon-button-justify"></span>
+                                            </div>
+                                            <div class="inline-block-label__bottom">
+                                                <span class="icon-radio-inactive"></span>
+                                                <span class="icon-radio-active"></span>
+                                            </div>
+                                        </label>
+            
+                                    </div>
+            
+                            </div>
+                        </div>
+                    </div>
+                </article>
+            </div>
+            <div class="panel-option-container animated button-textAlignment <%=buttonAlignment==='full-width'?'':'hidden'%> card-template-option-text-alignement">
+                <article class="panel-option">
+                    <header>
+                        <h3 class="option-name">
+                            <span class="icon-button-text"></span>
+                            <%=__("alignText")%>
+                        </h3>
+                        <span class="panel-content-legend">
+                            <%=__("alignTextLegend")%>
+                        </span>
+                    </header>
+                    <div class="option-content">
+                        <div class="controlPanel-selector">
+                            <div class="option radio">
+                                <%
+                                var _id= _.uniqueId('alignText');
+                                %>
+            
+                                    <div class="text-align-radio">
+            
+                                        <label for="<%=_id %>" class="inline-block-label">
+                                            <input id="<%= _id%>" type="radio" class="field-input" value="text-left" name="textAlignment" <%=textAlignment==="text-left"? ' checked="checked" ':'' %> />
+                                            <div class="inline-block-label__top">
+                                                <span class="i-block-social-align_left"></span>
+                                            </div>
+                                            <div class="inline-block-label__bottom">
+                                                <span class="icon-radio-inactive"></span>
+                                                <span class="icon-radio-active"></span>
+                                            </div>
+                                        </label>
+            
+                                        <% _id= _.uniqueId('alignText');      %>
+            
+                                        <label for="<%=_id %>" class="inline-block-label">
+                                            <input id="<%= _id%>" type="radio" class="field-input" value="text-center" name="textAlignment" <%=textAlignment==="text-center"? ' checked="checked" ':'' %> />
+                                            <div class="inline-block-label__top">
+                                                <span class="icon-button-align_center"></span>
+                                            </div>
+                                            <div class="inline-block-label__bottom">
+                                                <span class="icon-radio-inactive"></span>
+                                                <span class="icon-radio-active"></span>
+                                            </div>
+                                        </label>
+            
+                                        <% _id= _.uniqueId('alignText');      %>
+            
+                                        <label for="<%=_id %>" class="inline-block-label">
+                                            <input id="<%= _id%>" type="radio" class="field-input" value="text-right" name="textAlignment" <%=textAlignment==="text-right"? ' checked="checked" ':'' %> />
+                                            <div class="inline-block-label__top">
+                                                <span class="icon-button-align_right"></span>
+                                            </div>
+                                            <div class="inline-block-label__bottom">
+                                                <span class="icon-radio-inactive"></span>
+                                                <span class="icon-radio-active"></span>
+                                            </div>
+                                        </label>
+            
+                                    </div>
+            
+                            </div>
+                        </div>
+                    </div>
+                </article>
+            </div>            
+        </div><br/>
+    </div>
+    <div class="button-group save-or-cancel">
+        <a class="button cancel">
+            <span class="wrapper">
+                <span class="icon"></span>
+                <span class="text"><%= __("cancel")%></span>
+            </span>
+        </a>
+        <a class="button save">
+            <span class="wrapper">
+                <span class="icon"></span>
+                <span class="text"><%= __("save")%></span>
+            </span>
+        </a>
+    </div>
+</div>

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardList.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardOption.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardOption.html	(révision 13132)
@@ -0,0 +1,27 @@
+<div class="panel-option-container animated">
+    <article class="panel-option ">
+        <div class="option-card-content">
+            <div class="wrapper  black-dropdown">
+                <a class="btn" href="#">
+                    <span class="text">
+                        <span class="icon-add"></span>
+                        <%=__("addCard")%>
+                    </span>
+                </a>
+            </div>
+        </div>
+        <div class="panel-content active">
+            <div class="form-builder">
+                <div class="table-wrapper  col-setup">
+                    <div class="wrapper row-wrapper column-container">
+                        <div class="wrapper col-wrapper">
+                            <ul class="form-column" style="list-style-type:none;padding:0;margin:5px;min-height:100px;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px">
+            
+                            </ul>
+                        </div>
+                    </div>
+                </div>
+            </div>
+        </div>
+    </article>
+</div>

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardOption.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardStyleOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardStyleOption.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardStyleOption.html	(révision 13132)
@@ -0,0 +1,181 @@
+<div class="panel-option-container animated  mr15">
+    <article class="panel-option">
+        <header>
+            <h3 class="option-name"></span><%= __("styleDeGrille")%></h3>
+            <p class="panel-content-legend"><%= __("cardStyleLegend")%></p>
+        </header>
+        <div>
+            <div class="category-content radio-transformed stylleDeGrille stylleDeCard">
+                <% var _id= _.uniqueId('cardStyle'); %>
+                <div>
+                    <span class="effect-radio <%=(cardStyle===0)?'active':''%>" id="<%=_id %>" data-value="0" data-helper="masonry">
+                        <span class="helper">
+                            <span class="help"><%=__("masonryLegend")%></span>
+                            <span class="bottom"></span>
+                        </span>
+                        <span class="container">
+                            <span class="icon icon-photocss-column"></span>
+                            <span class="switch-container">
+                                <span class="radio">
+                                    <span></span>
+                                </span>
+                            </span>
+                        </span>
+                    </span>
+                </div>
+                <% var _id= _.uniqueId('cardStyle'); %>
+                <div>
+                    <span class="effect-radio <%=(cardStyle===1)?'active':''%>" id="<%=_id %>" data-value="1" data-helper="grid">
+                        <span class="helper">
+                            <span class="help"><%=__("gridLegend")%></span>
+                            <span class="bottom"></span>
+                        </span>
+                        <span class="container">
+                            <span class="icon icon-photogrid-block"></span>
+                            <span class="switch-container">
+                                <span class="radio">
+                                    <span></span>
+                                </span>
+                            </span>
+                        </span>
+                    </span>
+                </div>                
+            </div>
+        </div>
+    </article>
+    <div class="panel-option gallery-color <%= (cardStyle === 1) ? 'hidden' : '' %>" id="nav-option">
+        <p class="panel-legend"><%=__("arrowImages")%></p>
+        <% var _id=_.uniqueId('arrows') %>
+        <input id="<%= _id %>" class="field-input  for--block-label card-arrow" type="radio" name="Arrow" value="1" <%=(Arrow==1)?"checked":""%>/>
+        <label  class="block-label" for="<%= _id %>">
+            <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+            </div>
+            <div class="block-label-radio"><%= __("showArrow1")%></div>
+        </label>
+        <% var _id=_.uniqueId('arrows') %>
+        <input id="<%= _id %>" class="field-input  for--block-label card-arrow" type="radio" name="Arrow" value="2" <%=(Arrow==2)?"checked":""%>/>
+        <label  class="block-label" for="<%= _id %>">
+            <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+            </div>
+            <div class="block-label-radio"><%= __("showArrow2")%></div>
+        </label>
+    </div>
+    <div class="defilement <%= (cardStyle === 1) ? 'hidden' : '' %>">
+        <p class="panel-legend"><%=__("autoPlay")%></p>
+        <div class="option-content">
+          <% var _id=_.uniqueId('autoplay') %>
+          <input type="checkbox" class="blue-bg" name="Autoplay" id="<%=_id %>" <%=Autoplay?'checked="checked"':''%>>
+          <label for="<%=_id %>">
+            <span class="checkbox-wrapper">
+              <span class="icon-unchecked"></span>
+              <span class="icon-checked"></span>
+            </span>
+            <span class="text"><%=__("activeAutoPlay")%></span>
+          </label>
+        </div>
+    </div>
+    <div class="defilement-cran <%= (cardStyle === 1) ? 'hidden' : '' %>">
+        <p class="panel-legend"><%=__("duration")%></p>
+        <div class="option-content">
+            <div class="slider-container">
+                <span class="icon-less"></span>
+                <div class="slider"></div>
+                <span class="icon-more"></span>
+            </div>
+        </div>
+    </div>
+</div>
+<div class="mr15 grid-option-margin card-nbreImage">
+    <article class="panel-option">
+        <header>
+            <h3 class="option-name"><%=__("cardNombreCard")%></h3>
+            <p class="panel-content-legend"><%= __("cardNombreCardLegend")%></p>
+        </header>
+        <div class="option-content">
+            <div class="slider-container">
+                <span class="icon-less"></span>
+                <div class="slider"></div>
+                <span class="icon-more"></span>
+            </div>
+        </div>
+    </article>
+</div>
+<div class="panel-option-container animated">
+    <article class="panel-option background-color">
+        <header>
+            <h3 class="option-name"><%=__("cardStyleAffichage")%></h3>
+            <p class="panel-content-legend"><%=__("cardStyleAffichageDesc")%></p>
+        </header>
+        <div class="option-content colors">
+            <%var _id=_.uniqueId('cardStyleAffichage');%>
+            <input type="radio" class="select-box" name="AffichageCarrouselStyle" value="1" id="<%=_id %>" <%=(cardStyleAff==1)?'checked':''%>>
+            <label for="<%=_id %>">
+                <div class="wrapper">
+                    <div class="horizontal-wrap">
+                        <span class="icon-wrapper">
+                            <span class="icon-card-style1"></span>
+                        </span>
+                        <span class="name"><%= __("cardStyleOption")%> 1</span>
+                        <span class="desc"><%= __("DescStyle1")%></span>
+                    </div>
+                </div>
+            </label>
+            <%  var _id=_.uniqueId('cardStyleAffichage');%>
+            <input type="radio" class="select-box" name="AffichageCarrouselStyle" value="2" id="<%=_id %>" <%=(cardStyleAff==2)?'checked':''%>>
+            <label for="<%=_id %>">
+                <div class="wrapper">
+                    <div class="horizontal-wrap">
+                        <span class="icon-wrapper">
+                            <span class="icon-card-style2"></span>
+                        </span>
+                        <span class="name"><%= __("cardStyleOption")%> 2</span>
+                        <span class="desc"><%= __("DescStyle2")%></span>
+                    </div>
+                </div>
+            </label>
+            <%  var _id=_.uniqueId('cardStyleAffichage');%>
+            <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="3" id="<%=_id %>" <%=(cardStyleAff==3)?'checked':''%>>
+            <label for="<%=_id %>">
+                <div class="wrapper">
+                    <div class="horizontal-wrap">
+                        <span class="icon-wrapper">
+                            <span class="icon-card-style3"></span>
+                        </span>
+                        <span class="name"><%= __("cardStyleOption")%> 3</span>
+                        <span class="desc"><%= __("DescStyle3")%></span>
+                    </div>
+                </div>
+            </label>
+            <%  var _id=_.uniqueId('cardStyleAffichage');%>
+            <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="4" id="<%=_id %>" <%=(cardStyleAff==4)?'checked':''%>>
+            <label for="<%=_id %>">
+                <div class="wrapper">
+                    <div class="horizontal-wrap">
+                        <span class="icon-wrapper">
+                            <span class="icon-card-style4"></span>
+                        </span>
+                        <span class="name"><%= __("cardStyleOption")%> 4</span>
+                        <span class="desc"><%= __("DescStyle4")%></span>
+                    </div>
+                </div>
+            </label>
+            <%  var _id=_.uniqueId('cardStyleAffichage');%>
+            <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="5" id="<%=_id %>" <%=(cardStyleAff==5)?'checked':''%>>
+            <label for="<%=_id %>">
+                <div class="wrapper">
+                    <div class="horizontal-wrap">
+                        <span class="icon-wrapper">
+                            <span class="icon-card-style5"></span>
+                        </span>
+                        <span class="name"><%= __("cardStyleOption")%> 5</span>
+                        <span class="desc"><%= __("DescStyle5")%></span>
+                    </div>
+                </div>
+            </label>
+        </div>
+    </article>
+</div>
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/selectIcon.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/selectIcon.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/selectIcon.html	(révision 13132)
@@ -0,0 +1,14 @@
+<div class="container-icon option-content">
+    <div class="grid-container-icon">
+        <%for(i=0;i<content.length;i++){
+            svg = content[i];
+        %>
+            <div  class="box-icon <%=selected === svg.name ? 'selected-icon':''%>" data-name="<%= svg.name%>"  >
+                <span class="wrapper-icon">
+                    <%= svg.content%>
+                </span>
+            </div>
+        <%}%>
+    </div>
+</div>
+
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardBlockView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardBlockView.js	(révision 13132)
@@ -0,0 +1,62 @@
+define(
+    [
+        "jquery",
+        "underscore",
+        "JEditor/PagePanel/Contents/Blocks/Block/Views/BlockView",
+        "text!../Templates/CardBlock.html",
+        "i18n!../nls/i18n",
+        "JEditor/PagePanel/Contents/Blocks/CardBlock/Models/SvgCollection",
+    ],
+    function($, _, BlockView, template, translate, SvgCollection) {
+        /**
+         * Vue des blocs cartes
+         * @extends BlockView;
+         */
+        var CardBlockView = BlockView.extend({
+            attributes: {
+                tabindex: 1,
+                class: "block cardblock buttonblock"
+            },
+            initialize: function() {
+                BlockView.prototype.initialize.apply(this, arguments);
+                this.template = this.buildTemplate(template, translate);
+                // Écoute de l'événement
+                this.listenTo(this.model.options.cardOption, 'add:card', this.render);
+                this.listenTo(this.model.options.cardOption, 'remove:card', this.render);
+                this.listenTo(this.model.options.cardOption, 'update:card', this.render);
+                this.listenTo(this.model.options.cardOption, "cards:reordered", this.render); 
+                this.listenTo(this.model.options.cardOption, "change", this.render);   
+            },
+            _onLoad: function() {
+                this.render();
+            },
+            render: function() {
+                this._super();
+                var cards = this.model.options.cardOption.cards;
+                this.$('.content').empty();
+
+                _.each(cards, function(card) {
+                    // Crée un élément pour la carte
+                    var cardElement = $(this.template({ card: card }));
+
+                    // Vérifie si l'icône n'est pas vide
+                    if (card.icon && card.icon !== "") {
+                        var svgCollectionObj = new SvgCollection({ "svgName": card.icon });
+                        svgCollectionObj.fetchIcons(_.bind(function(error, svg) {
+                            if (error) {
+                                console.error(error);
+                            } else {
+                                cardElement.find(".blk-card__icon").html(svg.content);
+                            }
+                        }, this));
+                    }
+                    this.$('.content').append(cardElement);
+                }, this);
+
+                return this;
+            }
+        });
+
+        return CardBlockView;
+    }
+);

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardBlockView.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardOptionView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardOptionView.js	(révision 13132)
@@ -0,0 +1,78 @@
+define(
+    [
+        "jquery",
+        "underscore",
+        "text!../Templates/CardOption.html",
+        "JEditor/PagePanel/Contents/Options/Views/AbstractOptionView",
+        "./CardListView"
+    ],
+    function($, _, cardOption, AbstractOptionView, CardListView) {
+        /**
+         * Options de la carte
+         * @class CardOptionView
+         * @extends AbstractOptionView
+         */
+        var CardOptionView = AbstractOptionView.extend({
+            optionType: 'cardOption',
+            className: 'card-option-home panel-content',
+            events: {
+                "click .option-card-content": "onAddClick"
+            },
+            initialize: function() {
+                this._super();
+                this._template = this.buildTemplate(cardOption, this.translate);
+                this.listenTo(this.model, 'remove:card', this.render);
+            },
+            render: function() {
+                var that = this;
+                this.undelegateEvents();
+                this.$el.empty();
+                this.$el.html(this._template(this.model));
+                // Parcourir et rendre chaque carte dans la vue
+                this.model.cards.forEach(function (card) {
+                    var view = new CardListView({ model: card });
+                    that.$("ul.form-column").append(view.el);
+                    view.render();
+                });
+                /**
+                 * changer l'ordre des cartes
+                 */ 
+                this.$("ul.form-column").sortable({
+                    connectWith:"ul.form-column",
+                    handle:".icon-grip",
+                    placeholder:"field-placeholder",
+                    forcePlaceholderSize: true,
+                    update:function(event,ui){
+                        var newCards = [];
+                        var listeSN = that.model.cards;
+                        $(this).children().each(function(index){
+                            var id = $(this).find(".show-part").attr('data-edit-id');
+                            var card = listeSN.find(function(item) {
+                                return item.cid == id;
+                            });
+                            newCards.push(card);
+                        });
+                        that.model.cards = newCards;
+                        that.$("ul.form-column").empty(); 
+                        that.render();
+                        that.model.trigger("cards:reordered", newCards);
+
+                    }
+                });
+                this.delegateEvents();
+                return this;
+            },
+            /**
+             * fonction permet d'ajouter des cartes
+             */
+            onAddClick: function(event){
+                event.preventDefault();
+                this.model.addCardToList();
+                this.render();
+                
+                return false;
+            }
+        });
+
+        return CardOptionView;
+    });
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardStyleOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardStyleOptionView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardStyleOptionView.js	(révision 13132)
@@ -0,0 +1,138 @@
+define(
+    [
+        "jquery",
+        "underscore",
+        "text!../Templates/CardStyleOption.html",
+        "JEditor/Commons/Events",
+        "JEditor/Commons/Files/Models/FileGroup",
+        "JEditor/PagePanel/Contents/Options/Views/AbstractOptionView",
+        "JEditor/Commons/Utils"
+    ],
+    function($, _, cardStyleOption, Events, FileGroup, AbstractOptionView, Utils) {
+        /**
+         * Options de la grille
+         * @class GridOptionView
+         * @extends AbstractOptionView
+         */
+        var CardStyleOptionView = AbstractOptionView.extend({
+            optionType: 'cardStyleOption',
+            tagName: "div",
+            className: "gallery-template-option galleryStyle panel-content",
+            events: {
+                'click .stylleDeCard div .effect-radio'   : '_onChangeStylleImage',
+                'slidechange .card-nbreImage .slider'   :   '_onSliderChangeNbreCard',
+                'change input[type="radio"].select-box': '_onStyleAffichageChange',
+                'click input[type=radio].card-arrow':'_onChangeRadio',
+                'change input[type="checkbox"].blue-bg': '_onChangeAutoplay',
+                'slidechange .defilement-cran .slider': 'onSliderChangeDefilement',
+            },
+            initialize: function() {
+                this._super();
+                this.template = this.buildTemplate(cardStyleOption, this.translate);
+            },
+            render: function() {
+                this.undelegateEvents();
+                var templateVars = {
+                    cardStyle:this.model.cardStyle,
+                    cardNbreCard:this.model.cardNbreCard,
+                    cardStyleAff:this.model.cardStyleAff,
+                    Arrow:this.model.Arrow,
+                    Autoplay:this.model.Autoplay,
+                    Duration:this.model.Duration
+                };
+                this.$el.html(this.template(templateVars));
+                this.dom[this.cid].autoplay = this.$('.defilement');
+                this.dom[this.cid].cran = this.$('.defilement-cran');
+                if (this.model.cardStyle == 1) {
+                    $('#nav-option').addClass('hidden');
+                } else {
+                    $('#nav-option').removeClass('hidden');
+                    this._showHide();
+                }
+                var slider = this.$('.defilement-cran .slider');
+                slider.slider({
+                    min: 3000,
+                    max: 6000,
+                    step: 1000,
+                    value:this.model.Duration,
+                    range:"min"
+                });
+                this.dom[this.cid].slider = slider;
+                this.scrollables({
+                    advanced:{ autoScrollOnFocus: false }
+                });
+                this.delegateEvents();
+                this.$('.card-nbreImage .slider').slider({
+                    range: "min",
+                    value: this.model.cardNbreCard,
+                    min: 1,
+                    max: 5,
+                    step: 1
+                });
+
+                return this;
+            },
+            _onChangeStylleImage : function(event){
+                this.$(".effect-radio").removeClass("active");
+                var $target = $(event.currentTarget);
+                $target.addClass("active");
+                var value = parseInt($target.attr("data-value"));
+                if (value == 1) {
+                    $('#nav-option').addClass('hidden');
+                    $('.defilement').addClass('hidden');
+                    $('.defilement-cran').addClass('hidden');
+                } else {
+                    $('#nav-option').removeClass('hidden');
+                    if (this.model.Arrow == 2) {
+                        this._showHide(); 
+                    }
+                }
+                this.model.cardStyle=value;
+
+                this.render();
+            },
+            /**
+            * Slider change
+            */
+            _onSliderChangeNbreCard: function(event, ui){
+                var value = ui.value;
+                this.model.cardNbreCard=value;
+
+                return false;
+            },
+            _onStyleAffichageChange :function(event){
+                var $target = $(event.currentTarget);
+                this.model.cardStyleAff = $target.val();
+            },
+            _onChangeRadio: function(event){
+                var value = event.currentTarget.value;
+                if (this.model.Arrow == value ) {
+                    this.model.Arrow = 0;
+                    event.currentTarget.removeAttribute("checked");
+                }
+                else this.model.Arrow = value;
+                this._showHide(); 
+            },
+            _showHide :function(){
+                this.dom[this.cid].autoplay.addClass('hidden');
+                this.dom[this.cid].cran.addClass('hidden');
+                if (this.model.Arrow == 2 ) {
+                    this.dom[this.cid].autoplay.removeClass('hidden');
+                    if (this.model.Autoplay) {
+                        this.dom[this.cid].cran.removeClass('hidden');
+                    }
+                }
+            },
+            _onChangeAutoplay: function(){
+                this.model.Autoplay = !this.model.Autoplay;
+                this._showHide();       
+            },
+            onSliderChangeDefilement: function(event, ui) {
+                var value = ui.value;
+                this.model.Duration=value;
+                
+                return false;
+            }, 
+        });
+        return CardStyleOptionView;
+    });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/SelectIconView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/SelectIconView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/SelectIconView.js	(révision 13132)
@@ -0,0 +1,90 @@
+define([
+    "jquery",
+    "underscore",
+    "text!../Templates/selectIcon.html",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/DialogView",
+    "JEditor/ParamsPanel/Models/Params",
+    "JEditor/PagePanel/Contents/Blocks/CardBlock/Models/SvgCollection",
+    "i18n!../nls/i18n",
+    //not in params
+    "jqueryPlugins/dropdown"
+], function($, _, selectIcon, Events, DialogView,SvgCollection, translate) {
+    var SelectIconView = DialogView.extend({
+        className: 'selectIcon',
+        events: {
+            'click .box-icon': 'onSelect',
+            // 'click [data-select]': '_onSelectClick',
+        },
+        currentList: [],
+        constructor: function(options) {
+            var opts = _.extend({
+                title: translate("browseIconTitle"),
+                buttons: [
+                    {
+                        text: translate("choose"),
+                        class: 'okay',
+                        click: _.bind(this.onOk, this)
+                    },
+                    {
+                        text: translate("cancel"),
+                        class: 'cancel',
+                        click: _.bind(this.onCancel, this)
+                    }
+                ],
+                width: 720,
+                height: 600
+            }, options);
+            return DialogView.call(this, opts);
+        },
+        initialize: function(options) {
+            this._super();
+            this._template = this.buildTemplate(selectIcon, translate);
+
+        },
+        getIcons: function(usedIcon){
+            var svgCollectionObj = new SvgCollection();
+            svgCollectionObj.fetchIcons(_.bind(function(error, svgs) {
+                if (error) {
+                    console.error(error);
+                } else {
+                    this.svgCollection = svgs;
+                    this.selected = (usedIcon !== '' ? usedIcon : '');
+                    currentList = this.svgCollection;
+                    this.trigger('data:fetched');
+                }
+            },this));
+
+            this.listenTo(this, 'data:fetched', this.render);
+        },
+        render: function() {
+           
+            var data = (this.svgCollection === undefined ? [] : this.svgCollection);
+            this._super();
+            this.undelegateEvents();
+             this.$el.html(this._template({content:data, selected: this.selected}));
+            this.delegateEvents();
+            return this;
+        },
+        onCancel: function() {
+            $('.box-icon').css("background-color", "transparent");
+            this.$el.dialog('close');
+        },
+        onOk: function() {
+            var selectedIcon = this.selected;
+            var svgObj = this.svgCollection.filter(function(item) {
+                return item.name === selectedIcon;
+             })[0];
+            this.trigger(Events.ButtonEvents.SELECT_ICON, svgObj );
+            this.$el.dialog('close');
+        },
+        onSelect: function(e){
+            $('.box-icon').css("background-color", "transparent");
+            var $target = $(e.currentTarget);
+            $target.css('background-color', '#41ffbe');
+            var name = $target.data('name');
+            this.selected = name;
+        }
+    });
+    return SelectIconView;
+});
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/main.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/main.js	(révision 13132)
@@ -0,0 +1,11 @@
+// this file has been auto-generated by a grunt task, it will be overriden, do not modify it
+define(["./CardBlockView", "./CardOptionView", "./CardListView", "./CardStyleOptionView", "./SelectIconView"],function(CardBlockView, CardOptionView, CardListView, CardStyleOptionView, SelectIconView){
+    var comp={
+        "CardBlockView":CardBlockView,
+        "CardOptionView":CardOptionView,
+        "CardListView":CardListView,
+        "CardStyleOptionView": CardStyleOptionView,
+        "SelectIconView": SelectIconView
+    };
+    return comp;
+});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/main.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/main.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/main.js	(révision 13132)
@@ -0,0 +1,7 @@
+// this file has been auto-generated by a grunt task, it will be overriden, do not modify it
+define(["./CardBlock","./Views/main","./Models/main","i18n!./nls/i18n"],function(CardBlock,Views,Models,i18n){
+    CardBlock.Models=Models;
+    CardBlock.Views=Views;
+    CardBlock.i18n=i18n;
+    return CardBlock;
+});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/main.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/de-de/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/de-de/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/de-de/i18n.js	(révision 13132)
@@ -0,0 +1 @@
+define({});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/de-de/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/en-au/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/en-au/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/en-au/i18n.js	(révision 13132)
@@ -0,0 +1,63 @@
+define(
+    {
+        "BLOCK_NAME": "Card",
+        "cardBlockOption": "Card options",
+        "block": "Block",
+        "move": "Move",
+        "edit": "Edit",
+        "delete": "Delete",
+        "cardOption": "Card",
+        "cardStyleOption": "Style",
+        "addCard": "Add a card",
+        "description": "Description",
+        "title": "Title",
+        "cancel":"Cancel",
+        "save":"Save",
+        "cardFieldOptionsMessage":"Bullet points",
+        "options":"Enter one bullet point per line",
+        "showIconLegend": "Show an icon on the card",
+        "browseIconsLegend":"Browse icons",
+        "showButtonLegend": "Show a button",
+        "addButton": "Button text",
+        "styleDeGrille" : "Grid Style",
+        "cardStyleLegend" : "Apply a grid style",
+        "masonryLegend"         :   "Carousel",
+        "gridLegend"            :   "Grid",
+        "cardNombreCard"       :   "Number of cards",
+        "cardNombreCardLegend" :   "Drag to adjust the number of cards displayed",
+        "cardStyleAffichage"    :   "Style of the cards",
+        "cardStyleAffichageDesc":   "Apply a style to the cards",
+        "cardStyleOption"       :"Style",
+        "DescStyle1"           :  "Classic", 
+        "DescStyle2"           :  "Framed title",
+        "DescStyle3"           :  "Framed action",
+        "DescStyle4"           :  "Centered icon and title",
+        "DescStyle5"           :  "Gradient background",
+        "DescStyle6"           :  "Text next to image",
+        "arrowImages"            :   "Navigation choices",
+        "showArrow1"            :   "Classic navigation arrows",
+        "showArrow2"            :   "Advanced navigation with *active* indicators",
+        "autoPlay"              :  "Automatic scrolling",
+        "activeAutoPlay"        :  "Activate automatic scrolling",
+        "duration"              :  "Slide to adjust delay",
+        "colorButton":"Button color",
+        "colorButtonLegend":"Apply a color to the button",
+        "sizeButton": "Button size",
+        "pastel":"Pastel",
+        "vibrante":"Vibrant",
+        "contour":"Contour",
+        "pastel-lead":"Pastel Lead",
+        "vibrante-lead":"Vibrant Lead",
+        "contour-lead":"Outline Lead",
+        "sizeButtonLegend": "Select the size of your button here.",
+        "alignButton": "Button alignment",
+        "alignButtonLegend": "Select the alignment of your button on the page",
+        "leftAlignButton": "Left-aligned",
+        "centerAlignButton": "Center",
+        "rightAlignButton": "Right-aligned",
+        "justifyAlignButton": "Justified",
+        "alignText": "Text alignment",
+        "alignTextLegend": "Select the alignment of your text in the button.",
+        "button": "Button"
+    }
+);
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/en-au/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/en-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/en-ca/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/en-ca/i18n.js	(révision 13132)
@@ -0,0 +1,63 @@
+define(
+    {
+        "BLOCK_NAME": "Card",
+        "cardBlockOption": "Card options",
+        "block": "Block",
+        "move": "Move",
+        "edit": "Edit",
+        "delete": "Delete",
+        "cardOption": "Card",
+        "cardStyleOption": "Style",
+        "addCard": "Add a card",
+        "description": "Description",
+        "title": "Title",
+        "cancel":"Cancel",
+        "save":"Save",
+        "cardFieldOptionsMessage":"Bullet points",
+        "options":"Enter one bullet point per line",
+        "showIconLegend": "Show an icon on the card",
+        "browseIconsLegend":"Browse icons",
+        "showButtonLegend": "Show a button",
+        "addButton": "Button text",
+        "styleDeGrille" : "Grid Style",
+        "cardStyleLegend" : "Apply a grid style",
+        "masonryLegend"         :   "Carousel",
+        "gridLegend"            :   "Grid",
+        "cardNombreCard"       :   "Number of cards",
+        "cardNombreCardLegend" :   "Drag to adjust the number of cards displayed",
+        "cardStyleAffichage"    :   "Style of the cards",
+        "cardStyleAffichageDesc":   "Apply a style to the cards",
+        "cardStyleOption"       :"Style",
+        "DescStyle1"           :  "Classic", 
+        "DescStyle2"           :  "Framed title",
+        "DescStyle3"           :  "Framed action",
+        "DescStyle4"           :  "Centered icon and title",
+        "DescStyle5"           :  "Gradient background",
+        "DescStyle6"           :  "Text next to image",
+        "arrowImages"            :   "Navigation choices",
+        "showArrow1"            :   "Classic navigation arrows",
+        "showArrow2"            :   "Advanced navigation with *active* indicators",
+        "autoPlay"              :  "Automatic scrolling",
+        "activeAutoPlay"        :  "Activate automatic scrolling",
+        "duration"              :  "Slide to adjust delay",
+        "colorButton":"Button color",
+        "colorButtonLegend":"Apply a color to the button",
+        "sizeButton": "Button size",
+        "pastel":"Pastel",
+        "vibrante":"Vibrant",
+        "contour":"Contour",
+        "pastel-lead":"Pastel Lead",
+        "vibrante-lead":"Vibrant Lead",
+        "contour-lead":"Outline Lead",
+        "sizeButtonLegend": "Select the size of your button here.",
+        "alignButton": "Button alignment",
+        "alignButtonLegend": "Select the alignment of your button on the page",
+        "leftAlignButton": "Left-aligned",
+        "centerAlignButton": "Center",
+        "rightAlignButton": "Right-aligned",
+        "justifyAlignButton": "Justified",
+        "alignText": "Text alignment",
+        "alignTextLegend": "Select the alignment of your text in the button.",
+        "button": "Button"
+    }
+);
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/en-ca/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/es-es/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/es-es/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/es-es/i18n.js	(révision 13132)
@@ -0,0 +1 @@
+define({});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/es-es/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/i18n.js	(révision 13132)
@@ -0,0 +1,8 @@
+define(
+    {
+        "fr-fr": true,
+        "fr-ca": true,
+        "en-au": true,
+        "en-ca": true
+    }
+)
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json	(révision 13131)
+++ src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json	(révision 13132)
@@ -20,5 +20,6 @@
  "GalerieBlock":"GalerieBlock",
  "CompareBlock":"CompareBlock",
  "LoopBlock":"LoopBlock",
- "Slideshow":"Slideshow"
+ "Slideshow":"Slideshow",
+ "CardBlock":"CardBlock"
 }
Index: src/js/build.js
===================================================================
--- src/js/build.js	(révision 13131)
+++ src/js/build.js	(révision 13132)
@@ -65,7 +65,8 @@
         "JEditor/PagePanel/Contents/Blocks/TwitterTimelineBlock",
         "JEditor/PagePanel/Contents/Blocks/EvaluationBlock",
 	    "JEditor/PagePanel/Contents/Zones/Versions",
-        "JEditor/App/routes"
+        "JEditor/App/routes",
+        "JEditor/PagePanel/Contents/Blocks/CardBlock"
     ],
     shim: {
         'Modernizr': {
Index: src/js/config.js
===================================================================
--- src/js/config.js	(révision 13131)
+++ src/js/config.js	(révision 13132)
@@ -52,6 +52,7 @@
         "JEditor/PagePanel/Contents/Blocks/GridBlock",
         "JEditor/PagePanel/Contents/Blocks/NewsletterBlock",
         "JEditor/PagePanel/Contents/Blocks/TableBlock",
+        "JEditor/PagePanel/Contents/Blocks/CardBlock",
         "JEditor/App/routes"
     ],
     shim: {
Index: src/js/main.js
===================================================================
--- src/js/main.js	(révision 13131)
+++ src/js/main.js	(révision 13132)
@@ -69,7 +69,8 @@
     "JEditor/PagePanel/Contents/Blocks/TwitterTimelineBlock",
     "JEditor/PagePanel/Contents/Blocks/EvaluationBlock",
     "JEditor/PagePanel/Contents/Zones/Versions",
-    "JEditor/App/routes"
+    "JEditor/App/routes",
+    "JEditor/PagePanel/Contents/Blocks/CardBlock"
   ],
   shim: {
     "Modernizr": {
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardListView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardListView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardListView.js	(révision 13132)
@@ -0,0 +1,335 @@
+define(
+    [
+        "jquery",
+        "underscore",
+        "JEditor/Commons/Ancestors/Views/View",
+        "text!../Templates/CardList.html",
+        "JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/SelectIconView",
+        "JEditor/PagePanel/Contents/Blocks/CardBlock/Models/SvgCollection",
+        "JEditor/Commons/Events",
+        "JEditor/Commons/Links/Views/LinkView",
+        "collection!JEditor/Commons/Pages/Models/PageCollection",
+        "i18n!../nls/i18n"
+    ],
+    function ($, _, View, template, SelectIconView, SvgCollection,Events, LinkView, PageCollection, i18n) {
+        var CardListView = View.extend({
+            tagName:'li',
+            className:'form-element',
+            _existeIcon: false,
+            //_existeBtn: false,
+            events: {
+                "click .icon-edit": "edit",
+                "click .button.cancel":"cancel",
+                "click .btn-browse-icons": "_onClickBrowseIcons",
+                "click  .switch.card-icon": "_onClickSwitchIcon",
+                "click  .switch.card-button": "_onClickSwitchButton",
+                "click .button.save": "save",
+                "click .delete-card": "onDeleteClick",
+                'click .card-template-option-color .effect-radio': "_onChangeColor",
+                "click input[name='buttonAlignment']:checked" : "cancelButtonAligment",
+                "click input[name='size']:checked" : "actionButtonSize",
+                "click input[name='textAlignment']:checked" : "actionTextButtonAligment"
+            },
+            initialize:function () {
+                View.prototype.initialize.call(this);
+                this.template=this.buildTemplate(template, i18n);
+                this.selectIconDialog = new SelectIconView();
+                this.listenTo(this.selectIconDialog, Events.ButtonEvents.SELECT_ICON, this.onSelectedIcon);
+                this._existeIcon = (this.model.icon !== '') ? true : false;
+                //this._existeBtn = (this.model.buttonText !== '') ? true : false;
+                this._linkSelectorView = new LinkView({
+                    model: this.model.link,
+                    pageCollection: PageCollection.getInstance()
+                });
+                this.listenTo(this.model,"change:buttonAlignment",this.toggleTextAlignment);
+            },  
+            render:function () {
+                if (!this.model) {
+                    return this;
+                }
+                var templateVars = {
+                    index: this.model.index, 
+                    icon: this.model.icon,
+                    title: this.model.title,
+                    description:this.model.description,
+                    options: this.model.options,
+                    buttonText: this.model.buttonText,
+                    color: this.model.color,
+                    size: this.model.size,
+                    buttonAlignment: this.model.buttonAlignment,
+                    textAlignment: this.model.textAlignment,
+                    id: this.model.cid
+                };
+                this.$el.html(this.template(templateVars ));
+                
+                // Fonction pour appliquer le style à l'élément correspondant à un input radio coché
+                function styleCheckedRadio(inputName) {
+                    var checkedRadio = this.$("input[name='" + inputName + "']:checked");
+                    var labelTop = checkedRadio.next('.inline-block-label__top');
+
+                    if (labelTop.length) {
+                        labelTop.css({
+                            'background-color': '#34d399',
+                            'box-shadow': '0px 0px 0px 2px #0b2428, 0px 0px 0px 5px #34d399',
+                            'border-radius': '4px'
+                        });
+                    }
+                }
+                styleCheckedRadio.call(this, 'size');
+                styleCheckedRadio.call(this, 'buttonAlignment');
+                styleCheckedRadio.call(this, 'textAlignment');
+
+                this.actionBtn = this.$('.action-btn');
+                this.actionBtn.append(this._linkSelectorView.render({model:this.model.link}).el);
+                this.selectedIconEl = this.$('.selected-icon-wrapper');
+                this.btnBrowseIconsEl = this.$('.btn-browse-icons');
+                this.templateCardOption = this.$('.card-template-option');
+                this.templateCardOptionColor = this.$('.card-template-option-color');
+                this.templateCardOptionSize = this.$('.card-template-option-size');
+                this.templateCardOptionButtonAlignment = this.$('.card-template-option-alignement');
+                this.templateCardOptionButtonTextAlignment = this.$('.card-template-option-text-alignement');
+                this.switchIcon = this.$(".card-icon");
+                this.switchBtn = this.$(".card-button");
+                
+                //cacher au chargement
+                this.selectedIconEl.hide();
+                this.btnBrowseIconsEl.hide(); 
+                this.templateCardOption.hide();
+                this.templateCardOptionColor.hide();
+                this.templateCardOptionSize.hide();
+                this.templateCardOptionButtonAlignment.hide();
+                this.templateCardOptionButtonTextAlignment.hide();
+                if(this.model.icon !== ""){
+                    this.getIconContent(this.model.icon);
+                } else {
+                    this.switchIcon.css('background-color', '#4b4b4b');
+                    this.switchIcon.find('span').css('float', 'left');
+                }
+                this._onChangeShowParams();
+                this.scrollables();
+
+                return this;
+            },
+            edit:function (event) {
+                event.preventDefault();
+                var $editPart = this.$('.edit-part').filter('[data-edit-id="' + this.model.index + '"]');
+                if ($editPart.hasClass('editing')) {
+                    this.cancel(event);
+                } else {
+                    this.$('.edit-part').addClass('hidden').removeClass('editing');
+                    $editPart.removeClass('hidden').addClass('editing');
+                }
+            },
+            optionsToArray:function (options) {
+                return options.split("\n");
+            },
+            save:function (event) {
+                event = event || {};
+                event.preventDefault = event.preventDefault || function() {};
+                var $clickedButton = $(event.currentTarget); 
+                var $parent = $clickedButton.closest('[data-edit-id]');
+                var editId = $parent.data('edit-id');
+                var $editPart = this.$('.edit-part').filter('[data-edit-id="' + editId + '"]');
+                // Récupérer le titre
+                var newTitle = $editPart.find('input[name="label"]').val();
+                this.model.title = newTitle;
+
+                // Récupérer la description
+                var newDescription = $editPart.find('textarea[name="description"]').val();
+                this.model.description = newDescription;
+
+                // Récupérer les listes
+                var liste = $editPart.find('textarea[name="options"]').val();
+                var optionsArray = this.optionsToArray(liste);
+                this.model.options = optionsArray;
+
+                // Récupérer les text du bouton
+                var texButton = $editPart.find('input[name="text"]').val();
+                this.model.buttonText = (this.model.existeBtn != false) ? texButton : '';
+                
+                var updatedAttributes = {
+                    title: newTitle,
+                    description: newDescription,
+                    options: optionsArray,
+                    buttonText: texButton
+                };
+                this.model.cards.updateCardToList(this.model, updatedAttributes);
+                
+                var $cardToUpdate = this.$('.show-part').filter('[data-edit-id="' + editId + '"]');
+                $cardToUpdate.find('.text.field-name').text(newTitle || this.translate('card'));
+
+                $editPart.addClass('hidden').removeClass('editing');
+
+                this.render();
+            },
+            cancel:function (event) {
+                event = event || {};
+                event.preventDefault = event.preventDefault || function() {};
+                var $clickedButton = $(event.currentTarget); 
+                var $parent = $clickedButton.closest('[data-edit-id]');
+                var editId = $parent.data('edit-id');
+                var $editPart = this.$('.edit-part').filter('[data-edit-id="' + editId + '"]');
+                $editPart.addClass('hidden').removeClass('editing');
+            },
+            /**
+             * trigger le modal de selection d'iĉone à utiliser
+             *  
+             */
+            _onClickBrowseIcons:function(){
+                var usedIcon = (this.model.icon !== '' ? this.model.icon: '');
+                this.selectIconDialog.getIcons(usedIcon);
+                this.selectIconDialog.open();
+            },
+            /**
+             * Récupérer l'icone
+             */
+            onSelectedIcon:function(event){
+                this.selectedIconEl.empty();
+                this.selectedIconEl.append(event.content);
+                this.model.icon = event.name;
+            },
+            getIconContent:function(svgName){
+                var svgCollectionObj = new SvgCollection({"svgName":svgName});
+                svgCollectionObj.fetchIcons(_.bind(function(error, svg) {
+                    if (error) {
+                        console.error(error);
+                    } else {
+                        this.selectedIconEl.html(svg.content);
+                        this.selectedIconEl.show();
+                        this.btnBrowseIconsEl.show();
+                        this.switchIcon.css('background-color', '#34d399');
+                        this.switchIcon.find('span').css('float', 'right'); 
+                    }
+                },this));
+    
+            },
+            _onClickSwitchIcon: function() {
+                this._existeIcon = !this._existeIcon;
+                this._onChangeShowIcon();
+
+                return false;
+            },
+            /**
+             * afficher ou pas le bouton parcourir les icones
+             * 
+             */
+            _onChangeShowIcon : function (){
+                if (this._existeIcon) {
+                    this.switchIcon.css('background-color', '#34d399');
+                    this.switchIcon.find('span').css('float', 'right');
+                    this.btnBrowseIconsEl.show(); 
+                    this.selectedIconEl.show();
+                }
+                else {
+                    this.switchIcon.css('background-color', '#4b4b4b');
+                    this.switchIcon.find('span').css('float', 'left');
+                    this.btnBrowseIconsEl.hide();
+                    this.selectedIconEl.hide();
+                    this.selectedIconEl.empty();
+                    this.model.icon = "";
+                }
+            },
+            _onClickSwitchButton: function() {
+                this.model.existeBtn = !this.model.existeBtn;
+                this._onChangeShowParams();
+
+                return false;
+            },
+            /**
+             * afficher ou pas l'infos du bouton
+             */
+            _onChangeShowParams : function (){
+                if (this.model.existeBtn) {
+                    this.switchBtn.css('background-color', '#34d399');
+                    this.switchBtn.find('span').css('float', 'right');
+                    this.templateCardOption.show();
+                    this.templateCardOptionColor.show();
+                    this.templateCardOptionSize.show();
+                    this.templateCardOptionButtonAlignment.show();
+                    this.templateCardOptionButtonTextAlignment.show();
+                }
+                else {
+                    this.switchBtn.css('background-color', '#4b4b4b');
+                    this.switchBtn.find('span').css('float', 'left');
+                    this.templateCardOption.hide();
+                    this.templateCardOptionColor.hide();
+                    this.templateCardOptionSize.hide();
+                    this.templateCardOptionButtonAlignment.hide();
+                    this.templateCardOptionButtonTextAlignment.hide();
+                    this.$('.text-btn').val('');
+                    this.model.buttonText = "";
+                }
+            },
+            /**
+             * fonction permet de supprimer une carte
+             */
+            onDeleteClick: function(event){
+                event.preventDefault();
+                this.model.cards.removeCardToList(this.model);
+                this.render();
+            },
+            /**
+             * action changer le color du boutton
+             * @param {*} event 
+             */
+            _onChangeColor:function(event){
+                this.$(".effect-radio").removeClass("active");
+                var $target = $(event.currentTarget);
+                $target.addClass("active");
+                var value = $target.attr("data-value");
+                this.model.color=value;
+             },
+              /**
+             * annuler l'alignement du bouton
+             */
+            cancelButtonAligment:function (event) {
+                var $target = $(event.currentTarget);
+                if ($target.attr("value") == this.model.buttonAlignment) {
+                    this.model.buttonAlignment = "";
+                    $target.removeAttr('checked');
+                } else {
+                    this.$("input[name='buttonAlignment']").removeAttr('checked');
+                    this.$("input[name='buttonAlignment']").next('.inline-block-label__top').css({
+                        'background-color': '',
+                        'box-shadow': '',
+                        'border-radius': ''
+                    });
+                    this.model.buttonAlignment = $target.attr("value");
+                }
+            },
+            actionButtonSize:function (event) {
+                var $target = $(event.currentTarget);
+                if ($target.attr("value") != this.model.size) {
+                    this.$("input[name='size']").removeAttr('checked');
+                    this.$("input[name='size']").next('.inline-block-label__top').css({
+                        'background-color': '',
+                        'box-shadow': '',
+                        'border-radius': ''
+                    });
+                    this.model.size = $target.attr("value");
+                }
+            },
+            /**
+             * affichage de l'alignement du texte
+             */
+            toggleTextAlignment:function () {
+                this.$(".button-textAlignment").toggleClass("hidden",this.model.buttonAlignment != "full-width");
+                this.$el.mCustomScrollbar('update');
+            },
+            actionTextButtonAligment: function (event) {
+                var $target = $(event.currentTarget);
+                if ($target.attr("value") != this.model.textAlignment) {
+                    this.$("input[name='textAlignment']").removeAttr('checked');
+                    this.$("input[name='textAlignment']").next('.inline-block-label__top').css({
+                        'background-color': '',
+                        'box-shadow': '',
+                        'border-radius': ''
+                    });
+                    this.model.textAlignment = $target.attr("value");
+                }
+            }
+        });
+
+        return CardListView;
+    }
+);

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardListView.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/fr-ca/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/fr-ca/i18n.js	(révision 13132)
@@ -0,0 +1,67 @@
+define(
+    {
+        "BLOCK_NAME": "Carte",
+        "cardBlockOption": "Options du carte",
+        "block": "Bloc",
+        "move": "Déplacer",
+        "edit": "Éditer",
+        "delete": "Supprimer",
+        "cardOption": "Carte",
+        "cardStyleOption": "Style",
+        "addCard": "Ajouter une carte",
+        "description": "Description",
+        "title": "Titre",
+        "cancel":"Annuler",
+        "save":"Sauvegarder",
+        "cardFieldOptionsMessage":"Bullet points",
+        "options":"Renseignez un bullet point par ligne",
+        "showIconLegend": "Afficher une icône sur la carte",
+        "browseIconsLegend":"Parcourir les icônes",
+        "showButtonLegend": "Afficher un bouton",
+        "addButton": "Texte du bouton",
+        "styleDeGrille" : "Style de grille",
+        "cardStyleLegend" : "Appliquez un style de grille",
+        "masonryLegend"         :   "Carousel",
+        "gridLegend"            :   "Grille",
+        "cardNombreCard"       :   "Nombre de cartes",
+        "cardNombreCardLegend" :   "Glissez pour ajuster le nombre de carte affichées",
+        "cardStyleAffichage"    :   "Style des cartes",
+        "cardStyleAffichageDesc":   "Appliquez un style aux cartes",
+        "cardStyleOption"       :"Style",
+        "DescStyle1"           :  "Classique", 
+        "DescStyle2"           :  "Titre encadré",
+        "DescStyle3"           :  "Action encadrée",
+        "DescStyle4"           :  "Icon et titre centrés",
+        "DescStyle5"           :  "Fond dégradé",
+        "DescStyle6"           :  "Texte à côté de l'image",
+        "arrowImages"            :   "Choix navigation",
+        "showArrow1"            :   "Flèches de navigation classique",
+        "showArrow2"            :   "Navigation avancée avec indicateurs *actif*",
+        "autoPlay"              :  "Défilement automatique",
+        "activeAutoPlay"        :  "Activer le défilement automatique",
+        "duration"              :  "Glissez pour ajuster le delais",
+        "colorButton":"Couleur du bouton",
+        "colorButtonLegend":"Appliquez une coleur au bouton",
+        "sizeButton": "Taille du bouton",
+        "pastel":"Pastel",
+        "vibrante":"Vibrante",
+        "contour":"Contour",
+        "pastel-lead":"Pastel Lead",
+        "vibrante-lead":"Vibrante Lead",
+        "contour-lead":"Contour Lead",
+        "sizeButton": "Taille du bouton",
+        "sizeButtonLegend": "Sélectionnez la taille de votre bouton ici",
+        "alignButton": "Alignement du bouton",
+        "alignButtonLegend": "Sélectionnez l'alignement du bouton dans la page",
+        "leftAlignButton": "Aligné à gauche",
+        "centerAlignButton": "Centré",
+        "rightAlignButton": "Aligné à droite",
+        "justifyAlignButton": "Justifié",
+        "alignText": "Alignement du texte",
+        "alignTextLegend": "Sélectionnez l'alignement du texte dans le bouton",
+        "leftAlignText": "Aligné à gauche",
+        "centerAlignText": "Centré",
+        "rightAlignText": "Aligné à droite",
+        "button": "Bouton"
+    }
+);
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/fr-fr/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/fr-fr/i18n.js	(révision 13132)
@@ -0,0 +1,67 @@
+define(
+    {
+        "BLOCK_NAME": "Carte",
+        "cardBlockOption": "Options du carte",
+        "block": "Bloc",
+        "move": "Déplacer",
+        "edit": "Éditer",
+        "delete": "Supprimer",
+        "cardOption": "Carte",
+        "cardStyleOption": "Style",
+        "addCard": "Ajouter une carte",
+        "description": "Description",
+        "title": "Titre",
+        "cancel":"Annuler",
+        "save":"Sauvegarder",
+        "cardFieldOptionsMessage":"Bullet points",
+        "options":"Renseignez un bullet point par ligne",
+        "showIconLegend": "Afficher une icône sur la carte",
+        "browseIconsLegend":"Parcourir les icônes",
+        "showButtonLegend": "Afficher un bouton",
+        "addButton": "Texte du bouton",
+        "styleDeGrille" : "Style de grille",
+        "cardStyleLegend" : "Appliquez un style de grille",
+        "masonryLegend"         :   "Carousel",
+        "gridLegend"            :   "Grille",
+        "cardNombreCard"       :   "Nombre de cartes",
+        "cardNombreCardLegend" :   "Glissez pour ajuster le nombre de carte affichées",
+        "cardStyleAffichage"    :   "Style des cartes",
+        "cardStyleAffichageDesc":   "Appliquez un style aux cartes",
+        "cardStyleOption"       :"Style",
+        "DescStyle1"           :  "Classique", 
+        "DescStyle2"           :  "Titre encadré",
+        "DescStyle3"           :  "Action encadrée",
+        "DescStyle4"           :  "Icon et titre centrés",
+        "DescStyle5"           :  "Fond dégradé",
+        "DescStyle6"           :  "Texte à côté de l'image",
+        "arrowImages"            :   "Choix navigation",
+        "showArrow1"            :   "Flèches de navigation classique",
+        "showArrow2"            :   "Navigation avancée avec indicateurs *actif*",
+        "autoPlay"              :  "Défilement automatique",
+        "activeAutoPlay"        :  "Activer le défilement automatique",
+        "duration"              :  "Glissez pour ajuster le delais",
+        "colorButton":"Couleur du bouton",
+        "colorButtonLegend":"Appliquez une coleur au bouton",
+        "sizeButton": "Taille du bouton",
+        "pastel":"Pastel",
+        "vibrante":"Vibrante",
+        "contour":"Contour",
+        "pastel-lead":"Pastel Lead",
+        "vibrante-lead":"Vibrante Lead",
+        "contour-lead":"Contour Lead",
+        "sizeButton": "Taille du bouton",
+        "sizeButtonLegend": "Sélectionnez la taille de votre bouton ici",
+        "alignButton": "Alignement du bouton",
+        "alignButtonLegend": "Sélectionnez l'alignement du bouton dans la page",
+        "leftAlignButton": "Aligné à gauche",
+        "centerAlignButton": "Centré",
+        "rightAlignButton": "Aligné à droite",
+        "justifyAlignButton": "Justifié",
+        "alignText": "Alignement du texte",
+        "alignTextLegend": "Sélectionnez l'alignement du texte dans le bouton",
+        "leftAlignText": "Aligné à gauche",
+        "centerAlignText": "Centré",
+        "rightAlignText": "Aligné à droite",
+        "button": "Bouton"
+    }
+);
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/nls/fr-fr/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
