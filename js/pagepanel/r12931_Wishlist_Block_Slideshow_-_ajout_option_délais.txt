Revision: r12931
Date: 2024-08-29 15:40:29 +0300 (lkm 29 Aog 2024) 
Author: t<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
Wishlist : Block Slideshow - ajout option délais

## Files changed

## Full metadata
------------------------------------------------------------------------
r12931 | trajaonar<PERSON>lo | 2024-08-29 15:40:29 +0300 (lkm 29 Aog 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/dev/#librairies/module/GestionContenuUtil/config/module.config.php
   M /branches/ideo3_v2/dev/#librairies/module/GestionContenuUtil/src/GestionContenuUtil/Core/BlockModuleHandlers/BlockSlideshowHandler.php
   M /branches/ideo3_v2/dev/#librairies/module/GestionContenuUtil/src/GestionContenuUtil/Core/BlockModuleHandlers/template/slideshowTemplate.html
   M /branches/ideo3_v2/dev/#librairies/module/GestionContenuUtil/src/GestionContenuUtil/Core/HtmlTagHandlers/SlideshowTagHandler.php
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Models/SlideshowOption.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Templates/slideshowOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Views/SlideshowOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/i18n.js

Wishlist : Block Slideshow - ajout option délais
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Models/SlideshowOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Models/SlideshowOption.js	(révision 12930)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Models/SlideshowOption.js	(révision 12931)
@@ -26,6 +26,7 @@
                             title: 'slideshow', 
                             fileGroup: null, 
                             Info        :   0,
+                            Delay: 4
                         },
                         initialize: function() {
                             this._super();
@@ -53,7 +54,8 @@
                             return {
                                 fileGroup: fileGroup?fileGroup.id:null,
                                 Info : info,
-                                optionType: 'slideshow'
+                                optionType: 'slideshow',
+                                Delay :   this.Delay,
                             }
                         },
                         JSONClone:function(){
@@ -61,7 +63,7 @@
                         }
                     }
             );
-            SlideshowOption.SetAttributes(['fileGroup', 'Info']);
+            SlideshowOption.SetAttributes(['fileGroup', 'Info' , 'Delay' ]);
 
             return SlideshowOption;
         });
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Templates/slideshowOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Templates/slideshowOption.html	(révision 12930)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Templates/slideshowOption.html	(révision 12931)
@@ -52,6 +52,18 @@
               <div class="block-label-radio"><%= __("doNothing")%></div>
             </label>
         </div>
+
+        <div class="panel-option-container animated slideshow-delay">
+          <article class="panel-option slideshow-delay">
+              <header>
+                  <h3 class="option-name"><%= __("delayText")%></h3>
+              </header>
+              <div class="option-content">
+                  <div class="slider-container"><span class="icon-less"></span><div class="slider"></div><span class="icon-more"></span></div>
+              </div>
+          </article>
+      </div>
+
     </article>
 </div>
 <div class="panel-option-container animated gallery-color">
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Views/SlideshowOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Views/SlideshowOptionView.js	(révision 12930)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Views/SlideshowOptionView.js	(révision 12931)
@@ -21,6 +21,7 @@
         var SlideshowOptionView = AbstractOptionView.extend({
             optionType: 'slideshow',
             events: {
+                'slidechange .slider'   :   'onSliderChange',
                 'click .files-action'   :   'openFileGroupDialog',
                 'click input[type=radio]':'_onChangeRadio',
                 'change input[type="checkbox"].blue-bg': '_onChangeArrow'
@@ -77,6 +78,19 @@
               
                 this.$el.empty();
                 this.$el.html(this._template(this.model));
+
+                // slide 
+                var slider = this.$('.slideshow-delay .slider');
+                slider.slider({
+                                min: 1,
+                                max: 4,
+                                step: 1,
+                                value:this.model.Delay,
+                                range:"min"
+                            });
+                this.dom[this.cid].slider = slider;
+                // slide 
+
                 this.scrollables({
                     advanced:{ autoScrollOnFocus: false }
                 });
@@ -92,6 +106,11 @@
                     this.model.Info= event.currentTarget.value;
                 }
             },
+            onSliderChange: function(event,ui){
+                var value = ui.value;
+                this.model.Delay=value;
+                return false;
+             },
         });
         return SlideshowOptionView;
     });
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-ca/i18n.js	(révision 12930)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-ca/i18n.js	(révision 12931)
@@ -55,5 +55,6 @@
     "DescStyle2":  "Texte encadré sur l'image",
     "DescStyle3":  "Texte sur l'image",
     "DescStyle4":  "Textes sous l'image avec bordures",
-    "DescStyle5":  "Textes sous l'image avec images arrondies"  
+    "DescStyle5":  "Textes sous l'image avec images arrondies",
+    "delayText" : "Glissez pour ajuster le délais"  
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-fr/i18n.js	(révision 12930)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-fr/i18n.js	(révision 12931)
@@ -56,5 +56,6 @@
     "DescStyle3":  "Texte sur l'image, animé au survol",
     "DescStyle4":  "Texte sous l'image, bordures",
     "DescStyle5":  "Texte sous l'image, images arrondies (idéal images carrés)",             
-    "DescStyle6":  "Texte à côté de l'image"             
+    "DescStyle6":  "Texte à côté de l'image",
+    "delayText" : "Glissez pour ajuster le délais"             
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/i18n.js	(révision 12930)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/i18n.js	(révision 12931)
@@ -57,7 +57,8 @@
         "DescStyle3"           :  "Text on the image, animated on hover",
         "DescStyle4"           :  "Text under the image, borders",
         "DescStyle5"           :  "Text under the image, rounded pictures (ideal for square images)",      
-        "DescStyle6"           :  "Text next to the the image"      
+        "DescStyle6"           :  "Text next to the the image",
+        "delayText" : "Slide to adjust the delay"    
      },
      "fr-fr":true, 
      "fr-ca":true 
