Revision: r13514
Date: 2024-11-26 10:32:30 +0300 (tlt 26 Nov 2024) 
Author: frahajanirina 

## Commit message
Wishlist:IDEO3.2:fix texte à afficher sur le bouton dans l'admin

## Files changed

## Full metadata
------------------------------------------------------------------------
r13514 | frahajanirina | 2024-11-26 10:32:30 +0300 (tlt 26 Nov 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js

Wishlist:IDEO3.2:fix texte à afficher sur le bouton dans l'admin
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js	(révision 13513)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js	(révision 13514)
@@ -46,5 +46,5 @@
     "DifferentTextOnMobile": "Texte différent sur mobile ?",
     "errorWithoutTextOnMobile": "Vous n’avez pas indiqué le texte pour mobile. Vous devez indiquer le texte à afficher ou désactiver l’option.",
     "Invalid_text": "Texte invalide",
-    "btnTextOnMobileWarning": "Manque text altérnatif mobile"
+    "btnTextOnMobileWarning": "Manque texte alternatif mobile"
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js	(révision 13513)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js	(révision 13514)
@@ -46,6 +46,6 @@
     "DifferentTextOnMobile": "Texte différent sur mobile ?",
     "errorWithoutTextOnMobile": "Vous n’avez pas indiqué le texte pour mobile. Vous devez indiquer le texte à afficher ou désactiver l’option.",
     "Invalid_text": "Texte invalide",
-    "btnTextOnMobileWarning": "Manque text altérnatif mobile"
+    "btnTextOnMobileWarning": "Manque texte alternatif mobile"
 
 });
