Revision: r13601
Date: 2024-12-13 13:39:08 +0300 (zom 13 Des 2024) 
Author: rrakotoarinelina 

## Commit message
Wishlist IDEO3.2 : Revoir edition d'un block de texte / block HTML - correction retour 3

## Files changed

## Full metadata
------------------------------------------------------------------------
r13601 | rrakotoarinelina | 2024-12-13 13:39:08 +0300 (zom 13 Des 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js

Wishlist IDEO3.2 : Revoir edition d'un block de texte / block HTML - correction retour 3
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js	(révision 13600)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js	(révision 13601)
@@ -15,12 +15,14 @@
     var TextEditionView = DialogView.extend({
         className: 'textEdition',
         events: {
+            'dialogbeforeclose': 'beforeClose',
+            // 'dialogclose': 'beforeClose',
         },
         // events:  {'click .action.delete': "confirmDelete", 'keydown .content': '_onKeyPress', 'dblclick .content': 'avoidPropagation'},
         constructor: function(options) {
             this.textBlockView = options.textObject;
             this.editor = null;
-
+            this.isClosing = false;  // Initialize the flag here
             var opts = _.extend({
                 title: translate("textEdition"),
                 buttons: [
@@ -35,7 +37,7 @@
                         click: _.bind(this.onCancel, this)
                     }
                 ],
-                close: _.bind(this.onClose, this)
+                // close: _.bind(this.onClose, this),
             }, options);
 
             return DialogView.call(this, opts);
@@ -50,6 +52,7 @@
             this._template = this.buildTemplate(textEdition, translate);
             this.listenTo(this.textBlockView.model, Events.BackboneEvents.CHANGE + ':content', this.onContentChange);
             this.trigger(Events.LoadEvents.LOAD_START, this);
+
         },
         
         render: function() {
@@ -67,9 +70,22 @@
             this.initContent();
             DialogView.prototype.open.call(this);
         },
+        beforeClose: function(event, ui) {
+            event.preventDefault();
 
+            // Ajout et utilisation de flag pour eviter plusieurs vérification 
+            if (!this.isClosing) {
+                console.log('if');
+                this.isClosing = true;
+                this.handleConfirmationOperation(false, _.bind(function() {
+                    this.destroyEditorCloseModal();
+                    this.isClosing = false;
+                }, this));
+            }
+            return false;
+        },
+       
         initializeCKEditor: function() {
-            var bodyFontFamily = $('body').css('font-family');
             
             // Récupère la configuration CKEditor depuis textBlockView
             var editorConfig = _.extend({}, this.textBlockView.CKconfig, {
@@ -143,10 +159,6 @@
             event.editor.focus();
             this.trigger(Events.LoadEvents.LOAD_SUCCESS, this);
         },
-        open: function() {
-            this.initContent();
-            DialogView.prototype.open.call(this);
-        },
 
         onEditorChange: function() {
             // Gère les changements en temps réel dans l'éditeur
@@ -164,14 +176,12 @@
             if (this.textBlockView) {
                 this.textBlockView.model.set('content', this.originalContent);
             }
-            this.$el.dialog('close');
-            this.onClose();
+            this.destroyEditorCloseModal();
         },
 
         onOk: function() {
             this.updateModel();
-            this.$el.dialog('close');
-            this.onClose();
+            this.destroyEditorCloseModal();
         },
 
         updateModel : function() {
@@ -189,12 +199,12 @@
             }
         },
 
-        onClose: function() {
+        destroyEditorCloseModal: function() {
             if (this.editor) {
                 this.editor.destroy();
                 this.editor = null;
             }
-            
+            this.$el.dialog('close');
             this.remove();
         },
         updateOriginalContent: function() {
@@ -251,42 +261,54 @@
 
         },
         hasUnsavedChanges: function() {
-            var editorContent = this.editor.getData().trim();
-            var modelContent = this.textBlockView.model.get('content').trim();
-            var result = !(_.isEqual(editorContent, modelContent));
+            var result  = false;
+            if (this.editor && this.textBlockView) {
+                var editorContent = this.editor.getData().trim();
+                var modelContent = this.textBlockView.model.get('content').trim();
+                result = !(_.isEqual(editorContent, modelContent));
+            }
             return result;
         },
         showOptionPanel: function() {
-
+            this.handleConfirmationOperation(true);
+        },
+        confirmUnsaved: function(params) {
+            this.app.messageDelegate.set(new ConfirmUnsaved(params));
+        },
+        handleConfirmationOperation: function(showOptionPanel, callback) {
+            var self = this;
             if (this.textBlockView && this.hasUnsavedChanges()) {
                 this.confirmUnsaved({
-                    message : translate("quitWithoutSaving"),
-                    title : translate("unsavedChanges"),
-                    type : 'delete-not-saved',
-                    onYes : _.bind(function() {
+                    message: translate("quitWithoutSaving"),
+                    title: translate("unsavedChanges"),
+                    type: 'delete-not-saved',
+                    onYes: _.bind(function() {
                         this.updateModel();
-                        this.$el.dialog('close');
-                        return ContentView.prototype.edit.call(this.textBlockView);
+                        this.handleAfterConfirm(showOptionPanel, callback);
                     }, this),
-                    onNo : _.bind(function() {
-                        this.$el.dialog('close');
-                        return ContentView.prototype.edit.call(this.textBlockView);
+                    onNo: _.bind(function() {
+                        this.handleAfterConfirm(showOptionPanel, callback);
                     }, this),
-                    options : {
-                        dialogClass : 'delete no-close',
-                        dontAskAgain : true
+
+                    options: {
+                        dialogClass: 'delete no-close',
+                        dontAskAgain: true,
+                        onCancel: _.bind(function() {
+                            this.isClosing = false;
+                        }, this),
                     }
                 });
-            } 
-            else{
-                //fermeture du modal
-                this.$el.dialog('close');
+            } else {
+                this.handleAfterConfirm(showOptionPanel, callback);
+            }
+        },
+        handleAfterConfirm: function(showOptionPanel, callback) {
+            if(showOptionPanel) {
+                this.destroyEditorCloseModal();
                 return ContentView.prototype.edit.call(this.textBlockView);
             }
+            if (callback) callback();
         },
-        confirmUnsaved: function(params) {
-            this.app.messageDelegate.set(new ConfirmUnsaved(params));
-        }
 
     });
 
