Revision: r14234
Date: 2025-05-13 15:54:55 +0300 (tlt 13 Mey 2025) 
Author: rrakotoarinelina 

## Commit message
Whislist IDEO3.2 : Block Tableaux

## Files changed

## Full metadata
------------------------------------------------------------------------
r14234 | rrakotoarinelina | 2025-05-13 15:54:55 +0300 (tlt 13 Mey 2025) | 1 ligne
Chemins modifiés :
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/Templates/images
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/Templates/images/template2.png
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/Templates/tableEdition.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/Templates/table_templates.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/Views/TableBlockView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/Views/TableEditionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/nls/i18n.js
   A /branches/ideo3_v2/integration/src/less/imports/ckeditor_table_modeles.less
   M /branches/ideo3_v2/integration/src/less/main.less

Whislist IDEO3.2 : Block Tableaux
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/Templates/images/template2.png
===================================================================
Impossible d'afficher : fichier considéré comme binaire.
svn:mime-type = application/octet-stream

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/Templates/images/template2.png
___________________________________________________________________
Added: svn:mime-type
## -0,0 +1 ##
+application/octet-stream
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/Templates/tableEdition.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/Templates/tableEdition.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/Templates/tableEdition.html	(révision 14234)
@@ -0,0 +1,37 @@
+<style>
+.ui-dialog.tableEdition {
+  position: fixed !important;
+  width: 90vw !important;
+  max-width: 80em !important;
+  max-height: 90vh !important;
+  left: 50% !important;
+  transform: translateX(-50%) !important;
+  top: 10vh !important;
+  z-index: 1000000000 !important;
+  display: block;
+}
+.ui-dialog.tableEdition .ui-dialog-content {
+   height: 70vh !important;
+}
+.ui-dialog.tableEdition .cke_inner{
+    top: 0 !important;
+}
+.ui-dialog.tableEdition .cke_contents {
+    height: calc(70vh - 70px) !important;
+}
+
+select.cke_dialog_ui_input_select{
+    color: black !important;
+}
+
+.cke_dialog_ui_input_textarea{
+    color: black !important;
+}
+
+.ui-dialog.tableEdition .cke_button__blockstyles_label{
+    display: inline !important;
+}
+.cke_button .cke_button__source_label { display: inline-block !important; };
+</style>
+
+<div id="table-content"></div>
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/Templates/tableEdition.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/Templates/table_templates.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/Templates/table_templates.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/Templates/table_templates.js	(révision 14234)
@@ -0,0 +1,11 @@
+CKEDITOR.addTemplates('default', {
+    imagesPath: CKEDITOR.getUrl(__IDEO_INTEGRATION_PATH__ + '/src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/Templates/images/'),
+    templates: [
+        {
+            title: 'Tableau simple',
+            image: 'template2.png',
+            description: 'Tableau basique à deux colonnes',
+            html: '<table class="styled-table" border="1" cellpadding="5" cellspacing="0" style="width:100%"><thead><tr><th scope="col">Titre 1</th><th scope="col">Titre 2</th></tr></thead><tbody><tr><td>Contenu 1</td><td>Contenu 2</td></tr><tr><td>Contenu 3</td><td>Contenu 4</td></tr></tbody></table>'
+        }
+    ]
+});

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/Templates/table_templates.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/Views/TableBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/Views/TableBlockView.js	(révision 14233)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/Views/TableBlockView.js	(révision 14234)
@@ -8,8 +8,9 @@
     "JEditor/PagePanel/Contents/Blocks/Block/Block",
     "JEditor/PagePanel/Contents/Zones/Models/ZoneDependency",
     "ckeditor",
-    "i18n!../nls/i18n"
-], function($, _, block, Events, ContentView, BlockView, Block, ZoneDependency, CKEDITOR, translate) {
+    "i18n!../nls/i18n",
+    "./TableEditionView"
+], function($, _, block, Events, ContentView, BlockView, Block, ZoneDependency, CKEDITOR, translate, TableEditionView) {
     /**
      * Vue des Blocs de texte
      * @class TextBlockView
@@ -17,7 +18,7 @@
      * @property {Object} CKconfig Configuration de CKEditor
      *
      */
-    var TextBlockView = BlockView.extend(
+    var TableBlockView = BlockView.extend(
             /**
              * @lends TextBlockView.prototype
              */
@@ -25,37 +26,84 @@
                         tagname: "div",
                         attributes: {"class": "block", tabindex: 1},
                         events: _.extend({}, BlockView.prototype.events, {'click .action.delete': "confirmDelete", 'keydown .content': '_onKeyPress', 'dblclick .content': 'avoidPropagation'}),
+                        defaultToolbar: [
+                            {name: 'remove_formats', groups: ['format'], items: ["RemoveFormat"]},
+                            {name: 'styles', groups: ['styles'], items: ["Styles"] },
+                            {name: 'kikou', groups: ['basicstyles','cleanup'], items: ['Bold', 'Italic', 'Underline','Strike']},
+                            {name: 'links', groups: ['links'], items:['ideoLink', 'ideoUnlink', 'Anchor']},
+                            {name: 'paragraph', groups: ['align', 'bidi'], items: ["JustifyLeft", "JustifyCenter", "JustifyRight", "JustifyBlock"]},
+                            {name: 'list', groups: ['list'], items: ["NumberedList","BulletedList","-","Blockquote","Abbr","pre"]},
+                            {name: 'formats', groups: ['format'], items: ["Format","Font","FontSize"]},
+                            {name: 'ideo', groups: ['ideocolors'], items: ["ideoTextColor"]},
+                            {name: '_ideostyle', groups: ['ideostyle'], items: ['blockstyles']},
+                            {name: 'insert', items: ['Table']},
+                            {name: 'document', items: ['Templates']}
+                        ],
                         CKconfig: {
                             dialog_backgroundCoverColor: "#ffffff",
-                            toolbar: [
-                                {name: 'remove_formats', groups: ['format'], items: ["RemoveFormat"]},
-                                {name: 'kikou', groups: ['basicstyles','cleanup'], items: ['Bold', 'Italic', 'Underline','Strike']},
-                                {name:'links',items:['ideoLink', 'ideoUnlink']},
-                                {name: 'paragraph', groups: ['align', 'bidi'], items: ["JustifyLeft", "JustifyCenter", "JustifyRight", "JustifyBlock"]},
-                                {name: 'formats', groups: ['format'], items: ["Font","FontSize"]},
-                                {name: 'ideo', groups: ['ideocolors'], items: ["ideoTextColor"]},
-                                {name: '_ideostyle', groups: ['ideostyle'], items: ['blockstyles']},
-                                { name: 'insert', items : [ 'Table'] }
+                            extraPlugins: 'abbr,pre,ideolink,ideostyler,sourcedialog,templates,ideocolorbutton,ideocolorpicker,blockquote,removeformat,table,tabletools,tableresize,font',
+                            skin: 'ideo',
+                            fontSize_sizes: 'tiny/0.75em;small/0.875em;large/1.25em;xlarge/1.5em;xxlarge/2em;xxxlarge/2.5em',
+                            contentsCss: [
+                                __IDEO_CSS_PATH__ + 'ideo3-back.css',
+                                '.cke_editable blockquote { background: #eee; border-left: 10px solid #ccc; padding: 15px; color : rgb(204, 204, 204)}',
+                                '.cke_editable blockquote p {display: inline ; font-style: italic}',
+                                '.cke_button_label.cke_button__templates_label { display: inline-block !important; }',
+                                'body.cke_editable { color: #000000; background-color: #ffffff; font-family: Arial, Helvetica, sans-serif }'
                             ],
-                            extraPlugins: 'ideocolorbutton,ideocolorpicker,ideolink,ideostyler,blockquote,removeformat,table,tabletools,tableresize',
-                            skin: 'ideo',
-                            forcePasteAsPlainText: true,
+                            stylesSet: [
+                                { name: 'Highlight 1', element: 'span', attributes: { 'class': 'txt-highlight-1' } },
+                                { name: 'Highlight 2', element: 'span', attributes: { 'class': 'txt-highlight-2' } },
+                                { name: 'Highlight 3', element: 'span', attributes: { 'class': 'txt-highlight-3' } },
+                                { name: 'Highlight 4', element: 'span', attributes: { 'class': 'txt-highlight-4' } },
+                                { name: 'Underline 1', element: 'span', attributes: { 'class': 'txt-underline-1' } },
+                                { name: 'Color 1', element: 'span', attributes: { 'class': 'txt-color-1' } },
+                                { name: 'Color 2', element: 'span', attributes: { 'class': 'txt-color-2' } },
+                                { name: 'Color 3', element: 'span', attributes: { 'class': 'txt-color-3' } },
+                                { name: 'Color 4', element: 'span', attributes: { 'class': 'txt-color-4' } }
+                            ],
+                             // Add this property to show labels for specific buttons
+                            toolbar_buttons_showlabels: ['templates'],
+                            templates_replaceContent: false,
+                            forcePasteAsPlainText: false,
                             pasteFromWordRemoveFontStyles: true,
-                            language:window.userLocale.split('-')[0],
-                            format_tags: 'p;h2;h3;h4;h5;h6;pre;address;div',
-                            entities:false,
-                            title:false
+                            pasteFromWordRemoveStyles: false,
+                            language: window.userLocale.split('-')[0],
+                            preDefaultTitle: translate("defaultAccordionTitle"),
+                            preDefaultMonospace: false,
+                            preDefaultWrap: true,
+                            allowedContent: true,
+                            format_tags: 'p;h2;h3;h4;h5;h6',
+                            entities: false,
+                            autoParagraph: false,
+                            title: false,
                         },
                         _onKeyPress: function(event) {
                             event.stopImmediatePropagation();
                         },
                         /**
-                         * initialise la vue
+                         * Initialise la vue
                          */
                         initialize: function() {
                             this._super();
-                            this.class = "TextBlockView";
+                            this.class = "TableBlockView";
                             this.listenTo(this.model, Events.BackboneEvents.CHANGE + ':content', this.render);
+                            
+                            //edition code source uniquement pour superadmin et root
+                            if(this.app.user.can('edit_source')) {
+                                var rootToolbar = this.defaultToolbar.slice();
+                                rootToolbar.push({name: 'source', groups: ['source'], items: ["Sourcedialog"]});
+                                this.CKconfig.toolbar = rootToolbar;
+                            } else {
+                                this.CKconfig.toolbar = this.defaultToolbar;
+                            }
+                            
+                            // Configuration des templates
+                            this.CKconfig.templates = 'default';
+                            this.CKconfig.templates_files = [this.getTemplateFilePath()];
+                            
+                            // Créer la vue d'édition comme dans TextBlockView
+                            this.tableEditionDialog = new TableEditionView({textObject: this, app: this.app});
                         },
                         saveText: function(dependency) {
                             if (this.constructor.editor)
@@ -66,89 +114,32 @@
                             this.model.zone.removeDependency(this.textDependency);
                         },
                         /**
-                         * La vue du bloc de texte ne produit pas le même résultat que les autres: on affiche CKEditor
-                         *
+                         * La vue du bloc de tableau ouvre une modal d'édition
                          * @argument {jQuery.Event} event Évenement jQuery
                          */
                         edit: function(event) {
-                            var editable = this.dom[this.cid].content;
-                            var editor = CKEDITOR.inline(editable[0], this.CKconfig);
-                            event.stopPropagation();
-                            if (this.constructor.editor) {
-                                this.constructor.editor.destroy();
+                            // Empêcher la propagation de l'événement
+                            if (event) {
+                                event.stopPropagation();
                             }
-                            this.$el.blur();
-                            editable.attr('contenteditable', 'true');
-                            editor.on('instanceReady', this._onCKReady, this);
-                            editor.on('destroy', this._onCKDestroy, this);
-                            this.constructor.editor = editor;
+                            
+                            // Mettre à jour le contenu original avant d'ouvrir la boîte de dialogue
+                            this.tableEditionDialog.trigger('updateOriginalContent', this.model.get('content'));
+                            
+                            // Ouvrir la boîte de dialogue
+                            this.tableEditionDialog.open();
                         },
-                        showOptionPanel: function() {
-                            return ContentView.prototype.edit.call(this);
-                        },
-                        /**
-                         * Exécutée dés que CKEditor est prêt
-                         * @private
-                         */
-                        _onCKReady: function(event) {
-                            this.$el.attr('data-editing', 'true');
-                            this.constructor.editor = event.editor;
-                            var zone = this.model.zone;
-                            if (zone) {
-                                this.textDependency = new ZoneDependency({save: this.saveText}, this);
-                                zone.addDependency(this.textDependency);
-                                this.listenTo(this.model, Events.ContentEvents.REMOVED, this.onRemove);
-                            }
-                            this.constructor.editor.JEditorTextBlockView = this;
-                            var $editor = $('#cke_' + this.constructor.editor.name);
-                            this.constructor.editor.setReadOnly(false);
-                            this.constructor.editor.setData(this.model.content);
-                            if (this.$el.hasClass('focused'))
-                                this.$el.toggleClass('focused');
-                            this.dom[this.cid].overlay.hide();
-                            this.dom[this.cid].content.on('click.editBlock', function(event) {
-                                event.stopPropagation();
-                            });
-                            $(document).on('click.editBlock', _.bind(function(event) {
-                                this.constructor.editor.destroy();
-                            }, this));
-                            $editor.on("click.preventEscape", function(event) {
-                                event.stopPropagation();
-                            });
-                            this.constructor.editor.on('dialogShow', function(event) {
-                                //console.log(event);
-                                var dialogElement = $(event.data._.element.$);
-                                dialogElement.on('click.preventEscape', function(event) {
-                                    event.stopPropagation();
-                                });
-                            });
-                            event.editor.focus();
-                            //this.dom[this.cid].content.focus();
-                        },
-                        /**
-                         * Exécutée lors de la destruction de CKEditor
-                         * @private
-                         */
-                        _onCKDestroy: function(event) {
-                            var editor = event.editor;
-                            //unbindEvents
-                            var $editor = $('#cke_' + event.editor.name);
-                            this.dom[this.cid].content.off('click.editBlock');
-                            $(document).off('click.editBlock');
-                            $editor.off("click.preventEscape");
-                            ///other
-                            this.$el.attr('data-editing', 'false');
-                            this.dom[this.cid].content.attr('contenteditable', 'false');
-                            this.model.content = editor.getData();
-                            this.delegateEvents();
-                            this.dom[this.cid].overlay.show();
-                            this.triggerUpdate();
-                            this.constructor.editor = null;
-                            if (this.model.zone)
-                                this.model.zone.removeDependency(this.textDependency);
-                        },
-                        noSelect: function() {
+                        getTemplateFilePath: function() {
+                            // Create the correct path to the template files
+                            var basePath = __IDEO_INTEGRATION_PATH__ + '/src/js/';
+                            
+                            // Use the paths defined in TableBlock
+                            var templatePaths =  basePath + 'JEditor/PagePanel/Contents/Blocks/TableBlock/Templates/table_templates.js';
+                            
+                            return templatePaths;
                         }
+
                     });
-            return TextBlockView;
+            
+            return TableBlockView;
         });
Index: src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/Views/TableEditionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/Views/TableEditionView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/Views/TableEditionView.js	(révision 14234)
@@ -0,0 +1,344 @@
+define([
+    "jquery",
+    "underscore",
+    "text!../Templates/tableEdition.html",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/DialogView",
+    "i18n!../nls/i18n",
+    "ckeditor",
+    "JEditor/PagePanel/Contents/Zones/Models/ZoneDependency",
+    "JEditor/PagePanel/Contents/Ancestors/ContentView",
+    "JEditor/App/Messages/ConfirmUnsaved",
+    //not in params
+    "jqueryPlugins/dropdown"
+], function($, _, tableEdition, Events, DialogView, translate, CKEDITOR, ZoneDependency, ContentView, ConfirmUnsaved) {
+    var TableEditionView = DialogView.extend({
+        className: 'tableEdition',
+        events: {
+            'dialogbeforeclose': 'beforeClose',
+        },
+        /**
+         * Constructeur de la vue d'édition de tableau
+         */
+        constructor: function(options) {
+            this.tableBlockView = options.textObject;
+            this.editor = null;
+            this.isClosing = false;
+            this.app = options.app || this.tableBlockView.app;
+            
+            var opts = _.extend({
+                title: translate("tableEdition"),
+                buttons: [
+                    {
+                        text: translate("save"),
+                        class: 'okay',
+                        click: _.bind(this.onOk, this)
+                    },
+                    {
+                        text: translate("cancel"),
+                        class: 'cancel',
+                        click: _.bind(this.onCancel, this)
+                    }
+                ],
+            }, options);
+
+            return DialogView.call(this, opts);
+        },
+
+        /**
+         * Initialise la vue
+         */
+        initialize: function(options) {
+            this._super();
+            this._template = this.buildTemplate(tableEdition, translate);
+            
+            if (this.tableBlockView && this.tableBlockView.model) {
+                this.listenTo(this.tableBlockView.model, Events.BackboneEvents.CHANGE + ':content', this.onContentChange);
+            } else {
+                console.error('tableBlockView or model is undefined in TableEditionView initialize');
+            }
+            
+            this.trigger(Events.LoadEvents.LOAD_START, this);
+        },
+        
+        /**
+         * Rend la vue
+         */
+        render: function() {
+            this._super();
+            this.undelegateEvents();
+            this.$el.html(this._template());
+            this.initializeCKEditor();
+            this.delegateEvents();
+            return this;
+        },
+
+        /**
+         * Ouvre la boîte de dialogue
+         */
+        open: function() {
+            // Initialiser le contenu original
+            this.initContent();
+            // Appeler la méthode open du parent
+            DialogView.prototype.open.call(this);
+            // Empêcher le défilement du corps de la page
+            $('body').css('overflow', 'hidden');
+        },
+        
+        beforeClose: function(event, ui) {
+            event.preventDefault();
+
+            // Ajout et utilisation de flag pour eviter plusieurs vérification 
+            if (!this.isClosing) {
+                this.isClosing = true;
+                
+                if (this.hasUnsavedChanges()) {
+                    this.handleConfirmationOperation(false, _.bind(function() {
+                        this.destroyEditorCloseModal();
+                        this.isClosing = false;
+                    }, this));
+                } else {
+                    this.destroyEditorCloseModal();
+                    this.isClosing = false;
+                }
+            }
+            return false;
+        },
+        initContent: function() {
+            this.originalContent = this.tableBlockView.model.get('content');
+        },
+        initializeCKEditor: function() {
+            var editorConfig = _.extend({}, this.tableBlockView.CKconfig, {
+                height: '400px',
+                resize_enabled: false,
+                // Configuration globale pour les z-index des panneaux
+                baseFloatZIndex: 1000000000,
+                // Ensure all toolbar items are enabled
+                removeButtons: '',
+                allowedContent: true,
+                        // Add this configuration to show labels for specific buttons
+                toolbar_buttons_showlabels: ['templates'],
+                on: {
+                    instanceReady: function(evt) {
+                        // Add CSS rule when editor is ready
+                        $('head').append('<style>' + 
+                            '.cke_button__templates_label { display: inline-block !important; }' +
+                            '.cke_button__templates_icon { filter: brightness(0) invert(1) !important; ' +
+                            'background-image: url("' + CKEDITOR.getUrl('plugins/icons.png') + '") !important; }' +
+                            '</style>'
+                        );
+                    }
+                }
+            });
+
+            // S'assurer que les plugins nécessaires sont chargés
+            if (CKEDITOR.plugins && !CKEDITOR.plugins.get('templates')) {
+                CKEDITOR.plugins.addExternal('templates', CKEDITOR.basePath + 'plugins/templates/');
+            }
+            
+            // Make sure templates are properly configured
+            editorConfig.templates = 'default';
+            editorConfig.templates_files = [this.tableBlockView.getTemplateFilePath()];
+            editorConfig.templates_replaceContent = false;
+
+            this.editor = CKEDITOR.replace(this.$('#table-content')[0], editorConfig);
+            
+            this.editor.on('instanceReady', this._onCKReady, this);
+            this.editor.on('change', _.bind(this.onEditorChange, this));
+            
+            // Gérer uniquement les dialogues pour l'accessibilité
+            this.editor.on('dialogShow', function(event) {
+                var dialogElement = $(event.data._.element.$);
+                
+                // Gérer le focus pour l'accessibilité
+                var firstModal = $('.tableEdition');
+                firstModal.attr('aria-hidden', 'true');
+                
+                dialogElement.attr('tabindex', '-1').focus();
+                
+                dialogElement.on('focusin', function(e) {
+                    e.stopPropagation();
+                });
+                
+                dialogElement.on('dialogclose', function() {
+                    firstModal.removeAttr('aria-hidden');
+                });
+            });
+
+            this.setupStyleHandler();
+        },
+
+        _onCKReady: function(event) {
+            this.$el.attr('data-editing', 'true');
+            this.constructor.editor = event.editor;
+            var zone = this.tableBlockView.model.zone;
+            if (zone) {
+                this.textDependency = new ZoneDependency({save: this.tableBlockView.saveText}, this);
+                zone.addDependency(this.textDependency);
+                this.listenTo(this.tableBlockView.model, Events.ContentEvents.REMOVED, this.tableBlockView.onRemove);
+            }
+            this.constructor.editor.JEditorTextBlockView = this;
+            
+            this.constructor.editor.setReadOnly(false);
+            this.constructor.editor.setData(this.originalContent);
+            if (this.$el.hasClass('focused'))
+                this.$el.toggleClass('focused');
+
+            this.constructor.editor.on('dialogShow', function(event) {
+                var dialogElement = $(event.data._.element.$);
+                var firstModal = $('.tableEdition');
+            
+                firstModal.attr('aria-hidden', 'true');
+                dialogElement.attr('tabindex', '-1').focus();
+                
+                dialogElement.on('focusin', function(e) {
+                    e.stopPropagation();
+                });
+            
+                dialogElement.on('dialogclose', function() {
+                    firstModal.removeAttr('aria-hidden');
+                });
+            
+                dialogElement.css({
+                    'z-index': '1000000004'
+                }).on('click.preventEscape keydown.preventEscape', function(e) {
+                    e.stopPropagation();
+                });
+            });
+
+            event.editor.focus();
+            this.trigger(Events.LoadEvents.LOAD_SUCCESS, this);
+        },
+        setupStyleHandler: function() {
+            $(document).on('click', '.cke_button__ideolink', _.bind(function(event) {
+
+                var $ckPanelBlock = $('iframe.cke_panel_frame').contents().find('.cke_panel_block');
+                if ($ckPanelBlock.length) {
+                    $ckPanelBlock.css({
+                        'background-color': '#313131'
+                    });
+                }
+            }, this));
+
+        },
+
+        onEditorChange: function() {
+            // Gère les changements en temps réel dans l'éditeur
+        },
+
+        onContentChange: function() {
+            if (this.editor) {
+                this.editor.setData(this.tableBlockView.model.get('content'));
+            }
+        },
+
+        onCancel: function() {
+            if (this.tableBlockView) {
+                this.tableBlockView.model.set('content', this.originalContent);
+            }
+            this.destroyEditorCloseModal();
+        },
+
+        onOk: function() {
+            this.updateModel();
+            this.destroyEditorCloseModal();
+        },
+
+        updateModel : function() {
+            if (this.editor && this.tableBlockView) {
+                var newContent = this.editor.getData();
+                this.tableBlockView.model.set('content', newContent);
+                this.tableBlockView.triggerUpdate();
+                
+                if (this.tableBlockView.textDependency) {
+                    this.tableBlockView.textDependency.saved();
+                }
+            }
+        },
+
+        destroyEditorCloseModal: function() {
+            if (this.editor) {
+                this.editor.destroy();
+                this.editor = null;
+            }
+            $('body').css('overflow', '');
+            this.$el.dialog('close');
+            this.remove();
+        },
+        
+        updateOriginalContent: function() {
+            this.originalContent = this.tableBlockView.model.get('content');
+            this.render();
+        },
+        
+        hasUnsavedChanges: function() {
+            var result = false;
+            if (this.editor && this.tableBlockView) {
+                // Obtenir le contenu et le normaliser
+                var editorContent = this.editor.getData().trim();
+                var modelContent = this.tableBlockView.model.get('content').trim();
+                
+                // Normaliser le HTML pour gérer les différences de formatage
+                // 1. Créer des éléments <div> temporaires
+                var tempEditor = $('<div>').html(editorContent);
+                var tempModel = $('<div>').html(modelContent);
+                
+                // 2. Obtenir le HTML normalisé (cela supprime les espaces inutiles et normalise les attributs)
+                var normalizedEditor = tempEditor.html();
+                var normalizedModel = tempModel.html();
+                
+                // 3. Supprimer tous les espaces entre les balises pour gérer les différences de formatage
+                normalizedEditor = normalizedEditor.replace(/>\s+</g, '><');
+                normalizedModel = normalizedModel.replace(/>\s+</g, '><');
+                
+                // 4. Comparer les contenus normalisés
+                result = normalizedEditor !== normalizedModel;
+            }
+            return result;
+        },
+        
+        showOptionPanel: function() {
+            this.handleConfirmationOperation(true);
+        },
+        
+        confirmUnsaved: function(params) {
+            this.app.messageDelegate.set(new ConfirmUnsaved(params));
+        },
+        
+        handleConfirmationOperation: function(showOptionPanel, callback) {
+            if (this.tableBlockView && this.hasUnsavedChanges()) {
+                this.confirmUnsaved({
+                    message: translate("quitWithoutSaving"),
+                    title: translate("unsavedChanges"),
+                    type: 'delete-not-saved',
+                    onYes: _.bind(function() {
+                        this.updateModel();
+                        this.handleAfterConfirm(showOptionPanel, callback);
+                    }, this),
+                    onNo: _.bind(function() {
+                        this.handleAfterConfirm(showOptionPanel, callback);
+                    }, this),
+                    options: {
+                        dialogClass: 'delete no-close',
+                        dontAskAgain: true,
+                        onCancel: _.bind(function() {
+                            this.isClosing = false;
+                        }, this),
+                    }
+                });
+            } else {
+                this.handleAfterConfirm(showOptionPanel, callback);
+            }
+        },
+        
+        handleAfterConfirm: function(showOptionPanel, callback) {
+            if(showOptionPanel) {
+                this.destroyEditorCloseModal();
+                return ContentView.prototype.edit.call(this.tableBlockView);
+            }
+            if (callback) callback();
+        },
+    });
+
+    return TableEditionView;
+});

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/Views/TableEditionView.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/nls/fr-ca/i18n.js	(révision 14233)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/nls/fr-ca/i18n.js	(révision 14234)
@@ -1,5 +1,10 @@
 define({
     "BLOCK_NAME": "Tableau",
     "Title":"Titre",
-    "tableBlockOption": 'Option du tableau'
+    "tableBlockOption": 'Option du tableau',
+    "tableEdition":"Modification de tableau",
+    "save":"Enregistrer",
+    "cancel":"Annuler",
+    "quitWithoutSaving" : "Vous n'avez pas enregistré les modifications apportées, voulez-vous les enregistrer ?",
+    "unsavedChanges" : "sauvegarder les changements"
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/nls/fr-fr/i18n.js	(révision 14233)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/nls/fr-fr/i18n.js	(révision 14234)
@@ -1,5 +1,10 @@
 define({
     "BLOCK_NAME": "Tableau",
     "Title":"Titre",
-    "tableBlockOption": 'Option du tableau'
+    "tableBlockOption": 'Option du tableau',
+    "tableEdition":"Modification de tableau",
+    "save":"Enregistrer",
+    "cancel":"Annuler",
+    "quitWithoutSaving" : "Vous n'avez pas enregistré les modifications apportées, voulez-vous les enregistrer ?",
+    "unsavedChanges" : "sauvegarder les changements"
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/nls/i18n.js	(révision 14233)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TableBlock/nls/i18n.js	(révision 14234)
@@ -1,5 +1,10 @@
 define({"root": {
         "BLOCK_NAME": "Table",
         "Title":"Title",
-        "tableBlockOption": 'Table option'
+        "tableBlockOption": 'Table option',
+        "tableEdition":"Table edit",
+        "save":"Save",
+        "cancel":"Cancel",
+        "quitWithoutSaving": "You haven't saved the changes you've made. Would you like to save them ?",
+        "unsavedChanges": "Save changes"
     }, "fr-fr": true, "fr-ca": true});
Index: src/less/imports/ckeditor_table_modeles.less
===================================================================
--- src/less/imports/ckeditor_table_modeles.less	(nonexistent)
+++ src/less/imports/ckeditor_table_modeles.less	(révision 14234)
@@ -0,0 +1,87 @@
+/* Table Styles */
+.styled-table {
+    width: 100%;
+    border-collapse: collapse;
+    margin: 25px 0;
+    font-size: 0.9em;
+    font-family: sans-serif;
+    box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
+    
+    thead tr {
+      background-color: @pageColor;
+      color: #ffffff;
+      text-align: left;
+    }
+    
+    th, td {
+      padding: 12px 15px;
+    }
+    
+    tbody tr {
+      border-bottom: 1px solid #dddddd;
+      
+      &:nth-of-type(even) {
+        background-color: #f3f3f3;
+      }
+      
+      &:last-of-type {
+        border-bottom: 2px solid @pageColor;
+      }
+      
+      &.active-row {
+        font-weight: bold;
+        color: @pageColor;
+      }
+    }
+    
+    /* Responsive table */
+    @media screen and (max-width: 600px) {
+      display: block;
+      overflow-x: auto;
+    }
+  }
+  
+  /* Alternative table styles */
+  .minimal-table {
+    width: 100%;
+    border-collapse: collapse;
+    
+    th, td {
+      padding: 8px;
+      text-align: left;
+      border-bottom: 1px solid #ddd;
+    }
+    
+    tr:hover {
+      background-color: #f5f5f5;
+    }
+  }
+  
+  .bordered-table {
+    width: 100%;
+    border-collapse: collapse;
+    
+    th, td {
+      padding: 8px;
+      border: 1px solid #ddd;
+    }
+    
+    th {
+      background-color: #f2f2f2;
+      font-weight: bold;
+    }
+  }
+  
+  .striped-table {
+    width: 100%;
+    border-collapse: collapse;
+    
+    th, td {
+      padding: 8px;
+      text-align: left;
+    }
+    
+    tr:nth-child(even) {
+      background-color: #f2f2f2;
+    }
+  }
\ No newline at end of file

Property changes on: src/less/imports/ckeditor_table_modeles.less
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/less/main.less
===================================================================
--- src/less/main.less	(révision 14233)
+++ src/less/main.less	(révision 14234)
@@ -43,6 +43,7 @@
 // media-queries
 @import "./imports/common/queries/height-based/main.less";
 @import "./imports/common/queries/width-based/main.less";
+@import "./imports/ckeditor_table_modeles.less";
 
 
 /* ==========================================================================
