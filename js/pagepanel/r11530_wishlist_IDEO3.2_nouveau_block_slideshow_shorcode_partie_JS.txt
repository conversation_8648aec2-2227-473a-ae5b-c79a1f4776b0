Revision: r11530
Date: 2023-11-07 09:04:34 +0300 (tlt 07 Nov 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: nouveau block slideshow + shorcode (partie JS)

## Files changed

## Full metadata
------------------------------------------------------------------------
r11530 | srazanandralisoa | 2023-11-07 09:04:34 +0300 (tlt 07 Nov 2023) | 1 ligne
Chemins modifiés :
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Templates/editImageInfosDialog.html
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Templates/imageCollection.html
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Templates/imageGroup.html
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Templates/imageGroupCollection.html
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Views/CollectionSelectorDialog.js
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Views/EditImageInfo.js
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Views/FileGroupListView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Views/ImageGroupView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/main.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js
   D /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/editImageInfosDialog.html
   D /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageCollection.html
   D /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageGroup.html
   D /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageGroupCollection.html
   D /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselFileEditionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js
   D /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/EditImageInfo.js
   D /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/FileGroupListView.js
   D /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/ImageGroupView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/main.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieFieldView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridStyleOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopOptionView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Models
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Models/SlideshowOption.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Models/SlideshowStyleOption.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Models/main.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/SlideshowBlock.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Templates
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Templates/slideshowOption.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Templates/slideshowStyle.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Views
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Views/SlideshowBlockView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Views/SlideshowOptionView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Views/SlideshowStyleOptionView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Views/main.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/main.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/de-de
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/de-de/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/en-au
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/en-au/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/en-us
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/en-us/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/es-es
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/es-es/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-ca
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-ca/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-fr
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-fr/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json
   M /branches/ideo3_v2/integration/src/js/build.js
   M /branches/ideo3_v2/integration/src/js/main.js

wishlist IDEO3.2: nouveau block slideshow + shorcode (partie JS)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Files/Templates/editImageInfosDialog.html
===================================================================
--- src/js/JEditor/Commons/Files/Templates/editImageInfosDialog.html	(nonexistent)
+++ src/js/JEditor/Commons/Files/Templates/editImageInfosDialog.html	(révision 11530)
@@ -0,0 +1,21 @@
+<header>
+    <div>
+    <span class="back"></span>
+    <h2><%=__("editImages")%></h2>
+    </div>
+    <div><p><%=__("nameAndDescribeImages")%></p></div>
+</header>
+<section>
+    <div class="col image">
+        <div class="image-wrapper">
+            <div class="overlay">
+                <button data-action="edit"><%=__("edit")%></button>
+            </div>
+        </div>
+    </div>
+    <div class="col about">
+        <input type="text" name="title" />
+        <input type="text" name="title" />
+        
+    </div>
+</section>
\ No newline at end of file

Property changes on: src/js/JEditor/Commons/Files/Templates/editImageInfosDialog.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/Commons/Files/Templates/imageCollection.html
===================================================================
--- src/js/JEditor/Commons/Files/Templates/imageCollection.html	(nonexistent)
+++ src/js/JEditor/Commons/Files/Templates/imageCollection.html	(révision 11530)
@@ -0,0 +1,17 @@
+<div class="panel-head">
+    <span class="file-count">
+        <span class="count"><%=group.length%></span>
+        <span class="text"><%=__("imageInGroup")%></span>
+    </span>
+    <a class="new-group action <%= group.isNew()?'selected':''%>" data-action="newgroup" href="#">
+        <span class="icon-file-group"></span>
+        <span class="text"><%=__("newImageGroup") %></span>
+    </a>
+    <a class="existing action <%= !group.isNew()?'selected':''%>" data-action="selectgroup" href="#">
+        <span class="icon-gallery"  ></span>
+        <span class="text"><%=__("useExistingGroup")%></span>
+    </a>
+</div>
+<div class="panel-content">
+    
+</div>
\ No newline at end of file

Property changes on: src/js/JEditor/Commons/Files/Templates/imageCollection.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/Commons/Files/Templates/imageGroup.html
===================================================================
--- src/js/JEditor/Commons/Files/Templates/imageGroup.html	(nonexistent)
+++ src/js/JEditor/Commons/Files/Templates/imageGroup.html	(révision 11530)
@@ -0,0 +1,72 @@
+<header>
+    <div class="title">
+        <div>
+            <span class="icon-gallery"></span><input type="text" class="gallery-name" placeholder="<%=__("newCollectionName")%>" value="<%=Utils.stripHTML(group.name)%>" name="name"/><span class="icon-edit"></span>
+        </div>
+    </div><% if(group.files.length>0){%>
+
+    <div class="actions">
+        <div>
+            <div class="batch">
+                <div class="action" data-action="deleteSelected">
+                    <span class="icon-bin"></span>
+                    <span class="text"><%= __("delete") %></span>
+                </div>
+                <div class="dropdown">
+                    <a class="dropdown-toggle select" data-toggle="dropdown" href="#"><span class="caret"></span><span class="selected <%=selectedCount>0?'':'none' %> "><span class="count"><%=selectedCount>0?selectedCount:'' %></span><span class="icon-check"></span></span></a>
+                    <ul class="dropdown-menu" role="menu">
+                        <li><a class="action" data-action="selectAll" href="#"><%=__("allF")%></a></li>
+                        <li><a class="action" data-action="selectNone" href="#"><%=__("noneF")%></a></li>
+                    </ul>
+                </div>
+            </div>
+        </div>
+    </div>
+    <% } %>
+</header>
+<div class="content scrollbar-classic">
+    <% if( !group.isNew()){ %>
+    <div class="action back">
+        <span class="image"><span class="icon-prev-arrow"></span></span>
+        <span class="text"><%=__("backToCollectionList")%></span>
+    </div>
+    <% } %>
+    <div class="group-content <%=group.files.length===0?'empty':''%> uploader">
+
+        <% for( var i = 0; i<group.files.length; i++){ 
+            var file=group.files.at(i);
+            %>
+            <div class="thumb <%= selected[file.id]?'selected':'' %> <%= (i===0?'first':'')%>" data-id="<%=file.id %>" style="background-image: url(<%= file.thumb %>);">
+                <span class="select"><span class="icon-check"></span></span>
+                <div class="menu-wrapper">
+                    <ul class="menu">
+                        <li class="action" data-action="deleteItem" data-id="<%=file.id %>"><span class="icon-bin"></span></li>
+                        <li class="action" data-action="crop" data-id="<%=file.id %>"><span class="icon-crop"></span></li>
+                        <li class="action" data-action="edit" data-id="<%=file.id %>"><span class="icon-pencil"></span></li>
+                    </ul>
+                </div>
+            </div>
+            <% } %>
+            <div class="action inline" data-action="showUploader">
+                <span class="image"><span class="icon-hexagon-add"></span></span>
+                <span class="text"><%=__("addImageToCollection")%></span>
+            </div>
+            <div class="action empty" data-action="showUploader">
+                <div class="wrapper">
+                    <span class="icon-wrapper">
+                        <% if(group.isNew()){ %>
+                        <span class="icon-new-collection"></span>
+                        <% } else{ %>
+                        <span class="icon-gallery"></span>
+                        <% } %>
+                    </span>
+                    <span class="intro">
+                        <%= group.isNew()?__("emptyNewCollection"):__("emptyCollection") %>
+                    </span>
+                    <span class="how-to">
+                        <%= __("howToAddImages") %>
+                    </span>
+                </div>
+            </div>
+    </div>
+</div>
\ No newline at end of file

Property changes on: src/js/JEditor/Commons/Files/Templates/imageGroup.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/Commons/Files/Templates/imageGroupCollection.html
===================================================================
--- src/js/JEditor/Commons/Files/Templates/imageGroupCollection.html	(nonexistent)
+++ src/js/JEditor/Commons/Files/Templates/imageGroupCollection.html	(révision 11530)
@@ -0,0 +1,25 @@
+<div class="content filegroup-collection scrollbar-classic">
+    <div class="group-list">
+        <% for(var i =0; i<content.length; i++){ 
+        var fileGroup=content[i];
+        if(!fileGroup.isNew()){
+        %>
+
+        <div class="thumb" data-id="<%=fileGroup.id%>">
+            <div class="shadow"></div>
+            
+            <div class="image" style="background-image: url(<%= fileGroup.files.at(0)?fileGroup.files.at(0).thumb:'#'%>)">
+                <div class="overlay">
+                    <span class="groupName"><%=fileGroup.name%></span>
+                    <span class="fileCount"><span class="count"><%=fileGroup.length%></span><span class="icon-image"></span></span>
+                </div>
+                <div class="corner"></div>
+            </div>
+
+        </div>
+
+        <% 
+        }
+        }; %>
+    </div>
+</div>
\ No newline at end of file

Property changes on: src/js/JEditor/Commons/Files/Templates/imageGroupCollection.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/Commons/Files/Views/CollectionSelectorDialog.js
===================================================================
--- src/js/JEditor/Commons/Files/Views/CollectionSelectorDialog.js	(nonexistent)
+++ src/js/JEditor/Commons/Files/Views/CollectionSelectorDialog.js	(révision 11530)
@@ -0,0 +1,153 @@
+define([
+    "jquery",
+    "underscore",
+    "text!../Templates/imageCollection.html",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/DialogView",
+    "JEditor/Commons/Files/Views/ImageGroupView",
+    "JEditor/Commons/Files/Views/FileGroupListView",
+    "collection!JEditor/Commons/Files/Models/FileGroupCollection",
+    "collection!JEditor/Commons/Files/Models/FileGroup",
+    "JEditor/Commons/Files/Views/EditImageInfo",
+    "i18n!../nls/i18n"], function($, _, imageCollection, Events, DialogView, ImageGroupView, FileGroupListView, FileGroupCollection, FileGroup, EditImageInfo, translate) {
+    var CollectionSelectorDialog = DialogView.extend({
+        className: 'image-group-edit',
+        events: {
+            'click .action[data-action]': '_onActionClick'
+        },
+        constructor: function(options) {
+            this.translate = translate
+            if (!options)
+                var options = {};
+            options.width = 750;
+            options.height = 600;
+            options.buttons = [
+                {
+                    class: 'okay',
+                    text: this.translate("okay"),
+                    click: _.bind(this.onOk, this)
+                },
+                {
+                    text: this.translate("cancel"),
+                    class: 'cancel',
+                    click: _.bind(this.onCancel, this)
+                }
+            ]
+            DialogView.call(this, options);
+            this.on('open close', this.onToggle);
+        },
+        initialize: function() {
+            this._super();
+            this._template = this.buildTemplate(imageCollection, translate);
+            this.childViews = {};
+            this.childViews.editGroupView = new ImageGroupView({model: this.model, dialog: this});
+            this.childViews.imageGroupList = new FileGroupListView({collection: FileGroupCollection.getInstance()});
+            this.listenTo(this.childViews.imageGroupList, Events.ChoiceEvents.SELECT, this.setGroup);
+            this.listenTo(this.model, 'change:length', this.updateImageCount);
+            this.render();
+        },
+        _onActionClick: function(event) {
+            var $target = $(event.currentTarget);
+            var action = $target.data('action');
+            if (this[action] && !$target.hasClass('selected')) {
+                this.dom[this.cid].buttons.removeClass("selected");
+                $target.addClass("selected");
+                this[action]();
+            }
+            return false;
+        },
+        onToggle: function() {
+           this.childViews.editGroupView.updateScrollables();
+        },
+        newgroup: function(e) {
+            this.listenToOnce(this.childViews.imageGroupList, Events.ViewEvents.HIDE, function() {
+                this.stopListening(this.model, 'change:length');
+                var groupCollection = FileGroupCollection.getInstance();
+                this.model = new FileGroup();
+                groupCollection.add(this.model);
+                this.listenTo(this.model, 'change:length', this.updateImageCount);
+                this.updateImageCount();
+                this.childViews.editGroupView.setModel(this.model);
+                this.childViews.editGroupView.render();
+                this.childViews.editGroupView.show();
+                
+            });
+            this.childViews.imageGroupList.hide();
+            return false;
+        },
+        selectgroup: function(e) {
+            if (this.model.length === 0 && !this.model.name)
+                this.model.destroy();
+            this.listenToOnce(this.childViews.editGroupView, Events.ViewEvents.HIDE, function() {
+                this.childViews.imageGroupList.show();
+                this.childViews.imageGroupList.scrollables();
+            });
+            this.childViews.editGroupView.hide();
+            return false;
+        },
+        setGroup: function(view, group) {
+            this.stopListening(this.model, 'change:length');
+            this.model = group;
+            this.listenTo(this.model, 'change:length', this.updateImageCount);
+            this.updateImageCount();
+            this.childViews.editGroupView.setModel(group);
+            this.render();
+            this.dom[this.cid].buttons.removeClass("selected");
+            this.listenToOnce(this.childViews.editGroupView, Events.ViewEvents.SHOW, function() {
+             //   this.childViews.editGroupView.updateScrollables();
+            });
+            this.childViews.editGroupView.show();
+        },
+        onLengthChange: function() {
+            var fileGroupCollection = FileGroupCollection.getInstance();
+        },
+        updateImageCount: function() {
+            this.dom[this.cid].imageCount.text(this.model.length);
+        },
+        editImage: function(file) {
+            if (!this.childViews.singleFileEdit) {
+                this.childViews.singleFileEdit = new EditImageInfo({model: file});
+            } else {
+                this.childViews.singleFileEdit.setModel(file);
+            }
+        },
+        render: function() {
+            this.undelegateEvents();
+            this._super();
+            this.$el.html(this._template({group: this.model}));
+            this.dom[this.cid].panelContent = this.$('.panel-content');
+            this.dom[this.cid].buttons = this.$('.action[data-action="selectgroup"],.action[data-action="newgroup"]');
+            this.dom[this.cid].newButton = this.dom[this.cid].buttons.filter('[data-action="newgroup"]');
+            this.dom[this.cid].browseButton = this.dom[this.cid].buttons.filter('[data-action="selectgroup"]');
+            this.dom[this.cid].imageCount = this.$('.panel-head>.file-count>.count');
+            this.dom[this.cid].panelContent.append(this.childViews.editGroupView.render().el);
+            this.dom[this.cid].panelContent.append(this.childViews.imageGroupList.render().el);
+            this.childViews.imageGroupList.$el.hide();
+            this.listenToOnce(this.childViews.editGroupView, Events.ChoiceEvents.BACK, function() {
+                this.dom[this.cid].browseButton.removeClass('selected');
+                this.dom[this.cid].browseButton.trigger('click');
+            });
+            this.delegateEvents();
+            if (this.model.length === 0 && !this.model.name) {
+                this.dom[this.cid].newButton.removeClass('selected');
+                this.dom[this.cid].newButton.trigger('click');
+            } else
+                this.childViews.editGroupView.$el.show();
+            return this;
+        },
+        onCancel: function() {
+            if (this.model.length === 0 && !this.model.name)
+                this.model.destroy();
+            this.$el.dialog('close');
+        },
+        onOk: function() {
+            this.trigger(Events.ChoiceEvents.SELECT, this, this.model);
+            var files=this.model.attributes.files.models;
+            
+            var groupCollection = FileGroupCollection.getInstance();
+            groupCollection.add(this.model);
+            this.$el.dialog('close');
+        }
+    });
+    return CollectionSelectorDialog;
+});
\ No newline at end of file
Index: src/js/JEditor/Commons/Files/Views/EditImageInfo.js
===================================================================
--- src/js/JEditor/Commons/Files/Views/EditImageInfo.js	(nonexistent)
+++ src/js/JEditor/Commons/Files/Views/EditImageInfo.js	(révision 11530)
@@ -0,0 +1,23 @@
+define([
+	"text!../Templates/editImageInfosDialog.html",
+	"JEditor/Commons/Ancestors/Views/View"
+],function(	editImageInfosDialog,
+	View
+){
+var EditImageInfo = View.extend({
+    initialize:function(){
+        this._super();
+        this._template = this.buildTemplate(editImageInfosDialog,translate);
+        
+    },
+    render:function(){
+        this.$el.html(this._template());
+        return this;
+    },
+    setModel:function(model){
+        this.model=model;
+        this.render();
+    }
+});
+return EditImageInfo;
+});
\ No newline at end of file

Property changes on: src/js/JEditor/Commons/Files/Views/EditImageInfo.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/Commons/Files/Views/FileGroupListView.js
===================================================================
--- src/js/JEditor/Commons/Files/Views/FileGroupListView.js	(nonexistent)
+++ src/js/JEditor/Commons/Files/Views/FileGroupListView.js	(révision 11530)
@@ -0,0 +1,34 @@
+define( [
+    "jquery",
+    "text!../Templates/imageGroupCollection.html",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/ListView",
+    "i18n!../nls/i18n"
+], function($, imageGroupCollection, Events, ListView, translate) {
+    var FileGroupListView = ListView.extend({
+        events: {
+            'click .action[data-action="tryreload"]': '_tryReload',
+            'click .thumb': '_selectFile'
+        },
+        initialize: function() {
+            this._super();
+            this._template = this.buildTemplate(imageGroupCollection, translate);
+            this._emptyTemplate = this.buildTemplate(imageGroupCollection, translate);
+        },
+        _selectFile: function(e) {
+            var target = $(e.currentTarget).data('id');
+            var file = this.collection.get(target);
+            this.trigger(Events.ChoiceEvents.SELECT, this, file);
+        },
+        _onLoaded: function() {
+            this.loaded = true;
+            this.render();
+        },
+        render: function() {
+            this._super();
+           // this.scrollables();
+            return this;
+        }
+    });
+    return FileGroupListView;
+});
\ No newline at end of file

Property changes on: src/js/JEditor/Commons/Files/Views/FileGroupListView.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/Commons/Files/Views/ImageGroupView.js
===================================================================
--- src/js/JEditor/Commons/Files/Views/ImageGroupView.js	(nonexistent)
+++ src/js/JEditor/Commons/Files/Views/ImageGroupView.js	(révision 11530)
@@ -0,0 +1,218 @@
+define([
+    "jquery",
+    "underscore",
+    "text!../Templates/imageGroup.html",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/View",
+    "JEditor/FilePanel/Views/ImageEditView",
+    "JEditor/FilePanel/Models/FileCollection",
+    "JEditor/Commons/Files/Views/FileSelectorDialog",
+    "JEditor/Commons/Utils",
+    "i18n!../nls/i18n",
+    //not in params
+    "jqueryPlugins/dropdown",
+    "jqueryPlugins/uploader"
+], function($, _, imageGroup, Events, View, ImageEditView, FileCollection, FileSelectorDialog, Utils, translate) {
+    var ImageGroupView = View.extend({
+        selected: null,
+        events: {
+            'uploadercomplete .uploader': '_onUploaded',
+            'keyup header input[type="text"]': '_setGroupName',
+            'paste header input[type="text"]': '_setGroupName',
+            'click .thumb': '_toggleSelected',
+            'click [data-action]': '_onActionClick',
+            'uploader_parsestock_image': 'openImageFileSelector',
+            'click .action.back': '_onBack',
+        },
+        initialize: function() {
+            this._super();
+            //remplacer par la nouvelle Filecolletion 
+            this.fileCollection =  new FileCollection();
+            this.fileCollection.fetch();
+            this._template = this.buildTemplate(imageGroup, translate);
+            this.selected = {};
+            this.model.files.each(function(file) {
+                this.selected[file.id] = false;
+            }, this);
+            this.translate = translate;
+            this.listenTo(this.model.files, Events.BackboneEvents.ADD, this.render);
+            this.listenTo(this.model.files, Events.BackboneEvents.REMOVE, this.render);
+            this.selectFileView = new FileSelectorDialog({collection: this.fileCollection, allowMultipleSelect: true});
+            this.listenTo(this.selectFileView, Events.ListViewEvents.CHOOSE_FILE, this._useExistingFile);
+        },
+        crop: function(fileId) {
+            var file = this.model.files.get(fileId);
+            var imageEditView = new ImageEditView({model: file});
+            var widget = this.options.dialog.$el.dialog('widget');
+            imageEditView.open();
+            this.listenToOnce(imageEditView, Events.ImageEditorEvents.SAVE, this._onImageEdited);
+            return false;
+        },
+        edit: function(fileId) {
+            this.options.dialog.editImage(this.model.files.get(fileId));
+        },
+        _onImageEdited: function(newFile, oldFile) {
+            this.model.files.remove(oldFile);
+            this.model.files.add(newFile);
+            this.model.save();
+            this.render();
+        },
+        _onActionClick: function(e) {
+            var $target = $(e.currentTarget);
+            var action = $target.data('action');
+            var fileId = $target.data('id');
+            if (this[action])
+                this[action](fileId);
+            return false;
+        },
+        selectAll: function() {
+            for (var file in this.selected) {
+                this.selected[file] = true;
+            }
+            this.render();
+        },
+        selectNone: function() {
+            for (var file in this.selected) {
+                this.selected[file] = false;
+            }
+            this.render();
+        },
+        _toggleSelected: function(e) {
+            var $target = $(e.currentTarget);
+            var id = $target.data('id');
+            this.selected[id] = !this.selected[id];
+            this._updateSelectCount();
+            $target.toggleClass('selected');
+        },
+        _updateSelectCount: function() {
+            var selectedCount = 0;
+            for (var selected in this.selected) {
+                if (this.selected[selected])
+                    selectedCount++;
+            }
+            if (selectedCount > 0) {
+                this.dom[this.cid].selectCount.parent().removeClass('none')
+                this.dom[this.cid].selectCount.text(selectedCount);
+            }
+            else {
+                this.dom[this.cid].selectCount.parent().addClass('none');
+                this.dom[this.cid].selectCount.text('');
+            }
+
+        },
+        _setGroupName: function(e) {
+            if (this._timeout)
+                window.clearTimeout(this._timeout);
+            var $target = $(e.currentTarget);
+            this._timeout = window.setTimeout(_.bind(function() {
+                var value = $target.val();
+                if (value && value !== this.model.name) {
+                    this.model.name = value;
+                    console.log(this.count);
+                    this.model.save();
+                }
+            }, this), 500);
+
+        },
+        deleteSelected: function() {
+            for (var fileId in this.selected) {
+                var selected = this.selected[fileId];
+                if (selected)
+                    this.model.files.remove(this.model.files.get(fileId));
+            }
+            this.model.save();
+            this.render();
+        },
+        deleteItem: function(id) {
+            var file = this.model.files.get(id);
+            delete this.selected[file.id];
+            this.model.files.remove(file);
+            this.model.save();
+        },
+        showUploader: function() {
+            this.dom[this.cid].uploader.uploader('showMenu');
+        },
+        setModel: function(model) {
+            this.stopListening(this.model.files, Events.BackboneEvents.ADD);
+            this.stopListening(this.model.files, Events.BackboneEvents.REMOVE);
+            this.model = model;
+            model.files.each(function(file) {
+                this.selected[file.id] = false;
+            }, this);
+            this.listenTo(this.model.files, Events.BackboneEvents.ADD, this.render);
+            this.listenTo(this.model.files, Events.BackboneEvents.REMOVE, this.render);
+            this.render();
+        },
+        render: function() {
+            this.undelegateEvents();
+            var selectedCount = 0;
+            for (var selected in this.selected) {
+                if (this.selected[selected])
+                    selectedCount++;
+            }
+            this.$el.html(this._template({group: this.model, selected: this.selected, selectedCount: selectedCount, Utils: Utils}));
+            this.dom[this.cid].uploader = this.$('.uploader');
+            this.dom[this.cid].uploader.uploader({showMenu: false, maxFiles: -1, menuContainer: this.$('.content'), lang: this.translate.translations, customStockEvent: '_parsestock_image'});
+            this.dom[this.cid].selectCount = this.$('.batch > .dropdown > a .selected .count');
+
+            this.$('.dropdown-toggle.select').dropdown();
+            this.delegateEvents();
+            return this;
+        },
+        _onUploaded: function(event, data) {
+            var files = data.filesData;
+            var toAdd = [];
+            for (var i = 0; i < files.length; i++) {
+                var file = this.fileCollection.create(files[i].response);
+                this.selected[file.id] = false;
+                toAdd.push(file);
+            }
+            this.model.files.add(toAdd, {at: 0});
+            if (!this.model.name) {
+                var date = new Date();
+                var dateString = date;
+                this.model.name = this.translate("newCollectionName") + Utils.dateFormat(this.translate('dateFormat'))
+            }
+            this.model.save();
+        },
+        resetFilter: function(){
+            this.fileCollection.resetFilter();
+          //  this.fileCollection.fetch();
+        },
+        openImageFileSelector: function() {
+            var selectFileView, that = this;
+            this.resetFilter();
+            selectFileView = new FileSelectorDialog({collection: this.fileCollection, allowMultipleSelect: true});
+            this.listenTo(selectFileView, Events.ListViewEvents.CHOOSE_FILE, this._useExistingFile);
+            this.listenTo(selectFileView, Events.DialogEvents.CLOSE, function() {
+                that.stopListening(selectFileView);
+                selectFileView.remove();
+            });
+            selectFileView.imagesOnly();
+            selectFileView.open();
+        },
+        _useExistingFile: function(selected) {
+            var file, id;
+            if (!selected)
+                return;
+            for (id in selected) {
+                file = selected[id];
+                if (!file)
+                    continue;
+                this.model.files.add(file, {at: 0});
+                this.selected[file.id] = false;
+            }
+            if (!this.model.name) {
+                var date = new Date();
+                var dateString = date;
+                this.model.name = this.translate("newCollectionName") + Utils.dateFormat(this.translate('dateFormat'));
+            }
+            this.model.save();
+
+        },
+        _onBack: function() {
+            this.trigger(Events.ChoiceEvents.BACK);
+        },
+    });
+    return ImageGroupView;
+});
\ No newline at end of file

Property changes on: src/js/JEditor/Commons/Files/Views/ImageGroupView.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/Commons/Files/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/Commons/Files/nls/fr-ca/i18n.js	(révision 11529)
+++ src/js/JEditor/Commons/Files/nls/fr-ca/i18n.js	(révision 11530)
@@ -1 +1,45 @@
-define({"allFiles":"Tous les fichiers","Photos":"Photos","Files":"Fichiers","sortBy":"Classer par","fileSort":"Pas de classement","fileSortcreatedAtAsc":"Date d'ajout croissante","fileSortcreatedAtDesc":"Date d'ajout décroissante","fileSortnameAsc":"Fichiers de A à Z","fileSortnameDesc":"Fichiers de Z à A","deleteAction":"Suppression","cancel":"Annuler","File(s)":"Fichier(s) sur","selectFile":"Parcourir la base de mon site","choose":"Choisir","defaultUploaderMessage":"Cliquez ici ou glissez-déposez pour remplacer l'image","defaultUploaderMessageImage":"Cliquez ici ou glissez-déposez pour remplacer l'image","defaultUploaderMessageFile":"Cliquez ici ou glissez-déposez pour remplacer le fichier","browseImageOnComputer":"Parcourir les dossiers <br>sur mon ordinateur","imagesFromStock":"Parcourir la base d'images<br> de mon site","importFailBadType":"Impossible d'importer le fichier %name% car il représente un danger potentiel","progressLoadingFiles":"Veuillez patienter pendant le chargement des fichiers","uploadFailTooBig": 'Le fichier "%name%" (%filesize% mo ) est trop volumineux pour être importé (limite %limite% mo)',"uploadFailErrorsOccured":"Des erreurs ont eu lieu pendant le transfert de fichier","uploadSuccess":"Le transfert des fichiers s'est déroulé avec succès","uploadFailServerError":"Impossible de transférer le fichier %name% à cause de l'erreur: \"%error%\"","LoadMoreImage":"Charger plus de fichiers ..."});
\ No newline at end of file
+define({
+    "allFiles":"Tous les fichiers",
+    "Photos":"Photos",
+    "Files":"Fichiers",
+    "sortBy":"Classer par",
+    "fileSort":"Pas de classement",
+    "fileSortcreatedAtAsc":"Date d'ajout croissante",
+    "fileSortcreatedAtDesc":"Date d'ajout décroissante",
+    "fileSortnameAsc":"Fichiers de A à Z",
+    "fileSortnameDesc":"Fichiers de Z à A",
+    "deleteAction":"Suppression",
+    "cancel":"Annuler",
+    "File(s)":"Fichier(s) sur",
+    "selectFile":"Parcourir la base de mon site",
+    "choose":"Choisir",
+    "defaultUploaderMessage":"Cliquez ici ou glissez-déposez pour remplacer l'image",
+    "defaultUploaderMessageImage":"Cliquez ici ou glissez-déposez pour remplacer l'image",
+    "defaultUploaderMessageFile":"Cliquez ici ou glissez-déposez pour remplacer le fichier",
+    "browseImageOnComputer":"Parcourir les dossiers <br>sur mon ordinateur",
+    "imagesFromStock":"Parcourir la base d'images<br> de mon site",
+    "importFailBadType":"Impossible d'importer le fichier %name% car il représente un danger potentiel",
+    "progressLoadingFiles":"Veuillez patienter pendant le chargement des fichiers",
+    "uploadFailTooBig": 'Le fichier "%name%" (%filesize% mo ) est trop volumineux pour être importé (limite %limite% mo)',"uploadFailErrorsOccured":"Des erreurs ont eu lieu pendant le transfert de fichier",
+    "uploadSuccess":"Le transfert des fichiers s'est déroulé avec succès",
+    "uploadFailServerError":"Impossible de transférer le fichier %name% à cause de l'erreur: \"%error%\"",
+    "LoadMoreImage":"Charger plus de fichiers ...",
+    "selectClickActionImage":"Sélectionnez l'action souhaitée au clic sur l’image.",
+    "editMyCarrousel":"Éditer ma carrousel",
+    "imageInGroup":"Images <br/>dans ma galerie",
+    "newImageGroup":"Créer une nouvelle <br/> collection d'images.",
+    "useExistingGroup":"Utiliser une collection <br/>d'images existante.",
+    "newCollectionName":"Nouvelle collection",
+    "backToCollectionList":"Retour à la liste des collections",
+    "addImageToCollection":"Ajouter des images à ma collection",
+    "howToAddImages" :"Cliquez ici ou glissez-déposez de nouvelles images pour les ajouter à votre collection.",
+    "okay"  :"Valider",
+    "cancel":"Annuler",
+    "editMyGallery" :"Éditer ma carrousel",
+    "newGallery" :"Nouvelle carrousel",
+    "dateFormat" :"Y-m-d H:i:s",
+    "emptyNewCollection" :"Votre nouvelle collection d’images est vide.",
+    "edit" :"Éditer",
+    "delete":"Supprimer",
+    "allF"  :"Toutes"
+});
\ No newline at end of file
Index: src/js/JEditor/Commons/Files/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/Commons/Files/nls/fr-fr/i18n.js	(révision 11529)
+++ src/js/JEditor/Commons/Files/nls/fr-fr/i18n.js	(révision 11530)
@@ -1,3 +1,46 @@
-define({"allFiles":"Tous les fichiers","Photos":"Photos","Files":"Fichiers","sortBy":"Classer par","fileSort":"Pas de classement","fileSortcreatedAtAsc":"Date d'ajout croissante","fileSortcreatedAtDesc":"Date d'ajout décroissante","fileSortnameAsc":"Fichiers de A à Z","fileSortnameDesc":"Fichiers de Z à A","deleteAction":"Suppression","cancel":"Annuler","File(s)":"Fichier(s) sur","selectFile":"Parcourir la base de mon site","choose":"Choisir","defaultUploaderMessage":"Cliquez ici ou glissez-déposez pour remplacer l'image","defaultUploaderMessageImage":"Cliquez ici ou glissez-déposez pour remplacer l'image","defaultUploaderMessageFile":"Cliquez ici ou glissez-déposez pour remplacer le fichier","browseImageOnComputer":"Parcourir les dossiers <br>sur mon ordinateur","imagesFromStock":"Parcourir la base d'images<br> de mon site","importFailBadType":"Impossible d'importer le fichier %name% car il représente un danger potentiel","progressLoadingFiles":"Veuillez patienter pendant le chargement des fichiers","uploadFailTooBig":'Le fichier "%name%" (%filesize% mo ) est trop volumineux pour être importé (limite %limite% mo)',"uploadFailErrorsOccured":"Des erreurs ont eu lieu pendant le transfert de fichier","uploadSuccess":"Le transfert des fichiers s'est déroulé avec succès",
-"uploadFailServerError":"Impossible de transférer le fichier %name% à cause de l'erreur: \"%error%\"",
-"LoadMoreImage":"Charger plus de fichiers ..."});
\ No newline at end of file
+define({
+    "allFiles":"Tous les fichiers",
+    "Photos":"Photos",
+    "Files":"Fichiers",
+    "sortBy":"Classer par",
+    "fileSort":"Pas de classement",
+    "fileSortcreatedAtAsc":"Date d'ajout croissante",
+    "fileSortcreatedAtDesc":"Date d'ajout décroissante",
+    "fileSortnameAsc":"Fichiers de A à Z",
+    "fileSortnameDesc":"Fichiers de Z à A",
+    "deleteAction":"Suppression",
+    "cancel":"Annuler",
+    "File(s)":"Fichier(s) sur",
+    "selectFile":"Parcourir la base de mon site",
+    "choose":"Choisir",
+    "defaultUploaderMessage":"Cliquez ici ou glissez-déposez pour remplacer l'image",
+    "defaultUploaderMessageImage":"Cliquez ici ou glissez-déposez pour remplacer l'image",
+    "defaultUploaderMessageFile":"Cliquez ici ou glissez-déposez pour remplacer le fichier",
+    "browseImageOnComputer":"Parcourir les dossiers <br>sur mon ordinateur",
+    "imagesFromStock":"Parcourir la base d'images<br> de mon site",
+    "importFailBadType":"Impossible d'importer le fichier %name% car il représente un danger potentiel",
+    "progressLoadingFiles":"Veuillez patienter pendant le chargement des fichiers",
+    "uploadFailTooBig":'Le fichier "%name%" (%filesize% mo ) est trop volumineux pour être importé (limite %limite% mo)',
+    "uploadFailErrorsOccured":"Des erreurs ont eu lieu pendant le transfert de fichier",
+    "uploadSuccess":"Le transfert des fichiers s'est déroulé avec succès",
+    "uploadFailServerError":"Impossible de transférer le fichier %name% à cause de l'erreur: \"%error%\"",
+    "LoadMoreImage":"Charger plus de fichiers ...",
+    "selectClickActionImage":"Sélectionnez l'action souhaitée au clic sur l’image.",
+    "editMyCarrousel":"Éditer ma carrousel",
+    "imageInGroup":"Images <br/>dans ma galerie",
+    "newImageGroup":"Créer une nouvelle <br/> collection d'images.",
+    "useExistingGroup":"Utiliser une collection <br/>d'images existante.",
+    "newCollectionName":"Nouvelle collection",
+    "backToCollectionList":"Retour à la liste des collections",
+    "addImageToCollection":"Ajouter des images à ma collection",
+    "howToAddImages" :"Cliquez ici ou glissez-déposez de nouvelles images pour les ajouter à votre collection.",
+    "okay"  :"Valider",
+    "cancel":"Annuler",
+    "editMyGallery" :"Éditer ma carrousel",
+    "newGallery" :"Nouvelle carrousel",
+    "dateFormat" :"Y-m-d H:i:s",
+    "emptyNewCollection" :"Votre nouvelle collection d’images est vide.",
+    "edit" :"Éditer",
+    "delete":"Supprimer",
+    "allF"  :"Toutes",
+});
\ No newline at end of file
Index: src/js/JEditor/Commons/Files/nls/i18n.js
===================================================================
--- src/js/JEditor/Commons/Files/nls/i18n.js	(révision 11529)
+++ src/js/JEditor/Commons/Files/nls/i18n.js	(révision 11530)
@@ -1,3 +1,47 @@
-define({ "root": {"allFiles":"All Files","Photos":"Photos","Files":"Files","sortBy":"Sort by","fileSort":"Not sorted","fileSortcreatedAtAsc":"Sort by date asc","fileSortcreatedAtDesc":"Sort by date desc","fileSortnameAsc":"Sort by name asc","fileSortnameDesc":"Sort by name desc","deleteAction":"Delete","cancel":"Cancel","File(s)":"File(s) on","selectFile":"Select File","choose":"Choose","defaultUploaderMessage":"Click here or drag-and-drop to change the picture","defaultUploaderMessageImage":"Click here or drag-and-drop to change the picture","defaultUploaderMessageFile":"Click here or drag-and-drop to change the file","browseImageOnComputer":"Browse directories <br> on my comptuer","imagesFromStock":"Browse pictures <br> on my site","importFailBadType":"Impossible to import file%name % due to a potential harm","progressLoadingFiles":"Please wait during file loading","uploadFailTooBig":  'The file "%name%" (%filesize% mo )  is too large to be imported (limit %limite% mo)',"uploadFailErrorsOccured":"There were errors during file transfer","uploadSuccess":"The file transfer was successful",
-"uploadFailServerError":"Impossible to transfer file %name% because \"%error%\"",
-"LoadMoreImage":"Load more files ..."}, "fr-fr":true, "fr-ca":true })
\ No newline at end of file
+define({ "root": {
+    "allFiles":"All Files",
+    "Photos":"Photos",
+    "Files":"Files",
+    "sortBy":"Sort by",
+    "fileSort":"Not sorted",
+    "fileSortcreatedAtAsc":"Sort by date asc",
+    "fileSortcreatedAtDesc":"Sort by date desc",
+    "fileSortnameAsc":"Sort by name asc",
+    "fileSortnameDesc":"Sort by name desc",
+    "deleteAction":"Delete",
+    "cancel":"Cancel",
+    "File(s)":"File(s) on",
+    "selectFile":"Select File",
+    "choose":"Choose",
+    "defaultUploaderMessage":"Click here or drag-and-drop to change the picture",
+    "defaultUploaderMessageImage":"Click here or drag-and-drop to change the picture",
+    "defaultUploaderMessageFile":"Click here or drag-and-drop to change the file",
+    "browseImageOnComputer":"Browse directories <br> on my comptuer",
+    "imagesFromStock":"Browse pictures <br> on my site",
+    "importFailBadType":"Impossible to import file%name % due to a potential harm",
+    "progressLoadingFiles":"Please wait during file loading",
+    "uploadFailTooBig":  'The file "%name%" (%filesize% mo )  is too large to be imported (limit %limite% mo)',
+    "uploadFailErrorsOccured":"There were errors during file transfer",
+    "uploadSuccess":"The file transfer was successful",
+    "uploadFailServerError":"Impossible to transfer file %name% because \"%error%\"",
+    "LoadMoreImage":"Load more files ...",
+    "selectClickActionImage":"Select the desired action by clicking on the image.",
+    "editMyCarrousel":"Edit my carousel",
+    "imageInGroup": "Images <br/>in my gallery",
+    "newImageGroup" :"Create a new <br/> collection of images.",
+    "useExistingGroup" :"Use an existing collection <br/>of images.",
+    "newCollectionName":"New collection",
+    "backToCollectionList":"Back to the list of collections",
+    "addImageToCollection":"Add images to my collection",
+    "howToAddImages" :"Click here or drag and drop new images to add them to your collection.",
+    "okay" :"Confirm",
+    "cancel": "Cancel",
+    "editMyGallery" :"Edit my carousel",
+    "newGallery":"New carousel",
+    "dateFormat" :"Y-m-d H:i:s",
+    "emptyNewCollection":"Your new image collection is empty.",
+    "edit" : "Edit",
+    "delete": "Delete",
+    "allF" : "All",
+    "noneF":"None"
+}, "fr-fr":true, "fr-ca":true })
\ No newline at end of file
Index: src/js/JEditor/Commons/main.js
===================================================================
--- src/js/JEditor/Commons/main.js	(révision 11529)
+++ src/js/JEditor/Commons/main.js	(révision 11530)
@@ -29,8 +29,12 @@
     "JEditor/Commons/Files/Models/FileGroup",
     "JEditor/Commons/Files/Models/FileGroupCollection",
     "JEditor/Commons/Files/Models/Image",
+    "JEditor/Commons/Files/Views/CollectionSelectorDialog",
+    "JEditor/Commons/Files/Views/EditImageInfo",
+    "JEditor/Commons/Files/Views/FileGroupListView",
     "JEditor/Commons/Files/Views/FileSelectorDialog",
     "JEditor/Commons/Files/Views/FileUploaderView",
+    "JEditor/Commons/Files/Views/ImageGroupView",
     "JEditor/Commons/Files/Views/ReadOnlyFileListManagerView",
     "JEditor/Commons/Files/Views/ReadOnlyFileListView",
     "JEditor/Commons/Files/Views/SelectFileView",
Index: src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js	(révision 11529)
+++ src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js	(révision 11530)
@@ -42,7 +42,7 @@
                                 events: {'click .block.available': 'addToCurrentContainer', 'dragstart .bloc.available': '_ondragStart'},
                                 blockOrder: {
                                     'standard': ["TextBlock", "ImageBlock", "ButtonBlock", "SeparatorBlock", "TableBlock"],
-                                    'advanced': ["CarrouselBlock", "GridBlock","GalerieBlock","CompareBlock", "LoopBlock", "FormBlock", /*"VideoLinkeoBlock",*/ "MapBlock", "VideoBlock", "HtmlBlock"],
+                                    'advanced': ["CarrouselBlock", "GridBlock","GalerieBlock","CompareBlock","SlideshowBlock", "LoopBlock", "FormBlock", /*"VideoLinkeoBlock",*/ "MapBlock", "VideoBlock", "HtmlBlock"],
                                     'others': ["LegalsBlock","SiteMapBlock","SocialNetworkBlock"],
                                     'ignored':['TwitterTimelineBlock']
                                 },
Index: src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js	(révision 11529)
+++ src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js	(révision 11530)
@@ -19,7 +19,8 @@
   "./CarrouselBlock",
   "./GalerieBlock",
   "./CompareBlock",
-  "./LoopBlock"
+  "./LoopBlock",
+  "./SlideshowBlock"
 ], function (
   ImageBlock,
   HtmlBlock,
@@ -41,7 +42,8 @@
   CarrouselBlock,
   GalerieBlock,
   CompareBlock,
-  LoopBlock
+  LoopBlock,
+  SlideshowBlock
 ) {
   var component = {
     "ImageBlock": ImageBlock,
@@ -64,7 +66,8 @@
     "CarrouselBlock"  : CarrouselBlock,
     "GalerieBlock" : GalerieBlock,
     "CompareBlock": CompareBlock,
-    "LoopBlock": LoopBlock
+    "LoopBlock": LoopBlock,
+    "SlideshowBlock": SlideshowBlock
   };
   return component;
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageCollection.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageCollection.html	(révision 11529)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageCollection.html	(nonexistent)
@@ -1,17 +0,0 @@
-<div class="panel-head">
-    <span class="file-count">
-        <span class="count"><%=group.length%></span>
-        <span class="text"><%=__("imageInGroup")%></span>
-    </span>
-    <a class="new-group action <%= group.isNew()?'selected':''%>" data-action="newgroup" href="#">
-        <span class="icon-file-group"></span>
-        <span class="text"><%=__("newImageGroup") %></span>
-    </a>
-    <a class="existing action <%= !group.isNew()?'selected':''%>" data-action="selectgroup" href="#">
-        <span class="icon-gallery"  ></span>
-        <span class="text"><%=__("useExistingGroup")%></span>
-    </a>
-</div>
-<div class="panel-content">
-    
-</div>
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageCollection.html
___________________________________________________________________
Deleted: svn:executable
## -1 +0,0 ##
-*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageGroup.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageGroup.html	(révision 11529)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageGroup.html	(nonexistent)
@@ -1,72 +0,0 @@
-<header>
-    <div class="title">
-        <div>
-            <span class="icon-gallery"></span><input type="text" class="gallery-name" placeholder="<%=__("newCollectionName")%>" value="<%=Utils.stripHTML(group.name)%>" name="name"/><span class="icon-edit"></span>
-        </div>
-    </div><% if(group.files.length>0){%>
-
-    <div class="actions">
-        <div>
-            <div class="batch">
-                <div class="action" data-action="deleteSelected">
-                    <span class="icon-bin"></span>
-                    <span class="text"><%= __("delete") %></span>
-                </div>
-                <div class="dropdown">
-                    <a class="dropdown-toggle select" data-toggle="dropdown" href="#"><span class="caret"></span><span class="selected <%=selectedCount>0?'':'none' %> "><span class="count"><%=selectedCount>0?selectedCount:'' %></span><span class="icon-check"></span></span></a>
-                    <ul class="dropdown-menu" role="menu">
-                        <li><a class="action" data-action="selectAll" href="#"><%=__("allF")%></a></li>
-                        <li><a class="action" data-action="selectNone" href="#"><%=__("noneF")%></a></li>
-                    </ul>
-                </div>
-            </div>
-        </div>
-    </div>
-    <% } %>
-</header>
-<div class="content scroll-container">
-    <% if( !group.isNew()){ %>
-    <div class="action back">
-        <span class="image"><span class="icon-prev-arrow"></span></span>
-        <span class="text"><%=__("backToCollectionList")%></span>
-    </div>
-    <% } %>
-    <div class="group-content <%=group.files.length===0?'empty':''%> uploader">
-
-        <% for( var i = 0; i<group.files.length; i++){ 
-            var file=group.files.at(i);
-            %>
-            <div class="thumb <%= selected[file.id]?'selected':'' %> <%= (i===0?'first':'')%>" data-id="<%=file.id %>" style="background-image: url(<%= file.thumb %>);">
-                <span class="select"><span class="icon-check"></span></span>
-                <div class="menu-wrapper">
-                    <ul class="menu">
-                        <li class="action" data-action="deleteItem" data-id="<%=file.id %>"><span class="icon-bin"></span></li>
-                        <li class="action" data-action="crop" data-id="<%=file.id %>"><span class="icon-crop"></span></li>
-                        <li class="action" data-action="edit" data-id="<%=file.id %>"><span class="icon-pencil"></span></li>
-                    </ul>
-                </div>
-            </div>
-            <% } %>
-            <div class="action inline" data-action="showUploader">
-                <span class="image"><span class="icon-hexagon-add"></span></span>
-                <span class="text"><%=__("addImageToCollection")%></span>
-            </div>
-            <div class="action empty" data-action="showUploader">
-                <div class="wrapper">
-                    <span class="icon-wrapper">
-                        <% if(group.isNew()){ %>
-                        <span class="icon-new-collection"></span>
-                        <% } else{ %>
-                        <span class="icon-gallery"></span>
-                        <% } %>
-                    </span>
-                    <span class="intro">
-                        <%= group.isNew()?__("emptyNewCollection"):__("emptyCollection") %>
-                    </span>
-                    <span class="how-to">
-                        <%= __("howToAddImages") %>
-                    </span>
-                </div>
-            </div>
-    </div>
-</div>
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageGroup.html
___________________________________________________________________
Deleted: svn:executable
## -1 +0,0 ##
-*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageGroupCollection.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageGroupCollection.html	(révision 11529)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageGroupCollection.html	(nonexistent)
@@ -1,25 +0,0 @@
-<div class="content filegroup-collection scroll-container">
-    <div class="group-list">
-        <% for(var i =0; i<content.length; i++){ 
-        var fileGroup=content[i];
-        if(!fileGroup.isNew()){
-        %>
-
-        <div class="thumb" data-id="<%=fileGroup.id%>">
-            <div class="shadow"></div>
-            
-            <div class="image" style="background-image: url(<%= fileGroup.files.at(0)?fileGroup.files.at(0).thumb:'#'%>)">
-                <div class="overlay">
-                    <span class="groupName"><%=fileGroup.name%></span>
-                    <span class="fileCount"><span class="count"><%=fileGroup.length%></span><span class="icon-image"></span></span>
-                </div>
-                <div class="corner"></div>
-            </div>
-
-        </div>
-
-        <% 
-        }
-        }; %>
-    </div>
-</div>
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageGroupCollection.html
___________________________________________________________________
Deleted: svn:executable
## -1 +0,0 ##
-*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/editImageInfosDialog.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/editImageInfosDialog.html	(révision 11529)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/editImageInfosDialog.html	(nonexistent)
@@ -1,21 +0,0 @@
-<header>
-    <div>
-    <span class="back"></span>
-    <h2><%=__("editImages")%></h2>
-    </div>
-    <div><p><%=__("nameAndDescribeImages")%></p></div>
-</header>
-<section>
-    <div class="col image">
-        <div class="image-wrapper">
-            <div class="overlay">
-                <button data-action="edit"><%=__("edit")%></button>
-            </div>
-        </div>
-    </div>
-    <div class="col about">
-        <input type="text" name="title" />
-        <input type="text" name="title" />
-        
-    </div>
-</section>
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/editImageInfosDialog.html
___________________________________________________________________
Deleted: svn:executable
## -1 +0,0 ##
-*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/EditImageInfo.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/EditImageInfo.js	(révision 11529)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/EditImageInfo.js	(nonexistent)
@@ -1,25 +0,0 @@
-define([
-	"text!../Templates/editImageInfosDialog.html",
-	"JEditor/PagePanel/Contents/Blocks/GalleryBlock/Views/EditImageInfo",
-	"JEditor/Commons/Ancestors/Views/View"
-],function(	editImageInfosDialog,
-	EditImageInfo,
-	View
-){
-var EditImageInfo = View.extend({
-    initialize:function(){
-        this._super();
-        this._template = this.buildTemplate(editImageInfosDialog,translate);
-        
-    },
-    render:function(){
-        this.$el.html(this._template());
-        return this;
-    },
-    setModel:function(model){
-        this.model=model;
-        this.render();
-    }
-});
-return EditImageInfo;
-});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/EditImageInfo.js
___________________________________________________________________
Deleted: svn:executable
## -1 +0,0 ##
-*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/FileGroupListView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/FileGroupListView.js	(révision 11529)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/FileGroupListView.js	(nonexistent)
@@ -1,34 +0,0 @@
-define( [
-    "jquery",
-    "text!../Templates/imageGroupCollection.html",
-    "JEditor/Commons/Events",
-    "JEditor/Commons/Ancestors/Views/ListView",
-    "i18n!../nls/i18n"
-], function($, imageGroupCollection, Events, ListView, translate) {
-    var FileGroupListView = ListView.extend({
-        events: {
-            'click .action[data-action="tryreload"]': '_tryReload',
-            'click .thumb': '_selectFile'
-        },
-        initialize: function() {
-            this._super();
-            this._template = this.buildTemplate(imageGroupCollection, translate);
-            this._emptyTemplate = this.buildTemplate(imageGroupCollection, translate);
-        },
-        _selectFile: function(e) {
-            var target = $(e.currentTarget).data('id');
-            var file = this.collection.get(target);
-            this.trigger(Events.ChoiceEvents.SELECT, this, file);
-        },
-        _onLoaded: function() {
-            this.loaded = true;
-            this.render();
-        },
-        render: function() {
-            this._super();
-            this.scrollables();
-            return this;
-        }
-    });
-    return FileGroupListView;
-});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/ImageGroupView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/ImageGroupView.js	(révision 11529)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/ImageGroupView.js	(nonexistent)
@@ -1,218 +0,0 @@
-define([
-    "jquery",
-    "underscore",
-    "text!../Templates/imageGroup.html",
-    "JEditor/Commons/Events",
-    "JEditor/Commons/Ancestors/Views/View",
-    "JEditor/FilePanel/Views/ImageEditView",
-    "JEditor/FilePanel/Models/FileCollection",
-    "JEditor/Commons/Files/Views/FileSelectorDialog",
-    "JEditor/Commons/Utils",
-    "i18n!../nls/i18n",
-    //not in params
-    "jqueryPlugins/dropdown",
-    "jqueryPlugins/uploader"
-], function($, _, imageGroup, Events, View, ImageEditView, FileCollection, FileSelectorDialog, Utils, translate) {
-    var ImageGroupView = View.extend({
-        selected: null,
-        events: {
-            'uploadercomplete .uploader': '_onUploaded',
-            'keyup header input[type="text"]': '_setGroupName',
-            'paste header input[type="text"]': '_setGroupName',
-            'click .thumb': '_toggleSelected',
-            'click [data-action]': '_onActionClick',
-            'uploader_parsestock_image': 'openImageFileSelector',
-            'click .action.back': '_onBack',
-        },
-        initialize: function() {
-            this._super();
-            //remplacer par la nouvelle Filecolletion 
-            this.fileCollection =  new FileCollection();
-            this.fileCollection.fetch();
-            this._template = this.buildTemplate(imageGroup, translate);
-            this.selected = {};
-            this.model.files.each(function(file) {
-                this.selected[file.id] = false;
-            }, this);
-            this.listenTo(this.model.files, Events.BackboneEvents.ADD, this.render);
-            this.listenTo(this.model.files, Events.BackboneEvents.REMOVE, this.render);
-            this.selectFileView = new FileSelectorDialog({collection: this.fileCollection, allowMultipleSelect: true});
-            this.listenTo(this.selectFileView, Events.ListViewEvents.CHOOSE_FILE, this._useExistingFile);
-        },
-        crop: function(fileId) {
-            var file = this.model.files.get(fileId);
-            var imageEditView = new ImageEditView({model: file});
-            var widget = this.options.dialog.$el.dialog('widget');
-            imageEditView.open();
-            this.listenToOnce(imageEditView, Events.ImageEditorEvents.SAVE, this._onImageEdited);
-            return false;
-        },
-        edit: function(fileId) {
-            this.options.dialog.editImage(this.model.files.get(fileId));
-        },
-        _onImageEdited: function(newFile, oldFile) {
-            this.model.files.remove(oldFile);
-            this.model.files.add(newFile);
-            this.model.save();
-            this.render();
-        },
-        _onActionClick: function(e) {
-            var $target = $(e.currentTarget);
-            var action = $target.data('action');
-            var fileId = $target.data('id');
-            if (this[action])
-                this[action](fileId);
-            return false;
-        },
-        selectAll: function() {
-            for (var file in this.selected) {
-                this.selected[file] = true;
-            }
-            this.render();
-        },
-        selectNone: function() {
-            for (var file in this.selected) {
-                this.selected[file] = false;
-            }
-            this.render();
-        },
-        _toggleSelected: function(e) {
-            var $target = $(e.currentTarget);
-            var id = $target.data('id');
-            this.selected[id] = !this.selected[id];
-            this._updateSelectCount();
-            $target.toggleClass('selected');
-        },
-        _updateSelectCount: function() {
-            var selectedCount = 0;
-            for (var selected in this.selected) {
-                if (this.selected[selected])
-                    selectedCount++;
-            }
-            if (selectedCount > 0) {
-                this.dom[this.cid].selectCount.parent().removeClass('none')
-                this.dom[this.cid].selectCount.text(selectedCount);
-            }
-            else {
-                this.dom[this.cid].selectCount.parent().addClass('none');
-                this.dom[this.cid].selectCount.text('');
-            }
-
-        },
-        _setGroupName: function(e) {
-            if (this._timeout)
-                window.clearTimeout(this._timeout);
-            var $target = $(e.currentTarget);
-            this._timeout = window.setTimeout(_.bind(function() {
-                var value = $target.val();
-                if (value && value !== this.model.name) {
-                    this.model.name = value;
-                    console.log(this.count);
-                    this.model.save();
-                }
-            }, this), 500);
-
-        },
-        deleteSelected: function() {
-            for (var fileId in this.selected) {
-                var selected = this.selected[fileId];
-                if (selected)
-                    this.model.files.remove(this.model.files.get(fileId));
-            }
-            this.model.save();
-            this.render();
-        },
-        deleteItem: function(id) {
-            var file = this.model.files.get(id);
-            delete this.selected[file.id];
-            this.model.files.remove(file);
-            this.model.save();
-        },
-        showUploader: function() {
-            this.dom[this.cid].uploader.uploader('showMenu');
-        },
-        setModel: function(model) {
-            this.stopListening(this.model.files, Events.BackboneEvents.ADD);
-            this.stopListening(this.model.files, Events.BackboneEvents.REMOVE);
-            this.model = model;
-            model.files.each(function(file) {
-                this.selected[file.id] = false;
-            }, this);
-            this.listenTo(this.model.files, Events.BackboneEvents.ADD, this.render);
-            this.listenTo(this.model.files, Events.BackboneEvents.REMOVE, this.render);
-            this.render();
-        },
-        render: function() {
-            this.undelegateEvents();
-            var selectedCount = 0;
-            for (var selected in this.selected) {
-                if (this.selected[selected])
-                    selectedCount++;
-            }
-            this.$el.html(this._template({group: this.model, selected: this.selected, selectedCount: selectedCount, Utils: Utils}));
-            this.scrollables();
-            this.dom[this.cid].uploader = this.$('.uploader');
-            this.dom[this.cid].uploader.uploader({showMenu: false, maxFiles: -1, menuContainer: this.$('.content'), lang: this.translate.translations, customStockEvent: '_parsestock_image'});
-            this.dom[this.cid].selectCount = this.$('.batch > .dropdown > a .selected .count');
-
-            this.$('.dropdown-toggle.select').dropdown();
-            this.delegateEvents();
-            return this;
-        },
-        _onUploaded: function(event, data) {
-            var files = data.filesData;
-            var toAdd = [];
-            for (var i = 0; i < files.length; i++) {
-                var file = this.fileCollection.create(files[i].response);
-                this.selected[file.id] = false;
-                toAdd.push(file);
-            }
-            this.model.files.add(toAdd, {at: 0});
-            if (!this.model.name) {
-                var date = new Date();
-                var dateString = date;
-                this.model.name = this.translate("newCollectionName") + Utils.dateFormat(this.translate('dateFormat'))
-            }
-            this.model.save();
-        },
-        resetFilter: function(){
-            this.fileCollection.resetFilter();
-          //  this.fileCollection.fetch();
-        },
-        openImageFileSelector: function() {
-            var selectFileView, that = this;
-            this.resetFilter();
-            selectFileView = new FileSelectorDialog({collection: this.fileCollection, allowMultipleSelect: true});
-            this.listenTo(selectFileView, Events.ListViewEvents.CHOOSE_FILE, this._useExistingFile);
-            this.listenTo(selectFileView, Events.DialogEvents.CLOSE, function() {
-                that.stopListening(selectFileView);
-                selectFileView.remove();
-            });
-            selectFileView.imagesOnly();
-            selectFileView.open();
-        },
-        _useExistingFile: function(selected) {
-            var file, id;
-            if (!selected)
-                return;
-            for (id in selected) {
-                file = selected[id];
-                if (!file)
-                    continue;
-                this.model.files.add(file, {at: 0});
-                this.selected[file.id] = false;
-            }
-            if (!this.model.name) {
-                var date = new Date();
-                var dateString = date;
-                this.model.name = this.translate("newCollectionName") + Utils.dateFormat(this.translate('dateFormat'));
-            }
-            this.model.save();
-
-        },
-        _onBack: function() {
-            this.trigger(Events.ChoiceEvents.BACK);
-        },
-    });
-    return ImageGroupView;
-});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselFileEditionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselFileEditionView.js	(révision 11529)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselFileEditionView.js	(nonexistent)
@@ -1,153 +0,0 @@
-define([
-    "jquery",
-    "underscore",
-    "text!../Templates/imageCollection.html",
-    "JEditor/Commons/Events",
-    "JEditor/Commons/Ancestors/Views/DialogView",
-    "JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/ImageGroupView",
-    "JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/FileGroupListView",
-    "collection!JEditor/Commons/Files/Models/FileGroupCollection",
-    "collection!JEditor/Commons/Files/Models/FileGroup",
-    "JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/EditImageInfo"
-            ,
-    "i18n!../nls/i18n"], function($, _, imageCollection, Events, DialogView, ImageGroupView, FileGroupListView, FileGroupCollection, FileGroup, EditImageInfo, translate) {
-    var CarrouselFileEditionView = DialogView.extend({
-        className: 'image-group-edit',
-        events: {
-            'click .action[data-action]': '_onActionClick'
-        },
-        constructor: function(options) {
-            if (!options)
-                var options = {};
-            options.width = 750;
-            options.height = 600;
-            options.buttons = [
-                {
-                    class: 'okay',
-                    text: this.translate("okay"),
-                    click: _.bind(this.onOk, this)
-                },
-                {
-                    text: this.translate("cancel"),
-                    class: 'cancel',
-                    click: _.bind(this.onCancel, this)
-                }
-            ]
-            DialogView.call(this, options);
-            this.on('open close', this.onToggle);
-        },
-        initialize: function() {
-            this._super();
-            this._template = this.buildTemplate(imageCollection, translate);
-            this.childViews = {};
-            this.childViews.editGroupView = new ImageGroupView({model: this.model, dialog: this});
-            this.childViews.imageGroupList = new FileGroupListView({collection: FileGroupCollection.getInstance()});
-            this.listenTo(this.childViews.imageGroupList, Events.ChoiceEvents.SELECT, this.setGroup);
-            this.listenTo(this.model, 'change:length', this.updateImageCount);
-            this.render();
-        },
-        _onActionClick: function(event) {
-            var $target = $(event.currentTarget);
-            var action = $target.data('action');
-            if (this[action] && !$target.hasClass('selected')) {
-                this.dom[this.cid].buttons.removeClass("selected");
-                $target.addClass("selected");
-                this[action]();
-            }
-            return false;
-        },
-        onToggle: function() {
-            this.childViews.editGroupView.updateScrollables();
-        },
-        newgroup: function(e) {
-            this.listenToOnce(this.childViews.imageGroupList, Events.ViewEvents.HIDE, function() {
-                this.stopListening(this.model, 'change:length');
-                var groupCollection = FileGroupCollection.getInstance();
-                this.model = new FileGroup();
-                groupCollection.add(this.model);
-                this.listenTo(this.model, 'change:length', this.updateImageCount);
-                this.updateImageCount();
-                this.childViews.editGroupView.setModel(this.model);
-                this.childViews.editGroupView.render();
-                this.childViews.editGroupView.show();
-                
-            });
-            this.childViews.imageGroupList.hide();
-            return false;
-        },
-        selectgroup: function(e) {
-            if (this.model.length === 0 && !this.model.name)
-                this.model.destroy();
-            this.listenToOnce(this.childViews.editGroupView, Events.ViewEvents.HIDE, function() {
-                this.childViews.imageGroupList.show();
-                this.childViews.imageGroupList.scrollables();
-            });
-            this.childViews.editGroupView.hide();
-            return false;
-        },
-        setGroup: function(view, group) {
-            this.stopListening(this.model, 'change:length');
-            this.model = group;
-            this.listenTo(this.model, 'change:length', this.updateImageCount);
-            this.updateImageCount();
-            this.childViews.editGroupView.setModel(group);
-            this.render();
-            this.dom[this.cid].buttons.removeClass("selected");
-            this.listenToOnce(this.childViews.editGroupView, Events.ViewEvents.SHOW, function() {
-                this.childViews.editGroupView.updateScrollables();
-            });
-            this.childViews.editGroupView.show();
-        },
-        onLengthChange: function() {
-            var fileGroupCollection = FileGroupCollection.getInstance();
-        },
-        updateImageCount: function() {
-            this.dom[this.cid].imageCount.text(this.model.length);
-        },
-        editImage: function(file) {
-            if (!this.childViews.singleFileEdit) {
-                this.childViews.singleFileEdit = new EditImageInfo({model: file});
-            } else {
-                this.childViews.singleFileEdit.setModel(file);
-            }
-        },
-        render: function() {
-            this.undelegateEvents();
-            this._super();
-            this.$el.html(this._template({group: this.model}));
-            this.dom[this.cid].panelContent = this.$('.panel-content');
-            this.dom[this.cid].buttons = this.$('.action[data-action="selectgroup"],.action[data-action="newgroup"]');
-            this.dom[this.cid].newButton = this.dom[this.cid].buttons.filter('[data-action="newgroup"]');
-            this.dom[this.cid].browseButton = this.dom[this.cid].buttons.filter('[data-action="selectgroup"]');
-            this.dom[this.cid].imageCount = this.$('.panel-head>.file-count>.count');
-            this.dom[this.cid].panelContent.append(this.childViews.editGroupView.render().el);
-            this.dom[this.cid].panelContent.append(this.childViews.imageGroupList.render().el);
-            this.childViews.imageGroupList.$el.hide();
-            this.listenToOnce(this.childViews.editGroupView, Events.ChoiceEvents.BACK, function() {
-                this.dom[this.cid].browseButton.removeClass('selected');
-                this.dom[this.cid].browseButton.trigger('click');
-            });
-            this.delegateEvents();
-            if (this.model.length === 0 && !this.model.name) {
-                this.dom[this.cid].newButton.removeClass('selected');
-                this.dom[this.cid].newButton.trigger('click');
-            } else
-                this.childViews.editGroupView.$el.show();
-            return this;
-        },
-        onCancel: function() {
-            if (this.model.length === 0 && !this.model.name)
-                this.model.destroy();
-            this.$el.dialog('close');
-        },
-        onOk: function() {
-            this.trigger(Events.ChoiceEvents.SELECT, this, this.model);
-            var files=this.model.attributes.files.models;
-            
-            var groupCollection = FileGroupCollection.getInstance();
-            groupCollection.add(this.model);
-            this.$el.dialog('close');
-        }
-    });
-    return CarrouselFileEditionView;
-});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselFileEditionView.js
___________________________________________________________________
Deleted: svn:executable
## -1 +0,0 ##
-*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js	(révision 11529)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js	(révision 11530)
@@ -6,7 +6,7 @@
         "JEditor/Commons/Events",
         "JEditor/Commons/Files/Models/FileGroup",
         "JEditor/PagePanel/Contents/Options/Views/AbstractOptionView",
-        "JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselFileEditionView",
+        "JEditor/Commons/Files/Views/CollectionSelectorDialog",
         "JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/OptionTypeLienView",
         "JEditor/App/Views/RightPanelView",
         "JEditor/PagePanel/Contents/Options/Views/OptionCollectionView",
@@ -13,7 +13,7 @@
         "JEditor/PagePanel/Contents/Options/Models/OptionsCollection",
         "JEditor/Commons/Utils"
     ],
-    function($, _, carrouselOption, Events, FileGroup, AbstractOptionView,CarrouselFileEditionView,OptionTypeLienView, Utils) {
+    function($, _, carrouselOption, Events, FileGroup, AbstractOptionView,CollectionSelectorDialog,OptionTypeLienView, Utils) {
         /**
          * Options de la galerie
          * @class CarrouselOptionView
@@ -43,7 +43,7 @@
                 else
                     fileGroup = new FileGroup();
                 viewAttributes = {title: this.translate("editMyCarrousel"), model: fileGroup};
-                fileGroupDialog = new CarrouselFileEditionView(viewAttributes);
+                fileGroupDialog = new CollectionSelectorDialog(viewAttributes);
                 this.listenTo(fileGroupDialog, Events.ChoiceEvents.SELECT, function(view, selected) {
                     this.model.setFileGroup(selected);
                 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/main.js	(révision 11529)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/main.js	(révision 11530)
@@ -3,10 +3,6 @@
         "./CarrouselBlockView",
         "./CarrouselOptionView",
         "./CarrouselStyleOptionView",
-        "./ImageGroupView",
-        "./EditImageInfo",
-        "./FileGroupListView",
-        "./CarrouselFileEditionView",
         "./OptionTypeLienView"
     ],
     function(  
@@ -13,10 +9,6 @@
         CarrouselBlockView,
         CarrouselOptionView,
         CarrouselStyleOptionView,
-        ImageGroupView,
-        EditImageInfo,
-        FileGroupListView,
-        CarrouselFileEditionView,
         OptionTypeLienView
     ){
         var comp={
@@ -23,10 +15,6 @@
         "CarrouselBlockView"        :   CarrouselBlockView,
         "CarrouselOptionView"       :   CarrouselOptionView,
         "CarrouselStyleOptionView"  :   CarrouselStyleOptionView,
-        "ImageGroupView"            :   ImageGroupView,
-        "EditImageInfo"             :   EditImageInfo,
-        "FileGroupListView"         :   FileGroupListView,
-        "CarrouselFileEditionView"  :   CarrouselFileEditionView,
         "OptionTypeLienView"        :   OptionTypeLienView
         };
         return comp;
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieFieldView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieFieldView.js	(révision 11529)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieFieldView.js	(révision 11530)
@@ -9,10 +9,10 @@
     "text!../Templates/galerieField.html",
     "./GalerieFieldView",
     "JEditor/Commons/Files/Models/FileGroup",
-    "JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselFileEditionView",
+    "JEditor/Commons/Files/Views/CollectionSelectorDialog",
     "JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/CollectionView",
     "i18n!../nls/i18n"
-],function (_,View, Events, FieldTemplate, GalerieFieldView, FileGroup, CarrouselFileEditionView, CollectionView, translate) {
+],function (_,View, Events, FieldTemplate, GalerieFieldView, FileGroup, CollectionSelectorDialog, CollectionView, translate) {
     
     var GalerieFieldView=View.extend({
         events: {
@@ -38,7 +38,7 @@
             event.preventDefault();
             fileGroup = new FileGroup();
             viewAttributes = {title: translate("editMyGrid"), model: fileGroup};
-            fileGroupDialog = new CarrouselFileEditionView(viewAttributes);
+            fileGroupDialog = new CollectionSelectorDialog(viewAttributes);
             this.listenTo(fileGroupDialog, Events.ChoiceEvents.SELECT, function(view, selected) {
                 // 
                 this.model.addField( selected );
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-ca/i18n.js	(révision 11529)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-ca/i18n.js	(révision 11530)
@@ -45,7 +45,7 @@
    'landscape'             :   "Paysage",
    'portrait'              :   "Portrait",
    'square'                :   "Carré",
-   "editMyGrid"            :   "Modifier ma grille ",
+   "editMyGrid"            :   "Modifier ma galerie ",
    "DescStyle1"           :  "Textes sous l'image", 
    "DescStyle2"           :  "Texte encadré sur l'image",
    "DescStyle3"           :  "Texte sur l'image" ,
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js	(révision 11529)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js	(révision 11530)
@@ -29,7 +29,7 @@
    "galerieStyleContent" :   "Style de grille",
    "galerieStyleLegend" :   "Appliquez un style de grille",
    "masonryLegend"         :   "Tuiles",
-   "galerieLegend"            :   "Grille",
+   "galerieLegend"            :   "Galerie",
    "galerieNombreImage"       :   "Nombre d'image",
    "galerieNombreImageLegend" :   "Glissez pour ajuster le nombre d'images affichées",
    "galerieStyleAffichage"    :   "Style des images",
@@ -45,7 +45,7 @@
    'landscape'             :   "Paysage",
    'portrait'              :   "Portrait",
    'square'                :   "Carré",
-   "editMyGrid"            :   "Modifier ma grille ",
+   "editMyGrid"            :   "Modifier ma galerie ",
    "DescStyle1"           :  "Textes sous l'image", 
    "DescStyle2"           :  "Texte encadré sur l'image",
    "DescStyle3"           :  "Texte sur l'image",
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/i18n.js	(révision 11529)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/i18n.js	(révision 11530)
@@ -54,7 +54,7 @@
       'landscape'             :   "Landscape",
       'portrait'              :   "Portrait",
       'square'                :   "Square",
-      "editMyGrid"            :   "Edit my Grid",
+      "editMyGrid"            :   "Edit my Gallery",
       "DescStyle1"           :  "Text under the image", 
       "DescStyle2"           :  "Text framed on the image",
       "DescStyle3"           :  "Text on the image",
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridOptionView.js	(révision 11529)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridOptionView.js	(révision 11530)
@@ -6,12 +6,12 @@
         "JEditor/Commons/Events",
         "JEditor/Commons/Files/Models/FileGroup",
         "JEditor/PagePanel/Contents/Options/Views/AbstractOptionView",
-        "JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselFileEditionView",
+        "JEditor/Commons/Files/Views/CollectionSelectorDialog",
         "JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridOptionTypeLienView",
         "JEditor/Commons/Utils",
         "i18n!../nls/i18n"
     ],
-    function($, _, gridOption, Events, FileGroup, AbstractOptionView, CarrouselFileEditionView,GridOptionTypeLienView, Utils) {
+    function($, _, gridOption, Events, FileGroup, AbstractOptionView, CollectionSelectorDialog,GridOptionTypeLienView, Utils) {
         /**
          * Options de la grille
          * @class GridOptionView
@@ -44,7 +44,7 @@
                 else
                     fileGroup = new FileGroup();
                 viewAttributes = {title: this.translate("editMyGrid"), model: fileGroup};
-                fileGroupDialog = new CarrouselFileEditionView(viewAttributes);
+                fileGroupDialog = new CollectionSelectorDialog(viewAttributes);
                 this.listenTo(fileGroupDialog, Events.ChoiceEvents.SELECT, function(view, selected) {
                     this.model.setFileGroup(selected);
                     this.model.fileGroupId=selected.id;
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridStyleOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridStyleOptionView.js	(révision 11529)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridStyleOptionView.js	(révision 11530)
@@ -9,7 +9,7 @@
             "JEditor/Commons/Utils",
             "i18n!../nls/i18n"
         ],
-        function($, _, gridStyleOption, Events, FileGroup, AbstractOptionView,GridStyleOptionTypeLienView, Utils, translate) {
+        function($, _, gridStyleOption, Events, FileGroup, AbstractOptionView, Utils, translate) {
             /**
              * Options de la grille
              * @class GridOptionView
Index: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopOptionView.js	(révision 11529)
+++ src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopOptionView.js	(révision 11530)
@@ -6,10 +6,10 @@
         "JEditor/Commons/Events",
         "JEditor/Commons/Files/Models/FileGroup",
         "JEditor/PagePanel/Contents/Options/Views/AbstractOptionView",
-        "JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselFileEditionView",
+        "JEditor/Commons/Files/Views/CollectionSelectorDialog",
         "JEditor/App/Messages/ClipboardModule", 
     ],
-    function($, _, loopOption, Events, FileGroup, AbstractOptionView, LoopFileEditionView, ClipboardModule) {
+    function($, _, loopOption, Events, FileGroup, AbstractOptionView, CollectionSelectorDialog, ClipboardModule) {
         /**
          * Options de la boucle
          * @class LoopOptionView
@@ -36,7 +36,7 @@
                 else
                     fileGroup = new FileGroup();
                 viewAttributes = {title: this.translate("editMyLoop"), model: fileGroup};
-                fileGroupDialog = new LoopFileEditionView(viewAttributes);
+                fileGroupDialog = new CollectionSelectorDialog(viewAttributes);
                 this.listenTo(fileGroupDialog, Events.ChoiceEvents.SELECT, function(view, selected) {
                     this.model.setFileGroup(selected);
                 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Models/SlideshowOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Models/SlideshowOption.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Models/SlideshowOption.js	(révision 11530)
@@ -0,0 +1,67 @@
+define([
+    "JEditor/Commons/Events",
+    "JEditor/PagePanel/Contents/Options/Models/AbstractOption",
+    "collection!JEditor/Commons/Files/Models/FileGroupCollection",
+    "JEditor/Commons/Files/Models/FileGroup"
+], function(Events,
+        AbstractOption,
+        FileGroupCollection,
+        FileGroup
+        ) {
+    var fileGroupCollection = FileGroupCollection.getInstance();
+    /**
+     * @class GalleryOption
+     * @extends AbstractOption
+     * @type @exp;JEditor@pro;Contents@pro;Options@pro;AbstractOption@call;extend
+     * @property {FileGroup} fileGroup Le groupe de photo utilisé dans la gallerie (non sérialisé dans le JSON)
+     */
+    var SlideshowOption = AbstractOption.extend(
+            /**
+             * @lends GalleryOptions.prototype
+             */
+                    {
+                        defaults: {
+                            priority: 70, 
+                            optionType: 'slideshow', 
+                            title: 'slideshow', 
+                            fileGroup: null, 
+                            Info        :   0,
+                        },
+                        initialize: function() {
+                            this._super();
+                            this.on('change:fileGroup', this.onFileGroupChange);
+                            this.onFileGroupChange();
+                        },
+                        onFileGroupChange: function() {
+                            var fileGroup = this.getFileGroup();
+                            if (this.previousAttributes().fileGroup)
+                                this.stopListening(this.previousAttributes().fileGroup);
+                            if (fileGroup && !(fileGroup instanceof FileGroup))
+                                this.setFileGroup(fileGroupCollection.get(fileGroup));
+                            else if (fileGroup) {
+
+                                this.listenTo(fileGroup, 'sync change', function() {
+                                    this.trigger('change:fileGroup', this, {});
+                                    this.trigger(Events.BackboneEvents.CHANGE, this, {});
+                                });
+                            }
+                            return this;
+                        },
+                        toJSON: function() {
+                            var fileGroup = this.getFileGroup();
+                            var info = this.getInfo();
+                            return {
+                                fileGroup: fileGroup?fileGroup.id:null,
+                                Info : info,
+                                optionType: 'slideshow'
+                            }
+                        },
+                        JSONClone:function(){
+                            return this.toJSON();
+                        }
+                    }
+            );
+            SlideshowOption.SetAttributes(['fileGroup', 'Info']);
+
+            return SlideshowOption;
+        });
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Models/SlideshowStyleOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Models/SlideshowStyleOption.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Models/SlideshowStyleOption.js	(révision 11530)
@@ -0,0 +1,29 @@
+define(["JEditor/PagePanel/Contents/Options/Models/AbstractOption"], function(AbstractOption) {
+    /**
+     * Styles de la galerie
+     * @class SlideshowStyleOption
+     * @extends AbstractOption
+     */
+    var SlideshowStyleOption = AbstractOption.extend(
+            /**
+             * @lends SlideshowStyleOptions.prototype
+             */
+                    {
+                        defaults: {
+                            optionType: 'slideshowStyle', 
+                            priority: 80,
+                            FormatImage :   'landscape',
+                            StyleAffichage  :1
+                        },
+                        toJSON: function() {
+                            return { 
+                                optionType:this.optionType,
+                                FormatImage :   this.FormatImage,
+                                StyleAffichage  :this.StyleAffichage
+                            }
+                        }
+                    }
+            );
+            SlideshowStyleOption.SetAttributes(['FormatImage','StyleAffichage']);
+            return SlideshowStyleOption;
+        });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Models/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Models/main.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Models/main.js	(révision 11530)
@@ -0,0 +1,8 @@
+// this file has been auto-generated by a grunt task, it will be overriden, do not modify it
+define(["./SlideshowOption","./SlideshowStyleOption"],function(SlideshowOption,SlideshowStyleOption){
+    var comp={
+        "SlideshowOption": SlideshowOption,
+        "SlideshowStyleOption":SlideshowStyleOption
+    };
+    return comp;
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/SlideshowBlock.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/SlideshowBlock.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/SlideshowBlock.js	(révision 11530)
@@ -0,0 +1,34 @@
+define( [
+    "JEditor/Commons/Events",
+    "JEditor/PagePanel/Contents/Blocks/Block/Block",
+    "JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Models/SlideshowOption",
+    "JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Models/SlideshowStyleOption"
+], function(Events, Block, SlideshowOption, SlideshowStyleOption) {
+    /**
+     * Bloc de grille
+     * @class SlideshowBlock
+     * @extends Block
+     */
+    var SlideshowBlock = Block.extend(
+            /**
+             * @lends SlideshowBlock
+             */
+                    {
+                        defaults: {
+                            type: 'slideshow', 
+                            contentType: 'slideshowBlock',
+                        },
+                        initialize: function() {
+                            this._super();
+                            if (!this.options.slideshow)
+                                this.options.add(new SlideshowOption());
+                            if(!this.options.slideshowStyle)
+                                this.options.add(new SlideshowStyleOption());
+                        },
+                        
+                    }
+            );
+
+            SlideshowBlock.ICON = 'icon-gallery';
+            return SlideshowBlock;
+        });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Templates/slideshowOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Templates/slideshowOption.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Templates/slideshowOption.html	(révision 11530)
@@ -0,0 +1,59 @@
+<div class="panel-option-container animated gallery-files">
+    <article class="panel-option ">
+        <header>
+            <h3 class="option-name"><span class="icon-image"></span><%= __("slideshowContent")%></h3>
+            <p class="panel-content-legend"><span class="icon-warning" style="color: #d42525;"></span> <span style="color: #d42525;"><%= __("imageWarning")%></span> <%= __("imageWarningMsg")%> <a href="https://www.tineye.com/" target="_blank">TinEye</a>.</p>
+        </header>
+        <div class="option-content">
+            <div class="files-action  btn-gallery-content">
+              <div class="image-count">
+                <span class="image-count-wrap">
+                    <span class="count"><%=fileGroup?fileGroup.length:0%></span><span class="icon-image"></span>
+                    <span class="icon-hexagon-add"></span>
+                </span>
+              </div>
+              <div class="action">
+                  <% if(!fileGroup||fileGroup.length<1) {%>
+                  <div class="intro"><%=__("emptySlideshow")%></div>
+                  <% } %>
+                  <div class="action-desc"><%=__("clickToAddImages")%></div>
+              </div>
+            </div>
+        </div>
+
+        <div class="figcaption-img" id="figcaptionInfo">
+            <p class="panel-legend"><%=__("figcaptionImage")%></p>
+            <% var _id=_.uniqueId('figcaption') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="figcaption" value="1" <%=(Info==1)?"checked":""%>/>
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><%= __("figShowTitle")%></div>
+
+            </label>
+            <% var _id=_.uniqueId('figcaption') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="figcaption" value="2" <%=(Info==2)?"checked":""%>/>
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><%= __("figShowAll")%></div>
+            </label>
+            <% var _id=_.uniqueId('figcaption') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="figcaption" value="0"  <%=(Info==0)?"checked":""%>/>
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><%= __("doNothing")%></div>
+            </label>
+        </div>
+    </article>
+</div>
+<div class="panel-option-container animated gallery-color">
+  
+</div>
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Templates/slideshowStyle.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Templates/slideshowStyle.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Templates/slideshowStyle.html	(révision 11530)
@@ -0,0 +1,97 @@
+<div class="panel-option-container animated">
+    <article class="panel-option animations">
+        <header>
+            <h3 class="option-name"><%= __("ImageFormat")%></h3>
+            <p class="panel-content-legend"><%= __("ImageFormatDesc")%>.</p>
+        </header>
+        <div class="category-content radio-transformed">
+    
+            <div><span class="effect-radio <%=(FormatImage==='landscape')?'active':''%>" id="radio_dark_galleryTemplate954" data-value="landscape" data-helper="landscape"><span class="helper"><span class="help"><%=__("landscape")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-transition-alpha"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
+            
+            <div><span class="effect-radio <%=(FormatImage==='portrait')?'active':''%>" id="radio_dark_galleryTemplate955" data-value="portrait" data-helper="portrait"><span class="helper"><span class="help"><%=__("portrait")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-image-portrait"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
+            <div><span class="effect-radio <%=(FormatImage==='square')?'active':''%>" id="radio_dark_galleryTemplate955" data-value="square" data-helper="square"><span class="helper"><span class="help"><%=__("square")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-image-square"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
+            
+    </div>
+    </article>
+</div>
+<div class="panel-option-container animated">
+    <article class="panel-option background-color">
+        <header>
+            <h3 class="option-name"><%=__("slideshowStyleAffichage")%></h3>
+            <p class="panel-content-legend"><%=__("slideshowStyleAffichageDesc")%></p>
+        </header>
+        <div class="option-content colors">
+            <%  var _id=_.uniqueId('slideshowStyleAffichage');
+            %>
+                <input type="radio" class="select-box" name="AffichageSlideshowStyle" value="1" id="<%=_id %>" <%=(StyleAffichage==1)?'checked':''%>>
+                   <label for="<%=_id %>">
+                    <div class="wrapper">
+                        <div class="vertical-wrap">
+                            <span class="icon-wrapper">
+                                <span class="icon-collection-style1"></span>
+                            </span>
+                            <span class="name"><%= __("Style")%> 1</span>
+                            <span class="desc"><%= __("DescStyle1")%></span>
+                        </div>
+                    </div>
+                </label>
+                
+                <%  var _id=_.uniqueId('slideshowStyleAffichage');%>
+                <input type="radio" class="select-box" name="AffichageSlideshowStyle" value="2" id="<%=_id %>" <%=(StyleAffichage==2)?'checked':''%>>
+                   <label for="<%=_id %>">
+                    <div class="wrapper">
+                        <div class="vertical-wrap">
+                            <span class="icon-wrapper">
+                                <span class="icon-collection-style2"></span>
+                            </span>
+                            <span class="name"><%= __("Style")%> 2</span>
+                            <span class="desc"><%= __("DescStyle2")%></span>
+                        </div>
+                    </div>
+                </label>
+                
+                <%  var _id=_.uniqueId('slideshowStyleAffichage');%>
+                <input type="radio" class="select-box"  name="AffichageSlideshowStyle" value="3" id="<%=_id %>" <%=(StyleAffichage==3)?'checked':''%>>
+                   <label for="<%=_id %>">
+                    <div class="wrapper">
+                        <div class="vertical-wrap">
+                            <span class="icon-wrapper">
+                                <span class="icon-collection-style3"></span>
+                            </span>
+                            <span class="name"><%= __("Style")%> 3</span>
+                            <span class="desc"><%= __("DescStyle3")%></span>
+                        </div>
+                    </div>
+                </label>
+
+                <%  var _id=_.uniqueId('slideshowStyleAffichage');%>
+                <input type="radio" class="select-box"  name="AffichageSlideshowStyle" value="4" id="<%=_id %>" <%=(StyleAffichage==4)?'checked':''%>>
+                   <label for="<%=_id %>">
+                    <div class="wrapper">
+                        <div class="vertical-wrap">
+                            <span class="icon-wrapper">
+                                <span class="icon-collection-style4"></span>
+                            </span>
+                            <span class="name"><%= __("Style")%> 4</span>
+                            <span class="desc"><%= __("DescStyle4")%></span>
+                        </div>
+                    </div>
+                </label>
+
+                <%  var _id=_.uniqueId('slideshowStyleAffichage');%>
+                <input type="radio" class="select-box"  name="AffichageSlideshowStyle" value="5" id="<%=_id %>" <%=(StyleAffichage==5)?'checked':''%>>
+                   <label for="<%=_id %>">
+                    <div class="wrapper">
+                        <div class="vertical-wrap">
+                            <span class="icon-wrapper">
+                                <span class="icon-collection-style5"></span>
+                            </span>
+                            <span class="name"><%= __("Style")%> 5</span>
+                            <span class="desc"><%= __("DescStyle5")%></span>
+                        </div>
+                    </div>
+                </label>
+                
+        </div>
+    </article>
+</div>
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Views/SlideshowBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Views/SlideshowBlockView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Views/SlideshowBlockView.js	(révision 11530)
@@ -0,0 +1,77 @@
+define([
+    "jquery",
+    "underscore",
+    "JEditor/Commons/Events",
+    "JEditor/PagePanel/Contents/Blocks/Block/Views/BlockView",
+    "JEditor/Commons/Files/Models/FileGroup",
+    "less",
+    //not in params
+    "owlCarouselSync",
+], function($, _, Events, BlockView, FileGroup, less) {
+    var SlideshowBlockView = BlockView.extend({
+        attributes: {
+            tabindex: 0,
+            class: "slideshowblock block"
+        },
+        initialize: function() {
+            this._super();
+            this.lessParser = new less.Parser();
+        },
+        /**
+         *
+         * @param {Object} templateParams
+         * @returns {String}
+         */
+        render: function() {
+            this.undelegateEvents()
+            this._super();
+            this.triggerUpdate();
+            this.delegateEvents()
+            return this;
+        },
+        /**
+         *
+         * @returns {GalleryTemplate}
+         */
+        getTemplate: function() {
+            return this.getOptions().galleryTemplate.template;
+        },
+        getOptions: function() {
+            return this.model.options.gallery.design.options;
+        },
+        getFileGroup: function() {
+            return this.model.options.slideshow.fileGroup;
+        },
+       
+        getEffectsClass: function() {
+            var designOptions = this.getOptions();
+            var classes = [
+                designOptions.galleryStyle.theme
+            ];
+            if (designOptions.galleryAnimations) {
+                if (designOptions.galleryAnimations.transition)
+                    classes.push(designOptions.galleryAnimations.transition);
+            }
+            return classes.join(' ');
+        }, 
+        renderOptions: function(model, options) {
+            var template, uid, css, html, fileGroup;
+            fileGroup = this.getFileGroup();
+            if ((fileGroup instanceof FileGroup) && fileGroup.length != 0) {
+                this.dom[this.cid].content.html(
+                    '<div class="exist-gallery"><div><span class="icon-gallery-arrow-outer"></span><span class="count">&nbsp;'+fileGroup.length+'</span></div></div>');
+            }else{
+                this.dom[this.cid].content.html('<div class="empty-gallery"><span class="icon-gallery-arrow-outer"></span></div>');
+            }
+            return this;
+
+        },
+        getFileDesc: function(file) {
+            return file.desc[this.app.currentPanel.currentLang.id];
+        },
+        getFileTitle: function(file) {
+            return file.title[this.app.currentPanel.currentLang.id];
+        }
+    });
+    return SlideshowBlockView;
+});
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Views/SlideshowOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Views/SlideshowOptionView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Views/SlideshowOptionView.js	(révision 11530)
@@ -0,0 +1,97 @@
+define(
+    [
+        "jquery",
+        "underscore",
+        "text!../Templates/slideshowOption.html",
+        "JEditor/Commons/Events",
+        "JEditor/Commons/Files/Models/FileGroup",
+        "JEditor/PagePanel/Contents/Options/Views/AbstractOptionView",
+        "JEditor/Commons/Files/Views/CollectionSelectorDialog",
+        "JEditor/App/Views/RightPanelView",
+        "JEditor/PagePanel/Contents/Options/Views/OptionCollectionView",
+        "JEditor/PagePanel/Contents/Options/Models/OptionsCollection",
+        "JEditor/Commons/Utils"
+    ],
+    function($, _, slideshowOption, Events, FileGroup, AbstractOptionView,CollectionSelectorDialog, Utils) {
+        /**
+         * Options de la galerie
+         * @class SlideshowOptionView
+         * @extends AbstractOptionView
+         */
+        var SlideshowOptionView = AbstractOptionView.extend({
+            optionType: 'slideshow',
+            events: {
+                'click .files-action'   :   'openFileGroupDialog',
+                'click input[type=radio]':'_onChangeRadio',
+                'change input[type="checkbox"].blue-bg': '_onChangeArrow'
+                },
+            className: 'slideshow-option-home panel-content',
+            initialize: function() {
+                this._super();
+                this._template = this.buildTemplate(slideshowOption, this.translate);
+                this.listenTo(this.model, 'change:fileGroup', this.onFileGroupChange);
+            },
+            openFileGroupDialog: function() {
+                var fileGroup;
+                var viewAttributes;
+                var fileGroupDialog;
+                if (this.model.fileGroup)
+                    fileGroup = this.model.fileGroup;
+                else
+                    fileGroup = new FileGroup();
+                viewAttributes = {title: this.translate("editMySlideshow"), model: fileGroup};
+                fileGroupDialog = new CollectionSelectorDialog(viewAttributes);
+                this.listenTo(fileGroupDialog, Events.ChoiceEvents.SELECT, function(view, selected) {
+                    this.model.setFileGroup(selected);
+                });
+                this.listenTo(fileGroupDialog, Events.DialogEvents.CLOSE, function() {
+                    this.stopListening(fileGroupDialog);
+                    fileGroupDialog.remove();
+                });
+                fileGroupDialog.open();
+                return this;
+            },
+            onFileGroupChange: function(_fileGroup, options) {
+                var oldFilegroup = this.model.previousAttributes().fileGroup;
+                var fileGroup = this.model.getFileGroup();
+                if (oldFilegroup)
+                    this.stopListening(oldFilegroup, 'change:length');
+                if (fileGroup && !fileGroup.length && !fileGroup.name) {
+                    var date = new Date();
+                    var options = { year: 'numeric', month: 'numeric', day: 'numeric',hour: 'numeric', minute: 'numeric', second: 'numeric' };
+                    var dateString = date.toLocaleDateString('fr-FR', options);
+                    
+                    fileGroup.name = this.translate("newGallery")+dateString ;//+ Utils.dateFormat(this.translate('dateFormat'));
+                    fileGroup.save();
+                    this.listenTo(this.model.getFileGroup(), 'change:length', this.render);
+                }
+                else if (fileGroup)
+                    this.listenTo(this.model.getFileGroup(), 'change:length', this.render);
+                this.render();
+                return this;
+            },
+            render: function() {
+                this.undelegateEvents();
+                if (this.dom[this.cid].dialogTrigger)
+                    this._fileGroupDialog.detach(this.dom[this.cid].dialogTrigger[0]);
+              
+                this.$el.empty();
+                this.$el.html(this._template(this.model));
+                this.scrollables({
+                    advanced:{ autoScrollOnFocus: false }
+                });
+                this.delegateEvents();
+                return this;
+            },
+            _onChangeArrow: function(event){
+                this.model.Arrow=!this.model.Arrow;
+            },
+            _onChangeRadio: function(event){
+                var name = event.currentTarget.name;
+                if (name ==="figcaption"){
+                    this.model.Info= event.currentTarget.value;
+                }
+            },
+        });
+        return SlideshowOptionView;
+    });
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Views/SlideshowStyleOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Views/SlideshowStyleOptionView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Views/SlideshowStyleOptionView.js	(révision 11530)
@@ -0,0 +1,59 @@
+define([
+    "underscore",
+    "jquery",
+    "text!../Templates/slideshowStyle.html",
+    "JEditor/PagePanel/Contents/Options/Views/AbstractOptionView",
+    "i18n!../nls/i18n",
+], function(_, $, slideshowStyle,  AbstractOptionView, translate) {
+    /**
+     * Vue des options concernant le style de la diaporama
+     * @class SlideshowStyleOptionView
+     * @extends JEditor.Contents.Options.View.AbstractOptionView
+     */
+    var SlideshowStyleOptionView = AbstractOptionView.extend(
+            /** 
+             * @lends SlideshowStyleOptionView.prototype
+             */
+                    {
+                        optionType: 'slideshowStyle',
+                        tagName: "div",
+                        className: "gallery-template-option galleryStyle panel-content ",
+                        events: {
+                            'change input[type="radio"].select-box': '_onStyleAffichageChange',
+                            'click .effect-radio': '_onChangeFormatImage'
+                        },
+                        /**
+                         * initialise l'objet
+                         */
+                        initialize: function() {
+                            this._super();
+                            this.template = this.buildTemplate(slideshowStyle, translate);
+                        },
+                        _onChangeFormatImage:function(event){
+                           this.$(".effect-radio").removeClass("active");
+                            var $target = $(event.currentTarget);
+                            $target.addClass("active");
+                            var value = $target.attr("data-value");
+                            this.model.FormatImage=value;
+                        },  
+                        _onStyleAffichageChange :function(event){
+                            var $target = $(event.currentTarget);
+                            this.model.StyleAffichage = $target.val();
+                        },
+                        /**
+                         * actualise l'affichage de la vue
+                         */
+                        render: function() {
+                            var templateVars = {
+                                FormatImage :this.model.FormatImage,
+                                StyleAffichage:this.model.StyleAffichage
+                            };
+                            this.$el.html(this.template(templateVars));
+                            this.$('.category-content').radio();
+                            this.scrollables();
+                            return this;
+                        }
+                    }
+            );
+            return SlideshowStyleOptionView;
+        });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Views/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Views/main.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/Views/main.js	(révision 11530)
@@ -0,0 +1,9 @@
+// this file has been auto-generated by a grunt task, it will be overriden, do not modify it
+define(["./SlideshowBlockView","./SlideshowOptionView","./SlideshowStyleOptionView"],function(SlideshowBlockView, SlideshowOptionView, SlideshowStyleOptionView){
+    var comp={
+        "SlideshowBlockView":SlideshowBlockView,
+        "SlideshowOptionView":SlideshowOptionView,
+        "SlideshowStyleOptionView":SlideshowStyleOptionView
+    };
+    return comp;
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/main.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/main.js	(révision 11530)
@@ -0,0 +1,7 @@
+// this file has been auto-generated by a grunt task, it will be overriden, do not modify it
+define(["./SlideshowBlock","./Views/main","./Models/main","i18n!./nls/i18n"],function(SlideshowBlock,Views,Models,i18n){
+    SlideshowBlock.Models=Models;
+    SlideshowBlock.Views=Views;
+    SlideshowBlock.i18n=i18n;
+     return SlideshowBlock;
+ });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/de-de/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/de-de/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/de-de/i18n.js	(révision 11530)
@@ -0,0 +1 @@
+define({});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/de-de/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/en-au/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/en-au/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/en-au/i18n.js	(révision 11530)
@@ -0,0 +1 @@
+define({});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/en-au/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/en-us/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/en-us/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/en-us/i18n.js	(révision 11530)
@@ -0,0 +1 @@
+define({});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/en-us/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/es-es/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/es-es/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/es-es/i18n.js	(révision 11530)
@@ -0,0 +1 @@
+define({});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/es-es/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-ca/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-ca/i18n.js	(révision 11530)
@@ -0,0 +1,59 @@
+define({
+    "BLOCK_NAME":"Diaporama",
+    "pages":"Pages",
+    "cancel":"Cancel",
+    "slideshow":"Diaporama",
+    "slideshowOption":"Options",
+    "slideshowStyle":"Style",
+    "Style" : "Style" ,
+    "slideshowBlockOption":"Options du diaporama",
+    "slideshowContent":"Contenu du diaporama",
+    "imageWarningMsg":   "les images trouvées sur internet ne sont généralement pas libres d’utilisation. Pour vous aider à déterminer si une image est protégée par des droits d’auteur vous pouvez utiliser le service",  
+    "imageWarning":   "Attention",
+    "emptyLoop":   "Votre Diaporama est vide",
+    "clickToAddImages": "Cliquez ici pour ajouter<br/>des images.",  
+    "figcaptionImage":"Afficher les informations des images",
+    "figShowTitle" : "Afficher le titre",
+    "figShowAll" : "Afficher le titre et la description",
+    "doNothing":"Ne rien afficher",
+    "selectClickActionImage":   "Sélectionnez l'action souhaitée au clic sur l’image.",
+    "editMyCarrousel":"Éditer ma diaporama",
+    "imageInGroup":"Images <br/>dans ma galerie",
+    "newImageGroup": "Créer une nouvelle <br/> collection d'images.",
+    "useExistingGroup":"Utiliser une collection <br/>d'images existante.",
+    "newCollectionName":"Nouvelle collection",
+    "backToCollectionList":"Retour à la liste des collections",
+    "addImageToCollection"  :   "Ajouter des images à ma collection",
+    "howToAddImages":   "Cliquez ici ou glissez-déposez de nouvelles images pour les ajouter à votre collection.",
+    "okay":   "Valider",
+    "cancel":   "Annuler",
+    "editMyGallery":   "Éditer ma diaporama",
+    "newGallery":   "Nouvelle diaporama",
+    "dateFormat":   "Y-m-d H:i:s",
+    "emptyNewCollection":   "Votre nouvelle collection d’images est vide.",
+    "edit":   "Éditer",
+    "delete":   "Supprimer",
+    "allF":   "Toutes",
+    "noneF":   "Aucune",
+    "selectTypeLink":   "Selectionnez le type de lien souhaitée",
+    "LinkImage":   "Ajouter le lien sur l'image",
+    "LinkText":   "Ajouter le lien sur le texte",
+    "ButtonReadMore":   "Ajouter un bouton 'Consulter la page'",
+    "slideshowHeight":   "Nombre d'image",
+    "slideshowHeightDesc":   "Glissez pour ajuster le nombre d'images affichées",
+    "slideshowStyleAffichage"   :   "Style des images",
+    "slideshowStyleAffichageDesc"   :   "Appliquez un style aux images",
+    "ImageFormat":   "Format de l'image",
+    "ImageFormatDesc":   "Appliquez un format d'image au diaporama",
+    'landscape':   "Paysage",
+    'portrait':   "Portrait",
+    'square':   "Carré",
+    "arrowImage":   "Afficher des flèches de navigation",
+    "ShowArrow":   "Afficher les boutons de navigation",
+    "emptyCollection":   "Votre collection d’images est vide.",
+    "DescStyle1":  "Textes sous l'image", 
+    "DescStyle2":  "Texte encadré sur l'image",
+    "DescStyle3":  "Texte sur l'image",
+    "DescStyle4":  "Textes sous l'image avec bordures",
+    "DescStyle5":  "Textes sous l'image avec images arrondies"  
+});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-ca/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-fr/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-fr/i18n.js	(révision 11530)
@@ -0,0 +1,59 @@
+define({
+    "BLOCK_NAME":"Diaporama",
+    "pages":"Pages",
+    "cancel":"Cancel",
+    "slideshow":"Diaporama",
+    "slideshowOption":"Options",
+    "slideshowStyle":"Style",
+    "Style" : "Style" ,
+    "slideshowBlockOption":"Options du diaporama",
+    "slideshowContent":"Contenu du diaporama",
+    "imageWarningMsg":   "les images trouvées sur internet ne sont généralement pas libres d’utilisation. Pour vous aider à déterminer si une image est protégée par des droits d’auteur vous pouvez utiliser le service",  
+    "imageWarning":   "Attention",
+    "emptyLoop":   "Votre Diaporama est vide",
+    "clickToAddImages": "Cliquez ici pour ajouter<br/>des images.",  
+    "figcaptionImage":"Afficher les informations des images",
+    "figShowTitle" : "Afficher le titre",
+    "figShowAll" : "Afficher le titre et la description",
+    "doNothing":"Ne rien afficher",
+    "selectClickActionImage":   "Sélectionnez l'action souhaitée au clic sur l’image.",
+    "editMySlideshow":"Éditer ma diaporama",
+    "imageInGroup":"Images <br/>dans ma galerie",
+    "newImageGroup": "Créer une nouvelle <br/> collection d'images.",
+    "useExistingGroup":"Utiliser une collection <br/>d'images existante.",
+    "newCollectionName":"Nouvelle collection",
+    "backToCollectionList":"Retour à la liste des collections",
+    "addImageToCollection"  :   "Ajouter des images à ma collection",
+    "howToAddImages":   "Cliquez ici ou glissez-déposez de nouvelles images pour les ajouter à votre collection.",
+    "okay":   "Valider",
+    "cancel":   "Annuler",
+    "editMyGallery":   "Éditer ma diaporama",
+    "newGallery":   "Nouvelle diaporama",
+    "dateFormat":   "Y-m-d H:i:s",
+    "emptyNewCollection":   "Votre nouvelle collection d’images est vide.",
+    "edit":   "Éditer",
+    "delete":   "Supprimer",
+    "allF":   "Toutes",
+    "noneF":   "Aucune",
+    "selectTypeLink":   "Selectionnez le type de lien souhaitée",
+    "LinkImage":   "Ajouter le lien sur l'image",
+    "LinkText":   "Ajouter le lien sur le texte",
+    "ButtonReadMore":   "Ajouter un bouton 'Consulter la page'",
+    "slideshowHeight":   "Nombre d'image",
+    "slideshowHeightDesc":   "Glissez pour ajuster le nombre d'images affichées",
+    "slideshowStyleAffichage"   :   "Style des images",
+    "slideshowStyleAffichageDesc"   :   "Appliquez un style aux images",
+    "ImageFormat":   "Format de l'image",
+    "ImageFormatDesc":   "Appliquez un format d'image au diaporama",
+    'landscape':   "Paysage",
+    'portrait':   "Portrait",
+    'square':   "Carré",
+    "arrowImage":   "Afficher des flèches de navigation",
+    "ShowArrow":   "Afficher les boutons de navigation",
+    "emptyCollection":   "Votre collection d’images est vide.",
+    "DescStyle1":  "Textes sous l'image", 
+    "DescStyle2":  "Texte encadré sur l'image",
+    "DescStyle3":  "Texte sur l'image",
+    "DescStyle4":  "Textes sous l'image avec bordures",
+    "DescStyle5":  "Textes sous l'image avec images arrondies"  
+});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/fr-fr/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/i18n.js	(révision 11530)
@@ -0,0 +1,63 @@
+define({ 
+    "root": {
+        "BLOCK_NAME":"Slideshow",
+        "pages":"Pages",
+        "cancel":"Cancel",
+        "slideshow":"Slideshow",
+        "slideshowOption":"Options",
+        "slideshowStyle":"Style",
+        "Style" : "Style" ,
+        "slideshowBlockOption":"Slideshow options",
+        "slideshowContent":"Content of the slideshow",
+        "imageWarningMsg":"Images found on the internet are generally not free to use. To help you determine if an image is copyrighted you can use the",  
+        "imageWarning": "Warning",
+        "emptySlideshow":"Your slideshow is empty",
+        "clickToAddImages" : "Click here to add<br/>images.",  
+        "figcaptionImage":"Display image information",
+        "figShowTitle" : "Display title",
+        "figShowAll" : "Display title and description",
+        "doNothing":"Do not display anything",
+        "selectClickActionImage":   "Select the desired action by clicking on the image.",
+        "editMySlideshow"       :   "Edit my slideshow",
+        "imageInGroup"          :   "Images <br/>in my gallery",
+        "newImageGroup"         :   "Create a new <br/> collection of images.",
+        "useExistingGroup"      :   "Use an existing collection <br/>of images.",
+        "newCollectionName"     :   "New collection",
+        "backToCollectionList"  :   "Back to the list of collections",
+        "addImageToCollection"  :   "Add images to my collection",
+        "howToAddImages"        :   "Click here or drag and drop new images to add them to your collection.",
+        "okay"                  :   "Confirm",
+        "cancel"                :   "Cancel",
+        "editMyGallery"         :   "Edit my slideshow",
+        "newGallery"            :   "New slideshow",
+        "dateFormat"            :   "Y-m-d H:i:s",
+        "emptyNewCollection"    :   "Your new image collection is empty.",
+        "edit"                  :   "Edit",
+        "delete"                :   "Delete",
+        "allF"                  :   "All",
+        "noneF"                 :   "None",
+        "selectTypeLink"        :   "Select the type of link you want",
+        "LinkImage"             :   "Add link to image",
+        "LinkText"              :   "Add link to text",
+        "ButtonReadMore"        :   "Add a button 'Visit the page'",
+        "slideshowHeight"       :   "Number of images",
+        "slideshowHeightDesc"   :   "Drag to adjust the number of images displayed",
+        "slideshowStyleAffichage"   :    "Style of the images",
+        "slideshowStyleAffichageDesc"   :   "Apply a style to the images",
+        "ImageFormat"           :   "Image format",
+        "ImageFormatDesc"       :   "Apply an image format to the slideshow",
+        'landscape'             :   "Landscape",
+        'portrait'              :   "Portrait",
+        'square'                :   "Square",
+        "arrowImage"            :   "Display navigation arrows",
+        "ShowArrow"             :   "Show navigation buttons",
+        "emptyCollection"      :  "Your image collection is empty",
+        "DescStyle1"           :  "Text under the image", 
+        "DescStyle2"           :  "Text framed on the image",
+        "DescStyle3"           :  "Text on the image",
+        "DescStyle4"           :  "Text under the image with borders",
+        "DescStyle5"           :  "Text under the image with rounded pictures"  
+    },
+     "fr-fr":true, 
+     "fr-ca":true 
+    })
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/SlideshowBlock/nls/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json	(révision 11529)
+++ src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json	(révision 11530)
@@ -19,6 +19,6 @@
  "CarrouselBlock":"CarrouselBlock",
  "GalerieBlock":"GalerieBlock",
  "CompareBlock":"CompareBlock",
- "LoopBlock":"LoopBlock"
-
+ "LoopBlock":"LoopBlock",
+ "Slideshow":"Slideshow"
 }
Index: src/js/build.js
===================================================================
--- src/js/build.js	(révision 11529)
+++ src/js/build.js	(révision 11530)
@@ -53,6 +53,7 @@
         "JEditor/PagePanel/Contents/Blocks/MapBlock",
         "JEditor/PagePanel/Contents/Blocks/SeparatorBlock",
         "JEditor/PagePanel/Contents/Blocks/SiteMapBlock",
+        "JEditor/PagePanel/Contents/Blocks/SlideshowBlock",
         "JEditor/PagePanel/Contents/Blocks/TextBlock",
         "JEditor/PagePanel/Contents/Blocks/VideoBlock",
         "JEditor/PagePanel/Contents/Blocks/VideoLinkeoBlock",
Index: src/js/main.js
===================================================================
--- src/js/main.js	(révision 11529)
+++ src/js/main.js	(révision 11530)
@@ -58,6 +58,7 @@
     "JEditor/PagePanel/Contents/Blocks/MapBlock",
     "JEditor/PagePanel/Contents/Blocks/SeparatorBlock",
     "JEditor/PagePanel/Contents/Blocks/SiteMapBlock",
+    "JEditor/PagePanel/Contents/Blocks/SlideshowBlock",
     "JEditor/PagePanel/Contents/Blocks/TextBlock",
     "JEditor/PagePanel/Contents/Blocks/VideoBlock",
     "JEditor/PagePanel/Contents/Blocks/ButtonBlock",
