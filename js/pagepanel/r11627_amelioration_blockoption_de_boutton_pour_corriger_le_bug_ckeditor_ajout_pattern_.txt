Revision: r11627
Date: 2023-11-27 08:55:49 +0300 (lts 27 Nov 2023) 
Author: sraz<PERSON><PERSON>lis<PERSON> 

## Commit message
amelioration blockoption de boutton pour corriger le bug ckeditor(ajout pattern singleton pour model params)

## Files changed

## Full metadata
------------------------------------------------------------------------
r11627 | srazanandralisoa | 2023-11-27 08:55:49 +0300 (lts 27 Nov 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/App/App.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/SvgCollection.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Models/Params.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/ParamsPanel.js

amelioration blockoption de boutton pour corriger le bug ckeditor(ajout pattern singleton pour model params)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/App/App.js
===================================================================
--- src/js/JEditor/App/App.js	(révision 11626)
+++ src/js/JEditor/App/App.js	(révision 11627)
@@ -14,6 +14,7 @@
   "JEditor/App/Router",
   "JEditor/App/Cookie",
   "JEditor/Commons/Languages/Models/Language",
+  "JEditor/ParamsPanel/Models/Params",
   "collection!JEditor/Commons/Languages/Models/UILanguageList",
   "JEditor/Commons/Languages/Views/LanguagesDropDown",
   "i18n!./nls/i18n",
@@ -24,7 +25,7 @@
   "jqueryPlugins/affix",
   "moment-fr","moment-fr-ca","moment-en-au","moment-en-ca"
 ], function($, _, Konami, User, Events, BabblerView, Model, Collection, View, MessageDelegate, MessageView,
-  Config, Router, Cookie, Language, UILanguageList, LanguagesDropDown,
+  Config, Router, Cookie, Language, Params, UILanguageList, LanguagesDropDown,
   translate, applicationTpl, loadingTemplate,moment) {
   /**
    * CLass de l'application de base
@@ -131,6 +132,7 @@
       coreInit: function() {
         this.messageDelegate = MessageDelegate.getInstance();
         this.params = Config.getInstance();
+        this.paramsSetting = Params.getInstance();
         this.router = Router.getInstance();
         this.cookie = new Cookie();
         this.checkSessionTimeOut();
@@ -138,6 +140,7 @@
         this.listenToOnce(this.params, Events.BackboneEvents.SYNC, this.initView);
         this.listenTo(this.router, Events.BackboneEvents.ROUTE, this._onRoute);
         this.params.fetch();
+        this.paramsSetting.fetch();
       },
       //step 1
       initUser: function() {
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/SvgCollection.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/SvgCollection.js	(révision 11626)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/SvgCollection.js	(révision 11627)
@@ -41,23 +41,16 @@
                     },
 
                     fetchIcons: function(callback){
-                        var self = this;
-                        this.params = new Params();
-                        this.params.fetch().done((function(data) {
-                            self.name = data.IconsCollection || 'outline';
-                            self.fetch({
-                                success: function(collection, response, options) {
-                                    callback(null, response);
-                                },
-                                error: function(collection, response, options) {
-                                  callback(new Error('Failed to fetch SVG'));
-                                }
-                              });
-            
-                        })).fail(function(error) {
-                            console.log(error);
+                        this.params = Params.getInstance();
+                        this.name =  this.params.attributes.IconsCollection || 'outline';
+                        this.fetch({
+                            success: function(collection, response, options) {
+                                callback(null, response);
+                            },
+                            error: function(collection, response, options) {
+                                callback(new Error('Failed to fetch SVG'));
+                            }
                         });
-            
                     },
 
 
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js	(révision 11626)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js	(révision 11627)
@@ -60,7 +60,7 @@
                             this.$(".blk-button__label").css('margin-top','');
                             var svgName = this.model.options.ButtonStyleOption.icon;
                             // fetch icon content
-                            if(svgName !== ""  ){
+                            if(svgName !== "" && model.attributes.optionType === "ButtonStyleOption" ){
                                 var svgCollectionObj = new SvgCollection({"svgName":svgName});
                                 svgCollectionObj.fetchIcons(_.bind(function(error, svg) {
                                     if (error) {
Index: src/js/JEditor/ParamsPanel/Models/Params.js
===================================================================
--- src/js/JEditor/ParamsPanel/Models/Params.js	(révision 11626)
+++ src/js/JEditor/ParamsPanel/Models/Params.js	(révision 11627)
@@ -69,6 +69,11 @@
             Favicon: null,
             IconsCollection: "outline"
         },
+        constructor: function () {
+            if (arguments.callee.caller !== Params.getInstance)
+                throw new TypeError("Params est un singleton et ne peut être instancié");
+            Model.apply(this, arguments);
+        },
         initialize: function () {
             this._super();
             this.on("change:facebookUrl change:twitterUrl change:pinterestUrl change:mybusinessUrl change:youtubeUrl change:linkedinUrl change:viadeoUrl change:instagramUrl change:skypeUrl change:theforkUrl change:tiktokUrl change:tripadvisorUrl change:wazeUrl change:whatsappUrl change:slideshareUrl", this.onSocialNetworkUrlChange);
@@ -192,6 +197,21 @@
         SettingsEvents: {
             SOCIAL_NETWORK_CHANGE: "change:social_networks"
         }
-    })
+    }),
+     /**
+     * @static
+     * @private
+     */
+     Params.instance = null;
+     /**
+      * pour récupérer l'instance de la classe
+      * @returns {Config} l'instance de la classe
+      */
+     Params.getInstance = function () {
+         if (this.instance === null){
+            this.instance = new Params();
+         }
+         return this.instance;
+     }
     return Params;
 });
Index: src/js/JEditor/ParamsPanel/ParamsPanel.js
===================================================================
--- src/js/JEditor/ParamsPanel/ParamsPanel.js	(révision 11626)
+++ src/js/JEditor/ParamsPanel/ParamsPanel.js	(révision 11627)
@@ -52,7 +52,7 @@
                                 initialize: function () {
                                     this._super();
                                     this._template = this.buildTemplate(ParamsPanelTemplate, translate);
-                                    this.params = new Params();
+                                    this.params = Params.getInstance();
                                     this.menuEntries = {
                                         MarqueClient:{
                                             object: new MarqueClientView({model:this.params}),
