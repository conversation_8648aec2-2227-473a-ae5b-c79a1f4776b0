Revision: r13300
Date: 2024-10-22 07:23:39 +0300 (tlt 22 Okt 2024) 
Author: traj<PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist : block loop

## Files changed

## Full metadata
------------------------------------------------------------------------
r13300 | trajaonarivelo | 2024-10-22 07:23:39 +0300 (tlt 22 Okt 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopStyleOptionView.js

wishlist : block loop
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopStyleOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopStyleOptionView.js	(révision 13299)
+++ src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopStyleOptionView.js	(révision 13300)
@@ -123,6 +123,9 @@
                             {
                                 $('.reverse-bg').addClass('disableSolid');
                                 $('input[name="reverse"]').attr("disabled", "disabled").removeAttr("checked");
+
+                                // cacher bloc vitesse 
+                                $('.loop-speed').hide();
                             }
                            
                             this.dom[this.cid].slider = this.$('.loop-speed .slider');
