Revision: r10006
Date: 2022-12-16 15:16:21 +0300 (zom 16 Des 2022) 
Author: norajaonarivelo 

## Commit message
wishlist Ideo3.2:New Block Carrousel -Corection -bug affichage BO et - bug sur affichage du carrousel dans le front 

## Files changed

## Full metadata
------------------------------------------------------------------------
r10006 | norajaonarivelo | 2022-12-16 15:16:21 +0300 (zom 16 Des 2022) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/dev/#librairies/module/GestionContenuUtil/src/GestionContenuUtil/Core/BlockModuleHandlers/BlockCarrouselHandler.php
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselOption.html

wishlist Ideo3.2:New Block Carrousel -Corection -bug affichage BO et - bug sur affichage du carrousel dans le front 
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselOption.html	(révision 10005)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselOption.html	(révision 10006)
@@ -72,7 +72,7 @@
                 <span class="icon  icon-radio-active"></span>
                 <span class="icon  icon-radio-inactive"></span>
               </div>
-              <div class="block-label-radio"><span class="icon-find"></span><%= __("ActionZoom")%></div>
+              <div class="block-label-radio"><span class="icon-find"></span>&emsp;<%= __("ActionZoom")%></div>
 
             </label>
             <% var _id=_.uniqueId('link_action') %>
@@ -82,7 +82,7 @@
                 <span class="icon  icon-radio-active"></span>
                 <span class="icon  icon-radio-inactive"></span>
               </div>
-              <div class="block-label-radio"><span class="icon-link"></span><%= __("ActionPartner")%></div>
+              <div class="block-label-radio"><span class="icon-link"></span>&emsp;<%= __("ActionPartner")%></div>
 
             </label>
             <% var _id=_.uniqueId('link_action') %>
@@ -92,7 +92,7 @@
                 <span class="icon  icon-radio-active"></span>
                 <span class="icon  icon-radio-inactive"></span>
               </div>
-              <div class="block-label-radio"><span class="icon-delete"></span><%= __("doNothing")%></div>
+              <div class="block-label-radio"><span class="icon-delete"></span>&emsp;<%= __("doNothing")%></div>
 
             </label>
         </div>
