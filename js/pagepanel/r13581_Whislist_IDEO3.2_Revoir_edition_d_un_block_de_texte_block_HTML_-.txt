Revision: r13581
Date: 2024-12-05 14:35:31 +0300 (lkm 05 Des 2024) 
Author: rrakotoarinelina 

## Commit message
Whislist IDEO3.2 : Revoir edition d'un block de texte / block HTML -

## Files changed

## Full metadata
------------------------------------------------------------------------
r13581 | rrakotoarinelina | 2024-12-05 14:35:31 +0300 (lkm 05 Des 2024) | 1 ligne
Chemins modifiés :
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Templates/htmlEdition.html

Whislist IDEO3.2 : Revoir edition d'un block de texte / block HTML -
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Templates/htmlEdition.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Templates/htmlEdition.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Templates/htmlEdition.html	(révision 13581)
@@ -0,0 +1,35 @@
+<style>
+.ui-dialog .ui-dialog-content {
+  text-align: left;
+}
+</style>
+<div class="ace-close"><span class="icon-save"></span><span class="label"><%=__("Valider")%></span></div>
+<div class="tabs html-editor">
+    <% var htmlID=_.uniqueId('html-editor');%>
+    <% var jsID=_.uniqueId('js-editor');%>
+    <% var cssID=_.uniqueId('css-editor');%>
+    <ul class="editor-selection">
+        <li><a href="#<%= htmlID%>" data-action="html" ><%=__("html")%></a></li>
+        <li><a href="#<%=cssID%>" data-action="css" ><%=__("css")%></a></li>
+        <li><a href="#<%=jsID%>" data-action="js" ><%=__("javascript")%></a></li>
+    </ul>
+    <div class="code-editor" id="<%=htmlID%>">
+        <nav class="menu"></nav>
+        <div class="contentAce" data-mode="html"><%=html%></div>
+    </div>
+    <div class="code-editor" id="<%=cssID%>">
+        <nav class="menu"></nav>
+        <div class="contentAce" data-mode="css"><%=css%></div>    
+    </div>
+    <div class="code-editor" id="<%=jsID%>">
+        <nav class="menu">
+            <% var inputID=_.uniqueId('jsRunInput');%>
+            <input type="radio" value="inline" name="run-on" id="<%=inputID%>" <%=js.run==='inline'?' checked="checked" ':'' %>/><label for="<%=inputID%>"><span class="radio"><span class="icon-radio-active"></span><span class="icon-radio-inactive"></span></span><%=__("jsHookInline")%></label>
+            <% var inputID=_.uniqueId('jsRunInput');%>
+            <input type="radio" value="ready" name="run-on" id="<%=inputID%>" <%=js.run==='ready'?' checked="checked" ':'' %>/><label for="<%=inputID%>"><span class="radio"><span class="icon-radio-active"></span><span class="icon-radio-inactive"></span></span><%=__("jsHookReady")%></label>
+            <% var inputID=_.uniqueId('jsRunInput');%>
+            <input type="radio" value="load" name="run-on" id="<%=inputID%>" <%=js.run==='load'?' checked="checked" ':'' %>/><label for="<%=inputID%>"><span class="radio"><span class="icon-radio-active"></span><span class="icon-radio-inactive"></span></span><%=__("jsHookonLoad")%></label>
+        </nav>
+        <div class="contentAce" data-mode="javascript"><%=js.content%></div>
+    </div>
+</div>
\ No newline at end of file
