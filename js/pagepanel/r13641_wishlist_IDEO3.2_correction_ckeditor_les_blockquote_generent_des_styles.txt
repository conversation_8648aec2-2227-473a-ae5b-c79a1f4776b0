Revision: r13641
Date: 2024-12-19 10:13:06 +0300 (lkm 19 Des 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2 : correction ckeditor, les blockquote generent des styles

## Files changed

## Full metadata
------------------------------------------------------------------------
r13641 | srazanandralisoa | 2024-12-19 10:13:06 +0300 (lkm 19 Des 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js

wishlist IDEO3.2 : correction ckeditor, les blockquote generent des styles
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 13640)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 13641)
@@ -43,6 +43,8 @@
                             fontSize_sizes: 'tiny/0.75em;small/0.875em;large/1.25em;xlarge/1.5em;xxlarge/2em;xxxlarge/2.5em',
                             contentsCss: [
                                 __IDEO_CSS_PATH__ + 'ideo3-back.css',
+                                '.cke_editable blockquote { background: #eee; border-left: 10px solid #ccc; padding: 15px; color : rgb(204, 204, 204)}',
+                                '.cke_editable blockquote p {display: inline ; font-style: italic}',
                                 'body.cke_editable { color: #000000; background-color: #ffffff; font-family: Arial, Helvetica, sans-serif }'
                             ],
                             stylesSet: [
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js	(révision 13640)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js	(révision 13641)
@@ -237,25 +237,6 @@
                 }
 
             }, this));
-            $(document).on('click', '.cke_button__blockquote', _.bind(function(event) {
-               
-                var $iframe = $('.ui-dialog iframe');
-                var $blockquotes = $iframe.contents().find('blockquote');
-                //add class to this element 
-                $blockquotes.css({
-                    'background': '#eee',
-                    'border-left': '10px solid #ccc',
-                    'padding': '15px',
-                    'color' : 'rgb(204, 204, 204)',
-                });
-            
-                $blockquotes.find('p').css({
-                    'display': 'inline',
-                    'font-style': 'italic'
-                });
-            
-               
-            }, this));
 
         },
         hasUnsavedChanges: function() {
