Revision: r13485
Date: 2024-11-22 10:36:13 +0300 (zom 22 Nov 2024) 
Author: frahajanirina 

## Commit message
Wishlist:IDEO3.2:Blk cards : ajout d'options et modifs

## Files changed

## Full metadata
------------------------------------------------------------------------
r13485 | frahajanirina | 2024-11-22 10:36:13 +0300 (zom 22 Nov 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/CardOption.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardList.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardListView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardOptionView.js
   M /branches/ideo3_v2/integration/src/less/main.less

Wishlist:IDEO3.2:Blk cards : ajout d'options et modifs
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/CardOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/CardOption.js	(révision 13484)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Models/CardOption.js	(révision 13485)
@@ -32,8 +32,8 @@
                     });
                     this._super();
                 },
-                addCardToList: function() {
-                    var card = new Card();
+                addCardToList: function(card) {
+                    var card = card || new Card();
                     card.cards = this;
                     card.index = this.cards.length;
                     this.cards.push(card);
@@ -79,7 +79,7 @@
                 },
             }
         );
-        CardOption.SetAttributes(['cards']);
+        CardOption.SetAttributes(['cards', 'cardId']);
 
         return CardOption;
     }
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardList.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardList.html	(révision 13484)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardList.html	(révision 13485)
@@ -1,6 +1,9 @@
 <div class="show-part" data-edit-id="<%= id %>" style="position:relative;padding:5px;">
     <span class="icon icon-grip" style="cursor: move;"></span>
     <span class="text field-name" style="line-height:22px;position:absolute;top:5px;right:32px;left:30px;bottom:5px;text-overflow:ellipsis;"><%= title ? __("cardOption")+' '+'"'+title+'"' : __("cardOption") %></span>
+    <span class="duplicate-card">
+        <a class="icon-duplicate-block"></a>
+    </span>
     <span class="icon icon-edit" data-edit-id="<%= index %>" style="width:22px;height:22px;font-size:16px;text-align:center;position:absolute;line-height:22px;cursor:pointer;"></span>
     <span class="delete delete-card" data-edit-id="<%= index %>" style="width:22px;height:22px;font-size:16px;text-align:center;position:absolute;line-height:22px;cursor:pointer;">
         <span class="icon icon-delete"></span>
@@ -373,7 +376,7 @@
             </div>            
         </div><br/>
     </div>
-    <div class="button-group save-or-cancel">
+    <div class="button-group save-or-cancel card-save-or-cancel">
         <a class="button cancel">
             <span class="wrapper">
                 <span class="icon"></span>
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardOption.html	(révision 13484)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Templates/CardOption.html	(révision 13485)
@@ -1,5 +1,8 @@
 <div class="panel-option-container animated">
     <article class="panel-option ">
+        <% if (typeof(cardId) !== "undefined") { %>
+            <div class="shortcode">[[cards_<%= cardId %>]]</div>
+        <% } %>
         <div class="option-card-content">
             <div class="wrapper  black-dropdown">
                 <a class="btn" href="#">
@@ -10,7 +13,7 @@
                 </a>
             </div>
         </div>
-        <div class="panel-content active">
+        <div class="panel-content active <%= (typeof cardId !== 'undefined') ? 'card-panel-content' : '' %>">
             <div class="form-builder">
                 <div class="table-wrapper  col-setup">
                     <div class="wrapper row-wrapper column-container">
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardListView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardListView.js	(révision 13484)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardListView.js	(révision 13485)
@@ -28,7 +28,8 @@
                 'click .card-template-option-color .effect-radio': "_onChangeColor",
                 "click input[name='buttonAlignment']:checked" : "cancelButtonAligment",
                 "click input[name='size']:checked" : "actionButtonSize",
-                "click input[name='textAlignment']:checked" : "actionTextButtonAligment"
+                "click input[name='textAlignment']:checked" : "actionTextButtonAligment",
+                "click .duplicate-card": "onDuplicateClick"
             },
             initialize:function () {
                 View.prototype.initialize.call(this);
@@ -105,12 +106,12 @@
             },
             edit:function (event) {
                 event.preventDefault();
-                var $editPart = this.$('.edit-part').filter('[data-edit-id="' + this.model.index + '"]');
-                if ($editPart.hasClass('editing')) {
-                    this.cancel(event);
+                if (this.$el.hasClass("editing")) {
+                    this.cancel();
                 } else {
-                    this.$('.edit-part').addClass('hidden').removeClass('editing');
-                    $editPart.removeClass('hidden').addClass('editing');
+                    this.$el.addClass("editing");
+                    this.$el.find('.edit-part').removeClass("hidden");
+                    this.$el.parentsUntil('.col-setup', '.column-container').addClass("editing");
                 }
             },
             optionsToArray:function (options) {
@@ -151,18 +152,15 @@
                 var $cardToUpdate = this.$('.show-part').filter('[data-edit-id="' + editId + '"]');
                 $cardToUpdate.find('.text.field-name').text(newTitle || this.translate('card'));
 
-                $editPart.addClass('hidden').removeClass('editing');
+                this.$el.removeClass("editing");
+                this.$el.parentsUntil('.col-setup', '.column-container').removeClass("editing");
 
                 this.render();
             },
-            cancel:function (event) {
-                event = event || {};
-                event.preventDefault = event.preventDefault || function() {};
-                var $clickedButton = $(event.currentTarget); 
-                var $parent = $clickedButton.closest('[data-edit-id]');
-                var editId = $parent.data('edit-id');
-                var $editPart = this.$('.edit-part').filter('[data-edit-id="' + editId + '"]');
-                $editPart.addClass('hidden').removeClass('editing');
+            cancel:function () {
+                this.$el.removeClass("editing");
+                this.$el.find('.edit-part').addClass("hidden");
+                this.$el.parentsUntil('.col-setup', '.column-container').removeClass("editing");
             },
             /**
              * trigger le modal de selection d'iĉone à utiliser
@@ -327,6 +325,9 @@
                     });
                     this.model.textAlignment = $target.attr("value");
                 }
+            },
+            onDuplicateClick: function() {
+                this.model.cards.addCardToList(this.model.clone());
             }
         });
 
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardOptionView.js	(révision 13484)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardOptionView.js	(révision 13485)
@@ -4,9 +4,10 @@
         "underscore",
         "text!../Templates/CardOption.html",
         "JEditor/PagePanel/Contents/Options/Views/AbstractOptionView",
-        "./CardListView"
+        "./CardListView",
+        "JEditor/App/Messages/ClipboardModule"
     ],
-    function($, _, cardOption, AbstractOptionView, CardListView) {
+    function($, _, cardOption, AbstractOptionView, CardListView, ClipboardModule) {
         /**
          * Options de la carte
          * @class CardOptionView
@@ -16,18 +17,23 @@
             optionType: 'cardOption',
             className: 'card-option-home panel-content',
             events: {
-                "click .option-card-content": "onAddClick"
+                "click .option-card-content": "onAddClick",
+                'click .shortcode' : 'copyToClipboard'
             },
             initialize: function() {
                 this._super();
                 this._template = this.buildTemplate(cardOption, this.translate);
                 this.listenTo(this.model, 'remove:card', this.render);
+                this.listenTo(this.model, 'add:card', this.render);
             },
             render: function() {
                 var that = this;
                 this.undelegateEvents();
                 this.$el.empty();
-                this.$el.html(this._template(this.model));
+                this.$el.html(this._template(
+                    {
+                        cardId: this.model.cardId
+                    }));
                 // Parcourir et rendre chaque carte dans la vue
                 this.model.cards.forEach(function (card) {
                     var view = new CardListView({ model: card });
@@ -71,6 +77,9 @@
                 this.render();
                 
                 return false;
+            },
+            copyToClipboard : function (e){
+                ClipboardModule.copyToClipboard(e);
             }
         });
 
Index: src/less/main.less
===================================================================
--- src/less/main.less	(révision 13484)
+++ src/less/main.less	(révision 13485)
@@ -2934,4 +2934,34 @@
 .dashborard-icon-news {
   color: #03a9f4;
 }
+.duplicate-card {
+  position: absolute;
+  cursor: pointer;
+  right: 60px;
+  color: #666;
 
+  a:hover {
+    color: #fff;
+  }
+}
+.card-save-or-cancel {
+  position: sticky;
+  bottom: -10px;
+}
+.card-option-home.panel-content .shortcode {
+  font-family:'Open Sans',sans-serif;
+  font-weight:400;
+  font-size:.9em;
+  text-align:center;
+  color:#535353;
+  border:1px solid #313131;
+  padding-top:5px;
+  padding-bottom:5px;
+  border-radius:4px;
+  margin-bottom:1em;
+  cursor:pointer
+}
+.card-panel-content {
+  margin-top: 22px;
+}
+
