Revision: r11268
Date: 2023-09-08 11:10:35 +0300 (zom 08 Sep 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: nouveau block loop (partie JS)

## Files changed

## Full metadata
------------------------------------------------------------------------
r11268 | srazanandralisoa | 2023-09-08 11:10:35 +0300 (zom 08 Sep 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/LoopBlock.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Models
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Models/LoopOption.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Models/LoopStyleOption.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Models/main.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Templates
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Templates/loopOption.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Templates/loopStyle.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopBlockView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopOptionView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopStyleOptionView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/main.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/main.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/fr-ca
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/fr-fr
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/fr-fr/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json
   M /branches/ideo3_v2/integration/src/less/imports/gallery_style.less

wishlist IDEO3.2: nouveau block loop (partie JS)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js	(révision 11267)
+++ src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js	(révision 11268)
@@ -42,7 +42,7 @@
                                 events: {'click .block.available': 'addToCurrentContainer', 'dragstart .bloc.available': '_ondragStart'},
                                 blockOrder: {
                                     'standard': ["TextBlock", "ImageBlock", "ButtonBlock", "SeparatorBlock", "TableBlock"],
-                                    'advanced': ["CarrouselBlock", "GridBlock","GalerieBlock","CompareBlock", "FormBlock", /*"VideoLinkeoBlock",*/ "MapBlock", "VideoBlock", "HtmlBlock"],
+                                    'advanced': ["CarrouselBlock", "GridBlock","GalerieBlock","CompareBlock", "LoopBlock", "FormBlock", /*"VideoLinkeoBlock",*/ "MapBlock", "VideoBlock", "HtmlBlock"],
                                     'others': ["LegalsBlock","SiteMapBlock","SocialNetworkBlock"],
                                     'ignored':['TwitterTimelineBlock']
                                 },
Index: src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js	(révision 11267)
+++ src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js	(révision 11268)
@@ -18,7 +18,8 @@
   "./EvaluationBlock",
   "./CarrouselBlock",
   "./GalerieBlock",
-  "./CompareBlock"
+  "./CompareBlock",
+  "./LoopBlock"
 ], function (
   ImageBlock,
   HtmlBlock,
@@ -39,7 +40,8 @@
   EvaluationBlock,
   CarrouselBlock,
   GalerieBlock,
-  CompareBlock
+  CompareBlock,
+  LoopBlock
 ) {
   var component = {
     "ImageBlock": ImageBlock,
@@ -61,7 +63,8 @@
     "EvaluationBlock": EvaluationBlock,
     "CarrouselBlock"  : CarrouselBlock,
     "GalerieBlock" : GalerieBlock,
-    "CompareBlock": CompareBlock
+    "CompareBlock": CompareBlock,
+    "LoopBlock": LoopBlock
   };
   return component;
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/LoopBlock.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/LoopBlock.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/LoopBlock.js	(révision 11268)
@@ -0,0 +1,30 @@
+define( [
+    "JEditor/PagePanel/Contents/Blocks/Block/Block",
+    "JEditor/PagePanel/Contents/Blocks/LoopBlock/Models/LoopOption",
+    "JEditor/PagePanel/Contents/Blocks/LoopBlock/Models/LoopStyleOption",
+], function( Block,LoopOption,LoopStyleOption) {
+    /**
+     * Bloc de galerie
+     * @class LoopBlock
+     * @extends Block
+     */
+    var LoopBlock = Block.extend(
+            /**
+             * @lends LoopBlock
+             */
+                    {
+                        defaults: {type: 'loop', contentType: 'loopBlock'},
+                        initialize: function() {
+                            this._super();
+                            if (!this.options.loop)
+                                this.options.add(new LoopOption({},{content:this}));
+                            if (!this.options.loopStyle)
+                                this.options.add(new LoopStyleOption());
+                        },
+            
+                    }
+            );
+
+            LoopBlock.ICON = 'icon-transition-horizontal';
+            return LoopBlock;
+        });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Models/LoopOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Models/LoopOption.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Models/LoopOption.js	(révision 11268)
@@ -0,0 +1,61 @@
+define([
+    "JEditor/Commons/Events",
+    "JEditor/PagePanel/Contents/Options/Models/AbstractOption",
+    "collection!JEditor/Commons/Files/Models/FileGroupCollection",
+    "JEditor/Commons/Files/Models/FileGroup"
+], function(Events,
+        AbstractOption,
+        FileGroupCollection,
+        FileGroup
+        ) {
+    var fileGroupCollection = FileGroupCollection.getInstance();
+    /**
+     * @class LoopOption
+     * @extends AbstractOption
+     * @type @exp;JEditor@pro;Contents@pro;Options@pro;AbstractOption@call;extend
+     * @property {FileGroup} fileGroup Le groupe de photo utilisé dans le boucle (non sérialisé dans le JSON)
+     */
+    var LoopOption = AbstractOption.extend(
+            /**
+             * @lends LoopOptions.prototype
+             */
+                    {
+                        defaults: {
+                            priority: 70, 
+                            optionType: 'loop', 
+                            fileGroup: null, 
+                        },
+                        initialize: function() {
+                            this._super();
+                            this.on('change:fileGroup', this.onFileGroupChange);
+                            this.onFileGroupChange();
+                        },
+                        onFileGroupChange: function() {
+                            var fileGroup = this.getFileGroup();
+                            if (this.previousAttributes().fileGroup)
+                                this.stopListening(this.previousAttributes().fileGroup);
+                            if (fileGroup && !(fileGroup instanceof FileGroup))
+                                this.setFileGroup(fileGroupCollection.get(fileGroup));
+                            else if (fileGroup) {
+
+                                this.listenTo(fileGroup, 'sync change', function() {
+                                    this.trigger('change:fileGroup', this, {});
+                                    this.trigger(Events.BackboneEvents.CHANGE, this, {});
+                                });
+                            }
+                            return this;
+                        },
+                        toJSON: function() {
+                            var fileGroup = this.getFileGroup();
+                            return {
+                                optionType: 'loop',
+                                fileGroup: fileGroup?fileGroup.id:null,
+                            }
+                        },
+                        
+                    }
+            );
+            LoopOption.SetAttributes(['fileGroup']);
+
+            return LoopOption;
+        });
Index: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Models/LoopStyleOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Models/LoopStyleOption.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Models/LoopStyleOption.js	(révision 11268)
@@ -0,0 +1,33 @@
+define(["JEditor/PagePanel/Contents/Options/Models/AbstractOption"], function(AbstractOption) {
+    /**
+     * Styles du blocl boucle
+     * @class LoopStyleOption
+     * @extends AbstractOption
+     */
+    var LoopStyleOption = AbstractOption.extend(
+            /**
+             * @lends LoopStyleOptions.prototype
+             */
+                    {
+                        defaults: {
+                            optionType: 'loopStyle', 
+                            priority: 80,
+                            size : 'medium', 
+                            colorFilter : false,
+                            escapeSize :   2,
+                            speedLoop : 2
+                        },
+                        toJSON: function() {
+                            return {
+                                optionType:this.optionType,
+                                size :   this.size,
+                                colorFilter :   this.colorFilter,
+                                escapeSize  :this.escapeSize,
+                                speedLoop  :this.speedLoop
+                            }
+                        },
+                    }
+            );
+            LoopStyleOption.SetAttributes(['size', 'colorFilter','escapeSize','speedLoop']);
+            return LoopStyleOption;
+        });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Models/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Models/main.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Models/main.js	(révision 11268)
@@ -0,0 +1,8 @@
+// this file has been auto-generated by a grunt task, it will be overriden, do not modify it
+define(["./LoopOption","./LoopStyleOption"],function(LoopOption,LoopStyleOption){
+    var comp={
+        "LoopOption": LoopOption,
+        "LoopStyleOption":LoopStyleOption
+    };
+    return comp;
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Templates/loopOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Templates/loopOption.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Templates/loopOption.html	(révision 11268)
@@ -0,0 +1,24 @@
+<div class="panel-option-container animated gallery-files">
+    <article class="panel-option loop-panel">
+        <header>
+            <h3 class="option-name"><span class="icon-image"></span><%= __("loopContent")%></h3>
+            <p class="panel-content-legend"><span class="icon-warning" style="color: #d42525;"></span> <span style="color: #d42525;"><%= __("imageWarning")%></span> <%= __("imageWarningMsg")%> <a href="https://www.tineye.com/" target="_blank">TinEye</a>.</p>
+        </header>
+        <div class="option-content">
+            <div class="files-action  btn-gallery-content">
+                <div class="image-count">
+                    <span class="image-count-wrap">
+                        <span class="count"><%=fileGroup?fileGroup.length:0%></span><span class="icon-image"></span>
+                        <span class="icon-hexagon-add"></span>
+                    </span>
+                </div>
+                <div class="action">
+                    <% if(!fileGroup||fileGroup.length<1) {%>
+                        <div class="intro"><%=__("emptyLoop")%></div>
+                    <% } %>
+                    <div class="action-desc"><%=__("clickToAddImages")%></div>
+                </div>
+            </div>
+        </div>
+    </article>
+</div>
Index: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Templates/loopStyle.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Templates/loopStyle.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Templates/loopStyle.html	(révision 11268)
@@ -0,0 +1,131 @@
+<div class="panel-option-container animated">
+    <article class="panel-option">
+        <header>
+            <h3 class="option-name">
+                <span class="icon-button-size icon-midsize"></span>
+                <%=__("size")%>
+            </h3>
+            <span class="panel-content-legend">
+                <%=__("sizeLegend")%>
+            </span>
+        </header>
+        <div class="option-content">
+            <div class="controlPanel-selector">
+                <div class="option radio">
+                    <% var _id= _.uniqueId('size'); %>
+                    <div class="button-size-radio">
+                        <label for="<%=_id %>" class="inline-block-label">
+                            <input id="<%= _id%>" type="radio" class="field-input" value="tiny" name="size" <%=size==="tiny"? ' checked="checked" ':'' %> />
+                            
+                            <div class="inline-block-label__top">
+                                <span class="i-block-social-size_s"></span>
+                            </div>
+                            <div class="inline-block-label__bottom">
+                                <span class="icon-radio-inactive"></span>
+                                <span class="icon-radio-active"></span>
+                            </div>
+                        </label>
+
+
+                        <% _id= _.uniqueId('size');      %>
+
+                        <label for="<%=_id %>" class="inline-block-label">
+                            <input id="<%= _id%>" type="radio" class="field-input" value="small" name="size" <%=size==="small"? ' checked="checked" ':'' %> />
+                            <div class="inline-block-label__top">
+                                <span class="i-block-social-size_m"></span>
+                            </div>
+                            <div class="inline-block-label__bottom">
+                                <span class="icon-radio-inactive"></span>
+                                <span class="icon-radio-active"></span>
+                            </div>
+                        </label>
+
+                        <% _id= _.uniqueId('size');      %>
+
+                        <label for="<%=_id %>" class="inline-block-label">
+                            <input id="<%= _id%>" type="radio" class="field-input" value="medium" name="size" <%=size==="medium"? ' checked="checked" ':'' %> />
+                            <div class="inline-block-label__top">
+                                <span class="i-block-social-size_l"></span>
+                            </div>
+                            <div class="inline-block-label__bottom">
+                                <span class="icon-radio-inactive"></span>
+                                <span class="icon-radio-active"></span>
+                            </div>
+                        </label>
+
+                        <% _id= _.uniqueId('size');      %>
+
+                        <label for="<%=_id %>" class="inline-block-label">
+                            <input id="<%= _id%>" type="radio" class="field-input" value="large" name="size" <%=size==="large"? ' checked="checked" ':'' %> />
+                            <div class="inline-block-label__top">
+                                <span class="i-block-social-size_xl"></span>
+                            </div>
+                            <div class="inline-block-label__bottom">
+                                <span class="icon-radio-inactive"></span>
+                                <span class="icon-radio-active"></span>
+                            </div>
+                        </label>
+
+                    </div>
+                </div>
+            </div>
+        </div>
+    </article>
+</div>
+<div class="panel-option-container animated ">
+    <article class="panel-option background-color">
+        <header>
+            <h3 class="option-name"><%=__("blackwhite")%></h3>
+            <p class="panel-content-legend"><%=__("blackwhitedesc")%></p>
+        </header>
+        <div class="option-content colorFilter">
+            <% var _id= _.uniqueId('colorFilter'); %>
+            <div class="button-align-checkbox">
+                <label for="bw-<%=_id %>" class="inline-label">
+                    <input id="bw-<%= _id%>" type="checkbox" class="field-input" name="colorFilter" <%=colorFilter===true? ' checked="checked" ':'' %> />
+                    <div class="inline-label__container label">
+                        <span class="checkbox-wrap">
+                            <span class="icon-unchecked"></span>
+                            <span class="icon-checked"></span>
+                        </span>
+                        <span class="text">
+                            <%= __("blackwhite") %>
+                        </span>
+                    </div>
+                </label>
+            </div>
+        </div>
+    </article>
+</div>
+<div class="panel-option-container animated loop-spacing">
+    <article class="panel-option loop-spacing">
+        <header>
+            <h3 class="option-name"><%= __("escapeSize") %></h3>
+            <p class="panel-content-legend"><%= __("escapeSizeDesc")%></p>
+        </header>
+        <div class="option-content  ">
+            <div class="slider-container spacing-slider">
+                <span class="icon-less"></span>
+                <div class="slider spacing-size">
+                </div>
+                <span class="icon-more"></span>
+            </div>
+        </div>
+    </article>
+</div>
+<div class="panel-option-container animated loop-speed">
+    <article class="panel-option loop-speed ">
+        <header>
+            <h3 class="option-name"><%= __("speedLoop") %></h3>
+            <p class="panel-content-legend"><%= __("speedLoopDesc")%></p>
+        </header>
+        <div class="option-content">
+            <div class="slider-container">
+                <span class="icon-less"></span>
+                <div class="slider speed-slider">
+                </div>
+                <span class="icon-more"></span>
+            </div>
+        </div>
+    </article>
+</div>
Index: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopBlockView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopBlockView.js	(révision 11268)
@@ -0,0 +1,82 @@
+define([
+    "jquery",
+    "underscore",
+    "JEditor/Commons/Events",
+    "JEditor/PagePanel/Contents/Blocks/Block/Views/BlockView",
+    "JEditor/Commons/Files/Models/FileGroup",
+    "less",
+    //not in params
+    "owlCarouselSync",
+], function($, _, Events, BlockView, FileGroup, less) {
+    var LoopBlockView = BlockView.extend({
+        attributes: {
+            tabindex: 0,
+            class: "loopblock block"
+        },
+        initialize: function() {
+            this._super();
+            this.lessParser = new less.Parser();
+        },
+        
+        /**
+         * @param {Object} templateParams
+         * @returns {String}
+         */
+        renderHtml: function(templateParams) {
+            var galleryOptions = this.model.options.gallery;
+            var html = galleryOptions.design.options.galleryTemplate.template.html;
+            return _.template(html)(templateParams);
+
+        },
+        /**
+         *
+         * @returns {GalleryTemplate}
+         */
+        getTemplate: function() {
+            return this.getOptions().galleryTemplate.template;
+        },
+        /**
+         *
+         * @returns {}
+         */
+        getOptions: function() {
+            return this.model.options.gallery.design.options;
+        },
+        getFileGroup: function() {
+            return this.model.options.loop.fileGroup;
+        },
+        getDesign: function() {
+            return this.model.options.gallery.design;
+        },
+        getEffectsClass: function() {
+            var designOptions = this.getOptions();
+            var classes = [
+                designOptions.galleryStyle.theme
+            ];
+            if (designOptions.galleryAnimations) {
+                if (designOptions.galleryAnimations.transition)
+                    classes.push(designOptions.galleryAnimations.transition);
+            }
+            return classes.join(' ');
+        }, 
+        renderOptions: function(model, options) {
+            var template, uid, css, html, fileGroup;
+            fileGroup = this.getFileGroup();
+            if ((fileGroup instanceof FileGroup) && fileGroup.length != 0) {
+                this.dom[this.cid].content.html(
+                    '<div class="exist-gallery"><div><span class="icon-transition-horizontal"></span><span class="count">&nbsp;'+fileGroup.length+'</span></div></div>');
+            }else{
+                this.dom[this.cid].content.html('<div class="empty-gallery"><span class="icon-transition-horizontal"></span></div>');
+            }
+            return this;
+
+        },
+        getFileDesc: function(file) {
+            return file.desc[this.app.currentPanel.currentLang.id];
+        },
+        getFileTitle: function(file) {
+            return file.title[this.app.currentPanel.currentLang.id];
+        }
+    });
+    return LoopBlockView;
+});
Index: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopOptionView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopOptionView.js	(révision 11268)
@@ -0,0 +1,89 @@
+define(
+    [
+        "jquery",
+        "underscore",
+        "text!../Templates/loopOption.html",
+        "JEditor/Commons/Events",
+        "JEditor/Commons/Files/Models/FileGroup",
+        "JEditor/PagePanel/Contents/Options/Views/AbstractOptionView",
+        "JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselFileEditionView",
+        "JEditor/App/Messages/ClipboardModule", 
+    ],
+    function($, _, loopOption, Events, FileGroup, AbstractOptionView, LoopFileEditionView, ClipboardModule) {
+        /**
+         * Options de la boucle
+         * @class LoopOptionView
+         * @extends AbstractOptionView
+         */
+        var LoopOptionView = AbstractOptionView.extend({
+            optionType: 'loop',
+            events: {
+                'click .files-action'   :   'openFileGroupDialog',
+                'click .shortcode' : 'copyToClipboard'
+                },
+            className: 'loop-option-home panel-content',
+            initialize: function() {
+                this._super();
+                this._template = this.buildTemplate(loopOption, this.translate);
+                this.listenTo(this.model, 'change:fileGroup', this.onFileGroupChange);
+            },
+            openFileGroupDialog: function() {
+                var fileGroup;
+                var viewAttributes;
+                var fileGroupDialog;
+                if (this.model.fileGroup)
+                    fileGroup = this.model.fileGroup;
+                else
+                    fileGroup = new FileGroup();
+                viewAttributes = {title: this.translate("editMyLoop"), model: fileGroup};
+                fileGroupDialog = new LoopFileEditionView(viewAttributes);
+                this.listenTo(fileGroupDialog, Events.ChoiceEvents.SELECT, function(view, selected) {
+                    this.model.setFileGroup(selected);
+                });
+                this.listenTo(fileGroupDialog, Events.DialogEvents.CLOSE, function() {
+                    this.stopListening(fileGroupDialog);
+                    fileGroupDialog.remove();
+                });
+                fileGroupDialog.open();
+                return this;
+            },
+            onFileGroupChange: function(_fileGroup, options) {
+                var oldFilegroup = this.model.previousAttributes().fileGroup;
+                var fileGroup = this.model.getFileGroup();
+                if (oldFilegroup)
+                    this.stopListening(oldFilegroup, 'change:length');
+                if (fileGroup && !fileGroup.length && !fileGroup.name) {
+                    var date = new Date();
+                    var options = { year: 'numeric', month: 'numeric', day: 'numeric',hour: 'numeric', minute: 'numeric', second: 'numeric' };
+                    var dateString = date.toLocaleDateString('fr-FR', options);
+                    
+                    fileGroup.name = this.translate("newGallery")+dateString ;//+ Utils.dateFormat(this.translate('dateFormat'));
+                    fileGroup.save();
+                    this.listenTo(this.model.getFileGroup(), 'change:length', this.render);
+                }
+                else if (fileGroup)
+                    this.listenTo(this.model.getFileGroup(), 'change:length', this.render);
+                this.render();
+                return this;
+            },
+            render: function() {
+                this.undelegateEvents();
+                if (this.dom[this.cid].dialogTrigger)
+                    this._fileGroupDialog.detach(this.dom[this.cid].dialogTrigger[0]);
+              
+                this.$el.empty();
+                this.$el.html(this._template(this.model));
+
+                this.scrollables({
+                    advanced:{ autoScrollOnFocus: false }
+                });
+                this.delegateEvents();
+                return this;
+            },
+            copyToClipboard : function (e){
+                ClipboardModule.copyToClipboard(e);
+            },
+            
+        });
+        return LoopOptionView;
+    });
Index: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopStyleOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopStyleOptionView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopStyleOptionView.js	(révision 11268)
@@ -0,0 +1,96 @@
+define([
+    "underscore",
+    "jquery",
+    "text!../Templates/loopStyle.html",
+    "JEditor/PagePanel/Contents/Options/Views/AbstractOptionView",
+    "i18n!../nls/i18n",
+
+], function(_, $,loopStyle,  AbstractOptionView, translate) {
+    /**
+     * Vue des options concernant le style de la galerie
+     * @classLoopStyleOptionView
+     * @extends JEditor.Contents.Options.View.AbstractOptionView
+     */
+    var LoopStyleOptionView = AbstractOptionView.extend(
+            /** 
+             * @lendsLoopStyleOptionView.prototype
+             */
+                    {
+                        optionType: 'loopStyle',
+                        tagName: "div",
+                        className: "gallery-template-option galleryStyle panel-content ",
+                        events: {
+                            'slidechange .slider.spacing-size': '_setEscapeSize',
+                            'slidechange .slider.speed-slider': '_setSpeedLoop',
+                            'change input[type="radio"].select-box': '_onStyleAffichageChange',
+                            'click .effect-radio': '_onChangeFormatImage'
+                        },
+                        /**
+                         * initialise l'objet
+                         */
+                        initialize: function() {
+                            this._super();
+                            this.template = this.buildTemplate(loopStyle, translate);
+                        },
+                        _onChangeFormatImage:function(event){
+                           this.$(".effect-radio").removeClass("active");
+                            var $target = $(event.currentTarget);
+                            $target.addClass("active");
+                            var value = $target.attr("data-value");
+                            this.model.FormatImage=value;
+                        },  
+                        /** */
+                        _onStyleAffichageChange :function(event){
+                            var $target = $(event.currentTarget);
+                            this.model.StyleAffichage = $target.val();
+                        },
+                        /**
+                         * Slider change
+                         */
+                        _setSpeedLoop: function(event,ui){
+                            var value = ui.value;
+                            this.model.speedLoop = value;
+                            return false;
+                         },
+                          /**
+                         * Slider change
+                         */
+                        _setEscapeSize: function(event,ui){
+                            var value = ui.value;
+                            this.model.escapeSize = value;
+                            return false;
+                         },
+                        /**
+                         * actualise l'affichage de la vue
+                         */
+                        render: function() {
+                            //magique
+                           // this.undelegateEvents();
+                            var templateVars = {
+                                size :   this.model.size,
+                                colorFilter :   this.model.colorFilter
+                            };
+                            this.$el.html(this.template(templateVars));
+                           
+                            this.dom[this.cid].slider = this.$('.loop-speed .slider');
+                            this.dom[this.cid].slider.slider({
+                                min: 1,
+                                max: 3,
+                                step: 1,
+                                value: this.model.speedLoop,
+                                range:"min"
+                            });
+                            this.dom[this.cid].spacingSlider = this.$('.loop-spacing .slider');
+                            this.dom[this.cid].spacingSlider.slider({range: "min",
+                                value: this.model.escapeSize,
+                                min: 1,
+                                max: 4,
+                                step: 1
+                            });
+                            this.scrollables();
+                            return this;
+                        }
+                    }
+            );
+            return LoopStyleOptionView;
+        });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/main.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/main.js	(révision 11268)
@@ -0,0 +1,18 @@
+// this file has been auto-generated by a grunt task, it will be overriden, do not modify it
+define([
+    "./LoopBlockView",
+    "./LoopOptionView",
+    "./LoopStyleOptionView"
+],
+function(  
+    LoopBlockView,
+    LoopOptionView,
+    LoopStyleOptionView
+){
+    var comp={
+    "LoopBlockView"        :   LoopBlockView,
+    "LoopOptionView"       :   LoopOptionView,
+    "LoopStyleOptionView"  :   LoopStyleOptionView
+    };
+    return comp;
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/main.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/main.js	(révision 11268)
@@ -0,0 +1,7 @@
+// this file has been auto-generated by a grunt task, it will be overriden, do not modify it
+define(["./LoopBlock","./Views/main","./Models/main","i18n!./nls/i18n"],function(LoopBlock,Views,Models,i18n){
+    LoopBlock.Models=Models;
+    LoopBlock.Views=Views;
+    LoopBlock.i18n=i18n;
+    return LoopBlock;
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/fr-ca
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/fr-ca	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/fr-ca	(révision 11268)
@@ -0,0 +1 @@
+link fr-fr
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/fr-ca
___________________________________________________________________
Added: svn:special
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/fr-fr/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/fr-fr/i18n.js	(révision 11268)
@@ -0,0 +1,37 @@
+define({  
+    "BLOCK_NAME"            :   "Boucle",
+    "loopBlockOption"       :   "Options du boucle",
+    "loopContent"           :   "Contenu du boucle",
+    "imageWarningMsg"       :   "les images trouvées sur internet ne sont généralement pas libres d’utilisation. Pour vous aider à déterminer si une image est protégée par des droits d’auteur vous pouvez utiliser le service",  
+    "imageWarning"          :   "Attention",
+    "emptyLoop"             :   "Votre boucle est vide",
+    "clickToAddImages"      :   "Cliquez ici pour ajouter<br/>des images.",  
+    "loop"                  :   "Boucle",
+    "editMyLoop"            :   "Éditer ma boucle",
+    "imageInGroup"          :   "Images <br/>dans ma galerie",
+    "newImageGroup"         :   "Créer une nouvelle <br/> collection d'images.",
+    "useExistingGroup"      :   "Utiliser une collection <br/>d'images existante.",
+    "newCollectionName"     :   "Nouvelle collection",
+    "backToCollectionList"  :   "Retour à la liste des collections",
+    "addImageToCollection"  :   "Ajouter des images à ma collection",
+    "howToAddImages"        :   "Cliquez ici ou glissez-déposez de nouvelles images pour les ajouter à votre collection.",
+    "okay"                  :   "Valider",
+    "cancel"                :   "Annuler",
+    "editMyGallery"         :   "Éditer ma boucle",
+    "newGallery"            :   "Nouvelle boucle",
+    "dateFormat"            :   "Y-m-d H:i:s",
+    "emptyNewCollection"    :   "Votre nouvelle collection d’images est vide.",
+    "edit"                  :   "Éditer",
+    "delete"                :   "Supprimer",
+    "allF"                  :   "Toutes",
+    "noneF"                 :   "Aucune",
+    "emptyCollection"       :   "Votre collection d’images est vide.",
+    "size"                  :   "Taille des images",
+    "sizeLegend"            :   "Séléctionnez la taille des images",
+    "blackwhite"            :   "Noir et blanc",
+    "blackwhitedesc"        :   "Cocher la case pour forcer l'affichage du contenu en noir et blanc",
+    "escapeSize"            :   "Espaces",
+    "escapeSizeDesc"        :   "Glisser pour ajuster les espaces entre les contenus",
+    "speedLoop"             :   "Vitesse",
+    "speedLoopDesc"         :   "Glisser pour ajuster la vitesse du défilement",
+});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/fr-fr/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/i18n.js	(révision 11268)
@@ -0,0 +1,44 @@
+define({  
+    "root":{  
+       "BLOCK_NAME"            :   "Loop",
+       "loopBlockOption"       :   "Loop options",
+       "loopStyle"             :   "Style",
+       "Style"                 :   "Style" ,
+       "loopBlockOption"       :   "Loop options",
+       "loopContent"           :   "Content of the loop",
+       "imageWarningMsg"       :   "Images found on the internet are generally not free to use. To help you determine if an image is copyrighted you can use the",  
+       "imageWarning"          :   "Warning",
+       "emptyLoop"             :   "Your loop is empty",
+       "clickToAddImages"      :   "Click here to add<br/>images.",  
+       "loop"                  :   "Loop",
+       "editMyLoop"            :   "Edit my loop",
+       "imageInGroup"          :   "Images <br/>in my gallery",
+       "newImageGroup"         :   "Create a new <br/> collection of images.",
+       "useExistingGroup"      :   "Use an existing collection <br/>of images.",
+       "newCollectionName"     :   "New collection",
+       "backToCollectionList"  :   "Back to the list of collections",
+       "addImageToCollection"  :   "Add images to my collection",
+       "howToAddImages"        :   "Click here or drag and drop new images to add them to your collection.",
+       "okay"                  :   "Confirm",
+       "cancel"                :   "Cancel",
+       "editMyGallery"         :   "Edit my loop",
+       "newGallery"            :   "New loop",
+       "dateFormat"            :   "Y-m-d H:i:s",
+       "emptyNewCollection"    :   "Your new image collection is empty.",
+       "edit"                  :   "Edit",
+       "delete"                :   "Delete",
+       "allF"                  :   "All",
+       "noneF"                 :   "None",
+       "emptyCollection"       :   "Your image collection is empty",
+       "size"                  :   "Image size",
+       "sizeLegend"            :   "Select image size",
+       "blackwhite"            :   "Black and white",
+       "blackwhitedesc"        :   "Check box to force content to be displayed in black and white",
+       "escapeSize"            :   "Spaces",
+       "escapeSizeDesc"        :   "Drag to adjust spaces between content",
+       "speedLoop"             :   "Speed",
+       "speedLoopDesc"         :   "Drag to adjust scrolling speed",
+    },
+    "fr-fr":true,
+    "fr-ca": true
+ });
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json	(révision 11267)
+++ src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json	(révision 11268)
@@ -18,5 +18,7 @@
  "EvaluationBlock":"EvaluationBlock",
  "CarrouselBlock":"CarrouselBlock",
  "GalerieBlock":"GalerieBlock",
- "CompareBlock":"CompareBlock"
+ "CompareBlock":"CompareBlock",
+ "LoopBlock":"LoopBlock"
+
 }
Index: src/less/imports/gallery_style.less
===================================================================
--- src/less/imports/gallery_style.less	(révision 11267)
+++ src/less/imports/gallery_style.less	(révision 11268)
@@ -53,7 +53,7 @@
     border: 1px solid #ffffff;
     .border-radius(4px);
 
-    .icon-gallery, .icon-gallery-arrow-outer, .icon-photogrid-block {
+    .icon-gallery, .icon-transition-horizontal, .icon-gallery-arrow-outer, .icon-photogrid-block {
         top: 50%;
         left: 50%;
         .translate(-50%, -50%);
