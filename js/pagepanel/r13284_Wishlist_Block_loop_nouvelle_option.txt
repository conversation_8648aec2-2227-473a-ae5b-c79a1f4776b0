Revision: r13284
Date: 2024-10-21 12:36:34 +0300 (lts 21 Okt 2024) 
Author: traj<PERSON><PERSON><PERSON><PERSON> 

## Commit message
Wishlist : Block loop : nouvelle option

## Files changed

## Full metadata
------------------------------------------------------------------------
r13284 | traj<PERSON><PERSON><PERSON><PERSON> | 2024-10-21 12:36:34 +0300 (lts 21 Okt 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/dev/#librairies/module/GestionContenuUtil/src/GestionContenuUtil/Core/BlockModuleHandlers/BlockLoopHandler.php
   M /branches/ideo3_v2/dev/#librairies/module/GestionContenuUtil/src/GestionContenuUtil/Core/BlockModuleHandlers/template/loopTemplate.html
   M /branches/ideo3_v2/dev/#librairies/module/GestionContenuUtil/src/GestionContenuUtil/Services/LoopRendererService.php
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Models/LoopStyleOption.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Templates/loopStyle.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopStyleOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/i18n.js

Wishlist : Block loop : nouvelle option
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Models/LoopStyleOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Models/LoopStyleOption.js	(révision 13283)
+++ src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Models/LoopStyleOption.js	(révision 13284)
@@ -18,6 +18,7 @@
                             speedLoop : 2,
                             reverse: false,
                             solidBg: false,
+                            disableAnimation: false
                         },
                         toJSON: function() {
                             return {
@@ -27,11 +28,12 @@
                                 escapeSize  :this.escapeSize,
                                 speedLoop  :this.speedLoop,
                                 reverse: this.reverse,
-                                solidBg: this.solidBg
+                                solidBg: this.solidBg,
+                                disableAnimation: this.disableAnimation
                             }
                         },
                     }
             );
-            LoopStyleOption.SetAttributes(['size', 'colorFilter','escapeSize','speedLoop','reverse', 'solidBg']);
+            LoopStyleOption.SetAttributes(['size', 'colorFilter','escapeSize','speedLoop','reverse', 'solidBg', 'disableAnimation']);
             return LoopStyleOption;
         });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Templates/loopStyle.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Templates/loopStyle.html	(révision 13283)
+++ src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Templates/loopStyle.html	(révision 13284)
@@ -111,7 +111,7 @@
             <div class="button-align-checkbox">
                 <label for="rs-<%=_id %>" class="inline-label">
                     <input id="rs-<%= _id%>" type="checkbox" class="field-input" name="reverse" <%=reverse===true? ' checked="checked" ':'' %> />
-                    <div class="inline-label__container label">
+                    <div class="inline-label__container reverse-bg label">
                         <span class="checkbox-wrap">
                             <span class="icon-unchecked"></span>
                             <span class="icon-checked"></span>
@@ -122,6 +122,21 @@
                     </div>
                 </label>
             </div>
+            <div class="button-align-checkbox">
+                <label for="animation-<%=_id %>" class="inline-label">
+                    <input id="animation-<%= _id%>" type="checkbox" class="field-input" name="disableAnimation" <%=disableAnimation===true? ' checked="checked" ':'' %> />
+                    <div class="inline-label__container label">
+                        <span class="checkbox-wrap">
+                            <span class="icon-unchecked"></span>
+                            <span class="icon-checked"></span>
+                        </span>
+                        <span class="text">
+                            <%= __("animation") %>
+                        </span>
+                    </div>
+                </label>
+            </div>
+
         </div>
     </article>
 </div>
Index: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/fr-fr/i18n.js	(révision 13283)
+++ src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/fr-fr/i18n.js	(révision 13284)
@@ -37,4 +37,5 @@
     "blackwhite"            :   "Forcer noir et blanc",
     "solidBg"               :   "Fond blanc d'images",
     "reverse"               :   "Inverser l'animation",
+    "animation"             :   "Ne pas animer (affiche une grille statique)"
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/i18n.js	(révision 13283)
+++ src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/nls/i18n.js	(révision 13284)
@@ -41,6 +41,7 @@
        "blackwhite"            :   "Forcing black and white",
        "solidBg"               :   "White background images",
        "reverse"               :   "Reverse the animation",
+       "animation"             :   "Do not animate (shows a static grid)"    
     },
     "fr-fr":true,
     "fr-ca": true
Index: src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopStyleOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopStyleOptionView.js	(révision 13283)
+++ src/js/JEditor/PagePanel/Contents/Blocks/LoopBlock/Views/LoopStyleOptionView.js	(révision 13284)
@@ -24,7 +24,8 @@
                             'slidechange .slider.speed-slider': '_setSpeedLoop',
                             'click input[name="colorFilter"]': 'clickColorFilter',
                             'change input[type="radio"].select-box': '_onStyleAffichageChange',
-                            'click .effect-radio': '_onChangeFormatImage'
+                            'click .effect-radio': '_onChangeFormatImage',
+                            'click input[name="disableAnimation"]': 'clickDisableAnimation',
                         },
                         /**
                          * initialise l'objet
@@ -67,6 +68,25 @@
                                  $('input[name="solidBg"]').removeAttr("disabled");
                             }
                         },
+                        clickDisableAnimation:function(event){
+                            var $target = $(event.currentTarget);
+                            if ($target.is(':checked')) {
+                                // disable champ reverse 
+                                this.model.reverse=false;
+                                $('.reverse-bg').addClass('disableSolid');
+                                $('input[name="reverse"]').attr("disabled", "disabled").removeAttr("checked");
+                                
+                                // cacher bloc vitesse 
+                                $('.loop-speed').hide();
+                            }
+                            else{
+                                // champ reverse 
+                                $('.reverse-bg').removeClass('disableSolid');
+                                 $('input[name="reverse"]').removeAttr("disabled");
+                                 // bloc vitesse 
+                                $('.loop-speed').show();
+                            }
+                        },
                         /**
                          * Slider change
                          */
@@ -93,9 +113,17 @@
                                 size :   this.model.size,
                                 colorFilter :   this.model.colorFilter,
                                 solidBg :   this.model.solidBg,
-                                reverse :   this.model.reverse
+                                reverse :   this.model.reverse,
+                                disableAnimation :   this.model.disableAnimation
                             };
                             this.$el.html(this.template(templateVars));
+
+                            // cacher bloc vitesse et desactiver block inverser_animation si disableAnimation = true 
+                            if( this.model.disableAnimation )
+                            {
+                                $('.reverse-bg').addClass('disableSolid');
+                                $('input[name="reverse"]').attr("disabled", "disabled").removeAttr("checked");
+                            }
                            
                             this.dom[this.cid].slider = this.$('.loop-speed .slider');
                             this.dom[this.cid].slider.slider({
