Revision: r13487
Date: 2024-11-22 11:18:35 +0300 (zom 22 Nov 2024) 
Author: frahajanirina 

## Commit message
Wishlist:IDEO3.2:Blk Bouton : ajout option texte

## Files changed

## Full metadata
------------------------------------------------------------------------
r13487 | frahajanirina | 2024-11-22 11:18:35 +0300 (zom 22 Nov 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonOption.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonStyleOption.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js

Wishlist:IDEO3.2:Blk Bouton : ajout option texte
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonOption.js	(révision 13486)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonOption.js	(révision 13487)
@@ -19,7 +19,7 @@
              * @lends ImageOptionss
              */
                     {
-                        defaults: {optionType: 'ButtonOption', priority: 70, text: null},
+                        defaults: {optionType: 'ButtonOption', priority: 70, text: null, isDifferentText: false, textOnMobile: null},
                         initialize: function() {
                             this._super();
                             if (!(this.link instanceof Link)) {
@@ -32,8 +32,16 @@
                         validate: function(attributes, options) {
                             if (!attributes.text || !_.isString(attributes.text) || _.isString(attributes.text) && attributes.text.length > 50)
                                 return {field: "text", message: translate("Invalid_text")};
+                        },
+                        snapshot: function() {
+                            this.savedState = this.toJSON();
+                        },
+                        revert: function() {
+                            this.text = this.savedState.text;
+                            this.isDifferentText = this.savedState.isDifferentText;
+                            this.textOnMobile = this.savedState.textOnMobile;
                         }
                     });
-            ButtonOption.SetAttributes(['text','link']);
+            ButtonOption.SetAttributes(['text','link', 'isDifferentText', 'textOnMobile']);
             return ButtonOption;
         });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonStyleOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonStyleOption.js	(révision 13486)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/ButtonStyleOption.js	(révision 13487)
@@ -42,6 +42,16 @@
                             if(allowedColors.lastIndexOf(attributes.color)<=-1){
                                 return {field: "color", message: translate("Invalid_color" )};
                             }
+                        },
+                        snapshot: function() {
+                            this.savedState = this.toJSON();
+                        },
+                        revert: function() {
+                            this.icon = this.savedState.icon;
+                            this.size = this.savedState.size;
+                            this.buttonAlignment = this.savedState.buttonAlignment;
+                            this.textAlignment = this.savedState.textAlignment;
+                            this.color = this.savedState.color;
                         }
                     });
             ButtonStyleOption.SetAttributes(['icon','size', 'buttonAlignment', 'textAlignment','color']);
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonOption.html	(révision 13486)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonOption.html	(révision 13487)
@@ -7,6 +7,16 @@
         <div class="button-text">
             <input class="field-input" name="text" placeholder="<%=__('button')%>"value="<%=text%>" />
         </div>
+        <div class="panel-radio text-different-radio">
+            <div class="panel-radio-title">
+                <div class="radio-wrapper">
+                    <span class="icon-radio-active"></span>
+                    <span class="icon-radio-inactive"></span>
+                </div>
+                <div class="radio-label"><%=__("DifferentTextOnMobile")%></div>
+            </div>
+        </div>
+        <input class="field-input text-different-input"/>
         <header>
             <h3 class="option-name"><span class="icon-button-action"></span><%=__("selectClickActionButton")%></h3>
             <p class="panel-content-legend"><%= __("buttonActionLegend")%></p>
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js	(révision 13486)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonBlockView.js	(révision 13487)
@@ -3,7 +3,11 @@
     "text!../Templates/buttonBlock.html",
     "i18n!../nls/i18n",
     "JEditor/PagePanel/Contents/Blocks/ButtonBlock/Models/SvgCollection",
-], function(BlockView, template, translate,SvgCollection) {
+    "JEditor/Commons/Ancestors/Views/SaveCancelPanel",
+    "./ButtonOptionView",
+    "./ButtonStyleOptionView",
+    "JEditor/PagePanel/Contents/Options/Views/AdvancedOptionView"
+], function(BlockView, template, translate,SvgCollection, SaveCancelPanel, ButtonOptionView, ButtonStyleOptionView, AdvancedOptionView) {
     /**
      * Vue des blocs d'images
      * @class ImageBlockView
@@ -80,6 +84,56 @@
                             var textButton = this.model.options.ButtonOption.text?this.model.options.ButtonOption.text:translate('button');
                             this.$(".txt span").text(textButton);
                             return this;
+                        },
+                        edit: function() {
+                            var options, buttonOptionsView, contentOptions, buttonStylesOptionView, advancedView;
+                            
+                            options = this.model.options;
+                            options.ButtonOption.snapshot();
+                            options.ButtonStyleOption.snapshot();
+                            
+                            buttonOptionsView = new SaveCancelPanel();
+                            contentOptions = new ButtonOptionView({
+                                model: options.ButtonOption
+                            });
+                            buttonStylesOptionView = new ButtonStyleOptionView({
+                                model: options.ButtonStyleOption
+                            });
+                            advancedView = new AdvancedOptionView({
+                                model: options.advancedCSS
+                            });
+
+                            this.listenTo(buttonOptionsView, "save", function() {
+                                var isDifferentTextOnMobile = options.ButtonOption.isDifferentText;
+                                var textOnMobile = $('.text-different-input').val();
+                                if (isDifferentTextOnMobile && textOnMobile == '') {
+                                    this.error({
+                                        message: translate("errorWithoutTextOnMobile"),
+                                        title: translate("error")
+                                    });
+
+                                    return false
+                                }
+                                this.app.currentPanel.rightPanelView.hidePanel();
+                                this.app.currentPanel.rightPanelView.hideContent(buttonOptionsView);
+                                this.app.currentPanel.rightPanelView.removeContent(buttonOptionsView);
+                            });
+                            this.listenTo(buttonOptionsView, "cancel", function() {
+                                this.app.currentPanel.rightPanelView.hidePanel();
+                                this.app.currentPanel.rightPanelView.hideContent(buttonOptionsView);
+                                this.app.currentPanel.rightPanelView.removeContent(buttonOptionsView);
+                                
+                                options.ButtonOption.revert();
+                                options.ButtonStyleOption.revert();
+                            });
+                            buttonOptionsView.addPane(translate("ButtonOption"), contentOptions);
+                            buttonOptionsView.addPane(translate("ButtonStyleOption"), buttonStylesOptionView);
+                            buttonOptionsView.addPane(translate("advancedCSS"), advancedView);
+                            buttonOptionsView.setTitle(translate("buttonBlockOption"));
+                            
+                            this.app.currentPanel.rightPanelView.addContent(buttonOptionsView);
+                            this.app.currentPanel.rightPanelView.showContent(buttonOptionsView);
+                            this.app.currentPanel.rightPanelView.showPanel();
                         }
                     });
             return ButtonBlockView;
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonOptionView.js	(révision 13486)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonOptionView.js	(révision 13487)
@@ -21,6 +21,8 @@
                         tagName: "div",
                         className: "panel-content button-panel ",
                         events: {
+                            'click .text-different-radio': '_onClickAddDifferentText',
+                            'change .text-different-input': 'inputChange'
                         },
                         /**
                          * initialise l'objet
@@ -41,7 +43,29 @@
                             this.$el.html(this.template(templateVars));
                             this.$el.append(this._linkSelectorView.render({model:this.model.link}).el);
                             this.scrollables();
+                            this._checkTextDifferent();
+
                             return this;
+                        },
+                        _checkTextDifferent : function (){   
+                            var radio = $('.text-different-radio'), input = $('.text-different-input');
+
+                            if (this.model.isDifferentText) {
+                                radio.addClass('selected');
+                                input.show();
+                                input.val(this.model.textOnMobile);
+                            } else {
+                                radio.removeClass('selected');
+                                input.hide();
+                            }
+                        },
+                        _onClickAddDifferentText: function(){
+                            this.model.isDifferentText = !this.model.isDifferentText;
+
+                            this.render();
+                        },
+                        inputChange: function(event) {
+                            this.model.textOnMobile = event.target.value;
                         }
                     });
 
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js	(révision 13486)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-ca/i18n.js	(révision 13487)
@@ -41,5 +41,8 @@
     "vibrante-lead":"Vibrante Lead",
     "contour-lead":"Contour Lead",
     "browseIconTitle":"Browse the icon database",
-    "choose":"Choose"
+    "choose":"Choose",
+    "advancedCSS": "Avancé",
+    "DifferentTextOnMobile": "Texte différent sur mobile ?",
+    "errorWithoutTextOnMobile": "Vous n’avez pas indiqué le texte pour mobile. Vous devez indiquer le texte à afficher ou désactiver l’option."
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js	(révision 13486)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/fr-fr/i18n.js	(révision 13487)
@@ -41,6 +41,9 @@
     "vibrante-lead":"Vibrante Lead",
     "contour-lead":"Contour Lead",
     "browseIconTitle":"Parcourir la base d'icônes",
-    "choose":"Choisir"
+    "choose":"Choisir",
+    "advancedCSS": "Avancé",
+    "DifferentTextOnMobile": "Texte différent sur mobile ?",
+    "errorWithoutTextOnMobile": "Vous n’avez pas indiqué le texte pour mobile. Vous devez indiquer le texte à afficher ou désactiver l’option."
 
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js	(révision 13486)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js	(révision 13487)
@@ -42,7 +42,10 @@
         "vibrante-lead":"Vibrante Lead",
         "contour-lead":"Contour Lead",
         "browseIconTitle":"Browse the icon database",
-        "choose":"Choose"
+        "choose":"Choose",
+        "advancedCSS": "Advanced",
+        "DifferentTextOnMobile": "Different text on mobile ?",
+        "errorWithoutTextOnMobile": "You have not specified the text for mobile. You must indicate the text to display or deactivate the option."
     },
     "fr-fr": true,
     "fr-ca": true
