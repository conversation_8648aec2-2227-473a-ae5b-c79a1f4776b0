Revision: r13580
Date: 2024-12-05 14:29:48 +0300 (lkm 05 Des 2024) 
Author: rrakotoarinelina 

## Commit message
Whislist IDEO3.2 : Revoir edition d'un block de texte / block HTML src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Templates/htmlEdition.html

## Files changed

## Full metadata
------------------------------------------------------------------------
r13580 | rrakotoarinelina | 2024-12-05 14:29:48 +0300 (lkm 05 Des 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Views/HtmlBlockView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Views/HtmlEditionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/nls/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Templates/textEdition.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/i18n.js

Whislist IDEO3.2 : Revoir edition d'un block de texte / block HTML src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Templates/htmlEdition.html
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Views/HtmlBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Views/HtmlBlockView.js	(révision 13579)
+++ src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Views/HtmlBlockView.js	(révision 13580)
@@ -1,6 +1,16 @@
-define([ "jquery", "underscore", "text!../Templates/htmlBlock.html", "text!../Templates/htmlIframeBlock.html", "JEditor/PagePanel/Contents/Blocks/HtmlBlock/Views/HtmlBlockView", "JEditor/PagePanel/Contents/Blocks/Block/Views/BlockView", "JEditor/App/Messages/Error", "i18n!../nls/i18n", "ace/ace",
+define([ 
+	"jquery",
+	"underscore",
+	"text!../Templates/htmlBlock.html",
+	"text!../Templates/htmlIframeBlock.html",
+	"JEditor/PagePanel/Contents/Blocks/HtmlBlock/Views/HtmlBlockView",
+	"JEditor/PagePanel/Contents/Blocks/HtmlBlock/Views/HtmlEditionView",
+	"JEditor/PagePanel/Contents/Blocks/Block/Views/BlockView",
+	"JEditor/App/Messages/Error",
+	"i18n!../nls/i18n",
+	"ace/ace",
 // not in params
-"jqueryui/tabs" ], function($, _, htmlBlock, htmlIframeBlock, HtmlBlockView, BlockView, Error, translate, ace) {
+"jqueryui/tabs" ], function($, _, htmlBlock, htmlIframeBlock, HtmlBlockView,HtmlEditionView, BlockView, Error, translate, ace) {
 	ace = window.ace;
 	var HtmlBlockView = BlockView.extend({
 		attributes : {
@@ -18,9 +28,7 @@
 			this._super();
 			HtmlBlockView.instances[this.cid] = this; // on instancie notre
 														// HtmlBlockView
-			this._contentTemplate = this.buildTemplate(htmlBlock, translate);
 			this._iframeContent = this.buildTemplate(htmlIframeBlock, translate);
-			this.jsRun = this.model.options.html.js.run;
 			if (this.debug)
 				console.log('initializing #' + this.cid);
 
@@ -37,9 +45,7 @@
 		avoidPropagation : function(event) {
 			event.stopImmediatePropagation();
 		},
-		setJsRun : function(event, ui) {
-			this.jsRun = $(event.currentTarget).val();
-		},
+
 		/**
 		 * redimensionne l'iframe en fonction de sa hauteur C'est un callback de
 		 * postMessage, event.data contient les donnees transmises au message
@@ -50,38 +56,8 @@
 			this.dom[this.cid].content.find('iframe').css('height', event.data.height + 30);
 			this.triggerUpdate();
 		},
+		
 		/**
-		 * appel suite au clic sur le bouton valider de l'éditeur ace mise à
-		 * jour des données du modèle lancement du render de l'iframe
-		 */
-		validate : function(event) {
-			event.stopImmediatePropagation();
-			for (var i = 0; i < this.editors.length; i++) {
-				var editor = this.editors[i];
-				var mode = editor.getSession().getMode().$id.split('/');
-				switch (mode[2]) {
-				case 'javascript':
-					this.model.options.html.set('js', {
-						content : editor.getValue(),
-						run : this.jsRun
-					});
-					break;
-				case 'html':
-					var fakeContainer=$('<div></div>');
-					fakeContainer.html(editor.getValue());
-					var scripts = fakeContainer.find('script');
-					scripts.remove();
-					this.model.options.html.set(mode[2], fakeContainer.html());
-					break;
-				default:
-					this.model.options.html.set(mode[2], editor.getValue());
-					break;
-				}
-				editor.destroy();
-			}
-			this._display();
-		},
-		/**
 		 * Transposition des données dans un iframe pour isoler le code
 		 */
 		_display : function() {
@@ -134,33 +110,11 @@
 			this.triggerUpdate();
 		},
 		edit : function(event) {
-			// on passe le block en mode édition
-			this.$el.attr('data-editing', true);
-			this.dom[this.cid].overlay.hide();
-			this.$el.blur();
-			event.stopPropagation();
-			// on construit nos editeurs Ace
-			this.editors = [];
-			this.dom[this.cid].content.html(this._contentTemplate(this.model.options.html.toJSON()));
-			this.$('.tabs').tabs();
-			this.$('.code-editor>.contentAce').each(_.bind(function(index, element) {
-				var editor = ace.edit(element);
-				editor.getSession().setMode("ace/mode/" + $(element).attr('data-mode')); // html/css/javascript
-				var mode = $(element).attr('data-mode');
-				switch (mode) {
-				case 'javascript':
-					var jsContent = this.model.options.html.js;
-					editor.setValue(jsContent.content); // injection des données
-					break;
-				default:
-					editor.setValue(this.model.options.html.get(mode)); // injection
-																		// des
-																		// données
-					break;
-				}
-				this.editors.push(editor);
-			}, this));
-			this.triggerUpdate();
+			 this.htmlEditionDialog  = new HtmlEditionView({
+				 'htmlBlockView' : this
+			 }
+			 );
+			this.htmlEditionDialog.open();
 		},
 	});
 
Index: src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Views/HtmlEditionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Views/HtmlEditionView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Views/HtmlEditionView.js	(révision 13580)
@@ -0,0 +1,129 @@
+define([
+    "jquery",
+    "underscore",
+    "text!../Templates/htmlEdition.html",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/DialogView",
+    "i18n!../nls/i18n",
+    //not in params
+    "jqueryPlugins/dropdown",
+    "ace/ace"
+], function($, _, htmlEdition, Events, DialogView, translate,ace) {
+    ace = window.ace;
+    var HtmlEditionView = DialogView.extend({
+        className: 'htmlEdition',
+        events: {
+            'click .ace-close' : "validate",
+            'change .code-editor input[type="radio"]' : 'setJsRun',
+        },
+        
+        constructor: function(options) {
+            this.htmlBlockView = options.htmlBlockView;
+            this.originalContent = this.htmlBlockView.model.options.html.toJSON();
+            this.editor = null;
+            var opts = _.extend({
+                title: translate("htmlEdition"),
+                buttons: [
+                    {
+                        text: translate("cancel"),
+                        class: 'cancel',
+                        click: _.bind(this.onCancel, this)
+                    }
+                ],
+                width: 1000,
+                height: 600,
+                close: _.bind(this.onClose, this)
+            }, options);
+
+            return DialogView.call(this, opts);
+        },
+
+        initialize: function(options) {
+            this._super();
+            this._template = this.buildTemplate(htmlEdition, translate);
+            this.jsRun = this.htmlBlockView.model.options.html.js.run;
+        },
+
+        render: function() {
+            this._super();
+            this.undelegateEvents();
+            this.$el.html(this._template(this.originalContent));
+            this.initEditMode();
+            this.delegateEvents();
+            return this;
+        },
+
+        onCancel: function() {
+
+            this.$el.dialog('close');
+        },
+
+        onClose: function() {
+            
+            // Remove the dialog
+            this.remove();
+        },
+        initEditMode : function(){
+            this.editors = [];
+            this.$('.tabs').tabs();
+			this.$('.code-editor>.contentAce').each(_.bind(function(index, element) {
+				var editor = ace.edit(element);
+				editor.getSession().setMode("ace/mode/" + $(element).attr('data-mode')); // html/css/javascript
+                var mode = $(element).attr('data-mode');
+				switch (mode) {
+				case 'javascript':
+					var jsContent = this.htmlBlockView.model.options.html.js;
+					editor.setValue(jsContent.content); // injection des données
+					break;
+				default:
+					editor.setValue(this.htmlBlockView.model.options.html.get(mode)); // injection
+																		// des
+																		// données
+					break;
+				}
+				this.editors.push(editor);
+			}, this));
+             this.htmlBlockView.triggerUpdate();
+        },
+        setJsRun : function(event, ui) {
+			this.jsRun = $(event.currentTarget).val();
+		},
+        		/**
+		 * appel suite au clic sur le bouton valider de l'éditeur ace mise à
+		 * jour des données du modèle lancement du render de l'iframe
+		 */
+		validate : function(event) {
+			event.stopImmediatePropagation();
+			for (var i = 0; i < this.editors.length; i++) {
+				var editor = this.editors[i];
+				var mode = editor.getSession().getMode().$id.split('/');
+				switch (mode[2]) {
+				case 'javascript':
+					this.htmlBlockView.model.options.html.set('js', {
+						content : editor.getValue(),
+						run : this.jsRun
+					});
+					break;
+				case 'html':
+					var fakeContainer=$('<div></div>');
+					fakeContainer.html(editor.getValue());
+					var scripts = fakeContainer.find('script');
+					scripts.remove();
+					this.htmlBlockView.model.options.html.set(mode[2], fakeContainer.html());
+					break;
+				default:
+					this.htmlBlockView.model.options.html.set(mode[2], editor.getValue());
+					break;
+				}
+				editor.destroy();
+			}
+            this.$el.dialog('close');
+			this.htmlBlockView._display();
+		},
+        setJsRun : function(event, ui) {
+			this.jsRun = $(event.currentTarget).val();
+		},
+    });
+
+    return HtmlEditionView;
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/nls/fr-ca/i18n.js	(révision 13579)
+++ src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/nls/fr-ca/i18n.js	(révision 13580)
@@ -1 +1,5 @@
-define({"BLOCK_NAME":"Html","Valider":"Valider","html":"Html","css":"Css","javascript":"Javascript","jsHookInline":"inline","jsHookReady":"dom ready","jsHookonLoad":"on load","blockTitle":"Bloc HTML avanc\u00e9","blockInstructions":"Editez le code source de ce bloc en double-cliquant sur celui-ci"});
\ No newline at end of file
+define({"BLOCK_NAME":"Html","Valider":"Valider","html":"Html","css":"Css","javascript":"Javascript","jsHookInline":"inline","jsHookReady":"dom ready","jsHookonLoad":"on load","blockTitle":"Bloc HTML avanc\u00e9","blockInstructions":"Editez le code source de ce bloc en double-cliquant sur celui-ci",
+    "htmlEdition":"Modification html",
+    "save":"Enregistrer",
+    "cancel":"Annuler",
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/nls/fr-fr/i18n.js	(révision 13579)
+++ src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/nls/fr-fr/i18n.js	(révision 13580)
@@ -1 +1,5 @@
-define({"BLOCK_NAME":"Html","Valider":"Valider","html":"Html","css":"Css","javascript":"Javascript","jsHookInline":"inline","jsHookReady":"dom ready","jsHookonLoad":"on load","blockTitle":"Bloc HTML avanc\u00e9","blockInstructions":"Editez le code source de ce bloc en double-cliquant sur celui-ci"});
\ No newline at end of file
+define({"BLOCK_NAME":"Html","Valider":"Valider","html":"Html","css":"Css","javascript":"Javascript","jsHookInline":"inline","jsHookReady":"dom ready","jsHookonLoad":"on load","blockTitle":"Bloc HTML avanc\u00e9","blockInstructions":"Editez le code source de ce bloc en double-cliquant sur celui-ci",
+    "htmlEdition":"Modification html",
+    "save":"Enregistrer",
+    "cancel":"Annuler",
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/nls/i18n.js	(révision 13579)
+++ src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/nls/i18n.js	(révision 13580)
@@ -1 +1,5 @@
-define({"root":{"BLOCK_NAME":"HTML","Valider":"Validate","html":"HTML","css":"CSS","javascript":"Javascript","jsHookInline":"Inline","jsHookReady":"Dom ready","jsHookonLoad":"On load","blockTitle":"Power advanced HTML","blockInstructions":"Edit the source code of this block by double-clicking on it"},"fr-fr":true, "fr-ca":true});
\ No newline at end of file
+define({"root":{"BLOCK_NAME":"HTML","Valider":"Validate","html":"HTML","css":"CSS","javascript":"Javascript","jsHookInline":"Inline","jsHookReady":"Dom ready","jsHookonLoad":"On load","blockTitle":"Power advanced HTML","blockInstructions":"Edit the source code of this block by double-clicking on it",
+    "htmlEdition":"Html edit",
+    "save":"Save",
+    "cancel":"Cancel",
+},"fr-fr":true, "fr-ca":true});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Templates/textEdition.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Templates/textEdition.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Templates/textEdition.html	(révision 13580)
@@ -0,0 +1,17 @@
+<style>
+/* Styles pour assurer la visibilité du texte dans CKEditor */
+.cke_editable,
+.cke_wysiwyg_frame, 
+.cke_wysiwyg_div {
+    color: black !important;
+    background-color: #000000 !important;
+}
+
+</style>
+<div class="text-edition-container">
+    <div class="editor-wrapper">
+        <div id="text-content" class="editor-content">
+            <!-- Le contenu sera injecté ici par CKEditor -->
+        </div>
+    </div>
+</div>
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 13579)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextBlockView.js	(révision 13580)
@@ -5,11 +5,12 @@
     "JEditor/Commons/Events",
     "JEditor/PagePanel/Contents/Ancestors/ContentView",
     "JEditor/PagePanel/Contents/Blocks/Block/Views/BlockView",
+    "JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView",
     "JEditor/PagePanel/Contents/Blocks/Block/Block",
     "JEditor/PagePanel/Contents/Zones/Models/ZoneDependency",
     "ckeditor",
     "i18n!../nls/i18n"
-], function($, _, block, Events, ContentView, BlockView, Block, ZoneDependency, CKEDITOR, translate) {
+], function($, _, block, Events, ContentView, BlockView,TextEditionView, Block, ZoneDependency, CKEDITOR, translate) {
     /**
      * Vue des Blocs de texte
      * @class TextBlockView
@@ -86,6 +87,9 @@
                             } else {
                                 this.CKconfig.toolbar = this.defaultToolbar;
                             }
+                            //initialiser modal 
+                            this.textEditionDialog = new TextEditionView({textObject:this});
+                        
                         },
                         attributes:function(){
                             return {"class": "block", tabindex: 1};
@@ -95,6 +99,9 @@
                                 this.constructor.editor.destroy();
                             dependency.saved();
                         },
+                        /**
+                         * Removes the text dependency from the model's zone when the TextBlockView is removed.
+                         */
                         onRemove: function() {
                             this.model.zone.removeDependency(this.textDependency);
                         },
@@ -104,94 +111,16 @@
                          * @argument {jQuery.Event} event Évenement jQuery
                          */
                         edit: function(event) {
+                            
                             var editable = this.dom[this.cid].content;
-                            var editor = CKEDITOR.inline(editable[0], this.CKconfig);
-                            event.stopPropagation();
-                            if (this.constructor.editor) {
-                                this.constructor.editor.destroy();
-                            }
-                            this.$el.blur();
-                            editable.attr('contenteditable', 'true');
-                            editor.on('instanceReady', this._onCKReady, this);
-                            editor.on('destroy', this._onCKDestroy, this);
-                            editor.on('change', this._onCKChange, this);
-                            this.constructor.editor = editor;
-                        },
-                        showOptionPanel: function() {
-                            return ContentView.prototype.edit.call(this);
-                        },
-                        /**
-                         * Exécutée dés que CKEditor est prêt
-                         * @private
-                         */
-                        _onCKReady: function(event) {
-                            this.$el.attr('data-editing', 'true');
-                            this.constructor.editor = event.editor;
-                            var zone = this.model.zone;
-                            if (zone) {
-                                this.textDependency = new ZoneDependency({save: this.saveText}, this);
-                                zone.addDependency(this.textDependency);
-                                this.listenTo(this.model, Events.ContentEvents.REMOVED, this.onRemove);
-                            }
-                            this.constructor.editor.JEditorTextBlockView = this;
-                            var $editor = $('#cke_' + this.constructor.editor.name);
-                            this.constructor.editor.setReadOnly(false);
-                            this.constructor.editor.setData(this.model.content);
-                            if (this.$el.hasClass('focused'))
-                                this.$el.toggleClass('focused');
-                            this.dom[this.cid].overlay.hide();
-                            this.dom[this.cid].content.on('click.editBlock', function(event) {
+                            editable.on('click.editBlock', function(event) {
                                 event.stopPropagation();
                             });
-                            $(document).on('click.editBlock', _.bind(function(event) {
-                                this.constructor.editor.destroy();
-                            }, this));
-                            $editor.on("click.preventEscape", function(event) {
-                                event.stopPropagation();
-                            });
-                            this.constructor.editor.on('dialogShow', function(event) {
-                                //console.log(event);
-                                var dialogElement = $(event.data._.element.$);
-                                dialogElement.on('click.preventEscape', function(event) {
-                                    event.stopPropagation();
-                                });
-                            });
-                            event.editor.focus();
-                            //this.dom[this.cid].content.focus();
+
+                            this.textEditionDialog.open();
+
                         },
                         /**
-                         * Exécutée lors de la destruction de CKEditor
-                         * @private
-                         */
-                        _onCKDestroy: function(event) {
-                            var editor = event.editor;
-                            //unbindEvents
-                            var $editor = $('#cke_' + event.editor.name);
-                            this.dom[this.cid].content.off('click.editBlock');
-                            $(document).off('click.editBlock');
-                            $editor.off("click.preventEscape");
-                            ///other
-                            this.$el.attr('data-editing', 'false');
-                            this.dom[this.cid].content.attr('contenteditable', 'false');
-                            this.model.content = editor.getData();
-                            this.delegateEvents();
-                            this.dom[this.cid].overlay.show();
-                            this.triggerUpdate();
-                            this.constructor.editor = null;
-                            if (this.model.zone)
-                                this.model.zone.removeDependency(this.textDependency);
-                        },
-                        /**
-                         * Fired when the content of the editor is changed.
-                         * @private
-                         */
-                        _onCKChange: function(event) {
-                            window.clearTimeout(this.changeTimeout);
-                            this.changeTimeout = window.setTimeout(this.updateHeight, 500, this);
-                        },
-                        noSelect: function() {
-                        },
-                        /**
                          * maj hauteur bloc texte
                          */
                         updateHeight: function(that) {
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js	(révision 13580)
@@ -0,0 +1,205 @@
+define([
+    "jquery",
+    "underscore",
+    "text!../Templates/textEdition.html",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/DialogView",
+    "i18n!../nls/i18n",
+    "ckeditor",
+    "JEditor/PagePanel/Contents/Zones/Models/ZoneDependency",
+    "JEditor/PagePanel/Contents/Ancestors/ContentView",
+    //not in params
+    "jqueryPlugins/dropdown"
+], function($, _, textEdition, Events, DialogView, translate, CKEDITOR,ZoneDependency,ContentView) {
+    var TextEditionView = DialogView.extend({
+        className: 'textEdition',
+        events: {
+            'click .cke_dialog_ui_input_text': 'handleInputClick',
+        },
+        // events:  {'click .action.delete': "confirmDelete", 'keydown .content': '_onKeyPress', 'dblclick .content': 'avoidPropagation'},
+        
+        constructor: function(options) {
+            this.textBlockView = options.textObject;
+            this.originalContent = this.textBlockView.model.get('content');
+            this.editor = null;
+
+            var opts = _.extend({
+                title: translate("textEdition"),
+                buttons: [
+                    {
+                        text: translate("save"),
+                        class: 'okay',
+                        click: _.bind(this.onOk, this)
+                    },
+                    {
+                        text: translate("cancel"),
+                        class: 'cancel',
+                        click: _.bind(this.onCancel, this)
+                    }
+                ],
+                width: 1000,
+                height: 560,
+                close: _.bind(this.onClose, this)
+            }, options);
+
+            return DialogView.call(this, opts);
+        },
+
+        initialize: function(options) {
+            this._super();
+            this._template = this.buildTemplate(textEdition, translate);
+            this.listenTo(this.textBlockView.model, Events.BackboneEvents.CHANGE + ':content', this.onContentChange);
+            this.trigger(Events.LoadEvents.LOAD_START, this);
+            // initialisation CKEditor
+        },
+        
+        render: function() {
+            
+            this._super();
+            this.undelegateEvents();
+            this.$el.html(this._template());
+            
+            this.initializeCKEditor();
+            this.delegateEvents();
+            return this;
+        },
+
+        initializeCKEditor: function() {
+            // Récupère la configuration CKEditor depuis textBlockView
+            var editorConfig = _.extend({}, this.textBlockView.CKconfig, {
+                height: '400px'
+            });
+
+            // Initialise CKEditor dans le div content du template
+            this.editor = CKEDITOR.replace(this.$('#text-content')[0], editorConfig);
+
+            // Définit le contenu initial lorsque l'éditeur est prêt
+            this.editor.on('instanceReady', this._onCKReady, this);
+
+            // z-index pour les barres d'outils
+            this.setupStyleComboZIndexHandler();
+
+            // Gère les changements de l'éditeur
+            this.editor.on('change', _.bind(this.onEditorChange, this));
+
+        },
+        /**
+        * Exécutée dès que CKEditor est prêt
+        * @private
+        */
+        _onCKReady: function(event) {
+            this.$el.attr('data-editing', 'true');
+            this.constructor.editor = event.editor;
+            var zone = this.textBlockView.model.zone;
+            if (zone) {
+                this.textDependency = new ZoneDependency({save: this.textBlockView.saveText}, this);
+                zone.addDependency(this.textDependency);
+                this.listenTo(this.textBlockView.model, Events.ContentEvents.REMOVED, this.textBlockView.onRemove);
+            }
+            this.constructor.editor.JEditorTextBlockView = this;
+            
+            this.constructor.editor.setReadOnly(false);
+            this.constructor.editor.setData(this.originalContent);
+            if (this.$el.hasClass('focused'))
+                this.$el.toggleClass('focused');
+
+            this.constructor.editor.on('dialogShow', function(event) {
+           
+                var dialogElement = $(event.data._.element.$);
+                var firstModal = $('.textEdition');
+            
+                // Désactive temporairement le piège de focus sur la première modale
+                firstModal.attr('aria-hidden', 'true');
+                
+                // Forcer le focus sur le nouveau dialog
+                dialogElement.attr('tabindex', '-1').focus();
+                
+                // Assurer que le focus ne sorte pas du second dialog
+                dialogElement.on('focusin', function(e) {
+                    e.stopPropagation();
+                });
+            
+                //Restorer le focus sur la première modale lorsque le second dialog se ferme
+                dialogElement.on('dialogclose', function() {
+                    firstModal.removeAttr('aria-hidden');
+                });
+            
+                // ajouter un z-index plus élevé pour le nouveau dialog
+                dialogElement.css({
+                    'z-index': '1000000004'
+                }).on('click.preventEscape keydown.preventEscape', function(e) {
+                    e.stopPropagation();
+                });
+
+            });
+
+            event.editor.focus();
+            this.trigger(Events.LoadEvents.LOAD_SUCCESS, this);
+        },
+
+        onEditorChange: function() {
+            // Gère les changements en temps réel dans l'éditeur
+            // Vous pouvez ajouter une fonctionnalité de sauvegarde automatique ici si nécessaire
+        },
+
+        onContentChange: function() {
+            // Met à jour le contenu de l'éditeur si le modèle change de manière externe
+            if (this.editor) {
+                this.editor.setData(this.textBlockView.model.get('content'));
+            }
+        },
+
+        onCancel: function() {
+            if (this.textBlockView) {
+                this.textBlockView.model.set('content', this.originalContent);
+            }
+            this.$el.dialog('close');
+        },
+
+        onOk: function() {
+            if (this.editor && this.textBlockView) {
+                var newContent = this.editor.getData();
+                
+                // mettre a jour le model si ok
+                this.textBlockView.model.set('content', newContent);
+                
+                this.textBlockView.triggerUpdate();
+                
+                if (this.textBlockView.textDependency) {
+                    this.textBlockView.textDependency.saved();
+                }
+            }
+            
+            this.$el.dialog('close');
+        },
+
+        onClose: function() {
+            if (this.editor) {
+                this.editor.destroy();
+                this.editor = null;
+            }
+            
+            this.remove();
+        },
+        setupStyleComboZIndexHandler: function() {
+            //pour styles, format et fontsize du toolbar ckeditor
+            $(document).on('click', '.cke_combo', _.bind(function(event) {
+                var $comboPanel = $('.cke_combopanel');
+                
+                if ($comboPanel.length) {
+                    $comboPanel.css({
+                        'z-index': '1000000004', 
+                    });
+                }
+            }, this));
+        },
+        showOptionPanel: function() {
+            //fermeture du modal
+            this.$el.dialog('close');
+            return ContentView.prototype.edit.call(this.textBlockView);
+        },
+
+    });
+
+    return TextEditionView;
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/fr-ca/i18n.js	(révision 13579)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/fr-ca/i18n.js	(révision 13580)
@@ -1 +1,5 @@
-define({"BLOCK_NAME":"Texte","block":"Bloc","move":"Déplacer","edit":"Éditer","delete":"Supprimer","textBlockOption":"Options du bloc de texte","defaultAccordionTitle":"Un titre"});
\ No newline at end of file
+define({"BLOCK_NAME":"Texte","block":"Bloc","move":"Déplacer","edit":"Éditer","delete":"Supprimer","textBlockOption":"Options du bloc de texte","defaultAccordionTitle":"Un titre",
+    "textEdition":"Modification de texte",
+    "save":"Enregistrer",
+    "cancel":"Annuler",
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/fr-fr/i18n.js	(révision 13579)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/fr-fr/i18n.js	(révision 13580)
@@ -1 +1,5 @@
-define({"BLOCK_NAME":"Texte","block":"Bloc","move":"Déplacer","edit":"Éditer","delete":"Supprimer","textBlockOption":"Options du bloc de texte","defaultAccordionTitle":"Un titre"});
\ No newline at end of file
+define({"BLOCK_NAME":"Texte","block":"Bloc","move":"Déplacer","edit":"Éditer","delete":"Supprimer","textBlockOption":"Options du bloc de texte","defaultAccordionTitle":"Un titre",
+    "textEdition":"Modification de texte",
+    "save":"Enregistrer",
+    "cancel":"Annuler",
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/i18n.js	(révision 13579)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/nls/i18n.js	(révision 13580)
@@ -1 +1,5 @@
-define({ "root": {"BLOCK_NAME":"Text","block":"Block","move":"Move","edit":"Edit","delete":"Delete","textBlockOption":"Text block options","defaultAccordionTitle":"A title"}, "fr-fr":true, "fr-ca":true })
\ No newline at end of file
+define({ "root": {"BLOCK_NAME":"Text","block":"Block","move":"Move","edit":"Edit","delete":"Delete","textBlockOption":"Text block options","defaultAccordionTitle":"A title",
+    "textEdition":"Text edit",
+    "save":"Save",
+    "cancel":"Cancel",
+}, "fr-fr":true, "fr-ca":true })
\ No newline at end of file
