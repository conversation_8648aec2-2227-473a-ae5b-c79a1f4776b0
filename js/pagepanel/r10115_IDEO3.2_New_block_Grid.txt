Revision: r10115
Date: 2023-01-09 16:18:46 +0300 (lts 09 Jan 2023) 
Author: norajaonarivelo 

## Commit message
IDEO3.2 New block Grid

## Files changed

## Full metadata
------------------------------------------------------------------------
r10115 | norajaonarivelo | 2023-01-09 16:18:46 +0300 (lts 09 Jan 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselFileEditionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/OptionTypeLienView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/GridBlock.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Models/GridOption.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Models/GridStyleOption.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridBlock.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridStyleOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridBlockView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridOptionTypeLienView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridStyleOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/i18n.js

IDEO3.2 New block Grid
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselFileEditionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselFileEditionView.js	(révision 10114)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselFileEditionView.js	(révision 10115)
@@ -20,7 +20,6 @@
             if (!options)
                 var options = {};
             options.width = 750;
-            options.title = this.translate('editMyGallery')
             options.height = 600;
             options.buttons = [
                 {
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/OptionTypeLienView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/OptionTypeLienView.js	(révision 10114)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/OptionTypeLienView.js	(révision 10115)
@@ -24,8 +24,8 @@
             this.LinkText=false;
             var TypeLien,Info,Action;
             TypeLien = this.options.TypeLien ? this.options.TypeLien : null;
-            Action = this.options.Action ? this.options.Action : null;
-            Info = this.options.Info ? this.options.Info : null;
+            Action = (this.options.Action!==null) ? this.options.Action : null;
+            Info = (this.options.Info!==null)  ? this.options.Info : null;
             if(Info ==0){
                 this.LinkText=true;
                 if(TypeLien ==2){
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/GridBlock.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/GridBlock.js	(révision 10114)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/GridBlock.js	(révision 10115)
@@ -23,7 +23,7 @@
                                 this.options.add(new GridStyleOption());
                         },
                         _onDesignOptionChange: function() {
-                            this.trigger(Events.BackboneEvents.CHANGE, this, {});
+                           // this.trigger(Events.BackboneEvents.CHANGE, this, {});
                         },
                         _onDesignChange: function() {
                             this.stopListening(this.options.grid.previousAttributes().design.options, Events.BackboneEvents.CHANGE);
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Models/GridOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Models/GridOption.js	(révision 10114)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Models/GridOption.js	(révision 10115)
@@ -25,7 +25,15 @@
              * @lends GridOptions.prototype
              */
                     {
-                        defaults: {priority: 70, optionType: 'gridOption', fileGroup: null, zoom:true, captionType: "caption-hidden"},
+                        defaults: {
+                            priority: 70, 
+                            optionType: 'gridOption', 
+                            fileGroup: null, 
+                            grilleInfo:0, 
+                            grilleAction: 0,
+                            grilleTypeLink :1,
+                            fileGroupId: null
+                        },
                         initialize: function() {
                             this._super();
                             this.on('change:fileGroup', this.onFileGroupChange);
@@ -51,12 +59,12 @@
                             var captionType = this.getCaptionType();
                             return this;
                         },
-                        validate: function(attributes, options) {
-                            if (allowedCaptionType.lastIndexOf(attributes.captionType)<=-1)
-                                return {field: "captionType", message: translate("Invalid_captionType")};
-                        }
+                        // validate: function(attributes, options) {
+                        //     if (allowedCaptionType.lastIndexOf(attributes.captionType)<=-1)
+                        //         return {field: "captionType", message: translate("Invalid_captionType")};
+                        // }
                     }
             );
-            GridOption.SetAttributes(['fileGroup', 'zoom', 'captionType']);
+            GridOption.SetAttributes(['fileGroup', 'grilleInfo', 'grilleAction','grilleTypeLink','fileGroupId']);
             return GridOption;
         });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Models/GridStyleOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Models/GridStyleOption.js	(révision 10114)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Models/GridStyleOption.js	(révision 10115)
@@ -16,16 +16,20 @@
              * @lends GalleryStyleOptions.prototype
              */
                     {
-                        defaults: {optionType: 'gridStyleOption', priority: 80, rowHeight:160, justify:true, margins:10},
+                        defaults: {
+                            optionType: 'gridStyleOption', 
+                            priority: 80, 
+                            grilleStyle :   0,
+                            grilleFormat    :   'landscape',
+                            grilleNbreImage :   1,
+                            grilleStyleAff  :   1,
+                        },
                         
                         validate: function(attributes, options) {
-                            if (!attributes.rowHeight || attributes.rowHeight < 140 || attributes.rowHeight > 320)
-                                return {field: "rowHeight", message: translate("Invalid_rowHeight")};
-                            if (!attributes.margins || attributes.margins < 0 || attributes.margins > 30)
-                                return {field: "margins", message: translate("Invalid_margins")};
+                           
                         }
                     }
             );
-            GridStyleOption.SetAttributes(['rowHeight', 'justify', 'margins']);
+            GridStyleOption.SetAttributes(['grilleStyle', 'grilleFormat', 'grilleNbreImage','grilleStyleAff']);
             return GridStyleOption;
         });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridBlock.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridBlock.html	(révision 10114)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridBlock.html	(révision 10115)
@@ -1,25 +1,5 @@
 <div id="<%= uid %>">
     <% if(empty){ %>
         <div class="empty-gallery"><span class="icon-photogrid-block"></span></div>
-    <% } else { %>
-            <% files.each(function(file){%>
-                <% if(zoom){%>
-                <div class="jg-entry">
-                    <a href="<%= file.fileUrl %>" class="justifiedLightBox">
-                        <img src="<%= file.fileUrl %>" alt="<%= file.desc[language] %>">
-                    </a>
-                    <% if( captionType == 'caption-visible' ){ %>
-                    <div class="caption <%=captionType%>"><%= file.title[language]%></div>
-                    <% } %>
-                </div>
-                <% } else { %>
-                <div class="jg-entry">
-                    <img src="<%= file.fileUrl %>" alt="<%= file.desc[language] %>">
-                    <% if( captionType == 'caption-visible' ){ %>
-                    <div class="caption <%=captionType%>"><%= file.title[language]%></div>
-                    <% } %>
-                </div>
-            <% } %>
-        <% }); %>
     <% } %>
 </div>
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridOption.html	(révision 10114)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridOption.html	(révision 10115)
@@ -21,62 +21,75 @@
                     <div class="action-desc"><%=__("clickToAddImages")%></div>
                 </div>
             </div>
-            <div class="button-align-checkbox  mb1">
-            <% var _id= _.uniqueId('zoomOn'); %>
-
-                <label for="<%=_id%>" class="label">
-                    <input type="checkbox" id="<%=_id%>" class="zoom-picture" name="zoom" <%= zoom?'checked="checked"':''%>/>
-                    <span class="classic-modifier">
-                        <span class="icon-checked"></span>
-                        <span class="icon-unchecked"></span>
-                    </span>
-                    <span class="classic-modifier-desc"><span class="icon-find"></span> <%= __("zoomOnClic") %></span>
-                </label>
-
-            </div>
         </div>
     </article>
     <article class="panel-option">
-        <header>
-            <h3 class="option-name"><span class="icon-text"></span><%= __("photoTitle")%></h3>
-            <p class="panel-content-legend"><%= __("photoTitleLegend")%></p>
-        </header>
-        <div class="option checkbox">
-            <% var _id= _.uniqueId('captionType'); %>
-            <div class="button-align-checkbox">
-                <div>
-                    <label for="<%=_id%>" class="label">
-                        <input id="<%= _id%>" type="radio" class="field-input" value="caption-visible" name="captionType" <%=captionType==="caption-visible"? ' checked="checked" ':'' %> />
-                        <span class="classic-modifier">
-                            <span class="icon-radio-active"></span>
-                            <span class="icon-radio-inactive"></span>
-                        </span>
-                        <span class="classic-modifier-desc"> <%= __("permanentTitle")%></span>
-                    </label>
-                </div>
-                <% _id= _.uniqueId('captionType');      %>
-                <div>
-                    <label for="<%=_id%>" class="label">
-                        <input id="<%= _id%>" type="radio" class="field-input" value="caption-over" name="captionType" <%=captionType==="caption-over"? ' checked="checked" ':'' %> />
-                        <span class="classic-modifier">
-                            <span class="icon-radio-active"></span>
-                            <span class="icon-radio-inactive"></span>
-                        </span>
-                        <span class="classic-modifier-desc"> <%= __("onMouseOverTitle")%></span>
-                    </label>
-                </div>
-                <% _id= _.uniqueId('captionType');      %>
-                <div>
-                    <label for="<%=_id%>" class="label">
-                        <input id="<%= _id%>" type="radio" class="field-input" value="caption-hidden" name="captionType" <%=captionType==="caption-hidden"? ' checked="checked" ':'' %> />
-                        <span class="classic-modifier">
-                            <span class="icon-radio-active"></span>
-                            <span class="icon-radio-inactive"></span>
-                        </span>
-                        <span class="classic-modifier-desc"> <%= __("noTitle")%></span>
-                    </label>
-                </div>
-            </div>
+        <div class="figcaption-img" id="figcaptionInfo">
+            <p class="panel-legend"><%=__("figcaptionImage")%></p>
+            <% var _id=_.uniqueId('figcaption') ;%>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="figcaption" value="1" <%=(info==1)?"checked=checked":""%> />
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><%= __("figShowTitle")%></div>
+
+            </label>
+            <% var _id=_.uniqueId('figcaption') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="figcaption" value="2" <%=(info==2)?"checked=checked":""%>/>
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><%= __("figShowAll")%></div>
+            </label>
+            <% var _id=_.uniqueId('figcaption') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="figcaption" value="0" <%=(info==0)?"checked=checked":""%>  />
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><%= __("doNothing")%></div>
+            </label>
         </div>
+        <div class="link-img">
+            <p class="panel-legend"><%=__("selectClickActionImage")%></p>
+            <% var _id=_.uniqueId('link_action') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkAction" value="1"  <%=(action==1)?"checked=checked":""%>/>
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><span class="icon-find"></span>&emsp;<%= __("ActionZoom")%></div>
+
+            </label>
+            <% var _id=_.uniqueId('link_action') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkAction" value="2" <%=(action==2)?"checked=checked":""%> />
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><span class="icon-link"></span>&emsp;<%= __("ActionPartner")%></div>
+
+            </label>
+            <% var _id=_.uniqueId('link_action') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkAction" value="0" <%=(action==0)?"checked=checked":""%> />
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><span class="icon-delete"></span>&emsp;<%= __("doNothing")%></div>
+
+            </label>
+        </div>
+        <div class="link-img" id="typeDeLien">
+            
+        </div>
     </article>
 </div>
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridStyleOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridStyleOption.html	(révision 10114)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridStyleOption.html	(révision 10115)
@@ -1,47 +1,99 @@
 <div class="panel-option-container animated  mr15">
     <article class="panel-option">
         <header>
-            <h3 class="option-name"><span class="i-block-photogrid-bigheight"></span> <%=__("photoSize")%></h3>
-            <p class="panel-content-legend"><%= __("photoSizeLegend")%></p>
+            <h3 class="option-name"></span> <%=__("styleDeGrille")%></h3>
+            <p class="panel-content-legend"><%= __("styleDeGrilleLegend")%></p>
         </header>
-        <div class="option-content">
-            <div class="slider-container  grid-slider-container">
-                <span class="less i-block-photogrid-smallheight"></span>
-                <div class="row-height-slider slider-container"></div>
-                <span class="more i-block-photogrid-bigheight"></span>
+        <div>
+            <div class="category-content radio-transformed stylleDeGrille">
+                <% var _id= _.uniqueId('gridStyle'); %>
+                <div><span class="effect-radio <%=(grilleStyle===0)?'active':''%>" id="<%=_id %>" data-value="0" data-helper="masonry"><span class="helper"><span class="help"><%=__("masonryLegend")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-transition-alpha"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
+                <% var _id= _.uniqueId('gridStyle'); %>
+                <div><span class="effect-radio <%=(grilleStyle===1)?'active':''%>" id="<%=_id %>" data-value="1" data-helper="grid"><span class="helper"><span class="help"><%=__("gridLegend")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-transition-horizontal"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
             </div>
         </div>
+    </article>
+    <article class="panel-option" id="formatImage" <%=(grilleStyle===0)?'style="display:none"':''%>>
+        <header>
+            <h3 class="option-name"></span> <%=__("FormatImage")%></h3>
+            <p class="panel-content-legend"><%= __("FormatImageLegend")%></p>
+        </header>
         <div>
-            <div class="option checkbox">
-                <div class="button-align-checkbox">
-                    <% var _id= _.uniqueId('justify'); %>
-                    <label for="<%=_id%>" class="label">
-                        <input type="checkbox" id="<%=_id%>" class="justify-last-row" name="justify" <%= justify?'checked="checked"':''%>/>
-                        <span class="classic-modifier">
-                            <span class="icon-checked"></span>
-                            <span class="icon-unchecked"></span>
-                        </span>
-                        <span class="classic-modifier-desc"><%= __("justifyLastRow") %></span>
-                    </label>
-
-                    <p class="panel-content-legend"><%= __("justifyLastRowLegend")%></p>
-                </div>
+            <div class="category-content radio-transformed" id="Radioformatimage">
+                <% var _id= _.uniqueId('formatImage'); %>
+                <div><span class="effect-radio <%=(grilleFormat=='landscape')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="landscape" data-helper="landscape"><span class="helper"><span class="help"><%=__("landscape")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-transition-alpha"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
+                <% var _id= _.uniqueId('formatImage'); %>
+                <div><span class="effect-radio <%=(grilleFormat=='portrait')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="portrait" data-helper="portrait"><span class="helper"><span class="help"><%=__("portrait")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-transition-horizontal"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
+                <% var _id= _.uniqueId('formatImage'); %>
+                <div><span class="effect-radio <%=(grilleFormat=='square')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="square" data-helper="square"><span class="helper"><span class="help"><%=__("square")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-transition-vertical"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
             </div>
         </div>
     </article>
+
 </div>
-<div class="mr15 grid-option-margin">
+
+    
+<div class="mr15 grid-option-margin grid-nbreImage">
     <article class="panel-option">
         <header>
-            <h3 class="option-name"><span class="i-block-photogrid-biglmargin"></span> <%=__("gridMargin")%></h3>
-            <p class="panel-content-legend"><%= __("gridMarginLegend")%></p>
+            <h3 class="option-name"><%=__("gridNombreImage")%></h3>
+            <p class="panel-content-legend"><%= __("gridNombreImageLegend")%></p>
         </header>
         <div class="option-content">
-            <div class="slider-container  grid-slider-container">
-                <span class="less i-block-photogrid-smallmargin"></span>
-                <div class="margin-size-slider slider-container"></div>
-                <span class="more i-block-photogrid-biglmargin"></span>
-            </div>
+            <div class="slider-container"><span class="icon-less"></span><div class="slider"></div><span class="icon-more"></span></div>
         </div>
     </article>
+</div>
+<div class="panel-option-container animated">
+    <article class="panel-option background-color">
+        <header>
+            <h3 class="option-name"><%=__("gridStyleAffichage")%></h3>
+            <p class="panel-content-legend"><%=__("gridStyleAffichageDesc")%></p>
+        </header>
+        <div class="option-content colors">
+            <%  var _id=_.uniqueId('gridStyleAffichage');
+            %>
+                <input type="radio" class="select-box" name="AffichageCarrouselStyle" value="1" id="<%=_id %>" <%=(grilleStyleAff==1)?'checked':''%>>
+                   <label for="<%=_id %>">
+                    <div class="wrapper">
+                        <div class="vertical-wrap">
+                            <span class="icon-wrapper">
+                                <span class="icon icon-minimal-gallery"><span class="icon-image"></span></span>
+                            </span>
+                            <span class="name"><%= __("Style")%> 1</span>
+                            <span class="desc">lorem ipsum</span>
+                        </div>
+                    </div>
+                </label>
+                
+                <%  var _id=_.uniqueId('gridStyleAffichage');%>
+                <input type="radio" class="select-box" name="AffichageCarrouselStyle" value="2" id="<%=_id %>" <%=(grilleStyleAff==2)?'checked':''%>>
+                   <label for="<%=_id %>">
+                    <div class="wrapper">
+                        <div class="vertical-wrap">
+                            <span class="icon-wrapper">
+                                <span class="icon icon-medium-gallery"><span class="icon-image"></span></span>
+                            </span>
+                            <span class="name"><%= __("Style")%> 2</span>
+                            <span class="desc">lorem ipsum</span>
+                        </div>
+                    </div>
+                </label>
+                
+                <%  var _id=_.uniqueId('gridStyleAffichage');%>
+                <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="3" id="<%=_id %>" <%=(grilleStyleAff==3)?'checked':''%>>
+                   <label for="<%=_id %>">
+                    <div class="wrapper">
+                        <div class="vertical-wrap">
+                            <span class="icon-wrapper">
+                                <span class="icon icon-heavy-gallery"><span class="icon-image"></span></span>
+                            </span>
+                            <span class="name"><%= __("Style")%> 3</span>
+                            <span class="desc">lorem ipsum</span>
+                        </div>
+                    </div>
+                </label>
+                
+        </div>
+    </article>
 </div>
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridBlockView.js	(révision 10114)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridBlockView.js	(révision 10115)
@@ -30,12 +30,10 @@
             opts = this.model.options.gridOption;
             styleOpts = this.model.options.gridStyleOption;
             fileGroup = this.getFileGroup();
-            caption = opts.captionType;
-            zoom = opts.zoom;
-            justify = styleOpts.justify;
-            rowHeight = styleOpts.rowHeight;
-            margins = styleOpts.margins;
+            htmlContent = this.template({uid: uid,  empty: true});
+            this.$('.content').html(htmlContent);
 
+            /* 
             if ((fileGroup instanceof FileGroup) && fileGroup.length != 0) {
                 emptyGroup = false;
                 mesfichiers = fileGroup.files;
@@ -46,7 +44,7 @@
                 emptyGroup = true;
                 htmlContent = this.template({uid: uid, empty: true, justify: justify, zoom: zoom, rowHeight: rowHeight, margins: margins});
             }
-
+            
             this.$('.content').html(htmlContent);
 
             if ((fileGroup instanceof FileGroup) && fileGroup.length != 0) {
@@ -64,7 +62,7 @@
                     that.triggerUpdate();
                 });
             }
-
+            */
             return this;
         }
     });
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridOptionTypeLienView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridOptionTypeLienView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridOptionTypeLienView.js	(révision 10115)
@@ -0,0 +1,54 @@
+define([
+    "jquery",
+    "text!../Templates/gridOptionTypeLien.html",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/View",
+    "i18n!../nls/i18n",
+    
+], function($, gridOptionTypeLien, Events, View, translate) {
+    var gridOptionTypeLienView = View.extend({
+        events: {
+        },
+        initialize: function() {
+            this._super();
+            this.BouttonMoreInfo=false;
+            this.LinkText=false;
+            this._template = this.buildTemplate(gridOptionTypeLien, translate);
+            if (!this.options.TypeLien)
+                this.options.TypeLien = null;
+
+           
+        },
+        render: function() {
+            this.BouttonMoreInfo=false;
+            this.LinkText=false;
+            var TypeLien,Info,Action;
+            TypeLien = this.options.grilleTypeLink ? this.options.grilleTypeLink : null;
+            Action = (this.options.grilleAction!==null) ? this.options.grilleAction : null;
+            Info = (this.options.grilleInfo!==null) ? this.options.grilleInfo : null;
+            if(Info ==0){
+                this.LinkText=true;
+                if(TypeLien ==2){
+                    TypeLien =1;
+                }
+            }
+            if(Action == 1){
+                this.BouttonMoreInfo =true;
+                if((TypeLien ==3) ||(TypeLien== 2 && Info==0)){
+                    TypeLien =1;
+                }
+            }
+                
+            this.undelegateEvents();
+            this.$el.empty();
+            this.$el.html(this._template({TypeLien: TypeLien,LinkText:this.LinkText,BouttonMoreInfo:this.BouttonMoreInfo}));
+
+            this.delegateEvents();
+            return this;
+        },
+    });
+
+    Events.extend({
+    });
+    return gridOptionTypeLienView;
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridOptionView.js	(révision 10114)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridOptionView.js	(révision 10115)
@@ -1,67 +1,104 @@
 define(
-        [
-            "jquery",
-            "underscore",
-            "text!../Templates/gridOption.html",
-            "JEditor/Commons/Events",
-            "JEditor/Commons/Files/Models/FileGroup",
-            "JEditor/PagePanel/Contents/Options/Views/AbstractOptionView",
-            "JEditor/PagePanel/Contents/Blocks/GalleryBlock/Views/GalleryFileEditionView",
-            "JEditor/Commons/Utils",
-            "i18n!../nls/i18n"
-        ],
-        function($, _, gridOption, Events, FileGroup, AbstractOptionView, GalleryFileEditionView, Utils) {
-            /**
-             * Options de la grille
-             * @class GridOptionView
-             * @extends AbstractOptionView
-             */
-            var GridOptionView = AbstractOptionView.extend({
-                
-                optionType: 'gridOption',
-                events: {'click .files-action': '_openFileGroupDialog',
-                    'change .zoom-picture': '_selectZoomOnClic'},
-                className: 'grid-option-home panel-content',
-                initialize: function() {
-                    this._super();
-                    this.template = this.buildTemplate(gridOption, this.translate);
-                    this.listenTo(this.model, 'change:fileGroup', this.onFileGroupChange);
-                },
-                _selectZoomOnClic: function(event) {
-                    this.model.zoom = !this.model.zoom;
-                },
-                _openFileGroupDialog: function() {
-                    var fileGroup;
-                    var viewAttributes;
-                    var fileGroupDialog;
-                    if (this.model.fileGroup)
-                        fileGroup = this.model.fileGroup;
-                    else
-                        fileGroup = new FileGroup();
-                    viewAttributes = {title: "editMyGrid", model: fileGroup};
-                    fileGroupDialog = new GalleryFileEditionView(viewAttributes);
-                    this.listenTo(fileGroupDialog, Events.ChoiceEvents.SELECT, function(view, selected) {
-                        this.model.setFileGroup(selected);
-                    });
-                    this.listenTo(fileGroupDialog, Events.DialogEvents.CLOSE, function() {
-                        this.stopListening(fileGroupDialog);
-                        fileGroupDialog.remove();
-                    });
-                    fileGroupDialog.open();
-                    return this;
-                },
-                onFileGroupChange: function(fileGroup, options) {
-                    this.render();
-                    return this;
-                },
-                render: function() {
-                    this.undelegateEvents();
-                    var templateVars = {fileGroup:this.model.fileGroup,zoom:this.model.zoom,captionType:this.model.captionType};
-                    this.$el.html(this.template(templateVars));
-                    this.scrollables();
-                    this.delegateEvents();
-                    return this;
-                },
-            });
-            return GridOptionView;
-        });
\ No newline at end of file
+    [
+        "jquery",
+        "underscore",
+        "text!../Templates/gridOption.html",
+        "JEditor/Commons/Events",
+        "JEditor/Commons/Files/Models/FileGroup",
+        "JEditor/PagePanel/Contents/Options/Views/AbstractOptionView",
+        "JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselFileEditionView",
+        "JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridOptionTypeLienView",
+        "JEditor/Commons/Utils",
+        "i18n!../nls/i18n"
+    ],
+    function($, _, gridOption, Events, FileGroup, AbstractOptionView, CarrouselFileEditionView,GridOptionTypeLienView, Utils) {
+        /**
+         * Options de la grille
+         * @class GridOptionView
+         * @extends AbstractOptionView
+         */
+        var GridOptionView = AbstractOptionView.extend({
+            
+            optionType: 'gridOption',
+            events: {
+                'click .files-action': '_openFileGroupDialog',
+                //'change .zoom-picture': '_selectZoomOnClic',
+                'click input[type=radio]':'_onChangeRadio',
+            },
+            className: 'grid-option-home panel-content',
+            initialize: function() {
+                this._super();
+                this.template = this.buildTemplate(gridOption, this.translate);
+                this.GridOptionTypeLien =new GridOptionTypeLienView(this.model);
+                this.listenTo(this.model, 'change:fileGroup', this.onFileGroupChange);
+            },
+            _selectZoomOnClic: function(event) {
+                this.model.zoom = !this.model.zoom;
+            },
+            _openFileGroupDialog: function() {
+                var fileGroup;
+                var viewAttributes;
+                var fileGroupDialog;
+                if (this.model.fileGroup)
+                    fileGroup = this.model.fileGroup;
+                else
+                    fileGroup = new FileGroup();
+                viewAttributes = {title: this.translate("editMyGrid"), model: fileGroup};
+                fileGroupDialog = new CarrouselFileEditionView(viewAttributes);
+                this.listenTo(fileGroupDialog, Events.ChoiceEvents.SELECT, function(view, selected) {
+                    this.model.setFileGroup(selected);
+                    this.model.fileGroupId=selected.id;
+                });
+                this.listenTo(fileGroupDialog, Events.DialogEvents.CLOSE, function() {
+                    this.stopListening(fileGroupDialog);
+                    fileGroupDialog.remove();
+                });
+                fileGroupDialog.open();
+                return this;
+            },
+            onFileGroupChange: function(fileGroup, options) {
+                this.render();
+                return this;
+            },
+            _onChangeRadio: function(event){
+                var name =event.currentTarget.name;
+                if (name ==="figcaption"){
+                    this.model.grilleInfo=parseInt(event.currentTarget.value);
+                    this.GridOptionTypeLien.render();
+                }else if(name === "LinkAction"){
+                    this.model.grilleAction= parseInt(event.currentTarget.value);
+                    this.GridOptionTypeLien.render();
+                }else if(name === "LinkType"){
+                    this.model.grilleTypeLink=parseInt(event.currentTarget.value);
+                }
+                this.DeleteNameInputOnMoodel();
+            },
+            DeleteNameInputOnMoodel : function(){
+                var Mymodel=this.model.attributes;
+                if(Mymodel.hasOwnProperty("figcaption"))
+                    delete Mymodel.figcaption;
+                if(Mymodel.hasOwnProperty("LinkAction"))
+                    delete Mymodel.LinkAction;
+                if(Mymodel.hasOwnProperty("LinkType"))
+                    delete Mymodel.LinkType;
+              
+                this.model.attributes=Mymodel;
+            },
+            render: function() {
+                this.undelegateEvents();
+                var templateVars = {
+                    fileGroup:this.model.fileGroup,
+                    info:this.model.grilleInfo,
+                    action:this.model.grilleAction,
+                    typeLink:this.model.grilleTypeLink
+                };
+                this.$el.html(this.template(templateVars));
+                this.$("#typeDeLien").append(this.GridOptionTypeLien.el);
+                this.GridOptionTypeLien.render();
+                this.scrollables();
+                this.delegateEvents();
+                return this;
+            },
+        });
+        return GridOptionView;
+    });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridStyleOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridStyleOptionView.js	(révision 10114)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Views/GridStyleOptionView.js	(révision 10115)
@@ -9,7 +9,7 @@
             "JEditor/Commons/Utils",
             "i18n!../nls/i18n"
         ],
-        function($, _, gridStyleOption, Events, FileGroup, AbstractOptionView, Utils, translate) {
+        function($, _, gridStyleOption, Events, FileGroup, AbstractOptionView,GridStyleOptionTypeLienView, Utils, translate) {
             /**
              * Options de la grille
              * @class GridOptionView
@@ -17,19 +17,48 @@
              */
             var GridStyleOptionView = AbstractOptionView.extend({
                 optionType: 'gridStyleOption',
-                events: {'slide .row-height-slider': '_setRowHeight',
-                    'slide .margin-size-slider': '_setMargins',
-                    'change .justify-last-row': '_selectJustify'},
-                className: 'grid-option-home panel-content',
-                _setRowHeight: function(e, data) {
-                    this.model.rowHeight = data.value;
+                tagName: "div",
+                className: "gallery-template-option galleryStyle panel-content ",
+                events: {
+                    'click .stylleDeGrille div .effect-radio'   : '_onChangeStylleImage',
+                    'click #Radioformatimage div .effect-radio'       : '_onChangeFormatImage',
+                    'slidechange .slider'   :   '_onSliderChange',
+                    'change input[type="radio"].select-box': '_onStyleAffichageChange',
                 },
-                _setMargins: function(e, data) {
-                    this.model.margins = data.value;
+                _onChangeStylleImage : function(event){
+                    this.$(".effect-radio").removeClass("active");
+                    var $target = $(event.currentTarget);
+                    $target.addClass("active");
+                    var value = parseInt($target.attr("data-value"));
+                    if(value===1){
+                        $("#formatImage").show();
+                        var elGridFormat=$("#Radioformatimage").children().find("[data-value="+this.model.grilleFormat+"]");
+                        elGridFormat.addClass("active");
+                    }else{
+                        $("#formatImage").hide();
+                    }
+                    this.model.grilleStyle=value;
                 },
-                _selectJustify: function(event) {
-                    this.model.justify = !this.model.justify;
+                  /** */
+                  _onStyleAffichageChange :function(event){
+                    var $target = $(event.currentTarget);
+                    this.model.grilleStyleAff = $target.val();
                 },
+                /**
+                * Slider change
+                */
+                _onSliderChange: function(event,ui){
+                    var value = ui.value;
+                    this.model.grilleNbreImage=value;
+                    return false;
+                 },
+                _onChangeFormatImage : function(event){
+                    this.$(".formatImage-radio").removeClass("active");
+                    var $target = $(event.currentTarget);
+                    $target.addClass("active");
+                    var value = $target.attr("data-value");
+                    this.model.grilleFormat=value;
+                },
                 initialize: function() {
                     this._super();
                     this.template = this.buildTemplate(gridStyleOption, this.translate);
@@ -37,20 +66,20 @@
                 },
                 render: function() {
                     this.undelegateEvents();
-                    var templateVars = {rowHeight:this.model.rowHeight,justify:this.model.justify,margins:this.model.margins};
+                    var templateVars = {
+                        grilleStyle:this.model.grilleStyle,
+                        grilleFormat:this.model.grilleFormat,
+                        grilleNbreImage:this.model.grilleNbreImage,
+                        grilleStyleAff:this.model.grilleStyleAff
+                    };
                     this.$el.html(this.template(templateVars));
-                    this.$('.row-height-slider').slider({range: "min",
-                        value: this.model.rowHeight,
-                        min: 140,
-                        max: 320,
+                    this.$('.grid-nbreImage .slider').slider({
+                        range: "min",
+                        value: this.model.grilleNbreImage,
+                        min: 1,
+                        max: 5,
                         step: 1
                     });
-                    this.$('.margin-size-slider').slider({range: "min",
-                        value: this.model.margins,
-                        min: 0,
-                        max: 30,
-                        step: 1
-                    });
                     this.scrollables();
                     this.delegateEvents();
                     return this;
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-ca/i18n.js	(révision 10114)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-ca/i18n.js	(révision 10115)
@@ -1,8 +1,8 @@
 define({"BLOCK_NAME":"Grille",
-        "gridOption":"Grille photo",
-        "gridBlockOption":"Options de la grille photo",
+        "gridOption":"Grille",
+        "gridBlockOption":"Options de la grille",
         "gridStyleOption":"Style",
-        "gridContent":"Contenu de la grille photo",
+        "gridContent":"Contenu de la grille",
         "gridContentLegend":"Ajoutez ou supprimez des images sur votre grille.",
         "emptyGallery":"Votre grille est vide.",
         "no-emptyGallery":"Votre grille contient des images.",
@@ -17,8 +17,33 @@
         "photoSizeLegend":"Gérez la hauteur des lignes de votre grille photo ici.",
         "justifyLastRow":"Forcez à justifier la dernière ligne",
         "justifyLastRowLegend":"Par exemple, s'il ne reste qu'une seule image en dernière ligne de la grille, celle-ci occupera toute la largeur disponible, quitte à être démesurée par rapport aux autres images.",
-        "gridMargin":"Marge",
-        "gridMarginLegend":"Réglez ici l'espacement entre les photos de la grille.",
         "imageWarning": "Attention :",
-        "imageWarningMsg": "les images trouvées sur internet ne sont généralement pas libres d’utilisation. Pour vous aider à déterminer si une image est protégée par des droits d’auteur vous pouvez utiliser le service"
+        "imageWarningMsg": "les images trouvées sur internet ne sont généralement pas libres d’utilisation. Pour vous aider à déterminer si une image est protégée par des droits d’auteur vous pouvez utiliser le service",
+        "figcaptionImage"       :   "Afficher les informations des images",
+        "figShowTitle"          :   "Afficher le titre",
+        "figShowAll"            :   "Afficher le titre et la description",
+        "doNothing"             :   "Ne rien afficher",
+        "selectClickActionImage":   "Sélectionnez l'action souhaitée au clic sur l’image.",
+        "ActionZoom"            :   "Zoomer sur l'image",
+        "ActionPartner"         :   "Ouvrir le lien associé à l'image",
+        "gridStyleContent" :   "Style de grille",
+        "gridStyleLegend" :   "Appliquez un style de grille",
+        "masonryLegend"         :   "Tuiles",
+        "gridLegend"            :   "Grille",
+        "gridNombreImage"       :   "Nombre d'image",
+        "gridNombreImageLegend" :   "Glissez pour ajuster le nombre d'images affichées",
+        "gridStyleAffichage"    :   "Style des images",
+        "gridStyleAffichageDesc":   "Appliquez un style aux images",
+        "styleDeGrille"         :   "Style de grille",
+        "styleDeGrilleLegend"   :   "Appliquez un style de grille",
+        "FormatImage"           :   "Format de l'image",
+        "FormatImageLegend"     :   "Appliquez un format d'image au carousel",
+        "selectTypeLink"        :   "Selectionnez le type de lien souhaitée",
+        "LinkImage"             :   "Ajouter le lien sur l'image",
+        "LinkText"              :   "Ajouter le lien sur le texte",
+        "ButtonReadMore"        :   "Ajouter un bouton 'en savoir plus'",
+        'landscape'             :   "Paysage",
+        'portrait'              :   "Portrait",
+        'square'                :   "Carré",
+        "editMyGrid"            :   "Modifier ma grille ",
     });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-fr/i18n.js	(révision 10114)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-fr/i18n.js	(révision 10115)
@@ -1,8 +1,8 @@
 define({"BLOCK_NAME":"Grille",
-        "gridOption":"Grille photo",
-        "gridBlockOption":"Options de la grille photo",
+        "gridOption":"Grille",
+        "gridBlockOption":"Options de la grille",
         "gridStyleOption":"Style",
-        "gridContent":"Contenu de la grille photo",
+        "gridContent":"Contenu de la grille",
         "gridContentLegend":"Ajoutez ou supprimez des images sur votre grille.",
         "emptyGallery":"Votre grille est vide.",
         "no-emptyGallery":"Votre grille contient des images.",
@@ -17,8 +17,33 @@
         "photoSizeLegend":"Gérez la hauteur des lignes de votre grille photo ici.",
         "justifyLastRow":"Forcez à justifier la dernière ligne",
         "justifyLastRowLegend":"Par exemple, s'il ne reste qu'une seule image en dernière ligne de la grille, celle-ci occupera toute la largeur disponible, quitte à être démesurée par rapport aux autres images.",
-        "gridMargin":"Marge",
-        "gridMarginLegend":"Réglez ici l'espacement entre les photos de la grille.",
         "imageWarning": "Attention :",
-        "imageWarningMsg": "les images trouvées sur internet ne sont généralement pas libres d’utilisation. Pour vous aider à déterminer si une image est protégée par des droits d’auteur vous pouvez utiliser le service"
+        "imageWarningMsg": "les images trouvées sur internet ne sont généralement pas libres d’utilisation. Pour vous aider à déterminer si une image est protégée par des droits d’auteur vous pouvez utiliser le service",
+        "figcaptionImage"       :   "Afficher les informations des images",
+        "figShowTitle"          :   "Afficher le titre",
+        "figShowAll"            :   "Afficher le titre et la description",
+        "doNothing"             :   "Ne rien afficher",
+        "selectClickActionImage":   "Sélectionnez l'action souhaitée au clic sur l’image.",
+        "ActionZoom"            :   "Zoomer sur l'image",
+        "ActionPartner"         :   "Ouvrir le lien associé à l'image",
+        "gridStyleContent" :   "Style de grille",
+        "gridStyleLegend" :   "Appliquez un style de grille",
+        "masonryLegend"         :   "Tuiles",
+        "gridLegend"            :   "Grille",
+        "gridNombreImage"       :   "Nombre d'image",
+        "gridNombreImageLegend" :   "Glissez pour ajuster le nombre d'images affichées",
+        "gridStyleAffichage"    :   "Style des images",
+        "gridStyleAffichageDesc":   "Appliquez un style aux images",
+        "styleDeGrille"         :   "Style de grille",
+        "styleDeGrilleLegend"   :   "Appliquez un style de grille",
+        "FormatImage"           :   "Format de l'image",
+        "FormatImageLegend"     :   "Appliquez un format d'image au carousel",
+        "selectTypeLink"        :   "Selectionnez le type de lien souhaitée",
+        "LinkImage"             :   "Ajouter le lien sur l'image",
+        "LinkText"              :   "Ajouter le lien sur le texte",
+        "ButtonReadMore"        :   "Ajouter un bouton 'en savoir plus'",
+        'landscape'             :   "Paysage",
+        'portrait'              :   "Portrait",
+        'square'                :   "Carré",
+        "editMyGrid"            :   "Modifier ma grille ",
     });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/i18n.js	(révision 10114)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/i18n.js	(révision 10115)
@@ -1,4 +1,5 @@
-define({ "root": {
+define({ 
+"root": {
         "BLOCK_NAME":"Grid",
         "gridOption":"Grid",
         "gridBlockOption":"Grid options",
@@ -21,5 +22,37 @@
         "gridMargin":"Margin",
         "gridMarginLegend":"Set spacing between pictures in the grid.",
         "imageWarning": "Warning:",
-        "imageWarningMsg": "pictures found on the internet are usually not free to use. To help you identify if a picture is protected by copyright you can use"
+        "imageWarningMsg": "pictures found on the internet are usually not free to use. To help you identify if a picture is protected by copyright you can use",
+        "Style"                   :     "Style" ,
+        "figcaptionImage"       :   "Display image information",
+        "figShowTitle"          :   "Display title",
+        "figShowAll"            :   "Display the title and description",
+        "doNothing"             :   "Do not display anything",
+        "selectClickActionImage":   "Select the desired action by clicking on the image.",
+        "ActionZoom"            :   "Zoom in on the image",
+        "ActionPartner"         :   "Open the link associated with the image",
+        "gridStyleContent"      :   "Grid style",
+        "gridStyleLegend"       :   "Apply a grid style",
+        "masonryLegend"         :   "Masonry",
+        "gridLegend"            :   "Grid",
+        "gridNombreImage"       :   "Number of images",
+        "gridNombreImageLegend" :   "Drag to adjust the number of images displayed",
+        "gridStyleAffichage"    :   "Style of the images",
+        "gridStyleAffichageDesc":   "Apply a style to the images",
+        "styleDeGrille"         :   "Grid style",
+        "styleDeGrilleLegend"   :   "Apply a grid style",
+        "FormatImage"           :   "Image format",
+        "FormatImageLegend"     :   "Apply an image format to the grid",
+        "selectTypeLink"        :   "Select the type of link you want",
+        "LinkImage"             :   "Add link to image",
+        "LinkText"              :   "Add link to text",
+        "ButtonReadMore"        :   "Add a button 'read more'",
+        "gridOption"            :       "Grid",
+        "gridBlockOption"       :       "Grid options",
+        "gridStyleOption"       :       "Style",
+        "gridContent"           :       "Content of the grid",
+        'landscape'             :   "Landscape",
+        'portrait'              :   "Portrait",
+        'square'                :   "Square",
+        "editMyGrid"            :   "Edit my Grid",
 }, "fr-fr":true, "fr-ca":true })
\ No newline at end of file
