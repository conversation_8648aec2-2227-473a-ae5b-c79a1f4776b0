Revision: r11739
Date: 2023-12-18 09:55:27 +0300 (lts 18 Des 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
changer comportement blocks utilisant des collections d'images

## Files changed

## Full metadata
------------------------------------------------------------------------
r11739 | sraz<PERSON><PERSON><PERSON>oa | 2023-12-18 09:55:27 +0300 (lts 18 Des 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Templates/imageCollection.html
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Views/CollectionSelectorDialog.js

changer comportement blocks utilisant des collections d'images
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Files/Templates/imageCollection.html
===================================================================
--- src/js/JEditor/Commons/Files/Templates/imageCollection.html	(révision 11738)
+++ src/js/JEditor/Commons/Files/Templates/imageCollection.html	(révision 11739)
@@ -3,14 +3,15 @@
         <span class="count"><%=group.length%></span>
         <span class="text"><%=__("imageInGroup")%></span>
     </span>
+    <a class="existing action <%= !group.isNew()?'selected':''%>" data-action="selectgroup" href="#">
+        <span class="icon-gallery"  ></span>
+        <span class="text"><%=__("useExistingGroup")%></span>
+    </a>
     <a class="new-group action <%= group.isNew()?'selected':''%>" data-action="newgroup" href="#">
         <span class="icon-file-group"></span>
         <span class="text"><%=__("newImageGroup") %></span>
     </a>
-    <a class="existing action <%= !group.isNew()?'selected':''%>" data-action="selectgroup" href="#">
-        <span class="icon-gallery"  ></span>
-        <span class="text"><%=__("useExistingGroup")%></span>
-    </a>
+   
 </div>
 <div class="panel-content">
     
Index: src/js/JEditor/Commons/Files/Views/CollectionSelectorDialog.js
===================================================================
--- src/js/JEditor/Commons/Files/Views/CollectionSelectorDialog.js	(révision 11738)
+++ src/js/JEditor/Commons/Files/Views/CollectionSelectorDialog.js	(révision 11739)
@@ -129,8 +129,8 @@
             });
             this.delegateEvents();
             if (this.model.length === 0 && !this.model.name) {
-                this.dom[this.cid].newButton.removeClass('selected');
-                this.dom[this.cid].newButton.trigger('click');
+                this.dom[this.cid].browseButton.removeClass('selected');
+                this.dom[this.cid].browseButton.trigger('click');
             } else
                 this.childViews.editGroupView.$el.show();
             return this;
