Revision: r11459
Date: 2023-10-23 12:31:16 +0300 (lts 23 Okt 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: option lien GMB dans le block plan (partie js)

## Files changed

## Full metadata
------------------------------------------------------------------------
r11459 | srazanandralisoa | 2023-10-23 12:31:16 +0300 (lts 23 Okt 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Addresses/Models/Address.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Addresses/Templates/addressInput.html
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Addresses/Views/AddressEditView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Addresses/Views/nls/de-de/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Addresses/Views/nls/en-au/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Addresses/Views/nls/en-us/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Addresses/Views/nls/es-es/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Addresses/Views/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Addresses/Views/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Addresses/nls/en-au/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Addresses/nls/en-us/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Addresses/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Addresses/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Addresses/nls/i18n.js

wishlist IDEO3.2: option lien GMB dans le block plan (partie js)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Addresses/Models/Address.js
===================================================================
--- src/js/JEditor/Commons/Addresses/Models/Address.js	(révision 11458)
+++ src/js/JEditor/Commons/Addresses/Models/Address.js	(révision 11459)
@@ -26,7 +26,7 @@
              */
                     {
                         urlRoot: __IDEO_API_PATH__ + '/planAcces/api/addresses',
-                        defaults: {name: "", address: "", location: {lat: 48.87123070000001, lng: 2.3093473999999787}, phone: '', email: '', website: ''},
+                        defaults: {name: "", address: "", useGmb:true, idGmb:0, location: {lat: 48.87123070000001, lng: 2.3093473999999787}, phone: '', email: '', website: ''},
                         initialize: function() {
                             if (this.name === "") {
                                 this.name = translate("newAddress");
@@ -61,7 +61,7 @@
 
                         }
                     });
-            Address.SetAttributes(['location', 'address', 'phone', 'website', 'email', 'name']);
+            Address.SetAttributes(['location', 'address', 'useGmb', 'idGmb', 'phone', 'website', 'email', 'name']);
             Events.extend({
                 AddressEvents: {
                     FOUND: 'found',
Index: src/js/JEditor/Commons/Addresses/Templates/addressInput.html
===================================================================
--- src/js/JEditor/Commons/Addresses/Templates/addressInput.html	(révision 11458)
+++ src/js/JEditor/Commons/Addresses/Templates/addressInput.html	(révision 11459)
@@ -31,4 +31,22 @@
             </div>
         </div>
     </div>
+    <div class="gmb-option">
+        <% var _id=_.uniqueId('gmb') ;%>
+        <div>
+            <div class="text"><%= __("linkPlan")%></div>
+        <input id="<%= _id %>" class="field-input for--block-label" type="radio" name="useGmb-<%= _id %>"  value="true" <%=address&&address.useGmb?"checked=checked":""%>  />
+            <label  class="block-label" for="<%= _id %>" >
+                <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+                </div>
+                <div class="block-label-radio"><%= __("planLink")%></div>
+            </label>
+        </div>
+        <div>
+            <label><%= __("labelIdGmb")%></label>
+            <input type="text" name="idGmb" class="idGmb" value="<%=address&&address.idGmb?address.idGmb:''%>"/>
+        </div>
+    </div>
 </div>
\ No newline at end of file
Index: src/js/JEditor/Commons/Addresses/Views/AddressEditView.js
===================================================================
--- src/js/JEditor/Commons/Addresses/Views/AddressEditView.js	(révision 11458)
+++ src/js/JEditor/Commons/Addresses/Views/AddressEditView.js	(révision 11459)
@@ -21,7 +21,7 @@
              */
                     {
                         attributes: {'class': 'editor'},
-                        events: {'click .country-list>li>a': 'setCountry', 'autocompleteselect input[name="address"]': 'onSelect', 'change input': '_updateModel'},
+                        events: {'click .country-list>li>a': 'setCountry', 'autocompleteselect input[name="address"]': 'onSelect', 'click input[type="radio"]':'_onChangeRadio', 'change input': '_updateModel'},
                         /**
                          * initialise l'objet
                          */
@@ -47,6 +47,9 @@
                                 location[field] = value;
                                 this.model.location = location;
                                 this.updateMap();
+                            } 
+                            else if(field == 'idGmb') {
+                                this.model.idGmb = this.dom[this.cid].idGmb.val() ;
                             } else {
                                 this.model[field] = value;
                             }
@@ -53,6 +56,19 @@
                             
                             this.model.save();
                         },
+                        _onChangeRadio: function(){
+                            if (this.model.useGmb) {
+                                this.dom[this.cid].useGmb.removeAttr("checked");
+                                this.model.useGmb = false;
+                                this.dom[this.cid].idGmb.attr("disabled", "disabled")     
+                            }
+                            else{
+                                this.model.useGmb = true;
+                                this.dom[this.cid].useGmb.attr("checked","checked")
+                                this.dom[this.cid].idGmb.removeAttr("disabled");
+                            }
+                            this.model.save();
+                        },
                         /**
                          * met à jour l'affichage de la vue
                          */
@@ -63,6 +79,9 @@
                             this.dom[this.cid].mapContainer = this.$('.adjust').children('.map-container');
                             this.dom[this.cid].latInput = this.$el.find('.adjust').find('input[name="lat"]');
                             this.dom[this.cid].lngInput = this.$el.find('.adjust').find('input[name="lng"]');
+                            this.dom[this.cid].idGmb = this.$el.find('.gmb-option').find('input[name="idGmb"]');
+                            this.dom[this.cid].useGmb = this.$el.find('.gmb-option').find('input[type="radio"]');
+
                             var that = this;
                             this.dom[this.cid].addressInput.autocomplete({
                                 source: function(request, response) {
Index: src/js/JEditor/Commons/Addresses/Views/nls/de-de/i18n.js
===================================================================
--- src/js/JEditor/Commons/Addresses/Views/nls/de-de/i18n.js	(révision 11458)
+++ src/js/JEditor/Commons/Addresses/Views/nls/de-de/i18n.js	(révision 11459)
@@ -6,5 +6,7 @@
 	"website": "Site Web",
 	"adjustPointer": "Ajuster la position du pointeur",
 	"latt": "lattitude",
-	"lng": "longitude"
+	"lng": "longitude",
+	"planLink": "Lien du plan",
+	"labelIdGmb": "Préciser ID à utiliser si Plusieurs GMB"
 });
\ No newline at end of file
Index: src/js/JEditor/Commons/Addresses/Views/nls/en-au/i18n.js
===================================================================
--- src/js/JEditor/Commons/Addresses/Views/nls/en-au/i18n.js	(révision 11458)
+++ src/js/JEditor/Commons/Addresses/Views/nls/en-au/i18n.js	(révision 11459)
@@ -6,5 +6,7 @@
 	"website": "Site Web",
 	"adjustPointer": "Adjust the position of the pointer",
 	"latt": "Latitude",
-	"lng": "Longitude"
+	"lng": "Longitude",
+	"planLink": "Plan Link",
+	"labelIdGmb": "Specify ID to be used if more than one GMB"
 });
\ No newline at end of file
Index: src/js/JEditor/Commons/Addresses/Views/nls/en-us/i18n.js
===================================================================
--- src/js/JEditor/Commons/Addresses/Views/nls/en-us/i18n.js	(révision 11458)
+++ src/js/JEditor/Commons/Addresses/Views/nls/en-us/i18n.js	(révision 11459)
@@ -6,5 +6,7 @@
 	"website": "Site Web",
 	"adjustPointer": "Ajuster la position du pointeur",
 	"latt": "lattitude",
-	"lng": "longitude"
+	"lng": "longitude",
+	"planLink": "Plan Link",
+	"labelIdGmb": "Specify ID to be used if more than one GMB"
 });
\ No newline at end of file
Index: src/js/JEditor/Commons/Addresses/Views/nls/es-es/i18n.js
===================================================================
--- src/js/JEditor/Commons/Addresses/Views/nls/es-es/i18n.js	(révision 11458)
+++ src/js/JEditor/Commons/Addresses/Views/nls/es-es/i18n.js	(révision 11459)
@@ -6,5 +6,7 @@
 	"website": "Site Web",
 	"adjustPointer": "Ajuster la position du pointeur",
 	"latt": "lattitude",
-	"lng": "longitude"
+	"lng": "longitude",
+	"planLink": "Lien du plan",
+	"labelIdGmb": "Préciser ID à utiliser si Plusieurs GMB"
 });
\ No newline at end of file
Index: src/js/JEditor/Commons/Addresses/Views/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/Commons/Addresses/Views/nls/fr-ca/i18n.js	(révision 11458)
+++ src/js/JEditor/Commons/Addresses/Views/nls/fr-ca/i18n.js	(révision 11459)
@@ -6,5 +6,7 @@
 	"website": "Site Web",
 	"adjustPointer": "Ajuster la position du pointeur",
 	"latt": "lattitude",
-	"lng": "longitude"
+	"lng": "longitude",
+	"planLink": "Lien du plan",
+	"labelIdGmb": "Préciser ID à utiliser si Plusieurs GMB"
 });
\ No newline at end of file
Index: src/js/JEditor/Commons/Addresses/Views/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/Commons/Addresses/Views/nls/fr-fr/i18n.js	(révision 11458)
+++ src/js/JEditor/Commons/Addresses/Views/nls/fr-fr/i18n.js	(révision 11459)
@@ -6,5 +6,7 @@
 	"website": "Site Web",
 	"adjustPointer": "Ajuster la position du pointeur",
 	"latt": "lattitude",
-	"lng": "longitude"
+	"lng": "longitude",
+	"planLink": "Lien du plan",
+	"labelIdGmb": "Préciser ID à utiliser si Plusieurs GMB"
 });
\ No newline at end of file
Index: src/js/JEditor/Commons/Addresses/nls/en-au/i18n.js
===================================================================
--- src/js/JEditor/Commons/Addresses/nls/en-au/i18n.js	(révision 11458)
+++ src/js/JEditor/Commons/Addresses/nls/en-au/i18n.js	(révision 11459)
@@ -1,5 +1,8 @@
 define({
 	"newAddress": "New address",
 	"error": "Error",
-	"mapError": "Unable to create the plan, check your Internet connection"
+	"mapError": "Unable to create the plan, check your Internet connection",
+	"linkPlan":"Map link",
+	 "planLink":"Use GMB link if possible",
+	 "labelIdGmb":"specify ID to be used if more than one GMB"
 });
\ No newline at end of file
Index: src/js/JEditor/Commons/Addresses/nls/en-us/i18n.js
===================================================================
--- src/js/JEditor/Commons/Addresses/nls/en-us/i18n.js	(révision 11458)
+++ src/js/JEditor/Commons/Addresses/nls/en-us/i18n.js	(révision 11459)
@@ -1,5 +1,8 @@
 define({
 	"newAddress": "Nouvelle addresse",
 	"error": "Erreur",
-	"mapError": "Impossible de créer le plan, vérifiez votre connexion Internet"
+	"mapError": "Impossible de créer le plan, vérifiez votre connexion Internet",
+	"linkPlan":"Map link",
+	"planLink":"Use GMB link if possible",
+	"labelIdGmb":"specify ID to be used if more than one GMB"
 });
\ No newline at end of file
Index: src/js/JEditor/Commons/Addresses/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/Commons/Addresses/nls/fr-ca/i18n.js	(révision 11458)
+++ src/js/JEditor/Commons/Addresses/nls/fr-ca/i18n.js	(révision 11459)
@@ -1 +1 @@
-define({"newAddress":"Nouvelle addresse","name":"Nom","address":"Adresse","phone":"Téléphone","email":"email","website":"Site Web","adjustPointer":"Ajuster la position du pointeur","latt":"lattitude","lng":"longitude"});
\ No newline at end of file
+define({"newAddress":"Nouvelle addresse","name":"Nom","address":"Adresse","phone":"Téléphone","email":"email","website":"Site Web","adjustPointer":"Ajuster la position du pointeur","latt":"lattitude","lng":"longitude","linkPlan":"Lien du plan", "planLink":"Privilégier lien GMB si disponible","labelIdGmb":"Préciser ID à utiliser si plusieurs GMB"});
\ No newline at end of file
Index: src/js/JEditor/Commons/Addresses/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/Commons/Addresses/nls/fr-fr/i18n.js	(révision 11458)
+++ src/js/JEditor/Commons/Addresses/nls/fr-fr/i18n.js	(révision 11459)
@@ -1 +1 @@
-define({"newAddress":"Nouvelle addresse","name":"Nom","address":"Adresse","phone":"Téléphone","email":"email","website":"Site Web","adjustPointer":"Ajuster la position du pointeur","latt":"lattitude","lng":"longitude"});
\ No newline at end of file
+define({"newAddress":"Nouvelle addresse","name":"Nom","address":"Adresse","phone":"Téléphone","email":"email","website":"Site Web","adjustPointer":"Ajuster la position du pointeur","latt":"lattitude","lng":"longitude","linkPlan":"Lien du plan", "planLink":"Privilégier lien GMB si disponible","labelIdGmb":"Préciser ID à utiliser si plusieurs GMB"});
\ No newline at end of file
Index: src/js/JEditor/Commons/Addresses/nls/i18n.js
===================================================================
--- src/js/JEditor/Commons/Addresses/nls/i18n.js	(révision 11458)
+++ src/js/JEditor/Commons/Addresses/nls/i18n.js	(révision 11459)
@@ -1 +1 @@
-define({ "root": {"newAddress":"New address","name":"Name","address":"Address","phone":"Phone number","email":"E-mail","website":"WebSite","adjustPointer":"Adjust pointer position","latt":"Latitude","lng":"Longitude"}, "fr-fr":true, "fr-ca":true })
\ No newline at end of file
+define({ "root": {"newAddress":"New address","name":"Name","address":"Address","phone":"Phone number","email":"E-mail","website":"WebSite","adjustPointer":"Adjust pointer position","latt":"Latitude","lng":"Longitude" ,"linkPlan":"Map link", "planLink":"Use GMB link if possible","labelIdGmb":"specify ID to be used if more than one GMB"}, "fr-fr":true, "fr-ca":true })
\ No newline at end of file
