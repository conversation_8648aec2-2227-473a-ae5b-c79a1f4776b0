Revision: r13610
Date: 2024-12-16 09:39:59 +0300 (lts 16 Des 2024) 
Author: rrakotoarinelina 

## Commit message
Whishlist IDEO3.2 : Revoir edition d'un block de texte / block HTML - correction ajout message confirmation pour block html

## Files changed

## Full metadata
------------------------------------------------------------------------
r13610 | rrakotoarinelina | 2024-12-16 09:39:59 +0300 (lts 16 Des 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Views/HtmlEditionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js

Whishlist IDEO3.2 : Revoir edition d'un block de texte / block HTML - correction ajout message confirmation pour block html
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Views/HtmlEditionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Views/HtmlEditionView.js	(révision 13609)
+++ src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Views/HtmlEditionView.js	(révision 13610)
@@ -5,10 +5,11 @@
     "JEditor/Commons/Events",
     "JEditor/Commons/Ancestors/Views/DialogView",
     "i18n!../nls/i18n",
+    "JEditor/App/Messages/ConfirmUnsaved",
     //not in params
     "jqueryPlugins/dropdown",
-    "ace/ace"
-], function($, _, htmlEdition, Events, DialogView, translate,ace) {
+    "ace/ace",
+], function($, _, htmlEdition, Events, DialogView, translate,ConfirmUnsaved,ace ) {
     ace = window.ace;
     var HtmlEditionView = DialogView.extend({
         className: 'htmlEdition',
@@ -15,6 +16,7 @@
         events: {
             'click .ace-close' : "validate",
             'change .code-editor input[type="radio"]' : 'setJsRun',
+            'dialogbeforeclose': 'beforeClose',
         },
         
         constructor: function(options) {
@@ -21,6 +23,7 @@
             this.htmlBlockView = options.htmlBlockView;
             this.originalContent = this.htmlBlockView.model.options.html.toJSON();
             this.editor = null;
+            this.isClosing = false;  // Initialize the flag here
             var opts = _.extend({
                 title: translate("htmlEdition"),
                 buttons: [
@@ -30,9 +33,7 @@
                         click: _.bind(this.onCancel, this)
                     }
                 ],
-                width: 1000,
-                height: 600,
-                close: _.bind(this.onClose, this)
+                
             }, options);
 
             return DialogView.call(this, opts);
@@ -54,13 +55,15 @@
         },
 
         onCancel: function() {
-
-            this.$el.dialog('close');
+            //le close to modal trigger le dialogbeforeclose event 
+            //cette ligne est utile pour ne pas afficher le message de confirmation onsave
+            this.isClosing = true;
             this.onClose();
         },
 
         onClose: function() {
             
+            this.$el.dialog('close');
             // Remove the dialog
             this.remove();
         },
@@ -118,12 +121,90 @@
 				}
 				editor.destroy();
 			}
-            this.$el.dialog('close');
+
+            //le close to modal trigger le dialogbeforeclose event 
+            //cette ligne est utile pour ne pas afficher le message de confirmation onsave
+            this.isClosing = true;
+            this.onClose();
 			this.htmlBlockView._display();
 		},
         setJsRun : function(event, ui) {
 			this.jsRun = $(event.currentTarget).val();
 		},
+        confirmUnsaved: function(params) {
+            this.app.messageDelegate.set(new ConfirmUnsaved(params));
+        },
+        hasUnsavedChanges: function() {
+            var result = false;
+            var modelValues = this.getModelValues();
+            var editorValues = this.getEditorValues();
+            
+            result = !_.isEqual(modelValues.html, editorValues.html) || 
+                     !_.isEqual(modelValues.css, editorValues.css) || 
+                     !_.isEqual(modelValues.js, editorValues.javascript) ||
+                     !_.isEqual(modelValues.jsRun, editorValues.jsRun);
+        
+            return result;
+        },
+        
+        handleConfirmationOperation: function(event) {
+
+            if (this.htmlBlockView && this.hasUnsavedChanges()) {
+                this.confirmUnsaved({
+                    message: translate("quitWithoutSaving"),
+                    title: translate("unsavedChanges"),
+                    type: 'delete-not-saved',
+                    onYes: _.bind(function() {
+                        this.validate(event);
+                    }, this),
+                    onNo: _.bind(function() {
+                        this.onClose();
+                    }, this),
+
+                    options: {
+                        dialogClass: 'delete no-close',
+                        dontAskAgain: true,
+                        onCancel: _.bind(function() {
+                            this.isClosing = false;
+                        }, this),
+                    }
+                });
+            } else {
+               this.$el.dialog('close');
+               this.remove();
+            }
+        },
+        
+        beforeClose: function(event, ui) {
+
+            event.preventDefault();
+
+            // Ajout et utilisation de flag pour eviter plusieurs vérification 
+            if (!this.isClosing) {
+                this.isClosing = true;
+                this.handleConfirmationOperation(event);
+            }
+            return false;
+        },
+        getModelValues: function() {
+            return {
+                html: this.htmlBlockView.model.options.html.get('html'),
+                css: this.htmlBlockView.model.options.html.get('css'),
+                js: this.htmlBlockView.model.options.html.js.content,
+                jsRun: this.htmlBlockView.model.options.html.js.run
+            };
+        },
+        getEditorValues: function() {
+            var values = {
+                jsRun: this.jsRun
+            };
+            this.editors.forEach(editor => {
+                var mode = editor.getSession().getMode().$id.split('/')[2];
+                values[mode] = editor.getValue();
+            });
+            return values;
+        }
+        
     });
 
     return HtmlEditionView;
Index: src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/nls/fr-ca/i18n.js	(révision 13609)
+++ src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/nls/fr-ca/i18n.js	(révision 13610)
@@ -2,4 +2,6 @@
     "htmlEdition":"Modification html",
     "save":"Enregistrer",
     "cancel":"Annuler",
+    "quitWithoutSaving" : "Vous n'avez pas enregistré les modifications apportées, voulez-vous les enregistrer ?",
+    "unsavedChanges" : "Sauvegarder les changements"
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/nls/fr-fr/i18n.js	(révision 13609)
+++ src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/nls/fr-fr/i18n.js	(révision 13610)
@@ -2,4 +2,6 @@
     "htmlEdition":"Modification html",
     "save":"Enregistrer",
     "cancel":"Annuler",
+    "quitWithoutSaving" : "Vous n'avez pas enregistré les modifications apportées à l'html, voulez-vous les enregistrer ?",
+    "unsavedChanges" : "sauvegarder les changements"
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/nls/i18n.js	(révision 13609)
+++ src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/nls/i18n.js	(révision 13610)
@@ -2,4 +2,6 @@
     "htmlEdition":"Html edit",
     "save":"Save",
     "cancel":"Cancel",
+    "quitWithoutSaving": "You haven't saved the changes you've made. Would you like to save them ?",
+    "unsavedChanges": "Save changes"
 },"fr-fr":true, "fr-ca":true});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js	(révision 13609)
+++ src/js/JEditor/PagePanel/Contents/Blocks/TextBlock/Views/TextEditionView.js	(révision 13610)
@@ -16,7 +16,6 @@
         className: 'textEdition',
         events: {
             'dialogbeforeclose': 'beforeClose',
-            // 'dialogclose': 'beforeClose',
         },
         // events:  {'click .action.delete': "confirmDelete", 'keydown .content': '_onKeyPress', 'dblclick .content': 'avoidPropagation'},
         constructor: function(options) {
@@ -75,7 +74,6 @@
 
             // Ajout et utilisation de flag pour eviter plusieurs vérification 
             if (!this.isClosing) {
-                console.log('if');
                 this.isClosing = true;
                 this.handleConfirmationOperation(false, _.bind(function() {
                     this.destroyEditorCloseModal();
