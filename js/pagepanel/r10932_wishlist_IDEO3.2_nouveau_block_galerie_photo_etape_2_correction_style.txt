Revision: r10932
Date: 2023-05-17 14:13:26 +0300 (lrb 17 Mey 2023) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: nouveau block galerie photo etape 2 (correction style)

## Files changed

## Full metadata
------------------------------------------------------------------------
r10932 | srazanandralisoa | 2023-05-17 14:13:26 +0300 (lrb 17 Mey 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html

wishlist IDEO3.2: nouveau block galerie photo etape 2 (correction style)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html	(révision 10931)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html	(révision 10932)
@@ -9,12 +9,27 @@
                 <% var _id= _.uniqueId('galerieStyle'); %>
                 <div><span class="effect-radio <%=(galerieStyle===0)?'active':''%>" id="<%=_id %>" data-value="0" data-helper="masonry"><span class="helper"><span class="help"><%=__("masonryLegend")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-photocss-column"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
                 <% var _id= _.uniqueId('galerieStyle'); %>
-                <div><span class="effect-radio <%=(galerieStyle===1)?'active':''%>" id="<%=_id %>" data-value="1" data-helper="galerie"><span class="helper"><span class="help"><%=__("galerieLegend")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-photogalerie-block"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
+                <div><span class="effect-radio <%=(galerieStyle===1)?'active':''%>" id="<%=_id %>" data-value="1" data-helper="grid"><span class="helper"><span class="help"><%=__("galerieLegend")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-photogalerie-block"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
             </div>
         </div>
     </article>
 </div>
-
+<article class="panel-option" id="formatImage" <%=(galerieStyle===0)?'style="display:none"':''%>>
+    <header>
+        <h3 class="option-name"></span> <%=__("FormatImage")%></h3>
+        <p class="panel-content-legend"><%= __("FormatImageLegend")%></p>
+    </header>
+    <div>
+        <div class="category-content radio-transformed" id="Radioformatimage">
+            <% var _id= _.uniqueId('formatImage'); %>
+            <div><span class="effect-radio <%=(galerieFormat=='landscape')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="landscape" data-helper="landscape"><span class="helper"><span class="help"><%=__("landscape")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-transition-alpha"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
+            <% var _id= _.uniqueId('formatImage'); %>
+            <div><span class="effect-radio <%=(galerieFormat=='portrait')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="portrait" data-helper="portrait"><span class="helper"><span class="help"><%=__("portrait")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-image-square"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
+            <% var _id= _.uniqueId('formatImage'); %>
+            <div><span class="effect-radio <%=(galerieFormat=='square')?'active':''%> formatImage-radio" id="<%=_id %>" data-value="square" data-helper="square"><span class="helper"><span class="help"><%=__("square")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-image-portrait"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
+        </div>
+    </div>
+</article>
     
 <div class="mr15 galerie-option-margin galerie-nbreImage">
     <article class="panel-option">
