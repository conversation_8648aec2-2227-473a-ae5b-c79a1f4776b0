Revision: r13934
Date: 2025-02-27 12:00:08 +0300 (lkm 27 Feb 2025) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2 : evolution du bloc galerie (partie JS)

## Files changed

## Full metadata
------------------------------------------------------------------------
r13934 | srazanandralisoa | 2025-02-27 12:00:08 +0300 (lkm 27 Feb 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/Galerie.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieOption.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieBlock.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieField.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/CollectionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieBlockView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieFieldView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/page_panel/module/block-options/btn-gallery-content.less

wishlist IDEO3.2 : evolution du bloc galerie (partie JS)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/Galerie.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/Galerie.js	(révision 13933)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/Galerie.js	(révision 13934)
@@ -11,6 +11,7 @@
         defaults: function() 
         {
             var ret = {
+                page : "",
                 collections: []
             };
             return ret;  
@@ -67,8 +68,19 @@
             collection = this.collections.splice(index, 1)[0];
             this.trigger("remove:fieldgalerie", this);
             return this;
+        },
+        toJSON: function() {
+            var ret = Model.prototype.toJSON.call(this);
+            ret.collections = this.collections.map(function(field) {
+                return field.toJSON();
+            });
+            return ret;
+        },
+        JSONClone:function(){
+            return this.toJSON();
         }
-    }).setAttributes(["collections"]);
+
+    }).setAttributes(["page","collections"]);
    Galerie;
     return Galerie;
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieOption.js	(révision 13933)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieOption.js	(révision 13934)
@@ -28,7 +28,8 @@
                         defaults: {
                             priority: 70, 
                             optionType: 'GalerieOption', 
-                            galerie : null,
+                            galerie : [],
+                            isPage :false,
                             galerieInfo:0, 
                             galerieAction: 0,
                             galerieTypeLink :1,
@@ -36,12 +37,50 @@
                         },
                         initialize: function() {
                             this._super();
-                            if(!(this.galerie) || !(this.galerie instanceof Galerie)){
-                                this.galerie = new Galerie(Galerie.prototype.parse( this.galerie ||{}));
+                            if(!(this.galerie)|| this.galerie.length === 0) this.addPage();
+                            else if(!_.isArray(this.galerie)){
+                                var gallery = this.galerie;
+                                this.galerie = [];
+                                this.galerie.push(gallery);
                             }
+                            var that = this;
+                            this.galerie = this.galerie.map(function(galerie) {
+                                var newGallery = new Galerie(galerie);
+                                newGallery.galerieOption = that;
+                                return newGallery;
+                            });
+                            this.updateIsPage();
                         },
+                        updateIsPage: function(){
+                            this.isPage = (this.galerie.length > 1);
+                        },
+                        addPage: function() 
+                        {
+                            var galerie = new Galerie();
+                            galerie.galerieOption = this;
+                            this.galerie.push(galerie);
+                            this.updateIsPage();
+                            this.trigger("add:galerie", this);
+                        },
+                        /**
+                         * supression d'un model Galerie dans les liste de collections
+                         * @param {*} galerie 
+                         * @returns 
+                         */
+                        removePage: function(galerie)
+                        {
+                            var index;
+                            if (galerie instanceof Galerie)
+                                index = this.galerie.lastIndexOf(galerie);
+                            else
+                                index = galerie;
+                            galerie = this.galerie.splice(index, 1)[0];
+                            this.updateIsPage();
+                            this.trigger("remove:galerie", this);
+                            return this;
+                        },
                     }
             );
-            GalerieOption.SetAttributes([ 'galerie', 'galerieInfo', 'galerieAction','galerieTypeLink','ButtonText']);
+            GalerieOption.SetAttributes([ 'galerie', 'galerieInfo', 'galerieAction','galerieTypeLink','ButtonText','isPage']);
             return GalerieOption;
         });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieBlock.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieBlock.html	(révision 13933)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieBlock.html	(révision 13934)
@@ -7,7 +7,7 @@
         <div class="exist-gallery">
             <div>
                 <span class="icon-gallery"></span>
-                <span class="count">&nbsp;<%=fields.length%></span>
+                <span class="count">&nbsp;<%=fieldsLength%></span>
             </div>
            
         </div>
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieField.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieField.html	(révision 13933)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieField.html	(révision 13934)
@@ -1,6 +1,12 @@
 <div class="files-action  btn-galerie-content">
     <div class="wrapper  black-dropdown">
-        <div class="add-galerieField"  data-index="0">
+        <% if(isMoreThanOnePage){%>
+        <div class="wrap">
+            <input type="text" name="page-name" data-action-change="setPageNameFromInput" value="<%=galerie.page%>">
+            <span  title = "" class="delete icon icon-delete" data-cid="<%=galerie.page%>"></span>
+        </div>
+        <%}%>
+        <div class="add-galerieField"  data-cid="cid">
             <a class="btn" href="#">
                 <span class="text">
                     <span class="icon-add"></span>
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieOption.html	(révision 13933)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieOption.html	(révision 13934)
@@ -7,6 +7,14 @@
         <div class="option-content" id="galerie-collections">
             
         </div>
+        <div class="add-galeriePage"  data-index="0">
+          <a class="btn" href="#">
+              <span class="text">
+                  <span class="icon-add"></span>
+                  <%=__("addGaleriePage")%>
+              </span>
+          </a>
+      </div>
     </article>
     <article class="panel-option">
         <div class="figcaption-img" id="figcaptionInfo">
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/CollectionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/CollectionView.js	(révision 13933)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/CollectionView.js	(révision 13934)
@@ -10,7 +10,7 @@
             "click .delete":"onDeleteClick"
         },
         initialize:function () {
-            View.prototype.initialize.call(this);
+            this._super();
             this.template=this.buildTemplate(template,i18n);
             if (!this.lang)
             this.lang = this.app.params.defaultcontentlang;
@@ -23,7 +23,6 @@
             this.model.galerie.removeField(this.model);
         },
         render:function () {
-            View.prototype.render.call(this);
             var fileGroup = this.model.getFileGroup();
             var name = (fileGroup.name[this.lang])? fileGroup.name[this.lang] : i18n('nameNotFound');
             this.$el.html(this.template({field:fileGroup, name:  name}));
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieBlockView.js	(révision 13933)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieBlockView.js	(révision 13934)
@@ -30,17 +30,19 @@
         },
         renderOptions: function(model, options) {
             var uid, opts, styleOpts, galerie,  htmlContent;
-            var collections = [];
+            var fieldsLength = 0;
             uid = _.uniqueId('galerie');
             opts = this.model.options.GalerieOption;
             styleOpts = this.model.options.GalerieStyleOption;
-            galerie = opts.get("galerie").collections;
-            galerie.forEach(function (field) {
-                collections.push(field.attributes)
-            });
+            galerie = opts.get("galerie")
+            if (galerie.length > 1) {
+                fieldsLength = galerie.length;
+            }else if (galerie.length == 1) {
+                fieldsLength = galerie[0].collections.length;
+            }
            
-            if (collections.length != 0) {
-                htmlContent = this.template({uid: uid,  empty: false, fields:collections});
+            if (fieldsLength != 0) {
+                htmlContent = this.template({uid: uid,  empty: false, fieldsLength:fieldsLength});
             }
             else {
                 emptyGroup = true;
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieFieldView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieFieldView.js	(révision 13933)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieFieldView.js	(révision 13934)
@@ -4,7 +4,7 @@
  */
 define([
     "underscore",
-    "JEditor/Commons/Ancestors/Views/View",
+    "JEditor/Commons/Ancestors/Views/BabblerView",
     "JEditor/Commons/Events",
     "text!../Templates/galerieField.html",
     "./GalerieFieldView",
@@ -12,23 +12,27 @@
     "JEditor/Commons/Files/Views/CollectionSelectorDialog",
     "JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/CollectionView",
     "collection!JEditor/Commons/Files/Models/FileGroupCollection",
+    "JEditor/App/Config",
     "i18n!../nls/i18n"
-],function (_,View, Events, FieldTemplate, GalerieFieldView, FileGroup, CollectionSelectorDialog, CollectionView,FileGroupCollection, translate) {
+],function (_,BabblerView, Events, FieldTemplate, GalerieFieldView, FileGroup, CollectionSelectorDialog, CollectionView,FileGroupCollection, Config, translate) {
     
-    var GalerieFieldView=View.extend({
+    var GalerieFieldView = BabblerView.extend({
+        params: Config.getInstance(),
         events: {
             "sortstop .galerie-column":"onSortStop",
             "sortstart .galerie-column":"onSortStart",
-            
+            "click .add-galerieField .btn":"onAddClick",
+            "click .delete":"onDeleteClick",
+            "change input[name=page-name]": "changePageName"
         },
         /**
          * ici le model correspond au Models/Galerie
          */
         initialize: function() {
-            View.prototype.initialize.call(this);
+            this._super();
             this.template = this.buildTemplate(FieldTemplate,translate);
-             this.listenTo(this.model,"remove:fieldgalerie",this.render);
-             this.listenTo(this.model,"add:fieldgalerie",this.render);
+            this.listenTo(this.model,"remove:fieldgalerie",this.render);
+            this.listenTo(this.model,"add:fieldgalerie",this.render);
         },
         /**
          * on appel le view CarrouselFileEditionView pour selectionner ou ajouter une collection
@@ -54,10 +58,37 @@
             this.$(".add-field .dropdown-toggle").dropdown("toggle");
             return false;
         },
+        changePageName : function (e){
+            var $target = $(e.currentTarget);
+            this.model.page = $target.val();
+        },
+        onDeleteClick: function () {
+            var that = this;
+            if ( ! this.params.dontAskAgainFor['deletePageGalerie'] ) {
+                this.confirm({
+                    message: translate('confirmDeletePageGalerie', {
+                        'name': that.model.page
+                    }),
+                    title: translate("deleteAction"),
+                    type: 'delete',
+                    onOk: _.bind(function () {
+                        that.model.galerieOption.removePage(that.model);
+                    }, this),
+                    options: {
+                        dialogClass: 'delete no-close',
+                        dontAskAgain: true,
+                        subject: 'deletePageGalerie'
+                    }
+                });
+            } else {
+                that.model.galerieOption.removePage(that.model);
+            }
+        },
         render:function () {
             var that=this;
             this.$el.html(this.template({
-                galerie:this.model
+                galerie:this.model,
+                isMoreThanOnePage: this.model.galerieOption.isPage
             }));
             // render toute la liste des collections
             this.model.collections.forEach(function (field) {
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieOptionView.js	(révision 13933)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieOptionView.js	(révision 13934)
@@ -17,25 +17,27 @@
          
         optionType: 'galerieOption',
         events: {
-            "click .add-galerieField .btn":"onAddClick",
             'click .files-action': '_openFileGroupDialog',
             'click input[type=radio]':'_onChangeRadio',
+            "click .add-galeriePage .btn":"onAddPageClick",
         },
         className: 'gallerie-option-home panel-content',
-
+        pages :[],
         initialize: function() {
             this._super();
             this.template = this.buildTemplate(template, translate);
             this.GalerieOptionTypeLien = new GalerieOptionTypeLienView(this.model);
-            this.galerieField = new GalerieFieldView({model:this.model.galerie});
             this.listenTo(this.GalerieOptionTypeLien,'ChangeTypeLien',this.ChangeGalerieTypeLien);
+            this.listenTo(this.model,"remove:galerie",this.render);
+            this.listenTo(this.model,"add:galerie",this.render);
         },
         ChangeGalerieTypeLien:function(TypeLien){
             console.log(TypeLien);
             this.model.galerieTypeLink = TypeLien;
         },
-        onAddClick: function(event) {
-            this.galerieField.onAddClick(event);
+        onAddPageClick : function (event){
+            this.model.addPage();
+            return false;
         },
         DeleteNameInputOnMoodel : function(){
             var Mymodel=this.model.attributes;
@@ -56,8 +58,13 @@
                 typeLink:this.model.galerieTypeLink
             };
             this.$el.html(this.template(templateVars));
-            this.galerieField.render();
-            this.$("#galerie-collections").append(this.galerieField.el);
+            var that = this;
+            // render toute la liste des collections
+            this.model.galerie.forEach(function (page) {
+                var view = new GalerieFieldView({model:page});
+                that.$("#galerie-collections").append(view.el);
+                view.render();
+            });
            
             this.$("#typeDeLien").append(this.GalerieOptionTypeLien.el);
             this.GalerieOptionTypeLien.render();
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-ca/i18n.js	(révision 13933)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-ca/i18n.js	(révision 13934)
@@ -2,7 +2,7 @@
    "GalerieOption":"Galerie",
    "galerieBlockOption":"Options de la galerie",
    "GalerieStyleOption":"Style",
-   "galerieContent":" Créer une galerie",
+   "galerieContent":" Créer des galeries",
    "galerieContentLegend":"Créez et personnalisez ici votre galerie d'images.",
    "emptyGallery":"Votre grille est vide.",
    "no-emptyGallery":"Votre grille contient des images.",
@@ -54,5 +54,9 @@
    "addGalerieField": "ajouter une collection", 
    "DescStyle4"           :  "Textes sous l'image avec bordures",
    "nameNotFound"         :  '<span class="icon-warning" style="color: #d42525;"></span>collection sans nom',
-   "DescStyle5"           :  "Textes sous l'image avec images arrondies"
+   "DescStyle5"           :  "Textes sous l'image avec images arrondies",
+   "addGaleriePage"       :  "Ajouter une page",
+   "deleteAction": "Suppression",
+   "confirmDeletePageGalerie": "Voulez vous supprimer la page de la galerie ?",
+   "deletePage": "Supprimer la page",
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js	(révision 13933)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js	(révision 13934)
@@ -2,7 +2,7 @@
    "GalerieOption":"Galerie",
    "galerieBlockOption":"Options de la galerie",
    "GalerieStyleOption":"Style",
-   "galerieContent":" Créer une galerie",
+   "galerieContent":" Créer des galeries",
    "galerieContentLegend":"Créez et personnalisez ici votre galerie d'images.",
    "emptyGallery":"Votre grille est vide.",
    "no-emptyGallery":"Votre grille contient des images.",
@@ -55,5 +55,9 @@
    "DescStyle4"           :  "Texte sous l'image, bordures",
    "DescStyle5"           :  "Texte sous l'image, images arrondies (idéal images carrés)",
    "nameNotFound"         :  '<span class="icon-warning" style="color: #d42525;"></span>collection sans nom',
-   "DescStyle6"           :  "Texte à côté de l'image"
+   "DescStyle6"           :  "Texte à côté de l'image",
+   "addGaleriePage"       :  "Ajouter une page",
+   "deleteAction": "Suppression",
+   "confirmDeletePageGalerie": "Voulez vous supprimer la page de la galerie ?",
+   "deletePage": "Supprimer la page",
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/i18n.js	(révision 13933)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/i18n.js	(révision 13934)
@@ -4,7 +4,7 @@
       "GalerieOption":"Gallery",
       "galerieBlockOption":"Grid options",
       "GalerieStyleOption":"Style",
-      "galerieContent":"Create a gallery",
+      "galerieContent":"Create galleries",
       "galerieContentLegend":"Create and customize your image gallery here.",
       "emptyGallery":"Your grid is empty",
       "no-emptyGallery":"Your grid contains pictures",
@@ -64,7 +64,11 @@
       "DescStyle4"           : "Text under the image, borders",
       "nameNotFound"         :  '<span class="icon-warning" style="color: #d42525;"></span>Mameless collection',
       "DescStyle5"           :  "Text under the image, rounded pictures (ideal for square images)",
-      "DescStyle6"           :  "Text next to the the image"
+      "DescStyle6"           :  "Text next to the the image",
+      "addGaleriePage"       :  "Add Page",
+      "deleteAction": "Suppression",
+      "confirmDeletePageGalerie": "Do you want to delete the gallery page?",
+      "deletePage": "Delete page",
    },
    "fr-fr":true,
    "fr-ca":true
Index: src/less/imports/page_panel/module/block-options/btn-gallery-content.less
===================================================================
--- src/less/imports/page_panel/module/block-options/btn-gallery-content.less	(révision 13933)
+++ src/less/imports/page_panel/module/block-options/btn-gallery-content.less	(révision 13934)
@@ -62,6 +62,17 @@
 	}
 	
 }
+.add-galeriePage{
+	width: initial;
+	color: #999;
+	border: #fff 1px solid;
+	background-color: initial;
+	a {
+		width: -moz-available;
+		background-color: #1a1a1a;
+		padding: .6em 1.5em;
+	}
+}
 .btn-galerie-content {
 	background-color: #0f0f0f;
 	.add-galerieField {
@@ -70,6 +81,29 @@
 			width: 88%;
 		}
 	}
+	
+	.wrap {
+		display: flex;
+  		align-content: center;
+		width: 100%;
+		margin-bottom: 3px;
+		background-color: #000;
+		border: 1px solid #444;
+		outline: none;
+		border-radius: 2px;
+		color: #999;
+		input[name="page-name"] {
+			background-color: #000;
+			width: 100%;
+			border: none;
+		}
+		.delete{
+			font-size: 12px;
+			color: #999;
+			cursor: pointer;
+			padding: 12px;
+		}
+	}
 	ul.galerie-column {
 		list-style-type: none;
 		padding: 0;
