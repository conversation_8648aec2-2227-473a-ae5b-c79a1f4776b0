Revision: r14050
Date: 2025-04-07 14:00:33 +0300 (lts 07 Apr 2025) 
Author: rrakotoarinelina 

## Commit message
Nom des icons + filtre recherche (comme les pages) - correction comportement modal icon bizarre dans safari src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js

## Files changed

## Full metadata
------------------------------------------------------------------------
r14050 | rrakotoarinelina | 2025-04-07 14:00:33 +0300 (lts 07 Apr 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardListView.js

Nom des icons + filtre recherche (comme les pages) - correction comportement modal icon bizarre dans safari src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonStyleOptionView.js
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardListView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardListView.js	(révision 14049)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CardBlock/Views/CardListView.js	(révision 14050)
@@ -166,7 +166,8 @@
              * trigger le modal de selection d'iĉone à utiliser
              *  
              */
-            _onClickBrowseIcons:function(){
+            _onClickBrowseIcons:function(event){
+                event.stopPropagation();
                 var usedIcon = (this.model.icon !== '' ? this.model.icon: '');
                 this.selectIconDialog.getIcons(usedIcon);
                 this.selectIconDialog.open();
