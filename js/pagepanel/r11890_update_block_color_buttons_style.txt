Revision: r11890
Date: 2024-02-15 17:58:33 +0300 (lkm 15 Feb 2024) 
Author: mpartaux 

## Commit message
update block color buttons style

## Files changed

## Full metadata
------------------------------------------------------------------------
r11890 | mpartaux | 2024-02-15 17:58:33 +0300 (lkm 15 Feb 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/less/imports/page_panel/module/block-options/advanced-option.less
   M /branches/ideo3_v2/integration/src/less/imports/panel.less

update block color buttons style
------------------------------------------------------------------------

## Diff
Index: src/less/imports/page_panel/module/block-options/advanced-option.less
===================================================================
--- src/less/imports/page_panel/module/block-options/advanced-option.less	(révision 11889)
+++ src/less/imports/page_panel/module/block-options/advanced-option.less	(révision 11890)
@@ -22,6 +22,8 @@
 		border-radius: 4px;
 		overflow: hidden;
 		position: relative;
+
+		display: flex;
 	}
 
 	.container { // default color button
Index: src/less/imports/panel.less
===================================================================
--- src/less/imports/panel.less	(révision 11889)
+++ src/less/imports/panel.less	(révision 11890)
@@ -1070,12 +1070,40 @@
         width:33.3%;
         float:left;
         & .effect-radio{
+
+            margin: .5em auto;
+            height: 45px;
+
+
             & .container{
                 background:#222222;
+                display: flex;
+                justify-content: end;
             }
             &.active .container{
                 background:@pageColor;
             }
+            & .switch-container {
+                display: flex;
+                height: auto;
+                line-height: normal;
+                padding-inline: 5px;
+                align-items: center;
+                justify-content: center;
+                position: relative;
+                width: auto;
+
+                & .radio {
+                    margin-top: auto;
+                }
+            }
+            & .icon-form-long_text {
+                flex-grow: 1;
+                margin-top: 0;
+                display: inline-flex;
+                justify-content: center;
+                align-items: center;
+            }
         }
         & .container{
 
