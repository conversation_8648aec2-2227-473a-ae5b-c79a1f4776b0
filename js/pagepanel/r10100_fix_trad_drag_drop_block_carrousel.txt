Revision: r10100
Date: 2023-01-06 16:10:39 +0300 (zom 06 Jan 2023) 
Author: anthony 

## Commit message
fix trad drag&drop block carrousel

## Files changed

## Full metadata
------------------------------------------------------------------------
r10100 | anthony | 2023-01-06 16:10:39 +0300 (zom 06 Jan 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Ancestors/nls/de-de/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Ancestors/nls/es-es/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Ancestors/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Ancestors/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Ancestors/nls/i18n.js

fix trad drag&drop block carrousel
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Ancestors/nls/de-de/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Ancestors/nls/de-de/i18n.js	(révision 10099)
+++ src/js/JEditor/PagePanel/Contents/Ancestors/nls/de-de/i18n.js	(révision 10100)
@@ -1,3 +1,2 @@
 define({
-	"deleteAction": "Suppression"
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Ancestors/nls/es-es/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Ancestors/nls/es-es/i18n.js	(révision 10099)
+++ src/js/JEditor/PagePanel/Contents/Ancestors/nls/es-es/i18n.js	(révision 10100)
@@ -1,3 +1,2 @@
 define({
-	"deleteAction": "Suppression"
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Ancestors/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Ancestors/nls/fr-ca/i18n.js	(révision 10099)
+++ src/js/JEditor/PagePanel/Contents/Ancestors/nls/fr-ca/i18n.js	(révision 10100)
@@ -26,5 +26,6 @@
    "socialnetwork":"Liens sociaux",
    "separator":"Séparation",
    "table":"Tableau",
+   "carrousel":"Carrousel",
    "cannotAddContent":"Impossible d'ajouter une nouvelle colonne. <br>Le maximum est de 4 colonnes."
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Ancestors/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Ancestors/nls/fr-fr/i18n.js	(révision 10099)
+++ src/js/JEditor/PagePanel/Contents/Ancestors/nls/fr-fr/i18n.js	(révision 10100)
@@ -26,5 +26,6 @@
    "socialnetwork":"Liens sociaux",
    "separator":"Séparation",
    "table":"Tableau",
+   "carrousel":"Carrousel",
    "cannotAddContent":"Impossible d'ajouter une nouvelle colonne. <br>Le maximum est de 4 colonnes."
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Ancestors/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Ancestors/nls/i18n.js	(révision 10099)
+++ src/js/JEditor/PagePanel/Contents/Ancestors/nls/i18n.js	(révision 10100)
@@ -27,6 +27,7 @@
       "socialnetwork":"Social links",
       "separator":"Separator",
       "table":"Table",
+      "carrousel":"Carousel",
       "cannotAddContent":"You can not add a new column. <br> The maximum is 4 columns."
    },
    "fr-fr":true,
