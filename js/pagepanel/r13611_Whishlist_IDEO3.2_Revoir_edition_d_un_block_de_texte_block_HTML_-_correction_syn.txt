Revision: r13611
Date: 2024-12-16 10:15:44 +0300 (lts 16 Des 2024) 
Author: rrakotoarinelina 

## Commit message
Whishlist IDEO3.2 :Revoir edition d'un block de texte / block HTML - correction syntaxe pour compatibilité ES5

## Files changed

## Full metadata
------------------------------------------------------------------------
r13611 | rrakotoarinelina | 2024-12-16 10:15:44 +0300 (lts 16 Des 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Views/HtmlEditionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/nls/fr-fr/i18n.js

Whishlist IDEO3.2 :Revoir edition d'un block de texte / block HTML - correction syntaxe pour compatibilité ES5
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Views/HtmlEditionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Views/HtmlEditionView.js	(révision 13610)
+++ src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/Views/HtmlEditionView.js	(révision 13611)
@@ -139,13 +139,16 @@
             var modelValues = this.getModelValues();
             var editorValues = this.getEditorValues();
             
-            result = !_.isEqual(modelValues.html, editorValues.html) || 
-                     !_.isEqual(modelValues.css, editorValues.css) || 
-                     !_.isEqual(modelValues.js, editorValues.javascript) ||
-                     !_.isEqual(modelValues.jsRun, editorValues.jsRun);
+            result = !_.isEqual(this.trimValue(modelValues.html), this.trimValue(editorValues.html)) || 
+                     !_.isEqual(this.trimValue(modelValues.css), this.trimValue(editorValues.css)) || 
+                     !_.isEqual(this.trimValue(modelValues.js), this.trimValue(editorValues.javascript)) ||
+                     !_.isEqual(this.trimValue(modelValues.jsRun), this.trimValue(editorValues.jsRun));
         
             return result;
         },
+        trimValue : function(value) {
+            return String(value || '').trim();
+        },
         
         handleConfirmationOperation: function(event) {
 
@@ -198,7 +201,7 @@
             var values = {
                 jsRun: this.jsRun
             };
-            this.editors.forEach(editor => {
+            this.editors.forEach(function(editor) {
                 var mode = editor.getSession().getMode().$id.split('/')[2];
                 values[mode] = editor.getValue();
             });
Index: src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/nls/fr-fr/i18n.js	(révision 13610)
+++ src/js/JEditor/PagePanel/Contents/Blocks/HtmlBlock/nls/fr-fr/i18n.js	(révision 13611)
@@ -2,6 +2,6 @@
     "htmlEdition":"Modification html",
     "save":"Enregistrer",
     "cancel":"Annuler",
-    "quitWithoutSaving" : "Vous n'avez pas enregistré les modifications apportées à l'html, voulez-vous les enregistrer ?",
+    "quitWithoutSaving" : "Vous n'avez pas enregistré les modifications apportées, voulez-vous les enregistrer ?",
     "unsavedChanges" : "sauvegarder les changements"
 });
\ No newline at end of file
