Revision: r13864
Date: 2025-02-19 11:14:15 +0300 (lrb 19 Feb 2025) 
Author: rrakotoarinelina 

## Commit message
Whislist IDEO3.2 : Definir des cta - partie JS - correction retour

## Files changed

## Full metadata
------------------------------------------------------------------------
r13864 | rrakotoarinelina | 2025-02-19 11:14:15 +0300 (lrb 19 Feb 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonCTAOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Utils/Common.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonCTAOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/UpdateCTAView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/listeCTAView.js

Whislist IDEO3.2 : Definir des cta - partie JS - correction retour
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonCTAOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonCTAOption.html	(révision 13863)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonCTAOption.html	(révision 13864)
@@ -2,7 +2,7 @@
     <article class="panel-option">
         <header>
             <h3 class="option-name">
-                <span class="icon-button-block_icon icon-midsize"></span>
+                <span class="icon-image"></span>
                 <%=__("createCTA")%>
             </h3>
             <span class="panel-content-legend">
@@ -29,7 +29,7 @@
     <article class="panel-option">
        <header>
           <h3 class="option-name">
-             <span class="icon-drop"></span>
+             <span class="icon-image"></span>
              <%=__("applyCTA")%>
           </h3>
           <span class="panel-content-legend"> <%=__("applyCTALegend")%></span>
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonOption.html	(révision 13863)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Templates/buttonOption.html	(révision 13864)
@@ -1,7 +1,7 @@
 <div class="panel-option-container animated template-option">
     <article class="panel-option template-option">
         <header>
-            <h3 class="option-name"><span class="icon-button-text"></span><%=__("callToAction")%></h3>
+            <h3 class="option-name"><span class="icon-button-action"></span><%=__("callToAction")%></h3>
             <p class="panel-content-legend"><%= __("callToActionLegend")%></p>
         </header>
         <header>
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Utils/Common.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Utils/Common.js	(révision 13863)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Utils/Common.js	(révision 13864)
@@ -119,9 +119,19 @@
 
     $.toast(toastOptions);
 },
-  
-  
-  
+    handleCTASave: function(cta, successHandler) {
+        cta.save({}, {
+            wait: false,
+            success: successHandler,
+            error: function(xhr) {
+                console.error("Failed to save CTA:", {
+                    status: xhr.status,
+                    response: xhr.responseText,
+                    data: cta.toJSON()
+                });
+            }
+        });
+    }
   } 
     return Common;
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonCTAOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonCTAOptionView.js	(révision 13863)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/ButtonCTAOptionView.js	(révision 13864)
@@ -41,12 +41,14 @@
                 this.render();
             }.bind(this));
 
-            // this.listenTo(this.listeCTA, 'liste_cta:updated', function() {
-            //    this._showToast('updateCTA','#item-config');
-            // }.bind(this));
         },
+        _handleNewCTASuccess: function(cta) {
+            this.buttonModel.options.ButtonOption.shortcode = cta.ButtonOption.shortcode;
+            this.listeCTA.add(cta);
+            Common._showToast('.cta-list-content', translate("ctaSavedSuccessfully"));
+        },
+        
         _onAddCTA: function(e) {
-
             e.preventDefault();
 
             var cta = new CTAModel({
@@ -56,25 +58,12 @@
             });
             //ajout reference cta
             cta.ButtonOption.shortcode = "cta_" + (parseInt(this.listeCTA.length, 10) + 1);
-            cta.save({}, {
-                success: function(response) {
-                    console.log("CTA saved successfully:", response);
-                }.bind(this),
-                error: function(xhr) {
-                    console.log("Request details:", {
-                        status: xhr.status,
-                        response: xhr.responseText,
-                        data: cta.toJSON()
-                    });
-                    console.error("Failed to save CTA:", xhr);
-                }
-            });
-            //le cta crée est actif pour le bouton en cours 
-            this.buttonModel.options.ButtonOption.shortcode = cta.ButtonOption.shortcode;
-            //ajouter la nouvelle cta à la collection
-            this.listeCTA.add(cta);
-            Common._showToast('.cta-list-content',translate("ctaSavedSuccessfully"));
-           
+            //valeur par defaut si text button vide
+            if(cta.ButtonOption.text == null) {
+                cta.ButtonOption.text = cta.ButtonOption.shortcode ;
+                this.buttonModel.options.ButtonOption.text = cta.ButtonOption.shortcode ; 
+            }
+            Common.handleCTASave(cta, this._handleNewCTASuccess.bind(this,cta));
         },
         _onUpdateCTA: function(e) {
 
@@ -99,7 +88,6 @@
             var selectedCTA = this.listeCTA._getByShortCode(ctaShortcode);
             Common.setCTAToButtonModel(this.buttonModel, selectedCTA);
 
-            // Alternative 1: Trigger on the model itself
             this.buttonModel.trigger('cta:selected');
 
         },
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/UpdateCTAView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/UpdateCTAView.js	(révision 13863)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/UpdateCTAView.js	(révision 13864)
@@ -67,6 +67,16 @@
             // Enable save button after CTA selection
             this.$el.parent().find('.okay').removeClass('disabled');
         },
+        _handleUpdateCTASuccess: function() {
+            var selectedCTA = this.listeCTA._getByShortCode(this.ctaShortcode);
+            Common.setButtonModelToCTA(selectedCTA, this.ctaShortcode, this.buttonModel);
+            
+            this.listeCTAViewOnglet.render();
+            selectedCTA.trigger('cta:updated');
+            this.listeCTA.trigger('liste_cta:updated');
+            this.onClose();
+        },
+        
         _onUpdateOK: function() {
             var cta = new CTAModel({
                 ButtonOption : this.buttonModel.options.ButtonOption.toJSON(),
@@ -76,31 +86,8 @@
             });
             //ajout reference cta
             cta.ButtonOption.shortcode = this.ctaShortcode;
-            cta.save({}, {
-                wait: true,
-                success: function(response) {
-                    console.log("CTA updated successfully:", response);
-                    this.onClose();
-                }.bind(this),
-                error: function(xhr) {
-                    console.log("Request details:", {
-                        status: xhr.status,
-                        response: xhr.responseText,
-                        data: cta.toJSON()
-                    });
-                    console.error("Failed to save CTA:", xhr);
-                }
-            });
+            Common.handleCTASave(cta, this._handleUpdateCTASuccess.bind(this));
             
-            //update cta dans collection
-            var selectedCTA =  this.listeCTA._getByShortCode(this.ctaShortcode);
-            Common.setButtonModelToCTA(selectedCTA,this.ctaShortcode, this.buttonModel);
-
-            //peut etre remplacé par un evenement
-            this.listeCTAViewOnglet.render();
-           
-            selectedCTA.trigger('cta:updated');
-            this.listeCTA.trigger('liste_cta:updated');
         },
 
         _addSelectClass : function (e) {
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/listeCTAView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/listeCTAView.js	(révision 13863)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/Views/listeCTAView.js	(révision 13864)
@@ -42,21 +42,40 @@
                 var resultArray = [];
                 
                 data.forEach(function(cta) {
-                    if (!cta || 
-                        !cta.ButtonOption || 
-                        !cta.ButtonOption.text || 
-                        !cta.ButtonOption.link || 
-                        !cta.ButtonOption.link.href || 
-                        !cta.ButtonStyleOption || 
-                        !cta.ButtonStyleOption.color) {
-                        return; // Skip this iteration
+                    
+                    var shortcode, text, href, color;
+
+                    shortcode = (cta.ButtonOption && cta.ButtonOption.shortcode ) ? cta.ButtonOption.shortcode : "";
+                    text = (cta.ButtonOption && cta.ButtonOption.text) ? cta.ButtonOption.text : "";
+                    color = (cta.ButtonStyleOption && cta.ButtonStyleOption.color) ? cta.ButtonStyleOption.color : "";
+
+                    var href = "";
+                    var linkTypes = {
+                        toPage: 0,
+                        downloadFile: 2,
+                        doNothing : 3
+                      };
+                      
+                    if (cta && cta.ButtonOption && cta.ButtonOption.link) {
+                        switch (cta.ButtonOption.link.type) {
+                            case linkTypes.toPage:
+                            case linkTypes.downloadFile:
+                                href = cta.ButtonOption.link.name || "";
+                            break;
+                            case linkTypes.doNothing:
+                                href = "#";
+                            break;
+                            default:
+                                href = cta.ButtonOption.link.href || "";
+                            break;
+                        }
                     }
-                
+
                     var buttonInfo = {
-                        shortcode: cta.ButtonOption.shortcode,
-                        text: cta.ButtonOption.text,
-                        href: cta.ButtonOption.link.href,
-                        color: cta.ButtonStyleOption.color
+                        shortcode: shortcode,
+                        text: text,
+                        href: href,
+                        color: color
                     };
                 
                     resultArray.push(buttonInfo);
