Revision: r10160
Date: 2023-01-18 19:42:31 +0300 (lrb 18 Jan 2023) 
Author: anthony 

## Commit message
amelioration nouveau block map

## Files changed

## Full metadata
------------------------------------------------------------------------
r10160 | anthony | 2023-01-18 19:42:31 +0300 (lrb 18 Jan 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Addresses/Templates/addressInput.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Templates/infoWindow.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Templates/mapPointStyle.html

amelioration nouveau block map
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Addresses/Templates/addressInput.html
===================================================================
--- src/js/JEditor/Commons/Addresses/Templates/addressInput.html	(révision 10159)
+++ src/js/JEditor/Commons/Addresses/Templates/addressInput.html	(révision 10160)
@@ -17,12 +17,6 @@
     <div>
         <input type="tel" placeholder="<%=__('phone')%>" name="phone" class="phone" value="<%= address&&address.phone?address.phone:''%>"/>
     </div>
-    <div>
-        <input type="email" placeholder="<%=__('email')%>" name="email" class="email" value="<%= address&&address.email?address.email:''%>"/>
-    </div>
-    <div>
-        <input type="text" placeholder="<%=__('website')%>" class="website" name="website" value="<%= address&&address.website?address.website:''%>"/>
-    </div>
     <div class="adjust">
         <div class="text"><%= __("adjustPointer")%></div>
         <div class="map-container"></div>
Index: src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Templates/infoWindow.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Templates/infoWindow.html	(révision 10159)
+++ src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Templates/infoWindow.html	(révision 10160)
@@ -6,19 +6,9 @@
 <div class="container">
     <h1><%=address.name %></h1>
     <h2><%=address.address%></h2>
-    <% if(address.website){ %>
-    <div>
-        <span class="ideo-title"><%= __("websiteLabel")%></span><span class="ideo-value"><%=address.website %></span>
-    </div>
-    <% } %>
     <% if(address.phone){ %>
     <div>
         <span class="ideo-title"><%= __("phoneLabel")%></span><span class="ideo-value"><%=address.phone %></span>
     </div>
     <% } %>
-    <% if(address.email){ %>
-    <div>
-        <span class="ideo-title"><%= __("emailLabel")%></span><span class="ideo-value"><%=address.email %></span>
-    </div>
-    <% } %>
 </div>
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Templates/mapPointStyle.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Templates/mapPointStyle.html	(révision 10159)
+++ src/js/JEditor/PagePanel/Contents/Blocks/MapBlock/Templates/mapPointStyle.html	(révision 10160)
@@ -3,6 +3,7 @@
     <span class="address-line edit-toggle"><span class="text"><%=address.address.name %></span><span class="icon-edit"></span></span>
     <span class="address-actions">
         <span class="delete"><span class="icon-delete"></span></span>
+        <span class="popup <%=address.popup?'active':''%>"><span class="icon-popup-enabled"></span><span class="icon-popup-disabled"></span></span>
         <span class="pointer"><span class="icon-pointer-<%=address.pointer%>"></span></span>
     </span>
 </div>
