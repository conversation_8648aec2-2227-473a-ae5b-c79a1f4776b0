Revision: r10002
Date: 2022-12-15 15:46:24 +0300 (lkm 15 Des 2022) 
Author: nor<PERSON><PERSON>rive<PERSON> 

## Commit message
WishList Ideo3.2 new block carrousel Partie Integration suite

## Files changed

## Full metadata
------------------------------------------------------------------------
r10002 | norajaonarivelo | 2022-12-15 15:46:24 +0300 (lkm 15 Des 2022) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json

WishList Ideo3.2 new block carrousel Partie Integration suite
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js	(révision 10001)
+++ src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js	(révision 10002)
@@ -42,7 +42,7 @@
                                 events: {'click .block.available': 'addToCurrentContainer', 'dragstart .bloc.available': '_ondragStart'},
                                 blockOrder: {
                                     'standard': ["TextBlock", "ImageBlock", "ButtonBlock", "SeparatorBlock", "TableBlock"],
-                                    'advanced': ["GalleryBlock", "GridBlock", "FormBlock", /*"VideoLinkeoBlock",*/ "MapBlock", "VideoBlock", "HtmlBlock"],
+                                    'advanced': ["CarrouselBlock", "GridBlock", "FormBlock", /*"VideoLinkeoBlock",*/ "MapBlock", "VideoBlock", "HtmlBlock"],
                                     'others': ["LegalsBlock","SiteMapBlock","SocialNetworkBlock"],
                                     'ignored':['TwitterTimelineBlock']
                                 },
Index: src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js	(révision 10001)
+++ src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js	(révision 10002)
@@ -16,7 +16,8 @@
   "./SocialNetworkBlock",
   "./FormBlock",
   "./LegalsBlock",
-  "./EvaluationBlock"
+  "./EvaluationBlock",
+  "./CarrouselBlock"
 ], function (
   ImageBlock,
   GalleryBlock,
@@ -35,7 +36,8 @@
   SocialNetworkBlock,
   FormBlock,
   LegalsBlock,
-  EvaluationBlock
+  EvaluationBlock,
+  CarrouselBlock
 ) {
   var component = {
     "ImageBlock": ImageBlock,
@@ -55,7 +57,8 @@
     "SocialNetworkBlock": SocialNetworkBlock,
     "FormBlock": FormBlock,
     "LegalsBlock": LegalsBlock,
-    "EvaluationBlock": EvaluationBlock
+    "EvaluationBlock": EvaluationBlock,
+    "CarrouselBlock"  : CarrouselBlock
   };
   return component;
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json	(révision 10001)
+++ src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json	(révision 10002)
@@ -16,5 +16,6 @@
  "SocialNetworkBlock": "SocialNetworkBlock",
  "FormBlock": "FormBlock",
  "LegalsBlock": "LegalsBlock",
- "EvaluationBlock":"EvaluationBlock"
+ "EvaluationBlock":"EvaluationBlock",
+ "CarrouselBlock":"CarrouselBlock"
 }
