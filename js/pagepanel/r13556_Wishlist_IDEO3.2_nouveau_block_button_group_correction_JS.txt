Revision: r13556
Date: 2024-12-03 16:08:55 +0300 (tlt 03 Des 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
Wishlist IDEO3.2: nouveau block button group (correction JS)

## Files changed

## Full metadata
------------------------------------------------------------------------
r13556 | srazanandralisoa | 2024-12-03 16:08:55 +0300 (tlt 03 Des 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Links/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Ancestors/ContentView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Templates/buttonGroupBlock.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Templates/buttonGroupStyleOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Views/ButtonGroupStyleOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/i18n.js

Wishlist IDEO3.2: nouveau block button group (correction JS)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Links/nls/i18n.js
===================================================================
--- src/js/JEditor/Commons/Links/nls/i18n.js	(révision 13555)
+++ src/js/JEditor/Commons/Links/nls/i18n.js	(révision 13556)
@@ -1 +1 @@
-define({ "root": {"imageZoom":"Zoom on","imageZoomEnabled":"Zoom enabled","imageZoomTitle":"Show picture title on zoom","imageZoomDesc":"Show picture description on zoom","openLink":"Open a link","targetSelf":"Open the link in the same window","targetBlank":"Open the link in antoher window","relLitebox":"Open the link in another dialog box","sendToPage":"Send to antoher page of the site","selectPage":"Select a page","downloadFile":"Download a file","downloadTheFile":"Download the file","openInNewWindow":"Open the file in another window","doNothing":"Do nothing"}, "fr-fr":true, "fr-ca":true })
\ No newline at end of file
+define({ "root": {"imageZoom":"Zoom on","imageZoomEnabled":"Zoom enabled","imageZoomTitle":"Show picture title on zoom","imageZoomDesc":"Show picture description on zoom","openLink":"Open a link","targetSelf":"Open the link in the same window","targetBlank":"Open the link in another window","relLitebox":"Open the link in another dialog box","sendToPage":"Send to another page of the site","selectPage":"Select a page","downloadFile":"Download a file","downloadTheFile":"Download the file","openInNewWindow":"Open the file in another window","doNothing":"Do nothing"}, "fr-fr":true, "fr-ca":true })
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Ancestors/ContentView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Ancestors/ContentView.js	(révision 13555)
+++ src/js/JEditor/PagePanel/Contents/Ancestors/ContentView.js	(révision 13556)
@@ -293,7 +293,7 @@
                     this.model.addChild(futureChild, sensor.data('index'));
                     event.stopImmediatePropagation();
                 } catch (e) {
-                    if (e.message === "parent is null" ){
+                    if (/^[a-zA-Z]+ is null$/.test(e.message)) {
                         this.error({
                             message: translate('cannotAddAnotherContent')
                         });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js	(révision 13555)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonBlock/nls/i18n.js	(révision 13556)
@@ -36,10 +36,10 @@
         "colorButton":"Button color",
         "colorButtonLegend":"Apply a color to the button",
         "pastel":"Pastel",
-        "vibrant":"Vibrant",
-        "outline":"Outline",
+        "vibrante":"Vibrant",
+        "contour":"Outline",
         "pastel-lead":"Pastel Lead",
-        "vibrante-lead":"Vibrante Lead",
+        "vibrante-lead":"Vibrant Lead",
         "contour-lead":"Contour Lead",
         "browseIconTitle":"Browse the icon database",
         "choose":"Choose",
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Templates/buttonGroupStyleOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Templates/buttonGroupStyleOption.html	(révision 13555)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Templates/buttonGroupStyleOption.html	(révision 13556)
@@ -115,7 +115,7 @@
                 </div>
             </div>
         </div>
-        <div class="option-content">
+        <div class="option-content mobileButtonWrap">
          <% var _id=_.uniqueId('iconButton') %>
             <input type="checkbox" class="blue-bg show-browse-icon-button field-input" name="mobileButtonWrap" id="<%=_id %>" <%= mobileButtonWrap ? 'checked="checked"' : '' %> >
             <label for="<%=_id %>">
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Views/ButtonGroupStyleOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Views/ButtonGroupStyleOptionView.js	(révision 13555)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Views/ButtonGroupStyleOptionView.js	(révision 13556)
@@ -19,6 +19,9 @@
                         tagName: "div",
                         className: "panel-content buttonGroup-style-panel ",
                        
+                        events: {
+                            'click input[name=buttonMargin]': "_onChangebuttonMargin",
+                        },
                         /**
                          * initialise l'objet
                          */
@@ -38,10 +41,25 @@
                                 mobileButtonWrap:this.model.mobileButtonWrap
                             };
                             this.$el.html(this.template(templateVars));
+                            this.showHideWrap();
                             this.scrollables(); 
                             return this;
+                        },
+                        showHideWrap : function(){
+                            if ( this.model.buttonMargin === "0") {
+                                this.model.mobileButtonWrap = true
+                                this.$('.option-content.mobileButtonWrap').show();
+                            }
+                            else this.$('.option-content.mobileButtonWrap').hide();
+                        },
+                        _onChangebuttonMargin: function(event){
+                            event.stopPropagation();
+                            var $target = $(event.currentTarget);
+                            this.model.buttonMargin = $target.attr("value");
+                            this.showHideWrap();
                         }
                         
+                        
             }
     );
     Events.extend({
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/fr-ca/i18n.js	(révision 13555)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/fr-ca/i18n.js	(révision 13556)
@@ -1,5 +1,5 @@
 define({
-    "BLOCK_NAME": "Group de bouton",
+    "BLOCK_NAME": "Groupr de boutons",
     "buttonGroupBlockOption": "Options du groupe de boutons",
     "alignButton": "Alignement des boutons",
     "alignButtonLegend": "Sélectionnez l'alignement des boutons dans le groupe",
@@ -8,8 +8,9 @@
     "rightAlignButton": "Aligné à droite",
     "justifyAlignButton": "Justifié",
     "buttonGroupStyleOption": "Style",
-    "buttonGroup":"group de boutons",
+    "buttonGroup":"Groupe de boutons",
     "margeButton":"Marges des boutons",
     "margeButtonLegend": "Ajouter ou retirer les marges entre les boutons",
-    "underlineMobileLegend":"Passer les boutons à la ligne en mobile"
+    "underlineMobileLegend":"Passer les boutons à la ligne en mobile",
+    "addButton": "Ajouter un bouton"
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/fr-fr/i18n.js	(révision 13555)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/fr-fr/i18n.js	(révision 13556)
@@ -1,5 +1,5 @@
 define({
-    "BLOCK_NAME": "Group de bouton",
+    "BLOCK_NAME": "Groupe de boutons",
     "buttonGroupBlockOption": "Options du groupe de boutons",
     "alignButton": "Alignement des boutons",
     "alignButtonLegend": "Sélectionnez l'alignement des boutons dans le groupe",
@@ -8,9 +8,10 @@
     "rightAlignButton": "Aligné à droite",
     "justifyAlignButton": "Justifié",
     "buttonGroupStyleOption": "Style",
-    "buttonGroup":"group de boutons",
+    "buttonGroup":"Groupe de boutons",
     "margeButton":"Marges des boutons",
     "margeButtonLegend": "Ajouter ou retirer les marges entre les boutons",
-    "underlineMobileLegend":"Passer les boutons à la ligne en mobile"
+    "underlineMobileLegend":"Passer les boutons à la ligne en mobile",
+    "addButton": "Ajouter un bouton"
 
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/i18n.js	(révision 13555)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/nls/i18n.js	(révision 13556)
@@ -12,8 +12,8 @@
         "buttonGroup":"Button group",
         "margeButton":"Button margins",
         "margeButtonLegend": "Add or remove margins between buttons",
-        "underlineMobileLegend":"Pass line buttons on mobile"
-
+        "underlineMobileLegend":"Pass line buttons on mobile",
+        "addButton": "Add button"
     },
     "fr-fr": true,
     "fr-ca": true
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Templates/buttonGroupBlock.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Templates/buttonGroupBlock.html	(révision 13555)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Templates/buttonGroupBlock.html	(révision 13556)
@@ -4,7 +4,7 @@
     <div class="block-button blk-button">
     <span class="button add-button">
       <span class="plus-btn" >+</span>
-      <span class="txt blk-button__label"><span>Ajouter un bouton</span></span>
+      <span class="txt blk-button__label"><span><%=__addButton%></span></span>
     </span>
   </div>
 </div>
\ No newline at end of file
