Revision: r13944
Date: 2025-03-11 14:21:49 +0300 (tlt 11 Mar 2025) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: Evolution du bloc galerie (retour partie JS)

## Files changed

## Full metadata
------------------------------------------------------------------------
r13944 | srazanandralisoa | 2025-03-11 14:21:49 +0300 (tlt 11 Mar 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/Galerie.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieOption.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieFieldView.js
   M /branches/ideo3_v2/integration/src/less/imports/page_panel/module/block-options/btn-gallery-content.less

wishlist IDEO3.2: Evolution du bloc galerie (retour partie JS)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/Galerie.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/Galerie.js	(révision 13943)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/Galerie.js	(révision 13944)
@@ -8,8 +8,7 @@
       "./GalerieField"
     ], function(_, Model, FileGroup, GalerieField) {
     var Galerie = Model.extend({
-        defaults: function() 
-        {
+        defaults: function() {
             var ret = {
                 page : "",
                 collections: []
@@ -16,10 +15,14 @@
             };
             return ret;  
         },
+        constructor: function(attrs, options) 
+        {
+            Model.call(this, attrs, options);
+        },
         initialize : function ()
         {
+            this._super();
             var that = this;
-            Model.prototype.initialize.call(this);
             this.collections = (this.collections ? this.collections : [] ).map(function(field) {
                     field = new GalerieField(field);
                     field.galerie = that;
@@ -81,6 +84,5 @@
         }
 
     }).setAttributes(["page","collections"]);
-   Galerie;
     return Galerie;
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieOption.js	(révision 13943)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieOption.js	(révision 13944)
@@ -45,7 +45,9 @@
                             }
                             var that = this;
                             this.galerie = this.galerie.map(function(galerie) {
-                                var newGallery = new Galerie(galerie);
+                                var newGallery = galerie;
+                                if (!(galerie instanceof Galerie))
+                                     newGallery = new Galerie(galerie);
                                 newGallery.galerieOption = that;
                                 return newGallery;
                             });
@@ -57,6 +59,7 @@
                         addPage: function() 
                         {
                             var galerie = new Galerie();
+                            galerie.page = "Page "+ (this.galerie.length + 1);
                             galerie.galerieOption = this;
                             this.galerie.push(galerie);
                             this.updateIsPage();
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieFieldView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieFieldView.js	(révision 13943)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieFieldView.js	(révision 13944)
@@ -55,6 +55,10 @@
                 fileGroupDialog.remove();
             });
             fileGroupDialog.open();
+            fileGroupDialog.selectgroup();
+            fileGroupDialog.dom[fileGroupDialog.cid].buttons.removeClass("selected");
+            fileGroupDialog.dom[fileGroupDialog.cid].browseButton.addClass("selected");
+            
             this.$(".add-field .dropdown-toggle").dropdown("toggle");
             return false;
         },
Index: src/less/imports/page_panel/module/block-options/btn-gallery-content.less
===================================================================
--- src/less/imports/page_panel/module/block-options/btn-gallery-content.less	(révision 13943)
+++ src/less/imports/page_panel/module/block-options/btn-gallery-content.less	(révision 13944)
@@ -96,6 +96,7 @@
 			background-color: #000;
 			width: 100%;
 			border: none;
+			color: #999;
 		}
 		.delete{
 			font-size: 12px;
