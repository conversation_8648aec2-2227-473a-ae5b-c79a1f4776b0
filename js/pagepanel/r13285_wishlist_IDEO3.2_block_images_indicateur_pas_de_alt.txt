Revision: r13285
Date: 2024-10-21 16:05:07 +0300 (lts 21 Okt 2024) 
Author: sraz<PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: block images, indicateur pas de alt

## Files changed

## Full metadata
------------------------------------------------------------------------
r13285 | srazanandralisoa | 2024-10-21 16:05:07 +0300 (lts 21 Okt 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageBlockView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/edit.less

wishlist IDEO3.2: block images, indicateur pas de alt
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 13284)
+++ src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 13285)
@@ -18,7 +18,7 @@
     "imageEdition": "Retouche",
     "imageEdited": "Cette image a<br />été retouchée",
     "imageTitle": "Titre",
-    "imageDesc": "Description",
+    "imageDesc": "Texte alternatif / description",
     "Favicon": "Favicon",
     "btnFavicon": "Définir comme favicon",
     "imageFavicon": "Cette image est définie en tant que favicon",
Index: src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 13284)
+++ src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 13285)
@@ -18,7 +18,7 @@
     "imageEdition": "Retouche",
     "imageEdited": "Cette image a<br />été retouchée",
     "imageTitle": "Titre",
-    "imageDesc": "Description",
+    "imageDesc": "Texte alternatif / description",
     "Favicon": "Favicon",
     "btnFavicon": "Définir comme favicon",
     "imageFavicon": "Cette image est définie en tant que favicon",
Index: src/js/JEditor/FilePanel/nls/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/i18n.js	(révision 13284)
+++ src/js/JEditor/FilePanel/nls/i18n.js	(révision 13285)
@@ -19,7 +19,7 @@
         "imageEdition": "Edition",
         "imageEdited": "This picture <br />was edited",
         "imageTitle": "Title",
-        "imageDesc": "Description",
+        "imageDesc": "Alternative text / description",
         "Favicon": "Favicon",
         "btnFavicon": "Define as favicon",
         "imageFavicon": "This image is defined as a favicon",
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageBlockView.js	(révision 13284)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageBlockView.js	(révision 13285)
@@ -1,10 +1,12 @@
 define([
 	"JEditor/Commons/Events",
 	"JEditor/PagePanel/Contents/Blocks/ImageBlock/Views/ImageBlockView",
-	"JEditor/PagePanel/Contents/Blocks/Block/Views/BlockView"
+	"JEditor/PagePanel/Contents/Blocks/Block/Views/BlockView",
+    "i18n!../nls/i18n",
 ],function(	Events,
 	ImageBlockView,
-	BlockView
+	BlockView,
+    translate
 ){
 var /**
  * Vue des blocs d'images
@@ -55,11 +57,23 @@
                         this.delegateEvents()
                         return this;
                     },
+                    isContentAlt: function (){
+                        var lang = this.app.currentPanel.currentLang.id;
+                        var image = this.model.options.image;
+                        var descPlaceholder = image.file.desc[lang];
+                        if (descPlaceholder || image.contextInfos.desc ) {
+                            return true;
+                        }
+                        return false;
+                    },
                     renderOptions: function(model, options) {
                         var image = this.model.options.image;
 
                         if (image.file.fileUrl !== '#') {
-                            this.dom[this.cid].content.html('<img src="' + this.model.options.image.file.fileUrl + '" alt="' + this.model.options.image.file.desc + '"/>');
+                            $htmlImg = '<img src="' + this.model.options.image.file.fileUrl + '" alt="' + this.model.options.image.file.desc + '"/>';
+                            if(!this.isContentAlt()) 
+                                $htmlImg +=  '<span class="imageAltWarning" ><span class="icon-warning"></span> <span> '+translate('imageAltWarning')+'</span></span>';
+                            this.dom[this.cid].content.html($htmlImg);
                         }
                         else {
                             this.dom[this.cid].content.addClass('preview-wrapper').html('<div class="preview"><span class="icon-image"></span></div>');
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-ca/i18n.js	(révision 13284)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-ca/i18n.js	(révision 13285)
@@ -5,7 +5,7 @@
   "editImage": "Retoucher l'image",
   "selectClickActionImage": "Sélectionnez l'action souhaitée au clic sur l’image.",
   "imageTitle": "Titre de l'image",
-  "imageAlt": "Texte alternatif",
+  "imageAlt": "Texte alternatif / description",
   "cancel": "Annuler",
   "image": "Image",
   "imageBlockOption": "Options du bloc d'image",
@@ -23,4 +23,5 @@
   "useHd" : "Utiliser un meilleure définition d’image (HD) :",
   "hdWarningMsg" : "activer cette option chargera une image de meilleure qualité dans la page. Cela pourra avoir un impact négatif sur la vitesse de chargement",
   "hdTitle" : "Activer la haute définition",
+  "imageAltWarning" : "Manque texte alternatif"
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-fr/i18n.js	(révision 13284)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/fr-fr/i18n.js	(révision 13285)
@@ -5,7 +5,7 @@
   "editImage": "Retoucher l'image",
   "selectClickActionImage": "Sélectionnez l'action souhaitée au clic sur l’image.",
   "imageTitle": "Titre de l'image",
-  "imageAlt": "Texte alternatif",
+  "imageAlt": "Texte alternatif / description",
   "cancel": "Annuler",
   "image": "Image",
   "imageBlockOption": "Options du bloc d'image",
@@ -23,4 +23,5 @@
   "useHd" : "Utiliser un meilleure définition d’image (HD) :",
   "hdWarningMsg" : "activer cette option chargera une image de meilleure qualité dans la page. Cela pourra avoir un impact négatif sur la vitesse de chargement",
   "hdTitle" : "Activer la haute définition",
+  "imageAltWarning" : "Manque texte alternatif"
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/i18n.js	(révision 13284)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ImageBlock/nls/i18n.js	(révision 13285)
@@ -6,7 +6,7 @@
       "editImage":"Image editing",
       "selectClickActionImage":"Select the desired action when clicking image",
       "imageTitle":"Image Title",
-      "imageAlt":"Alternative text",
+      "imageAlt":"Alternative text / description",
       "cancel":"Cancel",
       "image":"Image",
       "imageBlockOption":"Options of the box image",
@@ -24,6 +24,7 @@
       "useHd" : "Using a higher-definition (HD) image:",
       "hdWarningMsg" : "enabling this option will load a higher-quality image onto the page. This may have a negative impact on loading speed",
       "hdTitle" : "Enable high definition",
+      "imageAltWarning" : "Alternative text is missing"
    },
    "fr-fr":true,
    "fr-ca":true
Index: src/less/imports/edit.less
===================================================================
--- src/less/imports/edit.less	(révision 13284)
+++ src/less/imports/edit.less	(révision 13285)
@@ -445,7 +445,23 @@
                             &>.content>img{opacity:0.8;}
                         }
 
-                        .content{padding:0; overflow:hidden; width:96%; margin:0 2%; }
+                        .content{
+                            padding:0; 
+                            overflow:hidden;
+                            width:96%;
+                            margin:0 2%; 
+                            .imageAltWarning {
+                                position: absolute;
+                                color: #d42525;
+                                left: 22px;
+                                bottom: 10px;
+                                background-color: #fff;
+                                padding: 3px 8px;
+                                border-radius: 14px;
+                                font-size: 11px;
+                                font-family: sans-serif;
+                            }
+                        }
                         &.empty{
                             min-height:200px;
                             .content{min-height:200px; }
