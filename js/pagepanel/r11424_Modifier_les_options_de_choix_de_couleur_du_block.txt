Revision: r11424
Date: 2023-10-16 09:43:59 +0300 (lts 16 Okt 2023) 
Author: rrakotoarinelina 

## Commit message
Modifier les options de choix de couleur du block

## Files changed

## Full metadata
------------------------------------------------------------------------
r11424 | rrakotoarinelina | 2023-10-16 09:43:59 +0300 (lts 16 Okt 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/Models/StylesOption.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Options/Templates/stylesOption.html

Modifier les options de choix de couleur du block
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Options/Models/StylesOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/Models/StylesOption.js	(révision 11423)
+++ src/js/JEditor/PagePanel/Contents/Options/Models/StylesOption.js	(révision 11424)
@@ -156,7 +156,6 @@
                     "surface2" : "Surface 2",
                     "surface3" : "Surface 3",
                     "surface4" : "Surface 4",
-                    "surface5" : "Surface 5",
                     "accent0" : "Accent 0",
                     "accent1" : "Accent 1",
                     "lead0" : "Lead 0",
Index: src/js/JEditor/PagePanel/Contents/Options/Templates/stylesOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Options/Templates/stylesOption.html	(révision 11423)
+++ src/js/JEditor/PagePanel/Contents/Options/Templates/stylesOption.html	(révision 11424)
@@ -29,7 +29,12 @@
             <%  
             _.each(classColor,function(value,key){%>
                 <%  var _id=_.uniqueId('couleur');%>
-                <div>
+
+                <% if (key === 'accent0' || key === 'lead0') { %>
+                    <div style="clear: both;">
+                <% } else { %>
+                    <div>
+                <% } %>
                     <span class="effect-radio <%=(defaultColorOption===key)?'active':''%>" id="<%=_id%>" data-value="<%=key%>" data-helper="<%=value%>">
                         <span class="helper">
                             <span class="help"><%=value%></span>
