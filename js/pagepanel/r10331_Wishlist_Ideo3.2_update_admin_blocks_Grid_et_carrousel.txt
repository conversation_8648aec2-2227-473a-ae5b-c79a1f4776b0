Revision: r10331
Date: 2023-02-03 13:59:08 +0300 (zom 03 Feb 2023) 
Author: norajaonarivelo 

## Commit message
Wishlist Ideo3.2:update admin blocks Grid et carrousel 

## Files changed

## Full metadata
------------------------------------------------------------------------
r10331 | norajaonarivelo | 2023-02-03 13:59:08 +0300 (zom 03 Feb 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselStyle.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridStyleOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/gallery_style.less

Wishlist Ideo3.2:update admin blocks Grid et carrousel 
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselStyle.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselStyle.html	(révision 10330)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselStyle.html	(révision 10331)
@@ -43,7 +43,7 @@
                                 <span class="icon-collection-style1"></span>
                             </span>
                             <span class="name"><%= __("Style")%> 1</span>
-                            <span class="desc">lorem ipsum</span>
+                            <span class="desc"><%= __("DescStyle1")%></span>
                         </div>
                     </div>
                 </label>
@@ -57,7 +57,7 @@
                                 <span class="icon-collection-style2"></span>
                             </span>
                             <span class="name"><%= __("Style")%> 2</span>
-                            <span class="desc">lorem ipsum</span>
+                            <span class="desc"><%= __("DescStyle2")%></span>
                         </div>
                     </div>
                 </label>
@@ -71,7 +71,7 @@
                                 <span class="icon-collection-style3"></span>
                             </span>
                             <span class="name"><%= __("Style")%> 3</span>
-                            <span class="desc">lorem ipsum</span>
+                            <span class="desc"><%= __("DescStyle3")%></span>
                         </div>
                     </div>
                 </label>
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js	(révision 10330)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js	(révision 10331)
@@ -38,8 +38,8 @@
     "ButtonReadMore"        :   "Ajouter un bouton 'en savoir plus'",
     "carrouselHeight"       :   "Nombre d'image",
     "carrouselHeightDesc"   :   "Glissez pour ajuster le nombre d'images affichées",
-    "carrouselStyleAffichage"   :   "Style du carrousel",
-    "carrouselStyleAffichageDesc"   :   "Appliquez un style au carrousel photos",
+    "carrouselStyleAffichage"   :   "Style des images",
+    "carrouselStyleAffichageDesc"   :   "Appliquez un style aux images",
     "ImageFormat"           :   "Format de l'image",
     "ImageFormatDesc"       :   "Appliquez un format d'image au carrousel",
     'landscape'             :   "Paysage",
@@ -48,4 +48,7 @@
     "arrowImage"            :   "Afficher des flèches de navigation",
     "ShowArrow"             :   "Afficher les boutons de navigation",
     "emptyCollection":"Votre collection d’images est vide.",
+    "DescStyle1"           :  "Textes sous l'image", 
+    "DescStyle2"           :  "Texte encadré sur l'image",
+    "DescStyle3"           :  "Texte sur l'image"             
 });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js	(révision 10330)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js	(révision 10331)
@@ -42,8 +42,8 @@
        "ButtonReadMore"        :   "Add a button 'read more'",
        "carrouselHeight"       :   "Number of images",
        "carrouselHeightDesc"   :   "Drag to adjust the number of images displayed",
-       "carrouselStyleAffichage"   :   "Carousel style",
-       "carrouselStyleAffichageDesc"   :   "Apply a style to the photo carousel",
+       "carrouselStyleAffichage"   :    "Style of the images",
+       "carrouselStyleAffichageDesc"   :   "Apply a style to the images",
        "ImageFormat"           :   "Image format",
        "ImageFormatDesc"       :   "Apply an image format to the carousel",
        'landscape'             :   "Landscape",
@@ -52,6 +52,9 @@
        "arrowImage"            :   "Display navigation arrows",
        "ShowArrow"             :   "Show navigation buttons",
        "emptyCollection"      :"Your image collection is empty",
+       "DescStyle1"           :  "Text under the image", 
+       "DescStyle2"           :  "Text framed on the image",
+       "DescStyle3"           :  "Text on the image"             
     },
     "fr-fr":true,
  });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridStyleOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridStyleOption.html	(révision 10330)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/Templates/gridStyleOption.html	(révision 10331)
@@ -61,7 +61,7 @@
                                 <span class="icon-collection-style1"></span>
                             </span>
                             <span class="name"><%= __("Style")%> 1</span>
-                            <span class="desc">lorem ipsum</span>
+                            <span class="desc"><%= __("DescStyle1")%></span>
                         </div>
                     </div>
                 </label>
@@ -75,7 +75,7 @@
                                 <span class="icon-collection-style2"></span>
                             </span>
                             <span class="name"><%= __("Style")%> 2</span>
-                            <span class="desc">lorem ipsum</span>
+                            <span class="desc"><%= __("DescStyle2")%></span>
                         </div>
                     </div>
                 </label>
@@ -89,7 +89,7 @@
                                 <span class="icon-collection-style3"></span>
                             </span>
                             <span class="name"><%= __("Style")%> 3</span>
-                            <span class="desc">lorem ipsum</span>
+                            <span class="desc"><%= __("DescStyle3")%></span>
                         </div>
                     </div>
                 </label>
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-ca/i18n.js	(révision 10330)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-ca/i18n.js	(révision 10331)
@@ -46,4 +46,7 @@
         'portrait'              :   "Portrait",
         'square'                :   "Carré",
         "editMyGrid"            :   "Modifier ma grille ",
+        "DescStyle1"           :  "Textes sous l'image", 
+        "DescStyle2"           :  "Texte encadré sur l'image",
+        "DescStyle3"           :  "Texte sur l'image"      
     });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-fr/i18n.js	(révision 10330)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/fr-fr/i18n.js	(révision 10331)
@@ -46,4 +46,7 @@
         'portrait'              :   "Portrait",
         'square'                :   "Carré",
         "editMyGrid"            :   "Modifier ma grille ",
+        "DescStyle1"           :  "Textes sous l'image", 
+        "DescStyle2"           :  "Texte encadré sur l'image",
+        "DescStyle3"           :  "Texte sur l'image"      
     });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/i18n.js	(révision 10330)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GridBlock/nls/i18n.js	(révision 10331)
@@ -55,4 +55,7 @@
         'portrait'              :   "Portrait",
         'square'                :   "Square",
         "editMyGrid"            :   "Edit my Grid",
+        "DescStyle1"           :  "Text under the image", 
+        "DescStyle2"           :  "Text framed on the image",
+        "DescStyle3"           :  "Text on the image"    
 }, "fr-fr":true, "fr-ca":true })
\ No newline at end of file
Index: src/less/imports/gallery_style.less
===================================================================
--- src/less/imports/gallery_style.less	(révision 10330)
+++ src/less/imports/gallery_style.less	(révision 10331)
@@ -17,7 +17,6 @@
                     height:130px;
                     text-align:center;
                     vertical-align:middle;
-                    
                     &>.name{
                         color:#ffffff;
                         font-size: 14px;
@@ -29,6 +28,9 @@
                         display:block;
                     }
                 }
+                span.icon-wrapper{
+                    font-size: 2em;
+                }
             }
         }
         &:checked{
