Revision: r12546
Date: 2024-07-08 11:02:38 +0300 (lts 08 Jol 2024) 
Author: rrakotoarinelina 

## Commit message
Whishlist IDEO3.2 : Google Tag Manager : suite - partie js

## Files changed

## Full metadata
------------------------------------------------------------------------
r12546 | rrakotoarinelina | 2024-07-08 11:02:38 +0300 (lts 08 Jol 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Models/Params.js
   M /branches/ideo3_v2/integration/src/js/JEditor/ParamsPanel/Templates/SEO.html

Whishlist IDEO3.2 : Google Tag Manager : suite - partie js
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/ParamsPanel/Models/Params.js
===================================================================
--- src/js/JEditor/ParamsPanel/Models/Params.js	(révision 12545)
+++ src/js/JEditor/ParamsPanel/Models/Params.js	(révision 12546)
@@ -4,16 +4,13 @@
     "i18n!../nls/i18n"
 ], function (Model, Events, translate) {
     var GARegex=/^(G-\w{10}$)|((UA|YT|MO)-[0-9]{1,}-[0-9]{1,}$)/;
-    var GTMRegex=/^(G|GTM)-[a-zA-Z0-9]{1,}$/;
+    var GTMRegex=/^(GTM)-[a-zA-Z0-9]{1,}$/;
     var FBPixelRegex=/^[0-9]*$/;
     var FBDomainRegex=/^[^\s]{1,}$/;
-    // var WTRegex=/^[^\s]{1,}$/;
     var WTRegex=/^(?!['"]).*[^\s]+(?<!['"])$/;
-    // var ItunesAppIdRegex=/^[0-9]*$/;
     var ItunesAppIdRegex=/^(?!['"]).*[0-9]*(?<!['"])$/;
     var PVYoutubeIdRegex=/^[a-zA-Z0-9\-_]*$/;
     var quotesRegex = /^(?!['"]).*(?<!['"])$/;
-    // var RGPDLinkRegex=/^(http(s)?:\/\/www\.|http(s)?:\/\/){1}[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\#[a-z0-9]+)?(\/.*)?$/;
     var RGPDLinkRegex=/^(http(s)?:\/\/www\.|http(s)?:\/\/)[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\#[a-z0-9]+)?(\/[^"'<>]*)?$/;
     var regExes = {
         airbnbUrl:      /^(http(s)?:\/\/)?(www\.)?airbnb\.(fr|com|ca|com\.au)\/(.*)/,
Index: src/js/JEditor/ParamsPanel/Templates/SEO.html
===================================================================
--- src/js/JEditor/ParamsPanel/Templates/SEO.html	(révision 12545)
+++ src/js/JEditor/ParamsPanel/Templates/SEO.html	(révision 12546)
@@ -36,7 +36,7 @@
         </label>
     </div>
 
-    <!--div class="inline-params  <%=GTMKey?'':' disabled '%> thin-border  radius  shadow">
+    <div class="inline-params  <%=GTMKey?'':' disabled '%> thin-border  radius  shadow">
         <span class="inline-params__name">
             <img src="../integration/assets/img/google-tagmanager-icon.png">
             Google Tag Manager
@@ -46,7 +46,7 @@
                 <input type="text" data-autosave="true" name="GTMKey" class="field-input neutral-input  bold" value="<%=GTMKey%>"/>
             </span>
         </label>
-    </div-->
+    </div>
 
     <div class="inline-params  <%=FBPixelKey?'':' disabled '%> thin-border  radius  shadow">
         <span class="inline-params__name">
