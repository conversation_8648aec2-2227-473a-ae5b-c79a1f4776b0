Revision: r10001
Date: 2022-12-15 15:42:59 +0300 (lkm 15 Des 2022) 
Author: norajaonarivelo 

## Commit message
WishList Ideo3.2 new block carrousel Partie Integration

## Files changed

## Full metadata
------------------------------------------------------------------------
r10001 | norajaonarivelo | 2022-12-15 15:42:59 +0300 (lkm 15 Des 2022) | 1 ligne
Chemins modifiés :
   A /branches/ideo3_v2/integration/assets/CarrouselTemplates
   A /branches/ideo3_v2/integration/assets/CarrouselTemplates/carrouselTemplate.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/CarrouselBlock.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/CarrouselOption.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/CarrouselStyleOption.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/main.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselOption.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselStyle.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/editImageInfosDialog.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageCollection.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageGroup.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageGroupCollection.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselBlockView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselFileEditionView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselStyleOptionView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/EditImageInfo.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/FileGroupListView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/ImageGroupView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/main.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/main.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-ca
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/build.js
   M /branches/ideo3_v2/integration/src/js/main.js
   M /branches/ideo3_v2/integration/src/less/imports/panel.less

WishList Ideo3.2 new block carrousel Partie Integration
------------------------------------------------------------------------

## Diff
Index: assets/CarrouselTemplates/carrouselTemplate.html
===================================================================
--- assets/CarrouselTemplates/carrouselTemplate.html	(nonexistent)
+++ assets/CarrouselTemplates/carrouselTemplate.html	(révision 10001)
@@ -0,0 +1,15 @@
+<div class="blk-carousel {{Arrow}} {{StyleAffichage}} {{NombreImage}} {{FormatImage}} {{advancedCss}}">
+    <div class="blk-carousel__scene">
+        {{#bigBoucle}}
+        <div class="collection-item">
+
+            <figure class="lazy">
+                {{imagePicture}}
+
+                {{imageFigcaption}}
+            </figure>
+        
+        </div>
+        {{/bigBoucle}}
+    </div>
+</div>
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/CarrouselBlock.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/CarrouselBlock.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/CarrouselBlock.js	(révision 10001)
@@ -0,0 +1,33 @@
+define( [
+    "JEditor/Commons/Events",
+    "JEditor/PagePanel/Contents/Blocks/Block/Block",
+    "JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/CarrouselOption",
+    "JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/CarrouselStyleOption",
+], function(Events, Block,CarrouselOption,CarrouselStyleOption) {
+    /**
+     * Bloc de galerie
+     * @class CarrouselBlock
+     * @extends Block
+     */
+    var CarrouselBlock = Block.extend(
+            /**
+             * @lends CarrouselBlock
+             */
+                    {
+                        defaults: {type: 'carrousel', contentType: 'carrouselBlock'},
+                        initialize: function() {
+                            this._super();
+                            if (!this.options.carrousel)
+                                this.options.add(new CarrouselOption({},{content:this}));
+                            if (!this.options.carrouselStyle)
+                                this.options.add(new CarrouselStyleOption());
+                            //this.listenTo(this.options.gallery, 'change:design', this._onDesignChange);
+                            //this.listenTo(this.options.gallery.design.options, Events.BackboneEvents.CHANGE, this._onDesignOptionChange);
+                        },
+            
+                    }
+            );
+
+            CarrouselBlock.ICON = 'icon-gallery';
+            return CarrouselBlock;
+        });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/CarrouselOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/CarrouselOption.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/CarrouselOption.js	(révision 10001)
@@ -0,0 +1,78 @@
+define([
+    "JEditor/Commons/Events",
+    "JEditor/PagePanel/Contents/Options/Models/AbstractOption",
+    "collection!JEditor/Commons/Files/Models/FileGroupCollection",
+    "JEditor/Commons/Files/Models/FileGroup"
+], function(Events,
+        AbstractOption,
+        FileGroupCollection,
+        FileGroup
+        ) {
+    var fileGroupCollection = FileGroupCollection.getInstance();
+    /**
+     * @class GalleryOption
+     * @extends AbstractOption
+     * @type @exp;JEditor@pro;Contents@pro;Options@pro;AbstractOption@call;extend
+     * @property {FileGroup} fileGroup Le groupe de photo utilisé dans la gallerie (non sérialisé dans le JSON)
+     */
+    var CarrouselOption = AbstractOption.extend(
+            /**
+             * @lends GalleryOptions.prototype
+             */
+                    {
+                        defaults: {
+                            priority: 70, 
+                            optionType: 'carrousel', 
+                            title: 'carrousel', 
+                            fileGroup: null, 
+                            Arrow       :   false, 
+                            Info        :   0,
+                            Action      :   0,
+                            TypeLien    :   1,
+                        },
+                        initialize: function() {
+                            this._super();
+                            this.on('change:fileGroup', this.onFileGroupChange);
+                            this.onFileGroupChange();
+
+
+                        },
+                        onFileGroupChange: function() {
+                            var fileGroup = this.getFileGroup();
+                            if (this.previousAttributes().fileGroup)
+                                this.stopListening(this.previousAttributes().fileGroup);
+                            if (fileGroup && !(fileGroup instanceof FileGroup))
+                                this.setFileGroup(fileGroupCollection.get(fileGroup));
+                            else if (fileGroup) {
+
+                                this.listenTo(fileGroup, 'sync change', function() {
+                                    this.trigger('change:fileGroup', this, {});
+                                    this.trigger(Events.BackboneEvents.CHANGE, this, {});
+                                });
+                            }
+                            return this;
+                        },
+                        toJSON: function() {
+                            var fileGroup = this.getFileGroup();
+                            var arrow = this.getArrow();
+                            var info = this.getInfo();
+                            var action =this.getAction();
+                            var typelien =this.getTypeLien();
+                            return {
+                                fileGroup: fileGroup?fileGroup.id:null,
+                                Arrow: arrow,
+                                Info : info,
+                                TypeLien:typelien,
+                                Action: action,
+                                optionType: 'carrousel'
+                            }
+                        },
+                        JSONClone:function(){
+                            return this.toJSON();
+                        }
+                    }
+            );
+            CarrouselOption.SetAttributes(['fileGroup', 'Arrow','Info','Action','TypeLien']);
+
+            return CarrouselOption;
+        });
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/CarrouselStyleOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/CarrouselStyleOption.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/CarrouselStyleOption.js	(révision 10001)
@@ -0,0 +1,41 @@
+define(["JEditor/PagePanel/Contents/Options/Models/AbstractOption"], function(AbstractOption) {
+    /**
+     * Styles de la galerie
+     * @class CarrouselStyleOption
+     * @extends AbstractOption
+     * @property {array} colors Les couleurs personnalisées dans le design
+     * @property {string} theme classe css du thème de la galerie
+     */
+    var CarrouselStyleOption = AbstractOption.extend(
+            /**
+             * @lends CarrouselStyleOptions.prototype
+             */
+                    {
+                        defaults: {
+                            optionType: 'carrouselStyle', 
+                            colors: {}, 
+                            theme: 'minimal', 
+                            priority: 80,
+                            FormatImage :   'landscape',
+                            NombreImage :   1,
+                            StyleAffichage  :1
+                        },
+                        validate: function(attributes, options) {
+                            if (!attributes.theme || !_.isString(attributes.theme))
+                                return "theme doit être une string et non un " + typeof attributes.theme;
+                        },
+                        toJSON: function() {
+                            return {
+                                colors: this.colors,
+                                theme: this.theme,
+                                optionType:this.optionType,
+                                FormatImage :   this.FormatImage,
+                                NombreImage :   this.NombreImage,
+                                StyleAffichage  :this.StyleAffichage
+                            }
+                        }
+                    }
+            );
+            CarrouselStyleOption.SetAttributes(['colors', 'theme','FormatImage','NombreImage','StyleAffichage']);
+            return CarrouselStyleOption;
+        });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/main.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/main.js	(révision 10001)
@@ -0,0 +1,8 @@
+// this file has been auto-generated by a grunt task, it will be overriden, do not modify it
+define(["./CarrouselOption","./CarrouselStyleOption"],function(CarrouselOption,CarrouselStyleOption){
+    var comp={
+        "CarrouselOption": CarrouselOption,
+        "CarrouselStyleOption":CarrouselStyleOption
+    };
+    return comp;
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselStyle.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselStyle.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselStyle.html	(révision 10001)
@@ -0,0 +1,81 @@
+<div class="panel-option-container animated">
+    <article class="panel-option animations">
+        <header>
+            <h3 class="option-name"><%= __("ImageFormat")%></h3>
+            <p class="panel-content-legend"><%= __("ImageFormatDesc")%>.</p>
+        </header>
+        <div class="category-content radio-transformed">
+    
+            <div><span class="effect-radio <%=(FormatImage==='landscape')?'active':''%>" id="radio_dark_galleryTemplate954" data-value="landscape" data-helper="landscape"><span class="helper"><span class="help"><%=__("landscape")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-transition-alpha"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
+            
+            <div><span class="effect-radio <%=(FormatImage==='portrait')?'active':''%>" id="radio_dark_galleryTemplate955" data-value="portrait" data-helper="portrait"><span class="helper"><span class="help"><%=__("portrait")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-transition-horizontal"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
+            <div><span class="effect-radio <%=(FormatImage==='square')?'active':''%>" id="radio_dark_galleryTemplate955" data-value="square" data-helper="square"><span class="helper"><span class="help"><%=__("square")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-transition-vertical"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
+            
+    </div>
+    </article>
+</div>
+<div class="panel-option-container animated carrousel-height">
+    <article class="panel-option carrousel-height">
+        <header>
+            <h3 class="option-name"><%= __("carrouselHeight") %></h3>
+            <p class="panel-content-legend"><%= __("carrouselHeightDesc")%></p>
+        </header>
+        <div class="option-content">
+            <div class="slider-container"><span class="icon-less"></span><div class="slider"></div><span class="icon-more"></span></div>
+        </div>
+    </article>
+</div>
+
+<div class="panel-option-container animated">
+    <article class="panel-option background-color">
+        <header>
+            <h3 class="option-name"><%=__("carrouselStyleAffichage")%></h3>
+            <p class="panel-content-legend"><%=__("carrouselStyleAffichageDesc")%></p>
+        </header>
+        <div class="option-content colors">
+            <%  var _id=_.uniqueId('carrouselStyleAffichage');
+            %>
+                <input type="radio" class="select-box" name="AffichageCarrouselStyle" value="1" id="<%=_id %>" <%=(StyleAffichage==1)?'checked':''%>>
+                   <label for="<%=_id %>">
+                    <div class="wrapper">
+                        <div class="vertical-wrap">
+                            <span class="icon-wrapper">
+                                <span class="icon icon-minimal-gallery"><span class="icon-image"></span></span>
+                            </span>
+                            <span class="name"><%= __("Style")%> 1</span>
+                            <span class="desc">lorem ipsum</span>
+                        </div>
+                    </div>
+                </label>
+                
+                <%  var _id=_.uniqueId('carrouselStyleAffichage');%>
+                <input type="radio" class="select-box" name="AffichageCarrouselStyle" value="2" id="<%=_id %>" <%=(StyleAffichage==2)?'checked':''%>>
+                   <label for="<%=_id %>">
+                    <div class="wrapper">
+                        <div class="vertical-wrap">
+                            <span class="icon-wrapper">
+                                <span class="icon icon-medium-gallery"><span class="icon-image"></span></span>
+                            </span>
+                            <span class="name"><%= __("Style")%> 2</span>
+                            <span class="desc">lorem ipsum</span>
+                        </div>
+                    </div>
+                </label>
+                
+                <%  var _id=_.uniqueId('carrouselStyleAffichage');%>
+                <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="3" id="<%=_id %>" <%=(StyleAffichage==3)?'checked':''%>>
+                   <label for="<%=_id %>">
+                    <div class="wrapper">
+                        <div class="vertical-wrap">
+                            <span class="icon-wrapper">
+                                <span class="icon icon-heavy-gallery"><span class="icon-image"></span></span>
+                            </span>
+                            <span class="name"><%= __("Style")%> 3</span>
+                            <span class="desc">lorem ipsum</span>
+                        </div>
+                    </div>
+                </label>
+                
+        </div>
+    </article>
+</div>
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/editImageInfosDialog.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/editImageInfosDialog.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/editImageInfosDialog.html	(révision 10001)
@@ -0,0 +1,21 @@
+<header>
+    <div>
+    <span class="back"></span>
+    <h2><%=__("editImages")%></h2>
+    </div>
+    <div><p><%=__("nameAndDescribeImages")%></p></div>
+</header>
+<section>
+    <div class="col image">
+        <div class="image-wrapper">
+            <div class="overlay">
+                <button data-action="edit"><%=__("edit")%></button>
+            </div>
+        </div>
+    </div>
+    <div class="col about">
+        <input type="text" name="title" />
+        <input type="text" name="title" />
+        
+    </div>
+</section>
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/editImageInfosDialog.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageCollection.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageCollection.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageCollection.html	(révision 10001)
@@ -0,0 +1,17 @@
+<div class="panel-head">
+    <span class="file-count">
+        <span class="count"><%=group.length%></span>
+        <span class="text"><%=__("imageInGroup")%></span>
+    </span>
+    <a class="new-group action <%= group.isNew()?'selected':''%>" data-action="newgroup" href="#">
+        <span class="icon-file-group"></span>
+        <span class="text"><%=__("newImageGroup") %></span>
+    </a>
+    <a class="existing action <%= !group.isNew()?'selected':''%>" data-action="selectgroup" href="#">
+        <span class="icon-gallery"  ></span>
+        <span class="text"><%=__("useExistingGroup")%></span>
+    </a>
+</div>
+<div class="panel-content">
+    
+</div>
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageCollection.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageGroup.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageGroup.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageGroup.html	(révision 10001)
@@ -0,0 +1,72 @@
+<header>
+    <div class="title">
+        <div>
+            <span class="icon-gallery"></span><input type="text" class="gallery-name" placeholder="<%=__("newCollectionName")%>" value="<%=Utils.stripHTML(group.name)%>" name="name"/><span class="icon-edit"></span>
+        </div>
+    </div><% if(group.files.length>0){%>
+
+    <div class="actions">
+        <div>
+            <div class="batch">
+                <div class="action" data-action="deleteSelected">
+                    <span class="icon-bin"></span>
+                    <span class="text"><%= __("delete") %></span>
+                </div>
+                <div class="dropdown">
+                    <a class="dropdown-toggle select" data-toggle="dropdown" href="#"><span class="caret"></span><span class="selected <%=selectedCount>0?'':'none' %> "><span class="count"><%=selectedCount>0?selectedCount:'' %></span><span class="icon-check"></span></span></a>
+                    <ul class="dropdown-menu" role="menu">
+                        <li><a class="action" data-action="selectAll" href="#"><%=__("allF")%></a></li>
+                        <li><a class="action" data-action="selectNone" href="#"><%=__("noneF")%></a></li>
+                    </ul>
+                </div>
+            </div>
+        </div>
+    </div>
+    <% } %>
+</header>
+<div class="content scroll-container">
+    <% if( !group.isNew()){ %>
+    <div class="action back">
+        <span class="image"><span class="icon-prev-arrow"></span></span>
+        <span class="text"><%=__("backToCollectionList")%></span>
+    </div>
+    <% } %>
+    <div class="group-content <%=group.files.length===0?'empty':''%> uploader">
+
+        <% for( var i = 0; i<group.files.length; i++){ 
+            var file=group.files.at(i);
+            %>
+            <div class="thumb <%= selected[file.id]?'selected':'' %> <%= (i===0?'first':'')%>" data-id="<%=file.id %>" style="background-image: url(<%= file.thumb %>);">
+                <span class="select"><span class="icon-check"></span></span>
+                <div class="menu-wrapper">
+                    <ul class="menu">
+                        <li class="action" data-action="deleteItem" data-id="<%=file.id %>"><span class="icon-bin"></span></li>
+                        <li class="action" data-action="crop" data-id="<%=file.id %>"><span class="icon-crop"></span></li>
+                        <li class="action" data-action="edit" data-id="<%=file.id %>"><span class="icon-pencil"></span></li>
+                    </ul>
+                </div>
+            </div>
+            <% } %>
+            <div class="action inline" data-action="showUploader">
+                <span class="image"><span class="icon-hexagon-add"></span></span>
+                <span class="text"><%=__("addImageToCollection")%></span>
+            </div>
+            <div class="action empty" data-action="showUploader">
+                <div class="wrapper">
+                    <span class="icon-wrapper">
+                        <% if(group.isNew()){ %>
+                        <span class="icon-new-collection"></span>
+                        <% } else{ %>
+                        <span class="icon-gallery"></span>
+                        <% } %>
+                    </span>
+                    <span class="intro">
+                        <%= group.isNew()?__("emptyNewCollection"):__("emptyCollection") %>
+                    </span>
+                    <span class="how-to">
+                        <%= __("howToAddImages") %>
+                    </span>
+                </div>
+            </div>
+    </div>
+</div>
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageGroup.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageGroupCollection.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageGroupCollection.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageGroupCollection.html	(révision 10001)
@@ -0,0 +1,25 @@
+<div class="content filegroup-collection scroll-container">
+    <div class="group-list">
+        <% for(var i =0; i<content.length; i++){ 
+        var fileGroup=content[i];
+        if(!fileGroup.isNew()){
+        %>
+
+        <div class="thumb" data-id="<%=fileGroup.id%>">
+            <div class="shadow"></div>
+            
+            <div class="image" style="background-image: url(<%= fileGroup.files.at(0)?fileGroup.files.at(0).thumb:'#'%>)">
+                <div class="overlay">
+                    <span class="groupName"><%=fileGroup.name%></span>
+                    <span class="fileCount"><span class="count"><%=fileGroup.length%></span><span class="icon-image"></span></span>
+                </div>
+                <div class="corner"></div>
+            </div>
+
+        </div>
+
+        <% 
+        }
+        }; %>
+    </div>
+</div>
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/imageGroupCollection.html
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselBlockView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselBlockView.js	(révision 10001)
@@ -0,0 +1,82 @@
+define([
+    "jquery",
+    "underscore",
+    "JEditor/Commons/Events",
+    "JEditor/PagePanel/Contents/Blocks/Block/Views/BlockView",
+    "JEditor/Commons/Files/Models/FileGroup",
+    "less",
+    //not in params
+    "owlCarouselSync",
+], function($, _, Events, BlockView, FileGroup, less) {
+    var CarrouselBlockView = BlockView.extend({
+        attributes: {
+            tabindex: 0,
+            class: "carrouselblock block"
+        },
+        initialize: function() {
+            this._super();
+            this.lessParser = new less.Parser();
+        },
+        
+        /**
+         *
+         * @param {Object} templateParams
+         * @returns {String}
+         */
+        renderHtml: function(templateParams) {
+            var galleryOptions = this.model.options.gallery;
+            var html = galleryOptions.design.options.galleryTemplate.template.html;
+            return _.template(html)(templateParams);
+
+        },
+        /**
+         *
+         * @returns {GalleryTemplate}
+         */
+        getTemplate: function() {
+            return this.getOptions().galleryTemplate.template;
+        },
+        /**
+         *
+         * @returns {}
+         */
+        getOptions: function() {
+            return this.model.options.gallery.design.options;
+        },
+        getFileGroup: function() {
+            return this.model.options.carrousel.fileGroup;
+        },
+        getDesign: function() {
+            return this.model.options.gallery.design;
+        },
+        getEffectsClass: function() {
+            var designOptions = this.getOptions();
+            var classes = [
+                designOptions.galleryStyle.theme
+            ];
+            if (designOptions.galleryAnimations) {
+                if (designOptions.galleryAnimations.transition)
+                    classes.push(designOptions.galleryAnimations.transition);
+            }
+            return classes.join(' ');
+        },
+        renderOptions: function(model, options) {
+            var template, uid, css, html, fileGroup;
+            fileGroup = this.getFileGroup();
+            if(fileGroup){
+                this.dom[this.cid].content.html('<div class="empty-gallery"><span class="icon-gallery"></span></div>');
+            }else{
+                this.dom[this.cid].content.html('<div class="empty-gallery"><span class="icon-gallery"></span></div>');
+            }
+            return this;
+
+        },
+        getFileDesc: function(file) {
+            return file.desc[this.app.currentPanel.currentLang.id];
+        },
+        getFileTitle: function(file) {
+            return file.title[this.app.currentPanel.currentLang.id];
+        }
+    });
+    return CarrouselBlockView;
+});
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselStyleOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselStyleOptionView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselStyleOptionView.js	(révision 10001)
@@ -0,0 +1,86 @@
+define([
+    "underscore",
+    "jquery",
+    "text!../Templates/carrouselStyle.html",
+    "JEditor/PagePanel/Contents/Options/Views/AbstractOptionView",
+    "i18n!../nls/i18n",
+], function(_, $, carrouselStyle,  AbstractOptionView, translate) {
+    /**
+     * Vue des options concernant le style de la galerie
+     * @class CarrouselStyleOptionView
+     * @extends JEditor.Contents.Options.View.AbstractOptionView
+     */
+    var CarrouselStyleOptionView = AbstractOptionView.extend(
+            /** 
+             * @lends CarrouselStyleOptionView.prototype
+             */
+                    {
+                        optionType: 'carrouselStyle',
+                        tagName: "div",
+                        className: "gallery-template-option galleryStyle panel-content ",
+                        events: {
+                            'slidechange .slider'   :   'onSliderChange',
+                            'change input[type="radio"].select-box': '_onStyleAffichageChange',
+                            'click .effect-radio': '_onChangeFormatImage'
+                        },
+                        /**
+                         * initialise l'objet
+                         */
+                        initialize: function() {
+                            this._super();
+                            this.template = this.buildTemplate(carrouselStyle, translate);
+                            this.listenTo(this.model,"change:buttonAlignment",this.toggleTextAlignment);
+                        },
+                        _onChangeFormatImage:function(event){
+                           this.$(".effect-radio").removeClass("active");
+                            var $target = $(event.currentTarget);
+                            $target.addClass("active");
+                            var value = $target.attr("data-value");
+                            this.model.FormatImage=value;
+                        },  
+                        /** */
+                        _onStyleAffichageChange :function(event){
+                            var $target = $(event.currentTarget);
+                            this.model.StyleAffichage = $target.val();
+                        },
+                        /**
+                         * Slider change
+                         */
+                         onSliderChange: function(event,ui){
+                            var value = ui.value;
+                            this.model.NombreImage=value;
+                            return false;
+                         },
+                        /**
+                         * affichage de l'alignement du texte
+                         */
+                        toggleTextAlignment:function () {
+                            this.$(".button-textAlignment").toggleClass("hidden",this.model.buttonAlignment != "full-width");
+                            this.$el.mCustomScrollbar('update');
+                        },
+                        /**
+                         * actualise l'affichage de la vue
+                         */
+                        render: function() {
+                            var templateVars = {
+                                FormatImage :this.model.FormatImage,
+                                StyleAffichage:this.model.StyleAffichage
+                            };
+                            this.$el.html(this.template(templateVars));
+                            this.$('.category-content').radio();
+                            var slider = this.$('.carrousel-height .slider');
+                            slider.slider({
+                                min: 1,
+                                max: 5,
+                                step: 1,
+                                value:this.model.NombreImage,
+                                range:"min"
+                            });
+                            this.dom[this.cid].slider = slider;
+                            this.scrollables();
+                            return this;
+                        }
+                    }
+            );
+            return CarrouselStyleOptionView;
+        });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/EditImageInfo.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/EditImageInfo.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/EditImageInfo.js	(révision 10001)
@@ -0,0 +1,25 @@
+define([
+	"text!../Templates/editImageInfosDialog.html",
+	"JEditor/PagePanel/Contents/Blocks/GalleryBlock/Views/EditImageInfo",
+	"JEditor/Commons/Ancestors/Views/View"
+],function(	editImageInfosDialog,
+	EditImageInfo,
+	View
+){
+var EditImageInfo = View.extend({
+    initialize:function(){
+        this._super();
+        this._template = this.buildTemplate(editImageInfosDialog,translate);
+        
+    },
+    render:function(){
+        this.$el.html(this._template());
+        return this;
+    },
+    setModel:function(model){
+        this.model=model;
+        this.render();
+    }
+});
+return EditImageInfo;
+});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/EditImageInfo.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/FileGroupListView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/FileGroupListView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/FileGroupListView.js	(révision 10001)
@@ -0,0 +1,34 @@
+define( [
+    "jquery",
+    "text!../Templates/imageGroupCollection.html",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/ListView",
+    "i18n!../nls/i18n"
+], function($, imageGroupCollection, Events, ListView, translate) {
+    var FileGroupListView = ListView.extend({
+        events: {
+            'click .action[data-action="tryreload"]': '_tryReload',
+            'click .thumb': '_selectFile'
+        },
+        initialize: function() {
+            this._super();
+            this._template = this.buildTemplate(imageGroupCollection, translate);
+            this._emptyTemplate = this.buildTemplate(imageGroupCollection, translate);
+        },
+        _selectFile: function(e) {
+            var target = $(e.currentTarget).data('id');
+            var file = this.collection.get(target);
+            this.trigger(Events.ChoiceEvents.SELECT, this, file);
+        },
+        _onLoaded: function() {
+            this.loaded = true;
+            this.render();
+        },
+        render: function() {
+            this._super();
+            this.scrollables();
+            return this;
+        }
+    });
+    return FileGroupListView;
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/ImageGroupView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/ImageGroupView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/ImageGroupView.js	(révision 10001)
@@ -0,0 +1,211 @@
+define([
+    "jquery",
+    "underscore",
+    "text!../Templates/imageGroup.html",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/View",
+    "JEditor/FilePanel/Views/ImageEditView",
+    "collection!JEditor/Commons/Files/Models/FileDBCollection",
+    "JEditor/Commons/Files/Views/FileSelectorDialog",
+    "JEditor/Commons/Utils",
+    "i18n!../nls/i18n",
+    //not in params
+    "jqueryPlugins/dropdown",
+    "jqueryPlugins/uploader"
+], function($, _, imageGroup, Events, View, ImageEditView, FileDBCollection, FileSelectorDialog, Utils, translate) {
+    var ImageGroupView = View.extend({
+        selected: null,
+        events: {
+            'uploadercomplete .uploader': '_onUploaded',
+            'keyup header input[type="text"]': '_setGroupName',
+            'paste header input[type="text"]': '_setGroupName',
+            'click .thumb': '_toggleSelected',
+            'click [data-action]': '_onActionClick',
+            'uploader_parsestock_image': 'openImageFileSelector',
+            'click .action.back': '_onBack',
+        },
+        initialize: function() {
+            this._super();
+            this._template = this.buildTemplate(imageGroup, translate);
+            this.selected = {};
+            this.model.files.each(function(file) {
+                this.selected[file.id] = false;
+            }, this);
+            this.listenTo(this.model.files, Events.BackboneEvents.ADD, this.render);
+            this.listenTo(this.model.files, Events.BackboneEvents.REMOVE, this.render);
+            this.fileCollection = FileDBCollection.getInstance();
+            this.selectFileView = new FileSelectorDialog({collection: this.fileCollection, allowMultipleSelect: true});
+            this.listenTo(this.selectFileView, Events.ListViewEvents.CHOOSE_FILE, this._useExistingFile);
+        },
+        crop: function(fileId) {
+            var file = this.model.files.get(fileId);
+            var imageEditView = new ImageEditView({model: file});
+            var widget = this.options.dialog.$el.dialog('widget');
+            imageEditView.open();
+            this.listenToOnce(imageEditView, Events.ImageEditorEvents.SAVE, this._onImageEdited);
+            return false;
+        },
+        edit: function(fileId) {
+            this.options.dialog.editImage(this.model.files.get(fileId));
+        },
+        _onImageEdited: function(newFile, oldFile) {
+            this.model.files.remove(oldFile);
+            this.model.files.add(newFile);
+            this.model.save();
+            this.render();
+        },
+        _onActionClick: function(e) {
+            var $target = $(e.currentTarget);
+            var action = $target.data('action');
+            var fileId = $target.data('id');
+            if (this[action])
+                this[action](fileId);
+            return false;
+        },
+        selectAll: function() {
+            for (var file in this.selected) {
+                this.selected[file] = true;
+            }
+            this.render();
+        },
+        selectNone: function() {
+            for (var file in this.selected) {
+                this.selected[file] = false;
+            }
+            this.render();
+        },
+        _toggleSelected: function(e) {
+            var $target = $(e.currentTarget);
+            var id = $target.data('id');
+            this.selected[id] = !this.selected[id];
+            this._updateSelectCount();
+            $target.toggleClass('selected');
+        },
+        _updateSelectCount: function() {
+            var selectedCount = 0;
+            for (var selected in this.selected) {
+                if (this.selected[selected])
+                    selectedCount++;
+            }
+            if (selectedCount > 0) {
+                this.dom[this.cid].selectCount.parent().removeClass('none')
+                this.dom[this.cid].selectCount.text(selectedCount);
+            }
+            else {
+                this.dom[this.cid].selectCount.parent().addClass('none');
+                this.dom[this.cid].selectCount.text('');
+            }
+
+        },
+        _setGroupName: function(e) {
+            if (this._timeout)
+                window.clearTimeout(this._timeout);
+            var $target = $(e.currentTarget);
+            this._timeout = window.setTimeout(_.bind(function() {
+                var value = $target.val();
+                if (value && value !== this.model.name) {
+                    this.model.name = value;
+                    console.log(this.count);
+                    this.model.save();
+                }
+            }, this), 500);
+
+        },
+        deleteSelected: function() {
+            for (var fileId in this.selected) {
+                var selected = this.selected[fileId];
+                if (selected)
+                    this.model.files.remove(this.model.files.get(fileId));
+            }
+            this.model.save();
+            this.render();
+        },
+        deleteItem: function(id) {
+            var file = this.model.files.get(id);
+            delete this.selected[file.id];
+            this.model.files.remove(file);
+            this.model.save();
+        },
+        showUploader: function() {
+            this.dom[this.cid].uploader.uploader('showMenu');
+        },
+        setModel: function(model) {
+            this.stopListening(this.model.files, Events.BackboneEvents.ADD);
+            this.stopListening(this.model.files, Events.BackboneEvents.REMOVE);
+            this.model = model;
+            model.files.each(function(file) {
+                this.selected[file.id] = false;
+            }, this);
+            this.listenTo(this.model.files, Events.BackboneEvents.ADD, this.render);
+            this.listenTo(this.model.files, Events.BackboneEvents.REMOVE, this.render);
+            this.render();
+        },
+        render: function() {
+            this.undelegateEvents();
+            var selectedCount = 0;
+            for (var selected in this.selected) {
+                if (this.selected[selected])
+                    selectedCount++;
+            }
+            this.$el.html(this._template({group: this.model, selected: this.selected, selectedCount: selectedCount, Utils: Utils}));
+            this.scrollables();
+            this.dom[this.cid].uploader = this.$('.uploader');
+            this.dom[this.cid].uploader.uploader({showMenu: false, maxFiles: -1, menuContainer: this.$('.content'), lang: this.translate.translations, customStockEvent: '_parsestock_image'});
+            this.dom[this.cid].selectCount = this.$('.batch > .dropdown > a .selected .count');
+
+            this.$('.dropdown-toggle.select').dropdown();
+            this.delegateEvents();
+            return this;
+        },
+        _onUploaded: function(event, data) {
+            var files = data.filesData;
+            var toAdd = [];
+            for (var i = 0; i < files.length; i++) {
+                var file = FileDBCollection.getInstance().create(files[i].response);
+                this.selected[file.id] = false;
+                toAdd.push(file);
+            }
+            this.model.files.add(toAdd, {at: 0});
+            if (!this.model.name) {
+                var date = new Date();
+                var dateString = date;
+                this.model.name = this.translate("newCarrousel") + Utils.dateFormat(this.translate('dateFormat'))
+            }
+            this.model.save();
+        },
+        openImageFileSelector: function() {
+            var selectFileView, that = this;
+            selectFileView = new FileSelectorDialog({collection: this.fileCollection, allowMultipleSelect: true});
+            this.listenTo(selectFileView, Events.ListViewEvents.CHOOSE_FILE, this._useExistingFile);
+            this.listenTo(selectFileView, Events.DialogEvents.CLOSE, function() {
+                that.stopListening(selectFileView);
+                selectFileView.remove();
+            });
+            selectFileView.imagesOnly();
+            selectFileView.open();
+        },
+        _useExistingFile: function(selected) {
+            var file, id;
+            if (!selected)
+                return;
+            for (id in selected) {
+                file = selected[id];
+                if (!file)
+                    continue;
+                this.model.files.add(file, {at: 0});
+                this.selected[file.id] = false;
+            }
+            if (!this.model.name) {
+                var date = new Date();
+                var dateString = date;
+                this.model.name = this.translate("newGallery") + Utils.dateFormat(this.translate('dateFormat'));
+            }
+            this.model.save();
+
+        },
+        _onBack: function() {
+            this.trigger(Events.ChoiceEvents.BACK);
+        },
+    });
+    return ImageGroupView;
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/main.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/main.js	(révision 10001)
@@ -0,0 +1,7 @@
+// this file has been auto-generated by a grunt task, it will be overriden, do not modify it
+define(["./CarrouselBlock","./Views/main","./Models/main","i18n!./nls/i18n"],function(CarrouselBlock,Views,Models,i18n){
+    CarrouselBlock.Models=Models;
+    CarrouselBlock.Views=Views;
+    CarrouselBlock.i18n=i18n;
+    return CarrouselBlock;
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js	(révision 10001)
@@ -0,0 +1,51 @@
+define({  
+    "BLOCK_NAME"            :   "Carrousel",
+    "carrouselBlockOption"  :   "Options du carrousel",
+    "carrouselContent"      :   "Contenu du carrousel",
+    "imageWarningMsg"       :   "les images trouvées sur internet ne sont généralement pas libres d’utilisation. Pour vous aider à déterminer si une image est protégée par des droits d’auteur vous pouvez utiliser le service",  
+    "imageWarning"          :   "Attention",
+    "emptyCarrousel"        :   "Votre carrousel est vide",
+    "clickToAddImages"      :   "Cliquez ici pour ajouter<br/>des images.",  
+    "carrousel"             :   "Carrousel",
+    "figcaptionImage"       :   "Afficher les informations des images",
+    "figShowTitle"          :   "Afficher le titre",
+    "figShowAll"            :   "Afficher le titre et la description",
+    "doNothing"             :   "Ne rien afficher",
+    "ActionZoom"            :   "Zoomer sur l'image",
+    "ActionPartner"         :   "Ouvrir le lien associé à l'image",
+    "selectClickActionImage":   "Sélectionnez l'action souhaitée au clic sur l’image.",
+    "editMyCarrousel"       :   "Éditer ma carrousel",
+    "imageInGroup"          :   "Images <br/>dans ma galerie",
+    "newImageGroup"         :   "Créer une nouvelle <br/> collection d'images.",
+    "useExistingGroup"      :   "Utiliser une collection <br/>d'images existante.",
+    "newCollectionName"     :   "Nouvelle collection",
+    "backToCollectionList"  :   "Retour à la liste des collections",
+    "addImageToCollection"  :   "Ajouter des images à ma collection",
+    "howToAddImages"        :   "Cliquez ici ou glissez-déposez de nouvelles images pour les ajouter à votre collection.",
+    "okay"                  :   "Valider",
+    "cancel"                :   "Annuler",
+    "editMyGallery"         :   "Éditer ma carrousel",
+    "newGallery"            :   "Nouvelle carrousel",
+    "dateFormat"            :   "Y-m-d H:i:s",
+    "emptyNewCollection"    :   "Votre nouvelle collection d’images est vide.",
+    "edit"                  :   "Éditer",
+    "delete"                :   "Supprimer",
+    "allF"                  :   "Toutes",
+    "noneF"                 :   "Aucune",
+    "selectTypeLink"        :   "Selectionnez le type de lien souhaitée",
+    "LinkImage"             :   "Ajouter le lien sur l'image",
+    "LinkText"              :   "Ajouter le lien sur le texte",
+    "ButtonReadMore"        :   "Ajouter un bouton 'en savoir plus'",
+    "carrouselHeight"       :   "Nombre d'image",
+    "carrouselHeightDesc"   :   "Glissez pour ajuster le nombre d'images affichées",
+    "carrouselStyleAffichage"   :   "Style du carrousel",
+    "carrouselStyleAffichageDesc"   :   "Appliquez un style au carrousel photos",
+    "ImageFormat"           :   "Format de l'image",
+    "ImageFormatDesc"       :   "Appliquez un format d'image au carrousel",
+    'landscape'             :   "Paysage",
+    'portrait'              :   "Portrait",
+    'square'                :   "Carré",
+    "arrowImage"            :   "Afficher des flèches de navigation",
+    "ShowArrow"             :   "Afficher les boutons de navigation",
+   
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselOption.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselOption.html	(révision 10001)
@@ -0,0 +1,136 @@
+<div class="panel-option-container animated gallery-files">
+    <article class="panel-option ">
+        <header>
+            <h3 class="option-name"><span class="icon-image"></span><%= __("carrouselContent")%></h3>
+            <p class="panel-content-legend"><span class="icon-warning" style="color: #d42525;"></span> <span style="color: #d42525;"><%= __("imageWarning")%></span> <%= __("imageWarningMsg")%> <a href="https://www.tineye.com/" target="_blank">TinEye</a>.</p>
+        </header>
+        <div class="option-content">
+            <div class="btn-group files-action">
+                <div class="btn image-count">
+                    <div>
+                        <span class="count"><%=fileGroup?fileGroup.length:0%></span><span class="icon-image"></span>
+                        <span class="icon-hexagon-add"></span>
+                    </div>
+                </div>
+                <div class="action btn">
+                    <div>
+                        <div>
+                            <% if(!fileGroup||fileGroup.length<1) {%>
+                            <div class="intro"><%=__("emptyCarrousel")%></div>
+                            <% } %>
+                            <div class="action-desc"><%=__("clickToAddImages")%></div>
+                        </div>
+                    </div>
+                </div>
+            </div>
+        </div>
+        <div class="panel-option gallery-color">
+          <p class="panel-legend"><%=__("arrowImage")%></p>
+          <div class="option-content">
+              <% var _id=_.uniqueId('arrow') %>
+              <input type="checkbox" class="blue-bg" name="showTitle" id="<%=_id %>" <%=Arrow?'checked="checked"':''%>><label for="<%=_id %>"><span class="checkbox-wrapper"><span class="icon-unchecked"></span><span class="icon-checked"></span></span><span class="text"><%=__("ShowArrow")%></span></label>
+          </div>
+        </div>
+
+        <div class="figcaption-img" id="figcaptionInfo">
+            <p class="panel-legend"><%=__("figcaptionImage")%></p>
+            <% var _id=_.uniqueId('figcaption') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="figcaption" value="1" <%=(Info==1)?"checked":""%>/>
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><%= __("figShowTitle")%></div>
+
+            </label>
+            <% var _id=_.uniqueId('figcaption') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="figcaption" value="2" <%=(Info==2)?"checked":""%>/>
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><%= __("figShowAll")%></div>
+            </label>
+            <% var _id=_.uniqueId('figcaption') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="figcaption" value="0"  <%=(Info==0)?"checked":""%>/>
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><%= __("doNothing")%></div>
+            </label>
+        </div>
+        <div class="link-img">
+            <p class="panel-legend"><%=__("selectClickActionImage")%></p>
+            <% var _id=_.uniqueId('link_action') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkAction" value="1"  <%=(Action==1)?"checked":""%>/>
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><span class="icon-find"></span><%= __("ActionZoom")%></div>
+
+            </label>
+            <% var _id=_.uniqueId('link_action') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkAction" value="2"  <%=(Action==2)?"checked":""%>/>
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><span class="icon-link"></span><%= __("ActionPartner")%></div>
+
+            </label>
+            <% var _id=_.uniqueId('link_action') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkAction" value="0" <%=(Action==0)?"checked":""%>/>
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><span class="icon-delete"></span><%= __("doNothing")%></div>
+
+            </label>
+        </div>
+        <div class="link-img">
+            <p class="panel-legend"><%=__("selectTypeLink")%></p>
+            <% var _id=_.uniqueId('link_type') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkType" value="1"  <%=(TypeLien==1)?"checked":""%> />
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><%= __("LinkImage")%></div>
+
+            </label>
+            <% var _id=_.uniqueId('link_type') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkType" value="2"  <%=(TypeLien==2)?"checked":""%>/>
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><%= __("LinkText")%></div>
+
+            </label>
+            <% var _id=_.uniqueId('link_type') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkType" value="3"  <%=(TypeLien==3)?"checked":""%> />
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><%= __("ButtonReadMore")%></div>
+
+            </label>
+        </div>
+    </article>
+</div>
+<div class="panel-option-container animated gallery-color">
+  
+</div>
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselFileEditionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselFileEditionView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselFileEditionView.js	(révision 10001)
@@ -0,0 +1,154 @@
+define([
+    "jquery",
+    "underscore",
+    "text!../Templates/imageCollection.html",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/DialogView",
+    "JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/ImageGroupView",
+    "JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/FileGroupListView",
+    "collection!JEditor/Commons/Files/Models/FileGroupCollection",
+    "collection!JEditor/Commons/Files/Models/FileGroup",
+    "JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/EditImageInfo"
+            ,
+    "i18n!../nls/i18n"], function($, _, imageCollection, Events, DialogView, ImageGroupView, FileGroupListView, FileGroupCollection, FileGroup, EditImageInfo, translate) {
+    var CarrouselFileEditionView = DialogView.extend({
+        className: 'image-group-edit',
+        events: {
+            'click .action[data-action]': '_onActionClick'
+        },
+        constructor: function(options) {
+            if (!options)
+                var options = {};
+            options.width = 750;
+            options.title = this.translate('editMyGallery')
+            options.height = 600;
+            options.buttons = [
+                {
+                    class: 'okay',
+                    text: this.translate("okay"),
+                    click: _.bind(this.onOk, this)
+                },
+                {
+                    text: this.translate("cancel"),
+                    class: 'cancel',
+                    click: _.bind(this.onCancel, this)
+                }
+            ]
+            DialogView.call(this, options);
+            this.on('open close', this.onToggle);
+        },
+        initialize: function() {
+            this._super();
+            this._template = this.buildTemplate(imageCollection, translate);
+            this.childViews = {};
+            this.childViews.editGroupView = new ImageGroupView({model: this.model, dialog: this});
+            this.childViews.imageGroupList = new FileGroupListView({collection: FileGroupCollection.getInstance()});
+            this.listenTo(this.childViews.imageGroupList, Events.ChoiceEvents.SELECT, this.setGroup);
+            this.listenTo(this.model, 'change:length', this.updateImageCount);
+            this.render();
+        },
+        _onActionClick: function(event) {
+            var $target = $(event.currentTarget);
+            var action = $target.data('action');
+            if (this[action] && !$target.hasClass('selected')) {
+                this.dom[this.cid].buttons.removeClass("selected");
+                $target.addClass("selected");
+                this[action]();
+            }
+            return false;
+        },
+        onToggle: function() {
+            this.childViews.editGroupView.updateScrollables();
+        },
+        newgroup: function(e) {
+            this.listenToOnce(this.childViews.imageGroupList, Events.ViewEvents.HIDE, function() {
+                this.stopListening(this.model, 'change:length');
+                var groupCollection = FileGroupCollection.getInstance();
+                this.model = new FileGroup();
+                groupCollection.add(this.model);
+                this.listenTo(this.model, 'change:length', this.updateImageCount);
+                this.updateImageCount();
+                this.childViews.editGroupView.setModel(this.model);
+                this.childViews.editGroupView.render();
+                this.childViews.editGroupView.show();
+                
+            });
+            this.childViews.imageGroupList.hide();
+            return false;
+        },
+        selectgroup: function(e) {
+            if (this.model.length === 0 && !this.model.name)
+                this.model.destroy();
+            this.listenToOnce(this.childViews.editGroupView, Events.ViewEvents.HIDE, function() {
+                this.childViews.imageGroupList.show();
+                this.childViews.imageGroupList.scrollables();
+            });
+            this.childViews.editGroupView.hide();
+            return false;
+        },
+        setGroup: function(view, group) {
+            this.stopListening(this.model, 'change:length');
+            this.model = group;
+            this.listenTo(this.model, 'change:length', this.updateImageCount);
+            this.updateImageCount();
+            this.childViews.editGroupView.setModel(group);
+            this.render();
+            this.dom[this.cid].buttons.removeClass("selected");
+            this.listenToOnce(this.childViews.editGroupView, Events.ViewEvents.SHOW, function() {
+                this.childViews.editGroupView.updateScrollables();
+            });
+            this.childViews.editGroupView.show();
+        },
+        onLengthChange: function() {
+            var fileGroupCollection = FileGroupCollection.getInstance();
+        },
+        updateImageCount: function() {
+            this.dom[this.cid].imageCount.text(this.model.length);
+        },
+        editImage: function(file) {
+            if (!this.childViews.singleFileEdit) {
+                this.childViews.singleFileEdit = new EditImageInfo({model: file});
+            } else {
+                this.childViews.singleFileEdit.setModel(file);
+            }
+        },
+        render: function() {
+            this.undelegateEvents();
+            this._super();
+            this.$el.html(this._template({group: this.model}));
+            this.dom[this.cid].panelContent = this.$('.panel-content');
+            this.dom[this.cid].buttons = this.$('.action[data-action="selectgroup"],.action[data-action="newgroup"]');
+            this.dom[this.cid].newButton = this.dom[this.cid].buttons.filter('[data-action="newgroup"]');
+            this.dom[this.cid].browseButton = this.dom[this.cid].buttons.filter('[data-action="selectgroup"]');
+            this.dom[this.cid].imageCount = this.$('.panel-head>.file-count>.count');
+            this.dom[this.cid].panelContent.append(this.childViews.editGroupView.render().el);
+            this.dom[this.cid].panelContent.append(this.childViews.imageGroupList.render().el);
+            this.childViews.imageGroupList.$el.hide();
+            this.listenToOnce(this.childViews.editGroupView, Events.ChoiceEvents.BACK, function() {
+                this.dom[this.cid].browseButton.removeClass('selected');
+                this.dom[this.cid].browseButton.trigger('click');
+            });
+            this.delegateEvents();
+            if (this.model.length === 0 && !this.model.name) {
+                this.dom[this.cid].newButton.removeClass('selected');
+                this.dom[this.cid].newButton.trigger('click');
+            } else
+                this.childViews.editGroupView.$el.show();
+            return this;
+        },
+        onCancel: function() {
+            if (this.model.length === 0 && !this.model.name)
+                this.model.destroy();
+            this.$el.dialog('close');
+        },
+        onOk: function() {
+            this.trigger(Events.ChoiceEvents.SELECT, this, this.model);
+            var files=this.model.attributes.files.models;
+            
+            var groupCollection = FileGroupCollection.getInstance();
+            groupCollection.add(this.model);
+            this.$el.dialog('close');
+        }
+    });
+    return CarrouselFileEditionView;
+});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselFileEditionView.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js	(révision 10001)
@@ -0,0 +1,98 @@
+define(
+    [
+        "jquery",
+        "underscore",
+        "text!../Templates/carrouselOption.html",
+        "JEditor/Commons/Events",
+        "JEditor/Commons/Files/Models/FileGroup",
+        "JEditor/PagePanel/Contents/Options/Views/AbstractOptionView",
+        "JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselFileEditionView",
+        "JEditor/App/Views/RightPanelView",
+        "JEditor/PagePanel/Contents/Options/Views/OptionCollectionView",
+        "JEditor/PagePanel/Contents/Options/Models/OptionsCollection",
+        "JEditor/Commons/Utils"
+    ],
+    function($, _, carrouselOption, Events, FileGroup, AbstractOptionView,CarrouselFileEditionView, Utils) {
+        /**
+         * Options de la galerie
+         * @class CarrouselOptionView
+         * @extends AbstractOptionView
+         */
+        var CarrouselOptionView = AbstractOptionView.extend({
+            optionType: 'carrousel',
+            events: {
+                'click .files-action'   :   'openFileGroupDialog',
+                'click input[type=radio]':'_onChangeRadio',
+                'change input[type="checkbox"].blue-bg': '_onChangeArrow'
+                },
+            className: 'carrousel-option-home panel-content',
+            initialize: function() {
+                this._super();
+                this._template = this.buildTemplate(carrouselOption, this.translate);
+                this.listenTo(this.model, 'change:fileGroup', this.onFileGroupChange);
+            },
+            openFileGroupDialog: function() {
+                var fileGroup;
+                var viewAttributes;
+                var fileGroupDialog;
+                if (this.model.fileGroup)
+                    fileGroup = this.model.fileGroup;
+                else
+                    fileGroup = new FileGroup();
+                viewAttributes = {title: this.translate("editMyCarrousel"), model: fileGroup};
+                fileGroupDialog = new CarrouselFileEditionView(viewAttributes);
+                this.listenTo(fileGroupDialog, Events.ChoiceEvents.SELECT, function(view, selected) {
+                    this.model.setFileGroup(selected);
+                });
+                this.listenTo(fileGroupDialog, Events.DialogEvents.CLOSE, function() {
+                    this.stopListening(fileGroupDialog);
+                    fileGroupDialog.remove();
+                });
+                fileGroupDialog.open();
+                return this;
+            },
+            onFileGroupChange: function(_fileGroup, options) {
+                var oldFilegroup = this.model.previousAttributes().fileGroup;
+                var fileGroup = this.model.getFileGroup();
+                if (oldFilegroup)
+                    this.stopListening(oldFilegroup, 'change:length');
+                if (fileGroup && !fileGroup.length && !fileGroup.name) {
+                    var date = new Date();
+                    fileGroup.name = this.translate("newCarrousel") + Utils.dateFormat(this.translate('dateFormat'));
+                    fileGroup.save();
+                    this.listenTo(this.model.getFileGroup(), 'change:length', this.render);
+                }
+                else if (fileGroup)
+                    this.listenTo(this.model.getFileGroup(), 'change:length', this.render);
+                this.render();
+                return this;
+            },
+            render: function() {
+                this.undelegateEvents();
+                if (this.dom[this.cid].dialogTrigger)
+                    this._fileGroupDialog.detach(this.dom[this.cid].dialogTrigger[0]);
+                this.$el.empty();
+                this.$el.html(this._template(this.model));
+               
+                this.scrollables({
+                    advanced:{ autoScrollOnFocus: false }
+                });
+                this.delegateEvents();
+                return this;
+            },
+            _onChangeArrow: function(event){
+                this.model.Arrow=!this.model.Arrow;
+            },
+            _onChangeRadio: function(event){
+                var name =event.currentTarget.name;
+                if (name ==="figcaption"){
+                    this.model.Info= event.currentTarget.value;
+                }else if(name === "LinkAction"){
+                    this.model.Action= event.currentTarget.value;
+                }else if(name === "LinkType"){
+                    this.model.TypeLien= event.currentTarget.value;
+                }
+            },
+        });
+        return CarrouselOptionView;
+    });
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/main.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/main.js	(révision 10001)
@@ -0,0 +1,30 @@
+// this file has been auto-generated by a grunt task, it will be overriden, do not modify it
+define([
+        "./CarrouselBlockView",
+        "./CarrouselOptionView",
+        "./CarrouselStyleOptionView",
+        "./ImageGroupView",
+        "./EditImageInfo",
+        "./FileGroupListView",
+        "./CarrouselFileEditionView"
+    ],
+    function(  
+        CarrouselBlockView,
+        CarrouselOptionView,
+        CarrouselStyleOptionView,
+        ImageGroupView,
+        EditImageInfo,
+        FileGroupListView,
+        CarrouselFileEditionView
+    ){
+        var comp={
+        "CarrouselBlockView"        :   CarrouselBlockView,
+        "CarrouselOptionView"       :   CarrouselOptionView,
+        "CarrouselStyleOptionView"  :   CarrouselStyleOptionView,
+        "ImageGroupView"            :   ImageGroupView,
+        "EditImageInfo"             :   EditImageInfo,
+        "FileGroupListView"         :   FileGroupListView,
+        "CarrouselFileEditionView"  :   CarrouselFileEditionView
+        };
+        return comp;
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js	(révision 10001)
@@ -0,0 +1,56 @@
+define({  
+    "root":{  
+       "BLOCK_NAME":"Carrousel",
+       "carrouselBlockOption"    :     "Carrousel options",
+       "carrouselStyle"          :     "Style",
+       "Style"                   :     "Style" ,
+       "carrouselBlockOption"  :   "Carousel options",
+       "carrouselContent"      :   "Content of the carousel",
+       "imageWarningMsg"       :   "Images found on the internet are generally not free to use. To help you determine if an image is copyrighted you can use the",  
+       "imageWarning"          :   "Warning",
+       "emptyCarrousel"        :   "Your carousel is empty",
+       "clickToAddImages"      :   "Click here to add<br/>images.",  
+       "carrousel"             :   "Carrousel",
+       "figcaptionImage"       :   "Display image information",
+       "figShowTitle"          :   "Display title",
+       "figShowAll"            :   "Display title and description",
+       "doNothing"             :   "Do not display anything",
+       "ActionZoom"            :   "Zoom in on the image",
+       "ActionPartner"         :   "Open the link associated with the image",
+       "selectClickActionImage":   "Select the desired action by clicking on the image.",
+       "editMyCarrousel"       :   "Edit my carousel",
+       "imageInGroup"          :   "Images <br/>in my gallery",
+       "newImageGroup"         :   "Create a new <br/> collection of images.",
+       "useExistingGroup"      :   "Use an existing collection <br/>of images.",
+       "newCollectionName"     :   "New collection",
+       "backToCollectionList"  :   "Back to the list of collections",
+       "addImageToCollection"  :   "Add images to my collection",
+       "howToAddImages"        :   "Click here or drag and drop new images to add them to your collection.",
+       "okay"                  :   "Confirm",
+       "cancel"                :   "Cancel",
+       "editMyGallery"         :   "Edit my carousel",
+       "newGallery"            :   "New carrousel",
+       "dateFormat"            :   "Y-m-d H:i:s",
+       "emptyNewCollection"    :   "Your new image collection is empty.",
+       "edit"                  :   "Edit",
+       "delete"                :   "Delete",
+       "allF"                  :   "All",
+       "noneF"                 :   "None",
+       "selectTypeLink"        :   "Select the type of link you want",
+       "LinkImage"             :   "Add link to image",
+       "LinkText"              :   "Add link to text",
+       "ButtonReadMore"        :   "Add a button 'read more'",
+       "carrouselHeight"       :   "Number of images",
+       "carrouselHeightDesc"   :   "Drag to adjust the number of images displayed",
+       "carrouselStyleAffichage"   :   "Carousel style",
+       "carrouselStyleAffichageDesc"   :   "Apply a style to the photo carousel",
+       "ImageFormat"           :   "Image format",
+       "ImageFormatDesc"       :   "Apply an image format to the carousel",
+       'landscape'             :   "Landscape",
+       'portrait'              :   "Portrait",
+       'square'                :   "Square",
+       "arrowImage"            :   "Display navigation arrows",
+       "ShowArrow"             :   "Show navigation buttons",
+    },
+    "fr-fr":true,
+ });
\ No newline at end of file
Index: src/js/build.js
===================================================================
--- src/js/build.js	(révision 10000)
+++ src/js/build.js	(révision 10001)
@@ -44,6 +44,7 @@
         "JEditor/PagePanel/Contents/Blocks/Block",
         "JEditor/PagePanel/Contents/Blocks/FormBlock",
         "JEditor/PagePanel/Contents/Blocks/GalleryBlock",
+        "JEditor/PagePanel/Contents/Blocks/CarrouselBlock",
         "JEditor/PagePanel/Contents/Blocks/HtmlBlock",
         "JEditor/PagePanel/Contents/Blocks/ImageBlock",
         "JEditor/PagePanel/Contents/Blocks/LegalsBlock",
Index: src/js/main.js
===================================================================
--- src/js/main.js	(révision 10000)
+++ src/js/main.js	(révision 10001)
@@ -49,6 +49,7 @@
     "JEditor/PagePanel/Contents/Blocks/Block",
     "JEditor/PagePanel/Contents/Blocks/FormBlock",
     "JEditor/PagePanel/Contents/Blocks/GalleryBlock",
+    "JEditor/PagePanel/Contents/Blocks/CarrouselBlock",
     "JEditor/PagePanel/Contents/Blocks/HtmlBlock",
     "JEditor/PagePanel/Contents/Blocks/ImageBlock",
     "JEditor/PagePanel/Contents/Blocks/LegalsBlock",
Index: src/less/imports/panel.less
===================================================================
--- src/less/imports/panel.less	(révision 10000)
+++ src/less/imports/panel.less	(révision 10001)
@@ -665,6 +665,161 @@
         }
     }
 }
+.carrousel-option-home{
+    .btn.select-design{
+        background-color:#444444;
+        width:360px;
+        margin:auto;
+        color:#ffffff;
+        text-align:center;
+        height:35px;
+        display:table-cell;
+        margin-top:40px;
+        &:hover{
+            background-color:@pageColor;
+        }
+    }
+    .btn-group.files-action,.btn-group.design-action{
+        &.design-action{
+            margin-bottom:40px;
+        }
+        height:120px;
+        width:360px;
+        margin:auto;
+        &>.btn{
+            height:100%;
+            overflow:hidden;
+        }
+        .btn.image-count,.btn.design-icon{
+            &.btn.design-icon{
+                font-size:30px;
+            }
+            &>div{
+                &>span{
+                    display:inline-block;
+                }
+                display: table-cell;
+                height: 120px;
+                vertical-align:middle;
+                width: 120px;
+                text-align:center;
+            }
+            & *{
+                vertical-align: middle;
+            }
+            background-color:#444444;
+            color:#ffffff;
+            width:120px;
+            & .icon-image{
+                font-size:25px;
+            }
+            & .count{
+                position: relative;
+                top: -4px;
+                margin-right: 5px;
+                font-size: 25px;
+            }
+            & .icon-hexagon-add{
+                display:none;
+            }
+        }
+        .btn.action{
+            padding:0;
+            &>div{
+                background-color:#2f2f2f;
+                height:120px;
+                width:240px;
+                vertical-align:middle;
+                display:table-cell;
+                &>div{
+                    &>.intro{
+                        color:#ffffff;
+                    }
+                    &>.action-desc{
+                        color: #999999;
+                    }
+                }
+            }
+        }
+        &:hover{
+            & .btn.image-count,& .btn.design-icon{
+                background-color:@pageColor;
+                & .icon-hexagon-add{
+                    display:inline-block;
+                    font-size:35px;
+                }
+                & .icon-image,& .count{
+                    display:none;
+                }
+            }
+        }
+    }
+}
+.carrousel-template-option{
+    .category-content.radio-transformed{
+        .clearfix();
+        width:90%;
+        margin:auto;
+        padding:5%;
+        background:#2f2f2f;
+    }
+    .drop-down-wrapper .btn-group,.drop-down-wrapper .btn.dropdown-toggle,.drop-down-wrapper .dropdown-menu {
+        width:100%;
+        background:#444444;
+        &>li{
+            border-top:1px solid #555555;
+            &:first-child{
+                border:none;
+            }
+            &>a:hover{
+                background:#666666;
+            }
+        }
+    }
+    .drop-down-wrapper .btn.dropdown-toggle{
+        padding:5px 0;
+        background:@pageColor;
+        color:#ffffff;
+        &>span{
+            position:relative;
+            &.text{
+                float:left;
+                left:10px;
+            }
+            &.caret{
+                float:right;
+                right:10px;
+            }
+        }
+    }
+    .category-content.radio-transformed>div{
+        width:33.3%;
+        float:left;
+        & .effect-radio{
+            & .container{
+                background:#222222;
+            }
+            &.active .container{
+                background:@pageColor;
+            }
+        }
+        & .container{
+
+
+        }
+    }
+    .template-selector-wrapper{
+        .btn-group > .btn:active{
+            z-index:initial;
+        }
+        .category-content.radio-transformed{
+            z-index:999;
+        }
+        .effect-radio .icon{
+            font-size:30px;
+        }
+    }
+}
 .gallery-option-home{
     .btn.select-design{
         background-color:#444444;
