Revision: r10803
Date: 2023-04-06 08:28:53 +0300 (lkm 06 Apr 2023) 
Author: sraz<PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2 : block galerie photos(etape1) partie JS

## Files changed

## Full metadata
------------------------------------------------------------------------
r10803 | srazanandralisoa | 2023-04-06 08:28:53 +0300 (lkm 06 Apr 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/CarrouselBlock.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselBlockView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/GalerieBlock.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/Galerie.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieField.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieOption.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieStyleOption.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/main.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/collection.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieBlock.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieField.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieOption.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieOptionTypeLien.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/CollectionView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieBlockView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieFieldView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieOptionTypeLienView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieOptionView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieStyleOptionView.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/main.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/main.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/de-de
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/de-de/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/en-au
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/en-au/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/en-us
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/en-us/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/es-es
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/es-es/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-ca
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-ca/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js
   A /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json
   M /branches/ideo3_v2/integration/src/js/build.js
   M /branches/ideo3_v2/integration/src/js/main.js
   M /branches/ideo3_v2/integration/src/less/imports/gallery_style.less
   M /branches/ideo3_v2/integration/src/less/imports/page_panel/module/block-options/btn-gallery-content.less
   M /branches/ideo3_v2/integration/src/less/imports/page_panel/module/block-options/show-part.less

wishlist IDEO3.2 : block galerie photos(etape1) partie JS
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js	(révision 10802)
+++ src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js	(révision 10803)
@@ -42,7 +42,7 @@
                                 events: {'click .block.available': 'addToCurrentContainer', 'dragstart .bloc.available': '_ondragStart'},
                                 blockOrder: {
                                     'standard': ["TextBlock", "ImageBlock", "ButtonBlock", "SeparatorBlock", "TableBlock"],
-                                    'advanced': ["CarrouselBlock", "GridBlock", "FormBlock", /*"VideoLinkeoBlock",*/ "MapBlock", "VideoBlock", "HtmlBlock"],
+                                    'advanced': ["CarrouselBlock", "GridBlock","GalerieBlock", "FormBlock", /*"VideoLinkeoBlock",*/ "MapBlock", "VideoBlock", "HtmlBlock"],
                                     'others': ["LegalsBlock","SiteMapBlock","SocialNetworkBlock"],
                                     'ignored':['TwitterTimelineBlock']
                                 },
Index: src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js	(révision 10802)
+++ src/js/JEditor/PagePanel/Contents/Blocks/Blocks.js	(révision 10803)
@@ -16,7 +16,8 @@
   "./FormBlock",
   "./LegalsBlock",
   "./EvaluationBlock",
-  "./CarrouselBlock"
+  "./CarrouselBlock",
+  "./GalerieBlock"
 ], function (
   ImageBlock,
   HtmlBlock,
@@ -35,7 +36,8 @@
   FormBlock,
   LegalsBlock,
   EvaluationBlock,
-  CarrouselBlock
+  CarrouselBlock,
+  GalerieBlock
 ) {
   var component = {
     "ImageBlock": ImageBlock,
@@ -55,7 +57,8 @@
     "FormBlock": FormBlock,
     "LegalsBlock": LegalsBlock,
     "EvaluationBlock": EvaluationBlock,
-    "CarrouselBlock"  : CarrouselBlock
+    "CarrouselBlock"  : CarrouselBlock,
+    "GalerieBlock" : GalerieBlock,
   };
   return component;
 });
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/CarrouselBlock.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/CarrouselBlock.js	(révision 10802)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/CarrouselBlock.js	(révision 10803)
@@ -28,6 +28,6 @@
                     }
             );
 
-            CarrouselBlock.ICON = 'icon-gallery';
+            CarrouselBlock.ICON = 'icon-gallery-arrow-outer';
             return CarrouselBlock;
         });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselBlockView.js	(révision 10802)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselBlockView.js	(révision 10803)
@@ -65,9 +65,9 @@
             fileGroup = this.getFileGroup();
             if ((fileGroup instanceof FileGroup) && fileGroup.length != 0) {
                 this.dom[this.cid].content.html(
-                    '<div class="exist-gallery"><div><span class="icon-gallery"></span><span class="count">&nbsp;'+fileGroup.length+'</span></div></div>');
+                    '<div class="exist-gallery"><div><span class="icon-gallery-arrow-outer"></span><span class="count">&nbsp;'+fileGroup.length+'</span></div></div>');
             }else{
-                this.dom[this.cid].content.html('<div class="empty-gallery"><span class="icon-gallery"></span></div>');
+                this.dom[this.cid].content.html('<div class="empty-gallery"><span class="icon-gallery-arrow-outer"></span></div>');
             }
             return this;
 
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/GalerieBlock.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/GalerieBlock.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/GalerieBlock.js	(révision 10803)
@@ -0,0 +1,37 @@
+define( [
+    "JEditor/Commons/Events",
+    "JEditor/PagePanel/Contents/Blocks/Block/Block",
+    "JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/Galerie",
+    "JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieOption",
+    "JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieStyleOption"
+], function(Events, Block, Galerie, GalerieOption, GalerieStyleOption) {
+    /**
+     * Bloc de grille
+     * @class GalerieBlock
+     * @extends Block
+     */
+    var GalerieBlock = Block.extend(
+            /**
+             * @lends GalerieBlock
+             */
+                    {
+                        defaults: {
+                            type: 'galerie', 
+                            contentType: 'galerieBlock',
+                        },
+                        initialize: function() {
+                            this._super();
+                            if (!this.options.GalerieOption)
+                                this.options.add(new GalerieOption());
+                            if(!this.options.GalerieStyleOption)
+                                this.options.add(new GalerieStyleOption());
+                        },
+                        
+                    }
+            );
+
+            GalerieBlock.ICON = 'icon-gallery';
+            GalerieBlock.ACCESS_CONTROL=true;
+            GalerieBlock.ACCESS_CONTROL_CRITERIA=undefined;
+            return GalerieBlock;
+        });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/Galerie.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/Galerie.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/Galerie.js	(révision 10803)
@@ -0,0 +1,74 @@
+/**
+ * <AUTHOR>
+ * Model qui contient les listes des collection d'images dans une block galerie
+ */
+define(["underscore",
+     "JEditor/Commons/Ancestors/Models/Model",
+      "JEditor/Commons/Files/Models/FileGroup",
+      "./GalerieField"
+    ], function(_, Model, FileGroup, GalerieField) {
+    var Galerie = Model.extend({
+        defaults: function() 
+        {
+            var ret = {
+                collections: []
+            };
+            return ret;  
+        },
+        initialize : function ()
+        {
+            var that = this;
+            Model.prototype.initialize.call(this);
+            this.collections = (this.collections ? this.collections : [] ).map(function(field) {
+                    field = new GalerieField(field);
+                    field.galerie = that;
+                    return field;
+            });
+        },
+        /**
+         * structuration d'un model FielGroup pour etre un model GalerieField
+         * @param {FileGroup} field  
+         * @returns ret
+         */
+        Tofield: function (field)
+        {
+            var ret = {
+                id:field.id,
+                name: field.name,
+                length: field.length,
+            };
+            return ret;
+        },
+        /**
+         * creation d'un model GalerieField et on l'ajoute dans notre collections
+         * @param {*} field 
+         */
+        addField: function(field) 
+        {
+            var collection = new GalerieField(this.Tofield(field));
+            collection.galerie = this;
+            collection.index = this.collections.length ;
+            this.collections.push(collection);
+    
+            this.trigger("add:fieldgalerie", this);
+        },
+        /**
+         * supression d'un model GalerieField dans les liste de collections
+         * @param {*} field 
+         * @returns 
+         */
+        removeField: function(field)
+        {
+            var index;
+            if (field instanceof GalerieField)
+                index = this.collections.lastIndexOf(field);
+            else
+                index = field;
+            collection = this.collections.splice(index, 1)[0];
+            this.trigger("remove:fieldgalerie", this);
+            return this;
+        }
+    }).setAttributes(["collections"]);
+   Galerie;
+    return Galerie;
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieField.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieField.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieField.js	(révision 10803)
@@ -0,0 +1,29 @@
+/**
+ * <AUTHOR>
+ * nouveau model pour la collection dans le galerie
+ * @property {integer} id id de la collection
+ * @property {string} name nom de la collection 
+ * @property {integer} length nombre des image dans la collection
+ */
+define(["JEditor/Commons/Ancestors/Models/Model"], function(Model) {
+    var GalerieField = Model.extend({
+        defaults: function() {
+            var ret = {
+                id:null,
+                name: "",
+                length: 0,
+            };
+            return ret;
+        },
+        constructor: function(attrs, options) 
+        {
+            Model.call(this, attrs, options);
+        },
+        remove:function () 
+        {
+            this.galerie.removeField(null,null,this);
+            return this;
+        },
+    }).setAttributes(["name","length"]);
+    return GalerieField;
+});
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieOption.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieOption.js	(révision 10803)
@@ -0,0 +1,44 @@
+define([
+    "JEditor/Commons/Events",
+    "JEditor/PagePanel/Contents/Options/Models/AbstractOption",
+    "JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/Galerie",
+], function(Events,
+        AbstractOption,
+        Galerie
+        ) {
+    /**
+     * @class GalerieOption
+     * @extends AbstractOption
+     * @type @exp;JEditor@pro;Contents@pro;Options@pro;AbstractOption@call;extend
+     * @property {integer} priority ordre de priorité d'affichage dans le rightpanel
+     * @property {optionType} optionType nom qu'on attribut pour identifier l'option
+     * @property {optionType} galerie c'est un instance de Galerie, contient tout les listes de collections 
+     * @property {optionType} galerieInfo information et figcaption
+     * @property {optionType} galerieAction action sur les images
+     * @property {optionType} galerieTypeLink type des liens sur les images
+     * 
+     */
+    var GalerieOption = AbstractOption.extend(
+            /**
+             * @lends GalerieOptions.prototype
+             */
+                    {
+                        defaults: {
+                            priority: 70, 
+                            optionType: 'GalerieOption', 
+                            galerie : null,
+                            galerieInfo:0, 
+                            galerieAction: 0,
+                            galerieTypeLink :1,
+                        },
+                        initialize: function() {
+                            this._super();
+                            if(!(this.galerie) || !(this.galerie instanceof Galerie)){
+                                this.galerie = new Galerie(Galerie.prototype.parse( this.galerie ||{}));
+                            }
+                        },
+                    }
+            );
+            GalerieOption.SetAttributes([ 'galerie', 'galerieInfo', 'galerieAction','galerieTypeLink']);
+            return GalerieOption;
+        });
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieStyleOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieStyleOption.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieStyleOption.js	(révision 10803)
@@ -0,0 +1,35 @@
+define([
+    "JEditor/PagePanel/Contents/Options/Models/AbstractOption",
+    "JEditor/Commons/Events",
+], function(AbstractOption) {
+    /**
+     * Styles de la galerie
+     * @class GalleryStyleOption
+     * @extends AbstractOption
+     * @type @exp;JEditor@pro;Contents@pro;Options@pro;AbstractOption@call;extend
+     * @property {integer} priority ordre de priorité d'affichage dans le rightpanel
+     * @property {optionType} optionType nom qu'on attribut pour identifier l'option
+     * @property {optionType} galerieStyle  style de grille
+     * @property {optionType} galerieFormat  format de grille
+     * @property {optionType} galerieNbreImage nombre d'image pour une ligne
+     * @property {optionType} galerieStyleAff  style d'affichage
+     */
+    var GalerieStyleOption = AbstractOption.extend(
+            /**
+             * @lends GalleryStyleOptions.prototype
+             */
+                    {
+                        defaults: {
+                            priority: 80, 
+                            optionType: 'GalerieStyleOption', 
+                            galerieStyle :   0,
+                            galerieFormat    :   'landscape',
+                            galerieNbreImage :   3,
+                            galerieStyleAff  :   1,
+                        },
+                        
+                    }
+            );
+            GalerieStyleOption.SetAttributes(['galerieStyle', 'galerieFormat', 'galerieNbreImage','galerieStyleAff']);
+            return GalerieStyleOption;
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/main.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/main.js	(révision 10803)
@@ -0,0 +1,10 @@
+// this file has been auto-generated by a grunt task, it will be overriden, do not modify it
+define(["./GalerieField","./GalerieOption","./GalerieStyleOption","./Galerie"],function(GalerieField, GalerieOption, GalerieStyleOption, Galerie){
+    var comp={
+        "GalerieField":GalerieField,
+        "GalerieOption":GalerieOption,
+        "GalerieStyleOption":GalerieStyleOption,
+        "Galerie":Galerie
+    };
+    return comp;
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/collection.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/collection.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/collection.html	(révision 10803)
@@ -0,0 +1,8 @@
+
+    <div class = "show-part" id="<%= field.id %>" >
+        <span class="icon-grip"></span>
+        <span class="field-name"> <%= field.name %>  </span> 
+        <span class="collection-nb"> <%= field.length %>  images</span>
+        <span class="delete">&nbsp;x</span>
+    </div> 
+   </li> 
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieBlock.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieBlock.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieBlock.html	(révision 10803)
@@ -0,0 +1,15 @@
+<div id="<%= uid %>">
+    
+    <% if(empty){%>
+        <div class="empty-gallery"><span class="icon-gallery"></span></div>
+    <% } else{
+    %>
+        <div class="exist-gallery">
+            <div>
+                <span class="icon-gallery"></span>
+                <span class="count">&nbsp;<%=fields.length%></span>
+            </div>
+           
+        </div>
+    <% }%>
+</div>
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieField.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieField.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieField.html	(révision 10803)
@@ -0,0 +1,19 @@
+<div class="files-action  btn-galerie-content">
+    <div class="wrapper  black-dropdown">
+        <div class="add-galerieField"  data-index="0">
+            <a class="btn" href="#">
+                <span class="text">
+                    <span class="icon-add"></span>
+                    <%=__("addGalerieField")%>
+                </span>
+            </a>
+        </div>
+        <div class="wrapper row-wrapper column-container">
+            <div class="wrapper col-wrapper">
+                <ul class="galerie-column" >
+                </ul>
+            </div>
+        </div>
+    </div>
+</div>
+                
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieOption.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieOption.html	(révision 10803)
@@ -0,0 +1,80 @@
+<div class="panel-option-container animated gallery-files  social-option-wrap">
+    <article class="panel-option ">
+        <header>
+            <h3 class="option-name"><span class="icon-image"></span><%= __("galerieContent")%></h3>
+            <p class="panel-content-legend"></span> <%= __("galerieContentLegend")%> .</p>
+        </header>
+        <div class="option-content" id="galerie-collections">
+            
+        </div>
+    </article>
+    <article class="panel-option">
+        <div class="figcaption-img" id="figcaptionInfo">
+            <p class="panel-legend"><%=__("figcaptionImage")%></p>
+            <% var _id=_.uniqueId('figcaption') ;%>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="figcaption" value="1" <%=(info==1)?"checked=checked":""%> />
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><%= __("figShowTitle")%></div>
+
+            </label>
+            <% var _id=_.uniqueId('figcaption') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="figcaption" value="2" <%=(info==2)?"checked=checked":""%>/>
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><%= __("figShowAll")%></div>
+            </label>
+            <% var _id=_.uniqueId('figcaption') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="figcaption" value="0" <%=(info==0)?"checked=checked":""%>  />
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><%= __("doNothing")%></div>
+            </label>
+        </div>
+        <div class="link-img">
+            <p class="panel-legend"><%=__("selectClickActionImage")%></p>
+            <% var _id=_.uniqueId('link_action') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkAction" value="1"  <%=(action==1)?"checked=checked":""%>/>
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><span class="icon-find"></span>&emsp;<%= __("ActionZoom")%></div>
+
+            </label>
+            <% var _id=_.uniqueId('link_action') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkAction" value="2" <%=(action==2)?"checked=checked":""%> />
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><span class="icon-link"></span>&emsp;<%= __("ActionPartner")%></div>
+
+            </label>
+            <% var _id=_.uniqueId('link_action') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkAction" value="0" <%=(action==0)?"checked=checked":""%> />
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><span class="icon-delete"></span>&emsp;<%= __("doNothing")%></div>
+
+            </label>
+        </div>
+        <div class="link-img" id="typeDeLien">
+            
+        </div>
+    </article>
+</div>
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieOptionTypeLien.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieOptionTypeLien.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieOptionTypeLien.html	(révision 10803)
@@ -0,0 +1,31 @@
+<p class="panel-legend"><%=__("selectTypeLink")%></p>
+<% var _id= "link_type1" %>
+<input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkType" value="1"  <%=(TypeLien==1)?"checked":""%> />
+<label  class="block-label" for="<%= _id %>" >
+    <div class="radio-wrapper">
+    <span class="icon  icon-radio-active"></span>
+    <span class="icon  icon-radio-inactive"></span>
+    </div>
+    <div class="block-label-radio"><%= __("LinkImage")%></div>
+
+</label>
+<% var _id= "link_type2" %>
+<input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkType" value="2"  <%=(TypeLien==2)?"checked":""%>/>
+<label  class="block-label <%=(LinkText)?'carrouselTypeLinkDisable':''%>" for="<%= _id %>" >
+    <div class="radio-wrapper">
+    <span class="icon  icon-radio-active"></span>
+    <span class="icon  icon-radio-inactive"></span>
+    </div>
+    <div class="block-label-radio"><%= __("LinkText")%></div>
+
+</label>
+<% var _id= "link_type3" %>
+<input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="LinkType" value="3"  <%=(TypeLien==3)?"checked":""%> />
+<label  class="block-label <%=(BouttonMoreInfo)?'carrouselTypeLinkDisable':''%>" for="<%= _id %>" >
+    <div class="radio-wrapper">
+    <span class="icon  icon-radio-active"></span>
+    <span class="icon  icon-radio-inactive"></span>
+    </div>
+    <div class="block-label-radio"><%= __("ButtonReadMore")%></div>
+
+</label>
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/CollectionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/CollectionView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/CollectionView.js	(révision 10803)
@@ -0,0 +1,29 @@
+/**
+ * <AUTHOR>
+ * View pour chaque collection
+ */
+define(["JEditor/Commons/Ancestors/Views/View","text!../Templates/collection.html","i18n!../nls/i18n"],function (View,template,i18n) {
+    var CollectionView=View.extend({
+        tagName:'li',
+        className:'galerie-element',
+        events:{
+            "click .delete":"onDeleteClick"
+        },
+        initialize:function () {
+            View.prototype.initialize.call(this);
+            this.template=this.buildTemplate(template,i18n);
+        },
+        /**
+         * on appel la fonction removeField du model galerie pour effacer une collection 
+         * @param {*} event 
+         */
+        onDeleteClick:function (event) {
+            this.model.galerie.removeField(this.model);
+        },
+        render:function () {
+            View.prototype.render.call(this);
+            this.$el.html(this.template({field:this.model}));
+        }
+    });
+    return CollectionView;
+});
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieBlockView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieBlockView.js	(révision 10803)
@@ -0,0 +1,54 @@
+/**
+ * <AUTHOR>
+ * View pour le block galerie
+ */
+define([
+    "jquery",
+    "underscore",
+    "JEditor/Commons/Events",
+    "JEditor/PagePanel/Contents/Blocks/Block/Views/BlockView",
+    "JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/Galerie",
+    "JEditor/PagePanel/Contents/Blocks/GalerieBlock/Models/GalerieField",
+    "text!../Templates/galerieBlock.html",
+    "i18n!../nls/i18n",
+    "less",
+    //not in params
+    "owlCarouselSync",
+], function($, _, Events, BlockView, Galerie, GalerieField, template, translate, less) {
+    var GalerieBlockView = BlockView.extend({
+        attributes: {
+            tabindex: 0,
+            class: "Galerieblock block"
+        },
+        initialize: function() {
+            this._super();
+            this.template = this.buildTemplate(template, translate);
+        },
+        render :function(){
+            this._super();
+            return this;
+        },
+        renderOptions: function(model, options) {
+            var uid, opts, styleOpts, galerie,  htmlContent;
+            var collections = [];
+            uid = _.uniqueId('galerie');
+            opts = this.model.options.GalerieOption;
+            styleOpts = this.model.options.GalerieStyleOption;
+            galerie = opts.get("galerie").collections;
+            galerie.forEach(function (field) {
+                collections.push(field.attributes)
+            });
+           
+            if (collections.length != 0) {
+                htmlContent = this.template({uid: uid,  empty: false, fields:collections});
+            }
+            else {
+                emptyGroup = true;
+                htmlContent = this.template({uid: uid,  empty: true});
+            }
+            this.$('.content').html(htmlContent);
+            return this;
+        }
+    });
+    return GalerieBlockView;
+});
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieFieldView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieFieldView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieFieldView.js	(révision 10803)
@@ -0,0 +1,89 @@
+/**
+ * <AUTHOR>
+ * View pour la controle des collections dans la galerie
+ */
+define([
+    "underscore",
+    "JEditor/Commons/Ancestors/Views/View",
+    "JEditor/Commons/Events",
+    "text!../Templates/galerieField.html",
+    "./GalerieFieldView",
+    "JEditor/Commons/Files/Models/FileGroup",
+    "JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselFileEditionView",
+    "JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/CollectionView",
+    "i18n!../nls/i18n"
+],function (_,View, Events, FieldTemplate, GalerieFieldView, FileGroup, CarrouselFileEditionView, CollectionView, translate) {
+    
+    var GalerieFieldView=View.extend({
+        events: {
+            "sortstop .galerie-column":"onSortStop",
+            "sortstart .galerie-column":"onSortStart",
+            
+        },
+        /**
+         * ici le model correspond au Models/Galerie
+         */
+        initialize: function() {
+            View.prototype.initialize.call(this);
+            this.template = this.buildTemplate(FieldTemplate,translate);
+             this.listenTo(this.model,"remove:fieldgalerie",this.render);
+             this.listenTo(this.model,"add:fieldgalerie",this.render);
+        },
+        /**
+         * on appel le view CarrouselFileEditionView pour selectionner ou ajouter une collection
+         * @param {*} event 
+         * @returns false
+         */
+        onAddClick:function(event){
+            event.preventDefault();
+            fileGroup = new FileGroup();
+            viewAttributes = {title: translate("editMyGrid"), model: fileGroup};
+            fileGroupDialog = new CarrouselFileEditionView(viewAttributes);
+            this.listenTo(fileGroupDialog, Events.ChoiceEvents.SELECT, function(view, selected) {
+                // 
+                this.model.addField( selected );
+            });
+            this.listenTo(fileGroupDialog, Events.DialogEvents.CLOSE, function() {
+                this.stopListening(fileGroupDialog);
+                fileGroupDialog.remove();
+            });
+            fileGroupDialog.open();
+            this.$(".add-field .dropdown-toggle").dropdown("toggle");
+            return false;
+        },
+        render:function () {
+            var that=this;
+            this.$el.html(this.template({
+                galerie:this.model
+            }));
+            // render toute la liste des collections
+            this.model.collections.forEach(function (field) {
+                var view = new CollectionView({model:field});
+                that.$("ul.galerie-column").append(view.el);
+                view.render();
+            });
+            // permetre à ordoner la liste des collections 
+            this.$("ul.galerie-column").sortable({
+                cursor: "move",
+                placeholder:"field-placeholder",
+                update:function(event,ui){
+                    var newCollections = [];
+                    var listeSN = that.model.collections;
+                    $(this).children().each(function(index){
+                        console.log($(this).children(".show-part"));
+                        var id = $(this).children(".show-part").attr('id');
+                        var idx = listeSN.findIndex(function(item) {
+                            return item.id == id;
+                        });
+                        newCollections.push(listeSN[idx]);
+                    });
+                    that.model.collections = newCollections;
+                    that.$("ul.galerie-column").remove();
+                    that.render();
+                }
+            });
+           
+        },
+    });
+    return GalerieFieldView;
+});
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieOptionTypeLienView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieOptionTypeLienView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieOptionTypeLienView.js	(révision 10803)
@@ -0,0 +1,58 @@
+define([
+    "jquery",
+    "text!../Templates/galerieOptionTypeLien.html",
+    "JEditor/Commons/Events",
+    "JEditor/Commons/Ancestors/Views/View",
+    "i18n!../nls/i18n",
+    
+], function($, galerieOptionTypeLien, Events, View, translate) {
+    /**
+     * copie de GridOptionTypeLienView
+     * just les noms des variable qui sont changés
+     */
+    var galerieOptionTypeLienView = View.extend({
+        events: {
+        },
+        initialize: function() {
+            this._super();
+            this.BouttonMoreInfo=false;
+            this.LinkText=false;
+            this._template = this.buildTemplate(galerieOptionTypeLien, translate);
+            if (!this.options.TypeLien)
+                this.options.TypeLien = null;
+
+           
+        },
+        render: function() {
+            this.BouttonMoreInfo=false;
+            this.LinkText=false;
+            var TypeLien,Info,Action;
+            TypeLien = this.options.galerieTypeLink ? this.options.galerieTypeLink : null;
+            Action = (this.options.galerieAction!==null) ? this.options.galerieAction : null;
+            Info = (this.options.galerieInfo!==null) ? this.options.galerieInfo : null;
+            if(Info ==0){
+                this.LinkText=true;
+                if(TypeLien ==2){
+                    TypeLien =1;
+                }
+            }
+            if(Action == 1){
+                this.BouttonMoreInfo =true;
+                if((TypeLien ==3) ||(TypeLien== 2 && Info==0)){
+                    TypeLien =1;
+                }
+            }
+                
+            this.undelegateEvents();
+            this.$el.empty();
+            this.$el.html(this._template({TypeLien: TypeLien,LinkText:this.LinkText,BouttonMoreInfo:this.BouttonMoreInfo}));
+
+            this.delegateEvents();
+            return this;
+        },
+    });
+
+    Events.extend({
+    });
+    return galerieOptionTypeLienView;
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieOptionView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieOptionView.js	(révision 10803)
@@ -0,0 +1,78 @@
+define([
+    "jquery",
+    "underscore",
+    "text!../Templates/galerieOption.html",
+    "JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieFieldView",
+    "JEditor/PagePanel/Contents/Options/Views/AbstractOptionView",
+    "JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieOptionTypeLienView",
+    "JEditor/Commons/Utils",
+    "i18n!../nls/i18n"
+],function($, _, template, GalerieFieldView, AbstractOptionView, GalerieOptionTypeLienView, Utils, translate) {
+    /**
+     * copie de GridOption
+     * just les noms des variable qui sont changés
+     * et fonctionalité ajouter collection 
+     */
+    var GalerieOptionView = AbstractOptionView.extend({
+         
+        optionType: 'galerieOption',
+        events: {
+            "click .add-galerieField .btn":"onAddClick",
+            'click .files-action': '_openFileGroupDialog',
+            'click input[type=radio]':'_onChangeRadio',
+        },
+        className: 'gallerie-option-home panel-content',
+
+        initialize: function() {
+            this._super();
+            this.template = this.buildTemplate(template, translate);
+            this.GalerieOptionTypeLien = new GalerieOptionTypeLienView(this.model);
+            this.galerieField = new GalerieFieldView({model:this.model.galerie});
+        },
+        onAddClick: function(event) {
+            this.galerieField.onAddClick(event);
+        },
+        DeleteNameInputOnMoodel : function(){
+            var Mymodel=this.model.attributes;
+            if(Mymodel.hasOwnProperty("figcaption"))
+                delete Mymodel.figcaption;
+            if(Mymodel.hasOwnProperty("LinkAction"))
+                delete Mymodel.LinkAction;
+            if(Mymodel.hasOwnProperty("LinkType"))
+                delete Mymodel.LinkType;
+          
+            this.model.attributes=Mymodel;
+        },
+        render: function() {
+            this.undelegateEvents();
+            var templateVars = {
+                info:this.model.galerieInfo,
+                action:this.model.galerieAction,
+                typeLink:this.model.galerieTypeLink
+            };
+            this.$el.html(this.template(templateVars));
+            this.galerieField.render();
+            this.$("#galerie-collections").append(this.galerieField.el);
+           
+            this.$("#typeDeLien").append(this.GalerieOptionTypeLien.el);
+            this.GalerieOptionTypeLien.render();
+            this.scrollables();
+            this.delegateEvents();
+            return this;
+        },
+        _onChangeRadio: function(event){
+                var name =event.currentTarget.name;
+                if (name ==="figcaption"){
+                    this.model.galerieInfo=parseInt(event.currentTarget.value);
+                    this.GalerieOptionTypeLien.render();
+                }else if(name === "LinkAction"){
+                    this.model.galerieAction= parseInt(event.currentTarget.value);
+                    this.GalerieOptionTypeLien.render();
+                }else if(name === "LinkType"){
+                    this.model.galerieTypeLink=parseInt(event.currentTarget.value);
+                }
+                this.DeleteNameInputOnMoodel();
+            },
+    });
+    return GalerieOptionView;
+});
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieStyleOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieStyleOptionView.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/GalerieStyleOptionView.js	(révision 10803)
@@ -0,0 +1,84 @@
+define([
+    "jquery",
+    "underscore",
+    "text!../Templates/galerieStyleOption.html",
+    "JEditor/PagePanel/Contents/Options/Views/AbstractOptionView"
+],function($, _, 
+    template,
+    AbstractOptionView
+    ) {
+    /**
+     * copie de GridStyleOption
+     * just les noms des variable qui sont changés
+     */
+    var GalerieStyleOptionView = AbstractOptionView.extend({
+        optionType: 'galerieStyleOption',
+        tagName: "div",
+        className: "gallery-template-option galleryStyle panel-content ",
+        events: {
+            'click .stylleDegalerie div .effect-radio'   : '_onChangeStylleImage',
+            'click #Radioformatimage div .effect-radio'       : '_onChangeFormatImage',
+            'slidechange .slider'   :   '_onSliderChange',
+            'change input[type="radio"].select-box': '_onStyleAffichageChange',
+        },
+        _onChangeStylleImage : function(event){
+            this.$(".effect-radio").removeClass("active");
+            var $target = $(event.currentTarget);
+            $target.addClass("active");
+            var value = parseInt($target.attr("data-value"));
+            if(value===1){
+                $("#formatImage").show();
+                var elgalerieFormat=$("#Radioformatimage").children().find("[data-value="+this.model.galerieFormat+"]");
+                elgalerieFormat.addClass("active");
+            }else{
+                $("#formatImage").hide();
+            }
+            this.model.galerieStyle=value;
+        },
+          _onStyleAffichageChange :function(event){
+            var $target = $(event.currentTarget);
+            this.model.galerieStyleAff = $target.val();
+        },
+        /**
+        * chagement du slider
+        */
+        _onSliderChange: function(event,ui){
+            var value = ui.value;
+            this.model.galerieNbreImage=value;
+            return false;
+         },
+        _onChangeFormatImage : function(event){
+            this.$(".formatImage-radio").removeClass("active");
+            var $target = $(event.currentTarget);
+            $target.addClass("active");
+            var value = $target.attr("data-value");
+            this.model.galerieFormat=value;
+        },
+        initialize: function() {
+            this._super();
+            this.template = this.buildTemplate(template, this.translate);
+            this.listenTo(this.model, 'change:fileGroup', this.onFileGroupChange);
+        },
+        render: function() {
+            this.undelegateEvents();
+            var templateVars = {
+                galerieStyle:this.model.galerieStyle,
+                galerieFormat:this.model.galerieFormat,
+                galerieNbreImage:this.model.galerieNbreImage,
+                galerieStyleAff:this.model.galerieStyleAff
+            };
+            this.$el.html(this.template(templateVars));
+            this.$('.galerie-nbreImage .slider').slider({
+                range: "min",
+                value: this.model.galerieNbreImage,
+                min: 1,
+                max: 5,
+                step: 1
+            });
+            this.scrollables();
+            this.delegateEvents();
+            return this;
+        }
+    });
+    return GalerieStyleOptionView;
+});
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/main.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Views/main.js	(révision 10803)
@@ -0,0 +1,9 @@
+// this file has been auto-generated by a grunt task, it will be overriden, do not modify it
+define(["./GalerieBlockView","./GalerieOptionView","./GalerieStyleOptionView"],function(GalerieBlockView, GalerieOptionView, GalerieStyleOptionView){
+    var comp={
+        "GalerieBlockView":GalerieBlockView,
+        "GalerieOptionView":GalerieOptionView,
+        "GalerieStyleOptionView":GalerieStyleOptionView
+    };
+    return comp;
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/main.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/main.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/main.js	(révision 10803)
@@ -0,0 +1,7 @@
+// this file has been auto-generated by a grunt task, it will be overriden, do not modify it
+define(["./GalerieBlock","./Views/main","./Models/main","i18n!./nls/i18n"],function(GalerieBlock,Views,Models,i18n){
+   GalerieBlock.Models=Models;
+   GalerieBlock.Views=Views;
+   GalerieBlock.i18n=i18n;
+    return GalerieBlock;
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/de-de/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/de-de/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/de-de/i18n.js	(révision 10803)
@@ -0,0 +1 @@
+define({});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/de-de/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/en-au/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/en-au/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/en-au/i18n.js	(révision 10803)
@@ -0,0 +1 @@
+define({});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/en-au/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/en-us/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/en-us/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/en-us/i18n.js	(révision 10803)
@@ -0,0 +1 @@
+define({});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/en-us/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/es-es/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/es-es/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/es-es/i18n.js	(révision 10803)
@@ -0,0 +1 @@
+define({});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/es-es/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json	(révision 10802)
+++ src/js/JEditor/PagePanel/Contents/Blocks/blocks_build.json	(révision 10803)
@@ -16,5 +16,6 @@
  "FormBlock": "FormBlock",
  "LegalsBlock": "LegalsBlock",
  "EvaluationBlock":"EvaluationBlock",
- "CarrouselBlock":"CarrouselBlock"
+ "CarrouselBlock":"CarrouselBlock",
+ "GalerieBlock":"GalerieBlock"
 }
Index: src/js/build.js
===================================================================
--- src/js/build.js	(révision 10802)
+++ src/js/build.js	(révision 10803)
@@ -44,6 +44,7 @@
         "JEditor/PagePanel/Contents/Blocks/Block",
         "JEditor/PagePanel/Contents/Blocks/FormBlock",
         "JEditor/PagePanel/Contents/Blocks/CarrouselBlock",
+        "JEditor/PagePanel/Contents/Blocks/GalerieBlock",
         "JEditor/PagePanel/Contents/Blocks/HtmlBlock",
         "JEditor/PagePanel/Contents/Blocks/ImageBlock",
         "JEditor/PagePanel/Contents/Blocks/LegalsBlock",
Index: src/js/main.js
===================================================================
--- src/js/main.js	(révision 10802)
+++ src/js/main.js	(révision 10803)
@@ -49,6 +49,7 @@
     "JEditor/PagePanel/Contents/Blocks/Block",
     "JEditor/PagePanel/Contents/Blocks/FormBlock",
     "JEditor/PagePanel/Contents/Blocks/CarrouselBlock",
+    "JEditor/PagePanel/Contents/Blocks/GalerieBlock",
     "JEditor/PagePanel/Contents/Blocks/HtmlBlock",
     "JEditor/PagePanel/Contents/Blocks/ImageBlock",
     "JEditor/PagePanel/Contents/Blocks/LegalsBlock",
Index: src/less/imports/gallery_style.less
===================================================================
--- src/less/imports/gallery_style.less	(révision 10802)
+++ src/less/imports/gallery_style.less	(révision 10803)
@@ -53,7 +53,7 @@
     border: 1px solid #ffffff;
     .border-radius(4px);
 
-    .icon-gallery, .icon-photogrid-block {
+    .icon-gallery, .icon-gallery-arrow-outer, .icon-photogrid-block {
         top: 50%;
         left: 50%;
         .translate(-50%, -50%);
@@ -76,7 +76,7 @@
         color: #747474;   
         font-size: 2.2em;
         font-family: Raleway,sans-serif;
-        .icon-gallery, .icon-photogrid-block {
+        .icon-gallery, .icon-gallery-arrow-outer, .icon-photogrid-block {
         
             
            
Index: src/less/imports/page_panel/module/block-options/btn-gallery-content.less
===================================================================
--- src/less/imports/page_panel/module/block-options/btn-gallery-content.less	(révision 10802)
+++ src/less/imports/page_panel/module/block-options/btn-gallery-content.less	(révision 10803)
@@ -60,4 +60,70 @@
 			color: @greyM;
 		}
 	}
+	
+}
+.btn-galerie-content {
+	background-color: #0f0f0f;
+	.add-galerieField {
+		margin-bottom: 10px;
+		a {
+			width: 88%;
+		}
+	}
+	ul.galerie-column {
+		list-style-type: none;
+		padding: 0;
+		margin: 5px;
+		min-height: 100px;
+		-webkit-border-radius: 4px;
+		-moz-border-radius: 4px;
+		border-radius: 4px;
+		.field-placeholder {
+			border-radius: 5px;
+			background: #0f0f0f;
+			height: 5px;
+			border: 1px dashed #1a1a1a;
+		}
+		.galerie-element {
+			margin: 5px;
+			background: #474747;
+			height: 30px;
+			.show-part {
+				position: relative;
+				padding: 5px;
+				* {
+					display: inline-block;
+				}
+				.delete {
+					width: 22px;
+					height: 22px;
+					font-size: 22px;
+					text-align: center;
+					position: absolute;
+					line-height: 22px;
+					cursor: pointer;
+				}
+				.collection-nb {
+					right: 22px;
+					position: absolute;
+					font-size: 10px;
+				}
+				.icon-grip {
+					left: 0;
+					opacity: 0.3;
+					cursor: move;
+				}
+				.field-name {
+					line-height: 22px;
+					position: absolute;
+					top: 5px;
+					right: 32px;
+					left: 30px;
+					bottom: 5px;
+					font-size: 14px;
+					text-overflow: ellipsis;
+				}
+			}
+		}
+	}
 }
\ No newline at end of file
Index: src/less/imports/page_panel/module/block-options/show-part.less
===================================================================
--- src/less/imports/page_panel/module/block-options/show-part.less	(révision 10802)
+++ src/less/imports/page_panel/module/block-options/show-part.less	(révision 10803)
@@ -1,6 +1,6 @@
 .panel-content {
 
-	.form-element {
+	.form-element, .galerie-element {
 		height: auto !important;
 		margin: 5px 0 !important;
 		line-height: 1.6em;
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/Templates/galerieStyleOption.html	(révision 10803)
@@ -0,0 +1,82 @@
+<div class="panel-option-container animated  mr15">
+    <article class="panel-option">
+        <header>
+            <h3 class="option-name"></span> <%=__("styleDegalerie")%></h3>
+            <p class="panel-content-legend"><%= __("styleDegalerieLegend")%></p>
+        </header>
+        <div>
+            <div class="category-content radio-transformed stylleDegalerie">
+                <% var _id= _.uniqueId('galerieStyle'); %>
+                <div><span class="effect-radio <%=(galerieStyle===0)?'active':''%>" id="<%=_id %>" data-value="0" data-helper="masonry"><span class="helper"><span class="help"><%=__("masonryLegend")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-photocss-column"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>
+                <% var _id= _.uniqueId('galerieStyle'); %>
+                <div><span class="effect-radio <%=(galerieStyle===1)?'active':''%>" id="<%=_id %>" data-value="1" data-helper="galerie"><span class="helper"><span class="help"><%=__("galerieLegend")%></span><span class="bottom"></span></span><span class="container"><span class="icon icon-photogalerie-block"></span><span class="switch-container"><span class="radio"><span></span></span></span></span></span></div>                
+            </div>
+        </div>
+    </article>
+</div>
+
+    
+<div class="mr15 galerie-option-margin galerie-nbreImage">
+    <article class="panel-option">
+        <header>
+            <h3 class="option-name"><%=__("galerieNombreImage")%></h3>
+            <p class="panel-content-legend"><%= __("galerieNombreImageLegend")%></p>
+        </header>
+        <div class="option-content">
+            <div class="slider-container"><span class="icon-less"></span><div class="slider"></div><span class="icon-more"></span></div>
+        </div>
+    </article>
+</div>
+<div class="panel-option-container animated">
+    <article class="panel-option background-color">
+        <header>
+            <h3 class="option-name"><%=__("galerieStyleAffichage")%></h3>
+            <p class="panel-content-legend"><%=__("galerieStyleAffichageDesc")%></p>
+        </header>
+        <div class="option-content colors">
+            <%  var _id=_.uniqueId('galerieStyleAffichage');
+            %>
+                <input type="radio" class="select-box" name="AffichageCarrouselStyle" value="1" id="<%=_id %>" <%=(galerieStyleAff==1)?'checked':''%>>
+                   <label for="<%=_id %>">
+                    <div class="wrapper">
+                        <div class="vertical-wrap">
+                            <span class="icon-wrapper">
+                                <span class="icon-collection-style1"></span>
+                            </span>
+                            <span class="name"><%= __("Style")%> 1</span>
+                            <span class="desc"><%= __("DescStyle1")%></span>
+                        </div>
+                    </div>
+                </label>
+                
+                <%  var _id=_.uniqueId('galerieStyleAffichage');%>
+                <input type="radio" class="select-box" name="AffichageCarrouselStyle" value="2" id="<%=_id %>" <%=(galerieStyleAff==2)?'checked':''%>>
+                   <label for="<%=_id %>">
+                    <div class="wrapper">
+                        <div class="vertical-wrap">
+                            <span class="icon-wrapper">
+                                <span class="icon-collection-style2"></span>
+                            </span>
+                            <span class="name"><%= __("Style")%> 2</span>
+                            <span class="desc"><%= __("DescStyle2")%></span>
+                        </div>
+                    </div>
+                </label>
+                
+                <%  var _id=_.uniqueId('galerieStyleAffichage');%>
+                <input type="radio" class="select-box"  name="AffichageCarrouselStyle" value="3" id="<%=_id %>" <%=(galerieStyleAff==3)?'checked':''%>>
+                   <label for="<%=_id %>">
+                    <div class="wrapper">
+                        <div class="vertical-wrap">
+                            <span class="icon-wrapper">
+                                <span class="icon-collection-style3"></span>
+                            </span>
+                            <span class="name"><%= __("Style")%> 3</span>
+                            <span class="desc"><%= __("DescStyle3")%></span>
+                        </div>
+                    </div>
+                </label>
+                
+        </div>
+    </article>
+</div>
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-ca/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-ca/i18n.js	(révision 10803)
@@ -0,0 +1,53 @@
+define({  
+   "GalerieOption":"Galerie",
+   "galerieBlockOption":"Options de la galerie",
+   "GalerieStyleOption":"Style",
+   "galerieContent":" Créer une galerie",
+   "galerieContentLegend":"Créez et personnalisez ici votre galerie d'images.",
+   "emptyGallery":"Votre grille est vide.",
+   "no-emptyGallery":"Votre grille contient des images.",
+   "clickToAddImages":"Cliquez ici pour ajouter des images.",
+   "zoomOnClic":"Zoomer au clic sur les photos.",
+   "photoTitle":"Titres sur les photos",
+   "photoTitleLegend":"Gérez l'affichage des titres sur vos images.",
+   "permanentTitle":"Afficher les titres en permanence",
+   "onMouseOverTitle":"Afficher les titres au survol des photos",
+   "noTitle":"Ne pas afficher les titres",
+   "photoSize":"Taille des photos",
+   "photoSizeLegend":"Gérez la hauteur des lignes de votre grille photo ici.",
+   "justifyLastRow":"Forcez à justifier la dernière ligne",
+   "justifyLastRowLegend":"Par exemple, s'il ne reste qu'une seule image en dernière ligne de la grille, celle-ci occupera toute la largeur disponible, quitte à être démesurée par rapport aux autres images.",
+   "imageWarning": "Attention :",
+   "imageWarningMsg": "les images trouvées sur internet ne sont généralement pas libres d’utilisation. Pour vous aider à déterminer si une image est protégée par des droits d’auteur vous pouvez utiliser le service",
+   "figcaptionImage"       :   "Afficher les informations des images",
+   "figShowTitle"          :   "Afficher le titre",
+   "figShowAll"            :   "Afficher le titre et la description",
+   "doNothing"             :   "Ne rien afficher",
+   "selectClickActionImage":   "Sélectionnez l'action souhaitée au clic sur l’image.",
+   "ActionZoom"            :   "Zoomer sur l'image",
+   "ActionPartner"         :   "Ouvrir le lien associé à l'image",
+   "galerieStyleContent" :   "Style de grille",
+   "galerieStyleLegend" :   "Appliquez un style de grille",
+   "masonryLegend"         :   "Tuiles",
+   "galerieLegend"            :   "Grille",
+   "galerieNombreImage"       :   "Nombre d'image",
+   "galerieNombreImageLegend" :   "Glissez pour ajuster le nombre d'images affichées",
+   "galerieStyleAffichage"    :   "Style des images",
+   "galerieStyleAffichageDesc":   "Appliquez un style aux images",
+   "styleDeGrille"         :   "Style de grille",
+   "styleDeGrilleLegend"   :   "Appliquez un style de grille",
+   "FormatImage"           :   "Format de l'image",
+   "FormatImageLegend"     :   "Appliquez un format d'image au carousel",
+   "selectTypeLink"        :   "Selectionnez le type de lien souhaitée",
+   "LinkImage"             :   "Ajouter le lien sur l'image",
+   "LinkText"              :   "Ajouter le lien sur le texte",
+   "ButtonReadMore"        :   "Ajouter un bouton 'en savoir plus'",
+   'landscape'             :   "Paysage",
+   'portrait'              :   "Portrait",
+   'square'                :   "Carré",
+   "editMyGrid"            :   "Modifier ma grille ",
+   "DescStyle1"           :  "Textes sous l'image", 
+   "DescStyle2"           :  "Texte encadré sur l'image",
+   "DescStyle3"           :  "Texte sur l'image" ,
+   "addGalerieField": "ajouter une collection", 
+});
\ No newline at end of file
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js	(révision 10803)
@@ -0,0 +1,53 @@
+define({  
+   "GalerieOption":"Galerie",
+   "galerieBlockOption":"Options de la galerie",
+   "GalerieStyleOption":"Style",
+   "galerieContent":" Créer une galerie",
+   "galerieContentLegend":"Créez et personnalisez ici votre galerie d'images.",
+   "emptyGallery":"Votre grille est vide.",
+   "no-emptyGallery":"Votre grille contient des images.",
+   "clickToAddImages":"Cliquez ici pour ajouter des images.",
+   "zoomOnClic":"Zoomer au clic sur les photos.",
+   "photoTitle":"Titres sur les photos",
+   "photoTitleLegend":"Gérez l'affichage des titres sur vos images.",
+   "permanentTitle":"Afficher les titres en permanence",
+   "onMouseOverTitle":"Afficher les titres au survol des photos",
+   "noTitle":"Ne pas afficher les titres",
+   "photoSize":"Taille des photos",
+   "photoSizeLegend":"Gérez la hauteur des lignes de votre grille photo ici.",
+   "justifyLastRow":"Forcez à justifier la dernière ligne",
+   "justifyLastRowLegend":"Par exemple, s'il ne reste qu'une seule image en dernière ligne de la grille, celle-ci occupera toute la largeur disponible, quitte à être démesurée par rapport aux autres images.",
+   "imageWarning": "Attention :",
+   "imageWarningMsg": "les images trouvées sur internet ne sont généralement pas libres d’utilisation. Pour vous aider à déterminer si une image est protégée par des droits d’auteur vous pouvez utiliser le service",
+   "figcaptionImage"       :   "Afficher les informations des images",
+   "figShowTitle"          :   "Afficher le titre",
+   "figShowAll"            :   "Afficher le titre et la description",
+   "doNothing"             :   "Ne rien afficher",
+   "selectClickActionImage":   "Sélectionnez l'action souhaitée au clic sur l’image.",
+   "ActionZoom"            :   "Zoomer sur l'image",
+   "ActionPartner"         :   "Ouvrir le lien associé à l'image",
+   "galerieStyleContent" :   "Style de grille",
+   "galerieStyleLegend" :   "Appliquez un style de grille",
+   "masonryLegend"         :   "Tuiles",
+   "galerieLegend"            :   "Grille",
+   "galerieNombreImage"       :   "Nombre d'image",
+   "galerieNombreImageLegend" :   "Glissez pour ajuster le nombre d'images affichées",
+   "galerieStyleAffichage"    :   "Style des images",
+   "galerieStyleAffichageDesc":   "Appliquez un style aux images",
+   "styleDegalerie"         :   "Style de grille",
+   "styleDegalerieLegend"   :   "Appliquez un style de grille",
+   "FormatImage"           :   "Format de l'image",
+   "FormatImageLegend"     :   "Appliquez un format d'image au carousel",
+   "selectTypeLink"        :   "Selectionnez le type de lien souhaitée",
+   "LinkImage"             :   "Ajouter le lien sur l'image",
+   "LinkText"              :   "Ajouter le lien sur le texte",
+   "ButtonReadMore"        :   "Ajouter un bouton 'en savoir plus'",
+   'landscape'             :   "Paysage",
+   'portrait'              :   "Portrait",
+   'square'                :   "Carré",
+   "editMyGrid"            :   "Modifier ma grille ",
+   "DescStyle1"           :  "Textes sous l'image", 
+   "DescStyle2"           :  "Texte encadré sur l'image",
+   "DescStyle3"           :  "Texte sur l'image",
+   "addGalerieField": "ajouter une collection", 
+});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/fr-fr/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
Index: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/i18n.js	(nonexistent)
+++ src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/i18n.js	(révision 10803)
@@ -0,0 +1,65 @@
+define({  
+   "root":{  
+      "BLOCK_NAME":"Galerie",
+      "GalerieOption":"Gallery",
+      "galerieBlockOption":"Grid options",
+      "GalerieStyleOption":"Style",
+      "galerieContent":"Create a gallery",
+      "galerieContentLegend":"Create and customize your image gallery here.",
+      "emptyGallery":"Your grid is empty",
+      "no-emptyGallery":"Your grid contains pictures",
+      "clickToAddImages":"Click here to add pictures",
+      "zoomOnClic":"Zoom on click",
+      "photoTitle":"Captions",
+      "photoTitleLegend":"Manage captions display on your pictures.",
+      "permanentTitle":"Always show captions",
+      "onMouseOverTitle":"Show captions on mouse over",
+      "noTitle":"Never show captions",
+      "photoSize":"Pictures size",
+      "photoSizeLegend":"Manage row height of your grid.",
+      "justifyLastRow":"Force to justify last row",
+      "justifyLastRowLegend":"For example, if there is only one picture in the last row, it will fill the available space, even if it becomes disproportionate.",
+      "gridMargin":"Margin",
+      "gridMarginLegend":"Set spacing between pictures in the grid.",
+      "imageWarning": "Warning:",
+      "imageWarningMsg": "pictures found on the internet are usually not free to use. To help you identify if a picture is protected by copyright you can use",
+      "Style"                   :     "Style" ,
+      "figcaptionImage"       :   "Display image information",
+      "figShowTitle"          :   "Display title",
+      "figShowAll"            :   "Display the title and description",
+      "doNothing"             :   "Do not display anything",
+      "selectClickActionImage":   "Select the desired action by clicking on the image.",
+      "ActionZoom"            :   "Zoom in on the image",
+      "ActionPartner"         :   "Open the link associated with the image",
+      "galerieStyleContent"      :   "Grid style",
+      "galerieStyleLegend"       :   "Apply a grid style",
+      "masonryLegend"         :   "Masonry",
+      "galerieLegend"            :   "Grid",
+      "galerieNombreImage"       :   "Number of images",
+      "galerieNombreImageLegend" :   "Drag to adjust the number of images displayed",
+      "galerieStyleAffichage"    :   "Style of the images",
+      "galerieStyleAffichageDesc":   "Apply a style to the images",
+      "styleDeGrille"         :   "Grid style",
+      "styleDeGrilleLegend"   :   "Apply a grid style",
+      "FormatImage"           :   "Image format",
+      "FormatImageLegend"     :   "Apply an image format to the grid",
+      "selectTypeLink"        :   "Select the type of link you want",
+      "LinkImage"             :   "Add link to image",
+      "LinkText"              :   "Add link to text",
+      "ButtonReadMore"        :   "Add a button 'read more'",
+      "galerieOption"            :       "Grid",
+      "galerieBlockOption"       :       "Grid options",
+      "galerieStyleOption"       :       "Style",
+      "galerieContent"           :       "Content of the grid",
+      'landscape'             :   "Landscape",
+      'portrait'              :   "Portrait",
+      'square'                :   "Square",
+      "editMyGrid"            :   "Edit my Grid",
+      "DescStyle1"           :  "Text under the image", 
+      "DescStyle2"           :  "Text framed on the image",
+      "DescStyle3"           :  "Text on the image",
+      "addGalerieField": "add collection",  
+   },
+   "fr-fr":true,
+   "fr-ca":true
+});
\ No newline at end of file

Property changes on: src/js/JEditor/PagePanel/Contents/Blocks/GalerieBlock/nls/i18n.js
___________________________________________________________________
Added: svn:executable
## -0,0 +1 ##
+*
\ No newline at end of property
