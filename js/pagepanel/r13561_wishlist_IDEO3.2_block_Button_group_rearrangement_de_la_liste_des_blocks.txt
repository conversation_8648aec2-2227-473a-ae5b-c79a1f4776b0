Revision: r13561
Date: 2024-12-03 16:43:15 +0300 (tlt 03 Des 2024) 
Author: s<PERSON><PERSON><PERSON><PERSON><PERSON> 

## Commit message
wishlist IDEO3.2: block Button group ,rearrangement de la liste des blocks

## Files changed

## Full metadata
------------------------------------------------------------------------
r13561 | srazanandralisoa | 2024-12-03 16:43:15 +0300 (tlt 03 Des 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/NewsPanel/Views/AvailableView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Templates/buttonGroupBlock.html

wishlist IDEO3.2: block Button group ,rearrangement de la liste des blocks
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/NewsPanel/Views/AvailableView.js
===================================================================
--- src/js/JEditor/NewsPanel/Views/AvailableView.js	(révision 13560)
+++ src/js/JEditor/NewsPanel/Views/AvailableView.js	(révision 13561)
@@ -41,7 +41,7 @@
                             {
                                 events: {'click .block.available': 'addToCurrentContainer', 'dragstart .bloc.available': '_ondragStart'},
                                 blockOrder: {
-                                    'standard': ["TextBlock", "ImageBlock", "ButtonBlock", "SeparatorBlock", "TableBlock", "CardBlock",,"ButtonGroupBlock"],
+                                    'standard': ["TextBlock", "ImageBlock", "SeparatorBlock","ButtonBlock", "ButtonGroupBlock", "TableBlock", "CardBlock"],
                                     'advanced': ["CarrouselBlock", "GridBlock","GalerieBlock","CompareBlock","SlideshowBlock", "LoopBlock", "FormBlock", /*"VideoLinkeoBlock",*/ "MapBlock", "VideoBlock", "HtmlBlock"],
                                     'others': ["LegalsBlock","SiteMapBlock","SocialNetworkBlock"],
                                     'ignored':['TwitterTimelineBlock']
Index: src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js	(révision 13560)
+++ src/js/JEditor/PagePanel/Contents/Blocks/Block/Views/AvailableView.js	(révision 13561)
@@ -42,7 +42,7 @@
                             {
                                 events: {'click .block.available': 'addToCurrentContainer', 'dragstart .bloc.available': '_ondragStart'},
                                 blockOrder: {
-                                    'standard': ["TextBlock", "ImageBlock", "ButtonBlock", "SeparatorBlock", "TableBlock", "CardBlock","ButtonGroupBlock"],
+                                    'standard': ["TextBlock", "ImageBlock", "SeparatorBlock","ButtonBlock", "ButtonGroupBlock", "TableBlock", "CardBlock"],
                                     'advanced': ["CarrouselBlock", "GridBlock","GalerieBlock","CompareBlock","SlideshowBlock", "LoopBlock", "FormBlock", /*"VideoLinkeoBlock",*/ "MapBlock", "VideoBlock", "HtmlBlock"],
                                     'others': ["LegalsBlock","SiteMapBlock","SocialNetworkBlock"],
                                     'ignored':['TwitterTimelineBlock']
Index: src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Templates/buttonGroupBlock.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Templates/buttonGroupBlock.html	(révision 13560)
+++ src/js/JEditor/PagePanel/Contents/Blocks/ButtonGroupBlock/Templates/buttonGroupBlock.html	(révision 13561)
@@ -4,7 +4,7 @@
     <div class="block-button blk-button">
     <span class="button add-button">
       <span class="plus-btn" >+</span>
-      <span class="txt blk-button__label"><span><%=__addButton%></span></span>
+      <span class="txt blk-button__label"><span><%=__("addButton")%></span></span>
     </span>
   </div>
 </div>
\ No newline at end of file
