Revision: r11425
Date: 2023-10-16 11:00:59 +0300 (lts 16 Okt 2023) 
Author: rrakotoarinelina 

## Commit message
Options de style block newsletter

## Files changed

## Full metadata
------------------------------------------------------------------------
r11425 | rrakotoarinelina | 2023-10-16 11:00:59 +0300 (lts 16 Okt 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/NewsletterBlock.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Views/NewsletterBlockView.js

Options de style block newsletter
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/NewsletterBlock.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/NewsletterBlock.js	(révision 11424)
+++ src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/NewsletterBlock.js	(révision 11425)
@@ -1,7 +1,9 @@
 define([
-    "JEditor/PagePanel/Contents/Blocks/Block/Block"
+    "JEditor/PagePanel/Contents/Blocks/Block/Block",
+    "JEditor/PagePanel/Contents/Options/Models/StylesOption",
 ], function (
-        Block
+        Block,
+        StylesOption
         ) {
     /**
      * @class NewsletterBlock
@@ -18,6 +20,12 @@
                          */
                         initialize: function () {
                             this._super();
+                            if (this.options.colors)
+                                this.options.remove(this.options.colors);
+                            if (this.options.effects)
+                                this.options.remove(this.options.effects);
+                            if(!this.options.styles)
+                                this.options.add(new StylesOption()); 
                             
                         }
                     });
Index: src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Views/NewsletterBlockView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Views/NewsletterBlockView.js	(révision 11424)
+++ src/js/JEditor/PagePanel/Contents/Blocks/NewsletterBlock/Views/NewsletterBlockView.js	(révision 11425)
@@ -6,10 +6,15 @@
     "JEditor/PagePanel/Contents/Ancestors/ContentView",
     "JEditor/PagePanel/Contents/Blocks/Block/Views/BlockView",
     "JEditor/PagePanel/Contents/Blocks/Block/Block",
+    "JEditor/Commons/Ancestors/Views/SaveCancelPanel",
+    "JEditor/PagePanel/Contents/Options/Views/AdvancedOptionView",
+    "JEditor/PagePanel/Contents/Options/Views/StylesOptionView",
+    "JEditor/PagePanel/Contents/Options/Models/StylesOption",
+    "JEditor/PagePanel/Contents/Options/Models/AdvancedOption",
     "JEditor/PagePanel/Contents/Zones/Models/ZoneDependency",
     "ckeditor",
     "i18n!../nls/i18n"
-], function ($, _, template, Events, ContentView, BlockView, Block, ZoneDependency, CKEDITOR, translate) {
+], function ($, _, template, Events, ContentView, BlockView, Block,SaveCancelPanel,AdvancedOptionView,StylesOptionView,StylesOption,AdvancedOption, ZoneDependency, CKEDITOR, translate) {
     
 
     var i18nNewsletterBlock = {
@@ -76,6 +81,12 @@
                             }));
                             return this;
                         },
+                        attributes :function(){
+                            return {
+                                "class": "block form-block",
+                                tabindex: 1
+                            }
+                        },
                         renderOptions: function (model, options) {
                             return this;
                         }
