Revision: r12113
Date: 2024-03-13 09:22:29 +0300 (lrb 13 Mar 2024) 
Author: sraz<PERSON><PERSON><PERSON><PERSON> 

## Commit message
Wishlist IDEO3.2: Block <PERSON> , nouvelle navigation (partie JS)

## Files changed

## Full metadata
------------------------------------------------------------------------
r12113 | srazanandralisoa | 2024-03-13 09:22:29 +0300 (lrb 13 Mar 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/CarrouselOption.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselOption.html
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js

Wishlist IDEO3.2: Block Carroussel , nouvelle navigation (partie JS)
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/CarrouselOption.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/CarrouselOption.js	(révision 12112)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Models/CarrouselOption.js	(révision 12113)
@@ -25,10 +25,12 @@
                             optionType: 'carrousel', 
                             title: 'carrousel', 
                             fileGroup: null, 
-                            Arrow       :   false, 
+                            Arrow       :   0, 
                             Info        :   0,
                             Action      :   0,
                             TypeLien    :   1,
+                            Autoplay : false,
+                            Duration: 5000,
                         },
                         initialize: function() {
                             this._super();
@@ -58,6 +60,8 @@
                             var info = this.getInfo();
                             var action =this.getAction();
                             var typelien =this.getTypeLien();
+                            var autoplay =this.getAutoplay();
+                            var duration =this.getDuration();
                             return {
                                 fileGroup: fileGroup?fileGroup.id:null,
                                 Arrow: arrow,
@@ -64,6 +68,8 @@
                                 Info : info,
                                 TypeLien:typelien,
                                 Action: action,
+                                Duration : duration,
+                                Autoplay : autoplay,
                                 optionType: 'carrousel'
                             }
                         },
@@ -72,7 +78,7 @@
                         }
                     }
             );
-            CarrouselOption.SetAttributes(['fileGroup', 'Arrow','Info','Action','TypeLien']);
+            CarrouselOption.SetAttributes(['fileGroup', 'Arrow','Info','Action','TypeLien', 'Autoplay', 'Duration']);
 
             return CarrouselOption;
         });
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselOption.html
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselOption.html	(révision 12112)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Templates/carrouselOption.html	(révision 12113)
@@ -26,11 +26,51 @@
         </div>
         <div class="panel-option gallery-color">
           <p class="panel-legend"><%=__("arrowImage")%></p>
+          <% var _id=_.uniqueId('arrow') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="Arrow" value="1" <%=(Arrow==1)?"checked":""%>/>
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><%= __("ShowArrow1")%></div>
+
+            </label>
+            <% var _id=_.uniqueId('arrow') %>
+            <input id="<%= _id %>" class="field-input  for--block-label" type="radio" name="Arrow" value="2" <%=(Arrow==2)?"checked":""%>/>
+            <label  class="block-label" for="<%= _id %>">
+              <div class="radio-wrapper">
+                <span class="icon  icon-radio-active"></span>
+                <span class="icon  icon-radio-inactive"></span>
+              </div>
+              <div class="block-label-radio"><%= __("ShowArrow2")%></div>
+            </label>
+        </div>
+        </div>
+        <div class="defilement">
+          <p class="panel-legend"><%=__("autoPlay")%></p>
           <div class="option-content">
-              <% var _id=_.uniqueId('arrow') %>
-              <input type="checkbox" class="blue-bg" name="showTitle" id="<%=_id %>" <%=Arrow?'checked="checked"':''%>><label for="<%=_id %>"><span class="checkbox-wrapper"><span class="icon-unchecked"></span><span class="icon-checked"></span></span><span class="text"><%=__("ShowArrow")%></span></label>
-          </div>
+            <% var _id=_.uniqueId('autoplay') %>
+            <input type="checkbox" class="blue-bg" name="Autoplay" id="<%=_id %>" <%=Autoplay?'checked="checked"':''%>>
+            <label for="<%=_id %>">
+              <span class="checkbox-wrapper">
+                <span class="icon-unchecked"></span>
+                <span class="icon-checked"></span>
+              </span>
+              <span class="text"><%=__("activeAutoPlay")%></span>
+            </label>
         </div>
+        </div>
+        <div class="defilement-cran">
+          <p class="panel-legend"><%=__("duration")%></p>
+          <div class="option-content">
+            <div class="slider-container">
+              <span class="icon-less"></span>
+              <div class="slider"></div>
+              <span class="icon-more"></span>
+            </div>
+        </div>
+        </div>
 
         <div class="figcaption-img" id="figcaptionInfo">
             <p class="panel-legend"><%=__("figcaptionImage")%></p>
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js	(révision 12112)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/Views/CarrouselOptionView.js	(révision 12113)
@@ -24,7 +24,8 @@
             events: {
                 'click .files-action'   :   'openFileGroupDialog',
                 'click input[type=radio]':'_onChangeRadio',
-                'change input[type="checkbox"].blue-bg': '_onChangeArrow'
+                'change input[type="checkbox"].blue-bg': '_onChangeAutoplay',
+                'slidechange .slider': 'onSliderChange',
                 },
             className: 'carrousel-option-home panel-content',
             initialize: function() {
@@ -82,7 +83,19 @@
                 this.$el.html(this._template(this.model));
                 this.$("#typeDeLien").append(this.CarrouselOptionTypeLien.el);
                 this.CarrouselOptionTypeLien.render();
-
+                this.dom[this.cid].autoplay = this.$('.defilement');
+                this.dom[this.cid].cran = this.$('.defilement-cran');
+                this._showHide();
+                var slider = this.$('.defilement-cran .slider');
+                slider.slider({
+                    min: 3000,
+                    max: 6000,
+                    step: 1000,
+                    value:this.model.Duration,
+                    range:"min"
+                });
+                this.dom[this.cid].slider = slider;
+               
                 this.scrollables({
                     advanced:{ autoScrollOnFocus: false }
                 });
@@ -89,9 +102,25 @@
                 this.delegateEvents();
                 return this;
             },
-            _onChangeArrow: function(event){
-                this.model.Arrow=!this.model.Arrow;
+            _onChangeAutoplay: function(event){
+                this.model.Autoplay = !this.model.Autoplay;
+                this._showHide();       
             },
+             onSliderChange: function(e,ui) {
+                var value = ui.value;
+                this.model.Duration=value;
+                return false;
+            },
+            _showHide :function(){
+                this.dom[this.cid].autoplay.hide();
+                this.dom[this.cid].cran.hide();
+                if (this.model.Arrow == 2 ) {
+                    this.dom[this.cid].autoplay.show();
+                    if (this.model.Autoplay) {
+                        this.dom[this.cid].cran.show();
+                    }
+                }
+            },
             _onChangeRadio: function(event){
                 var name =event.currentTarget.name;
                 if (name ==="figcaption"){
@@ -102,6 +131,14 @@
                     this.CarrouselOptionTypeLien.render();
                 }else if(name === "LinkType"){
                     this.model.TypeLien= event.currentTarget.value;
+                }else if(name === "Arrow"){
+                    var value = event.currentTarget.value;
+                    if (this.model.Arrow == value ) {
+                        this.model.Arrow = 0;
+                        event.currentTarget.removeAttribute("checked");
+                    }
+                    else this.model.Arrow = value ;
+                    this._showHide();   
                 }
             },
             ChangeCarrouselTypeLien :function(TypeLien){
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js	(révision 12112)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/fr-fr/i18n.js	(révision 12113)
@@ -45,8 +45,12 @@
     'landscape'             :   "Paysage",
     'portrait'              :   "Portrait",
     'square'                :   "Carré",
-    "arrowImage"            :   "Afficher des flèches de navigation",
-    "ShowArrow"             :   "Afficher les boutons de navigation",
+    "arrowImage"            :   "Choix navigation",
+    "ShowArrow1"            :   "Flèches de navigation classique",
+    "ShowArrow2"            :   "Navigation avancée avec indicateurs *actif*",
+    "autoPlay"              :  "Défilement automatique",
+    "activeAutoPlay"        :  "Activer le défilement automatique",
+    "duration"              :  "Glissez pour ajuster le delais",
     "emptyCollection"       :   "Votre collection d’images est vide.",
     "DescStyle1"           :  "Texte sous l'image", 
     "DescStyle2"           :  "Texte encadré sur l'image",
Index: src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js
===================================================================
--- src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js	(révision 12112)
+++ src/js/JEditor/PagePanel/Contents/Blocks/CarrouselBlock/nls/i18n.js	(révision 12113)
@@ -49,8 +49,12 @@
        'landscape'             :   "Landscape",
        'portrait'              :   "Portrait",
        'square'                :   "Square",
-       "arrowImage"            :   "Display navigation arrows",
-       "ShowArrow"             :   "Show navigation buttons",
+       "arrowImage"            :   "Navigation choices",
+       "ShowArrow1"            :   "Classic navigation arrows",
+       "ShowArrow2"            :   "Advanced navigation with *active* indicators",
+       "autoPlay"              :  "Automatic scrolling",
+       "activeAutoPlay"        :  "Activate automatic scrolling",
+       "duration"              :  "Slide to adjust delay",
        "emptyCollection"      :  "Your image collection is empty",
        "DescStyle1"           :  "Text under the image", 
        "DescStyle2"           :  "Text framed on the image",
