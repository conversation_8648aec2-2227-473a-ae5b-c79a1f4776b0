Revision: r13817
Date: 2025-02-11 09:41:52 +0300 (tlt 11 Feb 2025) 
Author: frahajanirina 

## Commit message
Wishlist IDEO3.2: Revoir panel fichiers

## Files changed

## Full metadata
------------------------------------------------------------------------
r13817 | frahajanirina | 2025-02-11 09:41:52 +0300 (tlt 11 Feb 2025) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Models/FileCollection.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileList.html
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileListView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/filePanel.less

Wishlist IDEO3.2: Revoir panel fichiers
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/FilePanel/Models/FileCollection.js
===================================================================
--- src/js/JEditor/FilePanel/Models/FileCollection.js	(révision 13816)
+++ src/js/JEditor/FilePanel/Models/FileCollection.js	(révision 13817)
@@ -5,11 +5,13 @@
             type: null,
             rowcount: 100,
             offset: 0,
-            id :null
+            id :null,
+            name: null,
         },
         hasmore:true,
         countFile : 0,
         model:File,
+        searchQuery: null,
         // cette attribut pour verifier si on esat dans un detail de'un fichier
         detail:false,
         initialize: function (detail) {
@@ -97,6 +99,9 @@
         },
         setId:function(id){
             this.options.id=id;
+        },
+        setName: function (name) {
+            this.options.name = name;
         }
     });
     return FileCollection;
Index: src/js/JEditor/FilePanel/Templates/fileList.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileList.html	(révision 13816)
+++ src/js/JEditor/FilePanel/Templates/fileList.html	(révision 13817)
@@ -28,6 +28,13 @@
 
 <div class="warning-msg"><span class="icon-warning" style="color: #d42525;"></span> <span style="color: #d42525;"><%= __("imageWarning")%></span> <%= __("imageWarningMsg")%> <a href="https://www.tineye.com/" target="_blank">TinEye</a>.</div>
 </div>
+<div class="container-search">
+    <span> 
+        <i class="icon icon-find"></i>
+        <input type="text" placeholder="<%=__('searchFile')%>" class="file-input">
+        <button type="button" class="clear-btn hidden">&times;</button>
+    </span>
+</div>
 <div class="my-files fileList">
     <div class="content scroll-container">
             <% for(var i=0; i< content.length; i++){
@@ -34,7 +41,7 @@
             var file = content[i];
             %>
             <%if(!file.isLogo){%>
-            <div class="menu-wrapper file image <%=(selected[file.cid]===true)?'selected':''%> <%=(file.previewClass())%>" data-cid="<%=file.cid %>"  <%if(file.isImg()){%>style="background-image: url(<%= file.thumb %>);<%if(file.isSvg()){%>background-size:100%;background-repeat:no-repeat;<%}%>"<%}%>>
+            <div class="menu-wrapper file image <%=(selected[file.cid]===true)?'selected':''%> <%=(file.previewClass())%> img-display" data-cid="<%=file.cid %>"  <%if(file.isImg()){%>style="background-image: url(<%= file.thumb %>);<%if(file.isSvg()){%>background-size:100%;background-repeat:no-repeat;<%}%>"<%}%>>
                 <span class="select"><span class="icon-check"></span></span>
                 <%if(!file.isImg()){%>
                 <span class="icon-file ext">
@@ -53,7 +60,11 @@
                         <span class="icon-add"></span>
                     </li>
                 </ul>
-
+                <div class="fileName-container">
+                    <span class="<%= file.isImg() ? 'img-name' : 'other-name' %>">
+                        <%= file.name %>
+                    </span>               
+                </div>
             </div>
             <% } %>
             <% } %>
Index: src/js/JEditor/FilePanel/Views/FileListView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileListView.js	(révision 13816)
+++ src/js/JEditor/FilePanel/Views/FileListView.js	(révision 13817)
@@ -30,6 +30,8 @@
             'click [data-action="showIcons"]': 'showIcons',
             'click .flex-load-more .load-more' : 'loadMore',
             'click .flex-load-more .load-more' : 'loadMore',
+            'keypress .container-search .file-input': 'searchFile',
+            'click .container-search .clear-btn': 'reset'
         },
         initialize: function() {
             this._super();
@@ -214,6 +216,12 @@
         },
         render: function() {
             this._super();
+            if (this.collection.searchQuery) {
+                this.$('.file-input').val(this.collection.searchQuery);
+                $('.clear-btn').removeClass('hidden');
+            } else {
+                this.collection.setName(null);
+            }
             if(!this.app.user.can("delete_file")){
                 this.$("li.action.delete").replaceWith('<li class="" style="visibility: hidden;"></li>');
             }
@@ -222,6 +230,26 @@
             this.dom[this.cid].showUploader = this.$('input.upload.button');
             return this;
         },
+        searchFile: function(event) {
+            if (event.keyCode === 13) {
+                var $target = $(event.currentTarget),
+                    searchValue = $target.val().trim();
+
+                this.collection.setName(searchValue);
+                this.collection.searchQuery = searchValue;
+                this.collection.setOffset(0);
+
+                this.collection.fetch({ remove: true });
+            }
+        },
+        reset: function() {
+            $('.container-search .file-input').val('');
+            this.collection.setName(null);
+            this.collection.searchQuery = null;
+            this.collection.setOffset(0);
+
+            this.collection.fetch({ remove: true });
+        },
         uploadercomplete: function(e, data) {
             this.resetFilter();
             this.trigger(Events.ListViewEvents.UPLOADER_COMPLETE, e, data);
@@ -235,16 +263,22 @@
         // return this.filter(FileListView.FILTER_IMAGES_ONLY);
         this.collection.setType('image');
         this.collection.setOffset(0);
+        this.collection.setName(null);
+        this.collection.searchQuery = null;
         this.collection.fetch({remove: true});
         },
         onlyImagesNews : function() {
             this.collection.setType('news');
             this.collection.setOffset(0);
+            this.collection.setName(null);
+            this.collection.searchQuery = null;
             this.collection.fetch({remove: true});
         },
         resetFilter :function(){
             this.collection.setType(null);
             this.collection.setOffset(0);
+            this.collection.setName(null);
+            this.collection.searchQuery = null;
             this.collection.fetch({remove: true});
         },
         noImage: function() {
@@ -251,6 +285,8 @@
             //return this.filter(FileListView.FILTER_NO_IMAGES);
             this.collection.setType('file');
             this.collection.setOffset(0);
+            this.collection.setName(null);
+            this.collection.searchQuery = null;
             this.collection.fetch({remove: true});
         },
         loadMore: function(){
Index: src/js/JEditor/FilePanel/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 13816)
+++ src/js/JEditor/FilePanel/nls/fr-ca/i18n.js	(révision 13817)
@@ -134,4 +134,6 @@
     "showIcons" : "Importer depuis une base d'images",
     "mimeNotsupporte": "non pris en charge. Échec de la création d'une ressource",
     "NewsFiles" :"Entêtes articles",
+    "searchFile" : "Rechercher une image, un fichier ...",
+    "noResult": "Aucun résultat trouvé."
 });
\ No newline at end of file
Index: src/js/JEditor/FilePanel/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 13816)
+++ src/js/JEditor/FilePanel/nls/fr-fr/i18n.js	(révision 13817)
@@ -138,4 +138,6 @@
     "showIcons" : "Importer depuis une base d'images",
     "mimeNotsupporte": "non pris en charge. Échec de la création d'une ressource",
     "NewsFiles" :"Entêtes articles",
+    "searchFile" : "Rechercher une image, un fichier ...",
+    "noResult": "Aucun résultat trouvé."
 });
\ No newline at end of file
Index: src/js/JEditor/FilePanel/nls/i18n.js
===================================================================
--- src/js/JEditor/FilePanel/nls/i18n.js	(révision 13816)
+++ src/js/JEditor/FilePanel/nls/i18n.js	(révision 13817)
@@ -139,7 +139,9 @@
         "browseIconTitle":"Browse the icon database",
         "showIcons" : "Import from image database",
         "mimeNotsupporte":"not supported. Create resource failed",
-        "NewsFiles" :"Article headers"
+        "NewsFiles" :"Article headers",
+        "searchFile" : "Search for an image, file ...",
+        "noResult": "No results found."
     },
     "fr-fr": true, "fr-ca":true
 })
\ No newline at end of file
Index: src/less/imports/filePanel.less
===================================================================
--- src/less/imports/filePanel.less	(révision 13816)
+++ src/less/imports/filePanel.less	(révision 13817)
@@ -968,6 +968,52 @@
         }
     }
 }
+#files .my-files {
+    .img-display {
+        background-size: contain;
+        background-repeat: no-repeat;
+        margin-bottom: 50px;
+        border-radius: 7px;
+        background-color: #f1f1f1;
+        .fileName-container {
+            .img-name, .other-name {
+                display: block;
+                color: #fb923c;
+                text-align: left;
+                overflow: hidden; 
+                white-space: nowrap; 
+                text-overflow: ellipsis;
+            }
+            .img-name {
+                margin-top: 122px;
+            }
+            .other-name {
+                position: absolute;
+                top: 122px;
+                left: 0;
+                right: 0;
+            }
+        }
+    }   
+}
+.container-search {
+    margin-top: 50px;
+    text-align: center;
+    .file-input {
+        width: 40%;
+        border-radius: 5px;
+        border: 1px solid #ddd;
+        padding: 7px;
+    }
+    .clear-btn {
+        cursor: pointer;
+        background: none;
+        border: none;
+        font-size: 16px;
+        margin-left: -35px;
+        color: #888;
+    }
+}
 
 .my-files .file .masque {
     opacity: 0;
