Revision: r13097
Date: 2024-09-25 09:57:16 +0300 (lrb 25 Sep 2024) 
Author: traj<PERSON><PERSON><PERSON><PERSON> 

## Commit message
Whishlist : tar.gz fichier telechargement

## Files changed

## Full metadata
------------------------------------------------------------------------
r13097 | trajaonar<PERSON>lo | 2024-09-25 09:57:16 +0300 (lrb 25 Sep 2024) | 1 ligne
Chemins modifiés :
   M /branches/env/apache-php-node.dockerfile
   M /branches/ideo3_v2/dev/#librairies/vendor/CoreRessourceManager/src/CoreRessourceManager/Controller/ResourceZipExportController.php
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Views/FileManagerUIView.js

Whishlist : tar.gz fichier telechargement
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js	(révision 13096)
+++ src/js/JEditor/FilePanel/Views/CollectionManagerUIView.js	(révision 13097)
@@ -95,7 +95,7 @@
                         }
                         else
                         {
-                            window.location = __IDEO_RESSOURCE_IMAGE_PATH__+ 'collection.zip';
+                            window.location = __IDEO_RESSOURCE_IMAGE_PATH__+ 'collections.tar.gz';
                         }
                     })
                     return false;
Index: src/js/JEditor/FilePanel/Views/FileManagerUIView.js
===================================================================
--- src/js/JEditor/FilePanel/Views/FileManagerUIView.js	(révision 13096)
+++ src/js/JEditor/FilePanel/Views/FileManagerUIView.js	(révision 13097)
@@ -222,7 +222,7 @@
                             }
                             else
                             {
-                                window.location = __IDEO_RESSOURCE_IMAGE_PATH__+ 'fichiers.zip';
+                                window.location = __IDEO_RESSOURCE_IMAGE_PATH__+ 'fichiers.tar.gz';
                             }
                         })
                         return false;
