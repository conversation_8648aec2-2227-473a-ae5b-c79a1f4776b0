Revision: r10320
Date: 2023-02-03 10:04:27 +0300 (zom 03 Feb 2023) 
Author: jn.harison 

## Commit message
Debug File Panel: show image as a whole if svg

## Files changed

## Full metadata
------------------------------------------------------------------------
r10320 | jn.harison | 2023-02-03 10:04:27 +0300 (zom 03 Feb 2023) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/Commons/Files/Models/File.js
   M /branches/ideo3_v2/integration/src/js/JEditor/FilePanel/Templates/fileList.html

Debug File Panel: show image as a whole if svg
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/Commons/Files/Models/File.js
===================================================================
--- src/js/JEditor/Commons/Files/Models/File.js	(révision 10319)
+++ src/js/JEditor/Commons/Files/Models/File.js	(révision 10320)
@@ -52,12 +52,18 @@
                                  * 
                                  * @returns {Boolean}
                                  */
-                                  isVideo: function() {
+                                isVideo: function() {
                                     var regexpImg = /^video/;
                                     if (regexpImg.test(this.mimeType))
                                         return true;
                                     return false;
                                 },
+                                isSvg: function() {
+                                    var regexpImg = /^svg/;
+                                    if(regexpImg.test(this.ext))
+                                        return true;
+                                    return false;
+                                },
                                 /**
                                  * 
                                  * @returns {Boolean}
Index: src/js/JEditor/FilePanel/Templates/fileList.html
===================================================================
--- src/js/JEditor/FilePanel/Templates/fileList.html	(révision 10319)
+++ src/js/JEditor/FilePanel/Templates/fileList.html	(révision 10320)
@@ -6,7 +6,7 @@
             var file = content[i];
             %>
             <%if(!file.isLogo){%>
-            <div class="menu-wrapper file image <%=(selected[file.cid]===true)?'selected':''%> <%=(file.previewClass())%>" data-cid="<%=file.cid %>"  <%if(file.isImg()){%>style="background-image: url(<%= file.thumb %>);"<%}%>>
+            <div class="menu-wrapper file image <%=(selected[file.cid]===true)?'selected':''%> <%=(file.previewClass())%>" data-cid="<%=file.cid %>"  <%if(file.isImg()){%>style="background-image: url(<%= file.thumb %>);<%if(file.isSvg()){%>background-size:100%;background-repeat:no-repeat;<%}%>"<%}%>>
                 <span class="select"><span class="icon-check"></span></span>
                 <%if(!file.isImg()){%>
                 <span class="icon-file ext">
