Revision: r12985
Date: 2024-09-05 17:43:19 +0300 (lkm 05 Sep 2024) 
Author: rrakotoarinelina 

## Commit message
whislist IDEO3.2 : Update Stats - correction

## Files changed

## Full metadata
------------------------------------------------------------------------
r12985 | rrakotoarinelina | 2024-09-05 17:43:19 +0300 (lkm 05 Sep 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/StatsPanel/StatsPanel.js

whislist IDEO3.2 : Update Stats - correction
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/StatsPanel/StatsPanel.js
===================================================================
--- src/js/JEditor/StatsPanel/StatsPanel.js	(révision 12984)
+++ src/js/JEditor/StatsPanel/StatsPanel.js	(révision 12985)
@@ -90,12 +90,22 @@
                             this.undelegateEvents();
 
                             var key = __IDEO_VHOST_ID_KEY__ ? __IDEO_VHOST_ID_KEY__ : __IDEO_CODE_BOUTON__;
-                            var codebouton = __IDEO_SITE_CODE_BOUTON__; 
-                            var codeboutonsNonFr = ["AGCCND", "LNKUSA", "LNKAUS"]; 
+                            var codebouton = __IDEO_SITE_CODE_BOUTON__;
+                            var prefixCodeboutonsNonFr = ["AGCCND", "LNKUSA", "LNKAUS"];
                             var lang = (this.app.uiLanguage.id.substr(0, 2) == "fr") ? "fr" : "en";
-                            var url  = "";
+                            var url = "";
 
-                            if (codeboutonsNonFr.indexOf(codebouton) !== -1) {
+                            var startsWithNonFr = false;
+
+                            for (var i = 0; i < prefixCodeboutonsNonFr.length; i++) {
+                                var prefix = prefixCodeboutonsNonFr[i];
+                                if (codebouton.indexOf(prefix) === 0) {
+                                    startsWithNonFr = true;
+                                    break;
+                                }
+                            }
+                            
+                            if (startsWithNonFr) {
                                 url = `https://stats.linkeo.ovh/index.php?action=roi&id=${key}&ideo3=1&lang=${lang}`;
                             } else {
                                 url = `https://stats-gui-back-office.linkeo.ovh/?codebouton=${codebouton}&vhid=64021`;
