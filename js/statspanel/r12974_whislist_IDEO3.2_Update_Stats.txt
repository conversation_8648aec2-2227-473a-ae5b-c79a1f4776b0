Revision: r12974
Date: 2024-09-05 11:21:23 +0300 (lkm 05 Sep 2024) 
Author: rrakotoarinelina 

## Commit message
whislist IDEO3.2 : Update Stats

## Files changed

## Full metadata
------------------------------------------------------------------------
r12974 | rrakotoarinelina | 2024-09-05 11:21:23 +0300 (lkm 05 Sep 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/DashBoard/DashBoard.js
   M /branches/ideo3_v2/integration/src/js/JEditor/DashBoard/Templates/DashBoardTpl.html
   M /branches/ideo3_v2/integration/src/js/JEditor/StatsPanel/StatsPanel.js
   M /branches/ideo3_v2/integration/src/js/JEditor/StatsPanel/Templates/statsPanelTpl.html

whislist IDEO3.2 : Update Stats
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/DashBoard/DashBoard.js
===================================================================
--- src/js/JEditor/DashBoard/DashBoard.js	(révision 12973)
+++ src/js/JEditor/DashBoard/DashBoard.js	(révision 12974)
@@ -102,18 +102,21 @@
 
                             // recuperation de la langue pour transmettre à l'iframe stats
                             var lang = (this.app.uiLanguage.id.substr(0, 2) == "fr") ? "fr" : "en";
-
+                            var displayStat = false;
                             if( pays !== null ) {
                                 switch (pays[0]) {
                                     case 'AGCCND':
                                         phone = "**************";
+                                        displayStat = true;
                                         break;
                                     case 'LNKAUS':
                                         phone = "1300 546 536";
                                         schedule = "customerInfos_3";
+                                        displayStat = true;
                                         break;
                                     case 'LNKUSA':
                                         phone = "**************";
+                                        displayStat = true;
                                         break;
                                     case 'LNKARE':
                                         phone = "800 546536";
@@ -124,7 +127,7 @@
                             //code
                             this.setDOM();//création des variables this.dom[]
 
-                            this.$el.html(this._template({key:key,user:this.user,env:this.app.params.env_imbuilder,lang:lang,whitelabel:whitelabel,phone:phone,schedule:schedule}));
+                            this.$el.html(this._template({key:key,user:this.user,env:this.app.params.env_imbuilder,lang:lang,whitelabel:whitelabel,phone:phone,schedule:schedule,displayStat:displayStat}));
 
                             // init app intro (tutorial)
                             this.appIntro();
Index: src/js/JEditor/DashBoard/Templates/DashBoardTpl.html
===================================================================
--- src/js/JEditor/DashBoard/Templates/DashBoardTpl.html	(révision 12973)
+++ src/js/JEditor/DashBoard/Templates/DashBoardTpl.html	(révision 12974)
@@ -240,8 +240,10 @@
 
 <div class="stats">
     <div class="row">
-        <% if(!env){ %>
-        <iframe width="1024" height="495" src="https://stats.linkeo.ovh/index.php?id=<%= key %>&ideo3=1&action=dashboard&lang=<%=lang%>" scrolling="no" style="border:none;"></iframe>
+        <% if(displayStat){ %>
+            <% if(!env){ %>
+            <iframe width="1024" height="495" src="https://stats.linkeo.ovh/index.php?id=<%= key %>&ideo3=1&action=dashboard&lang=<%=lang%>" scrolling="no" style="border:none;"></iframe>
+            <% } %>
         <% } %>
     </div>
 </div>
Index: src/js/JEditor/StatsPanel/StatsPanel.js
===================================================================
--- src/js/JEditor/StatsPanel/StatsPanel.js	(révision 12973)
+++ src/js/JEditor/StatsPanel/StatsPanel.js	(révision 12974)
@@ -22,11 +22,13 @@
                         constructor: function(options) {
                             //mon code
                             PanelView.call(this, {panelName: 'designPanel'});
+
                         },
                         /**
                          * initialise l'objet
                          */
                         initialize: function() {
+                          
                             this._super();
                             this.childViews = {};
                             this._template = this.buildTemplate(statsPanelTpl, translate);
@@ -88,11 +90,20 @@
                             this.undelegateEvents();
 
                             var key = __IDEO_VHOST_ID_KEY__ ? __IDEO_VHOST_ID_KEY__ : __IDEO_CODE_BOUTON__;
+                            var codebouton = __IDEO_SITE_CODE_BOUTON__; 
+                            var codeboutonsNonFr = ["AGCCND", "LNKUSA", "LNKAUS"]; 
+                            var lang = (this.app.uiLanguage.id.substr(0, 2) == "fr") ? "fr" : "en";
+                            var url  = "";
 
+                            if (codeboutonsNonFr.indexOf(codebouton) !== -1) {
+                                url = `https://stats.linkeo.ovh/index.php?action=roi&id=${key}&ideo3=1&lang=${lang}`;
+                            } else {
+                                url = `https://stats-gui-back-office.linkeo.ovh/?codebouton=${codebouton}&vhid=64021`;
+                            }
+
                             // recuperation de la langue pour transmettre à l'iframe stats
-                            var lang = (this.app.uiLanguage.id.substr(0, 2) == "fr") ? "fr" : "en";
 
-                            this.$el.html(this._template({key:key, lang:lang}));
+                            this.$el.html(this._template({url}));
                             this.setDOM();//création des variables this.dom[]
                             var windowW = this.dom.window.width();
 
Index: src/js/JEditor/StatsPanel/Templates/statsPanelTpl.html
===================================================================
--- src/js/JEditor/StatsPanel/Templates/statsPanelTpl.html	(révision 12973)
+++ src/js/JEditor/StatsPanel/Templates/statsPanelTpl.html	(révision 12974)
@@ -1,5 +1,5 @@
 <iframe width="100%"
-	src="https://stats.linkeo.ovh/index.php?action=roi&id=<%= key %>&ideo3=1&lang=<%= lang %>" 
+	src="<%= url %>" 
 	style="border:none;
 	background: none repeat scroll 0% 0% transparent;min-height:550px;">
 </iframe>
