Revision: r13000
Date: 2024-09-06 12:39:59 +0300 (zom 06 Sep 2024) 
Author: jn.harison 

## Commit message
IDEO3.2 STATS:Correction du vhostID

## Files changed

## Full metadata
------------------------------------------------------------------------
r13000 | jn.harison | 2024-09-06 12:39:59 +0300 (zom 06 Sep 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/StatsPanel/StatsPanel.js

IDEO3.2 STATS:Correction du vhostID
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/StatsPanel/StatsPanel.js
===================================================================
--- src/js/JEditor/StatsPanel/StatsPanel.js	(révision 12999)
+++ src/js/JEditor/StatsPanel/StatsPanel.js	(révision 13000)
@@ -90,6 +90,7 @@
                             this.undelegateEvents();
 
                             var key = __IDEO_VHOST_ID_KEY__ ? __IDEO_VHOST_ID_KEY__ : __IDEO_CODE_BOUTON__;
+                            var vhostId = __IDEO_VHOST_ID__ ? __IDEO_VHOST_ID__ : '';
                             var codebouton = __IDEO_SITE_CODE_BOUTON__;
                             var prefixCodeboutonsNonFr = ["AGCCND", "LNKUSA", "LNKAUS"];
                             var lang = (this.app.uiLanguage.id.substr(0, 2) == "fr") ? "fr" : "en";
@@ -108,7 +109,7 @@
                             if (startsWithNonFr) {
                                 url = `https://stats.linkeo.ovh/index.php?action=roi&id=${key}&ideo3=1&lang=${lang}`;
                             } else {
-                                url = `https://stats-gui-back-office.linkeo.ovh/?codebouton=${codebouton}&vhid=${key}`;
+                                url = `https://stats-gui-back-office.linkeo.ovh/?codebouton=${codebouton}&vhid=${vhostId}`;
                             }
 
                             // recuperation de la langue pour transmettre à l'iframe stats
