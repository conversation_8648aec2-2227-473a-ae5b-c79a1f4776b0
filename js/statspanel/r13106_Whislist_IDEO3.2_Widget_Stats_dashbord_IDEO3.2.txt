Revision: r13106
Date: 2024-09-26 17:32:01 +0300 (lkm 26 Sep 2024) 
Author: rrakotoarinelina 

## Commit message
Whislist IDEO3.2 : Widget Stats dashbord IDEO3.2

## Files changed

## Full metadata
------------------------------------------------------------------------
r13106 | rrakotoarinelina | 2024-09-26 17:32:01 +0300 (lkm 26 Sep 2024) | 1 ligne
Chemins modifiés :
   M /branches/ideo3_v2/integration/src/js/JEditor/DashBoard/DashBoard.js
   M /branches/ideo3_v2/integration/src/js/JEditor/DashBoard/Templates/DashBoardTpl.html
   M /branches/ideo3_v2/integration/src/js/JEditor/DashBoard/nls/fr-ca/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/DashBoard/nls/fr-fr/i18n.js
   M /branches/ideo3_v2/integration/src/js/JEditor/DashBoard/nls/i18n.js
   M /branches/ideo3_v2/integration/src/less/imports/dashboard.less

Whislist IDEO3.2 : Widget Stats dashbord IDEO3.2
------------------------------------------------------------------------

## Diff
Index: src/js/JEditor/DashBoard/DashBoard.js
===================================================================
--- src/js/JEditor/DashBoard/DashBoard.js	(révision 13105)
+++ src/js/JEditor/DashBoard/DashBoard.js	(révision 13106)
@@ -99,35 +99,43 @@
                             var pays = /^(AGCCND|LNKAUS|LNKUSA|LNKARE)/.exec(__IDEO_SITE_CODE_BOUTON__);
                             var phone = "09 72 67 01 67";
                             var schedule = "customerInfos_2";
-
+                            
                             // recuperation de la langue pour transmettre à l'iframe stats
                             var lang = (this.app.uiLanguage.id.substr(0, 2) == "fr") ? "fr" : "en";
-                            var displayStat = false;
                             if( pays !== null ) {
                                 switch (pays[0]) {
                                     case 'AGCCND':
                                         phone = "**************";
-                                        displayStat = true;
                                         break;
-                                    case 'LNKAUS':
+                                        case 'LNKAUS':
                                         phone = "1300 546 536";
                                         schedule = "customerInfos_3";
-                                        displayStat = true;
                                         break;
-                                    case 'LNKUSA':
+                                        case 'LNKUSA':
                                         phone = "**************";
-                                        displayStat = true;
                                         break;
-                                    case 'LNKARE':
-                                        phone = "800 546536";
+                                        case 'LNKARE':
+                                            phone = "800 546536";
                                         break;
                                 }
                             }
+                            
+                            //url pour iframe 
+                            //check si codebouton non fr 
+                            var codeboutonsNonFr = /^(AGCCND|LNKUSA|LNKAUS)/.exec(__IDEO_SITE_CODE_BOUTON__);
+                            var vhostId = __IDEO_VHOST_ID__ ? __IDEO_VHOST_ID__ : '';
+                            var codebouton = __IDEO_SITE_CODE_BOUTON__;
+                            var url;
+                            if (codeboutonsNonFr != null) {
+                                url = 'https://stats.linkeo.ovh/index.php?id=' + key + '&ideo3=1&action=dashboard&lang=' + lang;
+                            } else {
+                                url = 'https://stats-gui-back-office.linkeo.ovh/?codebouton=' + codebouton + '&vhid=' + vhostId;
+                            }
 
                             //code
                             this.setDOM();//création des variables this.dom[]
 
-                            this.$el.html(this._template({key:key,user:this.user,env:this.app.params.env_imbuilder,lang:lang,whitelabel:whitelabel,phone:phone,schedule:schedule,displayStat:displayStat}));
+                            this.$el.html(this._template({key:key,user:this.user,env:this.app.params.env_imbuilder,lang:lang,whitelabel:whitelabel,phone:phone,schedule:schedule,url:url}));
 
                             // init app intro (tutorial)
                             this.appIntro();
Index: src/js/JEditor/DashBoard/Templates/DashBoardTpl.html
===================================================================
--- src/js/JEditor/DashBoard/Templates/DashBoardTpl.html	(révision 13105)
+++ src/js/JEditor/DashBoard/Templates/DashBoardTpl.html	(révision 13106)
@@ -240,11 +240,13 @@
 
 <div class="stats">
     <div class="row">
-        <% if(displayStat){ %>
-            <% if(!env){ %>
-            <iframe width="1024" height="495" src="https://stats.linkeo.ovh/index.php?id=<%= key %>&ideo3=1&action=dashboard&lang=<%=lang%>" scrolling="no" style="border:none;"></iframe>
-            <% } %>
+        <% if(!env){ %>
+        <iframe width="1024" height="495" src="<%=url%>" scrolling="no" style="border:none;"></iframe>
+        <div class="gotostats">
+            <a href="#stats" class="gotostats"><%=__('viewstats')%></a>
+        </div>
         <% } %>
+
     </div>
 </div>
 
Index: src/js/JEditor/DashBoard/nls/fr-ca/i18n.js
===================================================================
--- src/js/JEditor/DashBoard/nls/fr-ca/i18n.js	(révision 13105)
+++ src/js/JEditor/DashBoard/nls/fr-ca/i18n.js	(révision 13106)
@@ -40,4 +40,5 @@
     "news_explained": "Parcourez vos pages depuis le menu de gauche. Ajoutez du contenu et prévisualisez votre site a tout moment",
     "Marketingauto":"Marketing Automation",
     "marketingauto_explained":"Gestion de votre base clients et automatisation d'actions",
+    "viewstats":"View statistics >",
 });
Index: src/js/JEditor/DashBoard/nls/fr-fr/i18n.js
===================================================================
--- src/js/JEditor/DashBoard/nls/fr-fr/i18n.js	(révision 13105)
+++ src/js/JEditor/DashBoard/nls/fr-fr/i18n.js	(révision 13106)
@@ -40,4 +40,5 @@
     "news_explained": "Parcourez vos pages depuis le menu de gauche. Ajoutez du contenu et prévisualisez votre site a tout moment",
     "Marketingauto":"Marketing Automation",
     "marketingauto_explained":"Gestion de votre base clients et automatisation d'actions",
+    "viewstats":"Voir les statistiques >",
 });
Index: src/js/JEditor/DashBoard/nls/i18n.js
===================================================================
--- src/js/JEditor/DashBoard/nls/i18n.js	(révision 13105)
+++ src/js/JEditor/DashBoard/nls/i18n.js	(révision 13106)
@@ -48,6 +48,7 @@
       "news_explained": "Browse your pages from the left-hand menu. Add content and preview your site at any time",
       "Marketingauto":"Marketing Automation",
       "marketingauto_explained":"Manage your customer base and automate marketing actions",
+      "viewstats":"View statistics >",
    },
    "fr-fr":true,
    "fr-ca":true
Index: src/less/imports/dashboard.less
===================================================================
--- src/less/imports/dashboard.less	(révision 13105)
+++ src/less/imports/dashboard.less	(révision 13106)
@@ -252,4 +252,13 @@
 		}
 	}
 
+	.gotostats{
+		text-align: center;
+	}
+	.gotostats a{
+		text-decoration: none;
+		font-size: 20px;
+		color: black;
+	}
+
 }
\ No newline at end of file
