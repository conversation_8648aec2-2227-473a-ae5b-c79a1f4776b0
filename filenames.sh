#!/bin/bash

# Set the folder path here
FOLDER_PATH="js"
OUTPUT_FILE="file_first_parts.txt"

# Clear or create the output file
> "$OUTPUT_FILE"

# Loop through each file in the folder
for file in "$FOLDER_PATH"/*; do
    if [ -f "$file" ]; then
        # Get the filename without path
        filename=$(basename "$file")
        # Extract the first part before the first '_'
        first_part=$(echo "$filename" | cut -d'_' -f1)
        # Write to the output file
        echo "$first_part" >> "$OUTPUT_FILE"
    fi
done

echo "List of first parts written to $OUTPUT_FILE"
